﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace ns6
{
	// Token: 0x02000006 RID: 6
	[CompilerGenerated]
	internal sealed class Class5<T, U, V>
	{
		// Token: 0x1700000E RID: 14
		// (get) Token: 0x0600001E RID: 30 RVA: 0x000105C0 File Offset: 0x0000E7C0
		public T type
		{
			get
			{
				return this.gparam_0;
			}
		}

		// Token: 0x1700000F RID: 15
		// (get) Token: 0x0600001F RID: 31 RVA: 0x000105D8 File Offset: 0x0000E7D8
		public U idxcodes
		{
			get
			{
				return this.gparam_1;
			}
		}

		// Token: 0x17000010 RID: 16
		// (get) Token: 0x06000020 RID: 32 RVA: 0x000105F0 File Offset: 0x0000E7F0
		public V date
		{
			get
			{
				return this.gparam_2;
			}
		}

		// Token: 0x06000021 RID: 33 RVA: 0x00002ADA File Offset: 0x00000CDA
		[DebuggerHidden]
		public Class5(T gparam_3, U gparam_4, V gparam_5)
		{
			this.gparam_0 = gparam_3;
			this.gparam_1 = gparam_4;
			this.gparam_2 = gparam_5;
		}

		// Token: 0x06000022 RID: 34 RVA: 0x00010608 File Offset: 0x0000E808
		[DebuggerHidden]
		public bool Equals(object obj)
		{
			Class5<T, U, V> @class = obj as Class5<T, U, V>;
			bool result;
			if (@class != null && EqualityComparer<T>.Default.Equals(this.gparam_0, @class.gparam_0) && EqualityComparer<U>.Default.Equals(this.gparam_1, @class.gparam_1))
			{
				result = EqualityComparer<V>.Default.Equals(this.gparam_2, @class.gparam_2);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06000023 RID: 35 RVA: 0x00010670 File Offset: 0x0000E870
		[DebuggerHidden]
		public int GetHashCode()
		{
			return ((-********** + EqualityComparer<T>.Default.GetHashCode(this.gparam_0)) * -********** + EqualityComparer<U>.Default.GetHashCode(this.gparam_1)) * -********** + EqualityComparer<V>.Default.GetHashCode(this.gparam_2);
		}

		// Token: 0x06000024 RID: 36 RVA: 0x000106C8 File Offset: 0x0000E8C8
		[DebuggerHidden]
		public string ToString()
		{
			IFormatProvider provider = null;
			string format = "{{ type = {0}, idxcodes = {1}, date = {2} }}";
			object[] array = new object[3];
			int num = 0;
			T t = this.gparam_0;
			ref T ptr = ref t;
			T t2 = default(T);
			object obj;
			if (t2 == null)
			{
				t2 = t;
				ptr = ref t2;
				if (t2 == null)
				{
					obj = null;
					goto IL_46;
				}
			}
			obj = ptr.ToString();
			IL_46:
			array[num] = obj;
			int num2 = 1;
			U u = this.gparam_1;
			ref U ptr2 = ref u;
			U u2 = default(U);
			object obj2;
			if (u2 == null)
			{
				u2 = u;
				ptr2 = ref u2;
				if (u2 == null)
				{
					obj2 = null;
					goto IL_81;
				}
			}
			obj2 = ptr2.ToString();
			IL_81:
			array[num2] = obj2;
			int num3 = 2;
			V v = this.gparam_2;
			ref V ptr3 = ref v;
			V v2 = default(V);
			object obj3;
			if (v2 == null)
			{
				v2 = v;
				ptr3 = ref v2;
				if (v2 == null)
				{
					obj3 = null;
					goto IL_C0;
				}
			}
			obj3 = ptr3.ToString();
			IL_C0:
			array[num3] = obj3;
			return string.Format(provider, format, array);
		}

		// Token: 0x0400000E RID: 14
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly T gparam_0;

		// Token: 0x0400000F RID: 15
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly U gparam_1;

		// Token: 0x04000010 RID: 16
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly V gparam_2;
	}
}
