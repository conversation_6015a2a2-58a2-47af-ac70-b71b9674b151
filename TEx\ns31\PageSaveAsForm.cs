﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using TEx;

namespace ns31
{
	// Token: 0x020001A8 RID: 424
	internal sealed partial class PageSaveAsForm : Form
	{
		// Token: 0x1400007A RID: 122
		// (add) Token: 0x06001052 RID: 4178 RVA: 0x0006A8B8 File Offset: 0x00068AB8
		// (remove) Token: 0x06001053 RID: 4179 RVA: 0x0006A8F0 File Offset: 0x00068AF0
		public event EventHandler PageSaved
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06001054 RID: 4180 RVA: 0x0006A928 File Offset: 0x00068B28
		protected void method_0()
		{
			EventArgs e = new EventArgs();
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x06001055 RID: 4181 RVA: 0x00006F09 File Offset: 0x00005109
		public PageSaveAsForm(ChartUISettings? nullable_1, bool bool_1)
		{
			this.InitializeComponent();
			this.nullable_0 = nullable_1;
			this.bool_0 = bool_1;
		}

		// Token: 0x06001056 RID: 4182 RVA: 0x0006A950 File Offset: 0x00068B50
		public PageSaveAsForm() : this(null, true)
		{
		}

		// Token: 0x06001057 RID: 4183 RVA: 0x0006A970 File Offset: 0x00068B70
		private void PageSaveAsForm_Load(object sender, EventArgs e)
		{
			foreach (ChartPage chartPage in Base.UI.ChartPageList)
			{
				this.listBox_Pages.Items.Add(chartPage.Name);
			}
		}

		// Token: 0x06001058 RID: 4184 RVA: 0x00004268 File Offset: 0x00002468
		private void button_Cancel_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x06001059 RID: 4185 RVA: 0x00006F27 File Offset: 0x00005127
		private void listBox_Pages_SelectedIndexChanged(object sender, EventArgs e)
		{
			if (this.listBox_Pages.SelectedItem != null)
			{
				this.textBox_PageName.Text = this.listBox_Pages.SelectedItem.ToString();
			}
		}

		// Token: 0x0600105A RID: 4186 RVA: 0x0006A9D8 File Offset: 0x00068BD8
		private void button_OK_Click(object sender, EventArgs e)
		{
			if (this.textBox_PageName.Text.Length > 0)
			{
				if (Base.UI.ChartPageList.Where(new Func<ChartPage, bool>(this.method_1)).Any<ChartPage>())
				{
					if (MessageBox.Show("覆盖现有同名页面吗？", "提醒", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) == DialogResult.OK)
					{
						Base.UI.smethod_84(this.nullable_0, this.textBox_PageName.Text, this.bool_0);
						this.method_0();
					}
				}
				else
				{
					Base.UI.smethod_84(this.nullable_0, this.textBox_PageName.Text, this.bool_0);
					this.method_0();
				}
				base.Close();
			}
		}

		// Token: 0x17000267 RID: 615
		// (get) Token: 0x0600105B RID: 4187 RVA: 0x0006AA80 File Offset: 0x00068C80
		// (set) Token: 0x0600105C RID: 4188 RVA: 0x00006F53 File Offset: 0x00005153
		public bool IfSetSavedPageAsCurrPage
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x0600105D RID: 4189 RVA: 0x00006F5E File Offset: 0x0000515E
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600105F RID: 4191 RVA: 0x0006AE50 File Offset: 0x00069050
		[CompilerGenerated]
		private bool method_1(ChartPage chartPage_0)
		{
			return chartPage_0.Name == this.textBox_PageName.Text;
		}

		// Token: 0x0400082A RID: 2090
		private bool bool_0;

		// Token: 0x0400082B RID: 2091
		private ChartUISettings? nullable_0;

		// Token: 0x0400082C RID: 2092
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x0400082D RID: 2093
		private IContainer icontainer_0;
	}
}
