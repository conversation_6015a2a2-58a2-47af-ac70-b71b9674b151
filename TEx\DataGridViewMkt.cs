﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns22;
using ns28;
using ns30;
using TEx.Util;

namespace TEx
{
	// Token: 0x0200020B RID: 523
	[DesignerCategory("Code")]
	internal sealed class DataGridViewMkt : Class282
	{
		// Token: 0x06001568 RID: 5480 RVA: 0x0008D7BC File Offset: 0x0008B9BC
		public DataGridViewMkt(int autoSizeColumnsThreshold = 1350) : base(true)
		{
			this.AutoSizeColumnsParentWidthThreshold = autoSizeColumnsThreshold;
			base.CellFormatting += this.DataGridViewMkt_CellFormatting;
			base.DataSourceChanged += this.DataGridViewMkt_DataSourceChanged;
			base.ParentChanged += this.DataGridViewMkt_ParentChanged;
			base.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
			base.RowHeadersWidth = 50;
			base.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.DisableResizing;
		}

		// Token: 0x06001569 RID: 5481 RVA: 0x0008D828 File Offset: 0x0008BA28
		protected override void vmethod_0()
		{
			base.vmethod_0();
			base.BorderStyle = BorderStyle.FixedSingle;
			base.CellBorderStyle = DataGridViewCellBorderStyle.None;
			base.DefaultCellStyle.WrapMode = DataGridViewTriState.False;
			base.RowHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;
			base.RowsDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
			base.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;
			base.AllowUserToOrderColumns = true;
		}

		// Token: 0x0600156A RID: 5482 RVA: 0x0008D87C File Offset: 0x0008BA7C
		protected override void vmethod_2()
		{
			if (!base.IsDisposed)
			{
				try
				{
					if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
					{
						Color backColor = Color.FromArgb(22, 22, 22);
						Color backColor2 = Color.FromArgb(12, 12, 12);
						Color selectionBackColor = Color.FromArgb(49, 48, 56);
						base.BorderStyle = BorderStyle.FixedSingle;
						base.DefaultCellStyle.ForeColor = Color.White;
						base.DefaultCellStyle.BackColor = backColor;
						base.RowsDefaultCellStyle.ForeColor = Color.White;
						base.DefaultCellStyle.SelectionForeColor = Color.White;
						base.DefaultCellStyle.SelectionBackColor = selectionBackColor;
						base.AlternatingRowsDefaultCellStyle.BackColor = backColor;
						base.BackgroundColor = Color.Black;
						base.ColumnHeadersDefaultCellStyle.BackColor = backColor2;
						base.ColumnHeadersDefaultCellStyle.ForeColor = Color.FromArgb(192, 192, 192);
						base.RowHeadersDefaultCellStyle.BackColor = Color.Black;
						base.RowHeadersDefaultCellStyle.ForeColor = Color.White;
						base.RowHeadersDefaultCellStyle.SelectionBackColor = Color.Black;
						base.RowHeadersDefaultCellStyle.SelectionForeColor = Color.White;
						base.CellBorderStyle = DataGridViewCellBorderStyle.Single;
						try
						{
							base.GridColor = Color.FromArgb(30, 30, 30);
						}
						catch
						{
						}
						base.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
					}
					else
					{
						base.BorderStyle = BorderStyle.None;
						base.DefaultCellStyle.ForeColor = Color.Black;
						base.DefaultCellStyle.BackColor = Color.White;
						base.RowsDefaultCellStyle.ForeColor = Color.Black;
						base.DefaultCellStyle.SelectionForeColor = Color.Black;
						base.DefaultCellStyle.SelectionBackColor = Color.FloralWhite;
						base.AlternatingRowsDefaultCellStyle.BackColor = Color.White;
						base.BackgroundColor = Color.FromKnownColor(KnownColor.Control);
						base.ColumnHeadersDefaultCellStyle.BackColor = Color.FromKnownColor(KnownColor.Control);
						base.ColumnHeadersDefaultCellStyle.ForeColor = Color.Black;
						base.RowHeadersDefaultCellStyle.BackColor = Color.FromKnownColor(KnownColor.Control);
						base.RowHeadersDefaultCellStyle.ForeColor = Color.Black;
						base.RowHeadersDefaultCellStyle.SelectionBackColor = Color.FromKnownColor(KnownColor.Control);
						base.RowHeadersDefaultCellStyle.SelectionForeColor = Color.White;
						base.CellBorderStyle = DataGridViewCellBorderStyle.Single;
						try
						{
							base.GridColor = Color.FloralWhite;
						}
						catch
						{
						}
						base.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
					}
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
		}

		// Token: 0x0600156B RID: 5483 RVA: 0x0008DB10 File Offset: 0x0008BD10
		public void method_5(DataGridViewCellFormattingEventArgs dataGridViewCellFormattingEventArgs_0)
		{
			lock (dataGridViewCellFormattingEventArgs_0)
			{
				DataGridViewCell dataGridViewCell = base.Rows[dataGridViewCellFormattingEventArgs_0.RowIndex].Cells[dataGridViewCellFormattingEventArgs_0.ColumnIndex];
				dataGridViewCellFormattingEventArgs_0.CellStyle.SelectionForeColor = dataGridViewCell.Style.ForeColor;
			}
		}

		// Token: 0x0600156C RID: 5484 RVA: 0x0008DB78 File Offset: 0x0008BD78
		private void DataGridViewMkt_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
		{
			DataGridViewRow dataGridViewRow = (sender as DataGridView).Rows[e.RowIndex];
			ShowMktSymb showMktSymb = dataGridViewRow.DataBoundItem as ShowMktSymb;
			DataGridViewCell dataGridViewCell = dataGridViewRow.Cells[e.ColumnIndex];
			Color color = Color.FromArgb(240, 248, 136);
			if (e.Value != null)
			{
				if (e.ColumnIndex != 1)
				{
					if (e.ColumnIndex != 2)
					{
						if (e.ColumnIndex != 3 && e.ColumnIndex != 6)
						{
							if (e.ColumnIndex != 7)
							{
								if (e.ColumnIndex == 8)
								{
									this.method_6(dataGridViewCell, showMktSymb.Open, showMktSymb.LastDayClose);
									goto IL_11F;
								}
								if (e.ColumnIndex == 9)
								{
									this.method_6(dataGridViewCell, showMktSymb.High, showMktSymb.LastDayClose);
									goto IL_11F;
								}
								if (e.ColumnIndex == 10)
								{
									this.method_6(dataGridViewCell, showMktSymb.Low, showMktSymb.LastDayClose);
									goto IL_11F;
								}
								goto IL_11F;
							}
						}
						this.method_6(dataGridViewCell, showMktSymb.Price, showMktSymb.LastDayClose);
						goto IL_11F;
					}
				}
				if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
				{
					e.CellStyle.ForeColor = color;
					e.CellStyle.SelectionForeColor = color;
				}
			}
			IL_11F:
			e.CellStyle.SelectionForeColor = dataGridViewCell.Style.ForeColor;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				e.CellStyle.SelectionBackColor = Color.FromArgb(48, 48, 56);
				if (e.CellStyle.SelectionForeColor.R == 0 && e.CellStyle.SelectionForeColor.G == 0 && e.CellStyle.SelectionForeColor.B == 0)
				{
					if (e.ColumnIndex != 1)
					{
						if (e.ColumnIndex != 2)
						{
							e.CellStyle.SelectionForeColor = Color.White;
							return;
						}
					}
					e.CellStyle.SelectionForeColor = color;
				}
			}
			else
			{
				e.CellStyle.SelectionBackColor = Color.Moccasin;
			}
		}

		// Token: 0x0600156D RID: 5485 RVA: 0x0008DD64 File Offset: 0x0008BF64
		private void method_6(DataGridViewCell dataGridViewCell_0, decimal? nullable_0, decimal? nullable_1)
		{
			if (nullable_1 != null && nullable_0 != null)
			{
				if (nullable_0.Value > nullable_1.Value)
				{
					dataGridViewCell_0.Style.ForeColor = Color.FromArgb(255, 50, 50);
				}
				else if (nullable_0.Value < nullable_1.Value)
				{
					dataGridViewCell_0.Style.ForeColor = Color.FromArgb(0, 230, 0);
				}
			}
		}

		// Token: 0x0600156E RID: 5486 RVA: 0x000088D3 File Offset: 0x00006AD3
		private void DataGridViewMkt_DataSourceChanged(object sender, EventArgs e)
		{
			this.method_7();
		}

		// Token: 0x0600156F RID: 5487 RVA: 0x000041AE File Offset: 0x000023AE
		public void method_7()
		{
		}

		// Token: 0x06001570 RID: 5488 RVA: 0x000088DD File Offset: 0x00006ADD
		protected void OnDataError(bool displayErrorDialogIfNoHandler, DataGridViewDataErrorEventArgs e)
		{
			e.Cancel = true;
		}

		// Token: 0x06001571 RID: 5489 RVA: 0x0008DDE4 File Offset: 0x0008BFE4
		public void method_8(SortableBindingList<ShowMktSymb> sortableBindingList_0)
		{
			DataGridViewMkt.Class288 @class = new DataGridViewMkt.Class288();
			@class.dataGridViewMkt_0 = this;
			@class.sortableBindingList_0 = sortableBindingList_0;
			base.Invoke(new Action(@class.method_0));
		}

		// Token: 0x06001572 RID: 5490 RVA: 0x0008DE1C File Offset: 0x0008C01C
		protected override void vmethod_3()
		{
			Enum1 mktDgvType = this.MktDgvType;
			if (base.Columns.Count >= 1)
			{
				base.Columns[0].Visible = false;
				base.Columns[1].FillWeight = 65f;
				base.Columns[2].FillWeight = 75f;
				base.Columns[12].Visible = false;
				base.Columns[14].Visible = false;
				base.Columns[15].Visible = false;
				base.Columns[16].Visible = false;
				base.Columns[17].Visible = false;
				base.Columns[18].Visible = false;
				base.Columns[19].Visible = false;
				base.Columns[28].Visible = false;
				base.Columns[36].Visible = false;
				if (mktDgvType != Enum1.const_1)
				{
					if (mktDgvType != Enum1.const_2)
					{
						if (mktDgvType == Enum1.const_0)
						{
							this.method_9(false);
							this.method_11(false);
							base.Columns[23].Visible = false;
							this.method_12(false);
							base.Columns[21].FillWeight = 70f;
							base.Columns[29].FillWeight = 85f;
							base.Columns[30].FillWeight = 70f;
							base.Columns[31].FillWeight = 70f;
							base.Columns[32].FillWeight = 85f;
							base.Columns[33].FillWeight = 70f;
							base.Columns[34].FillWeight = 70f;
							base.Columns[35].FillWeight = 80f;
							return;
						}
						if (mktDgvType == Enum1.const_4)
						{
							this.method_9(false);
							this.method_10(false);
							return;
						}
						if (mktDgvType == Enum1.const_5)
						{
							this.method_9(false);
							this.method_12(false);
							this.method_13(false);
							this.method_11(false);
							base.Columns[23].Visible = false;
							base.Columns[34].Visible = false;
							base.Columns[35].Visible = false;
							goto IL_296;
						}
						goto IL_296;
					}
				}
				this.method_10(false);
				this.method_11(false);
				if (mktDgvType == Enum1.const_1)
				{
					base.Columns[5].Visible = false;
				}
				IL_296:;
			}
		}

		// Token: 0x06001573 RID: 5491 RVA: 0x0008E0C4 File Offset: 0x0008C2C4
		public void method_9(bool bool_2)
		{
			base.Columns[3].Visible = bool_2;
			base.Columns[4].Visible = bool_2;
			base.Columns[5].Visible = bool_2;
			base.Columns[6].Visible = bool_2;
			base.Columns[7].Visible = bool_2;
			base.Columns[8].Visible = bool_2;
			base.Columns[9].Visible = bool_2;
			base.Columns[10].Visible = bool_2;
			base.Columns[11].Visible = bool_2;
			base.Columns[13].Visible = bool_2;
		}

		// Token: 0x06001574 RID: 5492 RVA: 0x0008E18C File Offset: 0x0008C38C
		public void method_10(bool bool_2)
		{
			base.Columns[20].Visible = bool_2;
			base.Columns[21].Visible = bool_2;
			base.Columns[22].Visible = bool_2;
			base.Columns[23].Visible = bool_2;
			base.Columns[24].Visible = bool_2;
			base.Columns[25].Visible = bool_2;
			base.Columns[26].Visible = bool_2;
			base.Columns[27].Visible = bool_2;
			base.Columns[29].Visible = bool_2;
			base.Columns[30].Visible = bool_2;
			base.Columns[31].Visible = bool_2;
			base.Columns[32].Visible = bool_2;
			base.Columns[33].Visible = bool_2;
			base.Columns[34].Visible = bool_2;
			base.Columns[35].Visible = bool_2;
			base.Columns[37].Visible = bool_2;
			base.Columns[38].Visible = bool_2;
			base.Columns[39].Visible = bool_2;
		}

		// Token: 0x06001575 RID: 5493 RVA: 0x0008E2F4 File Offset: 0x0008C4F4
		public void method_11(bool bool_2)
		{
			base.Columns[40].Visible = bool_2;
			base.Columns[41].Visible = bool_2;
			base.Columns[42].Visible = bool_2;
			base.Columns[43].Visible = bool_2;
			base.Columns[44].Visible = bool_2;
			base.Columns[45].Visible = bool_2;
			base.Columns[46].Visible = bool_2;
			base.Columns[47].Visible = bool_2;
			base.Columns[48].Visible = bool_2;
			base.Columns[49].Visible = bool_2;
			base.Columns[50].Visible = bool_2;
			base.Columns[51].Visible = bool_2;
		}

		// Token: 0x06001576 RID: 5494 RVA: 0x0008E3E8 File Offset: 0x0008C5E8
		private void method_12(bool bool_2)
		{
			base.Columns[22].Visible = bool_2;
			base.Columns[24].Visible = bool_2;
			base.Columns[25].Visible = bool_2;
			base.Columns[26].Visible = bool_2;
			base.Columns[27].Visible = bool_2;
		}

		// Token: 0x06001577 RID: 5495 RVA: 0x0008E458 File Offset: 0x0008C658
		private void method_13(bool bool_2)
		{
			base.Columns[29].Visible = bool_2;
			base.Columns[32].Visible = bool_2;
			base.Columns[37].Visible = bool_2;
			base.Columns[38].Visible = bool_2;
		}

		// Token: 0x06001578 RID: 5496 RVA: 0x0008E4B4 File Offset: 0x0008C6B4
		public string[] method_14()
		{
			string[] result = null;
			if (this.SourceMktSymbLst != null)
			{
				List<ShowMktSymb> sourceMktSymbLst = this.SourceMktSymbLst;
				if (sourceMktSymbLst != null && sourceMktSymbLst.Count > 0)
				{
					result = sourceMktSymbLst.Select(new Func<ShowMktSymb, string>(DataGridViewMkt.<>c.<>9.method_0)).ToArray<string>();
				}
			}
			return result;
		}

		// Token: 0x06001579 RID: 5497 RVA: 0x000088E8 File Offset: 0x00006AE8
		private void DataGridViewMkt_ParentChanged(object sender, EventArgs e)
		{
			if (base.Parent != null)
			{
				base.Parent.SizeChanged += this.method_15;
				this.method_16();
			}
		}

		// Token: 0x0600157A RID: 5498 RVA: 0x00008911 File Offset: 0x00006B11
		private void method_15(object sender, EventArgs e)
		{
			this.method_16();
		}

		// Token: 0x0600157B RID: 5499 RVA: 0x0008E510 File Offset: 0x0008C710
		public void method_16()
		{
			DataGridViewAutoSizeColumnsMode dataGridViewAutoSizeColumnsMode = this.method_17();
			if (base.AutoSizeColumnsMode != dataGridViewAutoSizeColumnsMode)
			{
				base.AutoSizeColumnsMode = dataGridViewAutoSizeColumnsMode;
			}
		}

		// Token: 0x0600157C RID: 5500 RVA: 0x0008E538 File Offset: 0x0008C738
		private DataGridViewAutoSizeColumnsMode method_17()
		{
			DataGridViewAutoSizeColumnsMode result;
			if (base.Parent.Width > this.AutoSizeColumnsParentWidthThreshold)
			{
				result = DataGridViewAutoSizeColumnsMode.Fill;
			}
			else
			{
				result = DataGridViewAutoSizeColumnsMode.None;
			}
			return result;
		}

		// Token: 0x17000385 RID: 901
		// (get) Token: 0x0600157D RID: 5501 RVA: 0x0008E564 File Offset: 0x0008C764
		// (set) Token: 0x0600157E RID: 5502 RVA: 0x0000891B File Offset: 0x00006B1B
		public Enum1 MktDgvType
		{
			get
			{
				return this.enum1_0;
			}
			set
			{
				this.enum1_0 = value;
			}
		}

		// Token: 0x17000386 RID: 902
		// (get) Token: 0x0600157F RID: 5503 RVA: 0x0008E57C File Offset: 0x0008C77C
		// (set) Token: 0x06001580 RID: 5504 RVA: 0x00008926 File Offset: 0x00006B26
		public List<ShowMktSymb> SourceMktSymbLst { get; set; }

		// Token: 0x17000387 RID: 903
		// (get) Token: 0x06001581 RID: 5505 RVA: 0x0008E594 File Offset: 0x0008C794
		// (set) Token: 0x06001582 RID: 5506 RVA: 0x00008931 File Offset: 0x00006B31
		public string[] IdxClassAry { get; set; }

		// Token: 0x17000388 RID: 904
		// (get) Token: 0x06001583 RID: 5507 RVA: 0x0008E5AC File Offset: 0x0008C7AC
		// (set) Token: 0x06001584 RID: 5508 RVA: 0x0000893C File Offset: 0x00006B3C
		public int AutoSizeColumnsParentWidthThreshold { get; set; }

		// Token: 0x04000B04 RID: 2820
		private Enum1 enum1_0;

		// Token: 0x04000B05 RID: 2821
		[CompilerGenerated]
		private List<ShowMktSymb> list_0;

		// Token: 0x04000B06 RID: 2822
		[CompilerGenerated]
		private string[] string_0;

		// Token: 0x04000B07 RID: 2823
		[CompilerGenerated]
		private int int_0;

		// Token: 0x0200020C RID: 524
		[CompilerGenerated]
		private sealed class Class288
		{
			// Token: 0x06001586 RID: 5510 RVA: 0x00008947 File Offset: 0x00006B47
			internal void method_0()
			{
				this.dataGridViewMkt_0.DataSource = this.sortableBindingList_0;
				this.dataGridViewMkt_0.vmethod_3();
			}

			// Token: 0x04000B08 RID: 2824
			public DataGridViewMkt dataGridViewMkt_0;

			// Token: 0x04000B09 RID: 2825
			public SortableBindingList<ShowMktSymb> sortableBindingList_0;
		}
	}
}
