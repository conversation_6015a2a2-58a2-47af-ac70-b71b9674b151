﻿using System;
using System.Data;
using System.Runtime.CompilerServices;
using ns16;

namespace ns13
{
	// Token: 0x02000036 RID: 54
	internal sealed class Class15
	{
		// Token: 0x06000176 RID: 374 RVA: 0x00003062 File Offset: 0x00001262
		public Class15(Enum2 enum2_1, string string_2, string string_3, DataTable dataTable_3, DataTable dataTable_4, DataTable dataTable_5)
		{
			this.ReportAnlysType = enum2_1;
			this.ts_code = string_2;
			this.end_date = string_3;
			this.SrcDataTable = dataTable_3;
			this.SrcYrDataTable = dataTable_4;
			this.SrcQtDataTable = dataTable_5;
		}

		// Token: 0x17000065 RID: 101
		// (get) Token: 0x06000177 RID: 375 RVA: 0x00016C30 File Offset: 0x00014E30
		// (set) Token: 0x06000178 RID: 376 RVA: 0x00003099 File Offset: 0x00001299
		public Enum2 ReportAnlysType { get; set; }

		// Token: 0x17000066 RID: 102
		// (get) Token: 0x06000179 RID: 377 RVA: 0x00016C48 File Offset: 0x00014E48
		// (set) Token: 0x0600017A RID: 378 RVA: 0x000030A4 File Offset: 0x000012A4
		public string ts_code { get; set; }

		// Token: 0x17000067 RID: 103
		// (get) Token: 0x0600017B RID: 379 RVA: 0x00016C60 File Offset: 0x00014E60
		// (set) Token: 0x0600017C RID: 380 RVA: 0x000030AF File Offset: 0x000012AF
		public string end_date { get; set; }

		// Token: 0x17000068 RID: 104
		// (get) Token: 0x0600017D RID: 381 RVA: 0x00016C78 File Offset: 0x00014E78
		// (set) Token: 0x0600017E RID: 382 RVA: 0x000030BA File Offset: 0x000012BA
		public DataTable SrcDataTable { get; set; }

		// Token: 0x17000069 RID: 105
		// (get) Token: 0x0600017F RID: 383 RVA: 0x00016C90 File Offset: 0x00014E90
		// (set) Token: 0x06000180 RID: 384 RVA: 0x000030C5 File Offset: 0x000012C5
		public DataTable SrcYrDataTable { get; set; }

		// Token: 0x1700006A RID: 106
		// (get) Token: 0x06000181 RID: 385 RVA: 0x00016CA8 File Offset: 0x00014EA8
		// (set) Token: 0x06000182 RID: 386 RVA: 0x000030D0 File Offset: 0x000012D0
		public DataTable SrcQtDataTable { get; set; }

		// Token: 0x04000092 RID: 146
		[CompilerGenerated]
		private Enum2 enum2_0;

		// Token: 0x04000093 RID: 147
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04000094 RID: 148
		[CompilerGenerated]
		private string string_1;

		// Token: 0x04000095 RID: 149
		[CompilerGenerated]
		private DataTable dataTable_0;

		// Token: 0x04000096 RID: 150
		[CompilerGenerated]
		private DataTable dataTable_1;

		// Token: 0x04000097 RID: 151
		[CompilerGenerated]
		private DataTable dataTable_2;
	}
}
