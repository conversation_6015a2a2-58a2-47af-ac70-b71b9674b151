﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using System.Xml.Linq;
using ns11;
using ns14;
using ns15;
using ns28;
using ns32;
using ns6;
using TEx;

namespace ns24
{
	// Token: 0x02000179 RID: 377
	internal sealed partial class HotKeyCfgForm : Form
	{
		// Token: 0x14000075 RID: 117
		// (add) Token: 0x06000E2B RID: 3627 RVA: 0x0005A4F8 File Offset: 0x000586F8
		// (remove) Token: 0x06000E2C RID: 3628 RVA: 0x0005A530 File Offset: 0x00058730
		public event EventHandler HotKeyCfgChanging
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000E2D RID: 3629 RVA: 0x0005A568 File Offset: 0x00058768
		protected void method_0()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x14000076 RID: 118
		// (add) Token: 0x06000E2E RID: 3630 RVA: 0x0005A590 File Offset: 0x00058790
		// (remove) Token: 0x06000E2F RID: 3631 RVA: 0x0005A5C8 File Offset: 0x000587C8
		public event EventHandler HotKeyCfgChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000E30 RID: 3632 RVA: 0x0005A600 File Offset: 0x00058800
		protected void method_1()
		{
			EventHandler eventHandler = this.eventHandler_1;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x06000E31 RID: 3633 RVA: 0x0005A628 File Offset: 0x00058828
		public HotKeyCfgForm()
		{
			this.InitializeComponent();
			base.Load += this.HotKeyCfgForm_Load;
			this.nfBtn_Cancel = new Class306();
			this.nfBtn_Cancel.DialogResult = DialogResult.Cancel;
			this.nfBtn_Cancel.Name = "nfBtn_Cancel";
			this.nfBtn_Cancel.TabIndex = this.button_OK.TabIndex + 1;
			this.nfBtn_Cancel.Text = "取消";
			this.nfBtn_Cancel.UseVisualStyleBackColor = true;
			this.nfBtn_Cancel.Visible = true;
			base.Controls.Add(this.nfBtn_Cancel);
			base.CancelButton = this.nfBtn_Cancel;
		}

		// Token: 0x06000E32 RID: 3634 RVA: 0x0005A6DC File Offset: 0x000588DC
		private void HotKeyCfgForm_Load(object sender, EventArgs e)
		{
			List<Class278> usrHotKeyList = Class208.UsrHotKeyList;
			List<Class264> usrFnKeyList = Class184.UsrFnKeyList;
			this.list_0 = new List<HotKeyEditCtrl>();
			int x = 20;
			int num = 5;
			for (int i = 0; i < usrHotKeyList.Count; i++)
			{
				HotKeyEditCtrl hotKeyEditCtrl = new HotKeyEditCtrl(usrHotKeyList[i]);
				hotKeyEditCtrl.Location = new Point(x, num);
				this.panel1.Controls.Add(hotKeyEditCtrl);
				this.list_0.Add(hotKeyEditCtrl);
				num += hotKeyEditCtrl.Height;
			}
			for (int j = 0; j < usrFnKeyList.Count; j++)
			{
				HotKeyEditCtrl hotKeyEditCtrl2 = new HotKeyEditCtrl(usrFnKeyList[j]);
				hotKeyEditCtrl2.Location = new Point(x, num);
				this.panel1.Controls.Add(hotKeyEditCtrl2);
				this.list_0.Add(hotKeyEditCtrl2);
				num += hotKeyEditCtrl2.Height;
			}
			foreach (HotKeyEditCtrl hotKeyEditCtrl3 in this.list_0)
			{
				hotKeyEditCtrl3.InputBox.Leave += this.method_2;
			}
			this.nfBtn_Cancel.Size = this.button_OK.Size;
			this.nfBtn_Cancel.Location = new Point(this.button_OK.Location.X + this.button_OK.Width + Convert.ToInt32(Math.Ceiling(this.button_OK.Width / 8m)), this.button_OK.Location.Y);
		}

		// Token: 0x06000E33 RID: 3635 RVA: 0x0005A894 File Offset: 0x00058A94
		private void method_2(object sender, EventArgs e)
		{
			TextBox textBox = sender as TextBox;
			foreach (HotKeyEditCtrl hotKeyEditCtrl in this.list_0)
			{
				string text = textBox.Text.Trim();
				if (hotKeyEditCtrl.InputBox != textBox && hotKeyEditCtrl.InputBox.Text.Trim() == text && text != string.Empty)
				{
					MessageBox.Show("该按键已被占用，请重新输入！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					textBox.Focus();
					break;
				}
			}
		}

		// Token: 0x06000E34 RID: 3636 RVA: 0x0005A940 File Offset: 0x00058B40
		private void button_OK_Click(object sender, EventArgs e)
		{
			XDocument xdocument = new XDocument();
			XElement xelement = new XElement("ShortCutKeys");
			XElement xelement2 = new XElement("HotKeys");
			XElement xelement3 = new XElement("FnKeys");
			List<Class278> list = new List<Class278>();
			List<Class264> list2 = new List<Class264>();
			this.method_0();
			foreach (HotKeyEditCtrl hotKeyEditCtrl in this.list_0)
			{
				if (hotKeyEditCtrl.IsHotKey)
				{
					XElement xelement4 = new XElement("HotKey");
					hotKeyEditCtrl.HotKey.KeyModifier = hotKeyEditCtrl.Modifier;
					hotKeyEditCtrl.HotKey.Key = hotKeyEditCtrl.Key;
					xelement4.SetAttributeValue("Id", hotKeyEditCtrl.HotKey.Id);
					xelement4.SetAttributeValue("Modifier", Convert.ToString((int)hotKeyEditCtrl.Modifier));
					xelement4.SetAttributeValue("Key", Convert.ToString((int)hotKeyEditCtrl.Key));
					xelement2.Add(xelement4);
					list.Add(hotKeyEditCtrl.HotKey);
				}
				else
				{
					XElement xelement5 = new XElement("FnKey");
					hotKeyEditCtrl.FnKey.KeyStr = hotKeyEditCtrl.FnKeyStr.Trim();
					xelement5.SetAttributeValue("Id", hotKeyEditCtrl.FnKey.Id);
					xelement5.SetAttributeValue("KeyStr", hotKeyEditCtrl.FnKey.KeyStr);
					xelement3.Add(xelement5);
					list2.Add(hotKeyEditCtrl.FnKey);
				}
			}
			xelement.Add(xelement2);
			xelement.Add(xelement3);
			xdocument.Add(xelement);
			xdocument.Save(Base.UI.smethod_43());
			Class208.UsrHotKeyList = list;
			Class184.UsrFnKeyList = list2;
			Base.UI.smethod_164();
			this.method_1();
			base.Dispose();
		}

		// Token: 0x06000E35 RID: 3637 RVA: 0x000064CE File Offset: 0x000046CE
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000761 RID: 1889
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000762 RID: 1890
		[CompilerGenerated]
		private EventHandler eventHandler_1;

		// Token: 0x04000763 RID: 1891
		private List<HotKeyEditCtrl> list_0;

		// Token: 0x04000764 RID: 1892
		private Class306 nfBtn_Cancel;

		// Token: 0x04000765 RID: 1893
		private IContainer icontainer_0;
	}
}
