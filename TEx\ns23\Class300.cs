﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using Newtonsoft.Json;
using ns28;
using ns3;
using TEx;
using TEx.Util;

namespace ns23
{
	// Token: 0x02000228 RID: 552
	internal sealed class Class300
	{
		// Token: 0x170003B9 RID: 953
		// (get) Token: 0x060016EA RID: 5866 RVA: 0x0009AC7C File Offset: 0x00098E7C
		// (set) Token: 0x060016EB RID: 5867 RVA: 0x000095AC File Offset: 0x000077AC
		public string CurrGrpName { get; set; }

		// Token: 0x170003BA RID: 954
		// (get) Token: 0x060016EC RID: 5868 RVA: 0x0009AC94 File Offset: 0x00098E94
		// (set) Token: 0x060016ED RID: 5869 RVA: 0x000095B7 File Offset: 0x000077B7
		public List<Class301> CondGroups { get; set; }

		// Token: 0x170003BB RID: 955
		// (get) Token: 0x060016EE RID: 5870 RVA: 0x0009ACAC File Offset: 0x00098EAC
		// (set) Token: 0x060016EF RID: 5871 RVA: 0x000095C2 File Offset: 0x000077C2
		public List<FilterCond> LastConds { get; set; }

		// Token: 0x170003BC RID: 956
		// (get) Token: 0x060016F0 RID: 5872 RVA: 0x0009ACC4 File Offset: 0x00098EC4
		// (set) Token: 0x060016F1 RID: 5873 RVA: 0x000095CD File Offset: 0x000077CD
		public string LastRsltCsv { get; set; }

		// Token: 0x060016F2 RID: 5874 RVA: 0x0009ACDC File Offset: 0x00098EDC
		public List<FilterCond> method_0()
		{
			List<FilterCond> result = null;
			if (this.CondGroups != null && !string.IsNullOrEmpty(this.CurrGrpName))
			{
				Class301 @class = this.CondGroups.SingleOrDefault(new Func<Class301, bool>(this.method_2));
				if (@class != null)
				{
					result = @class.FilterConds;
				}
			}
			return result;
		}

		// Token: 0x060016F3 RID: 5875 RVA: 0x0009AD28 File Offset: 0x00098F28
		public bool method_1()
		{
			bool result = true;
			string filePath = Class300.smethod_0();
			try
			{
				string content = JsonConvert.SerializeObject(this, Formatting.Indented);
				Utility.SaveFile(filePath, content, null);
			}
			catch (Exception exception_)
			{
				result = false;
				Class182.smethod_0(exception_);
			}
			return result;
		}

		// Token: 0x060016F4 RID: 5876 RVA: 0x0009AD70 File Offset: 0x00098F70
		public static string smethod_0()
		{
			return Base.UI.smethod_44("fltconds.dat");
		}

		// Token: 0x060016F5 RID: 5877 RVA: 0x0009AD8C File Offset: 0x00098F8C
		public static Class300 smethod_1()
		{
			Class300 result = null;
			string text = Class300.smethod_0();
			if (new FileInfo(text).Exists)
			{
				try
				{
					result = JsonConvert.DeserializeObject<Class300>(File.ReadAllText(text));
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
			return result;
		}

		// Token: 0x060016F7 RID: 5879 RVA: 0x0009ADD8 File Offset: 0x00098FD8
		[CompilerGenerated]
		private bool method_2(Class301 class301_0)
		{
			return class301_0.Name.Equals(this.CurrGrpName, StringComparison.InvariantCultureIgnoreCase);
		}

		// Token: 0x04000BB1 RID: 2993
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04000BB2 RID: 2994
		[CompilerGenerated]
		private List<Class301> list_0;

		// Token: 0x04000BB3 RID: 2995
		[CompilerGenerated]
		private List<FilterCond> list_1;

		// Token: 0x04000BB4 RID: 2996
		[CompilerGenerated]
		private string string_1;
	}
}
