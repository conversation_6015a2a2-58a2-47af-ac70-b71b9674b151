﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns6;
using TEx;

namespace ns29
{
	// Token: 0x0200020E RID: 526
	[ToolboxBitmap(typeof(ListBox))]
	[DesignerCategory("")]
	internal sealed class Class289 : ListBox
	{
		// Token: 0x0600158A RID: 5514 RVA: 0x00008975 File Offset: 0x00006B75
		public Class289()
		{
			base.SetStyle(ControlStyles.OptimizedDoubleBuffer, true);
			this.DrawMode = System.Windows.Forms.DrawMode.OwnerDrawFixed;
			base.DrawItem += this.Class289_DrawItem;
		}

		// Token: 0x0600158B RID: 5515 RVA: 0x0008E5DC File Offset: 0x0008C7DC
		public void method_0()
		{
			this.SelectionBackColor = this.method_1();
			this.ForeColor = Base.UI.smethod_35();
			if (base.Parent != null)
			{
				this.BackColor = base.Parent.BackColor;
			}
			else
			{
				Color backColor = Color.FromArgb(206, 210, 217);
				if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
				{
					backColor = Class179.color_3;
				}
				this.BackColor = backColor;
			}
			this.Refresh();
		}

		// Token: 0x0600158C RID: 5516 RVA: 0x0008E654 File Offset: 0x0008C854
		private Brush method_1()
		{
			SolidBrush result;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				result = new SolidBrush(Color.FromArgb(86, 86, 86));
			}
			else
			{
				result = new SolidBrush(Color.FromKnownColor(KnownColor.Control));
			}
			return result;
		}

		// Token: 0x0600158D RID: 5517 RVA: 0x0008E694 File Offset: 0x0008C894
		private void Class289_DrawItem(object sender, DrawItemEventArgs e)
		{
			if (base.Items.Count > 0)
			{
				e.DrawBackground();
				Graphics graphics = e.Graphics;
				bool flag = (e.State & DrawItemState.Selected) == DrawItemState.Selected;
				Brush brush = flag ? this.SelectionBackColor : new SolidBrush(e.BackColor);
				int num = 0;
				double num2 = (double)(this.ItemHeight / 2) - 10.0;
				if (num2 > 0.0)
				{
					num = Convert.ToInt32(Math.Round(num2));
				}
				Rectangle r = new Rectangle(e.Bounds.X, e.Bounds.Y + num, e.Bounds.Width, e.Bounds.Height);
				graphics.FillRectangle(brush, e.Bounds);
				e.DrawFocusRectangle();
				FontStyle style = flag ? FontStyle.Bold : FontStyle.Regular;
				Font font = new Font(e.Font.FontFamily, e.Font.Size, style);
				string text = base.Items[e.Index].ToString();
				if (flag && !string.IsNullOrEmpty(this.SelectionAddtionalString))
				{
					text += this.SelectionAddtionalString;
				}
				e.Graphics.DrawString(text, font, new SolidBrush(this.ForeColor), r, StringFormat.GenericDefault);
			}
		}

		// Token: 0x17000389 RID: 905
		// (get) Token: 0x0600158E RID: 5518 RVA: 0x0008E7F0 File Offset: 0x0008C9F0
		// (set) Token: 0x0600158F RID: 5519 RVA: 0x000089A4 File Offset: 0x00006BA4
		public Brush SelectionBackColor { get; set; }

		// Token: 0x1700038A RID: 906
		// (get) Token: 0x06001590 RID: 5520 RVA: 0x0008E808 File Offset: 0x0008CA08
		// (set) Token: 0x06001591 RID: 5521 RVA: 0x000089AF File Offset: 0x00006BAF
		public string SelectionAddtionalString { get; set; }

		// Token: 0x04000B0C RID: 2828
		[CompilerGenerated]
		private Brush brush_0;

		// Token: 0x04000B0D RID: 2829
		[CompilerGenerated]
		private string string_0;
	}
}
