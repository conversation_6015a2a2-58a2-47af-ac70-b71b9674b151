﻿using System;
using System.Drawing;

namespace ns8
{
	// Token: 0x0200016A RID: 362
	internal sealed class EventArgs8 : EventArgs
	{
		// Token: 0x06000DC0 RID: 3520 RVA: 0x000061DA File Offset: 0x000043DA
		public EventArgs8(string string_1, Font font_0, Color? nullable_1) : this(string_1, font_0, nullable_1, true)
		{
		}

		// Token: 0x06000DC1 RID: 3521 RVA: 0x000061E6 File Offset: 0x000043E6
		public EventArgs8(string string_1, object object_1, Color? nullable_1, bool bool_1)
		{
			this.string_0 = string_1;
			this.object_0 = object_1;
			this.nullable_0 = nullable_1;
			this.bool_0 = bool_1;
		}

		// Token: 0x17000225 RID: 549
		// (get) Token: 0x06000DC2 RID: 3522 RVA: 0x00056BCC File Offset: 0x00054DCC
		public string Text
		{
			get
			{
				return this.string_0;
			}
		}

		// Token: 0x17000226 RID: 550
		// (get) Token: 0x06000DC3 RID: 3523 RVA: 0x00056BE4 File Offset: 0x00054DE4
		public Color? Color
		{
			get
			{
				return this.nullable_0;
			}
		}

		// Token: 0x17000227 RID: 551
		// (get) Token: 0x06000DC4 RID: 3524 RVA: 0x00056BFC File Offset: 0x00054DFC
		public object Font
		{
			get
			{
				return this.object_0;
			}
		}

		// Token: 0x17000228 RID: 552
		// (get) Token: 0x06000DC5 RID: 3525 RVA: 0x00056C14 File Offset: 0x00054E14
		public bool IfCreateNew
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x0400070C RID: 1804
		private readonly string string_0;

		// Token: 0x0400070D RID: 1805
		private readonly Color? nullable_0;

		// Token: 0x0400070E RID: 1806
		private readonly object object_0;

		// Token: 0x0400070F RID: 1807
		private readonly bool bool_0;
	}
}
