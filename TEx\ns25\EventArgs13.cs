﻿using System;

namespace ns25
{
	// Token: 0x02000248 RID: 584
	internal sealed class EventArgs13 : EventArgs
	{
		// Token: 0x060018D0 RID: 6352 RVA: 0x0000A308 File Offset: 0x00008508
		public EventArgs13(int int_1, bool bool_1)
		{
			this.int_0 = int_1;
			this.bool_0 = bool_1;
		}

		// Token: 0x17000426 RID: 1062
		// (get) Token: 0x060018D1 RID: 6353 RVA: 0x000A56F8 File Offset: 0x000A38F8
		public int NewAcctID
		{
			get
			{
				return this.int_0;
			}
		}

		// Token: 0x17000427 RID: 1063
		// (get) Token: 0x060018D2 RID: 6354 RVA: 0x000A5710 File Offset: 0x000A3910
		public bool IfSwitchToNewAcct
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x04000C67 RID: 3175
		private int int_0;

		// Token: 0x04000C68 RID: 3176
		private bool bool_0;
	}
}
