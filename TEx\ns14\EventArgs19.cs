﻿using System;

namespace ns14
{
	// Token: 0x02000276 RID: 630
	internal sealed class EventArgs19 : EventArgs
	{
		// Token: 0x06001B7A RID: 7034 RVA: 0x0000B538 File Offset: 0x00009738
		public EventArgs19(bool bool_1)
		{
			this.bool_0 = bool_1;
		}

		// Token: 0x1700047A RID: 1146
		// (get) Token: 0x06001B7B RID: 7035 RVA: 0x000B9B84 File Offset: 0x000B7D84
		// (set) Token: 0x06001B7C RID: 7036 RVA: 0x0000B549 File Offset: 0x00009749
		public bool Cancel
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x04000D96 RID: 3478
		private bool bool_0;
	}
}
