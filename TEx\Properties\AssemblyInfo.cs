﻿using System;
using System.Configuration.Assemblies;
using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

[assembly: AssemblyAlgorithmId(AssemblyHashAlgorithm.None)]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyTitle("TEx")]
[assembly: AssemblyDescription("交易练习者")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("TEx Studio")]
[assembly: AssemblyProduct("交易练习者")]
[assembly: AssemblyCopyright("Copyright © TEx Studio, 2025. All rights reserved.")]
[assembly: AssemblyTrademark("")]
[assembly: ComVisible(false)]
[assembly: Guid("6fecd38d-8f42-433f-9163-09177d3e02b0")]
[assembly: AssemblyFileVersion("*******")]
