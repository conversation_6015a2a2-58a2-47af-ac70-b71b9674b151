﻿namespace ns17
{
	// Token: 0x02000096 RID: 150
	internal sealed partial class ResetDataWnd : global::System.Windows.Forms.Form
	{
		// Token: 0x06000502 RID: 1282 RVA: 0x00026DD4 File Offset: 0x00024FD4
		private void InitializeComponent()
		{
			this.radioBtn_FmUISettings = new global::System.Windows.Forms.RadioButton();
			this.radioBtn_Pages = new global::System.Windows.Forms.RadioButton();
			this.radioBtn_DrawObjs = new global::System.Windows.Forms.RadioButton();
			this.radioBtn_AllData = new global::System.Windows.Forms.RadioButton();
			this.label_Notes = new global::DevComponents.DotNetBar.LabelX();
			this.button_Cancel = new global::System.Windows.Forms.Button();
			this.button_OK = new global::System.Windows.Forms.Button();
			this.panel1 = new global::System.Windows.Forms.Panel();
			this.radioBtn_LocalHdData = new global::System.Windows.Forms.RadioButton();
			this.panel1.SuspendLayout();
			base.SuspendLayout();
			this.radioBtn_FmUISettings.AutoSize = true;
			this.radioBtn_FmUISettings.Checked = true;
			this.radioBtn_FmUISettings.Location = new global::System.Drawing.Point(16, 15);
			this.radioBtn_FmUISettings.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.radioBtn_FmUISettings.Name = "radioBtn_FmUISettings";
			this.radioBtn_FmUISettings.Size = new global::System.Drawing.Size(88, 19);
			this.radioBtn_FmUISettings.TabIndex = 0;
			this.radioBtn_FmUISettings.TabStop = true;
			this.radioBtn_FmUISettings.Text = "参数设置";
			this.radioBtn_FmUISettings.UseVisualStyleBackColor = true;
			this.radioBtn_Pages.AutoSize = true;
			this.radioBtn_Pages.Location = new global::System.Drawing.Point(119, 15);
			this.radioBtn_Pages.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.radioBtn_Pages.Name = "radioBtn_Pages";
			this.radioBtn_Pages.Size = new global::System.Drawing.Size(88, 19);
			this.radioBtn_Pages.TabIndex = 1;
			this.radioBtn_Pages.TabStop = true;
			this.radioBtn_Pages.Text = "页面设置";
			this.radioBtn_Pages.UseVisualStyleBackColor = true;
			this.radioBtn_DrawObjs.AutoSize = true;
			this.radioBtn_DrawObjs.Location = new global::System.Drawing.Point(222, 15);
			this.radioBtn_DrawObjs.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.radioBtn_DrawObjs.Name = "radioBtn_DrawObjs";
			this.radioBtn_DrawObjs.Size = new global::System.Drawing.Size(88, 19);
			this.radioBtn_DrawObjs.TabIndex = 2;
			this.radioBtn_DrawObjs.TabStop = true;
			this.radioBtn_DrawObjs.Text = "画线数据";
			this.radioBtn_DrawObjs.UseVisualStyleBackColor = true;
			this.radioBtn_AllData.AutoSize = true;
			this.radioBtn_AllData.Location = new global::System.Drawing.Point(428, 15);
			this.radioBtn_AllData.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.radioBtn_AllData.Name = "radioBtn_AllData";
			this.radioBtn_AllData.Size = new global::System.Drawing.Size(88, 19);
			this.radioBtn_AllData.TabIndex = 4;
			this.radioBtn_AllData.TabStop = true;
			this.radioBtn_AllData.Text = "所有数据";
			this.radioBtn_AllData.UseVisualStyleBackColor = true;
			this.label_Notes.BackgroundStyle.CornerType = global::DevComponents.DotNetBar.eCornerType.Square;
			this.label_Notes.ForeColor = global::System.Drawing.Color.Black;
			this.label_Notes.Location = new global::System.Drawing.Point(60, 140);
			this.label_Notes.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.label_Notes.Name = "label_Notes";
			this.label_Notes.PaddingLeft = 2;
			this.label_Notes.Size = new global::System.Drawing.Size(549, 112);
			this.label_Notes.TabIndex = 3;
			this.label_Notes.Text = "重置账户系统参数设置为系统默认值，包括图表显示、界面及交易参数等。";
			this.label_Notes.TextLineAlignment = global::System.Drawing.StringAlignment.Near;
			this.label_Notes.WordWrap = true;
			this.button_Cancel.DialogResult = global::System.Windows.Forms.DialogResult.Cancel;
			this.button_Cancel.Location = new global::System.Drawing.Point(468, 279);
			this.button_Cancel.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_Cancel.Name = "button_Cancel";
			this.button_Cancel.Size = new global::System.Drawing.Size(116, 33);
			this.button_Cancel.TabIndex = 1;
			this.button_Cancel.Text = "取消";
			this.button_Cancel.UseVisualStyleBackColor = true;
			this.button_OK.Location = new global::System.Drawing.Point(339, 279);
			this.button_OK.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_OK.Name = "button_OK";
			this.button_OK.Size = new global::System.Drawing.Size(116, 33);
			this.button_OK.TabIndex = 0;
			this.button_OK.Text = "确定";
			this.button_OK.UseVisualStyleBackColor = true;
			this.panel1.Controls.Add(this.radioBtn_LocalHdData);
			this.panel1.Controls.Add(this.radioBtn_AllData);
			this.panel1.Controls.Add(this.radioBtn_FmUISettings);
			this.panel1.Controls.Add(this.radioBtn_DrawObjs);
			this.panel1.Controls.Add(this.radioBtn_Pages);
			this.panel1.Location = new global::System.Drawing.Point(42, 46);
			this.panel1.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.panel1.Name = "panel1";
			this.panel1.Size = new global::System.Drawing.Size(547, 49);
			this.panel1.TabIndex = 2;
			this.radioBtn_LocalHdData.AutoSize = true;
			this.radioBtn_LocalHdData.Location = new global::System.Drawing.Point(325, 15);
			this.radioBtn_LocalHdData.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.radioBtn_LocalHdData.Name = "radioBtn_LocalHdData";
			this.radioBtn_LocalHdData.Size = new global::System.Drawing.Size(88, 19);
			this.radioBtn_LocalHdData.TabIndex = 3;
			this.radioBtn_LocalHdData.TabStop = true;
			this.radioBtn_LocalHdData.Text = "行情数据";
			this.radioBtn_LocalHdData.UseVisualStyleBackColor = true;
			base.AcceptButton = this.button_OK;
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(8f, 15f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			this.BackColor = global::System.Drawing.SystemColors.Control;
			base.CancelButton = this.button_Cancel;
			base.ClientSize = new global::System.Drawing.Size(617, 329);
			base.Controls.Add(this.label_Notes);
			base.Controls.Add(this.panel1);
			base.Controls.Add(this.button_Cancel);
			base.Controls.Add(this.button_OK);
			base.FormBorderStyle = global::System.Windows.Forms.FormBorderStyle.FixedDialog;
			base.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = "ResetDataWnd";
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.StartPosition = global::System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "数据重置";
			this.panel1.ResumeLayout(false);
			this.panel1.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x04000209 RID: 521
		private global::System.Windows.Forms.RadioButton radioBtn_FmUISettings;

		// Token: 0x0400020A RID: 522
		private global::System.Windows.Forms.RadioButton radioBtn_Pages;

		// Token: 0x0400020B RID: 523
		private global::System.Windows.Forms.RadioButton radioBtn_DrawObjs;

		// Token: 0x0400020C RID: 524
		private global::System.Windows.Forms.RadioButton radioBtn_AllData;

		// Token: 0x0400020D RID: 525
		private global::System.Windows.Forms.Button button_Cancel;

		// Token: 0x0400020E RID: 526
		private global::System.Windows.Forms.Button button_OK;

		// Token: 0x0400020F RID: 527
		private global::DevComponents.DotNetBar.LabelX label_Notes;

		// Token: 0x04000210 RID: 528
		private global::System.Windows.Forms.Panel panel1;

		// Token: 0x04000211 RID: 529
		private global::System.Windows.Forms.RadioButton radioBtn_LocalHdData;
	}
}
