﻿using System;
using System.Xml;

namespace ns24
{
	// Token: 0x0200040E RID: 1038
	internal sealed class Class544 : IDisposable
	{
		// Token: 0x06002820 RID: 10272 RVA: 0x0000F85E File Offset: 0x0000DA5E
		public Class544(XmlWriter xmlWriter_1, string string_0)
		{
			this.xmlWriter_0 = xmlWriter_1;
			this.xmlWriter_0.WriteStartElement(string_0);
		}

		// Token: 0x06002821 RID: 10273 RVA: 0x0000F879 File Offset: 0x0000DA79
		public void Dispose()
		{
			this.xmlWriter_0.WriteEndElement();
		}

		// Token: 0x040013FD RID: 5117
		private readonly XmlWriter xmlWriter_0;
	}
}
