﻿using System;
using System.Net;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Security;
using System.Security.Permissions;
using System.Threading;
using System.Web.Services.Protocols;
using System.Windows.Forms;
using ns11;
using ns13;
using ns15;
using ns18;
using ns3;
using ns31;
using ns6;
using ns8;

namespace ns30
{
	// Token: 0x02000406 RID: 1030
	internal abstract class Class540
	{
		// Token: 0x140000C1 RID: 193
		// (add) Token: 0x060027EB RID: 10219 RVA: 0x00102D1C File Offset: 0x00100F1C
		// (remove) Token: 0x060027EC RID: 10220 RVA: 0x00102D54 File Offset: 0x00100F54
		public event EventHandler DebuggerLaunched
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x140000C2 RID: 194
		// (add) Token: 0x060027ED RID: 10221 RVA: 0x00102D8C File Offset: 0x00100F8C
		// (remove) Token: 0x060027EE RID: 10222 RVA: 0x00102DC4 File Offset: 0x00100FC4
		public event Delegate38 SendingReportFeedback
		{
			[CompilerGenerated]
			add
			{
				Delegate38 @delegate = this.delegate38_0;
				Delegate38 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate38 value2 = (Delegate38)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate38>(ref this.delegate38_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate38 @delegate = this.delegate38_0;
				Delegate38 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate38 value2 = (Delegate38)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate38>(ref this.delegate38_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x060027EF RID: 10223
		protected abstract void vmethod_0(EventArgs35 eventArgs35_0);

		// Token: 0x060027F0 RID: 10224
		protected abstract void vmethod_1(EventArgs34 eventArgs34_0);

		// Token: 0x060027F1 RID: 10225
		protected abstract void vmethod_2(EventArgs36 eventArgs36_0);

		// Token: 0x060027F2 RID: 10226 RVA: 0x0000F6AB File Offset: 0x0000D8AB
		[SecurityPermission(SecurityAction.Demand, UnmanagedCode = true)]
		public static void smethod_0(Class540 class540_1)
		{
			if (class540_1 != null)
			{
				Class540.class540_0 = class540_1;
				AppDomain.CurrentDomain.UnhandledException += class540_1.method_1;
				Application.ThreadException += class540_1.method_0;
			}
		}

		// Token: 0x170006DF RID: 1759
		// (get) Token: 0x060027F3 RID: 10227 RVA: 0x00102DFC File Offset: 0x00100FFC
		private static Class540 Handler
		{
			get
			{
				if (Class540.class540_0 == null)
				{
					foreach (Type type in Assembly.GetExecutingAssembly().GetTypes())
					{
						if (type != null && type.BaseType != null && type.BaseType == typeof(Class540))
						{
							try
							{
								Class540.class540_0 = (Class540)Activator.CreateInstance(type, true);
								if (Class540.class540_0 != null)
								{
									break;
								}
							}
							catch
							{
							}
						}
					}
				}
				return Class540.class540_0;
			}
		}

		// Token: 0x060027F4 RID: 10228 RVA: 0x00102E80 File Offset: 0x00101080
		public static void smethod_1(Exception exception_0, object[] object_0)
		{
			if (exception_0 != null && exception_0 is SecurityException && Class540.string_2 == "1" && Class540.Handler.method_3((SecurityException)exception_0))
			{
				return;
			}
			Class538.smethod_11(exception_0, object_0);
			Class540.Handler.method_4(exception_0, false, false);
		}

		// Token: 0x060027F5 RID: 10229 RVA: 0x0000F6DD File Offset: 0x0000D8DD
		public static void smethod_2(Exception exception_0)
		{
			if (exception_0 != null && exception_0 is SecurityException && Class540.string_2 == "1" && Class540.Handler.method_3((SecurityException)exception_0))
			{
				return;
			}
			Class540.Handler.method_4(exception_0, false, false);
		}

		// Token: 0x060027F6 RID: 10230 RVA: 0x00102ED0 File Offset: 0x001010D0
		public static Exception smethod_3(Exception exception_0, object[] object_0)
		{
			try
			{
				if (exception_0.GetType() == typeof(Exception) && exception_0.Message == "{report}")
				{
					exception_0 = exception_0.InnerException;
				}
				else
				{
					Class538.smethod_11(exception_0, object_0);
				}
				Class540.Handler.method_4(exception_0, true, false);
			}
			catch
			{
			}
			return new SoapException(exception_0.Message, SoapException.ServerFaultCode);
		}

		// Token: 0x060027F7 RID: 10231 RVA: 0x00102F44 File Offset: 0x00101144
		public static void smethod_4(Exception exception_0, object[] object_0)
		{
			try
			{
				if (exception_0.GetType() == typeof(Exception) && exception_0.Message == "{report}")
				{
					exception_0 = exception_0.InnerException;
				}
				else
				{
					Class538.smethod_11(exception_0, object_0);
				}
				Class540.Handler.method_4(exception_0, true, true);
			}
			catch
			{
			}
		}

		// Token: 0x060027F8 RID: 10232 RVA: 0x00102FA8 File Offset: 0x001011A8
		private void method_0(object sender, ThreadExceptionEventArgs e)
		{
			try
			{
				Exception ex = e.Exception;
				Type type = ex.GetType();
				if (type.Name == "UnhandledException" && type.Namespace == "SmartAssembly.SmartExceptionsCore")
				{
					ex = (Exception)type.GetField("PreviousException").GetValue(ex);
				}
				if (!(ex is SecurityException) || !(Class540.string_2 == "1") || !this.method_3(ex as SecurityException))
				{
					this.method_4(ex, true, false);
				}
			}
			catch
			{
			}
		}

		// Token: 0x060027F9 RID: 10233 RVA: 0x00103048 File Offset: 0x00101248
		private void method_1(object sender, UnhandledExceptionEventArgs e)
		{
			try
			{
				if (!(e.ExceptionObject is SecurityException) || !(Class540.string_2 == "1") || !this.method_3(e.ExceptionObject as SecurityException))
				{
					if (e.ExceptionObject is Exception)
					{
						this.method_4((Exception)e.ExceptionObject, !e.IsTerminating, false);
					}
				}
			}
			catch
			{
			}
		}

		// Token: 0x060027FA RID: 10234 RVA: 0x0000F71B File Offset: 0x0000D91B
		public void method_2(IWebProxy iwebProxy_1)
		{
			this.iwebProxy_0 = iwebProxy_1;
		}

		// Token: 0x060027FB RID: 10235 RVA: 0x0000F724 File Offset: 0x0000D924
		protected virtual Guid vmethod_3()
		{
			return Guid.Empty;
		}

		// Token: 0x060027FC RID: 10236 RVA: 0x001030C8 File Offset: 0x001012C8
		private bool method_3(SecurityException securityException_0)
		{
			EventArgs36 eventArgs = new EventArgs36(securityException_0);
			this.vmethod_2(eventArgs);
			if (eventArgs.ReportException)
			{
				return false;
			}
			if (!eventArgs.TryToContinue)
			{
				Application.Exit();
			}
			return true;
		}

		// Token: 0x060027FD RID: 10237 RVA: 0x001030FC File Offset: 0x001012FC
		private void method_4(Exception exception_0, bool bool_1, bool bool_2)
		{
			Type type = exception_0.GetType();
			if (type.Name == "UnhandledException" && type.Namespace == "SmartAssembly.SmartExceptionsCore")
			{
				exception_0 = (Exception)type.GetField("PreviousException").GetValue(exception_0);
			}
			bool flag = true;
			if (exception_0 != null && !(exception_0 is ThreadAbortException))
			{
				try
				{
					Class532 @class = new Class532(this.vmethod_3(), exception_0, this.iwebProxy_0);
					@class.SendingReportFeedback += this.method_7;
					@class.DebuggerLaunched += this.method_6;
					@class.FatalException += this.method_5;
					EventArgs35 eventArgs = new EventArgs35(@class, exception_0);
					if (Class510.smethod_0() != null)
					{
						eventArgs.method_1();
					}
					if (!bool_1)
					{
						eventArgs.method_0(false);
						eventArgs.TryToContinue = false;
					}
					else if (bool_2 || Class540.bool_0)
					{
						eventArgs.method_0(false);
						eventArgs.TryToContinue = true;
					}
					this.vmethod_0(eventArgs);
					flag = !eventArgs.TryToContinue;
				}
				catch (ThreadAbortException)
				{
				}
				catch (Exception exception_)
				{
					this.vmethod_1(new EventArgs34(exception_));
				}
				if (flag)
				{
					foreach (Assembly assembly in AppDomain.CurrentDomain.GetAssemblies())
					{
						try
						{
							string fullName = assembly.FullName;
							if (fullName.EndsWith("31bf3856ad364e35") && fullName.StartsWith("PresentationFramework,"))
							{
								object obj = assembly.GetType("System.Windows.Application").GetProperty("Current").GetGetMethod().Invoke(null, null);
								obj.GetType().GetMethod("Shutdown", new Type[0]).Invoke(obj, null);
							}
						}
						catch
						{
						}
					}
					try
					{
						Environment.ExitCode = -532462766;
						Application.Exit();
					}
					catch
					{
						try
						{
							Environment.Exit(-532462766);
						}
						catch
						{
						}
					}
				}
				return;
			}
		}

		// Token: 0x060027FE RID: 10238 RVA: 0x0000F72B File Offset: 0x0000D92B
		private void method_5(object sender, EventArgs34 e)
		{
			this.vmethod_1(e);
		}

		// Token: 0x060027FF RID: 10239 RVA: 0x00103314 File Offset: 0x00101514
		private void method_6(object sender, EventArgs e)
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(sender, e);
			}
		}

		// Token: 0x06002800 RID: 10240 RVA: 0x00103334 File Offset: 0x00101534
		private void method_7(object sender, EventArgs37 e)
		{
			Delegate38 @delegate = this.delegate38_0;
			if (@delegate != null)
			{
				@delegate(sender, e);
			}
		}

		// Token: 0x040013CF RID: 5071
		public const string string_0 = "{1fe9e38e-05cc-46a3-ae48-6cda8fb62056}";

		// Token: 0x040013D0 RID: 5072
		public const string string_1 = "{395edd3b-130e-4160-bb08-6931086cea46}";

		// Token: 0x040013D1 RID: 5073
		private static readonly bool bool_0 = Convert.ToBoolean("False");

		// Token: 0x040013D2 RID: 5074
		private static readonly string string_2 = "1";

		// Token: 0x040013D3 RID: 5075
		private static Class540 class540_0;

		// Token: 0x040013D4 RID: 5076
		private IWebProxy iwebProxy_0;

		// Token: 0x040013D5 RID: 5077
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x040013D6 RID: 5078
		[CompilerGenerated]
		private Delegate38 delegate38_0;
	}
}
