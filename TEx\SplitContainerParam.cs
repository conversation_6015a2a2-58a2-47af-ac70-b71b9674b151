﻿using System;
using System.Drawing;
using System.Windows.Forms;
using System.Xml.Linq;
using ns9;

namespace TEx
{
	// Token: 0x02000031 RID: 49
	[Serializable]
	internal sealed class SplitContainerParam
	{
		// Token: 0x06000135 RID: 309 RVA: 0x00002D25 File Offset: 0x00000F25
		public SplitContainerParam()
		{
		}

		// Token: 0x06000136 RID: 310 RVA: 0x000163D4 File Offset: 0x000145D4
		public SplitContainerParam(Orientation orientation, bool panel1Collapsed, bool panel2Collapsed, int splitterDistance, Type panel1CtrlType, Type panel2CtrlType, bool hasParentSpContainer, string parentSpContainerTag, bool isInParentSpContainerPanel1, string tag, Size size)
		{
			this._Orientation = orientation;
			this._Panel1Collapsed = panel1Collapsed;
			this._Panel2Collapsed = panel2Collapsed;
			this._SplitterDistance = splitterDistance;
			this._Panel1CtrlType = panel1CtrlType;
			this._Panel2CtrlType = panel2CtrlType;
			this._HasParentSpContainer = hasParentSpContainer;
			this._ParentSpContainerTag = parentSpContainerTag;
			this._IsInParentSpContainerPanel1 = this.IsInParentSpContainerPanel1;
			this._Tag = tag;
			this._Size = size;
		}

		// Token: 0x06000137 RID: 311 RVA: 0x00016444 File Offset: 0x00014644
		public SplitContainerParam(XElement el)
		{
			this.Orientation = (Orientation)Convert.ToInt32(el.Element("Orientation").Value);
			this.Panel1Collapsed = Convert.ToBoolean(Convert.ToInt32(el.Element("Panel1Collapsed").Value));
			this.Panel2Collapsed = Convert.ToBoolean(Convert.ToInt32(el.Element("Panel2Collapsed").Value));
			this.SplitterDistance = Convert.ToInt32(el.Element("SplitterDistance").Value);
			this.Panel1CtrlType = this.GetPanelCtrlTypeFromString(el.Element("Panel1CtrlType").Value);
			this.Panel2CtrlType = this.GetPanelCtrlTypeFromString(el.Element("Panel2CtrlType").Value);
			this.HasParentSpContainer = Convert.ToBoolean(Convert.ToInt32(el.Element("HasParentSpContainer").Value));
			this.ParentSpContainerTag = el.Element("ParentSpContainerTag").Value;
			this.IsInParentSpContainerPanel1 = Convert.ToBoolean(Convert.ToInt32(el.Element("IsInParentSpContainerPanel1").Value));
			this.Tag = el.Element("Tag").Value;
			XElement xelement = el.Element("Size");
			this.Size = new Size(Convert.ToInt32(xelement.Attribute("Width").Value), Convert.ToInt32(xelement.Attribute("Height").Value));
		}

		// Token: 0x06000138 RID: 312 RVA: 0x000165F8 File Offset: 0x000147F8
		private Type GetPanelCtrlTypeFromString(string typestr)
		{
			Type result;
			if (typestr.EndsWith("MaxRestoreBtnCtrl"))
			{
				result = typeof(Class56);
			}
			else if (typestr.EndsWith("SplitContainer"))
			{
				result = typeof(SplitContainer);
			}
			else if (typestr.EndsWith("ChtCtrl_KLine"))
			{
				result = typeof(ChtCtrl_KLine);
			}
			else if (typestr.EndsWith("ChtCtrl_Tick"))
			{
				result = typeof(ChtCtrl_Tick);
			}
			else if (typestr.EndsWith("TransTabCtrl"))
			{
				result = typeof(TransTabCtrl);
			}
			else if (typestr.EndsWith("Forms.Label"))
			{
				result = typeof(Label);
			}
			else
			{
				if (!string.IsNullOrEmpty(typestr))
				{
					Type type = Type.GetType(typestr);
					throw new Exception("Type: " + type.Name + " is not recognized!");
				}
				result = null;
			}
			return result;
		}

		// Token: 0x06000139 RID: 313 RVA: 0x000166D8 File Offset: 0x000148D8
		public XElement GetXElement(string rootName)
		{
			return new XElement(rootName, new object[]
			{
				new XElement("Orientation", Convert.ToInt32(this.Orientation)),
				new XElement("Panel1Collapsed", Convert.ToInt32(this.Panel1Collapsed)),
				new XElement("Panel2Collapsed", Convert.ToInt32(this.Panel2Collapsed)),
				new XElement("SplitterDistance", this.SplitterDistance),
				new XElement("Panel1CtrlType", (this.Panel1CtrlType == null) ? "" : this.Panel1CtrlType.ToString()),
				new XElement("Panel2CtrlType", (this.Panel2CtrlType == null) ? "" : this.Panel2CtrlType.ToString()),
				new XElement("HasParentSpContainer", Convert.ToInt32(this.HasParentSpContainer)),
				new XElement("ParentSpContainerTag", (this.ParentSpContainerTag == null) ? string.Empty : this.ParentSpContainerTag),
				new XElement("IsInParentSpContainerPanel1", Convert.ToInt32(this.IsInParentSpContainerPanel1)),
				new XElement("Tag", (this.Tag == null) ? string.Empty : this.Tag),
				new XElement("Size", new object[]
				{
					new XAttribute("Width", this.Size.Width.ToString()),
					new XAttribute("Height", this.Size.Height.ToString())
				})
			});
		}

		// Token: 0x1700004B RID: 75
		// (get) Token: 0x0600013A RID: 314 RVA: 0x000168D8 File Offset: 0x00014AD8
		// (set) Token: 0x0600013B RID: 315 RVA: 0x00002EE4 File Offset: 0x000010E4
		public Orientation Orientation
		{
			get
			{
				return this._Orientation;
			}
			set
			{
				this._Orientation = value;
			}
		}

		// Token: 0x1700004C RID: 76
		// (get) Token: 0x0600013C RID: 316 RVA: 0x000168F0 File Offset: 0x00014AF0
		// (set) Token: 0x0600013D RID: 317 RVA: 0x00002EEF File Offset: 0x000010EF
		public bool Panel1Collapsed
		{
			get
			{
				return this._Panel1Collapsed;
			}
			set
			{
				this._Panel1Collapsed = value;
			}
		}

		// Token: 0x1700004D RID: 77
		// (get) Token: 0x0600013E RID: 318 RVA: 0x00016908 File Offset: 0x00014B08
		// (set) Token: 0x0600013F RID: 319 RVA: 0x00002EFA File Offset: 0x000010FA
		public bool Panel2Collapsed
		{
			get
			{
				return this._Panel2Collapsed;
			}
			set
			{
				this._Panel2Collapsed = value;
			}
		}

		// Token: 0x1700004E RID: 78
		// (get) Token: 0x06000140 RID: 320 RVA: 0x00016920 File Offset: 0x00014B20
		// (set) Token: 0x06000141 RID: 321 RVA: 0x00002F05 File Offset: 0x00001105
		public int SplitterDistance
		{
			get
			{
				return this._SplitterDistance;
			}
			set
			{
				this._SplitterDistance = value;
			}
		}

		// Token: 0x1700004F RID: 79
		// (get) Token: 0x06000142 RID: 322 RVA: 0x00016938 File Offset: 0x00014B38
		// (set) Token: 0x06000143 RID: 323 RVA: 0x00002F10 File Offset: 0x00001110
		public Type Panel1CtrlType
		{
			get
			{
				return this._Panel1CtrlType;
			}
			set
			{
				this._Panel1CtrlType = value;
			}
		}

		// Token: 0x17000050 RID: 80
		// (get) Token: 0x06000144 RID: 324 RVA: 0x00016938 File Offset: 0x00014B38
		// (set) Token: 0x06000145 RID: 325 RVA: 0x00002F1B File Offset: 0x0000111B
		public Type Panel2CtrlType
		{
			get
			{
				return this._Panel1CtrlType;
			}
			set
			{
				this._Panel2CtrlType = value;
			}
		}

		// Token: 0x17000051 RID: 81
		// (get) Token: 0x06000146 RID: 326 RVA: 0x00016950 File Offset: 0x00014B50
		// (set) Token: 0x06000147 RID: 327 RVA: 0x00002F26 File Offset: 0x00001126
		public bool HasParentSpContainer
		{
			get
			{
				return this._HasParentSpContainer;
			}
			set
			{
				this._HasParentSpContainer = value;
			}
		}

		// Token: 0x17000052 RID: 82
		// (get) Token: 0x06000148 RID: 328 RVA: 0x00016968 File Offset: 0x00014B68
		// (set) Token: 0x06000149 RID: 329 RVA: 0x00002F31 File Offset: 0x00001131
		public string ParentSpContainerTag
		{
			get
			{
				return this._ParentSpContainerTag;
			}
			set
			{
				this._ParentSpContainerTag = value;
			}
		}

		// Token: 0x17000053 RID: 83
		// (get) Token: 0x0600014A RID: 330 RVA: 0x00016980 File Offset: 0x00014B80
		// (set) Token: 0x0600014B RID: 331 RVA: 0x00002F3C File Offset: 0x0000113C
		public bool IsInParentSpContainerPanel1
		{
			get
			{
				return this._IsInParentSpContainerPanel1;
			}
			set
			{
				this._IsInParentSpContainerPanel1 = value;
			}
		}

		// Token: 0x17000054 RID: 84
		// (get) Token: 0x0600014C RID: 332 RVA: 0x00016998 File Offset: 0x00014B98
		// (set) Token: 0x0600014D RID: 333 RVA: 0x00002F47 File Offset: 0x00001147
		public string Tag
		{
			get
			{
				return this._Tag;
			}
			set
			{
				this._Tag = value;
			}
		}

		// Token: 0x17000055 RID: 85
		// (get) Token: 0x0600014E RID: 334 RVA: 0x000169B0 File Offset: 0x00014BB0
		// (set) Token: 0x0600014F RID: 335 RVA: 0x00002F52 File Offset: 0x00001152
		public Size Size
		{
			get
			{
				return this._Size;
			}
			set
			{
				this._Size = value;
			}
		}

		// Token: 0x04000077 RID: 119
		private Orientation _Orientation;

		// Token: 0x04000078 RID: 120
		private bool _Panel1Collapsed;

		// Token: 0x04000079 RID: 121
		private bool _Panel2Collapsed;

		// Token: 0x0400007A RID: 122
		private int _SplitterDistance;

		// Token: 0x0400007B RID: 123
		private Type _Panel1CtrlType;

		// Token: 0x0400007C RID: 124
		private Type _Panel2CtrlType;

		// Token: 0x0400007D RID: 125
		private bool _HasParentSpContainer;

		// Token: 0x0400007E RID: 126
		private string _ParentSpContainerTag;

		// Token: 0x0400007F RID: 127
		private bool _IsInParentSpContainerPanel1;

		// Token: 0x04000080 RID: 128
		private string _Tag;

		// Token: 0x04000081 RID: 129
		private Size _Size;
	}
}
