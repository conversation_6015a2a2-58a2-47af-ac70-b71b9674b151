﻿using System;
using System.Net;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml.Serialization;

namespace SmartAssembly.SmartExceptionsCore
{
	// Token: 0x02000408 RID: 1032
	[WebServiceBinding(Name = "ReportingServiceSoap", Namespace = "http://www.smartassembly.com/webservices/Reporting/")]
	internal sealed class ReportingService : SoapHttpClientProtocol
	{
		// Token: 0x06002806 RID: 10246 RVA: 0x0000F795 File Offset: 0x0000D995
		public ReportingService(string serverUrl)
		{
			base.Url = serverUrl + "Reporting.asmx";
			base.Timeout = 180000;
		}

		// Token: 0x06002807 RID: 10247 RVA: 0x0000F7B9 File Offset: 0x0000D9B9
		[SoapDocumentMethod("http://www.smartassembly.com/webservices/Reporting/UploadReport2")]
		public string UploadReport2(string licenseID, [XmlElement(DataType = "base64Binary")] byte[] data, string email, string appFriendlyName, string buildFriendlyNumber)
		{
			return (string)base.Invoke("UploadReport2", new object[]
			{
				licenseID,
				data,
				email,
				appFriendlyName,
				buildFriendlyNumber
			})[0];
		}

		// Token: 0x06002808 RID: 10248 RVA: 0x00103354 File Offset: 0x00101554
		protected override WebRequest GetWebRequest(Uri uri)
		{
			WebRequest webRequest = base.GetWebRequest(uri);
			HttpWebRequest httpWebRequest = webRequest as HttpWebRequest;
			if (httpWebRequest != null)
			{
				httpWebRequest.ServicePoint.Expect100Continue = false;
			}
			return webRequest;
		}
	}
}
