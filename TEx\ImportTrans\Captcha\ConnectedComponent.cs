﻿using System;
using System.Collections.Generic;
using System.Drawing;

namespace TEx.ImportTrans.Captcha
{
	// Token: 0x02000380 RID: 896
	public sealed class ConnectedComponent
	{
		// Token: 0x17000643 RID: 1603
		// (get) Token: 0x060024FF RID: 9471 RVA: 0x000F692C File Offset: 0x000F4B2C
		// (set) Token: 0x06002500 RID: 9472 RVA: 0x0000E50F File Offset: 0x0000C70F
		public ConnectedComponent.CompareColor CompareColorCallback
		{
			get
			{
				return this.compareColor_0;
			}
			set
			{
				this.compareColor_0 = value;
			}
		}

		// Token: 0x17000644 RID: 1604
		// (get) Token: 0x06002501 RID: 9473 RVA: 0x000F6944 File Offset: 0x000F4B44
		// (set) Token: 0x06002502 RID: 9474 RVA: 0x0000E51A File Offset: 0x0000C71A
		public Rectangle Roi
		{
			get
			{
				return this.rectangle_0;
			}
			set
			{
				this.rectangle_0 = value;
			}
		}

		// Token: 0x06002503 RID: 9475 RVA: 0x000F695C File Offset: 0x000F4B5C
		public ConnectedComponent()
		{
			this.compareColor_0 = new ConnectedComponent.CompareColor(ConnectedComponent.<>c.<>9.method_0);
			this.rectangle_0 = new Rectangle(0, 0, 0, 0);
		}

		// Token: 0x06002504 RID: 9476 RVA: 0x000F69B4 File Offset: 0x000F4BB4
		public List<List<Point>> method_0(Bitmap bitmap_0)
		{
			int width = bitmap_0.Width;
			int height = bitmap_0.Height;
			Stack<Point> stack_ = new Stack<Point>();
			List<List<Point>> list = new List<List<Point>>();
			ConnectedComponent.Struct8 @struct;
			if (this.rectangle_0.Left == 0 && this.rectangle_0.Right == 0 && this.rectangle_0.Top == 0 && this.rectangle_0.Bottom == 0)
			{
				@struct.int_0 = 0;
				@struct.int_1 = width - 1;
				@struct.int_2 = 0;
				@struct.int_3 = height - 1;
			}
			else
			{
				@struct.int_0 = this.rectangle_0.Left;
				@struct.int_1 = this.rectangle_0.Right - 1;
				@struct.int_2 = this.rectangle_0.Top;
				@struct.int_3 = this.rectangle_0.Bottom - 1;
			}
			bool[,] array = new bool[height, width];
			for (int i = 0; i < height; i++)
			{
				for (int j = 0; j < width; j++)
				{
					array[i, j] = false;
				}
			}
			for (int k = @struct.int_0; k < @struct.int_1; k++)
			{
				for (int l = @struct.int_2; l < height; l++)
				{
					Point point = new Point(k, l);
					List<Point> list2 = this.method_1(stack_, bitmap_0, ref point, ref @struct, array);
					if (list2 != null)
					{
						list.Add(list2);
					}
				}
			}
			return list;
		}

		// Token: 0x06002505 RID: 9477 RVA: 0x000F6B18 File Offset: 0x000F4D18
		private List<Point> method_1(Stack<Point> stack_0, Bitmap bitmap_0, ref Point point_1, ref ConnectedComponent.Struct8 struct8_0, bool[,] bool_0)
		{
			if (point_1.X == 10)
			{
				int y = point_1.Y;
			}
			List<Point> result;
			if (!bool_0[point_1.Y, point_1.X])
			{
				bool_0[point_1.Y, point_1.X] = true;
				Color pixel = bitmap_0.GetPixel(point_1.X, point_1.Y);
				if (this.compareColor_0(ref pixel))
				{
					stack_0.Push(point_1);
					List<Point> list = new List<Point>();
					while (stack_0.Count > 0)
					{
						point_1 = stack_0.Pop();
						list.Add(point_1);
						this.method_2(ref point_1);
						foreach (Point item in this.point_0)
						{
							if (item.X >= struct8_0.int_0 && item.X <= struct8_0.int_1 && item.Y >= struct8_0.int_2 && item.Y <= struct8_0.int_3 && !bool_0[item.Y, item.X])
							{
								bool_0[item.Y, item.X] = true;
								pixel = bitmap_0.GetPixel(item.X, item.Y);
								if (this.compareColor_0(ref pixel))
								{
									stack_0.Push(item);
								}
							}
						}
					}
					result = list;
				}
				else
				{
					result = null;
				}
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06002506 RID: 9478 RVA: 0x000F6C9C File Offset: 0x000F4E9C
		private void method_2(ref Point point_1)
		{
			this.point_0[0].X = point_1.X;
			this.point_0[0].Y = point_1.Y - 1;
			this.point_0[1].X = point_1.X;
			this.point_0[1].Y = point_1.Y + 1;
			this.point_0[2].X = point_1.X - 1;
			this.point_0[2].Y = point_1.Y;
			this.point_0[3].X = point_1.X + 1;
			this.point_0[3].Y = point_1.Y;
			this.point_0[4].X = point_1.X - 1;
			this.point_0[4].Y = point_1.Y - 1;
			this.point_0[5].X = point_1.X + 1;
			this.point_0[5].Y = point_1.Y - 1;
			this.point_0[6].X = point_1.X - 1;
			this.point_0[6].Y = point_1.Y + 1;
			this.point_0[7].X = point_1.X + 1;
			this.point_0[7].Y = point_1.Y + 1;
		}

		// Token: 0x040011D1 RID: 4561
		private ConnectedComponent.CompareColor compareColor_0;

		// Token: 0x040011D2 RID: 4562
		private Rectangle rectangle_0;

		// Token: 0x040011D3 RID: 4563
		private Point[] point_0 = new Point[8];

		// Token: 0x02000381 RID: 897
		// (Invoke) Token: 0x06002508 RID: 9480
		public delegate bool CompareColor(ref Color cr);

		// Token: 0x02000382 RID: 898
		private struct Struct7
		{
			// Token: 0x0600250B RID: 9483 RVA: 0x0000E525 File Offset: 0x0000C725
			public Struct7(int int_2, int int_3)
			{
				this.int_1 = int_2;
				this.int_0 = int_3;
			}

			// Token: 0x040011D4 RID: 4564
			public int int_0;

			// Token: 0x040011D5 RID: 4565
			public int int_1;
		}

		// Token: 0x02000383 RID: 899
		private struct Struct8
		{
			// Token: 0x040011D6 RID: 4566
			public int int_0;

			// Token: 0x040011D7 RID: 4567
			public int int_1;

			// Token: 0x040011D8 RID: 4568
			public int int_2;

			// Token: 0x040011D9 RID: 4569
			public int int_3;
		}
	}
}
