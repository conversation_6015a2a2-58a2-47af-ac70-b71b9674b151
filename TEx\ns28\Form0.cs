﻿using System;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace ns28
{
	// Token: 0x0200008F RID: 143
	internal partial class Form0 : Form
	{
		// Token: 0x060004B0 RID: 1200
		[DllImport("user32.dll")]
		public static extern int SendMessage(IntPtr intptr_0, int int_0, int int_1, int int_2);

		// Token: 0x060004B1 RID: 1201
		[DllImport("user32.dll")]
		public static extern bool ReleaseCapture();

		// Token: 0x060004B2 RID: 1202 RVA: 0x00004045 File Offset: 0x00002245
		public Form0()
		{
			base.FormBorderStyle = FormBorderStyle.None;
			base.StartPosition = FormStartPosition.CenterScreen;
			base.MouseDown += this.Form0_MouseDown;
		}

		// Token: 0x060004B3 RID: 1203 RVA: 0x0000406F File Offset: 0x0000226F
		private void Form0_MouseDown(object sender, MouseEventArgs e)
		{
			this.method_0(e);
		}

		// Token: 0x060004B4 RID: 1204 RVA: 0x0000407A File Offset: 0x0000227A
		protected void method_0(MouseEventArgs mouseEventArgs_0)
		{
			if (mouseEventArgs_0.Button == MouseButtons.Left)
			{
				Form0.ReleaseCapture();
				Form0.SendMessage(base.Handle, 161, 2, 0);
			}
		}
	}
}
