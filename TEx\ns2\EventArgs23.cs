﻿using System;
using System.Collections.Generic;
using TEx;

namespace ns2
{
	// Token: 0x0200027A RID: 634
	internal sealed class EventArgs23 : EventArgs
	{
		// Token: 0x06001B8D RID: 7053 RVA: 0x0000B5CF File Offset: 0x000097CF
		public EventArgs23(List<SyncParam> list_1, bool bool_2, bool bool_3)
		{
			this.list_0 = list_1;
			this.bool_0 = bool_2;
			this.bool_1 = bool_3;
		}

		// Token: 0x17000484 RID: 1156
		// (get) Token: 0x06001B8E RID: 7054 RVA: 0x000B9C74 File Offset: 0x000B7E74
		public List<SyncParam> SyncParamList
		{
			get
			{
				return this.list_0;
			}
		}

		// Token: 0x17000485 RID: 1157
		// (get) Token: 0x06001B8F RID: 7055 RVA: 0x000B9C8C File Offset: 0x000B7E8C
		// (set) Token: 0x06001B90 RID: 7056 RVA: 0x0000B5EE File Offset: 0x000097EE
		public bool IfNoConfirmCopyToLocal
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x17000486 RID: 1158
		// (get) Token: 0x06001B91 RID: 7057 RVA: 0x000B9CA4 File Offset: 0x000B7EA4
		// (set) Token: 0x06001B92 RID: 7058 RVA: 0x0000B5F9 File Offset: 0x000097F9
		public bool IfNoConfirmCopytoSrvNewer
		{
			get
			{
				return this.bool_1;
			}
			set
			{
				this.bool_1 = value;
			}
		}

		// Token: 0x04000DA0 RID: 3488
		private readonly List<SyncParam> list_0;

		// Token: 0x04000DA1 RID: 3489
		private bool bool_0;

		// Token: 0x04000DA2 RID: 3490
		private bool bool_1;
	}
}
