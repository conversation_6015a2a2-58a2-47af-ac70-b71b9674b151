﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using TEx;
using TEx.Inds;
using TEx.SIndicator;

namespace ns9
{
	// Token: 0x020002CE RID: 718
	internal sealed partial class FormDelInd : Form
	{
		// Token: 0x0600202C RID: 8236 RVA: 0x000DC7D4 File Offset: 0x000DA9D4
		public FormDelInd()
		{
			this.InitializeComponent();
			Base.UI.smethod_54(this);
			this.dataGridView1.EditMode = DataGridViewEditMode.EditProgrammatically;
			this.dataGridView1.BackgroundColor = Color.White;
			this.dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
			this.dataGridView1.ColumnHeadersVisible = false;
			this.dataGridView1.RowHeadersVisible = false;
			this.dataGridView1.DataSource = this.bindingList_0;
		}

		// Token: 0x0600202D RID: 8237 RVA: 0x000DC860 File Offset: 0x000DAA60
		public void method_0(List<IndEx> list_1)
		{
			if (list_1 != null)
			{
				this.bindingList_0.Clear();
				this.list_0 = list_1;
				foreach (IndEx indEx in this.list_0)
				{
					this.bindingList_0.Add(new NameScript(indEx.EnName, indEx.UDInd.UDS.Script));
				}
			}
		}

		// Token: 0x0600202E RID: 8238 RVA: 0x000DC8EC File Offset: 0x000DAAEC
		private IndEx method_1(int int_0)
		{
			IndEx result;
			if (int_0 < this.list_0.Count)
			{
				result = this.list_0[int_0];
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x0600202F RID: 8239 RVA: 0x000DC91C File Offset: 0x000DAB1C
		private void buttonRemoveInd_Click(object sender, EventArgs e)
		{
			if (this.dataGridView1.CurrentRow != null)
			{
				int index = this.dataGridView1.CurrentRow.Index;
				IndEx indEx = this.method_1(index);
				if (indEx != null)
				{
					indEx.RemoveFromChart();
					this.list_0.Remove(indEx);
					this.bindingList_0.RemoveAt(index);
				}
			}
		}

		// Token: 0x06002030 RID: 8240 RVA: 0x0000D0D3 File Offset: 0x0000B2D3
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000FC0 RID: 4032
		private BindingList<NameScript> bindingList_0 = new BindingList<NameScript>();

		// Token: 0x04000FC1 RID: 4033
		private List<IndEx> list_0 = new List<IndEx>();

		// Token: 0x04000FC2 RID: 4034
		private IContainer icontainer_0;
	}
}
