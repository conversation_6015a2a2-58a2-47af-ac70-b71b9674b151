﻿using System;
using System.Drawing;
using System.Runtime.Serialization;
using ns28;

namespace TEx
{
	// Token: 0x02000196 RID: 406
	[Serializable]
	internal sealed class DrawGrnArwLDn : DrawRedArwUp, ISerializable
	{
		// Token: 0x06000FAF RID: 4015 RVA: 0x00006B34 File Offset: 0x00004D34
		public DrawGrnArwLDn()
		{
		}

		// Token: 0x06000FB0 RID: 4016 RVA: 0x00006B9F File Offset: 0x00004D9F
		public DrawGrnArwLDn(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = "左下箭头";
		}

		// Token: 0x06000FB1 RID: 4017 RVA: 0x00006B58 File Offset: 0x00004D58
		protected DrawGrnArwLDn(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x06000FB2 RID: 4018 RVA: 0x00006B69 File Offset: 0x00004D69
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x06000FB3 RID: 4019 RVA: 0x000623BC File Offset: 0x000605BC
		protected override Image vmethod_24()
		{
			return Class372.GreenArrow_LDown;
		}
	}
}
