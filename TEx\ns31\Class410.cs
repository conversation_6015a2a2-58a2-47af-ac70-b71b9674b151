﻿using System;
using ns11;
using ns12;
using ns14;
using ns22;
using ns28;
using ns9;
using TEx.SIndicator;

namespace ns31
{
	// Token: 0x02000311 RID: 785
	internal sealed class Class410 : Class409
	{
		// Token: 0x060021DD RID: 8669 RVA: 0x0000D8CF File Offset: 0x0000BACF
		public Class410(HToken htoken_1) : base(htoken_1)
		{
		}

		// Token: 0x060021DE RID: 8670 RVA: 0x000E7D7C File Offset: 0x000E5F7C
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			return base.vmethod_1(parserEnvironment_0);
		}

		// Token: 0x060021DF RID: 8671 RVA: 0x000E7D94 File Offset: 0x000E5F94
		public static Class408 smethod_0(Tokenes tokenes_0)
		{
			HToken htoken = tokenes_0.Current;
			if (htoken.Symbol.HSymbolType != Enum26.const_5 && htoken.Symbol.HSymbolType != Enum26.const_2 && htoken.Symbol.HSymbolType != Enum26.const_4 && htoken.Symbol.HSymbolType != Enum26.const_34)
			{
				if (htoken.Symbol.HSymbolType != Enum26.const_38)
				{
					if (htoken.Symbol.HSymbolType == Enum26.const_0)
					{
						return new Class411(htoken);
					}
					if (htoken.Symbol.HSymbolType == Enum26.const_1)
					{
						return TreeFunction.smethod_0(tokenes_0);
					}
					if (htoken.Symbol.HSymbolType == Enum26.const_23)
					{
						tokenes_0.method_1();
						if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
						{
							throw new Exception(htoken.method_0("没有操作数"));
						}
						Class408 @class = Class413.smethod_0(tokenes_0);
						tokenes_0.method_1();
						if (@class == null)
						{
							throw new Exception(htoken.method_0("内表达式错误"));
						}
						if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_24)
						{
							return @class;
						}
						throw new Exception(tokenes_0.Current.method_0("不是右括号。"));
					}
					else
					{
						if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_7)
						{
							tokenes_0.Current.Symbol.method_0(Enum26.const_39);
							return Class417.smethod_0(tokenes_0);
						}
						throw new Exception(htoken.method_0("无法解析。"));
					}
				}
			}
			return new Class410(htoken);
		}

		// Token: 0x060021E0 RID: 8672 RVA: 0x000E7F08 File Offset: 0x000E6108
		public override string vmethod_0()
		{
			return base.vmethod_0();
		}

		// Token: 0x170005D4 RID: 1492
		// (get) Token: 0x060021E1 RID: 8673 RVA: 0x000E7F20 File Offset: 0x000E6120
		// (set) Token: 0x060021E2 RID: 8674 RVA: 0x0000D8D8 File Offset: 0x0000BAD8
		public override Class408 Left
		{
			get
			{
				return base.Left;
			}
			protected set
			{
				base.Left = value;
			}
		}

		// Token: 0x170005D5 RID: 1493
		// (get) Token: 0x060021E3 RID: 8675 RVA: 0x000E7F38 File Offset: 0x000E6138
		// (set) Token: 0x060021E4 RID: 8676 RVA: 0x0000D8E3 File Offset: 0x0000BAE3
		public override Class408 Right
		{
			get
			{
				return base.Right;
			}
			protected set
			{
				base.Right = value;
			}
		}

		// Token: 0x170005D6 RID: 1494
		// (get) Token: 0x060021E5 RID: 8677 RVA: 0x000E7F50 File Offset: 0x000E6150
		// (set) Token: 0x060021E6 RID: 8678 RVA: 0x0000D8EE File Offset: 0x0000BAEE
		public override HToken Token
		{
			get
			{
				return base.Token;
			}
			protected set
			{
				base.Token = value;
			}
		}

		// Token: 0x060021E7 RID: 8679 RVA: 0x000E7D1C File Offset: 0x000E5F1C
		public string ToString()
		{
			return base.ToString();
		}
	}
}
