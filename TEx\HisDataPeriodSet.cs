﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Runtime.Serialization;
using ns11;
using ns28;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x020001E2 RID: 482
	[Serializable]
	internal sealed class HisDataPeriodSet : ISerializable, ICloneable
	{
		// Token: 0x060012C2 RID: 4802 RVA: 0x00002D25 File Offset: 0x00000F25
		public HisDataPeriodSet()
		{
		}

		// Token: 0x060012C3 RID: 4803 RVA: 0x00007CC9 File Offset: 0x00005EC9
		public HisDataPeriodSet(int stkSymbId, SortedList<DateTime, HisData> hisDataList, PeriodType periodType, int? periodUnits) : this(stkSymbId, hisDataList, periodType, periodUnits, false)
		{
		}

		// Token: 0x060012C4 RID: 4804 RVA: 0x0007F9FC File Offset: 0x0007DBFC
		public HisDataPeriodSet(SymbDataSet sds, PeriodType periodType, int? periodUnits)
		{
			this.sds = sds;
			this.stkSymbId = sds.SymblID;
			this.ifGenFromFetchedHDList = true;
			if (sds.CurrHisDataSet != null)
			{
				this.method_1(sds.CurrHisDataSet.FetchedHisDataList, periodType, periodUnits);
			}
			else
			{
				Class182.smethod_0(new Exception("sds.CurrHisDataSet is null!"));
			}
		}

		// Token: 0x060012C5 RID: 4805 RVA: 0x00007CD7 File Offset: 0x00005ED7
		public HisDataPeriodSet(SymbDataSet sds, SortedList<DateTime, HisData> hisDataList, PeriodType periodType, int? periodUnits)
		{
			this.sds = sds;
			this.stkSymbId = sds.SymblID;
			this.ifGenFromFetchedHDList = false;
			this.method_1(hisDataList, periodType, periodUnits);
		}

		// Token: 0x060012C6 RID: 4806 RVA: 0x0007FA58 File Offset: 0x0007DC58
		public HisDataPeriodSet(int symbId, SortedList<DateTime, HisData> hisDataList, PeriodType periodType, int? periodUnits, bool ifGenFromFetchedHDList)
		{
			this.stkSymbId = symbId;
			if (this.sds == null)
			{
				SymbDataSet symbDataSet = Base.Data.smethod_49(symbId, false);
				if (symbDataSet != null)
				{
					this.sds = symbDataSet;
				}
			}
			this.ifGenFromFetchedHDList = ifGenFromFetchedHDList;
			this.method_1(hisDataList, periodType, periodUnits);
		}

		// Token: 0x060012C7 RID: 4807 RVA: 0x0007FAA0 File Offset: 0x0007DCA0
		public HisDataPeriodSet(int stkSymbId, List<HisData> phdList, PeriodType periodType, int? periodUnits)
		{
			this.ifGenFromFetchedHDList = false;
			this.stkSymbId = stkSymbId;
			SortedList<DateTime, HisData> sortedList_;
			try
			{
				sortedList_ = new SortedList<DateTime, HisData>(phdList.ToDictionary(new Func<HisData, DateTime>(HisDataPeriodSet.<>c.<>9.method_0)));
			}
			catch (Exception)
			{
				sortedList_ = new SortedList<DateTime, HisData>(phdList.GroupBy(new Func<HisData, DateTime>(HisDataPeriodSet.<>c.<>9.method_1)).Select(new Func<IGrouping<DateTime, HisData>, HisData>(HisDataPeriodSet.<>c.<>9.method_2)).ToDictionary(new Func<HisData, DateTime>(HisDataPeriodSet.<>c.<>9.method_3)));
			}
			this.method_8(sortedList_, periodType, periodUnits);
		}

		// Token: 0x060012C8 RID: 4808 RVA: 0x0007FB80 File Offset: 0x0007DD80
		protected HisDataPeriodSet(SerializationInfo info, StreamingContext context)
		{
			this.stkSymbId = info.GetInt32("SymbId");
			this.periodType_0 = (PeriodType)info.GetValue("PeriodType", typeof(PeriodType));
			this.nullable_0 = new int?(info.GetInt32("PeriodUnits"));
			this.sortedList_0 = (SortedList<DateTime, HisData>)info.GetValue("PeriodHisDataList", typeof(SortedList<DateTime, HisData>));
			try
			{
				this.sortedList_1 = (SortedList<DateTime, HisData>)info.GetValue("oriPeriodHisDataList", typeof(SortedList<DateTime, HisData>));
			}
			catch
			{
			}
			this.double_0 = (double[])info.GetValue("CloseDataArray", typeof(double[]));
			this.double_1 = (double[])info.GetValue("HighDataArray", typeof(double[]));
			this.double_2 = (double[])info.GetValue("LowDataArray", typeof(double[]));
			this.nullable_1 = (int?)info.GetValue("NumberOfStickItemsPerDay", typeof(int?));
			this.string_0 = info.GetString("ChtCtrl_KLineTag");
			this.ifGenFromFetchedHDList = info.GetBoolean("ifGenFromFetchedHDList");
		}

		// Token: 0x060012C9 RID: 4809 RVA: 0x0007FCD4 File Offset: 0x0007DED4
		public void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			info.AddValue("SymbId", this.stkSymbId);
			info.AddValue("PeriodType", this.periodType_0);
			info.AddValue("PeriodUnits", this.nullable_0);
			if (this.periodType_0 == PeriodType.ByMins)
			{
				int? num = this.nullable_0;
				if (num.GetValueOrDefault() == 1 & num != null)
				{
					goto IL_73;
				}
			}
			info.AddValue("PeriodHisDataList", this.sortedList_0);
			IL_73:
			info.AddValue("CloseDataArray", this.double_0);
			info.AddValue("HighDataArray", this.double_1);
			info.AddValue("LowDataArray", this.double_2);
			info.AddValue("NumberOfStickItemsPerDay", this.nullable_1);
			info.AddValue("ChtCtrl_KLineTag", this.string_0);
			info.AddValue("ifGenFromFetchedHDList", this.ifGenFromFetchedHDList);
			info.AddValue("oriPeriodHisDataList", this.sortedList_1);
		}

		// Token: 0x060012CA RID: 4810 RVA: 0x0007FDD4 File Offset: 0x0007DFD4
		public void method_0()
		{
			SortedList<DateTime, HisData> fetchedHisDataList = this.SymbDataSet.CurrHisDataSet.FetchedHisDataList;
			if (this.IsNMinsPeriod)
			{
				this.method_1(fetchedHisDataList, this.periodType_0, this.nullable_0);
			}
		}

		// Token: 0x060012CB RID: 4811 RVA: 0x00007D05 File Offset: 0x00005F05
		public void method_1(SortedList<DateTime, HisData> sortedList_2, PeriodType periodType_1, int? nullable_2)
		{
			this.method_2(sortedList_2, periodType_1, nullable_2, false);
		}

		// Token: 0x060012CC RID: 4812 RVA: 0x0007FE10 File Offset: 0x0007E010
		public void method_2(SortedList<DateTime, HisData> sortedList_2, PeriodType periodType_1, int? nullable_2, bool bool_1)
		{
			bool flag = this.method_42(periodType_1, nullable_2);
			bool flag2 = periodType_1 == PeriodType.ByWeek || periodType_1 == PeriodType.ByMonth || (periodType_1 == PeriodType.ByDay && nullable_2 != null && nullable_2.Value > 7);
			SortedList<DateTime, HisData> sortedList_3;
			if (bool_1 && this.sortedList_1 != null && flag)
			{
				if (flag2)
				{
					sortedList_3 = this.method_13(sortedList_2, periodType_1, nullable_2);
					this.sortedList_1 = null;
				}
				else
				{
					sortedList_3 = this.sortedList_1;
				}
			}
			else
			{
				sortedList_3 = this.method_13(sortedList_2, periodType_1, nullable_2);
				if (!flag2)
				{
					this.sortedList_1 = sortedList_3;
				}
			}
			if (!flag2)
			{
				SortedList<DateTime, HisData> sortedList = this.method_4(sortedList_3);
				if (sortedList != null)
				{
					this.sortedList_0 = sortedList;
					this.bool_0 = true;
				}
				else
				{
					this.bool_0 = false;
				}
			}
			if (!this.bool_0 || flag2)
			{
				this.sortedList_0 = sortedList_3;
				if (!bool_1)
				{
					this.sortedList_1 = null;
				}
			}
			this.method_8(this.sortedList_0, periodType_1, nullable_2);
		}

		// Token: 0x060012CD RID: 4813 RVA: 0x0007FEE8 File Offset: 0x0007E0E8
		public void method_3()
		{
			if (this.PeriodType == PeriodType.ByMins)
			{
				int? periodUnits = this.PeriodUnits;
				if (periodUnits.GetValueOrDefault() == 60 & periodUnits != null)
				{
					SortedList<DateTime, HisData> periodHisDataList;
					if (this.sortedList_1 != null)
					{
						periodHisDataList = this.sortedList_1;
					}
					else
					{
						periodHisDataList = this.PeriodHisDataList;
						this.sortedList_1 = periodHisDataList;
					}
					SortedList<DateTime, HisData> sortedList = this.method_4(periodHisDataList);
					if (sortedList != null)
					{
						this.sortedList_0 = sortedList;
						this.bool_0 = true;
					}
					else
					{
						this.bool_0 = false;
					}
					this.method_7();
					return;
				}
			}
			if (this.PeriodType == PeriodType.ByMins)
			{
				if (this.SymbDataSet != null && this.SymbDataSet.CurrHisDataSet != null && this.SymbDataSet.CurrHisDataSet.FetchedHisDataList != null)
				{
					this.method_2(this.SymbDataSet.CurrHisDataSet.FetchedHisDataList, this.periodType_0, this.nullable_0, true);
				}
			}
			else if (this.SymbDataSet != null && this.SymbDataSet.Curr1hPeriodHisData != null && this.SymbDataSet.Curr1hPeriodHisData.PeriodHisDataList != null)
			{
				this.method_2(this.SymbDataSet.Curr1hPeriodHisData.PeriodHisDataList, this.periodType_0, this.nullable_0, true);
			}
		}

		// Token: 0x060012CE RID: 4814 RVA: 0x00080010 File Offset: 0x0007E210
		private SortedList<DateTime, HisData> method_4(SortedList<DateTime, HisData> sortedList_2)
		{
			SortedList<DateTime, HisData> result;
			if (this.Symbol.IsStock && (Base.UI.Form.StockRestorationMethod == null || Base.UI.Form.StockRestorationMethod.Value != StockRestorationMethod.None) && this.SymbDataSet != null)
			{
				List<StSplit> currStSplitList = this.SymbDataSet.CurrStSplitList;
				if (currStSplitList != null && currStSplitList.Any<StSplit>())
				{
					result = this.method_5(sortedList_2, currStSplitList);
				}
				else
				{
					result = null;
				}
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060012CF RID: 4815 RVA: 0x00080088 File Offset: 0x0007E288
		public SortedList<DateTime, HisData> method_5(SortedList<DateTime, HisData> sortedList_2, List<StSplit> list_0)
		{
			return this.method_6(sortedList_2, list_0, Base.UI.Form.StockRestorationMethod);
		}

		// Token: 0x060012D0 RID: 4816 RVA: 0x000800AC File Offset: 0x0007E2AC
		private SortedList<DateTime, HisData> method_6(SortedList<DateTime, HisData> sortedList_2, List<StSplit> list_0, StockRestorationMethod? nullable_2)
		{
			if (list_0 != null)
			{
				if (nullable_2 != null)
				{
					StockRestorationMethod? stockRestorationMethod = nullable_2;
					if (stockRestorationMethod.GetValueOrDefault() == StockRestorationMethod.None & stockRestorationMethod != null)
					{
						goto IL_80;
					}
				}
				SortedList<DateTime, HisData> sortedList = new SortedList<DateTime, HisData>();
				foreach (KeyValuePair<DateTime, HisData> keyValuePair in sortedList_2)
				{
					HisData hisData = this.SymbDataSet.method_92(this.Symbol, list_0, keyValuePair.Value, nullable_2);
					sortedList.Add(hisData.Date, hisData);
				}
				return sortedList;
			}
			IL_80:
			return sortedList_2;
		}

		// Token: 0x060012D1 RID: 4817 RVA: 0x00007D13 File Offset: 0x00005F13
		private void method_7()
		{
			this.method_8(this.sortedList_0, this.periodType_0, this.nullable_0);
		}

		// Token: 0x060012D2 RID: 4818 RVA: 0x00080150 File Offset: 0x0007E350
		private void method_8(SortedList<DateTime, HisData> sortedList_2, PeriodType periodType_1, int? nullable_2)
		{
			if (sortedList_2 != null && sortedList_2.Count > 0)
			{
				this.sortedList_0 = sortedList_2;
				this.periodType_0 = periodType_1;
				this.nullable_0 = nullable_2;
				this.double_0 = this.method_12(this.sortedList_0);
				this.double_1 = this.method_9(this.sortedList_0);
				this.double_2 = this.method_10(this.sortedList_0);
				this.double_3 = this.method_11(this.sortedList_0);
				this.nullable_1 = this.method_44(this.PeriodHisDataList.Keys.First<DateTime>().Date);
			}
			else
			{
				Class182.smethod_0(new Exception("phdList == null || phdList.Count == 0!"));
			}
		}

		// Token: 0x060012D3 RID: 4819 RVA: 0x00080204 File Offset: 0x0007E404
		private double[] method_9(SortedList<DateTime, HisData> sortedList_2)
		{
			int count = sortedList_2.Count;
			double[] array = new double[count];
			for (int i = 0; i < count; i++)
			{
				array[i] = sortedList_2.Values[i].High;
			}
			return array;
		}

		// Token: 0x060012D4 RID: 4820 RVA: 0x00080248 File Offset: 0x0007E448
		private double[] method_10(SortedList<DateTime, HisData> sortedList_2)
		{
			int count = sortedList_2.Count;
			double[] array = new double[count];
			for (int i = 0; i < count; i++)
			{
				array[i] = sortedList_2.Values[i].Low;
			}
			return array;
		}

		// Token: 0x060012D5 RID: 4821 RVA: 0x0008028C File Offset: 0x0007E48C
		private double[] method_11(SortedList<DateTime, HisData> sortedList_2)
		{
			int count = sortedList_2.Count;
			double[] array = new double[count];
			for (int i = 0; i < count; i++)
			{
				array[i] = sortedList_2.Values[i].Volume.Value;
			}
			return array;
		}

		// Token: 0x060012D6 RID: 4822 RVA: 0x000802D8 File Offset: 0x0007E4D8
		private double[] method_12(SortedList<DateTime, HisData> sortedList_2)
		{
			int count = sortedList_2.Count;
			double[] array = new double[count];
			for (int i = 0; i < count; i++)
			{
				array[i] = sortedList_2.Values[i].Close;
			}
			return array;
		}

		// Token: 0x060012D7 RID: 4823 RVA: 0x0008031C File Offset: 0x0007E51C
		private SortedList<DateTime, HisData> method_13(SortedList<DateTime, HisData> sortedList_2, PeriodType periodType_1, int? nullable_2)
		{
			SortedList<DateTime, HisData> result;
			if (sortedList_2 != null && sortedList_2.Any<KeyValuePair<DateTime, HisData>>())
			{
				sortedList_2 = this.method_43(sortedList_2);
				SortedList<DateTime, HisData> sortedList = new SortedList<DateTime, HisData>();
				DateTime date = sortedList_2.Keys.First<DateTime>().Date;
				ExchgOBT exchgOBT_ = Base.Data.smethod_110(this.Symbol, date);
				bool isStock = this.Symbol.IsStock;
				if (periodType_1 == PeriodType.ByMins)
				{
					int num;
					if (nullable_2 != null)
					{
						num = nullable_2.Value;
					}
					else
					{
						num = 1;
					}
					if (Utility.CanExactDiv(num, 60))
					{
						sortedList = this.method_19(sortedList_2, num / 60);
					}
					else
					{
						sortedList = this.method_14(sortedList_2, new int?(num));
					}
				}
				else if (periodType_1 == PeriodType.ByDay)
				{
					sortedList = this.method_23(sortedList_2, !isStock, nullable_2);
				}
				else if (periodType_1 == PeriodType.ByWeek)
				{
					sortedList = this.method_26(sortedList_2, exchgOBT_, !isStock);
				}
				else if (periodType_1 == PeriodType.ByMonth)
				{
					sortedList = this.method_27(sortedList_2, exchgOBT_, !isStock);
				}
				result = sortedList;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060012D8 RID: 4824 RVA: 0x00080400 File Offset: 0x0007E600
		private SortedList<DateTime, HisData> method_14(SortedList<DateTime, HisData> sortedList_2, int? nullable_2)
		{
			return this.method_15(sortedList_2, nullable_2, true);
		}

		// Token: 0x060012D9 RID: 4825 RVA: 0x0008041C File Offset: 0x0007E61C
		private SortedList<DateTime, HisData> method_15(SortedList<DateTime, HisData> sortedList_2, int? nullable_2, bool bool_1)
		{
			HisDataPeriodSet.Class265 @class = new HisDataPeriodSet.Class265();
			SortedList<DateTime, HisData> sortedList = new SortedList<DateTime, HisData>();
			DateTime dateTime = sortedList_2.Keys.First<DateTime>().Date;
			DateTime date = sortedList_2.Keys.Last<DateTime>().Date;
			@class.exchgOBT_0 = Base.Data.smethod_110(this.Symbol, dateTime);
			if (Base.Data.smethod_112(this.Symbol, dateTime) != null)
			{
				DateTime dt = sortedList_2.Keys.First<DateTime>();
				DateTime? nightTradingCloseDT = @class.exchgOBT_0.GetNightTradingCloseDT(dt);
				if (nightTradingCloseDT != null)
				{
					DateTime? dateTime2 = this.method_16(sortedList_2, nightTradingCloseDT.Value);
					if (dateTime2 != null)
					{
						dateTime = dateTime2.Value;
					}
				}
			}
			while (dateTime <= date)
			{
				HisDataPeriodSet.Class266 class2 = new HisDataPeriodSet.Class266();
				class2.class265_0 = @class;
				class2.class265_0.exchgOBT_0 = Base.Data.smethod_110(this.Symbol, dateTime);
				bool flag = Base.Data.smethod_112(this.Symbol, dateTime) != null;
				DateTime dateTime3 = dateTime.Date.Add(class2.class265_0.exchgOBT_0.DayOpenTime.Value.TimeOfDay);
				bool flag2 = false;
				if (flag)
				{
					DateTime? dateTime4 = this.method_17(dateTime3, sortedList_2, class2.class265_0.exchgOBT_0);
					if (dateTime4 != null)
					{
						flag2 = true;
						dateTime3 = dateTime4.Value;
					}
				}
				class2.dateTime_0 = class2.class265_0.exchgOBT_0.GetDayCloseDT(dateTime);
				DateTime dateTime5 = dateTime3.AddMinutes((double)nullable_2.Value);
				bool flag3 = class2.class265_0.exchgOBT_0 != null && class2.class265_0.exchgOBT_0.NoonBreakStartTime != null;
				bool flag4 = class2.class265_0.exchgOBT_0 != null && class2.class265_0.exchgOBT_0.AMRestStartTime != null;
				bool flag5 = class2.class265_0.exchgOBT_0 != null && class2.class265_0.exchgOBT_0.PMRestStartTime != null;
				for (;;)
				{
					this.method_31(sortedList_2, sortedList, dateTime3, dateTime5);
					dateTime3 = dateTime5;
					if (flag2)
					{
						if (class2.class265_0.exchgOBT_0.NightCloseTime.Value.TimeOfDay < class2.class265_0.exchgOBT_0.NightOpenTime.Value.TimeOfDay)
						{
							if (dateTime3.TimeOfDay >= class2.class265_0.exchgOBT_0.NightCloseTime.Value.TimeOfDay && dateTime3.TimeOfDay <= class2.class265_0.exchgOBT_0.DayOpenTime.Value.TimeOfDay)
							{
								dateTime3 = dateTime.Date.Add(class2.class265_0.exchgOBT_0.DayOpenTime.Value.TimeOfDay);
							}
						}
						else if (dateTime3.TimeOfDay >= class2.class265_0.exchgOBT_0.NightCloseTime.Value.TimeOfDay)
						{
							dateTime3 = dateTime.Date.Add(class2.class265_0.exchgOBT_0.DayOpenTime.Value.TimeOfDay);
						}
					}
					if (flag4 && dateTime3.TimeOfDay >= class2.class265_0.exchgOBT_0.AMRestStartTime.Value.TimeOfDay && dateTime3.TimeOfDay <= class2.class265_0.exchgOBT_0.AMRestEndTime.Value.TimeOfDay)
					{
						dateTime3 = dateTime.Date.Add(class2.class265_0.exchgOBT_0.AMRestEndTime.Value.TimeOfDay);
					}
					if (flag3 && dateTime3.TimeOfDay >= class2.class265_0.exchgOBT_0.NoonBreakStartTime.Value.TimeOfDay && dateTime3.TimeOfDay <= class2.class265_0.exchgOBT_0.NoonBreakEndTime.Value.TimeOfDay)
					{
						dateTime3 = dateTime.Date.Add(class2.class265_0.exchgOBT_0.NoonBreakEndTime.Value.TimeOfDay);
					}
					if (flag5 && dateTime3.TimeOfDay >= class2.class265_0.exchgOBT_0.PMRestStartTime.Value.TimeOfDay && dateTime3.TimeOfDay <= class2.class265_0.exchgOBT_0.PMRestEndTime.Value.TimeOfDay)
					{
						dateTime3 = dateTime.Date.Add(class2.class265_0.exchgOBT_0.PMRestEndTime.Value.TimeOfDay);
					}
					if (dateTime3 >= class2.dateTime_0)
					{
						break;
					}
					dateTime5 = dateTime3.AddMinutes((double)nullable_2.Value);
					if (flag2)
					{
						DateTime? nightTradingCloseDT2 = class2.class265_0.exchgOBT_0.GetNightTradingCloseDT(dateTime3);
						if (nightTradingCloseDT2 != null)
						{
							DateTime value = class2.class265_0.exchgOBT_0.NightCloseTime.Value;
							DateTime value2 = class2.class265_0.exchgOBT_0.NightOpenTime.Value;
							DateTime value3 = class2.class265_0.exchgOBT_0.DayOpenTime.Value;
							if (value.Date > value2.Date && dateTime5.TimeOfDay > value.TimeOfDay && dateTime5.TimeOfDay < value3.TimeOfDay)
							{
								if (bool_1)
								{
									dateTime5 = class2.dateTime_0.Date.Add(value3.TimeOfDay).Add(dateTime5.TimeOfDay - value.TimeOfDay);
								}
								else
								{
									dateTime5 = nightTradingCloseDT2.Value;
								}
							}
							else if (value.Date == value2.Date && ((dateTime5.TimeOfDay > value2.TimeOfDay && dateTime5.TimeOfDay > value.TimeOfDay) || dateTime5.TimeOfDay < value3.TimeOfDay))
							{
								if (bool_1)
								{
									if (dateTime5.TimeOfDay < value3.TimeOfDay)
									{
										dateTime5 = class2.dateTime_0.Date.Add(value3.TimeOfDay).Add(value.Date.AddDays(1.0) - value).Add(dateTime5.TimeOfDay);
									}
									else
									{
										dateTime5 = class2.dateTime_0.Date.Add(value3.TimeOfDay).Add(dateTime5.TimeOfDay - value.TimeOfDay);
									}
								}
								else
								{
									dateTime5 = nightTradingCloseDT2.Value;
								}
							}
						}
					}
					if (flag4 && dateTime3.TimeOfDay < class2.class265_0.exchgOBT_0.AMRestStartTime.Value.TimeOfDay && dateTime5.TimeOfDay > class2.class265_0.exchgOBT_0.AMRestStartTime.Value.TimeOfDay)
					{
						dateTime5 = dateTime5.Add(class2.class265_0.exchgOBT_0.AMRestEndTime.Value.TimeOfDay - class2.class265_0.exchgOBT_0.AMRestStartTime.Value.TimeOfDay);
					}
					if (flag3 && dateTime3.TimeOfDay < class2.class265_0.exchgOBT_0.NoonBreakStartTime.Value.TimeOfDay && dateTime5.TimeOfDay > class2.class265_0.exchgOBT_0.NoonBreakStartTime.Value.TimeOfDay)
					{
						dateTime5 = dateTime5.Add(class2.class265_0.exchgOBT_0.NoonBreakEndTime.Value.TimeOfDay - class2.class265_0.exchgOBT_0.NoonBreakStartTime.Value.TimeOfDay);
					}
					if (flag5 && dateTime3.TimeOfDay < class2.class265_0.exchgOBT_0.PMRestStartTime.Value.TimeOfDay && dateTime5.TimeOfDay > class2.class265_0.exchgOBT_0.PMRestStartTime.Value.TimeOfDay)
					{
						dateTime5 = dateTime5.Add(class2.class265_0.exchgOBT_0.PMRestEndTime.Value.TimeOfDay - class2.class265_0.exchgOBT_0.PMRestStartTime.Value.TimeOfDay);
					}
					if (dateTime5 > class2.dateTime_0)
					{
						dateTime5 = class2.dateTime_0;
					}
				}
				DateTime? dateTime6 = null;
				int num = sortedList_2.IndexOfKey(class2.dateTime_0);
				if (num >= 0)
				{
					if (num == sortedList_2.Count - 1)
					{
						break;
					}
					DateTime dateTime7 = sortedList_2.Keys[num + 1];
					if (class2.class265_0.exchgOBT_0.IsDayTradingDT(dateTime7))
					{
						dateTime6 = new DateTime?(dateTime7);
					}
					else
					{
						DateTime? nightTradingCloseDT3 = class2.class265_0.exchgOBT_0.GetNightTradingCloseDT(dateTime7);
						if (nightTradingCloseDT3 != null)
						{
							int num2 = sortedList_2.IndexOfKey(nightTradingCloseDT3.Value);
							if (num2 > 0)
							{
								if (num2 == sortedList_2.Count - 1)
								{
									break;
								}
								dateTime6 = new DateTime?(sortedList_2.Keys[num2 + 1]);
							}
						}
					}
				}
				if (dateTime6 != null)
				{
					dateTime = dateTime6.Value.Date;
				}
				else
				{
					IEnumerable<KeyValuePair<DateTime, HisData>> source = sortedList_2.Where(new Func<KeyValuePair<DateTime, HisData>, bool>(class2.method_0));
					if (!source.Any<KeyValuePair<DateTime, HisData>>())
					{
						break;
					}
					dateTime = source.First<KeyValuePair<DateTime, HisData>>().Key.Date;
				}
			}
			return sortedList;
		}

		// Token: 0x060012DA RID: 4826 RVA: 0x00080F38 File Offset: 0x0007F138
		public DateTime? method_16(SortedList<DateTime, HisData> sortedList_2, DateTime dateTime_0)
		{
			HisDataPeriodSet.Class267 @class = new HisDataPeriodSet.Class267();
			@class.dateTime_0 = dateTime_0;
			DateTime? result = null;
			int num = -1;
			int num2 = sortedList_2.IndexOfKey(@class.dateTime_0);
			if (num2 > -1 && num2 < sortedList_2.Count - 1)
			{
				num = num2 + 1;
			}
			if (num > -1)
			{
				result = new DateTime?(sortedList_2.Keys[num2 + 1].Date);
			}
			else
			{
				try
				{
					result = new DateTime?(sortedList_2.Keys.First(new Func<DateTime, bool>(@class.method_0)).Date);
				}
				catch
				{
				}
			}
			return result;
		}

		// Token: 0x060012DB RID: 4827 RVA: 0x00080FE4 File Offset: 0x0007F1E4
		public DateTime? method_17(DateTime dateTime_0, SortedList<DateTime, HisData> sortedList_2, ExchgOBT exchgOBT_0)
		{
			DateTime? dateTime = null;
			try
			{
				HisDataPeriodSet.Class268 @class = new HisDataPeriodSet.Class268();
				@class.dateTime_0 = dateTime_0.Date.Add(exchgOBT_0.DayOpenTime.Value.TimeOfDay);
				DateTime key = @class.dateTime_0.AddMinutes(1.0);
				int num = sortedList_2.IndexOfKey(key);
				if (num > 0)
				{
					int index = num - 1;
					dateTime = new DateTime?(sortedList_2.Keys[index]);
				}
				if (dateTime == null)
				{
					IEnumerable<DateTime> source = sortedList_2.Keys.Where(new Func<DateTime, bool>(@class.method_0));
					if (source.Any<DateTime>())
					{
						dateTime = new DateTime?(source.Last<DateTime>());
					}
				}
			}
			catch
			{
			}
			DateTime? result;
			if (dateTime != null && this.SymbDataSet != null)
			{
				exchgOBT_0 = this.SymbDataSet.method_67(dateTime.Value);
				result = exchgOBT_0.GetNightTradingStartDT(dateTime.Value);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060012DC RID: 4828 RVA: 0x000810FC File Offset: 0x0007F2FC
		[Obsolete]
		private SortedList<DateTime, HisData> method_18(SortedList<DateTime, HisData> sortedList_2, List<StartEndDT> list_0)
		{
			HisDataPeriodSet.Class269 @class = new HisDataPeriodSet.Class269();
			@class.sortedList_0 = sortedList_2;
			IEnumerable<HisData> source = list_0.SelectMany(new Func<StartEndDT, IEnumerable<KeyValuePair<DateTime, HisData>>>(@class.method_0), new Func<StartEndDT, KeyValuePair<DateTime, HisData>, <>f__AnonymousType16<StartEndDT, KeyValuePair<DateTime, HisData>>>(HisDataPeriodSet.<>c.<>9.method_4)).Where(new Func<<>f__AnonymousType16<StartEndDT, KeyValuePair<DateTime, HisData>>, bool>(HisDataPeriodSet.<>c.<>9.method_5)).Select(new Func<<>f__AnonymousType16<StartEndDT, KeyValuePair<DateTime, HisData>>, <>f__AnonymousType17<int, HisData>>(HisDataPeriodSet.<>c.<>9.method_6)).GroupBy(new Func<<>f__AnonymousType17<int, HisData>, int>(HisDataPeriodSet.<>c.<>9.method_7)).Select(new Func<IGrouping<int, <>f__AnonymousType17<int, HisData>>, HisData>(HisDataPeriodSet.<>c.<>9.method_8));
			source.ToList<HisData>();
			return new SortedList<DateTime, HisData>(source.ToDictionary(new Func<HisData, DateTime>(HisDataPeriodSet.<>c.<>9.method_12), new Func<HisData, HisData>(HisDataPeriodSet.<>c.<>9.method_13)));
		}

		// Token: 0x060012DD RID: 4829 RVA: 0x0008122C File Offset: 0x0007F42C
		private SortedList<DateTime, HisData> method_19(SortedList<DateTime, HisData> sortedList_2, int int_0)
		{
			SortedList<DateTime, HisData> result;
			if (sortedList_2 == null)
			{
				Class182.smethod_0(new Exception("hourHdList is null!"));
				result = null;
			}
			else
			{
				DateTime key = sortedList_2.First<KeyValuePair<DateTime, HisData>>().Key;
				DateTime key2 = sortedList_2.Last<KeyValuePair<DateTime, HisData>>().Key;
				List<KeyValuePair<DateTime, DateTime>> list = this.method_20(sortedList_2);
				IEnumerable<KeyValuePair<DateTime, HisData>> enumerable = new SortedList<DateTime, HisData>();
				foreach (KeyValuePair<DateTime, DateTime> keyValuePair in list)
				{
					DateTime key3 = keyValuePair.Key;
					DateTime value = keyValuePair.Value;
					ExchgOBT exchgOBT_ = Base.Data.smethod_110(this.Symbol, key3.Date);
					SortedList<DateTime, HisData> sortedList_3 = Base.Data.smethod_46(sortedList_2, this.Symbol, key3, value, 1, exchgOBT_);
					SortedList<DateTime, HisData> second = this.method_21(sortedList_3, exchgOBT_, int_0);
					enumerable = enumerable.Concat(second);
				}
				SortedList<DateTime, HisData> sortedList;
				try
				{
					sortedList = new SortedList<DateTime, HisData>(enumerable.ToDictionary(new Func<KeyValuePair<DateTime, HisData>, DateTime>(HisDataPeriodSet.<>c.<>9.method_14), new Func<KeyValuePair<DateTime, HisData>, HisData>(HisDataPeriodSet.<>c.<>9.method_15)));
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
					sortedList = new SortedList<DateTime, HisData>(enumerable.GroupBy(new Func<KeyValuePair<DateTime, HisData>, DateTime>(HisDataPeriodSet.<>c.<>9.method_16)).Select(new Func<IGrouping<DateTime, KeyValuePair<DateTime, HisData>>, KeyValuePair<DateTime, HisData>>(HisDataPeriodSet.<>c.<>9.method_17)).ToDictionary(new Func<KeyValuePair<DateTime, HisData>, DateTime>(HisDataPeriodSet.<>c.<>9.method_18), new Func<KeyValuePair<DateTime, HisData>, HisData>(HisDataPeriodSet.<>c.<>9.method_19)));
				}
				result = sortedList;
			}
			return result;
		}

		// Token: 0x060012DE RID: 4830 RVA: 0x00081408 File Offset: 0x0007F608
		private List<KeyValuePair<DateTime, DateTime>> method_20(SortedList<DateTime, HisData> sortedList_2)
		{
			List<KeyValuePair<DateTime, DateTime>> list = new List<KeyValuePair<DateTime, DateTime>>();
			DateTime key = sortedList_2.First<KeyValuePair<DateTime, HisData>>().Key;
			DateTime key2 = sortedList_2.Last<KeyValuePair<DateTime, HisData>>().Key;
			List<SymbNtTrDate> list2 = Base.Data.smethod_113(this.Symbol, key, key2);
			if (list2 != null && list2.Count > 0)
			{
				HisDataPeriodSet.Class270 @class = new HisDataPeriodSet.Class270();
				@class.symbNtTrDate_0 = list2.First<SymbNtTrDate>();
				DateTime value = @class.symbNtTrDate_0.FromDate.Value;
				if (value > key)
				{
					DateTime value2 = value.AddDays(-1.0);
					if (TApp.SrvParams.ExchgOBTList.FirstOrDefault(new Func<ExchgOBT, bool>(@class.method_0)).NightOpenTime != null)
					{
						value2 = value;
					}
					list.Add(new KeyValuePair<DateTime, DateTime>(key, value2));
				}
				@class.symbNtTrDate_1 = null;
				for (int i = 0; i < list2.Count; i++)
				{
					@class.symbNtTrDate_1 = list2[i];
					IEnumerable<ExchgOBT> exchgOBTList = TApp.SrvParams.ExchgOBTList;
					Func<ExchgOBT, bool> predicate;
					if ((predicate = @class.func_0) == null)
					{
						predicate = (@class.func_0 = new Func<ExchgOBT, bool>(@class.method_1));
					}
					ExchgOBT exchgOBT = exchgOBTList.FirstOrDefault(predicate);
					DateTime key3 = @class.symbNtTrDate_1.FromDate.Value;
					if (exchgOBT.NightOpenTime != null)
					{
						key3 = @class.symbNtTrDate_1.FromDate.Value.AddDays(1.0);
					}
					DateTime value3 = (@class.symbNtTrDate_1.ToDate != null) ? @class.symbNtTrDate_1.ToDate.Value : key2;
					list.Add(new KeyValuePair<DateTime, DateTime>(key3, value3));
				}
			}
			else
			{
				list.Add(new KeyValuePair<DateTime, DateTime>(key, key2));
			}
			return list;
		}

		// Token: 0x060012DF RID: 4831 RVA: 0x000815EC File Offset: 0x0007F7EC
		private SortedList<DateTime, HisData> method_21(SortedList<DateTime, HisData> sortedList_2, ExchgOBT exchgOBT_0, int int_0)
		{
			HisDataPeriodSet.Class271 @class = new HisDataPeriodSet.Class271();
			@class.hisDataPeriodSet_0 = this;
			@class.sortedList_0 = sortedList_2;
			@class.exchgOBT_0 = exchgOBT_0;
			SortedList<DateTime, HisData> sortedList = new SortedList<DateTime, HisData>();
			foreach (IGrouping<DateTime?, KeyValuePair<DateTime, HisData>> source in @class.sortedList_0.GroupBy(new Func<KeyValuePair<DateTime, HisData>, DateTime?>(@class.method_0)).Select(new Func<IGrouping<DateTime?, KeyValuePair<DateTime, HisData>>, IGrouping<DateTime?, KeyValuePair<DateTime, HisData>>>(HisDataPeriodSet.<>c.<>9.method_20)))
			{
				List<KeyValuePair<DateTime, HisData>> list = source.ToList<KeyValuePair<DateTime, HisData>>();
				int count = list.Count;
				for (int i = 0; i < count; i++)
				{
					List<HisData> list2 = new List<HisData>();
					int num = i;
					while (num < i + int_0 && num < count)
					{
						list2.Add(list[num].Value);
						num++;
					}
					HisData hisData = new HisData();
					hisData.Date = list2.Last<HisData>().Date;
					hisData.Open = list2.First<HisData>().Open;
					hisData.High = list2.Max(new Func<HisData, double>(HisDataPeriodSet.<>c.<>9.method_21));
					hisData.Low = list2.Min(new Func<HisData, double>(HisDataPeriodSet.<>c.<>9.method_22));
					hisData.Close = list2.Last<HisData>().Close;
					hisData.Volume = list2.Sum(new Func<HisData, double?>(HisDataPeriodSet.<>c.<>9.method_23));
					hisData.Amount = list2.Last<HisData>().Amount;
					HisData hisData2 = hisData;
					sortedList.Add(hisData2.Date, hisData2);
					i = num - 1;
				}
			}
			return sortedList;
		}

		// Token: 0x060012E0 RID: 4832 RVA: 0x000817E8 File Offset: 0x0007F9E8
		public DateTime? method_22(SortedList<DateTime, HisData> sortedList_2, DateTime dateTime_0, ExchgOBT exchgOBT_0)
		{
			DateTime? result;
			if (this.SymbDataSet != null && this.SymbDataSet.CurrHisDataSet != null)
			{
				result = new DateTime?(this.SymbDataSet.CurrHisDataSet.method_19(sortedList_2, dateTime_0, exchgOBT_0, this.Symbol, 60, new int?(30)));
			}
			else
			{
				DateTime? dateTime = null;
				if (exchgOBT_0.SupportsNightTrading)
				{
					dateTime = exchgOBT_0.GetNightTradingStartDT(dateTime_0);
				}
				else
				{
					dateTime = exchgOBT_0.GetDayTradingStartDT(dateTime_0);
				}
				if (dateTime == null)
				{
					if (dateTime_0.TimeOfDay > exchgOBT_0.DayCloseTime.Value.TimeOfDay)
					{
						dateTime = new DateTime?(dateTime_0.Date.AddDays(1.0));
					}
					else if (dateTime_0.TimeOfDay < exchgOBT_0.DayOpenTime.Value.TimeOfDay)
					{
						dateTime = new DateTime?(dateTime_0.Date);
					}
				}
				result = dateTime;
			}
			return result;
		}

		// Token: 0x060012E1 RID: 4833 RVA: 0x000818E0 File Offset: 0x0007FAE0
		private SortedList<DateTime, HisData> method_23(SortedList<DateTime, HisData> sortedList_2, bool bool_1, int? nullable_2)
		{
			HisDataPeriodSet.Class272 @class = new HisDataPeriodSet.Class272();
			@class.nullable_0 = nullable_2;
			SortedList<DateTime, HisData> sortedList;
			if (@class.nullable_0 != null && @class.nullable_0.Value >= 7)
			{
				sortedList = this.method_28(sortedList_2, bool_1);
			}
			else
			{
				sortedList = this.method_24(sortedList_2, bool_1);
			}
			if (@class.nullable_0 != null)
			{
				IEnumerable<IGrouping<int, KeyValuePair<DateTime, HisData>>> enumerable = sortedList.GroupBy(new Func<KeyValuePair<DateTime, HisData>, int>(HisDataPeriodSet.<>c.<>9.method_24)).Select(new Func<IGrouping<int, KeyValuePair<DateTime, HisData>>, IGrouping<int, KeyValuePair<DateTime, HisData>>>(HisDataPeriodSet.<>c.<>9.method_25));
				SortedList<DateTime, HisData> sortedList2 = new SortedList<DateTime, HisData>();
				foreach (IGrouping<int, KeyValuePair<DateTime, HisData>> source in enumerable)
				{
					try
					{
						new SortedList<DateTime, HisData>();
						var source2 = source.ToList<KeyValuePair<DateTime, HisData>>().ToDictionary(new Func<KeyValuePair<DateTime, HisData>, DateTime>(HisDataPeriodSet.<>c.<>9.method_26), new Func<KeyValuePair<DateTime, HisData>, HisData>(HisDataPeriodSet.<>c.<>9.method_27)).Values.Select(new Func<HisData, int, <>f__AnonymousType18<HisData, int>>(HisDataPeriodSet.<>c.<>9.method_28));
						var keySelector;
						if ((keySelector = @class.func_0) == null)
						{
							keySelector = (@class.func_0 = new Func<<>f__AnonymousType18<HisData, int>, int>(@class.method_0));
						}
						foreach (HisData hisData in source2.GroupBy(keySelector, new Func<<>f__AnonymousType18<HisData, int>, HisData>(HisDataPeriodSet.<>c.<>9.method_29)).Select(new Func<IGrouping<int, HisData>, HisData>(HisDataPeriodSet.<>c.<>9.method_30)))
						{
							sortedList2.Add(hisData.Date, hisData);
						}
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
				sortedList = sortedList2;
			}
			return sortedList;
		}

		// Token: 0x060012E2 RID: 4834 RVA: 0x00081B34 File Offset: 0x0007FD34
		private SortedList<DateTime, HisData> method_24(SortedList<DateTime, HisData> sortedList_2, bool bool_1)
		{
			SortedList<DateTime, HisData> result;
			if (sortedList_2 == null)
			{
				Class182.smethod_0(new Exception("hourHdList is null!"));
				result = null;
			}
			else
			{
				DateTime key = sortedList_2.First<KeyValuePair<DateTime, HisData>>().Key;
				DateTime key2 = sortedList_2.Last<KeyValuePair<DateTime, HisData>>().Key;
				List<KeyValuePair<DateTime, DateTime>> list = this.method_20(sortedList_2);
				IEnumerable<KeyValuePair<DateTime, HisData>> enumerable = new SortedList<DateTime, HisData>();
				foreach (KeyValuePair<DateTime, DateTime> keyValuePair in list)
				{
					DateTime key3 = keyValuePair.Key;
					DateTime value = keyValuePair.Value;
					ExchgOBT exchgOBT_ = Base.Data.smethod_110(this.Symbol, key3.Date);
					SortedList<DateTime, HisData> sortedList_3 = Base.Data.smethod_46(sortedList_2, this.Symbol, key3, value, 1, exchgOBT_);
					SortedList<DateTime, HisData> second = this.method_25(sortedList_3, exchgOBT_, bool_1);
					enumerable = enumerable.Concat(second);
				}
				result = new SortedList<DateTime, HisData>(enumerable.ToDictionary(new Func<KeyValuePair<DateTime, HisData>, DateTime>(HisDataPeriodSet.<>c.<>9.method_34), new Func<KeyValuePair<DateTime, HisData>, HisData>(HisDataPeriodSet.<>c.<>9.method_35)));
			}
			return result;
		}

		// Token: 0x060012E3 RID: 4835 RVA: 0x00081C60 File Offset: 0x0007FE60
		private SortedList<DateTime, HisData> method_25(SortedList<DateTime, HisData> sortedList_2, ExchgOBT exchgOBT_0, bool bool_1)
		{
			HisDataPeriodSet.Class273 @class = new HisDataPeriodSet.Class273();
			@class.hisDataPeriodSet_0 = this;
			@class.sortedList_0 = sortedList_2;
			@class.exchgOBT_0 = exchgOBT_0;
			SortedList<DateTime, HisData> sortedList = new SortedList<DateTime, HisData>();
			if (bool_1)
			{
				List<HisData> list = @class.sortedList_0.GroupBy(new Func<KeyValuePair<DateTime, HisData>, DateTime?>(@class.method_0)).Select(new Func<IGrouping<DateTime?, KeyValuePair<DateTime, HisData>>, HisData>(HisDataPeriodSet.<>c.<>9.method_36)).ToList<HisData>();
				int count = list.Count;
				for (int i = 0; i < count; i++)
				{
					HisData hisData = list[i];
					if (@class.exchgOBT_0.IsDayTradingDT(hisData.Date))
					{
						sortedList.Add(hisData.Date, hisData);
					}
					else if (i + 1 < count)
					{
						HisData hisData2 = list[i + 1];
						HisData hisData3 = new HisData();
						hisData3.Date = hisData2.Date;
						hisData3.Open = hisData.Open;
						hisData3.Low = ((hisData.Low < hisData2.Low) ? hisData.Low : hisData2.Low);
						hisData3.High = ((hisData.High > hisData2.High) ? hisData.High : hisData2.High);
						hisData3.Close = hisData2.Close;
						hisData3.Volume = hisData.Volume + hisData2.Volume;
						hisData3.Amount = hisData2.Amount;
						sortedList.Add(hisData2.Date, hisData3);
						i++;
					}
				}
			}
			else
			{
				foreach (HisData hisData4 in @class.sortedList_0.Where(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_1)).GroupBy(new Func<KeyValuePair<DateTime, HisData>, DateTime>(HisDataPeriodSet.<>c.<>9.method_40)).Select(new Func<IGrouping<DateTime, KeyValuePair<DateTime, HisData>>, HisData>(HisDataPeriodSet.<>c.<>9.method_41)))
				{
					sortedList.Add(hisData4.Date, hisData4);
				}
			}
			return sortedList;
		}

		// Token: 0x060012E4 RID: 4836 RVA: 0x00081EDC File Offset: 0x000800DC
		private SortedList<DateTime, HisData> method_26(SortedList<DateTime, HisData> sortedList_2, ExchgOBT exchgOBT_0, bool bool_1)
		{
			SortedList<DateTime, HisData> sortedList = new SortedList<DateTime, HisData>();
			IEnumerable<IGrouping<int, KeyValuePair<DateTime, HisData>>> enumerable = this.method_28(sortedList_2, bool_1).GroupBy(new Func<KeyValuePair<DateTime, HisData>, int>(HisDataPeriodSet.<>c.<>9.method_45)).Select(new Func<IGrouping<int, KeyValuePair<DateTime, HisData>>, IGrouping<int, KeyValuePair<DateTime, HisData>>>(HisDataPeriodSet.<>c.<>9.method_46));
			List<IGrouping<int, KeyValuePair<DateTime, HisData>>> list = new List<IGrouping<int, KeyValuePair<DateTime, HisData>>>();
			using (IEnumerator<IGrouping<int, KeyValuePair<DateTime, HisData>>> enumerator = enumerable.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					foreach (IGrouping<int, KeyValuePair<DateTime, HisData>> item in enumerator.Current.GroupBy(new Func<KeyValuePair<DateTime, HisData>, int>(HisDataPeriodSet.<>c.<>9.method_47)).Select(new Func<IGrouping<int, KeyValuePair<DateTime, HisData>>, IGrouping<int, KeyValuePair<DateTime, HisData>>>(HisDataPeriodSet.<>c.<>9.method_48)))
					{
						list.Add(item);
					}
				}
			}
			this.method_29(sortedList, list);
			return sortedList;
		}

		// Token: 0x060012E5 RID: 4837 RVA: 0x0008200C File Offset: 0x0008020C
		private SortedList<DateTime, HisData> method_27(SortedList<DateTime, HisData> sortedList_2, ExchgOBT exchgOBT_0, bool bool_1)
		{
			SortedList<DateTime, HisData> sortedList = new SortedList<DateTime, HisData>();
			IEnumerable<IGrouping<int, KeyValuePair<DateTime, HisData>>> enumerable = this.method_28(sortedList_2, bool_1).GroupBy(new Func<KeyValuePair<DateTime, HisData>, int>(HisDataPeriodSet.<>c.<>9.method_49)).Select(new Func<IGrouping<int, KeyValuePair<DateTime, HisData>>, IGrouping<int, KeyValuePair<DateTime, HisData>>>(HisDataPeriodSet.<>c.<>9.method_50));
			List<IGrouping<int, KeyValuePair<DateTime, HisData>>> list = new List<IGrouping<int, KeyValuePair<DateTime, HisData>>>();
			using (IEnumerator<IGrouping<int, KeyValuePair<DateTime, HisData>>> enumerator = enumerable.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					foreach (IGrouping<int, KeyValuePair<DateTime, HisData>> item in enumerator.Current.GroupBy(new Func<KeyValuePair<DateTime, HisData>, int>(HisDataPeriodSet.<>c.<>9.method_51)).Select(new Func<IGrouping<int, KeyValuePair<DateTime, HisData>>, IGrouping<int, KeyValuePair<DateTime, HisData>>>(HisDataPeriodSet.<>c.<>9.method_52)))
					{
						list.Add(item);
					}
				}
			}
			this.method_29(sortedList, list);
			return sortedList;
		}

		// Token: 0x060012E6 RID: 4838 RVA: 0x0008213C File Offset: 0x0008033C
		private SortedList<DateTime, HisData> method_28(SortedList<DateTime, HisData> sortedList_2, bool bool_1)
		{
			SortedList<DateTime, HisData> sortedList = this.method_24(sortedList_2, bool_1);
			SortedList<DateTime, HisData> sortedList2 = this.method_4(sortedList);
			if (sortedList2 != null)
			{
				sortedList = sortedList2;
			}
			return sortedList;
		}

		// Token: 0x060012E7 RID: 4839 RVA: 0x00082164 File Offset: 0x00080364
		private void method_29(SortedList<DateTime, HisData> sortedList_2, List<IGrouping<int, KeyValuePair<DateTime, HisData>>> list_0)
		{
			foreach (IGrouping<int, KeyValuePair<DateTime, HisData>> igrouping_ in list_0)
			{
				this.method_30(sortedList_2, igrouping_);
			}
		}

		// Token: 0x060012E8 RID: 4840 RVA: 0x000821B8 File Offset: 0x000803B8
		private void method_30(SortedList<DateTime, HisData> sortedList_2, IGrouping<int, KeyValuePair<DateTime, HisData>> igrouping_0)
		{
			if (igrouping_0 != null && igrouping_0.Any<KeyValuePair<DateTime, HisData>>())
			{
				try
				{
					SortedList<DateTime, HisData> sortedList = new SortedList<DateTime, HisData>(igrouping_0.ToDictionary(new Func<KeyValuePair<DateTime, HisData>, DateTime>(HisDataPeriodSet.<>c.<>9.method_53), new Func<KeyValuePair<DateTime, HisData>, HisData>(HisDataPeriodSet.<>c.<>9.method_54)));
					ExchgOBT exchgOBT = null;
					if (this.SymbDataSet != null)
					{
						exchgOBT = this.SymbDataSet.method_67(sortedList.Last<KeyValuePair<DateTime, HisData>>().Key);
					}
					DateTime dateTime_ = (exchgOBT != null) ? exchgOBT.GetDayCloseDT(igrouping_0.Last<KeyValuePair<DateTime, HisData>>().Key) : igrouping_0.Last<KeyValuePair<DateTime, HisData>>().Key;
					HisData hisData = this.method_32(sortedList, dateTime_);
					if (hisData != null)
					{
						sortedList_2.Add(hisData.Date, hisData);
					}
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
		}

		// Token: 0x060012E9 RID: 4841 RVA: 0x000822A4 File Offset: 0x000804A4
		private void method_31(SortedList<DateTime, HisData> sortedList_2, SortedList<DateTime, HisData> sortedList_3, DateTime dateTime_0, DateTime dateTime_1)
		{
			HisDataPeriodSet.Class274 @class = new HisDataPeriodSet.Class274();
			@class.dateTime_0 = dateTime_1;
			if (!sortedList_3.ContainsKey(@class.dateTime_0))
			{
				List<HisData> list = this.SymbDataSet.method_106(sortedList_2, dateTime_0, @class.dateTime_0, false, true, new int?(1));
				HisData hisData = null;
				if (list != null && list.Any<HisData>())
				{
					hisData = this.method_33(list, @class.dateTime_0);
				}
				else
				{
					HisData hisData2 = null;
					if (sortedList_3.Any<KeyValuePair<DateTime, HisData>>())
					{
						hisData2 = sortedList_3.Values.Last<HisData>();
					}
					else
					{
						IEnumerable<KeyValuePair<DateTime, HisData>> source = sortedList_2.Where(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_0));
						if (source.Any<KeyValuePair<DateTime, HisData>>())
						{
							hisData2 = source.Last<KeyValuePair<DateTime, HisData>>().Value;
						}
						else
						{
							source = sortedList_2.Where(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_1));
							if (source.Any<KeyValuePair<DateTime, HisData>>())
							{
								hisData2 = source.First<KeyValuePair<DateTime, HisData>>().Value;
							}
						}
					}
					if (hisData2 != null)
					{
						hisData = Class336.smethod_4(hisData2, @class.dateTime_0);
					}
				}
				if (hisData != null)
				{
					try
					{
						HisData hisData_ = null;
						if (sortedList_3.Any<KeyValuePair<DateTime, HisData>>())
						{
							hisData_ = sortedList_3.Values.Last<HisData>();
						}
						HisData hisData3 = HisDataPeriodSet.smethod_0(hisData, hisData_);
						sortedList_3.Add(hisData3.Date, hisData3);
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
			}
		}

		// Token: 0x060012EA RID: 4842 RVA: 0x000823E4 File Offset: 0x000805E4
		private HisData method_32(IEnumerable<KeyValuePair<DateTime, HisData>> ienumerable_0, DateTime dateTime_0)
		{
			HisData hisData = new HisData();
			hisData.Date = dateTime_0;
			hisData.Open = ienumerable_0.First<KeyValuePair<DateTime, HisData>>().Value.Open;
			hisData.Close = ienumerable_0.Last<KeyValuePair<DateTime, HisData>>().Value.Close;
			hisData.High = ienumerable_0.Max(new Func<KeyValuePair<DateTime, HisData>, double>(HisDataPeriodSet.<>c.<>9.method_55));
			hisData.Low = ienumerable_0.Min(new Func<KeyValuePair<DateTime, HisData>, double>(HisDataPeriodSet.<>c.<>9.method_56));
			hisData.Volume = ienumerable_0.Sum(new Func<KeyValuePair<DateTime, HisData>, double?>(HisDataPeriodSet.<>c.<>9.method_57));
			hisData.Amount = ienumerable_0.Last<KeyValuePair<DateTime, HisData>>().Value.Amount;
			return hisData;
		}

		// Token: 0x060012EB RID: 4843 RVA: 0x000824D0 File Offset: 0x000806D0
		private HisData method_33(List<HisData> list_0, DateTime dateTime_0)
		{
			HisData hisData = new HisData();
			hisData.Date = dateTime_0;
			HisData hisData2 = list_0.Last<HisData>();
			hisData.Open = list_0.First<HisData>().Open;
			hisData.Close = hisData2.Close;
			hisData.High = list_0.Max(new Func<HisData, double>(HisDataPeriodSet.<>c.<>9.method_58));
			hisData.Low = list_0.Min(new Func<HisData, double>(HisDataPeriodSet.<>c.<>9.method_59));
			hisData.Volume = list_0.Sum(new Func<HisData, double?>(HisDataPeriodSet.<>c.<>9.method_60));
			hisData.Amount = hisData2.Amount;
			return hisData;
		}

		// Token: 0x060012EC RID: 4844 RVA: 0x000825A0 File Offset: 0x000807A0
		private bool method_34(DateTime dateTime_0)
		{
			return this.PeriodHisDataList.ContainsKey(dateTime_0);
		}

		// Token: 0x060012ED RID: 4845 RVA: 0x000825C0 File Offset: 0x000807C0
		public bool method_35(HisData hisData_0)
		{
			return this.method_36(hisData_0.Date);
		}

		// Token: 0x060012EE RID: 4846 RVA: 0x000825E0 File Offset: 0x000807E0
		private bool method_36(DateTime dateTime_0)
		{
			DateTime? dateTime = this.SymbDataSet.method_66(dateTime_0);
			bool result;
			if (dateTime == null)
			{
				result = true;
			}
			else if (this.method_34(dateTime.Value))
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060012EF RID: 4847 RVA: 0x00082620 File Offset: 0x00080820
		public int method_37(DateTime dateTime_0, bool bool_1)
		{
			HisDataPeriodSet.Class275 @class = new HisDataPeriodSet.Class275();
			@class.dateTime_0 = dateTime_0;
			int num = -1;
			try
			{
				num = this.PeriodHisDataList.IndexOfKey(@class.dateTime_0);
				if (num < 0 && bool_1)
				{
					DateTime dateTime;
					if (this.SymbDataSet != null && this.SymbDataSet.CurrHisDataSet != null && this.SymbDataSet.CurrHisDataSet.CurrExchgOBT.IsTradingDT(@class.dateTime_0))
					{
						dateTime = this.PeriodHisDataList.Keys.FirstOrDefault(new Func<DateTime, bool>(@class.method_0));
					}
					else
					{
						dateTime = this.PeriodHisDataList.Keys.LastOrDefault(new Func<DateTime, bool>(@class.method_1));
					}
					if (dateTime != default(DateTime))
					{
						num = this.PeriodHisDataList.IndexOfKey(dateTime);
					}
				}
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
			return num;
		}

		// Token: 0x060012F0 RID: 4848 RVA: 0x00082708 File Offset: 0x00080908
		public DateTime? method_38(DateTime dateTime_0)
		{
			HisDataPeriodSet.Class276 @class = new HisDataPeriodSet.Class276();
			@class.dateTime_0 = dateTime_0;
			DateTime? result = null;
			if (this.PeriodHisDataList != null)
			{
				int num = this.PeriodHisDataList.IndexOfKey(@class.dateTime_0);
				if (num >= 0)
				{
					return new DateTime?(this.PeriodHisDataList.Keys[num]);
				}
				DateTime dateTime = this.PeriodHisDataList.Keys.FirstOrDefault(new Func<DateTime, bool>(@class.method_0));
				if (dateTime != default(DateTime))
				{
					return new DateTime?(dateTime);
				}
			}
			return result;
		}

		// Token: 0x060012F1 RID: 4849 RVA: 0x000827A4 File Offset: 0x000809A4
		public HisDataPeriodSet method_39(int int_0, PeriodType periodType_1, int? nullable_2)
		{
			return this.method_40(int_0, periodType_1, nullable_2, null, null);
		}

		// Token: 0x060012F2 RID: 4850 RVA: 0x000827D0 File Offset: 0x000809D0
		public HisDataPeriodSet method_40(int int_0, PeriodType periodType_1, int? nullable_2, DateTime? nullable_3, DateTime? nullable_4)
		{
			if (Convert.ToInt32(this.PeriodType) <= Convert.ToInt32(periodType_1) && (this.PeriodType != periodType_1 || this.PeriodUnits == null || nullable_2 == null || this.PeriodUnits.Value <= nullable_2.Value))
			{
				HisDataPeriodSet result;
				if (this.method_42(periodType_1, nullable_2))
				{
					result = (HisDataPeriodSet)this.System.ICloneable.Clone();
				}
				else
				{
					HisDataPeriodSet hisDataPeriodSet;
					if (nullable_3 != null && this.SymbDataSet != null)
					{
						hisDataPeriodSet = this.SymbDataSet.method_82(this.PeriodHisDataList, periodType_1, nullable_2, nullable_3, nullable_4, this.PeriodUnits.Value, 0);
					}
					else
					{
						hisDataPeriodSet = new HisDataPeriodSet(int_0, this.PeriodHisDataList, periodType_1, nullable_2);
					}
					result = hisDataPeriodSet;
				}
				return result;
			}
			throw new Exception("The period type/unit must be smaller than the current HDPeriodSet.");
		}

		// Token: 0x060012F3 RID: 4851 RVA: 0x000828A8 File Offset: 0x00080AA8
		public void method_41(HisDataPeriodSet hisDataPeriodSet_0)
		{
			try
			{
				this.PeriodHisDataList = new SortedList<DateTime, HisData>(this.PeriodHisDataList.Concat(hisDataPeriodSet_0.PeriodHisDataList).ToDictionary(new Func<KeyValuePair<DateTime, HisData>, DateTime>(HisDataPeriodSet.<>c.<>9.method_61), new Func<KeyValuePair<DateTime, HisData>, HisData>(HisDataPeriodSet.<>c.<>9.method_62)));
				this.CloseDataArray = Utility.ConcatDoubleArrays(this.CloseDataArray, hisDataPeriodSet_0.CloseDataArray);
				this.HighDataArray = Utility.ConcatDoubleArrays(this.HighDataArray, hisDataPeriodSet_0.HighDataArray);
				this.LowDataArray = Utility.ConcatDoubleArrays(this.LowDataArray, hisDataPeriodSet_0.LowDataArray);
				if (this.VolDataArray != null && hisDataPeriodSet_0.VolDataArray != null)
				{
					this.VolDataArray = Utility.ConcatDoubleArrays(this.VolDataArray, hisDataPeriodSet_0.VolDataArray);
				}
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x060012F4 RID: 4852 RVA: 0x0008299C File Offset: 0x00080B9C
		public bool method_42(PeriodType periodType_1, int? nullable_2)
		{
			bool result;
			if (this.PeriodType == periodType_1)
			{
				if (this.PeriodUnits != null || nullable_2 != null)
				{
					if (this.PeriodUnits == null && nullable_2 != null)
					{
						if (nullable_2.Value == 1)
						{
							goto IL_47;
						}
					}
					if (this.PeriodUnits != null && nullable_2 != null)
					{
						return this.PeriodUnits.Value == nullable_2.Value;
					}
					return false;
				}
				IL_47:
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060012F5 RID: 4853 RVA: 0x00082A30 File Offset: 0x00080C30
		public object Clone()
		{
			return new HisDataPeriodSet
			{
				SymbId = this.SymbId,
				ChtCtrl_KLineTag = this.ChtCtrl_KLineTag,
				CloseDataArray = this.CloseDataArray,
				HighDataArray = this.HighDataArray,
				LowDataArray = this.LowDataArray,
				NumberOfStickItemsPerDay = this.NumberOfStickItemsPerDay,
				PeriodHisDataList = this.method_43(this.PeriodHisDataList),
				PeriodUnits = this.PeriodUnits,
				PeriodType = this.PeriodType,
				VolDataArray = this.VolDataArray
			};
		}

		// Token: 0x060012F6 RID: 4854 RVA: 0x00082AC4 File Offset: 0x00080CC4
		private SortedList<DateTime, HisData> method_43(SortedList<DateTime, HisData> sortedList_2)
		{
			SortedList<DateTime, HisData> sortedList = new SortedList<DateTime, HisData>();
			HisData hisData_ = null;
			foreach (KeyValuePair<DateTime, HisData> keyValuePair in sortedList_2)
			{
				HisData hisData = HisDataPeriodSet.smethod_0(keyValuePair.Value, hisData_);
				sortedList.Add(keyValuePair.Key, hisData);
				hisData_ = hisData;
			}
			return sortedList;
		}

		// Token: 0x060012F7 RID: 4855 RVA: 0x00082B38 File Offset: 0x00080D38
		private static HisData smethod_0(HisData hisData_0, HisData hisData_1)
		{
			if (hisData_1 != null && (hisData_0.Date - hisData_1.Date).TotalDays < 30.0)
			{
				if (!HisDataPeriodSet.smethod_1(hisData_0.Open, hisData_1.Open))
				{
					hisData_0.Open = hisData_1.Open;
				}
				if (!HisDataPeriodSet.smethod_1(hisData_0.Close, hisData_1.Close))
				{
					hisData_0.Close = hisData_1.Close;
				}
				if (!HisDataPeriodSet.smethod_1(hisData_0.High, hisData_1.High))
				{
					hisData_0.High = hisData_1.High;
				}
				if (!HisDataPeriodSet.smethod_1(hisData_0.Low, hisData_1.Low))
				{
					hisData_0.Low = hisData_1.Low;
				}
			}
			return hisData_0;
		}

		// Token: 0x060012F8 RID: 4856 RVA: 0x00082BF0 File Offset: 0x00080DF0
		private static bool smethod_1(double double_4, double double_5)
		{
			bool result;
			if (double_4 <= 2147483647.0 && double_4 > 0.0 && double_4 / double_5 <= 5.0 && double_4 / double_5 >= 0.2)
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x170002D4 RID: 724
		// (get) Token: 0x060012F9 RID: 4857 RVA: 0x00082C40 File Offset: 0x00080E40
		// (set) Token: 0x060012FA RID: 4858 RVA: 0x00007D2F File Offset: 0x00005F2F
		public int SymbId
		{
			get
			{
				return this.stkSymbId;
			}
			set
			{
				this.stkSymbId = value;
			}
		}

		// Token: 0x170002D5 RID: 725
		// (get) Token: 0x060012FB RID: 4859 RVA: 0x00082C58 File Offset: 0x00080E58
		public StkSymbol Symbol
		{
			get
			{
				return SymbMgr.smethod_3(this.SymbId);
			}
		}

		// Token: 0x170002D6 RID: 726
		// (get) Token: 0x060012FC RID: 4860 RVA: 0x00082C74 File Offset: 0x00080E74
		public SymbDataSet SymbDataSet
		{
			get
			{
				SymbDataSet symbDataSet = null;
				try
				{
					symbDataSet = Base.Data.SymbDataSets.SingleOrDefault(new Func<SymbDataSet, bool>(this.method_46));
				}
				catch (Exception ex)
				{
					if (ex is InvalidOperationException)
					{
						Class182.smethod_0(ex);
						symbDataSet = Base.Data.SymbDataSets.FirstOrDefault(new Func<SymbDataSet, bool>(this.method_47));
					}
				}
				SymbDataSet result;
				if (symbDataSet == null && this.sds != null)
				{
					result = this.sds;
				}
				else
				{
					result = symbDataSet;
				}
				return result;
			}
		}

		// Token: 0x170002D7 RID: 727
		// (get) Token: 0x060012FD RID: 4861 RVA: 0x00082CF0 File Offset: 0x00080EF0
		// (set) Token: 0x060012FE RID: 4862 RVA: 0x00007D3A File Offset: 0x00005F3A
		public SortedList<DateTime, HisData> PeriodHisDataList
		{
			get
			{
				return this.sortedList_0;
			}
			set
			{
				this.sortedList_0 = value;
			}
		}

		// Token: 0x170002D8 RID: 728
		// (get) Token: 0x060012FF RID: 4863 RVA: 0x00082D08 File Offset: 0x00080F08
		// (set) Token: 0x06001300 RID: 4864 RVA: 0x00007D45 File Offset: 0x00005F45
		public PeriodType PeriodType
		{
			get
			{
				return this.periodType_0;
			}
			set
			{
				this.periodType_0 = value;
			}
		}

		// Token: 0x170002D9 RID: 729
		// (get) Token: 0x06001301 RID: 4865 RVA: 0x00082D20 File Offset: 0x00080F20
		// (set) Token: 0x06001302 RID: 4866 RVA: 0x00007D50 File Offset: 0x00005F50
		public int? PeriodUnits
		{
			get
			{
				return this.nullable_0;
			}
			set
			{
				this.nullable_0 = value;
			}
		}

		// Token: 0x170002DA RID: 730
		// (get) Token: 0x06001303 RID: 4867 RVA: 0x00082D38 File Offset: 0x00080F38
		// (set) Token: 0x06001304 RID: 4868 RVA: 0x00007D5B File Offset: 0x00005F5B
		public double[] CloseDataArray
		{
			get
			{
				return this.double_0;
			}
			set
			{
				this.double_0 = value;
			}
		}

		// Token: 0x170002DB RID: 731
		// (get) Token: 0x06001305 RID: 4869 RVA: 0x00082D50 File Offset: 0x00080F50
		// (set) Token: 0x06001306 RID: 4870 RVA: 0x00007D66 File Offset: 0x00005F66
		public double[] HighDataArray
		{
			get
			{
				return this.double_1;
			}
			set
			{
				this.double_1 = value;
			}
		}

		// Token: 0x170002DC RID: 732
		// (get) Token: 0x06001307 RID: 4871 RVA: 0x00082D68 File Offset: 0x00080F68
		// (set) Token: 0x06001308 RID: 4872 RVA: 0x00007D71 File Offset: 0x00005F71
		public double[] LowDataArray
		{
			get
			{
				return this.double_2;
			}
			set
			{
				this.double_2 = value;
			}
		}

		// Token: 0x170002DD RID: 733
		// (get) Token: 0x06001309 RID: 4873 RVA: 0x00082D80 File Offset: 0x00080F80
		// (set) Token: 0x0600130A RID: 4874 RVA: 0x00007D7C File Offset: 0x00005F7C
		public double[] VolDataArray
		{
			get
			{
				if (this.double_3 == null || this.double_3.Count<double>() < 1)
				{
					this.double_3 = this.method_11(this.sortedList_0);
				}
				return this.double_3;
			}
			set
			{
				this.double_3 = value;
			}
		}

		// Token: 0x170002DE RID: 734
		// (get) Token: 0x0600130B RID: 4875 RVA: 0x00082DC0 File Offset: 0x00080FC0
		// (set) Token: 0x0600130C RID: 4876 RVA: 0x00007D87 File Offset: 0x00005F87
		public int? NumberOfStickItemsPerDay
		{
			get
			{
				return this.nullable_1;
			}
			set
			{
				this.nullable_1 = value;
			}
		}

		// Token: 0x0600130D RID: 4877 RVA: 0x00082DD8 File Offset: 0x00080FD8
		public int? method_44(DateTime dateTime_0)
		{
			HisDataPeriodSet.Class277 @class = new HisDataPeriodSet.Class277();
			@class.dateTime_0 = dateTime_0;
			int? result = null;
			if (this.PeriodHisDataList != null && this.PeriodHisDataList.Any<KeyValuePair<DateTime, HisData>>() && this.PeriodHisDataList.Keys.First<DateTime>().Date <= @class.dateTime_0.Date && this.PeriodHisDataList.Keys.Last<DateTime>().Date >= @class.dateTime_0.Date)
			{
				result = new int?(this.PeriodHisDataList.Keys.Where(new Func<DateTime, bool>(@class.method_0)).Count<DateTime>());
			}
			return result;
		}

		// Token: 0x170002DF RID: 735
		// (get) Token: 0x0600130E RID: 4878 RVA: 0x00082E94 File Offset: 0x00081094
		// (set) Token: 0x0600130F RID: 4879 RVA: 0x00007D92 File Offset: 0x00005F92
		public string ChtCtrl_KLineTag
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x170002E0 RID: 736
		// (get) Token: 0x06001310 RID: 4880 RVA: 0x00082EAC File Offset: 0x000810AC
		public bool IsPeriodLong
		{
			get
			{
				bool result;
				if (this.PeriodType != PeriodType.ByDay && this.PeriodType != PeriodType.ByWeek && this.PeriodType != PeriodType.ByMonth)
				{
					if (this.PeriodType == PeriodType.ByMins && this.PeriodUnits != null)
					{
						int? periodUnits = this.PeriodUnits;
						if (periodUnits.GetValueOrDefault() >= 60 & periodUnits != null)
						{
							return Utility.CanExactDiv(this.PeriodUnits.Value, 60);
						}
					}
					result = false;
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x170002E1 RID: 737
		// (get) Token: 0x06001311 RID: 4881 RVA: 0x00082F30 File Offset: 0x00081130
		public bool IsPeriod1m
		{
			get
			{
				bool result;
				if (this.PeriodType == PeriodType.ByMins)
				{
					int? periodUnits = this.PeriodUnits;
					result = (periodUnits.GetValueOrDefault() == 1 & periodUnits != null);
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x170002E2 RID: 738
		// (get) Token: 0x06001312 RID: 4882 RVA: 0x00082F68 File Offset: 0x00081168
		public string PeriodDesc
		{
			get
			{
				return Base.UI.smethod_174(this.PeriodType, this.PeriodUnits);
			}
		}

		// Token: 0x06001313 RID: 4883 RVA: 0x00082F8C File Offset: 0x0008118C
		public bool method_45(HisDataPeriodSet hisDataPeriodSet_0)
		{
			if (this.PeriodType == hisDataPeriodSet_0.PeriodType)
			{
				int? periodUnits = this.PeriodUnits;
				int? periodUnits2 = hisDataPeriodSet_0.PeriodUnits;
				if (periodUnits.GetValueOrDefault() == periodUnits2.GetValueOrDefault() & periodUnits != null == (periodUnits2 != null))
				{
					return true;
				}
			}
			return false;
		}

		// Token: 0x170002E3 RID: 739
		// (get) Token: 0x06001314 RID: 4884 RVA: 0x00082FE4 File Offset: 0x000811E4
		public int Count
		{
			get
			{
				return this.PeriodHisDataList.Count;
			}
		}

		// Token: 0x170002E4 RID: 740
		// (get) Token: 0x06001315 RID: 4885 RVA: 0x00083000 File Offset: 0x00081200
		public int FirstYear
		{
			get
			{
				return this.PeriodHisDataList.Keys.First<DateTime>().Year;
			}
		}

		// Token: 0x170002E5 RID: 741
		// (get) Token: 0x06001316 RID: 4886 RVA: 0x0008302C File Offset: 0x0008122C
		public int LastYear
		{
			get
			{
				return this.PeriodHisDataList.Last<KeyValuePair<DateTime, HisData>>().Key.Year;
			}
		}

		// Token: 0x170002E6 RID: 742
		// (get) Token: 0x06001317 RID: 4887 RVA: 0x00083058 File Offset: 0x00081258
		// (set) Token: 0x06001318 RID: 4888 RVA: 0x00007D9D File Offset: 0x00005F9D
		public bool IsStockPriceRestored
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x170002E7 RID: 743
		// (get) Token: 0x06001319 RID: 4889 RVA: 0x00083070 File Offset: 0x00081270
		public bool IsNMinsPeriod
		{
			get
			{
				return HisDataPeriodSet.smethod_2(this.PeriodType, this.PeriodUnits);
			}
		}

		// Token: 0x0600131A RID: 4890 RVA: 0x00083094 File Offset: 0x00081294
		public static bool smethod_2(PeriodType periodType_1, int? nullable_2)
		{
			bool result;
			if (periodType_1 == PeriodType.ByMins && nullable_2 != null)
			{
				int? num = nullable_2;
				if (!(num.GetValueOrDefault() < 60 & num != null))
				{
					result = !Utility.CanExactDiv(nullable_2.Value, 60);
				}
				else
				{
					result = true;
				}
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600131B RID: 4891 RVA: 0x000830E4 File Offset: 0x000812E4
		public static HisDataPeriodSet smethod_3(int int_0, DateTime dateTime_0, PeriodType periodType_1, int? nullable_2)
		{
			HisData item = Base.Data.smethod_126(dateTime_0);
			return new HisDataPeriodSet(int_0, new List<HisData>
			{
				item
			}, periodType_1, nullable_2);
		}

		// Token: 0x0600131C RID: 4892 RVA: 0x00083114 File Offset: 0x00081314
		[CompilerGenerated]
		private bool method_46(SymbDataSet symbDataSet_0)
		{
			return symbDataSet_0.SymblID == this.SymbId;
		}

		// Token: 0x0600131D RID: 4893 RVA: 0x00083114 File Offset: 0x00081314
		[CompilerGenerated]
		private bool method_47(SymbDataSet symbDataSet_0)
		{
			return symbDataSet_0.SymblID == this.SymbId;
		}

		// Token: 0x040009CC RID: 2508
		private int stkSymbId;

		// Token: 0x040009CD RID: 2509
		private SortedList<DateTime, HisData> sortedList_0;

		// Token: 0x040009CE RID: 2510
		private PeriodType periodType_0;

		// Token: 0x040009CF RID: 2511
		private int? nullable_0;

		// Token: 0x040009D0 RID: 2512
		private double[] double_0;

		// Token: 0x040009D1 RID: 2513
		private double[] double_1;

		// Token: 0x040009D2 RID: 2514
		private double[] double_2;

		// Token: 0x040009D3 RID: 2515
		private double[] double_3;

		// Token: 0x040009D4 RID: 2516
		private int? nullable_1;

		// Token: 0x040009D5 RID: 2517
		private SortedList<DateTime, HisData> sortedList_1;

		// Token: 0x040009D6 RID: 2518
		private string string_0;

		// Token: 0x040009D7 RID: 2519
		private bool ifGenFromFetchedHDList;

		// Token: 0x040009D8 RID: 2520
		private SymbDataSet sds;

		// Token: 0x040009D9 RID: 2521
		public bool bool_0;

		// Token: 0x020001E4 RID: 484
		[CompilerGenerated]
		private sealed class Class265
		{
			// Token: 0x04000A1A RID: 2586
			public ExchgOBT exchgOBT_0;
		}

		// Token: 0x020001E5 RID: 485
		[CompilerGenerated]
		private sealed class Class266
		{
			// Token: 0x06001361 RID: 4961 RVA: 0x0008370C File Offset: 0x0008190C
			internal bool method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				bool result;
				if (keyValuePair_0.Key > this.dateTime_0)
				{
					result = this.class265_0.exchgOBT_0.IsDayTradingDT(keyValuePair_0.Key);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000A1B RID: 2587
			public DateTime dateTime_0;

			// Token: 0x04000A1C RID: 2588
			public HisDataPeriodSet.Class265 class265_0;
		}

		// Token: 0x020001E6 RID: 486
		[CompilerGenerated]
		private sealed class Class267
		{
			// Token: 0x06001363 RID: 4963 RVA: 0x0008374C File Offset: 0x0008194C
			internal bool method_0(DateTime dateTime_1)
			{
				return dateTime_1 > this.dateTime_0;
			}

			// Token: 0x04000A1D RID: 2589
			public DateTime dateTime_0;
		}

		// Token: 0x020001E7 RID: 487
		[CompilerGenerated]
		private sealed class Class268
		{
			// Token: 0x06001365 RID: 4965 RVA: 0x0008376C File Offset: 0x0008196C
			internal bool method_0(DateTime dateTime_1)
			{
				return dateTime_1 < this.dateTime_0;
			}

			// Token: 0x04000A1E RID: 2590
			public DateTime dateTime_0;
		}

		// Token: 0x020001E8 RID: 488
		[CompilerGenerated]
		private sealed class Class269
		{
			// Token: 0x06001367 RID: 4967 RVA: 0x0008378C File Offset: 0x0008198C
			internal IEnumerable<KeyValuePair<DateTime, HisData>> method_0(StartEndDT startEndDT_0)
			{
				return this.sortedList_0;
			}

			// Token: 0x04000A1F RID: 2591
			public SortedList<DateTime, HisData> sortedList_0;
		}

		// Token: 0x020001E9 RID: 489
		[CompilerGenerated]
		private sealed class Class270
		{
			// Token: 0x06001369 RID: 4969 RVA: 0x000837A4 File Offset: 0x000819A4
			internal bool method_0(ExchgOBT exchgOBT_0)
			{
				return exchgOBT_0.ID == this.symbNtTrDate_0.ExchgOBTID;
			}

			// Token: 0x0600136A RID: 4970 RVA: 0x000837C8 File Offset: 0x000819C8
			internal bool method_1(ExchgOBT exchgOBT_0)
			{
				return exchgOBT_0.ID == this.symbNtTrDate_1.ExchgOBTID;
			}

			// Token: 0x04000A20 RID: 2592
			public SymbNtTrDate symbNtTrDate_0;

			// Token: 0x04000A21 RID: 2593
			public SymbNtTrDate symbNtTrDate_1;

			// Token: 0x04000A22 RID: 2594
			public Func<ExchgOBT, bool> func_0;
		}

		// Token: 0x020001EA RID: 490
		[CompilerGenerated]
		private sealed class Class271
		{
			// Token: 0x0600136C RID: 4972 RVA: 0x000837EC File Offset: 0x000819EC
			internal DateTime? method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				HisDataPeriodSet hisDataPeriodSet = this.hisDataPeriodSet_0;
				SortedList<DateTime, HisData> sortedList_ = this.sortedList_0;
				KeyValuePair<DateTime, HisData> keyValuePair = keyValuePair_0;
				return hisDataPeriodSet.method_22(sortedList_, keyValuePair.Key, this.exchgOBT_0);
			}

			// Token: 0x04000A23 RID: 2595
			public HisDataPeriodSet hisDataPeriodSet_0;

			// Token: 0x04000A24 RID: 2596
			public SortedList<DateTime, HisData> sortedList_0;

			// Token: 0x04000A25 RID: 2597
			public ExchgOBT exchgOBT_0;
		}

		// Token: 0x020001EB RID: 491
		[CompilerGenerated]
		private sealed class Class272
		{
			// Token: 0x0600136E RID: 4974 RVA: 0x00083820 File Offset: 0x00081A20
			internal int method_0(<>f__AnonymousType18<HisData, int> <>f__AnonymousType18_0)
			{
				return <>f__AnonymousType18_0.index / this.nullable_0.Value;
			}

			// Token: 0x04000A26 RID: 2598
			public int? nullable_0;

			// Token: 0x04000A27 RID: 2599
			public Func<<>f__AnonymousType18<HisData, int>, int> func_0;
		}

		// Token: 0x020001EC RID: 492
		[CompilerGenerated]
		private sealed class Class273
		{
			// Token: 0x06001370 RID: 4976 RVA: 0x00083844 File Offset: 0x00081A44
			internal DateTime? method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				HisDataPeriodSet hisDataPeriodSet = this.hisDataPeriodSet_0;
				SortedList<DateTime, HisData> sortedList_ = this.sortedList_0;
				KeyValuePair<DateTime, HisData> keyValuePair = keyValuePair_0;
				return hisDataPeriodSet.method_22(sortedList_, keyValuePair.Key, this.exchgOBT_0);
			}

			// Token: 0x06001371 RID: 4977 RVA: 0x00083878 File Offset: 0x00081A78
			internal bool method_1(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				ExchgOBT exchgOBT = this.exchgOBT_0;
				KeyValuePair<DateTime, HisData> keyValuePair = keyValuePair_0;
				return exchgOBT.IsDayTradingDT(keyValuePair.Key);
			}

			// Token: 0x04000A28 RID: 2600
			public HisDataPeriodSet hisDataPeriodSet_0;

			// Token: 0x04000A29 RID: 2601
			public SortedList<DateTime, HisData> sortedList_0;

			// Token: 0x04000A2A RID: 2602
			public ExchgOBT exchgOBT_0;
		}

		// Token: 0x020001ED RID: 493
		[CompilerGenerated]
		private sealed class Class274
		{
			// Token: 0x06001373 RID: 4979 RVA: 0x000838A0 File Offset: 0x00081AA0
			internal bool method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				return keyValuePair_0.Key < this.dateTime_0;
			}

			// Token: 0x06001374 RID: 4980 RVA: 0x000838C4 File Offset: 0x00081AC4
			internal bool method_1(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				return keyValuePair_0.Key >= this.dateTime_0;
			}

			// Token: 0x04000A2B RID: 2603
			public DateTime dateTime_0;
		}

		// Token: 0x020001EE RID: 494
		[CompilerGenerated]
		private sealed class Class275
		{
			// Token: 0x06001376 RID: 4982 RVA: 0x000838E8 File Offset: 0x00081AE8
			internal bool method_0(DateTime dateTime_1)
			{
				return dateTime_1 >= this.dateTime_0;
			}

			// Token: 0x06001377 RID: 4983 RVA: 0x00083908 File Offset: 0x00081B08
			internal bool method_1(DateTime dateTime_1)
			{
				return dateTime_1 <= this.dateTime_0;
			}

			// Token: 0x04000A2C RID: 2604
			public DateTime dateTime_0;
		}

		// Token: 0x020001EF RID: 495
		[CompilerGenerated]
		private sealed class Class276
		{
			// Token: 0x06001379 RID: 4985 RVA: 0x00083928 File Offset: 0x00081B28
			internal bool method_0(DateTime dateTime_1)
			{
				return dateTime_1 >= this.dateTime_0;
			}

			// Token: 0x04000A2D RID: 2605
			public DateTime dateTime_0;
		}

		// Token: 0x020001F0 RID: 496
		[CompilerGenerated]
		private sealed class Class277
		{
			// Token: 0x0600137B RID: 4987 RVA: 0x00083948 File Offset: 0x00081B48
			internal bool method_0(DateTime dateTime_1)
			{
				return dateTime_1.Date == this.dateTime_0.Date;
			}

			// Token: 0x04000A2E RID: 2606
			public DateTime dateTime_0;
		}
	}
}
