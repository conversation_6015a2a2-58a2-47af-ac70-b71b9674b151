﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Runtime.Serialization;
using ns28;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000195 RID: 405
	[Serializable]
	internal class DrawRedArwUp : DrawObj, ISerializable
	{
		// Token: 0x06000FA6 RID: 4006 RVA: 0x000037AA File Offset: 0x000019AA
		public DrawRedArwUp()
		{
		}

		// Token: 0x06000FA7 RID: 4007 RVA: 0x00006B75 File Offset: 0x00004D75
		public DrawRedArwUp(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = "上箭头";
			base.CanChgColor = false;
			base.IsOneClickLoc = true;
		}

		// Token: 0x06000FA8 RID: 4008 RVA: 0x000037DC File Offset: 0x000019DC
		protected DrawRedArwUp(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x06000FA9 RID: 4009 RVA: 0x000037ED File Offset: 0x000019ED
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x06000FAA RID: 4010 RVA: 0x00062298 File Offset: 0x00060498
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			ImageObj item = this.method_39(chartCS_1, double_1, double_2, string_5);
			list.Add(item);
			return list;
		}

		// Token: 0x06000FAB RID: 4011 RVA: 0x000622C4 File Offset: 0x000604C4
		private ImageObj method_39(ChartCS chartCS_1, double double_1, double double_2, string string_5)
		{
			Image image = this.vmethod_24();
			double num = (double)image.Width;
			double num2 = (double)image.Height;
			GraphPane graphPane = chartCS_1.GraphPane;
			double num3 = (graphPane.XAxis.Scale.Max - graphPane.XAxis.Scale.Min) / (double)graphPane.Rect.Width * num;
			double height = (graphPane.YAxis.Scale.Max - graphPane.YAxis.Scale.Min) / (double)graphPane.Rect.Height * num2;
			ImageObj imageObj = new ImageObj(image, double_1 - num3 / 2.0, double_2, num3, height);
			imageObj.IsClippedToChartRect = true;
			imageObj.IsScaled = false;
			imageObj.ZOrder = ZOrder.A_InFront;
			imageObj.Tag = string_5;
			this.imageObj_0 = imageObj;
			return imageObj;
		}

		// Token: 0x06000FAC RID: 4012 RVA: 0x000623A4 File Offset: 0x000605A4
		protected virtual Image vmethod_24()
		{
			return Class372.RedArrow_Up;
		}

		// Token: 0x06000FAD RID: 4013 RVA: 0x000041AE File Offset: 0x000023AE
		protected override void vmethod_12(ChartCS chartCS_1)
		{
		}

		// Token: 0x06000FAE RID: 4014 RVA: 0x0001FB74 File Offset: 0x0001DD74
		protected override DrawLineStyle vmethod_23()
		{
			return null;
		}

		// Token: 0x040007C6 RID: 1990
		private ImageObj imageObj_0;
	}
}
