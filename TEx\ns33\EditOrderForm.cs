﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using TEx;
using TEx.Trading;

namespace ns33
{
	// Token: 0x0200016B RID: 363
	internal sealed partial class EditOrderForm : Form
	{
		// Token: 0x06000DC6 RID: 3526 RVA: 0x00056C2C File Offset: 0x00054E2C
		public EditOrderForm(int int_0)
		{
			this.InitializeComponent();
			this.order_0 = Base.Trading.smethod_30(int_0);
			StkSymbol stkSymbol = SymbMgr.smethod_3(this.order_0.SymbolID);
			this.StkSymbol = stkSymbol;
			this.label_Desc.Text = Base.Trading.smethod_35((OrderType)this.order_0.OrderType);
			this.method_0(this.numericUpDown_Price);
			this.method_1(this.numericUpDown_Units);
			long num = this.order_0.Units;
			if (num < 1L)
			{
				num = 1L;
			}
			this.numericUpDown_Units.Value = num;
			this.numericUpDown_Price.Value = this.order_0.Price;
			this.btn_OK.Click += this.btn_OK_Click;
			this.btn_Cancel.Click += this.btn_Cancel_Click;
			base.Load += this.EditOrderForm_Load;
		}

		// Token: 0x06000DC7 RID: 3527 RVA: 0x00056D28 File Offset: 0x00054F28
		private void EditOrderForm_Load(object sender, EventArgs e)
		{
			if (base.Owner != null)
			{
				base.Location = new Point(base.Owner.Location.X + base.Owner.Width / 2 - base.Width / 2, base.Owner.Location.Y + base.Owner.Height / 2 - base.Height / 2);
			}
		}

		// Token: 0x06000DC8 RID: 3528 RVA: 0x00056DA0 File Offset: 0x00054FA0
		private void method_0(NumericUpDown numericUpDown_0)
		{
			decimal value = this.stkSymbol_0.LeastPriceVar.Value;
			numericUpDown_0.Increment = value;
			numericUpDown_0.Maximum = 99999999m;
			numericUpDown_0.Minimum = 0m;
			numericUpDown_0.DecimalPlaces = this.stkSymbol_0.DigitNb;
			numericUpDown_0.Increment = value;
		}

		// Token: 0x06000DC9 RID: 3529 RVA: 0x0000620D File Offset: 0x0000440D
		private void method_1(NumericUpDown numericUpDown_0)
		{
			numericUpDown_0.Increment = 1m;
			numericUpDown_0.Maximum = 9999999m;
			numericUpDown_0.Minimum = 1m;
		}

		// Token: 0x06000DCA RID: 3530 RVA: 0x00056E00 File Offset: 0x00055000
		private void btn_OK_Click(object sender, EventArgs e)
		{
			decimal value = this.numericUpDown_Units.Value;
			decimal value2 = this.numericUpDown_Price.Value;
			if (this.order_0.Price != value2 || this.order_0.Units != value)
			{
				Base.Trading.smethod_63(this.Order.ID, value2, (long)Convert.ToInt32(value));
			}
			base.Close();
		}

		// Token: 0x06000DCB RID: 3531 RVA: 0x00004268 File Offset: 0x00002468
		private void btn_Cancel_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x17000229 RID: 553
		// (get) Token: 0x06000DCC RID: 3532 RVA: 0x00056E70 File Offset: 0x00055070
		// (set) Token: 0x06000DCD RID: 3533 RVA: 0x00006237 File Offset: 0x00004437
		public Order Order
		{
			get
			{
				return this.order_0;
			}
			set
			{
				this.order_0 = value;
			}
		}

		// Token: 0x1700022A RID: 554
		// (get) Token: 0x06000DCE RID: 3534 RVA: 0x00056E88 File Offset: 0x00055088
		// (set) Token: 0x06000DCF RID: 3535 RVA: 0x00056EA0 File Offset: 0x000550A0
		public StkSymbol StkSymbol
		{
			get
			{
				return this.stkSymbol_0;
			}
			set
			{
				this.stkSymbol_0 = value;
				if (Base.UI.Form.IsInBlindTestMode && !Base.UI.Form.IsSingleBlindTest)
				{
					this.label_Symb.Text = "●●";
				}
				else
				{
					this.label_Symb.Text = this.stkSymbol_0.Code;
				}
			}
		}

		// Token: 0x06000DD0 RID: 3536 RVA: 0x00006242 File Offset: 0x00004442
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000710 RID: 1808
		private Order order_0;

		// Token: 0x04000711 RID: 1809
		private StkSymbol stkSymbol_0;

		// Token: 0x04000712 RID: 1810
		private IContainer icontainer_0;
	}
}
