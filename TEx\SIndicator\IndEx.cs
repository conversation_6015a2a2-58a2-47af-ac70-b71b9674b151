﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using ns11;
using ns21;
using ns27;
using ns28;
using ns33;
using TEx.Chart;
using TEx.Comn;
using TEx.Inds;

namespace TEx.SIndicator
{
	// Token: 0x020002DB RID: 731
	internal class IndEx : Indicator
	{
		// Token: 0x06002093 RID: 8339 RVA: 0x0000D2DA File Offset: 0x0000B4DA
		protected IndEx(UserDefineInd udi)
		{
			this.list_0 = new List<ShapeCurve>();
			this.UDInd = udi;
			this.method_2();
		}

		// Token: 0x06002094 RID: 8340 RVA: 0x000E0300 File Offset: 0x000DE500
		public static IndEx smethod_0(UserDefineInd userDefineInd_2)
		{
			IndEx result;
			if (!(userDefineInd_2.Name == "VOL") && !(userDefineInd_2.Name == "CJL"))
			{
				result = new IndEx(userDefineInd_2);
			}
			else
			{
				result = new Class347(userDefineInd_2);
			}
			return result;
		}

		// Token: 0x06002095 RID: 8341 RVA: 0x000E0344 File Offset: 0x000DE544
		public override void AddNewItem(int itemIdx, HisData newhd, bool ifAddCompleteItem)
		{
			if (base.IsSelected)
			{
				base.RemoveSelectedBoxPts();
			}
			if (base.Chart.HisDataPeriodSet.IsPeriod1m || ifAddCompleteItem)
			{
				if (itemIdx == base.EndIdx)
				{
					if (base.Chart.HisDataPeriodSet != null)
					{
						IList<HisData> values = base.Chart.HisDataPeriodSet.PeriodHisDataList.Values;
						HisData hisData_;
						if (itemIdx < values.Count && itemIdx >= 0)
						{
							hisData_ = values[itemIdx];
						}
						else
						{
							hisData_ = values[values.Count - 1];
						}
						this.method_0(itemIdx, hisData_, true);
						goto IL_C3;
					}
					goto IL_C3;
				}
				else
				{
					using (List<ShapeCurve>.Enumerator enumerator = this.list_0.GetEnumerator())
					{
						while (enumerator.MoveNext())
						{
							ShapeCurve shapeCurve = enumerator.Current;
							shapeCurve.vmethod_2(itemIdx);
						}
						goto IL_C3;
					}
				}
			}
			this.method_0(itemIdx, newhd, true);
			IL_C3:
			for (int i = 0; i < base.CurveList.Count; i++)
			{
				if (!base.CurveList[i].Curve.IsVisible)
				{
					base.CurveList[i].Curve.IsVisible = true;
				}
			}
		}

		// Token: 0x06002096 RID: 8342 RVA: 0x000E046C File Offset: 0x000DE66C
		protected void method_0(int int_0, HisData hisData_0, bool bool_0)
		{
			DataArray[] array = this.method_8(int_0, hisData_0, bool_0);
			if (array != null)
			{
				DataArray[] array2 = this.method_1(array);
				for (int i = 0; i < this.list_0.Count; i++)
				{
					ShapeCurve shapeCurve = this.list_0[i];
					if (shapeCurve is Class397)
					{
						(shapeCurve as Class397).method_6(int_0, hisData_0, bool_0);
					}
					if (bool_0)
					{
						this.list_0[i].vmethod_3(int_0, array2[i]);
					}
					else
					{
						shapeCurve.vmethod_4(int_0, array2[i]);
					}
				}
			}
		}

		// Token: 0x06002097 RID: 8343 RVA: 0x000E04F0 File Offset: 0x000DE6F0
		public override void RescaleAxis()
		{
			double num = 0.0;
			double num2 = 0.0;
			this.method_6(ref num, ref num2);
			double num3 = (num - num2) / 10.0;
			if (this.GraphControl != null && this.GraphControl.GraphPane != null)
			{
				if (num3 != 0.0)
				{
					this.GraphControl.GraphPane.YAxis.Scale.Max = num + num3;
					this.GraphControl.GraphPane.YAxis.Scale.Min = num2 - num3;
				}
				else
				{
					this.GraphControl.GraphPane.Y2Axis.Scale.MaxAuto = true;
					this.GraphControl.GraphPane.Y2Axis.Scale.MinAuto = true;
				}
			}
			else
			{
				Class182.smethod_0(new Exception("GraphPane为null!"));
			}
		}

		// Token: 0x06002098 RID: 8344 RVA: 0x0000D2FC File Offset: 0x0000B4FC
		public override void ResetCurve()
		{
			this.userDefineInd_0 = null;
			base.ResetCurve();
		}

		// Token: 0x06002099 RID: 8345 RVA: 0x000E05D8 File Offset: 0x000DE7D8
		private DataArray[] method_1(DataArray[] dataArray_0)
		{
			List<DataArray> list = new List<DataArray>();
			foreach (DataArray dataArray in dataArray_0.Where(new Func<DataArray, bool>(IndEx.<>c.<>9.method_0)).ToList<DataArray>())
			{
				if (dataArray.ShapeStr == "DRAWCOLORLINE")
				{
					List<DataArray> collection = Class380.smethod_1(dataArray);
					list.AddRange(collection);
				}
				else
				{
					list.Add(dataArray);
				}
			}
			if (list.Count != this.list_0.Count)
			{
				throw new Exception("VisibleData数据长度错误");
			}
			return list.ToArray();
		}

		// Token: 0x0600209A RID: 8346 RVA: 0x0000D30D File Offset: 0x0000B50D
		public override void UpdateLastItem(int itemIdx, HisData hd)
		{
			this.method_0(itemIdx, hd, false);
		}

		// Token: 0x0600209B RID: 8347 RVA: 0x0000D31A File Offset: 0x0000B51A
		private void method_2()
		{
			base.IsMainChartInd = this.UDInd.UDS.MainK;
		}

		// Token: 0x0600209C RID: 8348 RVA: 0x000E06A4 File Offset: 0x000DE8A4
		protected void method_3(ChartTheme chartTheme_0, List<ShapeCurve> list_1)
		{
			if (list_1 != null && list_1.Count != 0)
			{
				List<Color> list = new List<Color>();
				List<ShapeCurve> list2 = list_1.Where(new Func<ShapeCurve, bool>(IndEx.<>c.<>9.method_1)).ToList<ShapeCurve>();
				List<ShapeCurve> list3 = new List<ShapeCurve>();
				foreach (ShapeCurve shapeCurve in list2)
				{
					try
					{
						Color? color = ParserEnvironment.smethod_7(shapeCurve.IndData.ColorStr);
						if (color == null)
						{
							list.Add(IndEx.smethod_1(chartTheme_0, list.ToArray()));
							list3.Add(shapeCurve);
						}
						else
						{
							shapeCurve.Curve.Color = color.Value;
						}
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
				if (list.Count != list3.Count)
				{
					throw new Exception("颜色容器的长度和数据容器的长度不同");
				}
				for (int i = 0; i < list3.Count; i++)
				{
					list3[i].Curve.Color = list[i];
				}
				foreach (ShapeCurve shapeCurve2 in list_1.Where(new Func<ShapeCurve, bool>(IndEx.<>c.<>9.method_2)).ToList<ShapeCurve>())
				{
					Color? color2 = ParserEnvironment.smethod_7(shapeCurve2.IndData.ColorStr);
					if (color2 != null)
					{
						shapeCurve2.Curve.Color = color2.Value;
					}
					if (shapeCurve2 is Class397 || shapeCurve2 is Class395)
					{
						JapaneseCandleStickItem japaneseCandleStickItem = shapeCurve2.Curve as JapaneseCandleStickItem;
						if (japaneseCandleStickItem != null)
						{
							this.method_4(japaneseCandleStickItem, chartTheme_0);
						}
					}
				}
			}
		}

		// Token: 0x0600209D RID: 8349 RVA: 0x000E0898 File Offset: 0x000DEA98
		private void method_4(JapaneseCandleStickItem japaneseCandleStickItem_0, ChartTheme chartTheme_0)
		{
			Color color = Color.FromArgb(255, 255, 236);
			if (chartTheme_0 == ChartTheme.Classic)
			{
				japaneseCandleStickItem_0.Stick.Color = Color.Red;
				japaneseCandleStickItem_0.Stick.RisingFill.Color = Color.Black;
				japaneseCandleStickItem_0.Stick.RisingBorder.Color = Color.Red;
				japaneseCandleStickItem_0.Stick.FallingColor = Color.Cyan;
				japaneseCandleStickItem_0.Stick.FallingBorder.Color = Color.Cyan;
				japaneseCandleStickItem_0.Stick.FallingFill.Color = Color.Cyan;
			}
			else
			{
				japaneseCandleStickItem_0.Stick.Color = Color.Red;
				japaneseCandleStickItem_0.Stick.RisingBorder.Color = Color.Red;
				japaneseCandleStickItem_0.Stick.FallingColor = Color.Green;
				japaneseCandleStickItem_0.Stick.FallingBorder.Color = Color.Green;
				japaneseCandleStickItem_0.Stick.FallingFill.Color = Color.Green;
				if (chartTheme_0 == ChartTheme.Modern)
				{
					japaneseCandleStickItem_0.Stick.RisingFill.Color = Color.White;
				}
				else if (chartTheme_0 == ChartTheme.Yellow)
				{
					japaneseCandleStickItem_0.Stick.RisingFill.Color = color;
				}
			}
		}

		// Token: 0x0600209E RID: 8350 RVA: 0x000E09C8 File Offset: 0x000DEBC8
		public override bool InitInd(ChartKLine chart)
		{
			bool result;
			if (!base.InitInd(chart))
			{
				result = false;
			}
			else
			{
				this.method_5();
				if (this.UDInd.method_7(chart.DP))
				{
					this.vmethod_0();
					result = true;
				}
				else
				{
					result = false;
				}
			}
			return result;
		}

		// Token: 0x0600209F RID: 8351 RVA: 0x000E0A0C File Offset: 0x000DEC0C
		private void method_5()
		{
			if (!this.UDInd.UDS.MainK && this.UDInd.UDS.YLine != "")
			{
				string[] array = this.UDInd.UDS.YLine.Split(new char[]
				{
					';'
				});
				List<double> list = new List<double>();
				for (int i = 0; i < array.Length; i++)
				{
					double item;
					if (double.TryParse(array[i], out item))
					{
						list.Add(item);
					}
				}
				list.Sort();
				if (list.Any<double>())
				{
					YGridAxis yGridAxis = new YGridAxis("", list.ToArray());
					this.GraphControl.GraphPane.ResetYAxis(yGridAxis);
					base.Chart.ApplyTheme(Base.UI.Form.ChartTheme);
					this.GraphControl.GraphPane.YAxis.MajorGrid.IsVisible = true;
					this.GraphControl.GraphPane.YAxis.MinorTic.IsAllTics = false;
					this.GraphControl.GraphPane.YAxis.Scale.IsSkipFirstLabel = false;
					this.GraphControl.GraphPane.YAxis.Scale.IsSkipLastLabel = false;
					this.GraphControl.GraphPane.YAxis.MajorGrid.IsZeroLine = false;
					this.GraphControl.GraphPane.YAxis.MajorGrid.DashOff = 2f;
					this.GraphControl.GraphPane.YAxis.MinorTic.Size = 0f;
					this.GraphControl.GraphPane.YAxis.MajorTic.Size = 0f;
				}
			}
		}

		// Token: 0x060020A0 RID: 8352 RVA: 0x0000D334 File Offset: 0x0000B534
		public override void ApplyTheme(ChartTheme theme)
		{
			base.ApplyTheme(theme);
			this.method_3(theme, this.list_0);
		}

		// Token: 0x060020A1 RID: 8353 RVA: 0x000E0BC8 File Offset: 0x000DEDC8
		public static Color smethod_1(ChartTheme chartTheme_0, Color[] color_2)
		{
			IndEx.Class376 @class = new IndEx.Class376();
			@class.color_0 = color_2;
			Color[] source;
			if (chartTheme_0 == ChartTheme.Classic)
			{
				source = IndEx.color_0;
			}
			else
			{
				source = IndEx.color_1;
			}
			IEnumerable<Color> source2 = source.Where(new Func<Color, bool>(@class.method_0));
			Color result;
			if (source2.Any<Color>())
			{
				result = source2.First<Color>();
			}
			else
			{
				result = source.First<Color>();
			}
			return result;
		}

		// Token: 0x060020A2 RID: 8354 RVA: 0x000E0C24 File Offset: 0x000DEE24
		protected virtual void vmethod_0()
		{
			base.InitItem();
			this.list_0.ForEach(new Action<ShapeCurve>(this.method_10));
			List<ShapeCurve> list = new List<ShapeCurve>();
			foreach (DataArray dataArray_ in this.UDInd.DataList.Where(new Func<DataArray, bool>(IndEx.<>c.<>9.method_3)))
			{
				List<ShapeCurve> collection = Class380.smethod_0(dataArray_, this.UDInd.DataProvider, this);
				list.AddRange(collection);
			}
			for (int i = 0; i < list.Count; i++)
			{
				Class380 @class = list[i];
				@class.vmethod_7(base.MaxSticksPerChart);
				Color? color = ParserEnvironment.smethod_7(@class.IndData.ColorStr);
				if (color != null)
				{
					@class.vmethod_6(this.CnName, this.GraphControl, color.Value);
				}
				else
				{
					@class.vmethod_6(this.CnName, this.GraphControl, Color.Red);
				}
				ShapeCurve shapeCurve = list[i];
				if (shapeCurve != null)
				{
					IndCurve item = new IndCurve(list[i].IndData.Name, shapeCurve.Curve, !list[i].IndData.NumVisibleLineNot);
					base.CurveList.Add(item);
				}
			}
			try
			{
				this.method_3(Base.UI.Form.ChartTheme, list);
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
			this.list_0 = list;
		}

		// Token: 0x060020A3 RID: 8355 RVA: 0x0000D34C File Offset: 0x0000B54C
		public override void InitItem()
		{
			this.vmethod_0();
		}

		// Token: 0x060020A4 RID: 8356 RVA: 0x0000D356 File Offset: 0x0000B556
		public override void RemoveFromChart()
		{
			this.list_0.ForEach(new Action<ShapeCurve>(this.method_11));
			base.RemoveFromChart();
		}

		// Token: 0x060020A5 RID: 8357 RVA: 0x000E0DCC File Offset: 0x000DEFCC
		public override Indicator GetIndNameByCurve(CurveItem curve)
		{
			foreach (ShapeCurve shapeCurve in this.list_0)
			{
				if (shapeCurve.Curve != null && shapeCurve.Curve == curve)
				{
					goto IL_4B;
				}
			}
			return null;
			IL_4B:
			return this;
		}

		// Token: 0x060020A6 RID: 8358 RVA: 0x000E0E3C File Offset: 0x000DF03C
		private void method_6(ref double double_0, ref double double_1)
		{
			double_0 = -10000000.0;
			double_1 = 100000000.0;
			if (this.list_0 != null && this.list_0.Any<ShapeCurve>())
			{
				bool flag = this.list_0.Exists(new Predicate<ShapeCurve>(IndEx.<>c.<>9.method_4));
				for (int i = 0; i < this.list_0.Count; i++)
				{
					ShapeCurve shapeCurve = this.list_0[i];
					try
					{
						if (flag || (shapeCurve.Curve.IsVisible && !shapeCurve.IndData.NumVisibleLineNot))
						{
							shapeCurve.vmethod_8(ref double_0, ref double_1);
						}
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
			}
		}

		// Token: 0x060020A7 RID: 8359 RVA: 0x000E0F08 File Offset: 0x000DF108
		protected override string GetDesc()
		{
			string name = this.UDInd.Name;
			string[] array = this.UDInd.UDS.UserDefineParams.Select(new Func<UserDefineParam, string>(IndEx.<>c.<>9.method_5)).ToArray<string>();
			string result;
			if (array.Any<string>())
			{
				result = name + "(" + string.Join(",", array) + ")";
			}
			else
			{
				result = name;
			}
			return result;
		}

		// Token: 0x060020A8 RID: 8360 RVA: 0x000E0F88 File Offset: 0x000DF188
		public string method_7()
		{
			DataProvider dataProvider = this.UDInd.DataProvider;
			return string.Concat(new string[]
			{
				"指标名称:",
				this.EnName,
				" 合约名称:",
				(dataProvider.TradingSymbol == null) ? string.Empty : dataProvider.TradingSymbol.ENName,
				" 时间范围：",
				dataProvider.PeriodHisDataList.Keys.First<DateTime>().ToString(),
				" ",
				dataProvider.PeriodHisDataList.Keys.Last<DateTime>().ToString(),
				" 长度：",
				dataProvider.PeriodHisDataList.Count.ToString()
			});
		}

		// Token: 0x060020A9 RID: 8361 RVA: 0x000E104C File Offset: 0x000DF24C
		protected DataArray[] method_8(int int_0, HisData hisData_0, bool bool_0)
		{
			IndEx.Class378 @class = new IndEx.Class378();
			@class.hisData_0 = hisData_0;
			DataProvider dataProvider = this.UDInd.DataProvider;
			if (int_0 >= dataProvider.PeriodHisDataList.Count)
			{
				Class182.smethod_0(new ArgumentOutOfRangeException(string.Concat(new object[]
				{
					int_0.ToString(),
					"的值大于数据长度",
					dataProvider.PeriodHisDataList.Count,
					"指标状态：",
					this.method_7()
				})));
				int_0 = dataProvider.PeriodHisDataList.Count - 1;
			}
			SymbDataSet symbDataSet = base.Chart.SymbDataSet;
			DataArray[] result;
			if (!bool_0 && this.userDefineInd_0 != null)
			{
				if (this.userDefineInd_0 == null)
				{
					throw new Exception("UpdataUDI没有初始化。");
				}
				HisData value = this.userDefineInd_0.DataProvider.PeriodHisDataList.Last<KeyValuePair<DateTime, HisData>>().Value;
				value.Close = @class.hisData_0.Close;
				if (@class.hisData_0.High > value.High)
				{
					value.High = @class.hisData_0.High;
				}
				if (@class.hisData_0.Low < value.Low)
				{
					value.Low = @class.hisData_0.Low;
				}
				double? volume = null;
				if (value.Date != @class.hisData_0.Date && int_0 > 0 && symbDataSet.CurrHisDataSet != null && symbDataSet.CurrHisDataSet.FetchedHisDataList != null)
				{
					List<HisData> list = symbDataSet.method_105(symbDataSet.CurrHisDataSet.FetchedHisDataList, dataProvider.PeriodHisDataList.Keys[int_0 - 1], @class.hisData_0.Date, new int?(1));
					if (list != null)
					{
						volume = list.Sum(new Func<HisData, double?>(IndEx.<>c.<>9.method_6));
					}
				}
				if (volume != null)
				{
					value.Volume = volume;
				}
				else
				{
					value.Volume += @class.hisData_0.Volume;
				}
				value.Amount = @class.hisData_0.Amount;
				result = this.userDefineInd_0.method_11();
			}
			else
			{
				IndEx.Class379 class2 = new IndEx.Class379();
				List<KeyValuePair<DateTime, HisData>> list2 = dataProvider.PeriodHisDataList.Where(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_0)).ToList<KeyValuePair<DateTime, HisData>>();
				list2.Add(new KeyValuePair<DateTime, HisData>(@class.hisData_0.Date, @class.hisData_0));
				class2.sortedList_0 = new SortedList<DateTime, HisData>();
				list2.ForEach(new Action<KeyValuePair<DateTime, HisData>>(class2.method_0));
				DataProvider dp = new DataProvider(class2.sortedList_0, symbDataSet.CurrSymbol);
				this.userDefineInd_0 = new UserDefineInd(dp);
				this.userDefineInd_0.PE = new ParserEnvironment(this.UDInd.UDS.UserDefineParams, this.userDefineInd_0);
				this.userDefineInd_0.ProgrameTree = this.UDInd.ProgrameTree;
				result = this.userDefineInd_0.method_11();
			}
			return result;
		}

		// Token: 0x060020AA RID: 8362 RVA: 0x0000D377 File Offset: 0x0000B577
		protected override double[] GetUpdatedLastOutputs(int itemIdx, HisData newHd)
		{
			throw new NotImplementedException();
		}

		// Token: 0x060020AB RID: 8363 RVA: 0x000E1374 File Offset: 0x000DF574
		public override void UpdateLastItem(int itemIdx, HDTick tick)
		{
			HisData hd = Base.Data.smethod_114(tick);
			this.UpdateLastItem(itemIdx, hd);
		}

		// Token: 0x060020AC RID: 8364 RVA: 0x000E1394 File Offset: 0x000DF594
		public List<UserDefineParam> method_9()
		{
			return this.UDInd.UDS.UserDefineParams;
		}

		// Token: 0x060020AD RID: 8365 RVA: 0x000E13B8 File Offset: 0x000DF5B8
		public override List<IndParam> GetIndParams()
		{
			return null;
		}

		// Token: 0x060020AE RID: 8366 RVA: 0x000041AE File Offset: 0x000023AE
		public override void SetIndParams(List<IndParam> indParamLst)
		{
		}

		// Token: 0x170005B4 RID: 1460
		// (get) Token: 0x060020AF RID: 8367 RVA: 0x000E13CC File Offset: 0x000DF5CC
		public ZedGraphControl GraphControl
		{
			get
			{
				return base.Chart.ZedGraphControl;
			}
		}

		// Token: 0x170005B5 RID: 1461
		// (get) Token: 0x060020B0 RID: 8368 RVA: 0x000E13E8 File Offset: 0x000DF5E8
		// (set) Token: 0x060020B1 RID: 8369 RVA: 0x0000D37E File Offset: 0x0000B57E
		public UserDefineInd UDInd { get; private set; }

		// Token: 0x170005B6 RID: 1462
		// (get) Token: 0x060020B2 RID: 8370 RVA: 0x000E1400 File Offset: 0x000DF600
		public bool HasLast
		{
			get
			{
				return this.UDInd.HasLast;
			}
		}

		// Token: 0x170005B7 RID: 1463
		// (get) Token: 0x060020B3 RID: 8371 RVA: 0x000E141C File Offset: 0x000DF61C
		public override string CnName
		{
			get
			{
				return this.UDInd.Name;
			}
		}

		// Token: 0x170005B8 RID: 1464
		// (get) Token: 0x060020B4 RID: 8372 RVA: 0x000E141C File Offset: 0x000DF61C
		public override string EnName
		{
			get
			{
				return this.UDInd.Name;
			}
		}

		// Token: 0x170005B9 RID: 1465
		// (get) Token: 0x060020B5 RID: 8373 RVA: 0x000E1438 File Offset: 0x000DF638
		// (set) Token: 0x060020B6 RID: 8374 RVA: 0x0000D389 File Offset: 0x0000B589
		public ESIndChartType MindChartType { get; set; }

		// Token: 0x170005BA RID: 1466
		// (get) Token: 0x060020B7 RID: 8375 RVA: 0x000E1450 File Offset: 0x000DF650
		// (set) Token: 0x060020B8 RID: 8376 RVA: 0x0000D394 File Offset: 0x0000B594
		public List<ShapeCurve> IndShapes
		{
			get
			{
				return this.list_0;
			}
			private set
			{
				this.list_0 = value;
			}
		}

		// Token: 0x060020BA RID: 8378 RVA: 0x0000D39F File Offset: 0x0000B59F
		[CompilerGenerated]
		private void method_10(ShapeCurve shapeCurve_0)
		{
			shapeCurve_0.vmethod_1(base.Chart.ZedGraphControl);
		}

		// Token: 0x060020BB RID: 8379 RVA: 0x0000D39F File Offset: 0x0000B59F
		[CompilerGenerated]
		private void method_11(ShapeCurve shapeCurve_0)
		{
			shapeCurve_0.vmethod_1(base.Chart.ZedGraphControl);
		}

		// Token: 0x04001007 RID: 4103
		protected List<ShapeCurve> list_0;

		// Token: 0x04001008 RID: 4104
		private static Color[] color_0 = new Color[]
		{
			Color.White,
			Color.Yellow,
			Color.Magenta,
			Color.LimeGreen,
			Color.MediumPurple,
			Color.Tomato,
			Color.Tan
		};

		// Token: 0x04001009 RID: 4105
		private static Color[] color_1 = new Color[]
		{
			Color.DarkBlue,
			Color.DarkOrange,
			Color.Purple,
			Color.Blue,
			Color.Violet,
			Color.DarkCyan,
			Color.DarkKhaki
		};

		// Token: 0x0400100A RID: 4106
		private UserDefineInd userDefineInd_0;

		// Token: 0x0400100B RID: 4107
		[CompilerGenerated]
		private UserDefineInd userDefineInd_1;

		// Token: 0x0400100C RID: 4108
		[CompilerGenerated]
		private ESIndChartType esindChartType_0;

		// Token: 0x020002DD RID: 733
		[CompilerGenerated]
		private sealed class Class376
		{
			// Token: 0x060020C6 RID: 8390 RVA: 0x000E15D4 File Offset: 0x000DF7D4
			internal bool method_0(Color color_1)
			{
				IndEx.Class377 @class = new IndEx.Class377();
				@class.color_0 = color_1;
				return !this.color_0.Any(new Func<Color, bool>(@class.method_0));
			}

			// Token: 0x04001015 RID: 4117
			public Color[] color_0;
		}

		// Token: 0x020002DE RID: 734
		[CompilerGenerated]
		private sealed class Class377
		{
			// Token: 0x060020C8 RID: 8392 RVA: 0x000E160C File Offset: 0x000DF80C
			internal bool method_0(Color color_1)
			{
				return color_1 == this.color_0;
			}

			// Token: 0x04001016 RID: 4118
			public Color color_0;
		}

		// Token: 0x020002DF RID: 735
		[CompilerGenerated]
		private sealed class Class378
		{
			// Token: 0x060020CA RID: 8394 RVA: 0x000E162C File Offset: 0x000DF82C
			internal bool method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				return keyValuePair_0.Key < this.hisData_0.Date;
			}

			// Token: 0x04001017 RID: 4119
			public HisData hisData_0;
		}

		// Token: 0x020002E0 RID: 736
		[CompilerGenerated]
		private sealed class Class379
		{
			// Token: 0x060020CC RID: 8396 RVA: 0x0000D3C2 File Offset: 0x0000B5C2
			internal void method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				this.sortedList_0.Add(keyValuePair_0.Key, keyValuePair_0.Value.Clone());
			}

			// Token: 0x04001018 RID: 4120
			public SortedList<DateTime, HisData> sortedList_0;
		}
	}
}
