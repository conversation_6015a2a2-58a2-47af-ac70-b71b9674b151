﻿using System;
using System.ComponentModel;
using System.IO;
using System.Timers;
using System.Xml;
using NAppUpdate.Framework;
using NAppUpdate.Framework.Sources;
using ns23;
using TEx;
using TEx.Comn;
using TEx.Util;

namespace ns18
{
	// Token: 0x020001DE RID: 478
	internal abstract class Class263
	{
		// Token: 0x0600129D RID: 4765 RVA: 0x00007B80 File Offset: 0x00005D80
		public Class263()
		{
			this.timer_0.Interval = 1000.0;
			this.timer_0.Elapsed += new ElapsedEventHandler(this.timer_0_Elapsed);
		}

		// Token: 0x0600129E RID: 4766 RVA: 0x00007BB5 File Offset: 0x00005DB5
		public Class263(DatFileInfo datFileInfo_1) : this()
		{
			this.DFileInfo = datFileInfo_1;
		}

		// Token: 0x0600129F RID: 4767 RVA: 0x00007BC6 File Offset: 0x00005DC6
		public void method_0()
		{
			this.method_1(this.DFileInfo);
		}

		// Token: 0x060012A0 RID: 4768 RVA: 0x0007F500 File Offset: 0x0007D700
		public object method_1(DatFileInfo datFileInfo_1)
		{
			this.IsUpdatingFile = true;
			this.DFileInfo = datFileInfo_1;
			this.FileObj = null;
			this.method_3();
			this.IsReadingFile = true;
			this.FileObj = this.vmethod_0();
			this.IsReadingFile = false;
			return this.FileObj;
		}

		// Token: 0x060012A1 RID: 4769 RVA: 0x0007F54C File Offset: 0x0007D74C
		public void method_2(DatFileInfo datFileInfo_1)
		{
			this.IsUpdatingFile = true;
			this.DFileInfo = datFileInfo_1;
			this.FileObj = null;
			if (this.backgroundWorker_0 == null)
			{
				if (!string.IsNullOrEmpty(this.FileFeedSrc))
				{
					this.backgroundWorker_0 = new BackgroundWorker();
					this.backgroundWorker_0.WorkerReportsProgress = true;
					this.backgroundWorker_0.WorkerSupportsCancellation = true;
					this.backgroundWorker_0.DoWork += this.backgroundWorker_0_DoWork;
					this.backgroundWorker_0.ProgressChanged += this.backgroundWorker_0_ProgressChanged;
					this.backgroundWorker_0.RunWorkerCompleted += this.backgroundWorker_0_RunWorkerCompleted;
					this.backgroundWorker_0.RunWorkerAsync(this.FileFeedSrc);
				}
			}
			else if (!this.backgroundWorker_0.IsBusy && !string.IsNullOrEmpty(this.FileFeedSrc))
			{
				this.backgroundWorker_0.RunWorkerAsync(this.FileFeedSrc);
			}
		}

		// Token: 0x060012A2 RID: 4770 RVA: 0x000041AE File Offset: 0x000023AE
		private void timer_0_Elapsed(object sender, EventArgs e)
		{
		}

		// Token: 0x060012A3 RID: 4771 RVA: 0x0007F634 File Offset: 0x0007D834
		private void backgroundWorker_0_DoWork(object sender, DoWorkEventArgs e)
		{
			string string_ = e.Argument.ToString();
			this.method_4(string_);
			e.Result = this.DFileInfo.FileName;
		}

		// Token: 0x060012A4 RID: 4772 RVA: 0x00007BD7 File Offset: 0x00005DD7
		private void method_3()
		{
			this.method_4(this.FileFeedSrc);
		}

		// Token: 0x060012A5 RID: 4773 RVA: 0x0007F668 File Offset: 0x0007D868
		private void method_4(string string_1)
		{
			if (!string.IsNullOrEmpty(string_1))
			{
				UpdateManager instance = UpdateManager.Instance;
				instance.UpdateSource = new SimpleWebSource();
				instance.Config.TempFolder = Path.Combine(TApp.string_10, "Updates");
				instance.ReinstateIfRestarted();
				if (Class183.smethod_0(new MemorySource(string_1)) > 0)
				{
					try
					{
						instance.PrepareUpdates();
						instance.ApplyUpdates(false);
						instance.CleanUp();
					}
					catch (Exception)
					{
						instance.CleanUp();
						throw;
					}
				}
				this.IsUpdatingFile = false;
			}
		}

		// Token: 0x060012A6 RID: 4774 RVA: 0x00007BE7 File Offset: 0x00005DE7
		private void backgroundWorker_0_ProgressChanged(object sender, ProgressChangedEventArgs e)
		{
			int progressPercentage = e.ProgressPercentage;
		}

		// Token: 0x060012A7 RID: 4775 RVA: 0x00007BF2 File Offset: 0x00005DF2
		private void backgroundWorker_0_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
		{
			if (e.Error == null && !e.Cancelled && e.Result != null)
			{
				this.IsReadingFile = true;
				this.FileObj = this.vmethod_1(e.Result.ToString());
			}
		}

		// Token: 0x060012A8 RID: 4776 RVA: 0x0007F6F4 File Offset: 0x0007D8F4
		protected virtual object vmethod_0()
		{
			return this.vmethod_1(this.DFileInfo.FileName);
		}

		// Token: 0x060012A9 RID: 4777
		protected abstract object vmethod_1(string string_1);

		// Token: 0x060012AA RID: 4778 RVA: 0x0007F718 File Offset: 0x0007D918
		public string method_5(DatFileInfo datFileInfo_1)
		{
			XmlDocument xmlDocument = new XmlDocument();
			XmlDeclaration newChild = xmlDocument.CreateXmlDeclaration("1.0", "utf-8", null);
			string value = TApp.FULLHOST + datFileInfo_1.Path.Replace(".", "").Replace("\\", "/");
			xmlDocument.AppendChild(newChild);
			XmlElement xmlElement = xmlDocument.CreateElement("Feed");
			xmlElement.SetAttribute("BaseUrl", value);
			xmlDocument.AppendChild(xmlElement);
			XmlElement xmlElement2 = xmlDocument.CreateElement("Tasks");
			XmlElement xmlElement3 = xmlDocument.CreateElement("FileUpdateTask");
			xmlElement3.SetAttribute("hotswap", "true");
			xmlElement3.SetAttribute("localPath", ".\\Data\\" + datFileInfo_1.FileName);
			xmlElement3.SetAttribute("fileSize", datFileInfo_1.FileSize.ToString());
			XmlElement xmlElement4 = xmlDocument.CreateElement("Conditions");
			XmlElement xmlElement5 = xmlDocument.CreateElement("FileExistsCondition");
			xmlElement5.SetAttribute("type", "or-not");
			xmlElement4.AppendChild(xmlElement5);
			xmlElement5 = xmlDocument.CreateElement("FileSizeCondition");
			xmlElement5.SetAttribute("type", "or-not");
			xmlElement5.SetAttribute("what", "is");
			xmlElement5.SetAttribute("size", datFileInfo_1.FileSize.ToString());
			xmlElement4.AppendChild(xmlElement5);
			xmlElement5 = xmlDocument.CreateElement("FileChecksumCondition");
			xmlElement5.SetAttribute("type", "or-not");
			xmlElement5.SetAttribute("checksumType", "sha256");
			xmlElement5.SetAttribute("checksum", datFileInfo_1.SHA);
			xmlElement4.AppendChild(xmlElement5);
			xmlElement3.AppendChild(xmlElement4);
			xmlElement2.AppendChild(xmlElement3);
			xmlElement.AppendChild(xmlElement2);
			return Utility.ConvertXMLDocToString(xmlDocument);
		}

		// Token: 0x170002C9 RID: 713
		// (get) Token: 0x060012AB RID: 4779 RVA: 0x0007F8E8 File Offset: 0x0007DAE8
		// (set) Token: 0x060012AC RID: 4780 RVA: 0x00007C2C File Offset: 0x00005E2C
		public DatFileInfo DFileInfo
		{
			get
			{
				return this.datFileInfo_0;
			}
			set
			{
				this.datFileInfo_0 = value;
				this.method_5(value);
			}
		}

		// Token: 0x170002CA RID: 714
		// (get) Token: 0x060012AD RID: 4781 RVA: 0x0007F900 File Offset: 0x0007DB00
		// (set) Token: 0x060012AE RID: 4782 RVA: 0x00007C3F File Offset: 0x00005E3F
		public string FileFeedSrc
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x170002CB RID: 715
		// (get) Token: 0x060012AF RID: 4783 RVA: 0x0007F918 File Offset: 0x0007DB18
		public bool IsBusy
		{
			get
			{
				bool result;
				if (!this.bool_0)
				{
					result = this.bool_1;
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x170002CC RID: 716
		// (get) Token: 0x060012B0 RID: 4784 RVA: 0x0007F93C File Offset: 0x0007DB3C
		// (set) Token: 0x060012B1 RID: 4785 RVA: 0x00007C4A File Offset: 0x00005E4A
		public bool IsUpdatingFile
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x170002CD RID: 717
		// (get) Token: 0x060012B2 RID: 4786 RVA: 0x0007F954 File Offset: 0x0007DB54
		// (set) Token: 0x060012B3 RID: 4787 RVA: 0x00007C55 File Offset: 0x00005E55
		public bool IsReadingFile
		{
			get
			{
				return this.bool_1;
			}
			set
			{
				this.bool_1 = value;
			}
		}

		// Token: 0x170002CE RID: 718
		// (get) Token: 0x060012B4 RID: 4788 RVA: 0x0007F96C File Offset: 0x0007DB6C
		// (set) Token: 0x060012B5 RID: 4789 RVA: 0x00007C60 File Offset: 0x00005E60
		public int SecsToWait
		{
			get
			{
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
			}
		}

		// Token: 0x170002CF RID: 719
		// (get) Token: 0x060012B6 RID: 4790 RVA: 0x0007F984 File Offset: 0x0007DB84
		// (set) Token: 0x060012B7 RID: 4791 RVA: 0x00007C6B File Offset: 0x00005E6B
		public object FileObj
		{
			get
			{
				return this.object_0;
			}
			set
			{
				this.object_0 = value;
			}
		}

		// Token: 0x040009B3 RID: 2483
		private BackgroundWorker backgroundWorker_0;

		// Token: 0x040009B4 RID: 2484
		private DatFileInfo datFileInfo_0;

		// Token: 0x040009B5 RID: 2485
		private string string_0;

		// Token: 0x040009B6 RID: 2486
		private Timer timer_0;

		// Token: 0x040009B7 RID: 2487
		private bool bool_0;

		// Token: 0x040009B8 RID: 2488
		private bool bool_1;

		// Token: 0x040009B9 RID: 2489
		private int int_0;

		// Token: 0x040009BA RID: 2490
		private object object_0;
	}
}
