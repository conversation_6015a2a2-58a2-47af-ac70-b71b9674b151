﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns12;
using ns28;
using ns30;
using ns6;
using TEx;
using TEx.Util;

namespace ns14
{
	// Token: 0x02000209 RID: 521
	[DesignerCategory("Code")]
	internal class Class283 : Class282
	{
		// Token: 0x06001559 RID: 5465 RVA: 0x0000888C File Offset: 0x00006A8C
		public Class283(bool bool_2 = false) : base(bool_2)
		{
			base.CellFormatting += this.Class283_CellFormatting;
		}

		// Token: 0x0600155A RID: 5466 RVA: 0x0008D308 File Offset: 0x0008B508
		protected virtual void Class283_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
		{
			DataGridView dataGridView = sender as DataGridView;
			DataGridViewCell dataGridViewCell = dataGridView.Rows[e.RowIndex].Cells[e.ColumnIndex];
			if (e.Value != null)
			{
				if (e.Value.GetType() == typeof(double))
				{
					if (double.IsNaN((double)e.Value))
					{
						e.Value = "-";
					}
				}
				else if (e.Value as string == "非数字")
				{
					e.Value = "-";
				}
				if (this.vmethod_4(dataGridView, e))
				{
					string text = e.Value.ToString();
					if (!string.IsNullOrEmpty(text))
					{
						if (!Utility.IsDigitChars(text, true, true))
						{
							goto IL_D4;
						}
						try
						{
							e.Value = Utility.GetUnitAbbrNbString(text);
							goto IL_D4;
						}
						catch (Exception exception_)
						{
							Class182.smethod_0(exception_);
							goto IL_D4;
						}
					}
					e.Value = "-";
				}
				IL_D4:
				if (this.vmethod_5(dataGridView, e))
				{
					if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
					{
						e.CellStyle.BackColor = Color.FromArgb(49, 49, 49);
						e.CellStyle.ForeColor = Class179.color_9;
						e.CellStyle.SelectionForeColor = Class179.color_9;
					}
					else
					{
						e.CellStyle.BackColor = Color.FromKnownColor(KnownColor.Control);
					}
				}
			}
			else
			{
				e.Value = "-";
			}
		}

		// Token: 0x0600155B RID: 5467 RVA: 0x00023060 File Offset: 0x00021260
		protected virtual bool vmethod_4(DataGridView dataGridView_0, DataGridViewCellFormattingEventArgs dataGridViewCellFormattingEventArgs_0)
		{
			return true;
		}

		// Token: 0x0600155C RID: 5468 RVA: 0x0008D468 File Offset: 0x0008B668
		protected virtual bool vmethod_5(DataGridView dataGridView_0, DataGridViewCellFormattingEventArgs dataGridViewCellFormattingEventArgs_0)
		{
			return !this.vmethod_4(dataGridView_0, dataGridViewCellFormattingEventArgs_0);
		}

		// Token: 0x0600155D RID: 5469 RVA: 0x0008D484 File Offset: 0x0008B684
		protected override void vmethod_0()
		{
			base.vmethod_0();
			base.BorderStyle = BorderStyle.None;
			base.CellBorderStyle = DataGridViewCellBorderStyle.Single;
			base.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
			base.AllowUserToOrderColumns = false;
			base.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
			base.ColumnHeadersDefaultCellStyle.Padding = new Padding(0);
			base.RowHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
			base.RowsDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
		}

		// Token: 0x0600155E RID: 5470 RVA: 0x0008D4E4 File Offset: 0x0008B6E4
		protected override void vmethod_2()
		{
			if (!base.IsDisposed)
			{
				try
				{
					Color color;
					Color backColor;
					Color backColor2;
					Color foreColor;
					Color color2;
					Color selectionBackColor;
					Color backgroundColor;
					Color gridColor;
					if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
					{
						color = Class179.color_11;
						backColor = Class179.color_3;
						backColor2 = Color.FromArgb(49, 49, 49);
						foreColor = Class179.color_9;
						color2 = color;
						selectionBackColor = Color.FromArgb(74, 74, 74);
						backgroundColor = Class179.color_3;
						gridColor = Color.FromArgb(69, 69, 69);
					}
					else
					{
						color = Class179.color_3;
						backColor = Color.White;
						backColor2 = Color.FromKnownColor(KnownColor.Control);
						foreColor = Class179.color_3;
						color2 = color;
						selectionBackColor = Class179.color_12;
						backgroundColor = Color.White;
						gridColor = Color.FromArgb(255, 250, 240);
					}
					this.smethod_0();
					base.DefaultCellStyle.ForeColor = color;
					base.DefaultCellStyle.BackColor = backColor;
					base.RowsDefaultCellStyle.ForeColor = color;
					base.DefaultCellStyle.SelectionForeColor = color;
					base.DefaultCellStyle.SelectionBackColor = selectionBackColor;
					base.AlternatingRowsDefaultCellStyle.BackColor = backColor;
					base.ColumnHeadersDefaultCellStyle.BackColor = backColor2;
					base.ColumnHeadersDefaultCellStyle.ForeColor = foreColor;
					base.RowHeadersDefaultCellStyle.BackColor = backColor2;
					base.RowHeadersDefaultCellStyle.ForeColor = color2;
					base.RowHeadersDefaultCellStyle.SelectionBackColor = selectionBackColor;
					base.RowHeadersDefaultCellStyle.SelectionForeColor = color2;
					try
					{
						base.GridColor = gridColor;
					}
					catch
					{
					}
					base.BackgroundColor = backgroundColor;
					this.smethod_1();
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
		}
	}
}
