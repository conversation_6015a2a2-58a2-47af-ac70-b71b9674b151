﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Timers;
using System.Windows.Forms;
using ns10;
using ns13;
using ns2;
using ns22;
using ns30;
using ns5;
using ns9;
using TEx.Trading;

namespace TEx.ImportTrans
{
	// Token: 0x02000365 RID: 869
	internal static class CfmmcRecImporter
	{
		// Token: 0x140000B0 RID: 176
		// (add) Token: 0x06002432 RID: 9266 RVA: 0x000F3C28 File Offset: 0x000F1E28
		// (remove) Token: 0x06002433 RID: 9267 RVA: 0x000F3C60 File Offset: 0x000F1E60
		public static event EventHandler LoggingIn
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06002434 RID: 9268 RVA: 0x0000E13A File Offset: 0x0000C33A
		private static void smethod_0()
		{
			CfmmcRecImporter.smethod_11(CfmmcRecImporter.eventHandler_0);
		}

		// Token: 0x140000B1 RID: 177
		// (add) Token: 0x06002435 RID: 9269 RVA: 0x000F3C98 File Offset: 0x000F1E98
		// (remove) Token: 0x06002436 RID: 9270 RVA: 0x000F3CD0 File Offset: 0x000F1ED0
		public static event EventHandler LoginSuccess
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06002437 RID: 9271 RVA: 0x0000E148 File Offset: 0x0000C348
		private static void smethod_1()
		{
			CfmmcRecImporter.smethod_11(CfmmcRecImporter.eventHandler_1);
		}

		// Token: 0x140000B2 RID: 178
		// (add) Token: 0x06002438 RID: 9272 RVA: 0x000F3D08 File Offset: 0x000F1F08
		// (remove) Token: 0x06002439 RID: 9273 RVA: 0x000F3D40 File Offset: 0x000F1F40
		public static event EventHandler IdPswdCheckFailed
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_2;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_2, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_2;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_2, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600243A RID: 9274 RVA: 0x0000E156 File Offset: 0x0000C356
		private static void smethod_2()
		{
			CfmmcRecImporter.smethod_11(CfmmcRecImporter.eventHandler_2);
		}

		// Token: 0x140000B3 RID: 179
		// (add) Token: 0x0600243B RID: 9275 RVA: 0x000F3D78 File Offset: 0x000F1F78
		// (remove) Token: 0x0600243C RID: 9276 RVA: 0x000F3DB0 File Offset: 0x000F1FB0
		public static event Delegate29 RecImportSuccess
		{
			[CompilerGenerated]
			add
			{
				Delegate29 @delegate = CfmmcRecImporter.delegate29_0;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref CfmmcRecImporter.delegate29_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate29 @delegate = CfmmcRecImporter.delegate29_0;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref CfmmcRecImporter.delegate29_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x0600243D RID: 9277 RVA: 0x000F3DE8 File Offset: 0x000F1FE8
		private static void smethod_3(int int_2, bool bool_1, string string_3)
		{
			EventArgs25 e = new EventArgs25(int_2, bool_1, string_3);
			CfmmcRecImporter.delegate29_0(null, e);
		}

		// Token: 0x140000B4 RID: 180
		// (add) Token: 0x0600243E RID: 9278 RVA: 0x000F3E0C File Offset: 0x000F200C
		// (remove) Token: 0x0600243F RID: 9279 RVA: 0x000F3E44 File Offset: 0x000F2044
		public static event Delegate29 RecImportFailed
		{
			[CompilerGenerated]
			add
			{
				Delegate29 @delegate = CfmmcRecImporter.delegate29_1;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref CfmmcRecImporter.delegate29_1, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate29 @delegate = CfmmcRecImporter.delegate29_1;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref CfmmcRecImporter.delegate29_1, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06002440 RID: 9280 RVA: 0x000F3E7C File Offset: 0x000F207C
		private static void smethod_4(int int_2, bool bool_1, string string_3)
		{
			EventArgs25 e = new EventArgs25(int_2, bool_1, string_3);
			CfmmcRecImporter.delegate29_1(null, e);
		}

		// Token: 0x06002441 RID: 9281 RVA: 0x0000E164 File Offset: 0x0000C364
		private static void smethod_5(EventArgs25 eventArgs25_0)
		{
			if (eventArgs25_0 != null)
			{
				CfmmcRecImporter.smethod_4(eventArgs25_0.TotalRecs, eventArgs25_0.Result, eventArgs25_0.Msg);
			}
		}

		// Token: 0x140000B5 RID: 181
		// (add) Token: 0x06002442 RID: 9282 RVA: 0x000F3EA0 File Offset: 0x000F20A0
		// (remove) Token: 0x06002443 RID: 9283 RVA: 0x000F3ED8 File Offset: 0x000F20D8
		public static event EventHandler NoRecNeedToDnldDetected
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_3;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_3, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_3;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_3, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06002444 RID: 9284 RVA: 0x0000E182 File Offset: 0x0000C382
		private static void smethod_6()
		{
			CfmmcRecImporter.smethod_11(CfmmcRecImporter.eventHandler_3);
		}

		// Token: 0x140000B6 RID: 182
		// (add) Token: 0x06002445 RID: 9285 RVA: 0x000F3F10 File Offset: 0x000F2110
		// (remove) Token: 0x06002446 RID: 9286 RVA: 0x000F3F48 File Offset: 0x000F2148
		public static event Delegate29 NotifyDnRecIndex
		{
			[CompilerGenerated]
			add
			{
				Delegate29 @delegate = CfmmcRecImporter.delegate29_2;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref CfmmcRecImporter.delegate29_2, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate29 @delegate = CfmmcRecImporter.delegate29_2;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref CfmmcRecImporter.delegate29_2, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06002447 RID: 9287 RVA: 0x000F3F80 File Offset: 0x000F2180
		private static void smethod_7(int int_2, bool bool_1, string string_3)
		{
			EventArgs25 e = new EventArgs25(int_2, bool_1, string_3);
			CfmmcRecImporter.delegate29_2(null, e);
		}

		// Token: 0x06002448 RID: 9288 RVA: 0x0000E190 File Offset: 0x0000C390
		private static void smethod_8(EventArgs25 eventArgs25_0)
		{
			CfmmcRecImporter.smethod_7(eventArgs25_0.TotalRecs, eventArgs25_0.Result, eventArgs25_0.Msg);
		}

		// Token: 0x140000B7 RID: 183
		// (add) Token: 0x06002449 RID: 9289 RVA: 0x000F3FA4 File Offset: 0x000F21A4
		// (remove) Token: 0x0600244A RID: 9290 RVA: 0x000F3FDC File Offset: 0x000F21DC
		public static event Delegate29 StartDownloadRecs
		{
			[CompilerGenerated]
			add
			{
				Delegate29 @delegate = CfmmcRecImporter.delegate29_3;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref CfmmcRecImporter.delegate29_3, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate29 @delegate = CfmmcRecImporter.delegate29_3;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref CfmmcRecImporter.delegate29_3, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x0600244B RID: 9291 RVA: 0x000F4014 File Offset: 0x000F2214
		private static void smethod_9(int int_2)
		{
			EventArgs25 e = new EventArgs25(int_2, true, string.Empty);
			CfmmcRecImporter.delegate29_3(new object(), e);
		}

		// Token: 0x140000B8 RID: 184
		// (add) Token: 0x0600244C RID: 9292 RVA: 0x000F4040 File Offset: 0x000F2240
		// (remove) Token: 0x0600244D RID: 9293 RVA: 0x000F4078 File Offset: 0x000F2278
		public static event EventHandler CfmmcAcctsAsynDownloaded
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_4;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_4, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = CfmmcRecImporter.eventHandler_4;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref CfmmcRecImporter.eventHandler_4, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600244E RID: 9294 RVA: 0x0000E1AB File Offset: 0x0000C3AB
		private static void smethod_10()
		{
			CfmmcRecImporter.smethod_11(CfmmcRecImporter.eventHandler_4);
		}

		// Token: 0x0600244F RID: 9295 RVA: 0x00030048 File Offset: 0x0002E248
		private static void smethod_11(EventHandler eventHandler_5)
		{
			EventArgs e = new EventArgs();
			if (eventHandler_5 != null)
			{
				eventHandler_5(null, e);
			}
		}

		// Token: 0x06002450 RID: 9296 RVA: 0x000F40B0 File Offset: 0x000F22B0
		static CfmmcRecImporter()
		{
			CfmmcRecImporter.timer_0.Elapsed += CfmmcRecImporter.smethod_14;
		}

		// Token: 0x06002451 RID: 9297 RVA: 0x0000E1B9 File Offset: 0x0000C3B9
		public static void smethod_12()
		{
			CfmmcRecImporter.smethod_13();
			CfmmcRecImporter.timer_0.Start();
		}

		// Token: 0x06002452 RID: 9298 RVA: 0x000F4114 File Offset: 0x000F2314
		private static void smethod_13()
		{
			CfmmcAutoDnldConfig cfmmcAutoDnldConfig = Base.UI.Form.CfmmcAutoDnldConfig;
			if (cfmmcAutoDnldConfig != null)
			{
				DateTime now = DateTime.Now;
				DateTime dateTime;
				if (cfmmcAutoDnldConfig.Frequency == AutoDownCfmmcFrequencyEnum.每天)
				{
					dateTime = now.Date.Add(cfmmcAutoDnldConfig.BeginTime.TimeOfDay);
					if (dateTime < now)
					{
						dateTime = dateTime.AddDays(1.0);
					}
				}
				else
				{
					int num = cfmmcAutoDnldConfig.WklyDnldDayOfWeek - now.DayOfWeek;
					if (num < 0)
					{
						num += 7;
					}
					dateTime = now.Date.AddDays((double)num).Add(cfmmcAutoDnldConfig.BeginTime.TimeOfDay);
					if (dateTime < now)
					{
						dateTime = dateTime.AddDays(7.0);
					}
				}
				double totalMilliseconds = (dateTime - now).TotalMilliseconds;
				CfmmcRecImporter.timer_0.Interval = (double)Convert.ToInt32(totalMilliseconds);
			}
		}

		// Token: 0x06002453 RID: 9299 RVA: 0x000F4204 File Offset: 0x000F2404
		private static void smethod_14(object sender, ElapsedEventArgs e)
		{
			Class478 @class = new Class478(Base.UI.Form.CfmmcAutoDnldConfig);
			CfmmcRecImporter.timer_0.Enabled = @class.TimerEnabled;
			if (@class.TimerInterval > 0)
			{
				CfmmcRecImporter.timer_0.Interval = (double)@class.TimerInterval;
			}
			if (@class.DownEnable)
			{
				CfmmcRecImporter.smethod_37();
			}
			CfmmcRecImporter.smethod_13();
		}

		// Token: 0x06002454 RID: 9300 RVA: 0x000F4260 File Offset: 0x000F2460
		private static void smethod_15(CfmmcWebDnloader cfmmcWebDnloader_0)
		{
			cfmmcWebDnloader_0.RecDnSuccess += CfmmcRecImporter.smethod_23;
			cfmmcWebDnloader_0.RecDnFailed += CfmmcRecImporter.smethod_17;
			cfmmcWebDnloader_0.IdPswdCheckFailed += CfmmcRecImporter.smethod_18;
			cfmmcWebDnloader_0.NotifyDnRecIndex += CfmmcRecImporter.smethod_19;
			cfmmcWebDnloader_0.LoggingIn += CfmmcRecImporter.smethod_20;
			cfmmcWebDnloader_0.LoginSuccess += CfmmcRecImporter.smethod_21;
		}

		// Token: 0x06002455 RID: 9301 RVA: 0x000F42DC File Offset: 0x000F24DC
		private static void smethod_16(CfmmcWebDnloader cfmmcWebDnloader_0)
		{
			cfmmcWebDnloader_0.RecDnSuccess -= CfmmcRecImporter.smethod_23;
			cfmmcWebDnloader_0.RecDnFailed -= CfmmcRecImporter.smethod_17;
			cfmmcWebDnloader_0.IdPswdCheckFailed -= CfmmcRecImporter.smethod_18;
			cfmmcWebDnloader_0.NotifyDnRecIndex -= CfmmcRecImporter.smethod_19;
			cfmmcWebDnloader_0.LoggingIn -= CfmmcRecImporter.smethod_20;
			cfmmcWebDnloader_0.LoginSuccess -= CfmmcRecImporter.smethod_21;
		}

		// Token: 0x06002456 RID: 9302 RVA: 0x0000E1CC File Offset: 0x0000C3CC
		private static void smethod_17(object sender, EventArgs25 e)
		{
			CfmmcRecImporter.smethod_5(e);
			CfmmcWebDnloader cfmmcWebDnloader = sender as CfmmcWebDnloader;
			CfmmcRecImporter.smethod_41(cfmmcWebDnloader);
			cfmmcWebDnloader.method_2();
		}

		// Token: 0x06002457 RID: 9303 RVA: 0x0000E1E8 File Offset: 0x0000C3E8
		private static void smethod_18(object sender, EventArgs26 e)
		{
			CfmmcRecImporter.smethod_2();
			CfmmcRecImporter.smethod_22(e.UsrId, e.Password);
		}

		// Token: 0x06002458 RID: 9304 RVA: 0x0000E202 File Offset: 0x0000C402
		private static void smethod_19(object sender, EventArgs25 e)
		{
			CfmmcRecImporter.smethod_8(e);
		}

		// Token: 0x06002459 RID: 9305 RVA: 0x0000E20C File Offset: 0x0000C40C
		private static void smethod_20(object sender, EventArgs e)
		{
			CfmmcRecImporter.smethod_0();
		}

		// Token: 0x0600245A RID: 9306 RVA: 0x0000E215 File Offset: 0x0000C415
		private static void smethod_21(object sender, EventArgs e)
		{
			CfmmcRecImporter.smethod_1();
		}

		// Token: 0x0600245B RID: 9307 RVA: 0x000F4358 File Offset: 0x000F2558
		private static void smethod_22(string string_3, string string_4)
		{
			CWrongUserName istoreElement_ = new CWrongUserName(string_3, string_4, DateTime.Now);
			((Interface4)new CWrongNameStore()).imethod_0(istoreElement_);
		}

		// Token: 0x0600245C RID: 9308 RVA: 0x000F4380 File Offset: 0x000F2580
		private static void smethod_23(object sender, EventArgs e)
		{
			int num = 0;
			try
			{
				CfmmcWebDnloader cfmmcWebDnloader = sender as CfmmcWebDnloader;
				CfmmcRecImporter.smethod_24(cfmmcWebDnloader.RecordList, cfmmcWebDnloader.UserName);
				CfmmcRecImporter.smethod_25(cfmmcWebDnloader);
				num = cfmmcWebDnloader.RecordList.Count;
				string string_;
				if (num > 0)
				{
					string_ = string.Format("导入监控中心交易记录完成！共导入{0}条记录。", num);
				}
				else
				{
					string_ = "未查询到需导入的监控中心交易记录。";
				}
				CfmmcRecImporter.smethod_3(num, true, string_);
				CfmmcRecImporter.smethod_41(cfmmcWebDnloader);
				cfmmcWebDnloader.method_2();
			}
			catch (Exception ex)
			{
				Class467.smethod_1(ex.Message);
				CfmmcRecImporter.smethod_4(num, false, ex.Message);
			}
		}

		// Token: 0x0600245D RID: 9309 RVA: 0x0000E21E File Offset: 0x0000C41E
		private static void smethod_24(List<List<string>> list_2, string string_3)
		{
			if (list_2 != null && list_2.Any<List<string>>())
			{
				Class463.smethod_14(list_2, CfmmcRecImporter.CfmmcRecFileLocalPath, string_3);
			}
		}

		// Token: 0x0600245E RID: 9310 RVA: 0x000F441C File Offset: 0x000F261C
		public static void smethod_25(CfmmcWebDnloader cfmmcWebDnloader_0)
		{
			Class448 @class = CfmmcRecImporter.smethod_26(cfmmcWebDnloader_0);
			if (@class.HasData)
			{
				List<Transaction> list = CfmmcRecImporter.smethod_27(@class);
				if (list != null && list.Any<Transaction>())
				{
					CfmmcAcct cfmmcAcct = Class463.smethod_11(cfmmcWebDnloader_0.UserName);
					cfmmcAcct.EndDate = new DateTime?(@class.Data.Max(new Func<TransData, DateTime>(CfmmcRecImporter.<>c.<>9.method_0)));
					DateTime dateTime = list.Min(new Func<Transaction, DateTime>(CfmmcRecImporter.<>c.<>9.method_1));
					if (dateTime < cfmmcAcct.BeginDate)
					{
						cfmmcAcct.BeginDate = new DateTime?(dateTime);
					}
					cfmmcAcct.LastDownloadTime = new DateTime?(DateTime.Now);
					Class463.smethod_0(cfmmcAcct);
				}
			}
		}

		// Token: 0x0600245F RID: 9311 RVA: 0x000F4508 File Offset: 0x000F2708
		public static Class448 smethod_26(CfmmcWebDnloader cfmmcWebDnloader_0)
		{
			Class448 @class = new Class448(TransData.string_0, cfmmcWebDnloader_0.UserName);
			for (int i = 0; i < cfmmcWebDnloader_0.RecordList.Count; i++)
			{
				TransData item = new TransData(cfmmcWebDnloader_0.RecordList[i], cfmmcWebDnloader_0.UserName);
				@class.Data.Add(item);
			}
			return @class;
		}

		// Token: 0x06002460 RID: 9312 RVA: 0x000F4568 File Offset: 0x000F2768
		public static List<Transaction> smethod_27(Class448 class448_0)
		{
			CfmmcAcct cfmmcAcct = Class463.smethod_11(class448_0.CfmmcAcctID);
			List<Transaction> list = TransFileImporter.smethod_5(class448_0);
			for (int i = 0; i < cfmmcAcct.BindingAccts.Count; i++)
			{
				TransFileImporter.smethod_2(list, cfmmcAcct.BindingAccts[i].Id);
			}
			return list;
		}

		// Token: 0x06002461 RID: 9313 RVA: 0x000F45BC File Offset: 0x000F27BC
		[Obsolete]
		private static CfmmcAcct smethod_28(Account account_0, CfmmcWebDnloader cfmmcWebDnloader_0)
		{
			CfmmcRecImporter.Class469 @class = new CfmmcRecImporter.Class469();
			@class.account_0 = account_0;
			CfmmcAcct cfmmcAcct = new CfmmcAcct();
			cfmmcAcct.ID = cfmmcWebDnloader_0.UserName;
			cfmmcAcct.Password = cfmmcWebDnloader_0.PassWord;
			if (cfmmcWebDnloader_0.RecordList.Any<List<string>>())
			{
				cfmmcAcct.BeginDate = new DateTime?(Convert.ToDateTime(cfmmcWebDnloader_0.RecordList.First<List<string>>()[12]));
			}
			cfmmcAcct.EndDate = new DateTime?(DateTime.Now);
			try
			{
				cfmmcAcct.BindingAccts.Single(new Func<BindingAcct, bool>(@class.method_0));
			}
			catch
			{
				BindingAcct bindingAcct = new BindingAcct();
				bindingAcct.UsrName = TApp.UserName;
				bindingAcct.Id = @class.account_0.ID;
				bindingAcct.BeginDate = null;
				bindingAcct.EndDate = null;
				if (cfmmcAcct.BindingAccts == null)
				{
					List<BindingAcct> bindingAccts = new List<BindingAcct>();
					cfmmcAcct.BindingAccts = bindingAccts;
					cfmmcAcct.BindingAccts.Add(bindingAcct);
				}
				else
				{
					cfmmcAcct.BindingAccts.Add(bindingAcct);
				}
			}
			return cfmmcAcct;
		}

		// Token: 0x06002462 RID: 9314 RVA: 0x000F46D8 File Offset: 0x000F28D8
		public static bool smethod_29(CWrongUserName cwrongUserName_0)
		{
			bool result;
			if (MessageBox.Show(string.Format("用户名:{0}和密码:{1}在{2}被检查出错误，\r\n请改正用户名或者密码再登录，否则连续错误登录两次，系统会禁止登录60分钟。\r\n确定用这个账号和密码登录吗？", cwrongUserName_0.name, cwrongUserName_0.password, cwrongUserName_0.date), "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06002463 RID: 9315 RVA: 0x000F4720 File Offset: 0x000F2920
		private static bool smethod_30(string string_3, string string_4)
		{
			Interface4 @interface = new CWrongNameStore();
			CWrongUserName cwrongUserName = new CWrongUserName(string_3, string_4, DateTime.Now);
			bool result;
			if (@interface.imethod_4(string_3 + string_4) != null)
			{
				if (CfmmcRecImporter.smethod_29(cwrongUserName))
				{
					@interface.imethod_2(cwrongUserName);
					result = true;
				}
				else
				{
					result = false;
				}
			}
			else
			{
				result = true;
			}
			return result;
		}

		// Token: 0x06002464 RID: 9316 RVA: 0x000F476C File Offset: 0x000F296C
		public static bool smethod_31(CfmmcAcct cfmmcAcct_0)
		{
			CfmmcWebDnloader cfmmcWebDnloader = new CfmmcWebDnloader(cfmmcAcct_0);
			cfmmcWebDnloader.IsInBackground = false;
			cfmmcWebDnloader.method_1();
			CfmmcRecImporter.smethod_15(cfmmcWebDnloader);
			CfmmcRecImporter.list_0.Add(cfmmcWebDnloader);
			return CfmmcRecImporter.smethod_32(cfmmcWebDnloader, true);
		}

		// Token: 0x06002465 RID: 9317 RVA: 0x000F47AC File Offset: 0x000F29AC
		public static bool smethod_32(CfmmcWebDnloader cfmmcWebDnloader_0, bool bool_1)
		{
			bool result;
			if (!CfmmcRecImporter.smethod_30(cfmmcWebDnloader_0.UserName, cfmmcWebDnloader_0.PassWord))
			{
				result = false;
			}
			else
			{
				result = CfmmcRecImporter.smethod_33(cfmmcWebDnloader_0, bool_1);
			}
			return result;
		}

		// Token: 0x06002466 RID: 9318 RVA: 0x000F47DC File Offset: 0x000F29DC
		private static bool smethod_33(CfmmcWebDnloader cfmmcWebDnloader_0, bool bool_1)
		{
			if (bool_1)
			{
				if (!cfmmcWebDnloader_0.IsDnldNeeded)
				{
					CfmmcRecImporter.smethod_6();
					CfmmcRecImporter.smethod_16(cfmmcWebDnloader_0);
					cfmmcWebDnloader_0.method_2();
					return false;
				}
				CfmmcRecImporter.smethod_9(cfmmcWebDnloader_0.DnDayList.Count);
				cfmmcWebDnloader_0.method_4();
			}
			else
			{
				cfmmcWebDnloader_0.method_7();
				CfmmcRecImporter.smethod_9(cfmmcWebDnloader_0.DnDayList.Count);
				cfmmcWebDnloader_0.method_4();
			}
			return true;
		}

		// Token: 0x06002467 RID: 9319 RVA: 0x000F4844 File Offset: 0x000F2A44
		public static string smethod_34(string string_3)
		{
			return Path.Combine(TApp.UserAcctFolder, string_3);
		}

		// Token: 0x17000634 RID: 1588
		// (get) Token: 0x06002468 RID: 9320 RVA: 0x000F4860 File Offset: 0x000F2A60
		public static string CfmmcRecFileLocalPath
		{
			get
			{
				return CfmmcRecImporter.smethod_34(CfmmcRecImporter.string_0);
			}
		}

		// Token: 0x17000635 RID: 1589
		// (get) Token: 0x06002469 RID: 9321 RVA: 0x000F487C File Offset: 0x000F2A7C
		// (set) Token: 0x0600246A RID: 9322 RVA: 0x0000E23B File Offset: 0x0000C43B
		public static int IEMajor { get; set; }

		// Token: 0x0600246B RID: 9323 RVA: 0x000F4894 File Offset: 0x000F2A94
		private static bool smethod_35()
		{
			bool result;
			try
			{
				if (CfmmcRecImporter.IEMajor <= 6)
				{
					result = false;
					goto IL_D5;
				}
			}
			catch (Exception)
			{
				result = false;
				goto IL_D5;
			}
			List<CfmmcAcct> list = Class463.smethod_9();
			if (!list.Any<CfmmcAcct>())
			{
				return false;
			}
			IEnumerable<CfmmcAcct> source = list.Where(new Func<CfmmcAcct, bool>(CfmmcRecImporter.<>c.<>9.method_2));
			if (!source.Any<CfmmcAcct>())
			{
				return false;
			}
			list = source.ToList<CfmmcAcct>();
			CfmmcRecImporter.list_1 = new List<CfmmcAcct>();
			foreach (CfmmcAcct cfmmcAcct in list)
			{
				if (new CfmmcWebDnloader(cfmmcAcct).IsDnldNeeded)
				{
					CfmmcRecImporter.list_1.Add(cfmmcAcct);
				}
			}
			if (!CfmmcRecImporter.list_1.Any<CfmmcAcct>())
			{
				return false;
			}
			CfmmcRecImporter.int_0 = 0;
			return true;
			IL_D5:
			return result;
		}

		// Token: 0x0600246C RID: 9324 RVA: 0x000F499C File Offset: 0x000F2B9C
		[Obsolete]
		private static bool smethod_36(CfmmcAcct cfmmcAcct_0)
		{
			CfmmcAutoDnldConfig cfmmcAutoDnldConfig = Base.UI.Form.CfmmcAutoDnldConfig;
			bool result;
			if (cfmmcAutoDnldConfig.Frequency == AutoDownCfmmcFrequencyEnum.每天)
			{
				if (cfmmcAcct_0.EndDate != null)
				{
					if ((cfmmcAcct_0.EndDate.Value - DateTime.Now).Days < 1)
					{
						result = true;
					}
					else
					{
						result = false;
					}
				}
				else
				{
					result = true;
				}
			}
			else if (cfmmcAutoDnldConfig.Frequency == AutoDownCfmmcFrequencyEnum.每周)
			{
				DateTime dateTime = DateTime.Now;
				while (dateTime.DayOfWeek != DayOfWeek.Saturday)
				{
					dateTime = dateTime.AddDays(-1.0);
				}
				if (cfmmcAcct_0.EndDate != null)
				{
					if (cfmmcAcct_0.EndDate.Value.Date < dateTime.Date)
					{
						result = true;
					}
					else
					{
						result = false;
					}
				}
				else
				{
					result = true;
				}
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600246D RID: 9325 RVA: 0x0000E245 File Offset: 0x0000C445
		public static void smethod_37()
		{
			if (CfmmcRecImporter.smethod_35())
			{
				if (CfmmcRecImporter.list_1 != null && CfmmcRecImporter.list_1.Any<CfmmcAcct>())
				{
					CfmmcRecImporter.int_0 = 0;
					CfmmcRecImporter.smethod_38(CfmmcRecImporter.int_0);
				}
			}
		}

		// Token: 0x0600246E RID: 9326 RVA: 0x0000E275 File Offset: 0x0000C475
		private static void smethod_38(int int_2)
		{
			CfmmcRecImporter.smethod_39(CfmmcRecImporter.list_1[int_2]);
		}

		// Token: 0x0600246F RID: 9327 RVA: 0x000F4A78 File Offset: 0x000F2C78
		public static bool smethod_39(CfmmcAcct cfmmcAcct_0)
		{
			Class466 parameter = new Class466(cfmmcAcct_0, cfmmcAcct_0.EndDate);
			Thread thread = new Thread(new ParameterizedThreadStart(CfmmcRecImporter.smethod_40));
			thread.IsBackground = true;
			thread.SetApartmentState(ApartmentState.STA);
			thread.Start(parameter);
			return true;
		}

		// Token: 0x06002470 RID: 9328 RVA: 0x000F4ABC File Offset: 0x000F2CBC
		private static void smethod_40(object object_0)
		{
			CfmmcWebDnloader cfmmcWebDnloader = new CfmmcWebDnloader((object_0 as Class466).cfmmcAcct_0);
			cfmmcWebDnloader.IsInBackground = true;
			cfmmcWebDnloader.method_1();
			CfmmcRecImporter.smethod_15(cfmmcWebDnloader);
			if (CfmmcRecImporter.smethod_32(cfmmcWebDnloader, true))
			{
				CfmmcRecImporter.bool_0 = true;
				while (CfmmcRecImporter.bool_0)
				{
					Thread.Sleep(1);
					Application.DoEvents();
				}
			}
		}

		// Token: 0x06002471 RID: 9329 RVA: 0x000F4B14 File Offset: 0x000F2D14
		private static bool smethod_41(CfmmcWebDnloader cfmmcWebDnloader_0)
		{
			CfmmcRecImporter.bool_0 = false;
			if (cfmmcWebDnloader_0 != null)
			{
				CfmmcRecImporter.smethod_42(cfmmcWebDnloader_0);
			}
			bool result;
			if (CfmmcRecImporter.int_0 >= 0)
			{
				Thread.Sleep(100);
				CfmmcRecImporter.int_0++;
				if (CfmmcRecImporter.int_0 < CfmmcRecImporter.list_1.Count)
				{
					CfmmcRecImporter.smethod_38(CfmmcRecImporter.int_0);
					result = true;
				}
				else
				{
					CfmmcRecImporter.smethod_10();
					CfmmcRecImporter.int_0 = -1;
					result = false;
				}
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06002472 RID: 9330 RVA: 0x0000E28A File Offset: 0x0000C48A
		public static void smethod_42(CfmmcWebDnloader cfmmcWebDnloader_0)
		{
			cfmmcWebDnloader_0.method_3();
			CfmmcRecImporter.smethod_16(cfmmcWebDnloader_0);
			cfmmcWebDnloader_0.method_2();
		}

		// Token: 0x06002473 RID: 9331 RVA: 0x0000E2A0 File Offset: 0x0000C4A0
		public static void smethod_43()
		{
			CfmmcRecImporter.smethod_44(false);
		}

		// Token: 0x06002474 RID: 9332 RVA: 0x000F4B80 File Offset: 0x000F2D80
		public static void smethod_44(bool bool_1)
		{
			for (int i = 0; i < CfmmcRecImporter.list_0.Count; i++)
			{
				CfmmcWebDnloader cfmmcWebDnloader = CfmmcRecImporter.list_0[i];
				if (cfmmcWebDnloader != null && (!bool_1 || (bool_1 && !cfmmcWebDnloader.IsInBackground)))
				{
					CfmmcRecImporter.smethod_16(cfmmcWebDnloader);
					cfmmcWebDnloader.method_2();
				}
			}
		}

		// Token: 0x17000636 RID: 1590
		// (get) Token: 0x06002475 RID: 9333 RVA: 0x000F4BD0 File Offset: 0x000F2DD0
		// (set) Token: 0x06002476 RID: 9334 RVA: 0x0000E2AA File Offset: 0x0000C4AA
		public static bool IsDownloading
		{
			get
			{
				return CfmmcRecImporter.bool_0;
			}
			set
			{
				CfmmcRecImporter.bool_0 = value;
			}
		}

		// Token: 0x0400118B RID: 4491
		internal static readonly string string_0 = "Trading\\cfmmcrec.dat";

		// Token: 0x0400118C RID: 4492
		internal static readonly string string_1 = "levenFeatrue.txt";

		// Token: 0x0400118D RID: 4493
		internal static readonly string string_2 = "cfmmcwac.dat";

		// Token: 0x0400118E RID: 4494
		private static System.Timers.Timer timer_0 = new System.Timers.Timer();

		// Token: 0x0400118F RID: 4495
		private static List<CfmmcWebDnloader> list_0 = new List<CfmmcWebDnloader>();

		// Token: 0x04001190 RID: 4496
		private static int int_0 = -1;

		// Token: 0x04001191 RID: 4497
		private static List<CfmmcAcct> list_1;

		// Token: 0x04001192 RID: 4498
		[CompilerGenerated]
		private static EventHandler eventHandler_0;

		// Token: 0x04001193 RID: 4499
		[CompilerGenerated]
		private static EventHandler eventHandler_1;

		// Token: 0x04001194 RID: 4500
		[CompilerGenerated]
		private static EventHandler eventHandler_2;

		// Token: 0x04001195 RID: 4501
		[CompilerGenerated]
		private static Delegate29 delegate29_0;

		// Token: 0x04001196 RID: 4502
		[CompilerGenerated]
		private static Delegate29 delegate29_1;

		// Token: 0x04001197 RID: 4503
		[CompilerGenerated]
		private static EventHandler eventHandler_3;

		// Token: 0x04001198 RID: 4504
		[CompilerGenerated]
		private static Delegate29 delegate29_2;

		// Token: 0x04001199 RID: 4505
		[CompilerGenerated]
		private static Delegate29 delegate29_3;

		// Token: 0x0400119A RID: 4506
		[CompilerGenerated]
		private static EventHandler eventHandler_4;

		// Token: 0x0400119B RID: 4507
		[CompilerGenerated]
		private static int int_1;

		// Token: 0x0400119C RID: 4508
		private static bool bool_0 = false;

		// Token: 0x02000367 RID: 871
		[CompilerGenerated]
		private sealed class Class469
		{
			// Token: 0x0600247E RID: 9342 RVA: 0x000F4C68 File Offset: 0x000F2E68
			internal bool method_0(BindingAcct bindingAcct_0)
			{
				return bindingAcct_0.Id == this.account_0.ID;
			}

			// Token: 0x040011A2 RID: 4514
			public Account account_0;
		}
	}
}
