﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using ns28;
using TEx.Chart;
using TEx.Inds;

namespace TEx.SIndicator
{
	// Token: 0x020002F2 RID: 754
	internal sealed class ShapeDrawICON : ShapeCurve
	{
		// Token: 0x0600211E RID: 8478 RVA: 0x000E2F18 File Offset: 0x000E1118
		public override void vmethod_6(string string_1, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			ShapeDrawICON.Class405 @class = new ShapeDrawICON.Class405();
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			if (base.IndData.SingleData.Contains("ICON"))
			{
				this.string_0 = (string)base.IndData.SingleData["ICON"];
				if (this.string_0.Length > 3 && this.string_0.Substring(0, 3) == "ICO")
				{
					this.string_0 = this.string_0.Substring(3, this.string_0.Length - 3);
					int num = 1;
					if (int.TryParse(this.string_0, out num))
					{
						this.string_0 = num.ToString();
					}
				}
			}
			LineItem lineItem = zedGraphControl_0.GraphPane.AddCurve(base.IndData.Name, base.DataView, Color.Transparent, SymbolType.Circle);
			lineItem.Line.IsVisible = false;
			Assembly executingAssembly = Assembly.GetExecutingAssembly();
			if (this.string_0 == "0")
			{
				this.string_0 = "1";
			}
			else
			{
				try
				{
					if (Convert.ToInt32(this.string_0) > 51)
					{
						this.string_0 = "1";
					}
				}
				catch
				{
				}
			}
			@class.string_0 = "TEx.Resources.ICON." + this.string_0 + ".png";
			string[] manifestResourceNames = executingAssembly.GetManifestResourceNames();
			if (!manifestResourceNames.Any(new Func<string, bool>(@class.method_0)))
			{
				@class.string_0 = manifestResourceNames.FirstOrDefault(new Func<string, bool>(ShapeDrawICON.<>c.<>9.method_0));
			}
			if (!string.IsNullOrEmpty(@class.string_0))
			{
				Image image = Image.FromStream(executingAssembly.GetManifestResourceStream(@class.string_0));
				if (image == null)
				{
					throw new Exception("未能加载图标'" + this.string_0 + "'!");
				}
				lineItem.Symbol.Size = 16f;
				lineItem.Symbol.Border.IsVisible = false;
				lineItem.Symbol.Fill = new Fill(image, WrapMode.Clamp);
			}
			this.curveItem_0 = lineItem;
			lineItem.Tag = string_1 + "_" + base.IndData.Name;
		}

		// Token: 0x0600211F RID: 8479 RVA: 0x0000D581 File Offset: 0x0000B781
		public ShapeDrawICON(DataArray data, DataProvider dp, IndEx indEx) : base(data, dp, indEx)
		{
		}

		// Token: 0x06002120 RID: 8480 RVA: 0x000E3168 File Offset: 0x000E1368
		protected override PointPair vmethod_0(int int_0, DataArray dataArray_1)
		{
			DateTime dateTime = base.method_0(int_0);
			if (dataArray_1.Data.Length < int_0 + 1)
			{
				Class182.smethod_0(new Exception("数据长度溢出。"));
				int_0 = dataArray_1.Data.Length - 1;
			}
			double y = dataArray_1.Data[int_0];
			if (dataArray_1.OtherDataArrayList.Count != 1)
			{
				throw new Exception("文字字段包含数据不足，请检查。");
			}
			PointPair result;
			if ((int)dataArray_1.OtherDataArrayList[0].Data[int_0] == 1)
			{
				result = new PointPair(new XDate(dateTime), y, 1.0);
			}
			else
			{
				result = new PointPair(new XDate(dateTime), double.NaN, double.NaN);
			}
			return result;
		}

		// Token: 0x0400102B RID: 4139
		private string string_0 = "";

		// Token: 0x020002F3 RID: 755
		[CompilerGenerated]
		private sealed class Class405
		{
			// Token: 0x06002122 RID: 8482 RVA: 0x000E3224 File Offset: 0x000E1424
			internal bool method_0(string string_1)
			{
				return string_1 == this.string_0;
			}

			// Token: 0x0400102C RID: 4140
			public string string_0;
		}
	}
}
