﻿using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Windows.Forms;
using ns28;

namespace ns30
{
	// Token: 0x02000413 RID: 1043
	[DesignerCategory("Code")]
	internal sealed class Control5 : Control
	{
		// Token: 0x06002841 RID: 10305 RVA: 0x0000FA3B File Offset: 0x0000DC3B
		protected void OnResize(EventArgs e)
		{
			base.Size = new Size(Convert.ToInt32(112f * this.float_0), Convert.ToInt32(32f * this.float_1));
			base.OnResize(e);
		}

		// Token: 0x06002842 RID: 10306 RVA: 0x0000FA71 File Offset: 0x0000DC71
		protected void ScaleCore(float dx, float dy)
		{
			this.float_0 = dx;
			this.float_1 = dy;
			base.ScaleCore(dx, dy);
			this.OnResize(EventArgs.Empty);
		}

		// Token: 0x06002843 RID: 10307 RVA: 0x0000FA94 File Offset: 0x0000DC94
		protected void Dispose(bool disposing)
		{
			if (disposing)
			{
				if (this.toolTip_0 != null)
				{
					this.toolTip_0.Dispose();
				}
				if (this.pictureBox_0 != null)
				{
					this.pictureBox_0.Dispose();
				}
			}
			base.Dispose(disposing);
		}

		// Token: 0x06002844 RID: 10308 RVA: 0x00103DA8 File Offset: 0x00101FA8
		private void Control5_Click(object sender, EventArgs e)
		{
			try
			{
				Process.Start("http://www.red-gate.com/products/dotnet-development/smartassembly/?utm_source=smartassemblyui&utm_medium=supportlink&utm_content=aerdialogbox&utm_campaign=smartassembly");
			}
			catch
			{
			}
		}

		// Token: 0x06002845 RID: 10309 RVA: 0x00103DD8 File Offset: 0x00101FD8
		public Control5()
		{
			base.SuspendLayout();
			this.label_0.FlatStyle = FlatStyle.System;
			this.label_0.Location = new Point(0, 10);
			this.label_0.Size = new Size(62, 24);
			this.label_0.Text = "Powered by";
			this.pictureBox_0.Image = Class537.smethod_0("{logo}");
			this.pictureBox_0.Location = new Point(72, 0);
			this.pictureBox_0.Size = new Size(32, 32);
			this.pictureBox_0.SizeMode = PictureBoxSizeMode.StretchImage;
			this.label_0.Click += this.Control5_Click;
			this.pictureBox_0.Click += this.Control5_Click;
			base.Click += this.Control5_Click;
			this.Cursor = Cursors.Hand;
			base.TabStop = false;
			base.Size = new Size(112, 32);
			base.Controls.AddRange(new Control[]
			{
				this.pictureBox_0,
				this.label_0
			});
			this.toolTip_0.SetToolTip(this, "Powered by SmartAssembly");
			this.toolTip_0.SetToolTip(this.label_0, "Powered by SmartAssembly");
			this.toolTip_0.SetToolTip(this.pictureBox_0, "Powered by SmartAssembly");
			base.ResumeLayout(true);
		}

		// Token: 0x04001411 RID: 5137
		private const string string_0 = "Powered by SmartAssembly";

		// Token: 0x04001412 RID: 5138
		private const string string_1 = "http://www.red-gate.com/products/dotnet-development/smartassembly/?utm_source=smartassemblyui&utm_medium=supportlink&utm_content=aerdialogbox&utm_campaign=smartassembly";

		// Token: 0x04001413 RID: 5139
		private Label label_0 = new Label();

		// Token: 0x04001414 RID: 5140
		private PictureBox pictureBox_0 = new PictureBox();

		// Token: 0x04001415 RID: 5141
		private ToolTip toolTip_0 = new ToolTip();

		// Token: 0x04001416 RID: 5142
		private float float_0 = 1f;

		// Token: 0x04001417 RID: 5143
		private float float_1 = 1f;
	}
}
