﻿using System;

namespace TEx.Trading
{
	// Token: 0x020003B7 RID: 951
	[Serializable]
	internal sealed class ShownOrder : Order
	{
		// Token: 0x170006A8 RID: 1704
		// (get) Token: 0x060026AE RID: 9902 RVA: 0x000FC34C File Offset: 0x000FA54C
		// (set) Token: 0x060026AF RID: 9903 RVA: 0x0000ED05 File Offset: 0x0000CF05
		public string SymblCode
		{
			get
			{
				return this._SymblCode;
			}
			set
			{
				this._SymblCode = value;
			}
		}

		// Token: 0x170006A9 RID: 1705
		// (get) Token: 0x060026B0 RID: 9904 RVA: 0x000FC364 File Offset: 0x000FA564
		// (set) Token: 0x060026B1 RID: 9905 RVA: 0x0000ED10 File Offset: 0x0000CF10
		public string OrderTypeDesc
		{
			get
			{
				return this._OrderTypeDesc;
			}
			set
			{
				this._OrderTypeDesc = value;
			}
		}

		// Token: 0x170006AA RID: 1706
		// (get) Token: 0x060026B2 RID: 9906 RVA: 0x000FC37C File Offset: 0x000FA57C
		// (set) Token: 0x060026B3 RID: 9907 RVA: 0x0000ED1B File Offset: 0x0000CF1B
		public string OrderStatusDesc
		{
			get
			{
				return this._OrderStatusDesc;
			}
			set
			{
				this._OrderStatusDesc = value;
			}
		}

		// Token: 0x040012A0 RID: 4768
		private string _SymblCode;

		// Token: 0x040012A1 RID: 4769
		private string _OrderTypeDesc;

		// Token: 0x040012A2 RID: 4770
		private string _OrderStatusDesc;
	}
}
