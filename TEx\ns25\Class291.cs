﻿using System;
using System.Linq;
using System.Windows.Forms;
using ns27;
using ns28;
using ns33;
using TEx;
using TEx.Trading;
using TEx.Util;

namespace ns25
{
	// Token: 0x02000212 RID: 530
	internal sealed class Class291 : Class290
	{
		// Token: 0x060015B2 RID: 5554 RVA: 0x00008AC0 File Offset: 0x00006CC0
		public Class291()
		{
			base.RowContextMenuStripNeeded += this.Class291_RowContextMenuStripNeeded;
		}

		// Token: 0x060015B3 RID: 5555 RVA: 0x00008ADC File Offset: 0x00006CDC
		public Class291(SortableBindingList<ShownOrder> sortableBindingList_1) : this()
		{
			this.sortableBindingList_0 = sortableBindingList_1;
		}

		// Token: 0x060015B4 RID: 5556 RVA: 0x00008AED File Offset: 0x00006CED
		protected override void vmethod_1()
		{
			this.sortableBindingList_0 = Base.Trading.CurrOrdersList;
			base.DataSource = this.sortableBindingList_0;
			this.method_9();
		}

		// Token: 0x060015B5 RID: 5557 RVA: 0x00008A1A File Offset: 0x00006C1A
		public void method_5()
		{
			base.DataSource = null;
			this.vmethod_1();
		}

		// Token: 0x060015B6 RID: 5558 RVA: 0x00008B0E File Offset: 0x00006D0E
		public void method_6(SortableBindingList<ShownOrder> sortableBindingList_1)
		{
			this.sortableBindingList_0 = sortableBindingList_1;
			this.method_5();
		}

		// Token: 0x060015B7 RID: 5559 RVA: 0x0008FC8C File Offset: 0x0008DE8C
		protected override void vmethod_0()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = "moItemcloseall";
			toolStripMenuItem.Text = "全部撤销";
			toolStripMenuItem.Click += this.method_7;
			toolStripMenuItem.Paint += this.method_8;
			ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
			contextMenuStrip.Items.Add(toolStripMenuItem);
			Base.UI.smethod_73(contextMenuStrip);
			this.ContextMenuStrip = contextMenuStrip;
		}

		// Token: 0x060015B8 RID: 5560 RVA: 0x00008B1F File Offset: 0x00006D1F
		private void method_7(object sender, EventArgs e)
		{
			Base.Trading.smethod_74();
			this.method_5();
		}

		// Token: 0x060015B9 RID: 5561 RVA: 0x00008B2E File Offset: 0x00006D2E
		private void method_8(object sender, PaintEventArgs e)
		{
			if (Base.Trading.smethod_37().Any<ShownOrder>())
			{
				(sender as ToolStripMenuItem).Enabled = false;
			}
			else
			{
				(sender as ToolStripMenuItem).Enabled = true;
			}
		}

		// Token: 0x060015BA RID: 5562 RVA: 0x0008FCFC File Offset: 0x0008DEFC
		public void method_9()
		{
			if (base.Columns.Count > 0)
			{
				base.Columns[0].HeaderText = "品种";
				base.Columns[1].HeaderText = "属性";
				base.Columns[2].HeaderText = "状态";
				base.Columns[3].Visible = false;
				base.Columns[4].Visible = false;
				base.Columns[5].Visible = false;
				base.Columns[6].Visible = false;
				base.Columns[7].Visible = false;
				base.Columns[8].HeaderText = "数量";
				base.Columns[9].HeaderText = "委托价格";
				base.Columns[10].HeaderText = "委托时间";
				base.Columns[11].Visible = false;
				base.Columns[12].Visible = false;
				if (Base.UI.Form.IsInBlindTestMode)
				{
					if (!Base.UI.Form.IsSingleBlindTest)
					{
						base.Columns[0].Visible = false;
					}
					base.Columns[10].Visible = false;
				}
				else
				{
					base.Columns[0].Visible = true;
					base.Columns[10].Visible = true;
				}
			}
		}

		// Token: 0x060015BB RID: 5563 RVA: 0x0008FE88 File Offset: 0x0008E088
		private void Class291_RowContextMenuStripNeeded(object sender, DataGridViewRowContextMenuStripNeededEventArgs e)
		{
			DataGridViewRow dataGridViewRow = base.Rows[e.RowIndex];
			ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = "moItemEdit";
			toolStripMenuItem.Text = "修改...";
			toolStripMenuItem.Click += this.method_10;
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Name = "moItemcloseCurr";
			toolStripMenuItem2.Text = "撤销当前";
			toolStripMenuItem2.Click += this.method_11;
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Name = "moItemcloseall2";
			toolStripMenuItem3.Text = "全部撤销";
			toolStripMenuItem3.Click += this.method_7;
			contextMenuStrip.Items.Add(toolStripMenuItem);
			contextMenuStrip.Items.Add(toolStripMenuItem2);
			contextMenuStrip.Items.Add(toolStripMenuItem3);
			Base.UI.smethod_73(contextMenuStrip);
			e.ContextMenuStrip = contextMenuStrip;
			ShownOrder shownOrder = dataGridViewRow.DataBoundItem as ShownOrder;
			e.ContextMenuStrip.Items[0].Enabled = true;
			e.ContextMenuStrip.Items[1].Enabled = true;
			e.ContextMenuStrip.Items[2].Enabled = true;
			if (Base.Trading.smethod_37().Count<ShownOrder>() < 1)
			{
				e.ContextMenuStrip.Items[0].Enabled = false;
				e.ContextMenuStrip.Items[1].Enabled = false;
				e.ContextMenuStrip.Items[2].Enabled = false;
			}
			else if (shownOrder.OrderStatus != 0)
			{
				e.ContextMenuStrip.Items[0].Enabled = false;
				e.ContextMenuStrip.Items[1].Enabled = true;
			}
			if (e.ContextMenuStrip.Items[0].Enabled)
			{
				e.ContextMenuStrip.Items[0].Tag = shownOrder.ID;
				e.ContextMenuStrip.Items[1].Tag = shownOrder.ID;
			}
		}

		// Token: 0x060015BC RID: 5564 RVA: 0x00008B58 File Offset: 0x00006D58
		private void method_10(object sender, EventArgs e)
		{
			new EditOrderForm((int)(sender as ToolStripMenuItem).Tag)
			{
				Owner = Base.UI.MainForm
			}.Show();
		}

		// Token: 0x060015BD RID: 5565 RVA: 0x000900A4 File Offset: 0x0008E2A4
		private void method_11(object sender, EventArgs e)
		{
			try
			{
				Base.Trading.smethod_73((int)(sender as ToolStripMenuItem).Tag);
				this.method_5();
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x04000B11 RID: 2833
		private SortableBindingList<ShownOrder> sortableBindingList_0;
	}
}
