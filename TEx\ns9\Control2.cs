﻿using System;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using DevComponents.DotNetBar;

namespace ns9
{
	// Token: 0x02000252 RID: 594
	internal sealed class Control2 : DevComponents.DotNetBar.TabControl
	{
		// Token: 0x0600196E RID: 6510 RVA: 0x0000A935 File Offset: 0x00008B35
		public Control2()
		{
			base.Style = eTabStripStyle.Flat;
			base.TabAlignment = eTabStripAlignment.Bottom;
			this.Dock = DockStyle.Fill;
			this.BackColor = Color.FromArgb(194, 217, 247);
		}

		// Token: 0x0600196F RID: 6511 RVA: 0x000AB134 File Offset: 0x000A9334
		public void method_0(string string_0)
		{
			Control2.Class317 @class = new Control2.Class317();
			@class.string_0 = string_0;
			TabItem tabItem = base.Tabs.Cast<TabItem>().ToList<TabItem>().SingleOrDefault(new Func<TabItem, bool>(@class.method_0));
			if (tabItem != null)
			{
				base.SelectedTab = tabItem;
			}
		}

		// Token: 0x02000253 RID: 595
		[CompilerGenerated]
		private sealed class Class317
		{
			// Token: 0x06001971 RID: 6513 RVA: 0x000AB17C File Offset: 0x000A937C
			internal bool method_0(TabItem tabItem_0)
			{
				return tabItem_0.Text.Contains(this.string_0);
			}

			// Token: 0x04000CC8 RID: 3272
			public string string_0;
		}
	}
}
