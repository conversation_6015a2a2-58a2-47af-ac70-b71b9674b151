﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Xml;
using ns11;
using ns13;
using ns18;
using ns19;
using ns22;
using ns24;
using ns25;
using ns28;
using ns30;
using SmartAssembly.Shared.ReportHelper;

namespace ns3
{
	// Token: 0x020003F0 RID: 1008
	internal sealed class Class532 : Class531
	{
		// Token: 0x0600276D RID: 10093 RVA: 0x00100540 File Offset: 0x000FE740
		public Class532(Guid guid_1, Exception exception_1, IWebProxy iwebProxy_1)
		{
			this.guid_0 = guid_1;
			this.exception_0 = exception_1;
			base.method_0(iwebProxy_1);
			string a = "UNICODE".ToUpper();
			if (a == "ASCII")
			{
				this.char_0 = new char[]
				{
					'a',
					'b',
					'c',
					'd',
					'e',
					'f',
					'g',
					'h',
					'i',
					'j',
					'k',
					'l',
					'm',
					'n',
					'o',
					'p',
					'q',
					'r',
					's',
					't',
					'u',
					'v',
					'w',
					'x',
					'y',
					'z',
					'A',
					'B',
					'C',
					'D',
					'E',
					'F',
					'G',
					'H',
					'I',
					'J',
					'K',
					'L',
					'M',
					'N',
					'O',
					'P',
					'Q',
					'R',
					'S',
					'T',
					'U',
					'V',
					'W',
					'X',
					'Y',
					'Z',
					'0',
					'1',
					'2',
					'3',
					'4',
					'5',
					'6',
					'7',
					'8',
					'9'
				};
				return;
			}
			if (!(a == "UNICODE"))
			{
				return;
			}
			this.char_0 = new char[]
			{
				'\u0001',
				'\u0002',
				'\u0003',
				'\u0004',
				'\u0005',
				'\u0006',
				'\a',
				'\b',
				'\u000e',
				'\u000f',
				'\u0010',
				'\u0011',
				'\u0012',
				'\u0013',
				'\u0014',
				'\u0015',
				'\u0016',
				'\u0017',
				'\u0018',
				'\u0019',
				'\u001a',
				'\u001b',
				'\u001c',
				'\u001d',
				'\u001e',
				'\u001f',
				'\u007f',
				'\u0080',
				'\u0081',
				'\u0082',
				'\u0083',
				'\u0084',
				'\u0086',
				'\u0087',
				'\u0088',
				'\u0089',
				'\u008a',
				'\u008b',
				'\u008c',
				'\u008d',
				'\u008e',
				'\u008f',
				'\u0090',
				'\u0091',
				'\u0092',
				'\u0093',
				'\u0094',
				'\u0095',
				'\u0096',
				'\u0097',
				'\u0098',
				'\u0099',
				'\u009a',
				'\u009b',
				'\u009c',
				'\u009d',
				'\u009e',
				'\u009f'
			};
		}

		// Token: 0x0600276E RID: 10094 RVA: 0x00100618 File Offset: 0x000FE818
		private static string smethod_0(object object_0)
		{
			try
			{
				if (object_0 == null)
				{
					return string.Empty;
				}
				if (object_0 is int)
				{
					return ((int)object_0).ToString("x");
				}
				if (object_0 is long)
				{
					return ((long)object_0).ToString("x");
				}
				if (object_0 is short)
				{
					return ((short)object_0).ToString("x");
				}
				if (object_0 is uint)
				{
					return ((uint)object_0).ToString("x");
				}
				if (object_0 is ulong)
				{
					return ((ulong)object_0).ToString("x");
				}
				if (object_0 is ushort)
				{
					return ((ushort)object_0).ToString("x");
				}
				if (object_0 is byte)
				{
					return ((byte)object_0).ToString("x");
				}
				if (object_0 is sbyte)
				{
					return ((sbyte)object_0).ToString("x");
				}
				if (object_0 is IntPtr)
				{
					return ((IntPtr)object_0).ToInt64().ToString("x");
				}
				if (object_0 is UIntPtr)
				{
					return ((UIntPtr)object_0).ToUInt64().ToString("x");
				}
			}
			catch
			{
			}
			return string.Empty;
		}

		// Token: 0x0600276F RID: 10095 RVA: 0x0000F186 File Offset: 0x0000D386
		private static string smethod_1(string string_10)
		{
			if (string_10.StartsWith("\"<RSAKeyValue>") && string_10.EndsWith("</RSAKeyValue>\""))
			{
				return "*** Information not reported for security reasons ***";
			}
			return string_10;
		}

		// Token: 0x06002770 RID: 10096 RVA: 0x001007B4 File Offset: 0x000FE9B4
		private void method_5(Class536 class536_0, FieldInfo fieldInfo_0)
		{
			string text = (fieldInfo_0 == null) ? null : fieldInfo_0.Name;
			string string_ = (fieldInfo_0 == null) ? "Object" : "Field";
			object obj = class536_0.method_0();
			if (obj == null)
			{
				using (new Class544(this.xmlWriter_0, string_))
				{
					if (fieldInfo_0 != null)
					{
						if (fieldInfo_0.IsStatic)
						{
							this.xmlWriter_0.WriteAttributeString("Static", "1");
						}
						Type fieldType = fieldInfo_0.FieldType;
						if (fieldType != null && fieldType.HasElementType)
						{
							this.method_9(fieldType.GetElementType());
							if (fieldType.IsByRef)
							{
								this.xmlWriter_0.WriteAttributeString("ByRef", "1");
							}
							if (fieldType.IsPointer)
							{
								this.xmlWriter_0.WriteAttributeString("Pointer", "1");
							}
							if (fieldType.IsArray)
							{
								this.xmlWriter_0.WriteAttributeString("Rank", fieldType.GetArrayRank().ToString());
							}
						}
						else
						{
							this.method_9(fieldType);
						}
					}
					if (text != null)
					{
						this.method_7(text);
					}
					this.xmlWriter_0.WriteAttributeString("Null", "1");
				}
				return;
			}
			Type type = class536_0.method_0().GetType();
			string text2 = null;
			string text3 = null;
			if (obj is string)
			{
				text2 = "System.String";
			}
			if (text2 == null)
			{
				if (!type.IsPrimitive && !(obj is IntPtr) && !(obj is UIntPtr))
				{
					if (type.IsValueType && type.Module != base.GetType().Module)
					{
						text2 = type.FullName;
					}
				}
				else
				{
					text2 = type.FullName;
					if (obj is char)
					{
						int num = (int)((char)obj);
						StringBuilder stringBuilder = new StringBuilder();
						if (num >= 32)
						{
							stringBuilder.Append('\'');
							stringBuilder.Append((char)obj);
							stringBuilder.Append("' ");
						}
						stringBuilder.Append("(0x");
						stringBuilder.Append(num.ToString("x"));
						stringBuilder.Append(')');
						text3 = stringBuilder.ToString();
					}
					if (obj is bool)
					{
						text3 = obj.ToString().ToLower();
					}
					if (text3 == null)
					{
						string text4 = Class532.smethod_0(obj);
						if (text4.Length > 0)
						{
							StringBuilder stringBuilder2 = new StringBuilder();
							stringBuilder2.Append(obj.ToString());
							stringBuilder2.Append(" (0x");
							stringBuilder2.Append(text4);
							stringBuilder2.Append(')');
							text3 = stringBuilder2.ToString();
						}
						else
						{
							text3 = obj.ToString();
						}
					}
				}
			}
			using (new Class544(this.xmlWriter_0, string_))
			{
				if (fieldInfo_0 != null && fieldInfo_0.IsStatic)
				{
					this.xmlWriter_0.WriteAttributeString("Static", "1");
				}
				if (text2 != null)
				{
					this.method_9(type);
					if (text != null)
					{
						this.method_7(text);
					}
					if (type.IsEnum)
					{
						text3 = obj.ToString();
					}
					if (obj is Guid)
					{
						text3 = "{" + obj + "}";
					}
					if (text3 == null)
					{
						text3 = "\"" + obj + "\"";
					}
					this.xmlWriter_0.WriteAttributeString("Value", Class532.smethod_1(text3));
				}
				else
				{
					if (fieldInfo_0 != null)
					{
						this.method_9(fieldInfo_0.FieldType);
					}
					this.method_6(class536_0);
					if (text != null)
					{
						this.method_7(text);
					}
				}
			}
		}

		// Token: 0x06002771 RID: 10097 RVA: 0x00100B1C File Offset: 0x000FED1C
		private void method_6(Class536 class536_0)
		{
			object obj = class536_0.method_0();
			int num = -1;
			for (int i = 0; i < this.list_0.Count; i++)
			{
				if (this.list_0[i].method_0() == obj)
				{
					num = i;
					IL_39:
					if (num == -1)
					{
						num = this.list_0.Count;
						this.list_0.Add(class536_0);
					}
					this.xmlWriter_0.WriteAttributeString("ID", num.ToString());
					return;
				}
			}
			goto IL_39;
		}

		// Token: 0x06002772 RID: 10098 RVA: 0x00100B98 File Offset: 0x000FED98
		private void method_7(string string_10)
		{
			int num = this.method_10(string_10);
			if (num != -1)
			{
				this.xmlWriter_0.WriteAttributeString("NameID", num.ToString());
				return;
			}
			this.xmlWriter_0.WriteAttributeString("Name", string_10);
		}

		// Token: 0x06002773 RID: 10099 RVA: 0x00100BDC File Offset: 0x000FEDDC
		private static Class532.Struct28 smethod_2(Type type_0)
		{
			Class532.Struct28 empty = Class532.Struct28.Empty;
			if (type_0 != null && type_0.Assembly.GetType("SmartAssembly.Attributes.PoweredByAttribute") != null)
			{
				empty.string_0 = ((type_0.MetadataToken & 16777215) - 1).ToString();
				Assembly assembly = type_0.Assembly;
				empty.struct27_0 = new Class532.Struct27(assembly.ManifestModule.ModuleVersionId.ToString("B"), assembly.FullName);
			}
			return empty;
		}

		// Token: 0x06002774 RID: 10100 RVA: 0x00100C54 File Offset: 0x000FEE54
		private int method_8(Class532.Struct28 struct28_0)
		{
			string key = struct28_0.struct27_0.string_0.ToUpper();
			if (this.dictionary_3.ContainsKey(key))
			{
				return this.dictionary_3[key];
			}
			int count = this.list_2.Count;
			this.list_2.Add(struct28_0.struct27_0);
			this.dictionary_3.Add(key, count);
			return count;
		}

		// Token: 0x06002775 RID: 10101 RVA: 0x00100CB8 File Offset: 0x000FEEB8
		private void method_9(Type type_0)
		{
			if (type_0 == null)
			{
				return;
			}
			try
			{
				Class532.Struct28 @struct = Class532.smethod_2(type_0);
				if (!@struct.IsEmpty)
				{
					this.xmlWriter_0.WriteAttributeString("TypeDefID", @struct.string_0);
					int num = this.method_8(@struct);
					if (num > 0)
					{
						this.xmlWriter_0.WriteAttributeString("Assembly", num.ToString());
					}
				}
				else
				{
					string fullName = type_0.FullName;
					int value;
					if (this.dictionary_2.ContainsKey(fullName))
					{
						value = this.dictionary_2[fullName];
					}
					else
					{
						StringBuilder stringBuilder = new StringBuilder();
						string name = type_0.Assembly.GetName().Name;
						if (name.Length > 0 && name != "mscorlib")
						{
							stringBuilder.Append('[');
							stringBuilder.Append(name);
							stringBuilder.Append(']');
						}
						string @namespace = type_0.Namespace;
						if (@namespace.Length > 0)
						{
							stringBuilder.Append(@namespace);
							stringBuilder.Append('.');
						}
						if (type_0.HasElementType)
						{
							type_0 = type_0.GetElementType();
						}
						int num2 = fullName.LastIndexOf("+");
						if (num2 > 0)
						{
							string value2 = fullName.Substring(@namespace.Length + 1, num2 - @namespace.Length).Replace("+", "/");
							stringBuilder.Append(value2);
						}
						stringBuilder.Append(type_0.Name);
						value = this.list_1.Count;
						this.list_1.Add(stringBuilder.ToString());
						this.dictionary_2.Add(fullName, value);
					}
					this.xmlWriter_0.WriteAttributeString("TypeName", value.ToString());
				}
			}
			catch
			{
			}
		}

		// Token: 0x06002776 RID: 10102 RVA: 0x00100E80 File Offset: 0x000FF080
		private int method_10(string string_10)
		{
			int result;
			try
			{
				bool flag = this.char_0[0] == '\u0001';
				if (string_10 != null && string_10.Length != 0 && (!flag || string_10.Length <= 4))
				{
					if (flag || string_10[0] == '#')
					{
						int num = 0;
						int num2 = string_10.Length - 1;
						IL_99:
						while (num2 >= 0 && (flag || num2 != 0))
						{
							char c = string_10[num2];
							bool flag2 = false;
							int i = 0;
							while (i < this.char_0.Length)
							{
								if (this.char_0[i] == c)
								{
									num = num * this.char_0.Length + i;
									flag2 = true;
									IL_91:
									if (flag2)
									{
										num2--;
										goto IL_99;
									}
									return -1;
								}
								else
								{
									i++;
								}
							}
							goto IL_91;
						}
						return num;
					}
				}
				result = -1;
			}
			catch
			{
				result = -1;
			}
			return result;
		}

		// Token: 0x06002777 RID: 10103 RVA: 0x00100F4C File Offset: 0x000FF14C
		private static string smethod_3()
		{
			string result;
			try
			{
				result = Application.ExecutablePath;
			}
			catch
			{
				result = "N/A";
			}
			return result;
		}

		// Token: 0x06002778 RID: 10104 RVA: 0x00100F7C File Offset: 0x000FF17C
		private Assembly[] method_11()
		{
			Assembly[] result;
			try
			{
				result = AppDomain.CurrentDomain.GetAssemblies();
			}
			catch
			{
				result = new Assembly[]
				{
					Class532.smethod_4()
				};
			}
			return result;
		}

		// Token: 0x06002779 RID: 10105 RVA: 0x00100FBC File Offset: 0x000FF1BC
		private static Assembly smethod_4()
		{
			Assembly result;
			try
			{
				result = Assembly.GetExecutingAssembly();
			}
			catch
			{
				result = null;
			}
			return result;
		}

		// Token: 0x0600277A RID: 10106 RVA: 0x0000F1A9 File Offset: 0x0000D3A9
		internal byte[] method_12()
		{
			return this.method_13();
		}

		// Token: 0x0600277B RID: 10107 RVA: 0x00100FE8 File Offset: 0x000FF1E8
		private byte[] method_13()
		{
			if (this.byte_0 != null)
			{
				return this.byte_0;
			}
			this.memoryStream_0 = new MemoryStream();
			this.xmlWriter_0 = new XmlTextWriter(this.memoryStream_0, new UTF8Encoding(false));
			this.xmlWriter_0.WriteStartDocument();
			using (new Class544(this.xmlWriter_0, "UnhandledExceptionReport"))
			{
				this.xmlWriter_0.WriteAttributeString("AssemblyID", "{8615E982-79D1-4651-9862-40779F762E5C}".ToUpper());
				this.xmlWriter_0.WriteAttributeString("DateTime", DateTime.Now.ToString("s"));
				this.xmlWriter_0.WriteAttributeString("Path", Class532.smethod_3());
				if (this.guid_0 != Guid.Empty)
				{
					this.xmlWriter_0.WriteAttributeString("UserID", this.guid_0.ToString("B"));
				}
				this.xmlWriter_0.WriteAttributeString("ReportID", Guid.NewGuid().ToString("B"));
				if (this.list_2.Count > 0)
				{
					this.list_2.Clear();
				}
				this.list_2.Add(new Class532.Struct27("{8615E982-79D1-4651-9862-40779F762E5C}", string.Empty));
				if (this.dictionary_3.Count > 0)
				{
					this.dictionary_3.Clear();
				}
				this.dictionary_3.Add("{8615E982-79D1-4651-9862-40779F762E5C}", 0);
				using (new Class544(this.xmlWriter_0, "Assemblies"))
				{
					Assembly assembly = Class532.smethod_4();
					foreach (Assembly assembly2 in this.method_11())
					{
						if (assembly2 != null)
						{
							using (new Class544(this.xmlWriter_0, "Assembly"))
							{
								try
								{
									this.xmlWriter_0.WriteAttributeString("Name", assembly2.FullName);
									this.xmlWriter_0.WriteAttributeString("CodeBase", assembly2.CodeBase);
									if (assembly2 == assembly)
									{
										this.xmlWriter_0.WriteAttributeString("This", "1");
									}
								}
								catch
								{
								}
							}
						}
					}
				}
				using (new Class544(this.xmlWriter_0, "CustomProperties"))
				{
					if (this.dictionary_0 != null && this.dictionary_0.Count > 0)
					{
						foreach (string text in this.dictionary_0.Keys)
						{
							using (new Class544(this.xmlWriter_0, "CustomProperty"))
							{
								this.xmlWriter_0.WriteAttributeString("Name", text);
								string text2 = (string)this.dictionary_0[text];
								if (text2 == null)
								{
									this.xmlWriter_0.WriteAttributeString("Null", "1");
								}
								else
								{
									this.xmlWriter_0.WriteAttributeString("Value", "\"" + text2 + "\"");
								}
							}
						}
					}
				}
				if (this.dictionary_1 != null && this.dictionary_1.Count > 0)
				{
					using (new Class544(this.xmlWriter_0, "AttachedFiles"))
					{
						foreach (string text3 in this.dictionary_1.Keys)
						{
							using (new Class544(this.xmlWriter_0, "AttachedFile"))
							{
								this.xmlWriter_0.WriteAttributeString("Key", text3);
								Class532.Struct26 @struct = this.dictionary_1[text3];
								this.xmlWriter_0.WriteAttributeString("FileName", @struct.string_0);
								XmlWriter xmlWriter = this.xmlWriter_0;
								string localName = "Length";
								int i = @struct.int_0;
								xmlWriter.WriteAttributeString(localName, i.ToString());
								if (@struct.string_2.Length > 0)
								{
									this.xmlWriter_0.WriteAttributeString("Error", @struct.string_2);
								}
								else
								{
									this.xmlWriter_0.WriteAttributeString("Data", @struct.string_1);
								}
							}
						}
					}
				}
				using (new Class544(this.xmlWriter_0, "SystemInformation"))
				{
					try
					{
						Version version = Environment.Version;
						Version version2 = Environment.OSVersion.Version;
						string value = Environment.OSVersion.Platform.ToString();
						Enum31 @enum;
						string value2;
						bool flag;
						OsInformation.smethod_3(out @enum, ref version, ref version2, ref value, out value2, out flag);
						this.xmlWriter_0.WriteElementString("NETVersion", version.ToString());
						this.xmlWriter_0.WriteElementString("OSVersion", version2.ToString());
						this.xmlWriter_0.WriteElementString("OSPlatformID", value);
						this.xmlWriter_0.WriteElementString("OSDescription", value2);
						this.xmlWriter_0.WriteElementString("ServicePack", Class543.ServicePack);
						this.xmlWriter_0.WriteElementString("ServerR2", Class543.IsServerR2 ? "1" : "0");
						this.xmlWriter_0.WriteElementString("X64", OsVersionInformation.IsX64 ? "1" : "0");
						this.xmlWriter_0.WriteElementString("Workstation", Class543.IsWorkstation ? "1" : "0");
					}
					catch
					{
					}
				}
				List<Exception> list = new List<Exception>();
				for (Exception innerException = this.exception_0; innerException != null; innerException = innerException.InnerException)
				{
					list.Add(innerException);
				}
				list.Reverse();
				using (new Class544(this.xmlWriter_0, "StackTrace"))
				{
					foreach (Exception ex in list)
					{
						try
						{
							this.method_16(ex);
							if (ex.Data.Contains("SmartStackFrames"))
							{
								ICollection collection = (ICollection)ex.Data["SmartStackFrames"];
								int count = collection.Count;
								int num = 0;
								foreach (object obj in collection)
								{
									try
									{
										Type type = obj.GetType();
										num++;
										if (num > 100 && num == count - 100)
										{
											using (new Class544(this.xmlWriter_0, "RemovedFrames"))
											{
												this.xmlWriter_0.WriteAttributeString("TotalFramesCount", count.ToString());
											}
										}
										else if (num <= 100 || num > count - 100)
										{
											int num2 = (int)type.GetField("MethodID").GetValue(obj);
											int num3 = (int)type.GetField("ILOffset").GetValue(obj);
											int num4 = (int)type.GetField("ExceptionStackDepth").GetValue(obj);
											object[] array2 = (object[])type.GetField("Objects").GetValue(obj);
											Class532.Struct28 struct28_ = Class532.smethod_2(type);
											if (!struct28_.IsEmpty)
											{
												using (new Class544(this.xmlWriter_0, "StackFrame"))
												{
													this.xmlWriter_0.WriteAttributeString("MethodID", num2.ToString());
													this.xmlWriter_0.WriteAttributeString("ExceptionStackDepth", num4.ToString());
													int num5 = this.method_8(struct28_);
													if (num5 > 0)
													{
														this.xmlWriter_0.WriteAttributeString("Assembly", num5.ToString());
													}
													if (num3 != -1)
													{
														this.xmlWriter_0.WriteAttributeString("ILOffset", num3.ToString());
													}
													foreach (object object_ in array2)
													{
														try
														{
															this.method_5(new Class536(object_, true), null);
														}
														catch
														{
														}
													}
												}
											}
										}
									}
									catch
									{
									}
								}
							}
						}
						catch
						{
						}
					}
				}
				this.method_14();
				using (new Class544(this.xmlWriter_0, "TypeNames"))
				{
					XmlWriter xmlWriter2 = this.xmlWriter_0;
					string localName2 = "Count";
					int i = this.list_1.Count;
					xmlWriter2.WriteAttributeString(localName2, i.ToString());
					for (int j = 0; j < this.list_1.Count; j++)
					{
						string value3;
						try
						{
							value3 = this.list_1[j].ToString();
						}
						catch (Exception ex2)
						{
							value3 = "\"" + ex2.Message + "\"";
						}
						this.xmlWriter_0.WriteElementString("TypeName", value3);
					}
				}
				using (new Class544(this.xmlWriter_0, "AssemblyIDs"))
				{
					XmlWriter xmlWriter3 = this.xmlWriter_0;
					string localName3 = "Count";
					int i = this.list_2.Count;
					xmlWriter3.WriteAttributeString(localName3, i.ToString());
					for (int k = 0; k < this.list_2.Count; k++)
					{
						using (new Class544(this.xmlWriter_0, "AssemblyID"))
						{
							Class532.Struct27 struct2 = this.list_2[k];
							this.xmlWriter_0.WriteAttributeString("ID", struct2.string_0);
							if (struct2.string_1.Length > 0)
							{
								this.xmlWriter_0.WriteAttributeString("FullName", struct2.string_1);
							}
						}
					}
				}
			}
			this.xmlWriter_0.WriteEndDocument();
			this.xmlWriter_0.Flush();
			this.memoryStream_0.Flush();
			this.byte_0 = this.memoryStream_0.ToArray();
			return this.byte_0;
		}

		// Token: 0x0600277C RID: 10108 RVA: 0x00101C08 File Offset: 0x000FFE08
		private void method_14()
		{
			using (new Class544(this.xmlWriter_0, "Objects"))
			{
				for (int i = 0; i < this.list_0.Count; i++)
				{
					Class536 class2 = this.list_0[i];
					object obj = class2.method_0();
					Type type = class2.method_1();
					using (new Class544(this.xmlWriter_0, "ObjectDef"))
					{
						this.xmlWriter_0.WriteAttributeString("ID", i.ToString());
						string text = null;
						bool flag = true;
						string[] array = "".Split(new char[]
						{
							','
						});
						for (int j = 0; j < array.Length; j++)
						{
							string text2 = array[j];
							if (text2 != "" && type.FullName.StartsWith(text2))
							{
								flag = false;
								IL_C3:
								object[] customAttributes = type.GetCustomAttributes(true);
								for (j = 0; j < customAttributes.Length; j++)
								{
									string name = ((Attribute)customAttributes[j]).GetType().Name;
									if (!(name != "DoNotCaptureFieldsAttribute") || !(name != "DoNotCaptureAttribute"))
									{
										flag = false;
										IL_117:
										if (flag)
										{
											try
											{
												text = obj.ToString();
												if (text == type.FullName)
												{
													text = null;
												}
												else if (type.IsEnum)
												{
													text = Enum.Format(type, obj, "d");
												}
												else if (obj is Guid)
												{
													text = "{" + text + "}";
												}
												else
												{
													text = "\"" + text + "\"";
												}
											}
											catch
											{
											}
											if (text != null)
											{
												this.xmlWriter_0.WriteAttributeString("Value", Class532.smethod_1(text));
											}
										}
										if (type.HasElementType)
										{
											this.method_9(type.GetElementType());
											if (type.IsByRef)
											{
												this.xmlWriter_0.WriteAttributeString("ByRef", "1");
											}
											if (type.IsPointer)
											{
												this.xmlWriter_0.WriteAttributeString("Pointer", "1");
											}
											if (type.IsArray)
											{
												Array array2 = (Array)obj;
												this.xmlWriter_0.WriteAttributeString("Rank", array2.Rank.ToString());
												StringBuilder stringBuilder = new StringBuilder();
												for (int k = 0; k < array2.Rank; k++)
												{
													if (k > 0)
													{
														stringBuilder.Append(',');
													}
													stringBuilder.Append(array2.GetLength(k));
												}
												this.xmlWriter_0.WriteAttributeString("Length", stringBuilder.ToString());
												if (array2.Rank == 1)
												{
													int length = array2.Length;
													for (int l = 0; l < length; l++)
													{
														if (l == 10 && length > 16)
														{
															l = length - 5;
														}
														try
														{
															this.method_5(new Class536(array2.GetValue(l), false), null);
														}
														catch
														{
														}
													}
												}
											}
										}
										else
										{
											this.method_9(type);
											if (class2.FirstLevel && flag)
											{
												try
												{
													if (obj is IEnumerable)
													{
														using (new Class544(this.xmlWriter_0, "IEnumerable"))
														{
															int num = 0;
															foreach (object object_ in ((IEnumerable)obj))
															{
																if (num > 20)
																{
																	this.xmlWriter_0.WriteElementString("More", string.Empty);
																	break;
																}
																this.method_5(new Class536(object_, false), null);
																num++;
															}
														}
													}
												}
												catch
												{
												}
												this.method_15(class2);
											}
										}
										goto IL_3A9;
									}
								}
								goto IL_117;
							}
						}
						goto IL_C3;
					}
					IL_3A9:;
				}
			}
		}

		// Token: 0x0600277D RID: 10109 RVA: 0x00102080 File Offset: 0x00100280
		private void method_15(Class536 class536_0)
		{
			foreach (FieldInfo fieldInfo in class536_0.method_1().GetFields(BindingFlags.DeclaredOnly | BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic))
			{
				try
				{
					if (!fieldInfo.IsLiteral)
					{
						if (!fieldInfo.IsStatic || !fieldInfo.IsInitOnly)
						{
							bool flag = true;
							object[] customAttributes = fieldInfo.GetCustomAttributes(true);
							int j = 0;
							while (j < customAttributes.Length)
							{
								if (!(((Attribute)customAttributes[j]).GetType().Name == "DoNotCaptureAttribute"))
								{
									j++;
								}
								else
								{
									flag = false;
									IL_7B:
									if (!flag)
									{
										goto IL_9E;
									}
									this.method_5(new Class536(fieldInfo.GetValue(class536_0.method_0()), false), fieldInfo);
									goto IL_9E;
								}
							}
							goto IL_7B;
						}
					}
				}
				catch
				{
				}
				IL_9E:;
			}
			class536_0 = new Class536(class536_0.method_0(), class536_0.method_1().BaseType, class536_0.FirstLevel);
			if (class536_0.method_1() == null)
			{
				return;
			}
			using (new Class544(this.xmlWriter_0, "Field"))
			{
				this.method_7("__base");
				XmlWriter xmlWriter = this.xmlWriter_0;
				string localName = "ID";
				int i = this.list_0.Count;
				xmlWriter.WriteAttributeString(localName, i.ToString());
			}
			this.list_0.Add(class536_0);
		}

		// Token: 0x0600277E RID: 10110 RVA: 0x001021D4 File Offset: 0x001003D4
		private void method_16(Exception exception_1)
		{
			using (new Class544(this.xmlWriter_0, "Exception"))
			{
				try
				{
					Type type = exception_1.GetType();
					this.method_9(type);
					string value = "N/A";
					try
					{
						value = exception_1.Message;
					}
					catch
					{
					}
					this.xmlWriter_0.WriteAttributeString("Message", value);
					string text = exception_1.StackTrace.Trim();
					this.xmlWriter_0.WriteAttributeString("ExceptionStackTrace", text);
					int num = text.IndexOf(' ');
					text = text.Substring(num + 1);
					num = text.IndexOf("\r\n");
					if (num != -1)
					{
						text = text.Substring(0, num);
					}
					this.xmlWriter_0.WriteAttributeString("Method", text);
					this.method_6(new Class536(exception_1, true));
				}
				catch
				{
				}
			}
		}

		// Token: 0x0600277F RID: 10111 RVA: 0x0000F1B1 File Offset: 0x0000D3B1
		internal void method_17(string string_10, object object_0)
		{
			this.dictionary_0.Add(string_10, object_0);
		}

		// Token: 0x06002780 RID: 10112 RVA: 0x001022C8 File Offset: 0x001004C8
		internal void method_18(string string_10, string string_11)
		{
			if (!File.Exists(string_11))
			{
				return;
			}
			Class532.Struct26 value = new Class532.Struct26(string_11);
			this.dictionary_1.Add(string_10, value);
		}

		// Token: 0x06002781 RID: 10113 RVA: 0x001022F4 File Offset: 0x001004F4
		internal bool method_19()
		{
			bool result;
			try
			{
				base.method_4(Enum35.const_0);
				byte[] array;
				try
				{
					array = this.method_13();
				}
				catch (Exception ex)
				{
					int num = -1;
					try
					{
						StackTrace stackTrace = new StackTrace(ex);
						if (stackTrace.FrameCount > 0)
						{
							num = stackTrace.GetFrame(stackTrace.FrameCount - 1).GetILOffset();
						}
					}
					catch
					{
					}
					base.method_3(Enum35.const_0, string.Format("ERR 2006: {0} @ 0x{1:x4}", ex.Message, num));
					return false;
				}
				Class531.Class535 class535_ = new Class531.Class535("<EMAIL>", "TEx", "v6.0.2.3 from 2025/4/18 9:31:56");
				result = base.method_1(array, class535_);
			}
			catch (ThreadAbortException)
			{
				result = false;
			}
			catch (Exception exception_)
			{
				this.method_20(new EventArgs34(exception_));
				result = false;
			}
			return result;
		}

		// Token: 0x140000BE RID: 190
		// (add) Token: 0x06002782 RID: 10114 RVA: 0x001023D4 File Offset: 0x001005D4
		// (remove) Token: 0x06002783 RID: 10115 RVA: 0x0010240C File Offset: 0x0010060C
		public event Delegate37 FatalException
		{
			[CompilerGenerated]
			add
			{
				Delegate37 @delegate = this.delegate37_0;
				Delegate37 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate37 value2 = (Delegate37)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate37>(ref this.delegate37_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate37 @delegate = this.delegate37_0;
				Delegate37 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate37 value2 = (Delegate37)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate37>(ref this.delegate37_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06002784 RID: 10116 RVA: 0x00102444 File Offset: 0x00100644
		public void method_20(EventArgs34 eventArgs34_0)
		{
			Delegate37 @delegate = this.delegate37_0;
			if (@delegate != null)
			{
				@delegate(this, eventArgs34_0);
			}
		}

		// Token: 0x140000BF RID: 191
		// (add) Token: 0x06002785 RID: 10117 RVA: 0x00102464 File Offset: 0x00100664
		// (remove) Token: 0x06002786 RID: 10118 RVA: 0x0010249C File Offset: 0x0010069C
		public event EventHandler DebuggerLaunched
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06002787 RID: 10119 RVA: 0x001024D4 File Offset: 0x001006D4
		private void method_21()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, EventArgs.Empty);
			}
		}

		// Token: 0x06002788 RID: 10120 RVA: 0x001024F8 File Offset: 0x001006F8
		internal void method_22()
		{
			try
			{
				string tempFileName = Path.GetTempFileName();
				this.method_23(tempFileName);
				Process.Start(Path.Combine(Class510.smethod_0(), "SmartAssembly.exe"), "/AddExceptionReport \"" + tempFileName + "\"");
				if (this.eventHandler_0 != null)
				{
					this.eventHandler_0(this, EventArgs.Empty);
				}
			}
			catch (ThreadAbortException)
			{
			}
			catch (Exception exception_)
			{
				this.method_20(new EventArgs34(exception_));
			}
		}

		// Token: 0x06002789 RID: 10121 RVA: 0x00102584 File Offset: 0x00100784
		internal bool method_23(string string_10)
		{
			bool result;
			try
			{
				byte[] array = this.method_13();
				byte[] array2;
				try
				{
					array2 = Class518.smethod_4(array);
				}
				catch
				{
					array2 = null;
				}
				byte[] array3 = Class530.smethod_0(array2, "<RSAKeyValue><Modulus>yQ2ConS5yOgbrcyEORGVmInrvFP5w6o7sazmQbVv+6zQy2jZIyOTO9IGZLSL3u1SOOyw1/Bl0b8VinqMKIocyDWI0FQe3cChNRue5/otHubE9DplNtBZHWm1OQ263cVUcudufVjHy38OGFNAoLoDY4Fl3ZMsbiERNrKQEnpaqA0=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>");
				FileStream fileStream = File.OpenWrite(string_10);
				byte[] bytes = Encoding.ASCII.GetBytes("{8615E982-79D1-4651-9862-40779F762E5C}");
				fileStream.Write(bytes, 0, bytes.Length);
				fileStream.Write(array3, 0, array3.Length);
				fileStream.Close();
				result = true;
			}
			catch (ThreadAbortException)
			{
				result = false;
			}
			catch (Exception)
			{
				result = false;
			}
			return result;
		}

		// Token: 0x04001381 RID: 4993
		private const string string_3 = "{bf13b64c-b3d2-4165-b3f5-7f852d4744cf}";

		// Token: 0x04001382 RID: 4994
		private const string string_4 = "{07572d6f-5375-47d5-8a8c-b5f0cbe5bad0}";

		// Token: 0x04001383 RID: 4995
		private const string string_5 = "{6d3806d4-1193-4601-a7df-2249c7f0014b}";

		// Token: 0x04001384 RID: 4996
		private const string string_6 = "{d316c294-ed40-4778-8b7b-29800a2dcbc3}";

		// Token: 0x04001385 RID: 4997
		private const string string_7 = "{a9035fc5-7ed1-4e0c-8962-dfcb1d508afc}";

		// Token: 0x04001386 RID: 4998
		private const string string_8 = "{73fbfb9b-41e7-4744-bf74-74b7c6c117c1}";

		// Token: 0x04001387 RID: 4999
		private readonly Exception exception_0;

		// Token: 0x04001388 RID: 5000
		private readonly Guid guid_0;

		// Token: 0x04001389 RID: 5001
		private readonly char[] char_0 = new char[0];

		// Token: 0x0400138A RID: 5002
		private readonly Dictionary<string, object> dictionary_0 = new Dictionary<string, object>();

		// Token: 0x0400138B RID: 5003
		private readonly Dictionary<string, Class532.Struct26> dictionary_1 = new Dictionary<string, Class532.Struct26>();

		// Token: 0x0400138C RID: 5004
		private XmlWriter xmlWriter_0;

		// Token: 0x0400138D RID: 5005
		private readonly List<Class536> list_0 = new List<Class536>();

		// Token: 0x0400138E RID: 5006
		private readonly List<string> list_1 = new List<string>();

		// Token: 0x0400138F RID: 5007
		private readonly Dictionary<string, int> dictionary_2 = new Dictionary<string, int>();

		// Token: 0x04001390 RID: 5008
		private readonly List<Class532.Struct27> list_2 = new List<Class532.Struct27>();

		// Token: 0x04001391 RID: 5009
		private readonly Dictionary<string, int> dictionary_3 = new Dictionary<string, int>();

		// Token: 0x04001392 RID: 5010
		private MemoryStream memoryStream_0;

		// Token: 0x04001393 RID: 5011
		private byte[] byte_0;

		// Token: 0x04001394 RID: 5012
		private const string string_9 = "SmartAssembly.exe";

		// Token: 0x04001395 RID: 5013
		[CompilerGenerated]
		private Delegate37 delegate37_0;

		// Token: 0x04001396 RID: 5014
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x020003F1 RID: 1009
		private struct Struct26
		{
			// Token: 0x0600278A RID: 10122 RVA: 0x00102620 File Offset: 0x00100820
			public Struct26(string string_3)
			{
				this.string_0 = string.Empty;
				this.string_1 = string.Empty;
				this.string_2 = string.Empty;
				this.int_0 = 0;
				try
				{
					FileInfo fileInfo = new FileInfo(string_3);
					this.string_0 = Path.GetFileName(string_3);
					this.int_0 = (int)fileInfo.Length;
					byte[] array = new byte[this.int_0];
					using (FileStream fileStream = File.Open(string_3, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
					{
						fileStream.Read(array, 0, this.int_0);
						fileStream.Close();
					}
					byte[] inArray;
					try
					{
						inArray = Class518.smethod_4(array);
					}
					catch
					{
						inArray = null;
					}
					this.string_1 = Convert.ToBase64String(inArray);
				}
				catch (Exception ex)
				{
					this.string_2 = ex.Message;
				}
			}

			// Token: 0x04001397 RID: 5015
			public readonly string string_0;

			// Token: 0x04001398 RID: 5016
			public readonly string string_1;

			// Token: 0x04001399 RID: 5017
			public readonly string string_2;

			// Token: 0x0400139A RID: 5018
			public readonly int int_0;
		}

		// Token: 0x020003F2 RID: 1010
		private struct Struct27
		{
			// Token: 0x0600278B RID: 10123 RVA: 0x0000F1C0 File Offset: 0x0000D3C0
			public Struct27(string string_2, string string_3)
			{
				this.string_0 = string_2;
				this.string_1 = string_3;
			}

			// Token: 0x0400139B RID: 5019
			public readonly string string_0;

			// Token: 0x0400139C RID: 5020
			public readonly string string_1;
		}

		// Token: 0x020003F3 RID: 1011
		private struct Struct28
		{
			// Token: 0x170006C9 RID: 1737
			// (get) Token: 0x0600278C RID: 10124 RVA: 0x0000F1D0 File Offset: 0x0000D3D0
			public bool IsEmpty
			{
				get
				{
					return this.string_0.Length == 0;
				}
			}

			// Token: 0x170006CA RID: 1738
			// (get) Token: 0x0600278D RID: 10125 RVA: 0x0000F1E0 File Offset: 0x0000D3E0
			public static Class532.Struct28 Empty
			{
				get
				{
					return new Class532.Struct28(string.Empty, string.Empty, string.Empty);
				}
			}

			// Token: 0x0600278E RID: 10126 RVA: 0x0000F1F6 File Offset: 0x0000D3F6
			private Struct28(string string_1, string string_2, string string_3)
			{
				this.string_0 = string_1;
				this.struct27_0 = new Class532.Struct27(string_2, string_3);
			}

			// Token: 0x0400139D RID: 5021
			public string string_0;

			// Token: 0x0400139E RID: 5022
			public Class532.Struct27 struct27_0;
		}
	}
}
