﻿using System;
using System.Collections.Generic;

namespace TEx.ImportTrans
{
	// Token: 0x02000357 RID: 855
	public sealed class CfmmcAcct : IStoreElement
	{
		// Token: 0x060023B9 RID: 9145 RVA: 0x0000DF57 File Offset: 0x0000C157
		public CfmmcAcct()
		{
			this.list_0 = new List<BindingAcct>();
		}

		// Token: 0x060023BA RID: 9146 RVA: 0x0000DF6C File Offset: 0x0000C16C
		public CfmmcAcct(string id, string password) : this()
		{
			this.id = id;
			this.password = password;
		}

		// Token: 0x060023BB RID: 9147 RVA: 0x0000DF84 File Offset: 0x0000C184
		public CfmmcAcct(string id, string password, DateTime? begDT, DateTime? endDT) : this(id, password)
		{
			this.begDT = begDT;
			this.endDT = endDT;
		}

		// Token: 0x060023BC RID: 9148 RVA: 0x0000DF9F File Offset: 0x0000C19F
		public CfmmcAcct(string id, string password, DateTime? begDT, DateTime? endDT, DateTime? lastDnldTime, string note) : this(id, password, begDT, endDT)
		{
			this.lastDnldTime = lastDnldTime;
			this.note = note;
		}

		// Token: 0x17000624 RID: 1572
		// (get) Token: 0x060023BD RID: 9149 RVA: 0x000F0B20 File Offset: 0x000EED20
		// (set) Token: 0x060023BE RID: 9150 RVA: 0x0000DFBE File Offset: 0x0000C1BE
		public string ID
		{
			get
			{
				return this.id;
			}
			set
			{
				this.id = value;
			}
		}

		// Token: 0x17000625 RID: 1573
		// (get) Token: 0x060023BF RID: 9151 RVA: 0x000F0B38 File Offset: 0x000EED38
		// (set) Token: 0x060023C0 RID: 9152 RVA: 0x0000DFC9 File Offset: 0x0000C1C9
		public string Password
		{
			get
			{
				return this.password;
			}
			set
			{
				this.password = value;
			}
		}

		// Token: 0x17000626 RID: 1574
		// (get) Token: 0x060023C1 RID: 9153 RVA: 0x000F0B50 File Offset: 0x000EED50
		// (set) Token: 0x060023C2 RID: 9154 RVA: 0x0000DFD4 File Offset: 0x0000C1D4
		public DateTime? BeginDate
		{
			get
			{
				return this.begDT;
			}
			set
			{
				this.begDT = value;
			}
		}

		// Token: 0x17000627 RID: 1575
		// (get) Token: 0x060023C3 RID: 9155 RVA: 0x000F0B68 File Offset: 0x000EED68
		// (set) Token: 0x060023C4 RID: 9156 RVA: 0x0000DFDF File Offset: 0x0000C1DF
		public DateTime? EndDate
		{
			get
			{
				return this.endDT;
			}
			set
			{
				this.endDT = value;
			}
		}

		// Token: 0x17000628 RID: 1576
		// (get) Token: 0x060023C5 RID: 9157 RVA: 0x000F0B80 File Offset: 0x000EED80
		// (set) Token: 0x060023C6 RID: 9158 RVA: 0x0000DFEA File Offset: 0x0000C1EA
		public DateTime? LastDownloadTime
		{
			get
			{
				return this.lastDnldTime;
			}
			set
			{
				this.lastDnldTime = value;
			}
		}

		// Token: 0x17000629 RID: 1577
		// (get) Token: 0x060023C7 RID: 9159 RVA: 0x000F0B98 File Offset: 0x000EED98
		// (set) Token: 0x060023C8 RID: 9160 RVA: 0x0000DFF5 File Offset: 0x0000C1F5
		public List<BindingAcct> BindingAccts
		{
			get
			{
				return this.list_0;
			}
			set
			{
				this.list_0 = value;
			}
		}

		// Token: 0x1700062A RID: 1578
		// (get) Token: 0x060023C9 RID: 9161 RVA: 0x000F0BB0 File Offset: 0x000EEDB0
		// (set) Token: 0x060023CA RID: 9162 RVA: 0x0000E000 File Offset: 0x0000C200
		public string Note
		{
			get
			{
				return this.note;
			}
			set
			{
				this.note = value;
			}
		}

		// Token: 0x0400113E RID: 4414
		private string id;

		// Token: 0x0400113F RID: 4415
		public string password;

		// Token: 0x04001140 RID: 4416
		public DateTime? begDT;

		// Token: 0x04001141 RID: 4417
		public DateTime? endDT;

		// Token: 0x04001142 RID: 4418
		private DateTime? lastDnldTime;

		// Token: 0x04001143 RID: 4419
		private List<BindingAcct> list_0;

		// Token: 0x04001144 RID: 4420
		private string note;
	}
}
