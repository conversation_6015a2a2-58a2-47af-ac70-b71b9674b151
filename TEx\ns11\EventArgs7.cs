﻿using System;
using System.Collections.Generic;
using System.Drawing;
using TEx;

namespace ns11
{
	// Token: 0x02000167 RID: 359
	internal sealed class EventArgs7 : EventArgs
	{
		// Token: 0x06000DA3 RID: 3491 RVA: 0x00006107 File Offset: 0x00004307
		public EventArgs7(DTValLocation dtvalLocation_1, Color? nullable_1 = null, DrawLineStyle drawLineStyle_1 = null, List<DrawSublineParam> list_1 = null)
		{
			this.dtvalLocation_0 = dtvalLocation_1;
			this.nullable_0 = nullable_1;
			this.drawLineStyle_0 = drawLineStyle_1;
			this.list_0 = list_1;
		}

		// Token: 0x1700021F RID: 543
		// (get) Token: 0x06000DA4 RID: 3492 RVA: 0x00056444 File Offset: 0x00054644
		public Color? Color
		{
			get
			{
				return this.nullable_0;
			}
		}

		// Token: 0x17000220 RID: 544
		// (get) Token: 0x06000DA5 RID: 3493 RVA: 0x0005645C File Offset: 0x0005465C
		public DTValLocation DTValLocation
		{
			get
			{
				return this.dtvalLocation_0;
			}
		}

		// Token: 0x17000221 RID: 545
		// (get) Token: 0x06000DA6 RID: 3494 RVA: 0x00056474 File Offset: 0x00054674
		public List<DrawSublineParam> ParamList
		{
			get
			{
				return this.list_0;
			}
		}

		// Token: 0x17000222 RID: 546
		// (get) Token: 0x06000DA7 RID: 3495 RVA: 0x0005648C File Offset: 0x0005468C
		public DrawLineStyle DrawLineStyle
		{
			get
			{
				return this.drawLineStyle_0;
			}
		}

		// Token: 0x040006FA RID: 1786
		private readonly Color? nullable_0;

		// Token: 0x040006FB RID: 1787
		private readonly DTValLocation dtvalLocation_0;

		// Token: 0x040006FC RID: 1788
		private readonly List<DrawSublineParam> list_0;

		// Token: 0x040006FD RID: 1789
		private readonly DrawLineStyle drawLineStyle_0;
	}
}
