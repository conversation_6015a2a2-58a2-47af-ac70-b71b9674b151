﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;
using ns2;
using ns25;
using ns28;
using ns30;
using TEx.Comn;

namespace TEx
{
	// Token: 0x020000A3 RID: 163
	internal sealed partial class BkupSyncCnfmWnd : Form
	{
		// Token: 0x14000024 RID: 36
		// (add) Token: 0x06000563 RID: 1379 RVA: 0x00029D60 File Offset: 0x00027F60
		// (remove) Token: 0x06000564 RID: 1380 RVA: 0x00029D98 File Offset: 0x00027F98
		public event Delegate27 SyncParamsConfirmed
		{
			[CompilerGenerated]
			add
			{
				Delegate27 @delegate = this.delegate27_0;
				Delegate27 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate27 value2 = (Delegate27)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate27>(ref this.delegate27_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate27 @delegate = this.delegate27_0;
				Delegate27 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate27 value2 = (Delegate27)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate27>(ref this.delegate27_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06000565 RID: 1381 RVA: 0x00029DD0 File Offset: 0x00027FD0
		protected void method_0(EventArgs23 eventArgs23_0)
		{
			Delegate27 @delegate = this.delegate27_0;
			if (@delegate != null)
			{
				@delegate(eventArgs23_0);
			}
		}

		// Token: 0x06000566 RID: 1382 RVA: 0x000045AC File Offset: 0x000027AC
		public BkupSyncCnfmWnd()
		{
			this.InitializeComponent();
		}

		// Token: 0x06000567 RID: 1383 RVA: 0x000045BC File Offset: 0x000027BC
		public BkupSyncCnfmWnd(List<SyncParam> syncPmLst) : this()
		{
			this.syncPmLst = syncPmLst;
			this.method_2();
		}

		// Token: 0x06000568 RID: 1384 RVA: 0x00029DF0 File Offset: 0x00027FF0
		private void BkupSyncCnfmWnd_Load(object sender, EventArgs e)
		{
			this.btnOK.Click += this.btnOK_Click;
			this.btnCancel.Click += this.btnCancel_Click;
			base.Deactivate += this.BkupSyncCnfmWnd_Deactivate;
			base.Activated += this.BkupSyncCnfmWnd_Activated;
		}

		// Token: 0x06000569 RID: 1385
		[DllImport("user32")]
		public static extern int SetForegroundWindow(IntPtr intptr_0);

		// Token: 0x0600056A RID: 1386 RVA: 0x000045D3 File Offset: 0x000027D3
		private void method_1(object sender, EventArgs e)
		{
			BkupSyncCnfmWnd.SetForegroundWindow(base.Handle);
		}

		// Token: 0x0600056B RID: 1387 RVA: 0x000041AE File Offset: 0x000023AE
		private void BkupSyncCnfmWnd_Deactivate(object sender, EventArgs e)
		{
		}

		// Token: 0x0600056C RID: 1388 RVA: 0x000041AE File Offset: 0x000023AE
		private void BkupSyncCnfmWnd_Activated(object sender, EventArgs e)
		{
		}

		// Token: 0x0600056D RID: 1389 RVA: 0x00029E54 File Offset: 0x00028054
		private void method_2()
		{
			foreach (SyncParam syncParam in this.syncPmLst)
			{
				switch (syncParam.enum8_0)
				{
				case Enum8.const_0:
					this.method_4(syncParam, this.chkBox_ToSv_AcctTrans, this.chkBox_ToLc_AcctTrans);
					break;
				case Enum8.const_1:
					this.method_4(syncParam, this.chkBox_ToSv_UISettings, this.chkBox_ToLc_UISettings);
					break;
				case Enum8.const_2:
					this.method_4(syncParam, this.chkBox_ToSv_SymbParams, this.chkBox_ToLc_SymbParams);
					break;
				case Enum8.const_3:
					this.method_4(syncParam, this.chkBox_ToSv_Pages, this.chkBox_ToLc_Pages);
					break;
				case Enum8.const_4:
					this.method_4(syncParam, this.chkBox_ToSv_ZiXuan, this.chkBox_ToLc_ZiXuan);
					break;
				case Enum8.const_5:
					this.method_4(syncParam, this.chkBox_ToSv_DrwObj, this.chkBox_ToLc_DrwObj);
					break;
				}
			}
			if (this.syncPmLst.Where(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_0)).Any<SyncParam>())
			{
				this.chkBox_noAskNextTime.Visible = false;
			}
		}

		// Token: 0x0600056E RID: 1390 RVA: 0x00029F8C File Offset: 0x0002818C
		private void btnOK_Click(object sender, EventArgs e)
		{
			if (this.chkBox_ToSv_AcctTrans.Enabled)
			{
				SyncParam syncParam = this.syncPmLst.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_1));
				if (!this.chkBox_ToSv_AcctTrans.Checked)
				{
					syncParam.bool_2 = true;
				}
			}
			if (this.chkBox_ToLc_AcctTrans.Enabled)
			{
				SyncParam syncParam2 = this.syncPmLst.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_2));
				if (!this.chkBox_ToLc_AcctTrans.Checked)
				{
					syncParam2.bool_2 = true;
				}
			}
			if (this.chkBox_ToSv_UISettings.Enabled)
			{
				SyncParam syncParam3 = this.syncPmLst.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_3));
				if (!this.chkBox_ToSv_UISettings.Checked)
				{
					syncParam3.bool_2 = true;
				}
			}
			if (this.chkBox_ToLc_UISettings.Enabled)
			{
				SyncParam syncParam4 = this.syncPmLst.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_4));
				if (!this.chkBox_ToLc_UISettings.Checked)
				{
					syncParam4.bool_2 = true;
				}
			}
			if (this.chkBox_ToSv_SymbParams.Enabled)
			{
				SyncParam syncParam5 = this.syncPmLst.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_5));
				if (!this.chkBox_ToSv_SymbParams.Checked)
				{
					syncParam5.bool_2 = true;
				}
			}
			if (this.chkBox_ToLc_SymbParams.Enabled)
			{
				SyncParam syncParam6 = this.syncPmLst.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_6));
				if (!this.chkBox_ToLc_SymbParams.Checked)
				{
					syncParam6.bool_2 = true;
				}
			}
			if (this.chkBox_ToSv_Pages.Enabled)
			{
				SyncParam syncParam7 = this.syncPmLst.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_7));
				SyncParam syncParam8 = this.syncPmLst.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_8));
				if (!this.chkBox_ToSv_Pages.Checked)
				{
					syncParam7.bool_2 = true;
					syncParam8.bool_2 = true;
				}
			}
			if (this.chkBox_ToLc_Pages.Enabled)
			{
				SyncParam syncParam9 = this.syncPmLst.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_9));
				SyncParam syncParam10 = this.syncPmLst.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_10));
				if (!this.chkBox_ToLc_Pages.Checked)
				{
					syncParam9.bool_2 = true;
					syncParam10.bool_2 = true;
				}
			}
			if (this.chkBox_ToSv_ZiXuan.Enabled)
			{
				SyncParam syncParam11 = this.syncPmLst.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_11));
				if (!this.chkBox_ToSv_ZiXuan.Checked)
				{
					syncParam11.bool_2 = true;
				}
			}
			if (this.chkBox_ToLc_ZiXuan.Enabled)
			{
				SyncParam syncParam12 = this.syncPmLst.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_12));
				if (!this.chkBox_ToLc_ZiXuan.Checked)
				{
					syncParam12.bool_2 = true;
				}
			}
			if (this.chkBox_ToSv_DrwObj.Enabled)
			{
				SyncParam syncParam13 = this.syncPmLst.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_13));
				if (!this.chkBox_ToSv_DrwObj.Checked)
				{
					syncParam13.bool_2 = true;
				}
			}
			if (this.chkBox_ToLc_DrwObj.Enabled)
			{
				SyncParam syncParam14 = this.syncPmLst.Single(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_14));
				if (!this.chkBox_ToLc_DrwObj.Checked)
				{
					syncParam14.bool_2 = true;
				}
			}
			bool bool_ = false;
			bool bool_2 = false;
			if (this.chkBox_noAskNextTime.Checked)
			{
				if (this.syncPmLst.Where(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_15)).Any<SyncParam>())
				{
					bool_ = true;
				}
				if (this.syncPmLst.Where(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_16)).Any<SyncParam>())
				{
					bool_2 = true;
				}
			}
			EventArgs23 eventArgs23_ = new EventArgs23(this.syncPmLst, bool_, bool_2);
			this.method_3(eventArgs23_);
			this.method_0(eventArgs23_);
			base.DialogResult = DialogResult.OK;
			base.Close();
		}

		// Token: 0x0600056F RID: 1391 RVA: 0x0002A44C File Offset: 0x0002864C
		private void method_3(EventArgs23 eventArgs23_0)
		{
			IEnumerable<SyncParam> enumerable = eventArgs23_0.SyncParamList.Where(new Func<SyncParam, bool>(BkupSyncCnfmWnd.<>c.<>9.method_17));
			if (enumerable.Any<SyncParam>())
			{
				using (IEnumerator<SyncParam> enumerator = enumerable.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						BkupSyncCnfmWnd.Class62 @class = new BkupSyncCnfmWnd.Class62();
						@class.syncParam_0 = enumerator.Current;
						switch (@class.syncParam_0.enum8_0)
						{
						case Enum8.const_0:
							TApp.ReqSyncFileSpms.RemoveAll(new Predicate<SrvParam>(@class.method_0));
							break;
						case Enum8.const_1:
							TApp.ReqSyncFileSpms.RemoveAll(new Predicate<SrvParam>(@class.method_1));
							break;
						case Enum8.const_2:
							TApp.ReqSyncFileSpms.RemoveAll(new Predicate<SrvParam>(@class.method_2));
							break;
						case Enum8.const_3:
							TApp.ReqSyncFileSpms.RemoveAll(new Predicate<SrvParam>(@class.method_3));
							break;
						case Enum8.const_4:
							TApp.ReqSyncFileSpms.RemoveAll(new Predicate<SrvParam>(@class.method_4));
							break;
						case Enum8.const_5:
							TApp.ReqSyncFileSpms.RemoveAll(new Predicate<SrvParam>(@class.method_5));
							break;
						}
					}
				}
			}
			if (eventArgs23_0.IfNoConfirmCopyToLocal)
			{
				Base.UI.Form.BackupSyncAutoOverwritingLocalFile = true;
			}
			if (eventArgs23_0.IfNoConfirmCopytoSrvNewer)
			{
				Base.UI.Form.BackupSyncConflictTreatmt = new BackupSyncConflictTreatmt?(BackupSyncConflictTreatmt.AutoSyncNoPrompt);
			}
		}

		// Token: 0x06000570 RID: 1392 RVA: 0x000045E3 File Offset: 0x000027E3
		private void btnCancel_Click(object sender, EventArgs e)
		{
			base.DialogResult = DialogResult.Cancel;
			base.Close();
		}

		// Token: 0x06000571 RID: 1393 RVA: 0x000045F4 File Offset: 0x000027F4
		private void method_4(SyncParam syncParam_0, CheckBox checkBox_0, CheckBox checkBox_1)
		{
			if (syncParam_0.bool_0)
			{
				checkBox_0.Enabled = true;
				checkBox_0.Checked = true;
			}
			else
			{
				checkBox_1.Enabled = true;
				checkBox_1.Checked = true;
			}
		}

		// Token: 0x1700010A RID: 266
		// (get) Token: 0x06000572 RID: 1394 RVA: 0x0002A5C8 File Offset: 0x000287C8
		// (set) Token: 0x06000573 RID: 1395 RVA: 0x0000461E File Offset: 0x0000281E
		public List<SyncParam> SyncPmLst
		{
			get
			{
				return this.syncPmLst;
			}
			set
			{
				if (this.syncPmLst == null || this.syncPmLst != value)
				{
					this.syncPmLst = value;
					this.method_2();
				}
			}
		}

		// Token: 0x06000574 RID: 1396 RVA: 0x00004640 File Offset: 0x00002840
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000256 RID: 598
		[CompilerGenerated]
		private Delegate27 delegate27_0;

		// Token: 0x04000257 RID: 599
		private List<SyncParam> syncPmLst;

		// Token: 0x04000258 RID: 600
		private IContainer icontainer_0;

		// Token: 0x020000A5 RID: 165
		[CompilerGenerated]
		private sealed class Class62
		{
			// Token: 0x0600058B RID: 1419 RVA: 0x0002B9E4 File Offset: 0x00029BE4
			internal bool method_0(SrvParam srvParam_0)
			{
				bool result;
				if (!srvParam_0.FileName.Equals("accts.dat") && !srvParam_0.FileName.Equals("accts.dat") && !srvParam_0.Note.Contains("Trading"))
				{
					result = false;
				}
				else
				{
					result = (srvParam_0.Value != null == this.syncParam_0.bool_0);
				}
				return result;
			}

			// Token: 0x0600058C RID: 1420 RVA: 0x0002BA48 File Offset: 0x00029C48
			internal bool method_1(SrvParam srvParam_0)
			{
				bool result;
				if (srvParam_0.FileName.Equals("fmui.cfg"))
				{
					result = (srvParam_0.Value != null == this.syncParam_0.bool_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0600058D RID: 1421 RVA: 0x0002BA88 File Offset: 0x00029C88
			internal bool method_2(SrvParam srvParam_0)
			{
				bool result;
				if (!srvParam_0.FileName.Equals("acsmbls.dat") && !srvParam_0.FileName.Equals("tsmbls.dat"))
				{
					result = false;
				}
				else
				{
					result = (srvParam_0.Value != null == this.syncParam_0.bool_0);
				}
				return result;
			}

			// Token: 0x0600058E RID: 1422 RVA: 0x0002BAD8 File Offset: 0x00029CD8
			internal bool method_3(SrvParam srvParam_0)
			{
				bool result;
				if (!srvParam_0.FileName.Equals("chtpg.dat") && !srvParam_0.FileName.Equals("indusr.dat"))
				{
					result = false;
				}
				else
				{
					result = (srvParam_0.Value != null == this.syncParam_0.bool_0);
				}
				return result;
			}

			// Token: 0x0600058F RID: 1423 RVA: 0x0002BB28 File Offset: 0x00029D28
			internal bool method_4(SrvParam srvParam_0)
			{
				bool result;
				if (srvParam_0.FileName.Equals("smktsymb.dat"))
				{
					result = (srvParam_0.Value != null == this.syncParam_0.bool_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x06000590 RID: 1424 RVA: 0x0002BB68 File Offset: 0x00029D68
			internal bool method_5(SrvParam srvParam_0)
			{
				bool result;
				if (srvParam_0.FileName.Equals("dwobjs.dat"))
				{
					result = (srvParam_0.Value != null == this.syncParam_0.bool_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400028B RID: 651
			public SyncParam syncParam_0;
		}
	}
}
