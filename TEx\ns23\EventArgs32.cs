﻿using System;
using System.Runtime.CompilerServices;
using ns19;
using TEx.SIndicator;

namespace ns23
{
	// Token: 0x02000303 RID: 771
	internal sealed class EventArgs32 : EventArgs
	{
		// Token: 0x170005C2 RID: 1474
		// (get) Token: 0x06002155 RID: 8533 RVA: 0x000E44C4 File Offset: 0x000E26C4
		// (set) Token: 0x06002156 RID: 8534 RVA: 0x0000D64C File Offset: 0x0000B84C
		public FormIndEditer IndEditer { get; private set; }

		// Token: 0x170005C3 RID: 1475
		// (get) Token: 0x06002157 RID: 8535 RVA: 0x000E44DC File Offset: 0x000E26DC
		// (set) Token: 0x06002158 RID: 8536 RVA: 0x0000D657 File Offset: 0x0000B857
		public Enum25 DoType { get; private set; }

		// Token: 0x06002159 RID: 8537 RVA: 0x0000D662 File Offset: 0x0000B862
		public EventArgs32(FormIndEditer formIndEditer_1, Enum25 enum25_1)
		{
			this.IndEditer = formIndEditer_1;
			this.DoType = enum25_1;
		}

		// Token: 0x04001037 RID: 4151
		[CompilerGenerated]
		private FormIndEditer formIndEditer_0;

		// Token: 0x04001038 RID: 4152
		[CompilerGenerated]
		private Enum25 enum25_0;
	}
}
