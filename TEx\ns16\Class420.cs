﻿using System;
using ns14;
using ns30;
using ns31;
using ns9;
using TEx.Inds;
using TEx.SIndicator;

namespace ns16
{
	// Token: 0x02000334 RID: 820
	internal sealed class Class420 : Class412
	{
		// Token: 0x0600228B RID: 8843 RVA: 0x0000D8A3 File Offset: 0x0000BAA3
		public Class420(HToken htoken_1, Class408 class408_2, Class408 class408_3) : base(htoken_1, class408_2, class408_3)
		{
		}

		// Token: 0x0600228C RID: 8844 RVA: 0x000EB66C File Offset: 0x000E986C
		public static Class408 smethod_0(Tokenes tokenes_0)
		{
			HToken htoken = tokenes_0.Current;
			Class408 @class = Class410.smethod_0(tokenes_0);
			if (@class == null)
			{
				throw new Exception(htoken.method_0("无法解析。"));
			}
			Class408 result;
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
			{
				result = @class;
			}
			else
			{
				tokenes_0.method_1();
				htoken = tokenes_0.Current;
				if (htoken.Symbol.HSymbolType == Enum26.const_8 | htoken.Symbol.HSymbolType == Enum26.const_9)
				{
					result = Class420.smethod_1(@class, tokenes_0);
				}
				else
				{
					tokenes_0.method_2();
					result = @class;
				}
			}
			return result;
		}

		// Token: 0x0600228D RID: 8845 RVA: 0x000EB6F8 File Offset: 0x000E98F8
		private static Class408 smethod_1(Class408 class408_2, Tokenes tokenes_0)
		{
			HToken htoken = tokenes_0.Current;
			if (!(htoken.Symbol.HSymbolType == Enum26.const_8 | htoken.Symbol.HSymbolType == Enum26.const_9))
			{
				throw new Exception(tokenes_0.Current.method_0("不是乘号或者除号。"));
			}
			HToken htoken2 = htoken;
			tokenes_0.method_1();
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
			{
				throw new Exception(htoken2.method_0("没有找到操作数"));
			}
			Class408 @class = Class410.smethod_0(tokenes_0);
			if (@class == null)
			{
				throw new Exception(htoken2.method_0("没有找到操作数"));
			}
			Class420 class2 = new Class420(htoken2, class408_2, @class);
			tokenes_0.method_1();
			Class408 result;
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_8 | tokenes_0.Current.Symbol.HSymbolType == Enum26.const_9)
			{
				result = Class420.smethod_1(class2, tokenes_0);
			}
			else if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
			{
				result = class2;
			}
			else
			{
				tokenes_0.method_2();
				result = class2;
			}
			return result;
		}

		// Token: 0x0600228E RID: 8846 RVA: 0x000EB7FC File Offset: 0x000E99FC
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			return this.vmethod_2(this.Left.vmethod_1(parserEnvironment_0), this.Right.vmethod_1(parserEnvironment_0));
		}

		// Token: 0x0600228F RID: 8847 RVA: 0x000EB82C File Offset: 0x000E9A2C
		protected override double vmethod_3(double double_0, double double_1)
		{
			Enum26 hsymbolType = this.Token.Symbol.HSymbolType;
			double result;
			if (hsymbolType != Enum26.const_8)
			{
				if (hsymbolType != Enum26.const_9)
				{
					throw new Exception(this.Token.method_0("不是乘号或者除号"));
				}
				result = double_0 / double_1;
			}
			else
			{
				result = double_0 * double_1;
			}
			return result;
		}

		// Token: 0x06002290 RID: 8848 RVA: 0x000EB878 File Offset: 0x000E9A78
		protected override DataArray vmethod_4(DataArray dataArray_0, DataArray dataArray_1)
		{
			Enum26 hsymbolType = this.Token.Symbol.HSymbolType;
			DataArray result;
			if (hsymbolType != Enum26.const_8)
			{
				if (hsymbolType != Enum26.const_9)
				{
					throw new Exception(this.Token.method_0("不是乘号或者除号"));
				}
				result = dataArray_0 / dataArray_1;
			}
			else
			{
				result = dataArray_0 * dataArray_1;
			}
			return result;
		}

		// Token: 0x06002291 RID: 8849 RVA: 0x000E8564 File Offset: 0x000E6764
		public override string vmethod_0()
		{
			return base.vmethod_0();
		}

		// Token: 0x170005E8 RID: 1512
		// (get) Token: 0x06002292 RID: 8850 RVA: 0x000E7D34 File Offset: 0x000E5F34
		// (set) Token: 0x06002293 RID: 8851 RVA: 0x0000D8AE File Offset: 0x0000BAAE
		public override Class408 Left
		{
			get
			{
				return base.Left;
			}
			protected set
			{
				base.Left = value;
			}
		}

		// Token: 0x170005E9 RID: 1513
		// (get) Token: 0x06002294 RID: 8852 RVA: 0x000E7D4C File Offset: 0x000E5F4C
		// (set) Token: 0x06002295 RID: 8853 RVA: 0x0000D8B9 File Offset: 0x0000BAB9
		public override Class408 Right
		{
			get
			{
				return base.Right;
			}
			protected set
			{
				base.Right = value;
			}
		}

		// Token: 0x170005EA RID: 1514
		// (get) Token: 0x06002296 RID: 8854 RVA: 0x000E7D64 File Offset: 0x000E5F64
		// (set) Token: 0x06002297 RID: 8855 RVA: 0x0000D8C4 File Offset: 0x0000BAC4
		public override HToken Token
		{
			get
			{
				return base.Token;
			}
			protected set
			{
				base.Token = value;
			}
		}

		// Token: 0x06002298 RID: 8856 RVA: 0x000E7D1C File Offset: 0x000E5F1C
		public string ToString()
		{
			return base.ToString();
		}
	}
}
