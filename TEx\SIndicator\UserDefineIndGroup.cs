﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

namespace TEx.SIndicator
{
	// Token: 0x02000337 RID: 823
	public sealed class UserDefineIndGroup
	{
		// Token: 0x170005ED RID: 1517
		// (get) Token: 0x060022A9 RID: 8873 RVA: 0x000EC40C File Offset: 0x000EA60C
		// (set) Token: 0x060022AA RID: 8874 RVA: 0x0000DAB7 File Offset: 0x0000BCB7
		public string Group { get; private set; }

		// Token: 0x170005EE RID: 1518
		// (get) Token: 0x060022AB RID: 8875 RVA: 0x000EC424 File Offset: 0x000EA624
		// (set) Token: 0x060022AC RID: 8876 RVA: 0x0000DAC2 File Offset: 0x0000BCC2
		public List<UserDefineIndScript> UDSList { get; private set; }

		// Token: 0x060022AD RID: 8877 RVA: 0x0000DACD File Offset: 0x0000BCCD
		public UserDefineIndGroup(string group, List<UserDefineIndScript> udsList)
		{
			this.Group = group;
			this.UDSList = udsList;
		}

		// Token: 0x040010D6 RID: 4310
		[CompilerGenerated]
		private string string_0;

		// Token: 0x040010D7 RID: 4311
		[CompilerGenerated]
		private List<UserDefineIndScript> list_0;
	}
}
