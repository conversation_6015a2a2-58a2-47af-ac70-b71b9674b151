﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using ns17;
using ns18;
using TEx;
using TEx.Util;

namespace ns22
{
	// Token: 0x02000062 RID: 98
	internal static class Class46
	{
		// Token: 0x0600034F RID: 847 RVA: 0x0001F804 File Offset: 0x0001DA04
		public static void smethod_0(string string_1, string string_2)
		{
			StreamWriter streamWriter = null;
			try
			{
				string text = TApp.StartedUp ? (TApp.UserAcctFolder + "\\Log\\") : ".\\Log\\";
				Utility.CreateDir(text);
				streamWriter = new StreamWriter(text + string_1, true, Encoding.UTF8);
				string value = string.Format("{0:yyyy-MM-dd HH:mm:ss}", DateTime.Now) + ", " + string_2;
				streamWriter.WriteLine(value);
			}
			catch
			{
			}
			if (streamWriter != null)
			{
				try
				{
					streamWriter.Close();
				}
				catch
				{
				}
			}
		}

		// Token: 0x06000350 RID: 848 RVA: 0x000036DD File Offset: 0x000018DD
		public static void smethod_1(string string_1, string string_2, string string_3)
		{
			Class46.smethod_0("TEx.log", string.Concat(new string[]
			{
				string_1,
				", ",
				string_2,
				", ",
				string_3
			}));
		}

		// Token: 0x06000351 RID: 849 RVA: 0x00003712 File Offset: 0x00001912
		public static void smethod_2(string string_1)
		{
			Class46.smethod_0("TEx.log", Class21.Info + ", " + string_1);
		}

		// Token: 0x06000352 RID: 850 RVA: 0x00003730 File Offset: 0x00001930
		public static void smethod_3(string string_1, string string_2)
		{
			Class46.smethod_0("TEx.log", string.Concat(new string[]
			{
				Class21.Info,
				", ",
				string_1,
				", ",
				string_2
			}));
		}

		// Token: 0x06000353 RID: 851 RVA: 0x0001F8A4 File Offset: 0x0001DAA4
		public static void smethod_4(Exception exception_0, bool bool_0 = true, string string_1 = null)
		{
			string text = exception_0.Message + "  " + exception_0.StackTrace;
			if (bool_0 && exception_0.InnerException != null)
			{
				text = text + " (" + exception_0.InnerException.Message + ")";
			}
			if (!string.IsNullOrEmpty(string_1))
			{
				text = text + "  " + string_1;
			}
			Class46.smethod_0("TEx.log", string.Concat(new string[]
			{
				Class21.Error,
				", ",
				Class22.ExceptionOccurred,
				", ",
				text
			}));
		}

		// Token: 0x06000354 RID: 852 RVA: 0x0001F940 File Offset: 0x0001DB40
		public static bool smethod_5(int int_0)
		{
			return Class46.smethod_6(Class46.LogFilePath, int_0);
		}

		// Token: 0x06000355 RID: 853 RVA: 0x0001F95C File Offset: 0x0001DB5C
		public static bool smethod_6(string string_1, int int_0)
		{
			bool result = false;
			FileInfo fileInfo = new FileInfo(Class46.LogFilePath);
			if (fileInfo.Exists && (double)fileInfo.Length / 1024.0 > (double)int_0)
			{
				List<string> list = new List<string>(File.ReadAllLines(fileInfo.FullName));
				int num = Convert.ToInt32(Math.Round((double)list.Count / 2.0));
				if (num > 0)
				{
					list.RemoveRange(0, num);
					try
					{
						File.WriteAllLines(fileInfo.FullName, list.ToArray());
						result = true;
					}
					catch
					{
					}
				}
			}
			return result;
		}

		// Token: 0x170000C5 RID: 197
		// (get) Token: 0x06000356 RID: 854 RVA: 0x0001F9FC File Offset: 0x0001DBFC
		public static string LogFilePath
		{
			get
			{
				return TApp.UserAcctFolder + "\\Log\\TEx.log";
			}
		}

		// Token: 0x0400012C RID: 300
		private const string string_0 = "TEx.log";
	}
}
