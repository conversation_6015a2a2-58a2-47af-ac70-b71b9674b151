﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns10;
using ns15;
using ns19;
using ns22;
using ns23;
using ns24;
using ns27;
using ns28;
using ns29;
using ns3;
using ns9;
using TEx.Chart;
using TEx.Comn;
using TEx.Inds;
using TEx.SIndicator;
using TEx.Util;

namespace TEx
{
	// Token: 0x0200017C RID: 380
	internal abstract class ChartKLine : ChartBase
	{
		// Token: 0x06000E51 RID: 3665 RVA: 0x000065DA File Offset: 0x000047DA
		public ChartKLine(ChtCtrl_KLine dChtCtrl_KLine, SplitterPanel panel) : base(dChtCtrl_KLine, panel)
		{
		}

		// Token: 0x1700023E RID: 574
		// (get) Token: 0x06000E52 RID: 3666 RVA: 0x0005B0E0 File Offset: 0x000592E0
		public List<IndEx> IndExList
		{
			get
			{
				return this.list_0.Where(new Func<Indicator, bool>(ChartKLine.<>c.<>9.method_0)).Select(new Func<Indicator, IndEx>(ChartKLine.<>c.<>9.method_1)).ToList<IndEx>();
			}
		}

		// Token: 0x1700023F RID: 575
		// (get) Token: 0x06000E53 RID: 3667 RVA: 0x0005B144 File Offset: 0x00059344
		public List<UserDefineIndScript> UDSList
		{
			get
			{
				return this.IndExList.Select(new Func<IndEx, UserDefineIndScript>(ChartKLine.<>c.<>9.method_2)).ToList<UserDefineIndScript>();
			}
		}

		// Token: 0x17000240 RID: 576
		// (get) Token: 0x06000E54 RID: 3668 RVA: 0x0005B184 File Offset: 0x00059384
		public DataProvider DP
		{
			get
			{
				return new DataProvider(base.HisDataPeriodSet.PeriodHisDataList, base.Symbol);
			}
		}

		// Token: 0x06000E55 RID: 3669 RVA: 0x0005B1AC File Offset: 0x000593AC
		protected void method_73(ContextMenuStrip contextMenuStrip_0)
		{
			string text = "指标管理";
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = text;
			toolStripMenuItem.Click += this.method_78;
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06000E56 RID: 3670 RVA: 0x0005B1F0 File Offset: 0x000593F0
		protected void method_74(ContextMenuStrip contextMenuStrip_0)
		{
			string text = "删除主图指标";
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			toolStripMenuItem.Text = text;
			toolStripMenuItem.Click += this.method_75;
		}

		// Token: 0x06000E57 RID: 3671 RVA: 0x000065EC File Offset: 0x000047EC
		private void method_75(object sender, EventArgs e)
		{
			FormDelInd formDelInd = new FormDelInd();
			formDelInd.method_0(this.ChtCtrl_KLine.method_72());
			formDelInd.ShowDialog();
		}

		// Token: 0x06000E58 RID: 3672 RVA: 0x0005B234 File Offset: 0x00059434
		protected void method_76(ContextMenuStrip contextMenuStrip_0)
		{
			string text = "指标参数";
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			toolStripMenuItem.Text = text;
			toolStripMenuItem.Click += this.method_77;
		}

		// Token: 0x06000E59 RID: 3673 RVA: 0x0000660C File Offset: 0x0000480C
		private void method_77(object sender, EventArgs e)
		{
			FormSetInd formSetInd = new FormSetInd();
			formSetInd.method_2(this.ChtCtrl_KLine.method_71());
			formSetInd.ShowDialog();
		}

		// Token: 0x06000E5A RID: 3674 RVA: 0x0000662C File Offset: 0x0000482C
		private void method_78(object sender, EventArgs e)
		{
			FormIndMgr formIndMgr = new FormIndMgr();
			formIndMgr.ShownIndEditer += this.method_79;
			formIndMgr.ShowDialog();
			formIndMgr.ShownIndEditer -= this.method_79;
		}

		// Token: 0x06000E5B RID: 3675 RVA: 0x0005B278 File Offset: 0x00059478
		private void method_79(object sender, EventArgs e)
		{
			EventArgs32 eventArgs = e as EventArgs32;
			if (eventArgs != null)
			{
				FormIndEditer indEditer = eventArgs.IndEditer;
				if (eventArgs.DoType == Enum25.flag_0)
				{
					indEditer.ReadyToLoadNewIndToChart += this.method_81;
				}
				else if (eventArgs.DoType == Enum25.flag_1)
				{
					indEditer.ReadyToReloadModifiedIndToChart += this.method_83;
				}
			}
		}

		// Token: 0x06000E5C RID: 3676 RVA: 0x0005B2D4 File Offset: 0x000594D4
		private IndEx method_80(string string_10)
		{
			ChartKLine.Class212 @class = new ChartKLine.Class212();
			@class.string_0 = string_10;
			IndEx result;
			if (!this.IndExList.Any(new Func<IndEx, bool>(@class.method_0)))
			{
				UserDefineIndScript userDefineIndScript = UserDefineFileMgr.UDSListChecked.SingleOrDefault(new Func<UserDefineIndScript, bool>(@class.method_1));
				if (userDefineIndScript == null)
				{
					result = null;
				}
				else
				{
					IndEx indEx = this.method_86(userDefineIndScript);
					if (this.vmethod_29(indEx, this, null))
					{
						result = indEx;
					}
					else
					{
						result = null;
					}
				}
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06000E5D RID: 3677 RVA: 0x0005B348 File Offset: 0x00059548
		protected void method_81(object sender, EventArgs e)
		{
			EventArgs30 eventArgs = e as EventArgs30;
			if (eventArgs == null)
			{
				MessageBox.Show("加载指标错误！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Hand);
			}
			else
			{
				this.method_82(eventArgs.UDS);
			}
		}

		// Token: 0x06000E5E RID: 3678 RVA: 0x0005B384 File Offset: 0x00059584
		public void method_82(UserDefineIndScript userDefineIndScript_0)
		{
			ChartKLine.Class213 @class = new ChartKLine.Class213();
			@class.userDefineIndScript_0 = userDefineIndScript_0;
			if (this.list_0.SingleOrDefault(new Func<Indicator, bool>(@class.method_0)) == null)
			{
				IndEx indicator_ = this.method_86(@class.userDefineIndScript_0);
				this.vmethod_29(indicator_, this, null);
			}
		}

		// Token: 0x06000E5F RID: 3679 RVA: 0x0005B3D0 File Offset: 0x000595D0
		protected void method_83(object sender, EventArgs e)
		{
			EventArgs30 eventArgs = e as EventArgs30;
			if (eventArgs == null)
			{
				MessageBox.Show("加载指标错误。");
			}
			else
			{
				UserDefineIndScript uds = eventArgs.UDS;
				this.method_84(uds);
			}
		}

		// Token: 0x06000E60 RID: 3680 RVA: 0x0005B404 File Offset: 0x00059604
		public void method_84(UserDefineIndScript userDefineIndScript_0)
		{
			ChartKLine.Class214 @class = new ChartKLine.Class214();
			@class.userDefineIndScript_0 = userDefineIndScript_0;
			Indicator indicator = this.list_0.FirstOrDefault(new Func<Indicator, bool>(@class.method_0));
			if (indicator != null)
			{
				IndEx indEx = indicator as IndEx;
				if (indEx != null)
				{
					indEx.RemoveFromChart();
				}
				indEx = this.method_86(@class.userDefineIndScript_0);
				this.vmethod_29(indEx, this, null);
			}
			else if (MessageBox.Show("是否将此指标加载到图形中?", "提示", MessageBoxButtons.OKCancel) == DialogResult.OK)
			{
				IndEx indicator_ = this.method_86(@class.userDefineIndScript_0);
				this.vmethod_29(indicator_, this, null);
			}
		}

		// Token: 0x06000E61 RID: 3681 RVA: 0x0005B490 File Offset: 0x00059690
		protected void method_85(object sender, EventArgs e)
		{
			ChartKLine.Class215 @class = new ChartKLine.Class215();
			@class.eventArgs30_0 = (e as EventArgs30);
			if (@class.eventArgs30_0 == null)
			{
				MessageBox.Show("加载指标错误！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Hand);
			}
			else if (this.IndExList.Any(new Func<IndEx, bool>(@class.method_0)))
			{
				MessageBox.Show("此指标已经存在，请重命名指标。", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
			else
			{
				IndEx indEx = this.method_86(@class.eventArgs30_0.UDS);
				if (indEx.IsMainChartInd)
				{
					if (indEx.InitInd(this))
					{
						this.method_123(indEx, this);
					}
				}
				else
				{
					this.ChtCtrl_KLine.method_73(indEx);
				}
			}
		}

		// Token: 0x06000E62 RID: 3682 RVA: 0x0005B538 File Offset: 0x00059738
		private IndEx method_86(UserDefineIndScript userDefineIndScript_0)
		{
			return IndEx.smethod_0(new UserDefineInd(this.DP, userDefineIndScript_0));
		}

		// Token: 0x06000E63 RID: 3683 RVA: 0x0000665F File Offset: 0x0000485F
		private void method_87(object sender, EventArgs e)
		{
			this.method_89("", Enum25.flag_0, null);
		}

		// Token: 0x06000E64 RID: 3684 RVA: 0x0005B55C File Offset: 0x0005975C
		private void method_88(object sender, EventArgs e)
		{
			IndEx indEx = this.ChtCtrl_KLine.SelectedInd as IndEx;
			if (indEx != null)
			{
				this.method_89("", Enum25.flag_1, indEx.UDInd.UDS);
			}
		}

		// Token: 0x06000E65 RID: 3685 RVA: 0x0005B598 File Offset: 0x00059798
		private void method_89(string string_10, Enum25 enum25_0, UserDefineIndScript userDefineIndScript_0)
		{
			switch (enum25_0)
			{
			case Enum25.flag_0:
			{
				FormIndEditer formIndEditer = new FormIndEditer();
				formIndEditer.ReadyToLoadNewIndToChart += this.method_85;
				formIndEditer.Group = string_10;
				formIndEditer.ShowDialog();
				formIndEditer.ReadyToLoadNewIndToChart -= this.method_85;
				break;
			}
			case Enum25.flag_1:
			{
				FormIndEditer formIndEditer2 = new FormIndEditer();
				formIndEditer2.ReadyToReloadModifiedIndToChart += this.method_83;
				if (formIndEditer2.method_8(userDefineIndScript_0))
				{
					formIndEditer2.ShowDialog();
				}
				formIndEditer2.ReadyToReloadModifiedIndToChart -= this.method_83;
				break;
			}
			}
		}

		// Token: 0x06000E66 RID: 3686 RVA: 0x0005B634 File Offset: 0x00059834
		protected override void vmethod_0()
		{
			base.vmethod_0();
			base.ZedGraphControl.ContextMenuBuilder += this.vmethod_27;
			base.ZedGraphControl.MouseClick += this.vmethod_31;
			base.ZedGraphControl.MouseDownEvent += this.method_142;
			base.ZedGraphControl.MouseUpEvent += this.method_141;
		}

		// Token: 0x06000E67 RID: 3687 RVA: 0x0005B6A8 File Offset: 0x000598A8
		protected virtual void vmethod_24()
		{
			foreach (Indicator indicator in this.IndList)
			{
				indicator.InitItem();
			}
		}

		// Token: 0x06000E68 RID: 3688 RVA: 0x0005B6FC File Offset: 0x000598FC
		public override void vmethod_3()
		{
			base.vmethod_3();
			GraphPane graphPane = base.GraphPane;
			double num = Convert.ToDouble(this.ChtCtrl_KLine.MaxSticksPerChart);
			double num2 = num / 12.0;
			graphPane.XAxis.Scale.Max = num + num2;
			if (base.IsXAxisVisible)
			{
				this.vmethod_26();
			}
		}

		// Token: 0x06000E69 RID: 3689 RVA: 0x0005B754 File Offset: 0x00059954
		public override void ApplyTheme(ChartTheme theme)
		{
			foreach (Indicator indicator in this.IndList)
			{
				if (indicator != null)
				{
					indicator.ApplyTheme(theme);
				}
			}
			if (base.SupportHighLowMark && base.GraphPane != null && base.GraphPane.GraphObjList != null)
			{
				foreach (GraphObj graphObj in base.GraphPane.GraphObjList.Where(new Func<GraphObj, bool>(ChartKLine.<>c.<>9.method_3)))
				{
					((HiLowMarkTextObj)graphObj).method_2();
				}
			}
			this.method_147();
			base.ApplyTheme(theme);
		}

		// Token: 0x06000E6A RID: 3690 RVA: 0x0005B840 File Offset: 0x00059A40
		public override void vmethod_4(int int_0)
		{
			int num = base.method_4(int_0);
			foreach (Indicator indicator in this.IndList)
			{
				bool isSelected;
				if (isSelected = indicator.IsSelected)
				{
					indicator.IsSelected = false;
				}
				indicator.StartIdx = num;
				indicator.EndIdx = int_0;
				for (int i = num; i <= int_0; i++)
				{
					indicator.AddNewItem(i);
				}
				if (isSelected)
				{
					indicator.IsSelected = true;
				}
			}
			base.vmethod_4(int_0);
		}

		// Token: 0x06000E6B RID: 3691 RVA: 0x0005B8E0 File Offset: 0x00059AE0
		public override void vmethod_6(HDTick hdtick_0)
		{
			foreach (Indicator indicator in this.IndList)
			{
				int itemIdx = this.ChtCtrl_KLine.IndexOfLastItemShown + 1;
				indicator.AddNewItem(itemIdx, hdtick_0);
			}
			if (base.IsXAxisVisible)
			{
				base.GraphPane.XAxis.Scale.BaseTic = (double)this.method_95();
			}
			base.vmethod_6(hdtick_0);
		}

		// Token: 0x06000E6C RID: 3692 RVA: 0x0005B970 File Offset: 0x00059B70
		public override void vmethod_5(HisData hisData_0)
		{
			int num = this.ChtCtrl_KLine.IndexOfLastItemShown + 1;
			if (num >= base.HisDataPeriodSet.PeriodHisDataList.Count)
			{
				Class182.smethod_0(new ArgumentOutOfRangeException("数据溢出,位置为：" + num));
			}
			else
			{
				foreach (Indicator indicator in this.IndList)
				{
					indicator.AddNewItem(num, hisData_0);
				}
			}
			if (base.IsXAxisVisible)
			{
				base.GraphPane.XAxis.Scale.BaseTic = (double)this.method_95();
			}
			base.vmethod_5(hisData_0);
		}

		// Token: 0x06000E6D RID: 3693 RVA: 0x0005BA30 File Offset: 0x00059C30
		public override void vmethod_7(int int_0, HisData hisData_0, bool bool_4)
		{
			HisData hisData = hisData_0.Clone();
			if (bool_4)
			{
				hisData = this.method_90(int_0, hisData_0);
				if (hisData == null)
				{
					return;
				}
				using (List<Indicator>.Enumerator enumerator = this.IndList.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						Indicator indicator = enumerator.Current;
						indicator.UpdateLastItem(int_0, hisData);
					}
					goto IL_84;
				}
			}
			foreach (Indicator indicator2 in this.IndList)
			{
				indicator2.UpdateLastItem(int_0, hisData_0);
			}
			IL_84:
			base.vmethod_7(int_0, hisData_0, bool_4);
		}

		// Token: 0x06000E6E RID: 3694 RVA: 0x0005BAE8 File Offset: 0x00059CE8
		public HisData method_90(int int_0, HisData hisData_0)
		{
			HisData hisData = null;
			bool flag = false;
			List<HisData> list = this.method_91(int_0, hisData_0.Date, ref flag);
			if (list != null && list.Any<HisData>())
			{
				StockRestorationMethod? stockRestorationMethod = Base.UI.Form.StockRestorationMethod;
				if (stockRestorationMethod.GetValueOrDefault() == StockRestorationMethod.Later & stockRestorationMethod != null)
				{
					hisData = hisData_0;
				}
				else
				{
					bool flag2 = false;
					if (!flag && base.Symbol.IsStock && Base.UI.Form.StockRestorationMethod != null)
					{
						stockRestorationMethod = Base.UI.Form.StockRestorationMethod;
						if (!(stockRestorationMethod.GetValueOrDefault() == StockRestorationMethod.None & stockRestorationMethod != null) && base.SymbDataSet.method_102(list.First<HisData>().Date, hisData_0.Date) != null)
						{
							flag2 = true;
						}
					}
					hisData = list.First<HisData>().Clone();
					if (flag2)
					{
						hisData = base.SymbDataSet.method_91(hisData);
					}
					double num = hisData.High;
					double num2 = hisData.Low;
					foreach (HisData hisData2 in list)
					{
						HisData hisData3 = hisData2;
						if (flag2)
						{
							hisData3 = base.SymbDataSet.method_91(hisData2);
						}
						if (hisData3.High > num)
						{
							num = hisData3.High;
						}
						if (hisData3.Low < num2)
						{
							num2 = hisData3.Low;
						}
					}
					hisData.High = num;
					hisData.Low = num2;
					hisData.Date = hisData_0.Date;
					hisData.Close = hisData_0.Close;
					hisData = base.SymbDataSet.method_91(hisData);
				}
			}
			return hisData;
		}

		// Token: 0x06000E6F RID: 3695 RVA: 0x0005BC94 File Offset: 0x00059E94
		public List<HisData> method_91(int int_0, DateTime dateTime_0, ref bool bool_4)
		{
			DateTime dateTime_ = DateTime.MinValue;
			List<HisData> result;
			if (int_0 > 0 && int_0 <= base.HisDataPeriodSet.PeriodHisDataList.Count)
			{
				dateTime_ = base.HisDataPeriodSet.PeriodHisDataList.Keys[int_0 - 1];
				ChtCtrl chtCtrl = this.method_92();
				SortedList<DateTime, HisData> sortedList_ = chtCtrl.method_40(dateTime_0, ref bool_4);
				int? nullable_ = chtCtrl.method_41();
				result = base.SymbDataSet.method_105(sortedList_, dateTime_, dateTime_0, nullable_);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06000E70 RID: 3696 RVA: 0x0005BD04 File Offset: 0x00059F04
		public ChtCtrl method_92()
		{
			ChartKLine.Class216 @class = new ChartKLine.Class216();
			@class.chartKLine_0 = this;
			@class.nullable_0 = null;
			@class.nullable_1 = null;
			if (Base.UI.Form.IsJustSpanMoved)
			{
				if (Base.UI.Form.SpanMoveChtCtrl != null)
				{
					@class.nullable_0 = new PeriodType?(Base.UI.Form.SpanMoveChtCtrl.PeriodType);
					@class.nullable_1 = Base.UI.Form.SpanMoveChtCtrl.PeriodUnits;
				}
			}
			else if (Base.UI.Form.AutoPlayPeriodType != null && Base.UI.Form.AutoPlayPeriodUnits != null)
			{
				@class.nullable_0 = new PeriodType?(Base.UI.Form.AutoPlayPeriodType.Value);
				@class.nullable_1 = new int?(Base.UI.Form.AutoPlayPeriodUnits.Value);
			}
			if (base.ChtCtrl.method_36(@class.nullable_0, @class.nullable_1))
			{
				ChtCtrl chtCtrl = Base.UI.ChtCtrlList.FirstOrDefault(new Func<ChtCtrl, bool>(@class.method_0));
				if (chtCtrl != null)
				{
					return chtCtrl;
				}
			}
			return base.ChtCtrl;
		}

		// Token: 0x06000E71 RID: 3697 RVA: 0x0005BE28 File Offset: 0x0005A028
		public override void vmethod_9(int int_0, HDTick hdtick_0, bool bool_4)
		{
			foreach (Indicator indicator in this.IndList)
			{
				indicator.UpdateLastItem(int_0, hdtick_0);
			}
			base.vmethod_9(int_0, hdtick_0, bool_4);
		}

		// Token: 0x06000E72 RID: 3698 RVA: 0x00006670 File Offset: 0x00004870
		public override void vmethod_11(HisData hisData_0)
		{
			this.method_147();
			this.vmethod_25();
			base.vmethod_11(hisData_0);
		}

		// Token: 0x06000E73 RID: 3699 RVA: 0x0005BE88 File Offset: 0x0005A088
		public virtual void vmethod_25()
		{
			this.method_93();
			if (base.SupportHighLowMark && !Base.UI.Form.NoShowHighLowMark && base.GraphPane != null && base.GraphPane.GraphObjList != null)
			{
				new HiLowMarkTextObj(this, true);
				new HiLowMarkTextObj(this, false);
			}
		}

		// Token: 0x06000E74 RID: 3700 RVA: 0x0005BED8 File Offset: 0x0005A0D8
		public void method_93()
		{
			if (base.SupportHighLowMark && base.GraphPane != null && base.GraphPane.GraphObjList != null)
			{
				base.GraphPane.GraphObjList.RemoveAll(new Predicate<GraphObj>(ChartKLine.<>c.<>9.method_4));
			}
		}

		// Token: 0x06000E75 RID: 3701 RVA: 0x0005BF34 File Offset: 0x0005A134
		public double method_94(DateTime dateTime_0)
		{
			ChartKLine.Class217 @class = new ChartKLine.Class217();
			@class.chartKLine_0 = this;
			@class.dateTime_0 = dateTime_0;
			double result = 0.0;
			@class.int_0 = base.ChtCtrl.HisDataPeriodSet.method_37(@class.dateTime_0, true);
			try
			{
				if (@class.int_0 > 0)
				{
					result = base.HisDataList.Where(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_0)).Sum(new Func<KeyValuePair<DateTime, HisData>, double?>(ChartKLine.<>c.<>9.method_5)).Value;
				}
				else
				{
					result = base.HisDataList.Where(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_1)).Sum(new Func<KeyValuePair<DateTime, HisData>, double?>(ChartKLine.<>c.<>9.method_6)).Value;
				}
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
			return result;
		}

		// Token: 0x06000E76 RID: 3702 RVA: 0x0005C030 File Offset: 0x0005A230
		public override void vmethod_12()
		{
			foreach (Indicator indicator in this.IndList)
			{
				indicator.ResetCurve();
			}
			base.vmethod_12();
		}

		// Token: 0x06000E77 RID: 3703 RVA: 0x0005C08C File Offset: 0x0005A28C
		public virtual void vmethod_26()
		{
			GraphPane graphPane = base.GraphPane;
			int? periodUnits = base.HisDataPeriodSet.PeriodUnits;
			if (base.HisDataPeriodSet.PeriodType == PeriodType.ByMins && periodUnits != null && periodUnits.Value < 15)
			{
				graphPane.XAxis.Scale.Format = "HH:mm";
				graphPane.XAxis.Scale.MajorStep = (double)this.XAxisTimeUnit;
			}
			else if ((base.HisDataPeriodSet.PeriodType == PeriodType.ByMins && periodUnits != null && periodUnits.Value >= 15) || base.HisDataPeriodSet.PeriodType != PeriodType.ByMins)
			{
				if (base.HisDataPeriodSet.PeriodType != PeriodType.ByWeek && base.HisDataPeriodSet.PeriodType != PeriodType.ByMonth && (base.HisDataPeriodSet.PeriodType != PeriodType.ByDay || base.HisDataPeriodSet.PeriodUnits == null || base.HisDataPeriodSet.PeriodUnits.Value <= 1))
				{
					graphPane.XAxis.Scale.Format = "MM/dd";
				}
				else
				{
					graphPane.XAxis.Scale.Format = "yyyyMM";
				}
				if (Base.UI.Form.IsInBlindTestMode)
				{
					graphPane.XAxis.Scale.Format = "●●/●●";
				}
				if (base.HisDataPeriodSet.PeriodType == PeriodType.ByMins)
				{
					int? numberOfStickItemsPerDay = base.HisDataPeriodSet.NumberOfStickItemsPerDay;
					if (numberOfStickItemsPerDay == null)
					{
						if (base.SymbDataSet.CurrDate != null)
						{
							DateTime date = base.SymbDataSet.CurrDate.Value.Date;
						}
						else
						{
							DateTime date2 = base.HisDataPeriodSet.PeriodHisDataList.Keys.Last<DateTime>().Date;
						}
						base.HisDataPeriodSet.NumberOfStickItemsPerDay = base.HisDataPeriodSet.method_44(base.SymbDataSet.CurrDate.Value.Date);
						numberOfStickItemsPerDay = base.HisDataPeriodSet.NumberOfStickItemsPerDay;
					}
					if (numberOfStickItemsPerDay != null)
					{
						graphPane.XAxis.Scale.MajorStep = (double)((int)Math.Ceiling((double)this.XAxisTimeUnit / (double)numberOfStickItemsPerDay.Value) * numberOfStickItemsPerDay.Value);
					}
				}
			}
			try
			{
				graphPane.XAxis.Scale.BaseTic = (double)this.method_96();
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x06000E78 RID: 3704 RVA: 0x0005C2F8 File Offset: 0x0005A4F8
		private int method_95()
		{
			int num = 1;
			int? periodUnits = base.HisDataPeriodSet.PeriodUnits;
			int indexOfLastItemShown = this.ChtCtrl_KLine.IndexOfLastItemShown;
			int indexOfFirstItemShown = this.ChtCtrl_KLine.IndexOfFirstItemShown;
			int maxSticksPerChart = this.ChtCtrl_KLine.MaxSticksPerChart;
			int num2 = (int)base.GraphPane.XAxis.Scale.BaseTic;
			int xaxisTimeUnit = (int)this.XAxisTimeUnit;
			int result;
			if (base.HisDataPeriodSet.PeriodType == PeriodType.ByMins && periodUnits != null && periodUnits.Value < 30)
			{
				if (indexOfLastItemShown + 2 > xaxisTimeUnit)
				{
					if (num2 == 1)
					{
						if (indexOfFirstItemShown + xaxisTimeUnit < base.HisDataPeriodSet.PeriodHisDataList.Count && Utility.CanExactDiv(base.HisDataPeriodSet.PeriodHisDataList.Keys[indexOfFirstItemShown + xaxisTimeUnit].Minute, xaxisTimeUnit))
						{
							return xaxisTimeUnit;
						}
						int num3 = this.method_96() - 1;
						if (num3 != 0)
						{
							return num3;
						}
					}
					else
					{
						if (indexOfLastItemShown < maxSticksPerChart - 1)
						{
							return num2;
						}
						return num2 - 1;
					}
				}
				result = num;
			}
			else
			{
				int num4 = (int)base.GraphPane.XAxis.Scale.MajorStep;
				if (num2 == 1)
				{
					bool flag = false;
					if (base.PeriodHisDataList.Count > indexOfFirstItemShown + num4)
					{
						flag = base.method_69(indexOfFirstItemShown + num4 - 1);
					}
					if (indexOfLastItemShown + 2 > num4 && flag)
					{
						result = num4;
					}
					else
					{
						result = this.method_96();
					}
				}
				else if (indexOfLastItemShown < maxSticksPerChart - 1)
				{
					result = num2;
				}
				else
				{
					result = num2 - 1;
				}
			}
			return result;
		}

		// Token: 0x06000E79 RID: 3705 RVA: 0x0005C47C File Offset: 0x0005A67C
		private int method_96()
		{
			int? periodUnits = base.HisDataPeriodSet.PeriodUnits;
			int xaxisTimeUnit = (int)this.XAxisTimeUnit;
			int maxSticksPerChart = this.ChtCtrl_KLine.MaxSticksPerChart;
			int num;
			int num2;
			if (this.ChtCtrl_KLine.IsInRetroMode)
			{
				num = this.ChtCtrl_KLine.RetroModeFirstItemIdx;
				num2 = this.ChtCtrl_KLine.RetroModeLastItemIdx;
			}
			else
			{
				num = this.ChtCtrl_KLine.IndexOfFirstItemShown;
				num2 = this.ChtCtrl_KLine.IndexOfLastItemShown;
			}
			bool flag = false;
			int i;
			if (base.HisDataPeriodSet.PeriodType == PeriodType.ByMins && periodUnits != null && periodUnits.Value < 15)
			{
				int result;
				try
				{
					if (num < base.HisDataPeriodSet.PeriodHisDataList.Count)
					{
						DateTime dateTime = base.HisDataPeriodSet.PeriodHisDataList.Keys[num];
						if (periodUnits.Value < xaxisTimeUnit && num2 + 2 > xaxisTimeUnit)
						{
							i = 0;
							while (i + 1 < maxSticksPerChart)
							{
								if (!Utility.CanExactDiv(periodUnits.Value * i + dateTime.Minute, xaxisTimeUnit))
								{
									i++;
								}
								else
								{
									flag = true;
									IL_FA:
									if (flag)
									{
										result = i + 1;
										goto IL_10B;
									}
									goto IL_106;
								}
							}
							goto IL_FA;
						}
					}
					IL_106:
					goto IL_14D;
				}
				catch
				{
					goto IL_14D;
				}
				IL_10B:
				return result;
			}
			int num3 = maxSticksPerChart;
			if (num2 < maxSticksPerChart)
			{
				num3 = num2 + 1;
			}
			i = 0;
			while (i < num3)
			{
				if (!base.method_69(i + num))
				{
					i++;
				}
				else
				{
					flag = true;
					IL_141:
					if (flag)
					{
						return i + 2;
					}
					goto IL_14D;
				}
			}
			goto IL_141;
			IL_14D:
			return 1;
		}

		// Token: 0x06000E7A RID: 3706 RVA: 0x0005C5F0 File Offset: 0x0005A7F0
		protected virtual void vmethod_27(ZedGraphControl zedGraphControl_1, ContextMenuStrip contextMenuStrip_0, Point point_0, ZedGraphControl.ContextMenuObjectState contextMenuObjectState_0)
		{
			contextMenuStrip_0.Items.Clear();
			Indicator indicator = this.method_143(point_0);
			if (indicator != null && indicator.IsSelected)
			{
				this.method_117(contextMenuStrip_0, indicator);
			}
			else
			{
				if (!Base.UI.IsInCreateNewPageState)
				{
					base.method_10(contextMenuStrip_0);
					base.method_38(contextMenuStrip_0);
					this.method_97(contextMenuStrip_0);
					base.method_38(contextMenuStrip_0);
				}
				this.method_99(contextMenuStrip_0);
				this.method_73(contextMenuStrip_0);
				this.method_76(contextMenuStrip_0);
				base.method_38(contextMenuStrip_0);
				this.vmethod_28(contextMenuStrip_0);
				base.method_13(contextMenuStrip_0);
				base.method_14(contextMenuStrip_0);
				base.method_15(contextMenuStrip_0);
				this.method_105(contextMenuStrip_0);
				this.method_112(contextMenuStrip_0);
				this.method_104(contextMenuStrip_0);
				if (base.Symbol.IsStock)
				{
					this.method_118(contextMenuStrip_0);
				}
				base.method_26(contextMenuStrip_0);
				if (!Base.UI.IsInCreateNewPageState)
				{
					base.method_38(contextMenuStrip_0);
					base.method_11(contextMenuStrip_0);
					this.method_114(contextMenuStrip_0);
					this.method_115(contextMenuStrip_0);
					base.method_31(contextMenuStrip_0);
					this.method_113(contextMenuStrip_0);
					base.method_38(contextMenuStrip_0);
					base.method_32(contextMenuStrip_0);
					if (base.ChtCtrl.SymbDataSet != null && base.ChtCtrl.SymbDataSet.CurrHisData != null)
					{
						base.method_33(contextMenuStrip_0);
					}
					base.method_12(contextMenuStrip_0);
				}
				Base.UI.smethod_73(contextMenuStrip_0);
			}
		}

		// Token: 0x06000E7B RID: 3707 RVA: 0x000041AE File Offset: 0x000023AE
		protected virtual void vmethod_28(ContextMenuStrip contextMenuStrip_0)
		{
		}

		// Token: 0x06000E7C RID: 3708 RVA: 0x0005C72C File Offset: 0x0005A92C
		protected void method_97(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "画线下单";
			if (Base.UI.DrawOdrWnd != null)
			{
				toolStripMenuItem.Checked = true;
			}
			else
			{
				toolStripMenuItem.Checked = false;
			}
			try
			{
				Keys keys = Class208.smethod_3(Enum3.const_21);
				if (keys != Keys.None)
				{
					toolStripMenuItem.ShortcutKeys = keys;
				}
			}
			catch
			{
			}
			toolStripMenuItem.Image = Class372.DrawOdrLineBtnIcon;
			toolStripMenuItem.Click += this.method_98;
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06000E7D RID: 3709 RVA: 0x00006687 File Offset: 0x00004887
		private void method_98(object sender, EventArgs e)
		{
			Base.UI.smethod_116();
		}

		// Token: 0x06000E7E RID: 3710 RVA: 0x0005C7B8 File Offset: 0x0005A9B8
		protected void method_99(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "主图指标";
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			toolStripMenuItem.DropDownItems.Add(new ToolStripMenuItem());
			toolStripMenuItem.MouseEnter += this.method_102;
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = "副图指标";
			contextMenuStrip_0.Items.Add(toolStripMenuItem2);
			toolStripMenuItem2.DropDownItems.Add(new ToolStripMenuItem());
			toolStripMenuItem2.MouseEnter += this.method_100;
		}

		// Token: 0x06000E7F RID: 3711 RVA: 0x0005C84C File Offset: 0x0005AA4C
		private void method_100(object sender, EventArgs e)
		{
			ToolStripMenuItem toolStripMenuItem = sender as ToolStripMenuItem;
			if (toolStripMenuItem != null)
			{
				this.method_101(toolStripMenuItem, ChartType.MACD);
			}
		}

		// Token: 0x06000E80 RID: 3712 RVA: 0x0005C870 File Offset: 0x0005AA70
		private void method_101(ToolStripMenuItem toolStripMenuItem_0, ChartType chartType_1)
		{
			toolStripMenuItem_0.DropDownItems.Clear();
			foreach (UserDefineIndGroup userDefineIndGroup in UserDefineFileMgr.UDGList)
			{
				UserDefineIndScript[] array;
				if (chartType_1 == ChartType.CandleStick)
				{
					array = userDefineIndGroup.UDSList.Where(new Func<UserDefineIndScript, bool>(ChartKLine.<>c.<>9.method_7)).ToArray<UserDefineIndScript>();
				}
				else
				{
					if (chartType_1 != ChartType.MACD)
					{
						break;
					}
					array = userDefineIndGroup.UDSList.Where(new Func<UserDefineIndScript, bool>(ChartKLine.<>c.<>9.method_8)).ToArray<UserDefineIndScript>();
				}
				if (array == null)
				{
					break;
				}
				if (array.Any<UserDefineIndScript>())
				{
					ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
					toolStripMenuItem.Text = userDefineIndGroup.Group;
					toolStripMenuItem_0.DropDownItems.Add(toolStripMenuItem);
					foreach (UserDefineIndScript userDefineIndScript in array)
					{
						ChartKLine.Class218 @class = new ChartKLine.Class218();
						@class.toolStripMenuItem_0 = Base.UI.smethod_77(userDefineIndScript.NameAndScript);
						@class.toolStripMenuItem_0.Name = userDefineIndScript.Name;
						@class.toolStripMenuItem_0.Click += this.method_103;
						toolStripMenuItem.DropDownItems.Add(@class.toolStripMenuItem_0);
						if (this.IndExList.Any(new Func<IndEx, bool>(@class.method_0)))
						{
							@class.toolStripMenuItem_0.Checked = true;
						}
					}
				}
			}
		}

		// Token: 0x06000E81 RID: 3713 RVA: 0x0005CA20 File Offset: 0x0005AC20
		private void method_102(object sender, EventArgs e)
		{
			ToolStripMenuItem toolStripMenuItem = sender as ToolStripMenuItem;
			if (toolStripMenuItem != null)
			{
				this.method_101(toolStripMenuItem, ChartType.CandleStick);
			}
		}

		// Token: 0x06000E82 RID: 3714 RVA: 0x0005CA44 File Offset: 0x0005AC44
		private void method_103(object sender, EventArgs e)
		{
			ChartKLine.Class219 @class = new ChartKLine.Class219();
			@class.toolStripMenuItem_0 = (sender as ToolStripMenuItem);
			if (@class.toolStripMenuItem_0 != null)
			{
				if (!@class.toolStripMenuItem_0.Checked)
				{
					string name = @class.toolStripMenuItem_0.Name;
					this.method_80(name);
				}
				else
				{
					Indicator indicator = this.ChtCtrl_KLine.IndList.FirstOrDefault(new Func<Indicator, bool>(@class.method_0));
					if (indicator != null)
					{
						indicator.RemoveFromChart();
					}
				}
				return;
			}
			throw new Exception("无法转换为右键菜单类");
		}

		// Token: 0x06000E83 RID: 3715 RVA: 0x0005CAC4 File Offset: 0x0005ACC4
		protected void method_104(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "时间周期";
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = "1分钟";
			toolStripMenuItem2.ShortcutKeyDisplayString = Class184.smethod_5(Enum3.const_25) + " Enter";
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = "3分钟";
			toolStripMenuItem3.ShortcutKeyDisplayString = Class184.smethod_5(Enum3.const_26) + " Enter";
			ToolStripMenuItem toolStripMenuItem4 = Base.UI.smethod_76();
			toolStripMenuItem4.Text = "5分钟";
			toolStripMenuItem4.ShortcutKeyDisplayString = Class184.smethod_5(Enum3.const_27) + " Enter";
			ToolStripMenuItem toolStripMenuItem5 = Base.UI.smethod_76();
			toolStripMenuItem5.Text = "10分钟";
			toolStripMenuItem5.ShortcutKeyDisplayString = Class184.smethod_5(Enum3.const_36) + " Enter";
			ToolStripMenuItem toolStripMenuItem6 = Base.UI.smethod_76();
			toolStripMenuItem6.Text = "15分钟";
			toolStripMenuItem6.ShortcutKeyDisplayString = Class184.smethod_5(Enum3.const_28) + " Enter";
			ToolStripMenuItem toolStripMenuItem7 = Base.UI.smethod_76();
			toolStripMenuItem7.Text = "30分钟";
			toolStripMenuItem7.ShortcutKeyDisplayString = Class184.smethod_5(Enum3.const_29) + " Enter";
			ToolStripMenuItem toolStripMenuItem8 = Base.UI.smethod_76();
			toolStripMenuItem8.Text = "1小时";
			toolStripMenuItem8.ShortcutKeyDisplayString = Class184.smethod_5(Enum3.const_30) + " Enter";
			ToolStripMenuItem toolStripMenuItem9 = Base.UI.smethod_76();
			toolStripMenuItem9.Text = "2小时";
			toolStripMenuItem9.ShortcutKeyDisplayString = Class184.smethod_5(Enum3.const_31) + " Enter";
			ToolStripMenuItem toolStripMenuItem10 = Base.UI.smethod_76();
			toolStripMenuItem10.Text = "4小时";
			toolStripMenuItem10.ShortcutKeyDisplayString = Class184.smethod_5(Enum3.const_32) + " Enter";
			ToolStripMenuItem toolStripMenuItem11 = Base.UI.smethod_76();
			toolStripMenuItem11.Text = "日";
			toolStripMenuItem11.ShortcutKeyDisplayString = Class184.smethod_5(Enum3.const_33) + " Enter";
			ToolStripMenuItem toolStripMenuItem12 = Base.UI.smethod_76();
			toolStripMenuItem12.Text = "周";
			toolStripMenuItem12.ShortcutKeyDisplayString = Class184.smethod_5(Enum3.const_34) + " Enter";
			ToolStripMenuItem toolStripMenuItem13 = Base.UI.smethod_76();
			toolStripMenuItem13.Text = "月";
			toolStripMenuItem13.ShortcutKeyDisplayString = Class184.smethod_5(Enum3.const_35) + " Enter";
			ToolStripMenuItem toolStripMenuItem14 = Base.UI.smethod_76();
			toolStripMenuItem14.Text = "自定N分钟";
			toolStripMenuItem14.ShortcutKeyDisplayString = Class184.smethod_5(Enum3.const_37) + " Enter";
			ToolStripMenuItem toolStripMenuItem15 = Base.UI.smethod_76();
			toolStripMenuItem15.Text = "自定N小时";
			toolStripMenuItem15.ShortcutKeyDisplayString = Class184.smethod_5(Enum3.const_38) + " Enter";
			ToolStripMenuItem toolStripMenuItem16 = Base.UI.smethod_76();
			toolStripMenuItem16.Text = "自定N天";
			toolStripMenuItem16.ShortcutKeyDisplayString = Class184.smethod_5(Enum3.const_39) + " Enter";
			toolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[]
			{
				toolStripMenuItem2,
				toolStripMenuItem3,
				toolStripMenuItem4,
				toolStripMenuItem5,
				toolStripMenuItem6,
				toolStripMenuItem7,
				toolStripMenuItem8,
				toolStripMenuItem9,
				toolStripMenuItem10,
				toolStripMenuItem11,
				toolStripMenuItem12,
				toolStripMenuItem13,
				toolStripMenuItem14,
				toolStripMenuItem15,
				toolStripMenuItem16
			});
			ChtCtrl_KLine chtCtrl_KLine = this.ChtCtrl_KLine;
			if (chtCtrl_KLine.HisDataPeriodSet.PeriodType == PeriodType.ByMins)
			{
				if (chtCtrl_KLine.HisDataPeriodSet.PeriodUnits != null)
				{
					int? periodUnits = chtCtrl_KLine.HisDataPeriodSet.PeriodUnits;
					if (!(periodUnits.GetValueOrDefault() == 1 & periodUnits != null))
					{
						periodUnits = chtCtrl_KLine.HisDataPeriodSet.PeriodUnits;
						if (periodUnits.GetValueOrDefault() == 3 & periodUnits != null)
						{
							toolStripMenuItem3.Checked = true;
							goto IL_601;
						}
						periodUnits = chtCtrl_KLine.HisDataPeriodSet.PeriodUnits;
						if (periodUnits.GetValueOrDefault() == 5 & periodUnits != null)
						{
							toolStripMenuItem4.Checked = true;
							goto IL_601;
						}
						periodUnits = chtCtrl_KLine.HisDataPeriodSet.PeriodUnits;
						if (periodUnits.GetValueOrDefault() == 10 & periodUnits != null)
						{
							toolStripMenuItem5.Checked = true;
							goto IL_601;
						}
						periodUnits = chtCtrl_KLine.HisDataPeriodSet.PeriodUnits;
						if (periodUnits.GetValueOrDefault() == 15 & periodUnits != null)
						{
							toolStripMenuItem6.Checked = true;
							goto IL_601;
						}
						periodUnits = chtCtrl_KLine.HisDataPeriodSet.PeriodUnits;
						if (periodUnits.GetValueOrDefault() == 30 & periodUnits != null)
						{
							toolStripMenuItem7.Checked = true;
							goto IL_601;
						}
						periodUnits = chtCtrl_KLine.HisDataPeriodSet.PeriodUnits;
						if (periodUnits.GetValueOrDefault() == 60 & periodUnits != null)
						{
							toolStripMenuItem8.Checked = true;
							goto IL_601;
						}
						periodUnits = chtCtrl_KLine.HisDataPeriodSet.PeriodUnits;
						if (periodUnits.GetValueOrDefault() == 120 & periodUnits != null)
						{
							toolStripMenuItem9.Checked = true;
							goto IL_601;
						}
						periodUnits = chtCtrl_KLine.HisDataPeriodSet.PeriodUnits;
						if (periodUnits.GetValueOrDefault() == 240 & periodUnits != null)
						{
							toolStripMenuItem10.Checked = true;
							goto IL_601;
						}
						if (Utility.CanExactDiv(chtCtrl_KLine.HisDataPeriodSet.PeriodUnits.Value, 60))
						{
							toolStripMenuItem15.Checked = true;
							goto IL_601;
						}
						toolStripMenuItem14.Checked = true;
						goto IL_601;
					}
				}
				toolStripMenuItem2.Checked = true;
			}
			else if (chtCtrl_KLine.HisDataPeriodSet.PeriodType == PeriodType.ByDay)
			{
				if (chtCtrl_KLine.HisDataPeriodSet.PeriodUnits != null)
				{
					if (chtCtrl_KLine.HisDataPeriodSet.PeriodUnits == null || chtCtrl_KLine.HisDataPeriodSet.PeriodUnits.Value != 1)
					{
						toolStripMenuItem16.Checked = true;
						goto IL_601;
					}
				}
				toolStripMenuItem11.Checked = true;
			}
			else if (chtCtrl_KLine.HisDataPeriodSet.PeriodType == PeriodType.ByWeek)
			{
				toolStripMenuItem12.Checked = true;
			}
			else if (chtCtrl_KLine.HisDataPeriodSet.PeriodType == PeriodType.ByMonth)
			{
				toolStripMenuItem13.Checked = true;
			}
			IL_601:
			foreach (object obj in toolStripMenuItem.DropDownItems)
			{
				if (obj is ToolStripMenuItem)
				{
					((ToolStripMenuItem)obj).Click += this.method_129;
				}
			}
		}

		// Token: 0x06000E84 RID: 3716 RVA: 0x0005D140 File Offset: 0x0005B340
		public void method_105(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "Ｋ线类型";
			toolStripMenuItem.DropDownOpening += this.method_106;
			toolStripMenuItem.DropDownItems.Add(new ToolStripMenuItem());
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06000E85 RID: 3717 RVA: 0x0005D190 File Offset: 0x0005B390
		private void method_106(object sender, EventArgs e)
		{
			ToolStripMenuItem toolStripMenuItem = sender as ToolStripMenuItem;
			toolStripMenuItem.DropDownItems.Clear();
			this.method_107(toolStripMenuItem);
		}

		// Token: 0x06000E86 RID: 3718 RVA: 0x0005D1B8 File Offset: 0x0005B3B8
		private void method_107(ToolStripMenuItem toolStripMenuItem_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "蜡烛线（空心阳线）";
			toolStripMenuItem.Click += this.method_108;
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = "蜡烛线（实心阳线）";
			toolStripMenuItem2.Click += this.method_109;
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = "竹节线（OHLC）";
			toolStripMenuItem3.Click += this.method_110;
			ToolStripMenuItem toolStripMenuItem4 = Base.UI.smethod_76();
			toolStripMenuItem4.Text = "竹节线（HLC）";
			toolStripMenuItem4.Click += this.method_111;
			toolStripMenuItem_0.DropDownItems.AddRange(new ToolStripItem[]
			{
				toolStripMenuItem,
				toolStripMenuItem2,
				toolStripMenuItem3,
				toolStripMenuItem4
			});
			if (Base.UI.Form.KLineType != null && Base.UI.Form.KLineType.Value != KLineType.CandleStick_EmptyUpK)
			{
				if (Base.UI.Form.KLineType.Value == KLineType.CandleStick_SolidUpK)
				{
					toolStripMenuItem2.Checked = true;
				}
				else if (Base.UI.Form.KLineType.Value == KLineType.OHLCBar)
				{
					toolStripMenuItem3.Checked = true;
				}
				else
				{
					toolStripMenuItem4.Checked = true;
				}
			}
			else
			{
				toolStripMenuItem.Checked = true;
			}
		}

		// Token: 0x06000E87 RID: 3719 RVA: 0x0005D2EC File Offset: 0x0005B4EC
		private void method_108(object sender, EventArgs e)
		{
			if (Base.UI.Form.KLineType != null && Base.UI.Form.KLineType.Value != KLineType.CandleStick_EmptyUpK)
			{
				Base.UI.Form.KLineType = new KLineType?(KLineType.CandleStick_EmptyUpK);
				Base.UI.smethod_19();
			}
		}

		// Token: 0x06000E88 RID: 3720 RVA: 0x0005D338 File Offset: 0x0005B538
		private void method_109(object sender, EventArgs e)
		{
			if (Base.UI.Form.KLineType == null || Base.UI.Form.KLineType.Value != KLineType.CandleStick_SolidUpK)
			{
				Base.UI.Form.KLineType = new KLineType?(KLineType.CandleStick_SolidUpK);
				Base.UI.smethod_19();
			}
		}

		// Token: 0x06000E89 RID: 3721 RVA: 0x0005D388 File Offset: 0x0005B588
		private void method_110(object sender, EventArgs e)
		{
			if (Base.UI.Form.KLineType == null || Base.UI.Form.KLineType.Value != KLineType.OHLCBar)
			{
				Base.UI.Form.KLineType = new KLineType?(KLineType.OHLCBar);
				Base.UI.smethod_19();
			}
		}

		// Token: 0x06000E8A RID: 3722 RVA: 0x0005D3D8 File Offset: 0x0005B5D8
		private void method_111(object sender, EventArgs e)
		{
			if (Base.UI.Form.KLineType == null || Base.UI.Form.KLineType.Value != KLineType.HLCBar)
			{
				Base.UI.Form.KLineType = new KLineType?(KLineType.HLCBar);
				Base.UI.smethod_19();
			}
		}

		// Token: 0x06000E8B RID: 3723 RVA: 0x0005D428 File Offset: 0x0005B628
		protected void method_112(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "视图组合";
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = "一栏";
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = "二栏";
			ToolStripMenuItem toolStripMenuItem4 = Base.UI.smethod_76();
			toolStripMenuItem4.Text = "三栏";
			ToolStripMenuItem toolStripMenuItem5 = Base.UI.smethod_76();
			toolStripMenuItem5.Text = "四栏";
			toolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[]
			{
				toolStripMenuItem2,
				toolStripMenuItem3,
				toolStripMenuItem4,
				toolStripMenuItem5
			});
			switch (this.ChtCtrl_KLine.NumberOfCharts)
			{
			case 1:
				toolStripMenuItem2.Checked = true;
				break;
			case 2:
				toolStripMenuItem3.Checked = true;
				break;
			case 3:
				toolStripMenuItem4.Checked = true;
				break;
			case 4:
				toolStripMenuItem5.Checked = true;
				break;
			}
			toolStripMenuItem2.Click += this.method_134;
			toolStripMenuItem3.Click += this.method_135;
			toolStripMenuItem4.Click += this.method_136;
			toolStripMenuItem5.Click += this.method_137;
		}

		// Token: 0x06000E8C RID: 3724 RVA: 0x0005D554 File Offset: 0x0005B754
		protected void method_113(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "画线工具";
			if (Base.UI.DrawToolWnd != null)
			{
				toolStripMenuItem.Checked = true;
			}
			else
			{
				toolStripMenuItem.Checked = false;
			}
			try
			{
				Keys keys = Class208.smethod_3(Enum3.const_19);
				if (keys != Keys.None)
				{
					toolStripMenuItem.ShortcutKeys = keys;
				}
			}
			catch
			{
			}
			toolStripMenuItem.Image = Class372.LineD;
			toolStripMenuItem.Click += this.method_139;
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06000E8D RID: 3725 RVA: 0x0005D5E0 File Offset: 0x0005B7E0
		protected void method_114(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "显示日分隔线";
			toolStripMenuItem.Click += this.method_132;
			if (Base.UI.Form.IfDispDayDivLine)
			{
				toolStripMenuItem.Checked = true;
			}
			else
			{
				toolStripMenuItem.Checked = false;
			}
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06000E8E RID: 3726 RVA: 0x0005D63C File Offset: 0x0005B83C
		protected void method_115(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "显示持仓线";
			toolStripMenuItem.Click += this.method_133;
			if (Base.UI.Form.IfDisableTsOdrLine)
			{
				toolStripMenuItem.Checked = false;
			}
			else
			{
				toolStripMenuItem.Checked = true;
			}
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06000E8F RID: 3727 RVA: 0x0005D698 File Offset: 0x0005B898
		protected void method_116(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "删除指标";
			toolStripMenuItem.Click += this.method_138;
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			contextMenuStrip_0.Items.Add(new ToolStripSeparator());
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = "修改公式";
			toolStripMenuItem2.Click += this.method_88;
			contextMenuStrip_0.Items.Add(toolStripMenuItem2);
			contextMenuStrip_0.Items.Add(new ToolStripSeparator());
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = "调整参数";
			toolStripMenuItem3.Click += this.method_140;
			contextMenuStrip_0.Items.Add(toolStripMenuItem3);
		}

		// Token: 0x06000E90 RID: 3728 RVA: 0x0005D75C File Offset: 0x0005B95C
		protected void method_117(ContextMenuStrip contextMenuStrip_0, Indicator indicator_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "删除指标";
			toolStripMenuItem.Click += this.method_138;
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			contextMenuStrip_0.Items.Add(new ToolStripSeparator());
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = "调整参数";
			toolStripMenuItem2.Click += this.method_140;
			contextMenuStrip_0.Items.Add(toolStripMenuItem2);
			contextMenuStrip_0.Items.Add(new ToolStripSeparator());
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = "修改公式";
			toolStripMenuItem3.Click += this.method_88;
			contextMenuStrip_0.Items.Add(toolStripMenuItem3);
		}

		// Token: 0x06000E91 RID: 3729 RVA: 0x0005D820 File Offset: 0x0005BA20
		protected void method_118(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "复权处理";
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = "前复权";
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = "后复权";
			ToolStripMenuItem toolStripMenuItem4 = Base.UI.smethod_76();
			toolStripMenuItem4.Text = "不复权";
			toolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[]
			{
				toolStripMenuItem2,
				toolStripMenuItem3,
				toolStripMenuItem4
			});
			if (Base.UI.Form.StockRestorationMethod == null)
			{
				Base.UI.Form.StockRestorationMethod = new StockRestorationMethod?(StockRestorationMethod.Prior);
			}
			StockRestorationMethod? stockRestorationMethod = Base.UI.Form.StockRestorationMethod;
			if (stockRestorationMethod != null)
			{
				switch (stockRestorationMethod.GetValueOrDefault())
				{
				case StockRestorationMethod.Prior:
					toolStripMenuItem2.Checked = true;
					break;
				case StockRestorationMethod.Later:
					toolStripMenuItem3.Checked = true;
					break;
				case StockRestorationMethod.None:
					toolStripMenuItem4.Checked = true;
					break;
				}
			}
			toolStripMenuItem2.Click += this.method_119;
			toolStripMenuItem3.Click += this.method_120;
			toolStripMenuItem4.Click += this.method_121;
		}

		// Token: 0x06000E92 RID: 3730 RVA: 0x0005D944 File Offset: 0x0005BB44
		private void method_119(object sender, EventArgs e)
		{
			if (Base.UI.Form.StockRestorationMethod != null)
			{
				StockRestorationMethod? stockRestorationMethod = Base.UI.Form.StockRestorationMethod;
				if (!(stockRestorationMethod.GetValueOrDefault() == StockRestorationMethod.Prior & stockRestorationMethod != null))
				{
					Base.UI.Form.StockRestorationMethod = new StockRestorationMethod?(StockRestorationMethod.Prior);
				}
			}
		}

		// Token: 0x06000E93 RID: 3731 RVA: 0x0005D998 File Offset: 0x0005BB98
		private void method_120(object sender, EventArgs e)
		{
			if (Base.UI.Form.StockRestorationMethod != null)
			{
				if (Base.UI.Form.StockRestorationMethod == null)
				{
					return;
				}
				StockRestorationMethod? stockRestorationMethod = Base.UI.Form.StockRestorationMethod;
				if (stockRestorationMethod.GetValueOrDefault() == StockRestorationMethod.Later & stockRestorationMethod != null)
				{
					return;
				}
			}
			Base.UI.Form.StockRestorationMethod = new StockRestorationMethod?(StockRestorationMethod.Later);
		}

		// Token: 0x06000E94 RID: 3732 RVA: 0x0005DA00 File Offset: 0x0005BC00
		private void method_121(object sender, EventArgs e)
		{
			if (Base.UI.Form.StockRestorationMethod != null)
			{
				if (Base.UI.Form.StockRestorationMethod == null)
				{
					return;
				}
				StockRestorationMethod? stockRestorationMethod = Base.UI.Form.StockRestorationMethod;
				if (stockRestorationMethod.GetValueOrDefault() == StockRestorationMethod.None & stockRestorationMethod != null)
				{
					return;
				}
			}
			Base.UI.Form.StockRestorationMethod = new StockRestorationMethod?(StockRestorationMethod.None);
		}

		// Token: 0x06000E95 RID: 3733 RVA: 0x0005DA68 File Offset: 0x0005BC68
		protected virtual bool vmethod_29(Indicator indicator_0, ChartKLine chartKLine_0, ToolStripMenuItem toolStripMenuItem_0)
		{
			if (toolStripMenuItem_0 != null)
			{
				this.method_127(toolStripMenuItem_0, indicator_0.Chart);
			}
			if (indicator_0.IsMainChartInd)
			{
				ChartCS chartCS;
				if (chartKLine_0.ChartType == ChartType.CandleStick)
				{
					chartCS = (chartKLine_0 as ChartCS);
				}
				else
				{
					chartCS = (this.ChtCtrl_KLine.ChartList.FirstOrDefault(new Func<ChartBase, bool>(ChartKLine.<>c.<>9.method_9)) as ChartCS);
				}
				if (chartCS == null)
				{
					return false;
				}
				if (!indicator_0.InitInd(chartCS))
				{
					return false;
				}
				chartCS.method_123(indicator_0, chartCS);
			}
			else
			{
				Class223 @class;
				if (chartKLine_0.ChartType == ChartType.MACD)
				{
					@class = (chartKLine_0 as Class223);
				}
				else
				{
					@class = (this.ChtCtrl_KLine.ChartList.FirstOrDefault(new Func<ChartBase, bool>(ChartKLine.<>c.<>9.method_10)) as Class223);
				}
				if (@class == null)
				{
					return false;
				}
				this.ChtCtrl_KLine.method_74(@class, indicator_0);
			}
			return true;
		}

		// Token: 0x06000E96 RID: 3734 RVA: 0x00006690 File Offset: 0x00004890
		public void method_122(Indicator indicator_0)
		{
			this.method_124(indicator_0, this, false);
		}

		// Token: 0x06000E97 RID: 3735 RVA: 0x0000669D File Offset: 0x0000489D
		public void method_123(Indicator indicator_0, ChartKLine chartKLine_0)
		{
			this.method_124(indicator_0, chartKLine_0, true);
		}

		// Token: 0x06000E98 RID: 3736 RVA: 0x0005DB58 File Offset: 0x0005BD58
		public void method_124(Indicator indicator_0, ChartKLine chartKLine_0, bool bool_4)
		{
			if (!this.method_126(indicator_0) || bool_4)
			{
				bool isSelected;
				if (isSelected = indicator_0.IsSelected)
				{
					indicator_0.IsSelected = false;
				}
				if (this.method_126(indicator_0))
				{
					indicator_0.RemoveFromChart();
				}
				indicator_0.InitItem();
				int num = this.IsInRetroMode ? this.ChtCtrl_KLine.RetroModeLastItemIdx : this.ChtCtrl_KLine.IndexOfLastItemShown;
				if (this.IsInRetroMode)
				{
					num = this.ChtCtrl_KLine.RetroModeLastItemIdx;
				}
				indicator_0.StartIdx = 0;
				indicator_0.EndIdx = num;
				for (int i = 0; i <= num; i++)
				{
					indicator_0.AddNewItem(i);
				}
				if (!this.method_126(indicator_0))
				{
					chartKLine_0.IndList.Add(indicator_0);
				}
				this.vmethod_19();
				indicator_0.IndicatorRemoved += this.vmethod_30;
				if (isSelected)
				{
					indicator_0.IsSelected = true;
				}
				chartKLine_0.vmethod_11(base.SymbDataSet.LastHisData);
				chartKLine_0.vmethod_15();
			}
		}

		// Token: 0x06000E99 RID: 3737 RVA: 0x0005DC44 File Offset: 0x0005BE44
		public void method_125(Indicator indicator_0)
		{
			if (this.IndList.Contains(indicator_0))
			{
				if (indicator_0.IsSelected)
				{
					indicator_0.IsSelected = false;
				}
				indicator_0.InitItem();
				int num = this.IsInRetroMode ? this.ChtCtrl_KLine.RetroModeLastItemIdx : this.ChtCtrl_KLine.IndexOfLastItemShown;
				if (this.IsInRetroMode)
				{
					num = this.ChtCtrl_KLine.RetroModeLastItemIdx;
				}
				indicator_0.StartIdx = 0;
				indicator_0.EndIdx = num;
				for (int i = 0; i <= num; i++)
				{
					indicator_0.AddNewItem(i);
				}
			}
		}

		// Token: 0x06000E9A RID: 3738 RVA: 0x000041AE File Offset: 0x000023AE
		protected virtual void vmethod_30(object sender, EventArgs27 e)
		{
		}

		// Token: 0x06000E9B RID: 3739 RVA: 0x0005DCD0 File Offset: 0x0005BED0
		public bool method_126(Indicator indicator_0)
		{
			ChartKLine.Class220 @class = new ChartKLine.Class220();
			@class.indicator_0 = indicator_0;
			@class.chartKLine_0 = this;
			bool result;
			if (this.IndList != null)
			{
				result = this.IndList.Exists(new Predicate<Indicator>(@class.method_0));
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06000E9C RID: 3740 RVA: 0x0005DD18 File Offset: 0x0005BF18
		private bool method_127(ToolStripMenuItem toolStripMenuItem_0, ChartKLine chartKLine_0)
		{
			ChartKLine.Class221 @class = new ChartKLine.Class221();
			@class.chartKLine_0 = chartKLine_0;
			bool result;
			using (IEnumerator enumerator = toolStripMenuItem_0.DropDownItems.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					ChartKLine.Class222 class2 = new ChartKLine.Class222();
					class2.class221_0 = @class;
					class2.toolStripMenuItem_0 = (ToolStripMenuItem)enumerator.Current;
					if (class2.toolStripMenuItem_0.Checked)
					{
						try
						{
							this.ChtCtrl_KLine.IndList.Single(new Func<Indicator, bool>(class2.method_0)).RemoveFromChart();
							class2.toolStripMenuItem_0.Checked = false;
							result = true;
							goto IL_9E;
						}
						catch
						{
						}
					}
				}
			}
			return false;
			IL_9E:
			return result;
		}

		// Token: 0x06000E9D RID: 3741 RVA: 0x0005DDE8 File Offset: 0x0005BFE8
		private void method_128(object sender, EventArgs3 e)
		{
			Indicator ind = e.Ind;
			ChartKLine chart = e.Chart;
			ind.SetIndParams(e.IndParamList);
			if (e.OwnerItem != null)
			{
				this.method_127(e.OwnerItem, chart);
			}
			if (ind.IsMainChartInd)
			{
				if (ind.InitInd(chart))
				{
					this.method_123(ind, chart);
				}
			}
			else
			{
				this.ChtCtrl_KLine.method_73(ind);
			}
		}

		// Token: 0x06000E9E RID: 3742 RVA: 0x0005DE50 File Offset: 0x0005C050
		private void method_129(object sender, EventArgs e)
		{
			ToolStripMenuItem toolStripMenuItem = (ToolStripMenuItem)sender;
			ChtCtrl_KLine chtCtrl_KLine = this.ChtCtrl_KLine;
			if (toolStripMenuItem.Text == "1分钟")
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(1));
			}
			else if (toolStripMenuItem.Text == "2分钟")
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(2));
			}
			else if (toolStripMenuItem.Text == "3分钟")
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(3));
			}
			else if (toolStripMenuItem.Text == "5分钟")
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(5));
			}
			else if (toolStripMenuItem.Text == "10分钟")
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(10));
			}
			else if (toolStripMenuItem.Text == "15分钟")
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(15));
			}
			else if (toolStripMenuItem.Text == "30分钟")
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(30));
			}
			else if (toolStripMenuItem.Text == "1小时")
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(60));
			}
			else if (toolStripMenuItem.Text == "2小时")
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(120));
			}
			else if (toolStripMenuItem.Text == "4小时")
			{
				chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(240));
			}
			else if (toolStripMenuItem.Text == "日")
			{
				chtCtrl_KLine.method_12(PeriodType.ByDay, null);
			}
			else if (toolStripMenuItem.Text == "周")
			{
				chtCtrl_KLine.method_12(PeriodType.ByWeek, null);
			}
			else if (toolStripMenuItem.Text == "月")
			{
				chtCtrl_KLine.method_12(PeriodType.ByMonth, null);
			}
			else if (toolStripMenuItem.Text == "自定N分钟")
			{
				this.method_130(Enum4.const_0);
			}
			else if (toolStripMenuItem.Text == "自定N小时")
			{
				this.method_130(Enum4.const_1);
			}
			else if (toolStripMenuItem.Text == "自定N天")
			{
				this.method_130(Enum4.const_2);
			}
		}

		// Token: 0x06000E9F RID: 3743 RVA: 0x0005E09C File Offset: 0x0005C29C
		private void method_130(Enum4 enum4_0)
		{
			int int_ = 10;
			if (enum4_0 == Enum4.const_0)
			{
				if (Base.UI.Form.NMinsPeriodUnits != null)
				{
					int_ = Base.UI.Form.NMinsPeriodUnits.Value;
				}
			}
			else if (enum4_0 == Enum4.const_1)
			{
				if (Base.UI.Form.NHoursPeriodUnits != null)
				{
					int_ = Base.UI.Form.NHoursPeriodUnits.Value;
				}
			}
			else if (Base.UI.Form.NDaysPeriodUnits != null)
			{
				int_ = Base.UI.Form.NDaysPeriodUnits.Value;
			}
			SetNPeriodWnd setNPeriodWnd = new SetNPeriodWnd(enum4_0, int_);
			setNPeriodWnd.NPeriodSet += this.method_131;
			setNPeriodWnd.ShowDialog();
		}

		// Token: 0x06000EA0 RID: 3744 RVA: 0x0005E150 File Offset: 0x0005C350
		private void method_131(EventArgs4 eventArgs4_0)
		{
			ChtCtrl_KLine chtCtrl_KLine = Base.UI.SelectedChtCtrl as ChtCtrl_KLine;
			if (chtCtrl_KLine != null)
			{
				switch (eventArgs4_0.NPeriodType)
				{
				case Enum4.const_0:
					chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(eventArgs4_0.Period));
					Base.UI.Form.NMinsPeriodUnits = new int?(eventArgs4_0.Period);
					break;
				case Enum4.const_1:
					chtCtrl_KLine.method_12(PeriodType.ByMins, new int?(eventArgs4_0.Period * 60));
					Base.UI.Form.NHoursPeriodUnits = new int?(eventArgs4_0.Period);
					break;
				case Enum4.const_2:
					chtCtrl_KLine.method_12(PeriodType.ByDay, new int?(eventArgs4_0.Period));
					Base.UI.Form.NDaysPeriodUnits = new int?(eventArgs4_0.Period);
					break;
				}
			}
		}

		// Token: 0x06000EA1 RID: 3745 RVA: 0x000066AA File Offset: 0x000048AA
		private void method_132(object sender, EventArgs e)
		{
			if (Base.UI.Form.IfDispDayDivLine)
			{
				Base.UI.Form.IfDispDayDivLine = false;
			}
			else
			{
				Base.UI.Form.IfDispDayDivLine = true;
			}
			Base.UI.smethod_31();
		}

		// Token: 0x06000EA2 RID: 3746 RVA: 0x000066D7 File Offset: 0x000048D7
		private void method_133(object sender, EventArgs e)
		{
			if (Base.UI.Form.IfDisableTsOdrLine)
			{
				Base.UI.Form.IfDisableTsOdrLine = false;
			}
			else
			{
				Base.UI.Form.IfDisableTsOdrLine = true;
			}
			Base.UI.smethod_31();
		}

		// Token: 0x06000EA3 RID: 3747 RVA: 0x00006704 File Offset: 0x00004904
		private void method_134(object sender, EventArgs e)
		{
			this.ChtCtrl_KLine.method_80();
		}

		// Token: 0x06000EA4 RID: 3748 RVA: 0x00006713 File Offset: 0x00004913
		private void method_135(object sender, EventArgs e)
		{
			this.ChtCtrl_KLine.method_81();
		}

		// Token: 0x06000EA5 RID: 3749 RVA: 0x00006722 File Offset: 0x00004922
		private void method_136(object sender, EventArgs e)
		{
			this.ChtCtrl_KLine.method_83();
		}

		// Token: 0x06000EA6 RID: 3750 RVA: 0x00006731 File Offset: 0x00004931
		private void method_137(object sender, EventArgs e)
		{
			this.ChtCtrl_KLine.method_85();
		}

		// Token: 0x06000EA7 RID: 3751 RVA: 0x0005E208 File Offset: 0x0005C408
		private void method_138(object sender, EventArgs e)
		{
			Indicator selectedInd = this.ChtCtrl_KLine.SelectedInd;
			if (selectedInd != null)
			{
				selectedInd.IsSelected = false;
				selectedInd.RemoveFromChart();
			}
		}

		// Token: 0x06000EA8 RID: 3752 RVA: 0x00006740 File Offset: 0x00004940
		private void method_139(object sender, EventArgs e)
		{
			Base.UI.smethod_115();
		}

		// Token: 0x06000EA9 RID: 3753 RVA: 0x0005E234 File Offset: 0x0005C434
		private void method_140(object sender, EventArgs e)
		{
			FormSetInd formSetInd = new FormSetInd();
			List<IndEx> list = this.ChtCtrl_KLine.method_71();
			IndEx indEx = this.ChtCtrl_KLine.SelectedInd as IndEx;
			if (indEx != null)
			{
				list.Remove(indEx);
				list.Insert(0, indEx);
			}
			formSetInd.method_2(list);
			formSetInd.ShowDialog();
		}

		// Token: 0x06000EAA RID: 3754 RVA: 0x0005E288 File Offset: 0x0005C488
		protected virtual void vmethod_31(object sender, MouseEventArgs e)
		{
			if (Base.UI.DrawMode == DrawMode.Off)
			{
				GraphPane graphPane = base.GraphPane;
				PointF pointF_ = new PointF((float)e.X, (float)e.Y);
				Indicator indicator = this.method_143(pointF_);
				if (indicator != null)
				{
					if (!indicator.IsSelected)
					{
						indicator.IsSelected = true;
					}
					else
					{
						indicator.IsSelected = false;
					}
				}
				else if (this.ChtCtrl_KLine.SelectedInd != null)
				{
					this.ChtCtrl_KLine.SelectedInd.IsSelected = false;
				}
			}
		}

		// Token: 0x06000EAB RID: 3755 RVA: 0x0005E300 File Offset: 0x0005C500
		private bool method_141(ZedGraphControl zedGraphControl_1, MouseEventArgs mouseEventArgs_0)
		{
			this.nullable_2 = null;
			this.ChtCtrl_KLine.Cursor = Cursors.Default;
			return false;
		}

		// Token: 0x06000EAC RID: 3756 RVA: 0x0005E330 File Offset: 0x0005C530
		private bool method_142(ZedGraphControl zedGraphControl_1, MouseEventArgs mouseEventArgs_0)
		{
			PointD pointD = base.method_50(mouseEventArgs_0.X, mouseEventArgs_0.Y);
			this.nullable_2 = new double?(pointD.X);
			return false;
		}

		// Token: 0x06000EAD RID: 3757 RVA: 0x0005E368 File Offset: 0x0005C568
		protected override bool zedGraphControl_0_MouseMoveEvent(ZedGraphControl zedGraphControl_1, MouseEventArgs mouseEventArgs_0)
		{
			bool result;
			if (mouseEventArgs_0.Button == MouseButtons.Left && this.nullable_2 != null && Base.UI.DrawingObj == null)
			{
				ChtCtrl_KLine chtCtrl_KLine = this.ChtCtrl_KLine;
				chtCtrl_KLine.Cursor = Cursors.NoMoveHoriz;
				PointD pointD = base.method_50(mouseEventArgs_0.X, mouseEventArgs_0.Y);
				if (!pointD.Equals(default(PointD)))
				{
					int num = Convert.ToInt32(Math.Round(pointD.X - this.nullable_2.Value));
					if (num != 0)
					{
						if (num > 0)
						{
							chtCtrl_KLine.method_120(num);
						}
						else
						{
							chtCtrl_KLine.method_122(-num);
						}
						this.nullable_2 = new double?(pointD.X);
						this.vmethod_25();
					}
				}
				result = true;
			}
			else
			{
				result = base.zedGraphControl_0_MouseMoveEvent(zedGraphControl_1, mouseEventArgs_0);
			}
			return result;
		}

		// Token: 0x06000EAE RID: 3758 RVA: 0x0005E440 File Offset: 0x0005C640
		private Indicator method_143(PointF pointF_0)
		{
			CurveItem curve;
			int num;
			if (base.GraphPane.FindNearestPoint(pointF_0, out curve, out num))
			{
				Indicator result;
				try
				{
					foreach (Indicator indicator in this.list_0)
					{
						if (indicator.GetIndNameByCurve(curve) != null)
						{
							result = indicator;
							goto IL_58;
						}
					}
					goto IL_5E;
				}
				catch (Exception)
				{
					result = null;
				}
				IL_58:
				return result;
			}
			IL_5E:
			return null;
		}

		// Token: 0x06000EAF RID: 3759 RVA: 0x00006749 File Offset: 0x00004949
		private void method_144()
		{
			base.GraphPane.YAxis.ScaleFormatEvent += this.method_146;
		}

		// Token: 0x06000EB0 RID: 3760 RVA: 0x00006769 File Offset: 0x00004969
		private void method_145()
		{
			base.GraphPane.YAxis.ScaleFormatEvent -= this.method_146;
		}

		// Token: 0x06000EB1 RID: 3761 RVA: 0x0005E4D0 File Offset: 0x0005C6D0
		private string method_146(GraphPane graphPane_0, Axis axis_0, double double_0, int int_0)
		{
			string result;
			if (double_0 == 0.0)
			{
				result = "0";
			}
			else
			{
				result = "";
			}
			return result;
		}

		// Token: 0x06000EB2 RID: 3762 RVA: 0x0005E4FC File Offset: 0x0005C6FC
		protected void method_147()
		{
			GraphPane graphPane = base.GraphPane;
			if (graphPane == null)
			{
				Class182.smethod_0(new Exception("myPane == null!"));
			}
			else
			{
				List<GraphObj> list = graphPane.GraphObjList.Where(new Func<GraphObj, bool>(ChartKLine.<>c.<>9.method_11)).ToList<GraphObj>();
				for (int i = 0; i < list.Count; i++)
				{
					graphPane.GraphObjList.Remove(list[i]);
					list[i] = null;
				}
				if (Base.UI.Form.IfDispDayDivLine && base.HisDataPeriodSet.PeriodType == PeriodType.ByMins)
				{
					int? periodUnits = base.HisDataPeriodSet.PeriodUnits;
					int periodOfChartDispDayDivLine = (int)Base.UI.Form.PeriodOfChartDispDayDivLine;
					if (periodUnits.GetValueOrDefault() <= periodOfChartDispDayDivLine & periodUnits != null)
					{
						List<int> list2 = this.ChtCtrl_KLine.method_107();
						if (list2 != null)
						{
							foreach (int num in list2)
							{
								Color color = Color.Silver;
								if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
								{
									color = Color.DarkRed;
								}
								LineObj lineObj = new LineObj(color, (double)num, graphPane.YAxis.Scale.Min, (double)num, graphPane.YAxis.Scale.Max);
								lineObj.IsClippedToChartRect = false;
								lineObj.Line.Style = DashStyle.Dash;
								lineObj.Tag = ChartKLine.string_9;
								lineObj.ZOrder = ZOrder.E_BehindCurves;
								graphPane.GraphObjList.Add(lineObj);
							}
						}
					}
				}
			}
		}

		// Token: 0x17000241 RID: 577
		// (get) Token: 0x06000EB3 RID: 3763 RVA: 0x0005E6B0 File Offset: 0x0005C8B0
		// (set) Token: 0x06000EB4 RID: 3764 RVA: 0x00006789 File Offset: 0x00004989
		public List<Indicator> IndList
		{
			get
			{
				return this.list_0;
			}
			set
			{
				this.list_0 = value;
			}
		}

		// Token: 0x17000242 RID: 578
		// (get) Token: 0x06000EB5 RID: 3765 RVA: 0x0005E6C8 File Offset: 0x0005C8C8
		// (set) Token: 0x06000EB6 RID: 3766 RVA: 0x00006794 File Offset: 0x00004994
		public List<Indicator> SelectedIndList
		{
			get
			{
				return this.list_1;
			}
			set
			{
				this.list_1 = value;
			}
		}

		// Token: 0x17000243 RID: 579
		// (get) Token: 0x06000EB7 RID: 3767 RVA: 0x0005E6E0 File Offset: 0x0005C8E0
		public ChtCtrl_KLine ChtCtrl_KLine
		{
			get
			{
				return (ChtCtrl_KLine)base.ChtCtrl;
			}
		}

		// Token: 0x17000244 RID: 580
		// (get) Token: 0x06000EB8 RID: 3768 RVA: 0x0005E6FC File Offset: 0x0005C8FC
		// (set) Token: 0x06000EB9 RID: 3769 RVA: 0x0000679F File Offset: 0x0000499F
		public TimeUnit XAxisTimeUnit
		{
			get
			{
				return this.timeUnit_0;
			}
			set
			{
				this.timeUnit_0 = value;
			}
		}

		// Token: 0x17000245 RID: 581
		// (get) Token: 0x06000EBA RID: 3770 RVA: 0x0005E714 File Offset: 0x0005C914
		// (set) Token: 0x06000EBB RID: 3771 RVA: 0x0005E72C File Offset: 0x0005C92C
		public bool IfYAxisOnlyDispZeroLabel
		{
			get
			{
				return this.bool_3;
			}
			set
			{
				if (this.bool_3 != value)
				{
					this.bool_3 = value;
					if (this.bool_3)
					{
						base.GraphPane.YAxis.ScaleFormatEvent += this.method_146;
					}
					else
					{
						base.GraphPane.YAxis.ScaleFormatEvent -= this.method_146;
					}
				}
			}
		}

		// Token: 0x17000246 RID: 582
		// (get) Token: 0x06000EBC RID: 3772 RVA: 0x0005E790 File Offset: 0x0005C990
		public bool IsInRetroMode
		{
			get
			{
				return this.ChtCtrl_KLine.IsInRetroMode;
			}
		}

		// Token: 0x17000247 RID: 583
		// (get) Token: 0x06000EBD RID: 3773 RVA: 0x0005E7AC File Offset: 0x0005C9AC
		public int FirstItemIndex
		{
			get
			{
				int result;
				if (this.ChtCtrl_KLine.IsInRetroMode)
				{
					result = this.ChtCtrl_KLine.RetroModeFirstItemIdx;
				}
				else
				{
					result = this.ChtCtrl_KLine.IndexOfFirstItemShown;
				}
				return result;
			}
		}

		// Token: 0x17000248 RID: 584
		// (get) Token: 0x06000EBE RID: 3774 RVA: 0x0005E7E4 File Offset: 0x0005C9E4
		public int LastItemIndex
		{
			get
			{
				int result = this.ChtCtrl_KLine.IndexOfLastItemShown;
				if (this.ChtCtrl_KLine.IsInRetroMode)
				{
					result = this.ChtCtrl_KLine.RetroModeLastItemIdx;
				}
				return result;
			}
		}

		// Token: 0x17000249 RID: 585
		// (get) Token: 0x06000EBF RID: 3775 RVA: 0x0005E81C File Offset: 0x0005CA1C
		public PeriodType PeriodType
		{
			get
			{
				return base.ChtCtrl.PeriodType;
			}
		}

		// Token: 0x1700024A RID: 586
		// (get) Token: 0x06000EC0 RID: 3776 RVA: 0x0005E838 File Offset: 0x0005CA38
		public int? PeriodUnits
		{
			get
			{
				return base.ChtCtrl.PeriodUnits;
			}
		}

		// Token: 0x04000773 RID: 1907
		private List<Indicator> list_0;

		// Token: 0x04000774 RID: 1908
		private List<Indicator> list_1;

		// Token: 0x04000775 RID: 1909
		private TimeUnit timeUnit_0 = TimeUnit.ThirtyMins;

		// Token: 0x04000776 RID: 1910
		private bool bool_3;

		// Token: 0x04000777 RID: 1911
		protected static readonly string string_9 = "dayDiv";

		// Token: 0x04000778 RID: 1912
		private double? nullable_2;

		// Token: 0x0200017D RID: 381
		// (Invoke) Token: 0x06000EC3 RID: 3779
		public delegate void Delegate11(ZedGraphControl control, ContextMenuStrip menuStrip, Point mousePt, ZedGraphControl.ContextMenuObjectState objState);

		// Token: 0x0200017F RID: 383
		[CompilerGenerated]
		private sealed class Class212
		{
			// Token: 0x06000ED5 RID: 3797 RVA: 0x0005E9A0 File Offset: 0x0005CBA0
			internal bool method_0(IndEx indEx_0)
			{
				return indEx_0.UDInd.UDS.Name == this.string_0;
			}

			// Token: 0x06000ED6 RID: 3798 RVA: 0x0005E9CC File Offset: 0x0005CBCC
			internal bool method_1(UserDefineIndScript userDefineIndScript_0)
			{
				return userDefineIndScript_0.Name == this.string_0;
			}

			// Token: 0x04000786 RID: 1926
			public string string_0;
		}

		// Token: 0x02000180 RID: 384
		[CompilerGenerated]
		private sealed class Class213
		{
			// Token: 0x06000ED8 RID: 3800 RVA: 0x0005E9F0 File Offset: 0x0005CBF0
			internal bool method_0(Indicator indicator_0)
			{
				return indicator_0.EnName == this.userDefineIndScript_0.Name;
			}

			// Token: 0x04000787 RID: 1927
			public UserDefineIndScript userDefineIndScript_0;
		}

		// Token: 0x02000181 RID: 385
		[CompilerGenerated]
		private sealed class Class214
		{
			// Token: 0x06000EDA RID: 3802 RVA: 0x0005EA18 File Offset: 0x0005CC18
			internal bool method_0(Indicator indicator_0)
			{
				return indicator_0.EnName == this.userDefineIndScript_0.Name;
			}

			// Token: 0x04000788 RID: 1928
			public UserDefineIndScript userDefineIndScript_0;
		}

		// Token: 0x02000182 RID: 386
		[CompilerGenerated]
		private sealed class Class215
		{
			// Token: 0x06000EDC RID: 3804 RVA: 0x0005EA40 File Offset: 0x0005CC40
			internal bool method_0(IndEx indEx_0)
			{
				return indEx_0.UDInd.UDS.Name == this.eventArgs30_0.UDS.Name;
			}

			// Token: 0x04000789 RID: 1929
			public EventArgs30 eventArgs30_0;
		}

		// Token: 0x02000183 RID: 387
		[CompilerGenerated]
		private sealed class Class216
		{
			// Token: 0x06000EDE RID: 3806 RVA: 0x0005EA78 File Offset: 0x0005CC78
			internal bool method_0(ChtCtrl chtCtrl_0)
			{
				bool result;
				if (chtCtrl_0.Symbol == this.chartKLine_0.Symbol && chtCtrl_0.PeriodType == this.nullable_0.Value)
				{
					int? periodUnits = chtCtrl_0.PeriodUnits;
					int? num = this.nullable_1;
					result = (periodUnits.GetValueOrDefault() == num.GetValueOrDefault() & periodUnits != null == (num != null));
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400078A RID: 1930
			public ChartKLine chartKLine_0;

			// Token: 0x0400078B RID: 1931
			public PeriodType? nullable_0;

			// Token: 0x0400078C RID: 1932
			public int? nullable_1;
		}

		// Token: 0x02000184 RID: 388
		[CompilerGenerated]
		private sealed class Class217
		{
			// Token: 0x06000EE0 RID: 3808 RVA: 0x0005EAE4 File Offset: 0x0005CCE4
			internal bool method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				bool result;
				if (keyValuePair_0.Key > this.chartKLine_0.HisDataPeriodSet.PeriodHisDataList.Keys[this.int_0 - 1] && keyValuePair_0.Key <= this.dateTime_0)
				{
					result = (keyValuePair_0.Value.Volume != null);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x06000EE1 RID: 3809 RVA: 0x0005EB54 File Offset: 0x0005CD54
			internal bool method_1(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				bool result;
				if (keyValuePair_0.Key <= this.dateTime_0)
				{
					result = (keyValuePair_0.Value.Volume != null);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400078D RID: 1933
			public ChartKLine chartKLine_0;

			// Token: 0x0400078E RID: 1934
			public int int_0;

			// Token: 0x0400078F RID: 1935
			public DateTime dateTime_0;
		}

		// Token: 0x02000185 RID: 389
		[CompilerGenerated]
		private sealed class Class218
		{
			// Token: 0x06000EE3 RID: 3811 RVA: 0x0005EB94 File Offset: 0x0005CD94
			internal bool method_0(IndEx indEx_0)
			{
				return indEx_0.EnName == this.toolStripMenuItem_0.Name;
			}

			// Token: 0x04000790 RID: 1936
			public ToolStripMenuItem toolStripMenuItem_0;
		}

		// Token: 0x02000186 RID: 390
		[CompilerGenerated]
		private sealed class Class219
		{
			// Token: 0x06000EE5 RID: 3813 RVA: 0x0005EBBC File Offset: 0x0005CDBC
			internal bool method_0(Indicator indicator_0)
			{
				return indicator_0.EnName == this.toolStripMenuItem_0.Name;
			}

			// Token: 0x04000791 RID: 1937
			public ToolStripMenuItem toolStripMenuItem_0;
		}

		// Token: 0x02000187 RID: 391
		[CompilerGenerated]
		private sealed class Class220
		{
			// Token: 0x06000EE7 RID: 3815 RVA: 0x0005EBE4 File Offset: 0x0005CDE4
			internal bool method_0(Indicator indicator_1)
			{
				bool result;
				if (indicator_1.EnName == this.indicator_0.EnName)
				{
					result = (indicator_1.Chart == this.chartKLine_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000792 RID: 1938
			public Indicator indicator_0;

			// Token: 0x04000793 RID: 1939
			public ChartKLine chartKLine_0;
		}

		// Token: 0x02000188 RID: 392
		[CompilerGenerated]
		private sealed class Class221
		{
			// Token: 0x04000794 RID: 1940
			public ChartKLine chartKLine_0;
		}

		// Token: 0x02000189 RID: 393
		[CompilerGenerated]
		private sealed class Class222
		{
			// Token: 0x06000EEA RID: 3818 RVA: 0x0005EC20 File Offset: 0x0005CE20
			internal bool method_0(Indicator indicator_0)
			{
				bool result;
				if (indicator_0.EnName == this.toolStripMenuItem_0.Text)
				{
					result = (indicator_0.Chart == this.class221_0.chartKLine_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000795 RID: 1941
			public ToolStripMenuItem toolStripMenuItem_0;

			// Token: 0x04000796 RID: 1942
			public ChartKLine.Class221 class221_0;
		}
	}
}
