﻿using System;
using TEx;

namespace ns9
{
	// Token: 0x020001F6 RID: 502
	internal sealed class EventArgs10 : EventArgs
	{
		// Token: 0x060013AD RID: 5037 RVA: 0x00007F19 File Offset: 0x00006119
		public EventArgs10(QuickWndItem quickWndItem_1)
		{
			this.quickWndItem_0 = quickWndItem_1;
		}

		// Token: 0x170002F4 RID: 756
		// (get) Token: 0x060013AE RID: 5038 RVA: 0x000842C8 File Offset: 0x000824C8
		public QuickWndItem QuickWndItem
		{
			get
			{
				return this.quickWndItem_0;
			}
		}

		// Token: 0x04000A3E RID: 2622
		private readonly QuickWndItem quickWndItem_0;
	}
}
