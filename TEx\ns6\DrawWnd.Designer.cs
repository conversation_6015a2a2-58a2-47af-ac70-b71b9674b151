﻿namespace ns6
{
	// Token: 0x0200019A RID: 410
	internal sealed partial class DrawWnd : global::System.Windows.Forms.Form
	{
		// Token: 0x06000FF1 RID: 4081 RVA: 0x000635B0 File Offset: 0x000617B0
		private void InitializeComponent()
		{
			global::System.ComponentModel.ComponentResourceManager componentResourceManager = new global::System.ComponentModel.ComponentResourceManager(typeof(global::ns6.DrawWnd));
			this.btnX_line = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_lineD = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_redArwUp = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_paralLine = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_square = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_grnArwDn = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_redArwRUp = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_redArwLUp = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_text = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_DelDraw = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_grnArwLDn = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_grnArwRDn = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_lineV = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_lineH = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_MousePt = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_ArrowLine = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_trendSpeed = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_goldenRatio = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_ratio = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_range = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_ellipse = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_gannFan = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_45DegreeUp = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_45DegreeDn = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_periodLines = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_fbLines = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_gannLines = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_measureObj = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_fbExtLines = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_lineHExt = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_lineDH = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_lineDExt = new global::DevComponents.DotNetBar.ButtonX();
			base.SuspendLayout();
			this.btnX_line.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_line.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_line.Image = global::ns28.Class372.Line;
			this.btnX_line.Location = new global::System.Drawing.Point(4, 42);
			this.btnX_line.Name = "btnX_line";
			this.btnX_line.ShowSubItems = false;
			this.btnX_line.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_line.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_line.TabIndex = 4;
			this.btnX_line.Tooltip = "直线";
			this.btnX_lineD.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_lineD.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_lineD.Image = global::ns28.Class372.LineD;
			this.btnX_lineD.Location = new global::System.Drawing.Point(73, 43);
			this.btnX_lineD.Name = "btnX_lineD";
			this.btnX_lineD.ShowSubItems = false;
			this.btnX_lineD.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_lineD.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_lineD.TabIndex = 6;
			this.btnX_lineD.Tooltip = "线段";
			this.btnX_redArwUp.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_redArwUp.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_redArwUp.Image = global::ns28.Class372.RedArrow_Up;
			this.btnX_redArwUp.Location = new global::System.Drawing.Point(6, 289);
			this.btnX_redArwUp.Name = "btnX_redArwUp";
			this.btnX_redArwUp.ShowSubItems = false;
			this.btnX_redArwUp.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_redArwUp.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_redArwUp.TabIndex = 25;
			this.btnX_redArwUp.Tooltip = "上箭头";
			this.btnX_paralLine.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_paralLine.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_paralLine.Image = global::ns28.Class372.ParalLine;
			this.btnX_paralLine.Location = new global::System.Drawing.Point(40, 115);
			this.btnX_paralLine.Name = "btnX_paralLine";
			this.btnX_paralLine.ShowSubItems = false;
			this.btnX_paralLine.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_paralLine.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_paralLine.TabIndex = 11;
			this.btnX_paralLine.Tooltip = "平行线";
			this.btnX_square.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_square.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_square.Image = global::ns28.Class372.Square;
			this.btnX_square.Location = new global::System.Drawing.Point(73, 7);
			this.btnX_square.Name = "btnX_square";
			this.btnX_square.ShowSubItems = false;
			this.btnX_square.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_square.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_square.TabIndex = 3;
			this.btnX_square.Tooltip = "矩形";
			this.btnX_grnArwDn.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_grnArwDn.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_grnArwDn.Image = global::ns28.Class372.GreenArrow_Down;
			this.btnX_grnArwDn.Location = new global::System.Drawing.Point(6, 323);
			this.btnX_grnArwDn.Name = "btnX_grnArwDn";
			this.btnX_grnArwDn.ShowSubItems = false;
			this.btnX_grnArwDn.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_grnArwDn.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_grnArwDn.TabIndex = 28;
			this.btnX_grnArwDn.Tooltip = "下箭头";
			this.btnX_redArwRUp.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_redArwRUp.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_redArwRUp.Image = global::ns28.Class372.RedArrow_RUp;
			this.btnX_redArwRUp.Location = new global::System.Drawing.Point(40, 289);
			this.btnX_redArwRUp.Name = "btnX_redArwRUp";
			this.btnX_redArwRUp.ShowSubItems = false;
			this.btnX_redArwRUp.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_redArwRUp.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_redArwRUp.TabIndex = 26;
			this.btnX_redArwRUp.Tooltip = "右上箭头";
			this.btnX_redArwLUp.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_redArwLUp.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_redArwLUp.Image = global::ns28.Class372.RedArrow_LUp;
			this.btnX_redArwLUp.Location = new global::System.Drawing.Point(73, 289);
			this.btnX_redArwLUp.Name = "btnX_redArwLUp";
			this.btnX_redArwLUp.ShowSubItems = false;
			this.btnX_redArwLUp.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_redArwLUp.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_redArwLUp.TabIndex = 27;
			this.btnX_redArwLUp.Tooltip = "左上箭头";
			this.btnX_text.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_text.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_text.Image = global::ns28.Class372.Text;
			this.btnX_text.Location = new global::System.Drawing.Point(4, 7);
			this.btnX_text.Name = "btnX_text";
			this.btnX_text.ShowSubItems = false;
			this.btnX_text.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_text.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_text.TabIndex = 1;
			this.btnX_text.Tooltip = "文字";
			this.btnX_DelDraw.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_DelDraw.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_DelDraw.Image = global::ns28.Class372.DelDraw;
			this.btnX_DelDraw.Location = new global::System.Drawing.Point(73, 357);
			this.btnX_DelDraw.Name = "btnX_DelDraw";
			this.btnX_DelDraw.ShowSubItems = false;
			this.btnX_DelDraw.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_DelDraw.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_DelDraw.TabIndex = 31;
			this.btnX_DelDraw.Tooltip = "全部删除";
			this.btnX_grnArwLDn.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_grnArwLDn.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_grnArwLDn.Image = global::ns28.Class372.GreenArrow_LDown;
			this.btnX_grnArwLDn.Location = new global::System.Drawing.Point(73, 323);
			this.btnX_grnArwLDn.Name = "btnX_grnArwLDn";
			this.btnX_grnArwLDn.ShowSubItems = false;
			this.btnX_grnArwLDn.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_grnArwLDn.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_grnArwLDn.TabIndex = 30;
			this.btnX_grnArwLDn.Tooltip = "左下箭头";
			this.btnX_grnArwRDn.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_grnArwRDn.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_grnArwRDn.Image = global::ns28.Class372.GreenArrow_RDown;
			this.btnX_grnArwRDn.Location = new global::System.Drawing.Point(40, 323);
			this.btnX_grnArwRDn.Name = "btnX_grnArwRDn";
			this.btnX_grnArwRDn.ShowSubItems = false;
			this.btnX_grnArwRDn.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_grnArwRDn.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_grnArwRDn.TabIndex = 29;
			this.btnX_grnArwRDn.Tooltip = "右下箭头";
			this.btnX_lineV.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_lineV.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_lineV.Image = global::ns28.Class372.LineV;
			this.btnX_lineV.Location = new global::System.Drawing.Point(6, 115);
			this.btnX_lineV.Name = "btnX_lineV";
			this.btnX_lineV.ShowSubItems = false;
			this.btnX_lineV.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_lineV.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_lineV.TabIndex = 10;
			this.btnX_lineV.Tooltip = "垂直线";
			this.btnX_lineH.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_lineH.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_lineH.Image = global::ns28.Class372.LineH;
			this.btnX_lineH.Location = new global::System.Drawing.Point(6, 78);
			this.btnX_lineH.Name = "btnX_lineH";
			this.btnX_lineH.ShowSubItems = false;
			this.btnX_lineH.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_lineH.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_lineH.TabIndex = 7;
			this.btnX_lineH.Tooltip = "水平线";
			this.btnX_MousePt.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_MousePt.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_MousePt.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("btnX_MousePt.Image");
			this.btnX_MousePt.Location = new global::System.Drawing.Point(6, 357);
			this.btnX_MousePt.Name = "btnX_MousePt";
			this.btnX_MousePt.ShowSubItems = false;
			this.btnX_MousePt.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_MousePt.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_MousePt.TabIndex = 0;
			this.btnX_MousePt.Tooltip = "取消画线模式";
			this.btnX_MousePt.Click += new global::System.EventHandler(this.btnX_MousePt_Click);
			this.btnX_ArrowLine.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_ArrowLine.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_ArrowLine.Image = global::ns28.Class372.Arrow;
			this.btnX_ArrowLine.Location = new global::System.Drawing.Point(40, 7);
			this.btnX_ArrowLine.Name = "btnX_ArrowLine";
			this.btnX_ArrowLine.ShowSubItems = false;
			this.btnX_ArrowLine.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_ArrowLine.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_ArrowLine.TabIndex = 2;
			this.btnX_ArrowLine.Tooltip = "箭头线";
			this.btnX_ArrowLine.Click += new global::System.EventHandler(this.btnX_ArrowLine_Click);
			this.btnX_trendSpeed.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_trendSpeed.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_trendSpeed.Image = global::ns28.Class372.TrendSpeed;
			this.btnX_trendSpeed.Location = new global::System.Drawing.Point(73, 151);
			this.btnX_trendSpeed.Name = "btnX_trendSpeed";
			this.btnX_trendSpeed.ShowSubItems = false;
			this.btnX_trendSpeed.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_trendSpeed.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_trendSpeed.TabIndex = 15;
			this.btnX_trendSpeed.Tooltip = "阻速线";
			this.btnX_goldenRatio.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_goldenRatio.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_goldenRatio.Image = global::ns28.Class372.GoldenRatio;
			this.btnX_goldenRatio.Location = new global::System.Drawing.Point(6, 254);
			this.btnX_goldenRatio.Name = "btnX_goldenRatio";
			this.btnX_goldenRatio.ShowSubItems = false;
			this.btnX_goldenRatio.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_goldenRatio.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_goldenRatio.TabIndex = 22;
			this.btnX_goldenRatio.Tooltip = "黄金分割线";
			this.btnX_ratio.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_ratio.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_ratio.Image = global::ns28.Class372.Ratio;
			this.btnX_ratio.Location = new global::System.Drawing.Point(40, 254);
			this.btnX_ratio.Name = "btnX_ratio";
			this.btnX_ratio.ShowSubItems = false;
			this.btnX_ratio.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_ratio.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_ratio.TabIndex = 23;
			this.btnX_ratio.Tooltip = "百分比线";
			this.btnX_range.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_range.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_range.Image = global::ns28.Class372.Range;
			this.btnX_range.Location = new global::System.Drawing.Point(73, 254);
			this.btnX_range.Name = "btnX_range";
			this.btnX_range.ShowSubItems = false;
			this.btnX_range.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_range.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_range.TabIndex = 24;
			this.btnX_range.Tooltip = "测距";
			this.btnX_ellipse.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_ellipse.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_ellipse.Image = global::ns28.Class372.Ellipse;
			this.btnX_ellipse.Location = new global::System.Drawing.Point(73, 115);
			this.btnX_ellipse.Name = "btnX_ellipse";
			this.btnX_ellipse.ShowSubItems = false;
			this.btnX_ellipse.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_ellipse.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_ellipse.TabIndex = 12;
			this.btnX_ellipse.Tooltip = "椭圆";
			this.btnX_gannFan.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_gannFan.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_gannFan.Image = global::ns28.Class372.GannFan;
			this.btnX_gannFan.Location = new global::System.Drawing.Point(73, 220);
			this.btnX_gannFan.Name = "btnX_gannFan";
			this.btnX_gannFan.ShowSubItems = false;
			this.btnX_gannFan.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_gannFan.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_gannFan.TabIndex = 21;
			this.btnX_gannFan.Tooltip = "江恩角度线";
			this.btnX_45DegreeUp.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_45DegreeUp.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_45DegreeUp.Image = global::ns28.Class372._45DgreeUp;
			this.btnX_45DegreeUp.Location = new global::System.Drawing.Point(6, 151);
			this.btnX_45DegreeUp.Name = "btnX_45DegreeUp";
			this.btnX_45DegreeUp.ShowSubItems = false;
			this.btnX_45DegreeUp.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_45DegreeUp.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_45DegreeUp.TabIndex = 13;
			this.btnX_45DegreeUp.Tooltip = "上45度";
			this.btnX_45DegreeDn.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_45DegreeDn.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_45DegreeDn.Image = global::ns28.Class372._45DgreeDn;
			this.btnX_45DegreeDn.Location = new global::System.Drawing.Point(39, 151);
			this.btnX_45DegreeDn.Name = "btnX_45DegreeDn";
			this.btnX_45DegreeDn.ShowSubItems = false;
			this.btnX_45DegreeDn.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_45DegreeDn.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_45DegreeDn.TabIndex = 14;
			this.btnX_45DegreeDn.Tooltip = "下45度";
			this.btnX_periodLines.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_periodLines.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_periodLines.Image = global::ns28.Class372.PeriodLines;
			this.btnX_periodLines.Location = new global::System.Drawing.Point(6, 185);
			this.btnX_periodLines.Name = "btnX_periodLines";
			this.btnX_periodLines.ShowSubItems = false;
			this.btnX_periodLines.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_periodLines.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_periodLines.TabIndex = 16;
			this.btnX_periodLines.Tooltip = "周期线";
			this.btnX_fbLines.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_fbLines.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_fbLines.Image = global::ns28.Class372.FbLines;
			this.btnX_fbLines.Location = new global::System.Drawing.Point(39, 185);
			this.btnX_fbLines.Name = "btnX_fbLines";
			this.btnX_fbLines.ShowSubItems = false;
			this.btnX_fbLines.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_fbLines.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_fbLines.TabIndex = 17;
			this.btnX_fbLines.Tooltip = "斐波那契时间序列";
			this.btnX_gannLines.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_gannLines.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_gannLines.Image = global::ns28.Class372.GannLines;
			this.btnX_gannLines.Location = new global::System.Drawing.Point(6, 220);
			this.btnX_gannLines.Name = "btnX_gannLines";
			this.btnX_gannLines.ShowSubItems = false;
			this.btnX_gannLines.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_gannLines.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_gannLines.TabIndex = 19;
			this.btnX_gannLines.Tooltip = "江恩时间序列";
			this.btnX_measureObj.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_measureObj.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_measureObj.Image = global::ns28.Class372.MeasureObj;
			this.btnX_measureObj.Location = new global::System.Drawing.Point(39, 220);
			this.btnX_measureObj.Name = "btnX_measureObj";
			this.btnX_measureObj.ShowSubItems = false;
			this.btnX_measureObj.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_measureObj.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_measureObj.TabIndex = 20;
			this.btnX_measureObj.Tooltip = "量度目标";
			this.btnX_fbExtLines.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_fbExtLines.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_fbExtLines.Image = global::ns28.Class372.FbLines_Ext;
			this.btnX_fbExtLines.Location = new global::System.Drawing.Point(73, 185);
			this.btnX_fbExtLines.Name = "btnX_fbExtLines";
			this.btnX_fbExtLines.ShowSubItems = false;
			this.btnX_fbExtLines.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_fbExtLines.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_fbExtLines.TabIndex = 18;
			this.btnX_fbExtLines.Tooltip = "斐波那契时间扩展";
			this.btnX_lineHExt.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_lineHExt.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_lineHExt.Image = global::ns28.Class372.LineHExt;
			this.btnX_lineHExt.Location = new global::System.Drawing.Point(73, 78);
			this.btnX_lineHExt.Name = "btnX_lineHExt";
			this.btnX_lineHExt.ShowSubItems = false;
			this.btnX_lineHExt.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_lineHExt.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_lineHExt.TabIndex = 9;
			this.btnX_lineHExt.Tooltip = "水平射线";
			this.btnX_lineDH.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_lineDH.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_lineDH.Image = global::ns28.Class372.LineDH;
			this.btnX_lineDH.Location = new global::System.Drawing.Point(40, 78);
			this.btnX_lineDH.Name = "btnX_lineDH";
			this.btnX_lineDH.ShowSubItems = false;
			this.btnX_lineDH.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_lineDH.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_lineDH.TabIndex = 8;
			this.btnX_lineDH.Tooltip = "水平线段";
			this.btnX_lineDExt.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_lineDExt.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_lineDExt.Image = global::ns28.Class372.LineDExt;
			this.btnX_lineDExt.Location = new global::System.Drawing.Point(40, 43);
			this.btnX_lineDExt.Name = "btnX_lineDExt";
			this.btnX_lineDExt.ShowSubItems = false;
			this.btnX_lineDExt.Size = new global::System.Drawing.Size(27, 28);
			this.btnX_lineDExt.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_lineDExt.TabIndex = 5;
			this.btnX_lineDExt.Tooltip = "射线";
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(8f, 15f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new global::System.Drawing.Size(105, 392);
			base.Controls.Add(this.btnX_lineHExt);
			base.Controls.Add(this.btnX_lineDH);
			base.Controls.Add(this.btnX_lineDExt);
			base.Controls.Add(this.btnX_fbExtLines);
			base.Controls.Add(this.btnX_measureObj);
			base.Controls.Add(this.btnX_gannLines);
			base.Controls.Add(this.btnX_fbLines);
			base.Controls.Add(this.btnX_periodLines);
			base.Controls.Add(this.btnX_45DegreeDn);
			base.Controls.Add(this.btnX_45DegreeUp);
			base.Controls.Add(this.btnX_gannFan);
			base.Controls.Add(this.btnX_ellipse);
			base.Controls.Add(this.btnX_range);
			base.Controls.Add(this.btnX_ratio);
			base.Controls.Add(this.btnX_goldenRatio);
			base.Controls.Add(this.btnX_trendSpeed);
			base.Controls.Add(this.btnX_ArrowLine);
			base.Controls.Add(this.btnX_MousePt);
			base.Controls.Add(this.btnX_lineH);
			base.Controls.Add(this.btnX_lineV);
			base.Controls.Add(this.btnX_grnArwRDn);
			base.Controls.Add(this.btnX_grnArwLDn);
			base.Controls.Add(this.btnX_DelDraw);
			base.Controls.Add(this.btnX_text);
			base.Controls.Add(this.btnX_redArwLUp);
			base.Controls.Add(this.btnX_redArwRUp);
			base.Controls.Add(this.btnX_grnArwDn);
			base.Controls.Add(this.btnX_square);
			base.Controls.Add(this.btnX_paralLine);
			base.Controls.Add(this.btnX_redArwUp);
			base.Controls.Add(this.btnX_lineD);
			base.Controls.Add(this.btnX_line);
			base.FormBorderStyle = global::System.Windows.Forms.FormBorderStyle.FixedToolWindow;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = "DrawWnd";
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			this.Text = "画线";
			base.Load += new global::System.EventHandler(this.DrawWnd_Load);
			base.ResumeLayout(false);
		}

		// Token: 0x040007D6 RID: 2006
		private global::DevComponents.DotNetBar.ButtonX btnX_line;

		// Token: 0x040007D7 RID: 2007
		private global::DevComponents.DotNetBar.ButtonX btnX_lineD;

		// Token: 0x040007D8 RID: 2008
		private global::DevComponents.DotNetBar.ButtonX btnX_redArwUp;

		// Token: 0x040007D9 RID: 2009
		private global::DevComponents.DotNetBar.ButtonX btnX_paralLine;

		// Token: 0x040007DA RID: 2010
		private global::DevComponents.DotNetBar.ButtonX btnX_square;

		// Token: 0x040007DB RID: 2011
		private global::DevComponents.DotNetBar.ButtonX btnX_grnArwDn;

		// Token: 0x040007DC RID: 2012
		private global::DevComponents.DotNetBar.ButtonX btnX_redArwRUp;

		// Token: 0x040007DD RID: 2013
		private global::DevComponents.DotNetBar.ButtonX btnX_redArwLUp;

		// Token: 0x040007DE RID: 2014
		private global::DevComponents.DotNetBar.ButtonX btnX_text;

		// Token: 0x040007DF RID: 2015
		private global::DevComponents.DotNetBar.ButtonX btnX_DelDraw;

		// Token: 0x040007E0 RID: 2016
		private global::DevComponents.DotNetBar.ButtonX btnX_grnArwLDn;

		// Token: 0x040007E1 RID: 2017
		private global::DevComponents.DotNetBar.ButtonX btnX_grnArwRDn;

		// Token: 0x040007E2 RID: 2018
		private global::DevComponents.DotNetBar.ButtonX btnX_lineV;

		// Token: 0x040007E3 RID: 2019
		private global::DevComponents.DotNetBar.ButtonX btnX_lineH;

		// Token: 0x040007E4 RID: 2020
		private global::DevComponents.DotNetBar.ButtonX btnX_MousePt;

		// Token: 0x040007E5 RID: 2021
		private global::DevComponents.DotNetBar.ButtonX btnX_ArrowLine;

		// Token: 0x040007E6 RID: 2022
		private global::DevComponents.DotNetBar.ButtonX btnX_trendSpeed;

		// Token: 0x040007E7 RID: 2023
		private global::DevComponents.DotNetBar.ButtonX btnX_goldenRatio;

		// Token: 0x040007E8 RID: 2024
		private global::DevComponents.DotNetBar.ButtonX btnX_ratio;

		// Token: 0x040007E9 RID: 2025
		private global::DevComponents.DotNetBar.ButtonX btnX_range;

		// Token: 0x040007EA RID: 2026
		private global::DevComponents.DotNetBar.ButtonX btnX_ellipse;

		// Token: 0x040007EB RID: 2027
		private global::DevComponents.DotNetBar.ButtonX btnX_gannFan;

		// Token: 0x040007EC RID: 2028
		private global::DevComponents.DotNetBar.ButtonX btnX_45DegreeUp;

		// Token: 0x040007ED RID: 2029
		private global::DevComponents.DotNetBar.ButtonX btnX_45DegreeDn;

		// Token: 0x040007EE RID: 2030
		private global::DevComponents.DotNetBar.ButtonX btnX_periodLines;

		// Token: 0x040007EF RID: 2031
		private global::DevComponents.DotNetBar.ButtonX btnX_fbLines;

		// Token: 0x040007F0 RID: 2032
		private global::DevComponents.DotNetBar.ButtonX btnX_gannLines;

		// Token: 0x040007F1 RID: 2033
		private global::DevComponents.DotNetBar.ButtonX btnX_measureObj;

		// Token: 0x040007F2 RID: 2034
		private global::DevComponents.DotNetBar.ButtonX btnX_fbExtLines;

		// Token: 0x040007F3 RID: 2035
		private global::DevComponents.DotNetBar.ButtonX btnX_lineHExt;

		// Token: 0x040007F4 RID: 2036
		private global::DevComponents.DotNetBar.ButtonX btnX_lineDH;

		// Token: 0x040007F5 RID: 2037
		private global::DevComponents.DotNetBar.ButtonX btnX_lineDExt;
	}
}
