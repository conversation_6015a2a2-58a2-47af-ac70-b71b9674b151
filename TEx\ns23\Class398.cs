﻿using System;
using System.Drawing;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns23
{
	// Token: 0x02000300 RID: 768
	internal sealed class Class398 : ShapeCurve
	{
		// Token: 0x0600214D RID: 8525 RVA: 0x0000D56B File Offset: 0x0000B76B
		public Class398(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}

		// Token: 0x0600214E RID: 8526 RVA: 0x000E4414 File Offset: 0x000E2614
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			StickItem stickItem = zedGraphControl_0.GraphPane.AddStick(base.IndData.Name, base.DataView, color_0);
			stickItem.Color = color_0;
			this.curveItem_0 = stickItem;
			stickItem.Tag = string_0 + "_" + base.IndData.Name;
			base.method_3(string_0, stickItem);
		}
	}
}
