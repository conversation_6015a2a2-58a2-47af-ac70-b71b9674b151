﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns13;
using ns28;
using TEx;
using TEx.ImportTrans;
using TEx.Trading;
using TEx.Util;

namespace ns22
{
	// Token: 0x020000A0 RID: 160
	internal sealed partial class CreateCfmmcAcctFrm : Form
	{
		// Token: 0x14000022 RID: 34
		// (add) Token: 0x06000551 RID: 1361 RVA: 0x00029000 File Offset: 0x00027200
		// (remove) Token: 0x06000552 RID: 1362 RVA: 0x00029038 File Offset: 0x00027238
		public event EventHandler RecAdded
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000553 RID: 1363 RVA: 0x00029070 File Offset: 0x00027270
		protected void vmethod_0()
		{
			EventArgs e = new EventArgs();
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x14000023 RID: 35
		// (add) Token: 0x06000554 RID: 1364 RVA: 0x00029098 File Offset: 0x00027298
		// (remove) Token: 0x06000555 RID: 1365 RVA: 0x000290D0 File Offset: 0x000272D0
		public event EventHandler RecUpdated
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000556 RID: 1366 RVA: 0x00029108 File Offset: 0x00027308
		protected void vmethod_1()
		{
			EventArgs e = new EventArgs();
			EventHandler eventHandler = this.eventHandler_1;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x06000557 RID: 1367 RVA: 0x00004541 File Offset: 0x00002741
		public CreateCfmmcAcctFrm(CfmmcAcct cfmmcAcct_1) : this()
		{
			this.method_0(cfmmcAcct_1);
		}

		// Token: 0x06000558 RID: 1368 RVA: 0x00004552 File Offset: 0x00002752
		public CreateCfmmcAcctFrm()
		{
			this.InitializeComponent();
			this.method_1();
			this.button_OK.Click += this.button_OK_Click;
			this.button_Cancel.Focus();
		}

		// Token: 0x06000559 RID: 1369 RVA: 0x00029130 File Offset: 0x00027330
		private void method_0(CfmmcAcct cfmmcAcct_1)
		{
			if (cfmmcAcct_1 != null)
			{
				this.cfmmcAcct_0 = cfmmcAcct_1;
				this.txtBox_UsrID.Text = cfmmcAcct_1.ID;
				this.txtBox_UsrID.Enabled = false;
				this.txtBox_Password.Text = cfmmcAcct_1.Password;
				this.txtBox_Notes.Text = cfmmcAcct_1.Note;
				using (List<BindingAcct>.Enumerator enumerator = cfmmcAcct_1.BindingAccts.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						CreateCfmmcAcctFrm.Class61 @class = new CreateCfmmcAcctFrm.Class61();
						@class.bindingAcct_0 = enumerator.Current;
						if (Base.Acct.CurrAccounts.Where(new Func<Account, bool>(@class.method_0)).Any<Account>())
						{
							for (int i = 0; i < this.chkListBox_Accts.Items.Count; i++)
							{
								Account account = (this.chkListBox_Accts.Items[i] as ComboBoxItem).Value as Account;
								if (account.UserName == @class.bindingAcct_0.UsrName && account.ID == @class.bindingAcct_0.Id)
								{
									this.chkListBox_Accts.SetItemChecked(i, true);
									break;
								}
							}
						}
					}
				}
			}
		}

		// Token: 0x0600055A RID: 1370 RVA: 0x00029274 File Offset: 0x00027474
		private void method_1()
		{
			foreach (Account account in Base.Acct.CurrAccounts)
			{
				ComboBoxItem comboBoxItem = new ComboBoxItem();
				comboBoxItem.Text = account.AcctName;
				bool isChecked = false;
				if (account.ID == Base.Acct.CurrAccount.ID)
				{
					ComboBoxItem comboBoxItem2 = comboBoxItem;
					comboBoxItem2.Text += "(当前账户)";
				}
				comboBoxItem.Value = account;
				this.chkListBox_Accts.Items.Add(comboBoxItem, isChecked);
			}
		}

		// Token: 0x0600055B RID: 1371 RVA: 0x00029318 File Offset: 0x00027518
		private void button_OK_Click(object sender, EventArgs e)
		{
			string text = this.txtBox_UsrID.Text;
			string text2 = this.txtBox_Password.Text;
			if (this.CfmmcAcctInEdit == null && (string.IsNullOrEmpty(text) || text.Length < 8))
			{
				MessageBox.Show("请正确输入查询账号！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				this.txtBox_UsrID.Focus();
			}
			else if (string.IsNullOrEmpty(text2))
			{
				MessageBox.Show("请正确输入密码！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				this.txtBox_Password.Focus();
			}
			else
			{
				List<BindingAcct> list = new List<BindingAcct>();
				foreach (object obj in this.chkListBox_Accts.CheckedIndices)
				{
					int index = (int)obj;
					Account account = (this.chkListBox_Accts.Items[index] as ComboBoxItem).Value as Account;
					BindingAcct item = new BindingAcct(account.UserName, account.ID);
					list.Add(item);
				}
				if (!list.Any<BindingAcct>())
				{
					MessageBox.Show("请选择至少一个本地账户以存储查询到的监控中心交易记录！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					this.chkListBox_Accts.Focus();
				}
				else if (this.CfmmcAcctInEdit != null)
				{
					this.method_2(this.CfmmcAcctInEdit, text, text2, list);
					this.vmethod_1();
					MessageBox.Show("已成功更新监控中心查询账号信息！", "确定", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					base.Close();
				}
				else
				{
					CfmmcAcct cfmmcAcct_ = new CfmmcAcct();
					if (!string.IsNullOrEmpty(text) && !string.IsNullOrEmpty(text2))
					{
						this.method_2(cfmmcAcct_, text, text2, list);
						this.vmethod_0();
						MessageBox.Show("已成功添加监控中心查询账号！", "确定", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
						base.Close();
					}
				}
			}
		}

		// Token: 0x0600055C RID: 1372 RVA: 0x000294B8 File Offset: 0x000276B8
		private void method_2(CfmmcAcct cfmmcAcct_1, string string_0, string string_1, List<BindingAcct> list_0)
		{
			cfmmcAcct_1.ID = string_0;
			cfmmcAcct_1.Password = string_1;
			cfmmcAcct_1.BindingAccts = list_0;
			if (!string.IsNullOrEmpty(this.txtBox_Notes.Text))
			{
				cfmmcAcct_1.Note = this.txtBox_Notes.Text;
			}
			Base.UI.smethod_177("正在更新账户信息...", this.method_3());
			Class463.smethod_0(cfmmcAcct_1);
			Class463.smethod_24(cfmmcAcct_1);
			Base.UI.smethod_178();
		}

		// Token: 0x0600055D RID: 1373 RVA: 0x00029524 File Offset: 0x00027724
		private Point? method_3()
		{
			Point? result = null;
			if (base.Visible)
			{
				result = new Point?(new Point(base.Location.X + base.Width / 2, base.Location.Y + base.Height / 2));
			}
			return result;
		}

		// Token: 0x17000109 RID: 265
		// (get) Token: 0x0600055E RID: 1374 RVA: 0x00029580 File Offset: 0x00027780
		public CfmmcAcct CfmmcAcctInEdit
		{
			get
			{
				return this.cfmmcAcct_0;
			}
		}

		// Token: 0x0600055F RID: 1375 RVA: 0x0000458B File Offset: 0x0000278B
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0400023A RID: 570
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x0400023B RID: 571
		[CompilerGenerated]
		private EventHandler eventHandler_1;

		// Token: 0x0400023C RID: 572
		private CfmmcAcct cfmmcAcct_0;

		// Token: 0x0400023D RID: 573
		private IContainer icontainer_0;

		// Token: 0x020000A1 RID: 161
		[CompilerGenerated]
		private sealed class Class61
		{
			// Token: 0x06000562 RID: 1378 RVA: 0x00029D20 File Offset: 0x00027F20
			internal bool method_0(Account account_0)
			{
				bool result;
				if (account_0.UserName == this.bindingAcct_0.UsrName)
				{
					result = (account_0.ID == this.bindingAcct_0.Id);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400024B RID: 587
			public BindingAcct bindingAcct_0;
		}
	}
}
