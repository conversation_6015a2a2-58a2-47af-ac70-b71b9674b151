﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns28;
using ns3;
using ns6;
using TEx.Chart;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x020001C9 RID: 457
	internal sealed class ChartTickM : Class224
	{
		// Token: 0x060011E7 RID: 4583 RVA: 0x000077D5 File Offset: 0x000059D5
		public ChartTickM(ChtCtrl_Tick dChtCtrl, SplitterPanel panel) : base(dChtCtrl, panel)
		{
		}

		// Token: 0x060011E8 RID: 4584 RVA: 0x0007B448 File Offset: 0x00079648
		protected override void vmethod_0()
		{
			base.vmethod_0();
			base.ChartType = ChartType.TickMain;
			this.vmethod_2();
			this.method_78();
			this.method_79();
			this.vmethod_3();
			this.ApplyTheme(Base.UI.Form.ChartTheme);
			base.GraphPane.Y2Axis.ScaleFormatEvent += this.method_88;
			base.GraphPane.YAxis.ScaleFormatEvent += this.method_89;
			base.ZedGraphControl.AxisChange();
			base.ZedGraphControl.Refresh();
		}

		// Token: 0x060011E9 RID: 4585 RVA: 0x0007B4DC File Offset: 0x000796DC
		public override void vmethod_2()
		{
			base.IsXAxisVisible = false;
			base.vmethod_2();
			GraphPane graphPane = base.GraphPane;
			PointPairList points = new PointPairList();
			string text = "TickLine";
			this.lineItem_0 = graphPane.AddCurve(text, points, (Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.White : this.color_0, SymbolType.None);
			this.lineItem_0.Tag = text;
			PointPairList points2 = new PointPairList();
			string text2 = "TickMA";
			this.lineItem_1 = graphPane.AddCurve(text2, points2, (Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.Yellow : this.color_1, SymbolType.None);
			this.lineItem_1.Line.IsAntiAlias = true;
			this.lineItem_1.Tag = text2;
			this.double_1 = 0.0;
		}

		// Token: 0x060011EA RID: 4586 RVA: 0x000077DF File Offset: 0x000059DF
		public override void vmethod_3()
		{
			base.vmethod_3();
			GraphPane graphPane = base.GraphPane;
			graphPane.Margin.Top = 4f;
			graphPane.Margin.Bottom = 1f;
			graphPane.Margin.Right = 5f;
		}

		// Token: 0x060011EB RID: 4587 RVA: 0x0007B5A4 File Offset: 0x000797A4
		private void method_78()
		{
			GraphPane graphPane = base.GraphPane;
			this.textObj_3 = new TextObj("", 0.364, 0.015, CoordType.ChartFraction, AlignH.Left, AlignV.Top);
			base.method_45(this.textObj_3);
			graphPane.GraphObjList.Add(this.textObj_3);
			this.textObj_4 = new TextObj("分时线", 0.88, 0.015, CoordType.ChartFraction, AlignH.Right, AlignV.Top);
			base.method_45(this.textObj_4);
			this.textObj_4.FontSpec.FontColor = ((Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.White : this.color_0);
			this.textObj_5 = new TextObj("日均线", 0.998, 0.015, CoordType.ChartFraction, AlignH.Right, AlignV.Top);
			base.method_45(this.textObj_5);
			this.textObj_5.FontSpec.FontColor = ((Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.Yellow : this.color_1);
			base.HeaderTextObj.FontSpec.Size = 12f;
			graphPane.GraphObjList.Add(this.textObj_4);
			graphPane.GraphObjList.Add(this.textObj_5);
		}

		// Token: 0x060011EC RID: 4588 RVA: 0x0007B6E8 File Offset: 0x000798E8
		private void method_79()
		{
			GraphPane graphPane = base.GraphPane;
			Color color = (Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.DarkRed : Color.Silver;
			this.lineObj_0 = new LineObj(color, graphPane.XAxis.Scale.Min, 0.0, graphPane.XAxis.Scale.Max, 0.0);
			this.lineObj_0.IsClippedToChartRect = false;
			this.lineObj_0.Line.Style = DashStyle.Solid;
			this.lineObj_0.Line.Width = 2f;
			this.lineObj_0.Tag = ChartTickM.string_10;
			this.lineObj_0.ZOrder = ZOrder.E_BehindCurves;
			graphPane.GraphObjList.Add(this.lineObj_0);
		}

		// Token: 0x060011ED RID: 4589 RVA: 0x0007B7B4 File Offset: 0x000799B4
		private void method_80()
		{
			GraphPane graphPane = base.GraphPane;
			LineObj closePriceLine = this.ClosePriceLine;
			if (closePriceLine != null)
			{
				closePriceLine.Location.Y1 = this.double_0;
				closePriceLine.Location.Width = graphPane.XAxis.Scale.Max - graphPane.XAxis.Scale.Min;
			}
		}

		// Token: 0x060011EE RID: 4590 RVA: 0x0007B814 File Offset: 0x00079A14
		private void method_81(HisData hisData_0)
		{
			if (this.lineObj_1 == null)
			{
				PaneBase graphPane = base.GraphPane;
				Color color = (Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.White : this.color_0;
				this.lineObj_1 = new LineObj(color, 1.0, hisData_0.Open, 1.0, hisData_0.Close);
				this.lineObj_1.IsClippedToChartRect = false;
				this.lineObj_1.Line.Style = DashStyle.Solid;
				this.lineObj_1.Tag = ChartTickM.string_11;
				this.lineObj_1.ZOrder = ZOrder.A_InFront;
				graphPane.GraphObjList.Add(this.lineObj_1);
			}
			else
			{
				this.lineObj_1.Location.Y1 = ((hisData_0.Close > hisData_0.Open) ? hisData_0.Open : hisData_0.Close);
				this.lineObj_1.Location.Height = Math.Abs(hisData_0.Close - hisData_0.Open);
			}
		}

		// Token: 0x060011EF RID: 4591 RVA: 0x0000781E File Offset: 0x00005A1E
		public override void vmethod_4(int int_0)
		{
			this.vmethod_2();
			base.vmethod_4(int_0);
		}

		// Token: 0x060011F0 RID: 4592 RVA: 0x0000782F File Offset: 0x00005A2F
		public override void vmethod_5(HisData hisData_0)
		{
			this.method_82(hisData_0, true);
		}

		// Token: 0x060011F1 RID: 4593 RVA: 0x0007B914 File Offset: 0x00079B14
		public void method_82(HisData hisData_0, bool bool_3)
		{
			if (base.SymbDataSet.HasValidDataSet)
			{
				if (hisData_0.Date.TimeOfDay == base.SymbDataSet.CurrHisDataSet.CurrDayBeginDT.AddMinutes(1.0).TimeOfDay)
				{
					this.method_84(hisData_0);
					this.method_81(hisData_0);
					double num = Math.Abs(hisData_0.Low - this.double_0);
					double num2 = Math.Abs(hisData_0.High - this.double_0);
					this.double_2 = ((num2 > num) ? num2 : num);
					base.method_5();
				}
				double x = XDate.DateTimeToXLDate(hisData_0.Date);
				PointPair point = new PointPair(x, hisData_0.Close);
				PointPair point2 = new PointPair(x, this.method_83(hisData_0));
				this.lineItem_0.AddPoint(point);
				this.lineItem_1.AddPoint(point2);
				base.vmethod_5(hisData_0);
			}
		}

		// Token: 0x060011F2 RID: 4594 RVA: 0x0007BA04 File Offset: 0x00079C04
		private double method_83(HisData hisData_0)
		{
			double num = 0.0;
			double num2 = 0.0;
			List<HisData> chtHDList = base.ChtHDList;
			int num3 = chtHDList.IndexOf(hisData_0);
			for (int i = 0; i <= num3; i++)
			{
				HisData hisData = chtHDList[i];
				double num4 = (hisData.Volume != null) ? hisData.Volume.Value : 0.0;
				if (i > 0 && num4 > 10.0)
				{
					double? num5 = chtHDList[i - 1].Volume;
					double num6 = 0.0;
					if (num5.GetValueOrDefault() > num6 & num5 != null)
					{
						num5 = num4 / chtHDList[i - 1].Volume;
						num6 = (double)10f;
						if (num5.GetValueOrDefault() > num6 & num5 != null)
						{
							double num7 = 0.0;
							for (int j = 0; j < i; j++)
							{
								num7 += ((chtHDList[j].Volume != null) ? chtHDList[j].Volume.Value : 0.0);
							}
							double num8 = num7 / (double)i;
							if (num4 / num8 > 10.0)
							{
								num4 = num8;
							}
						}
					}
				}
				num += hisData.Close * num4;
				num2 += num4;
			}
			return num / num2;
		}

		// Token: 0x060011F3 RID: 4595 RVA: 0x0007BBB8 File Offset: 0x00079DB8
		public override void vmethod_22(HisData hisData_0)
		{
			string str = (!Base.UI.Form.IsInBlindTestMode || Base.UI.Form.IsSingleBlindTest) ? base.Symbol.CNName : "●●";
			string str2 = "";
			if (base.Symbol.IsStock)
			{
				if (Base.UI.Form.StockRestorationMethod == null)
				{
					Base.UI.Form.StockRestorationMethod = new StockRestorationMethod?(StockRestorationMethod.Prior);
				}
				StockRestorationMethod? stockRestorationMethod = Base.UI.Form.StockRestorationMethod;
				if (stockRestorationMethod.GetValueOrDefault() == StockRestorationMethod.Prior & stockRestorationMethod != null)
				{
					str2 = ",前复权";
				}
				else
				{
					stockRestorationMethod = Base.UI.Form.StockRestorationMethod;
					if (stockRestorationMethod.GetValueOrDefault() == StockRestorationMethod.Later & stockRestorationMethod != null)
					{
						str2 = ",后复权";
					}
				}
			}
			try
			{
				base.HeaderTextObj.Text = str + "<分时" + str2 + ">";
				if (base.GraphPane.YAxis.Scale.IsReverse)
				{
					TextObj headerTextObj = base.HeaderTextObj;
					headerTextObj.Text += ChtCtrl.string_0;
				}
				if (base.ChtCtrl.IsInCrossReviewMode)
				{
					hisData_0 = this.method_87();
				}
				this.textObj_3.Text = string.Concat(new string[]
				{
					"[",
					string.Format("{0:u}", hisData_0.Date).Remove(19),
					Base.UI.Form.IfShowDayOfWeek ? (" " + Utility.GetCNDayOfWeek(hisData_0.Date)) : string.Empty,
					"]    ",
					base.method_54(hisData_0.Close)
				});
				this.textObj_3.Text = base.method_52(this.textObj_3.Text);
				if (!base.HeaderTextObj.IsVisible)
				{
					base.HeaderTextObj.IsVisible = true;
				}
				if (!this.textObj_3.IsVisible)
				{
					this.textObj_3.IsVisible = true;
				}
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x060011F4 RID: 4596 RVA: 0x0007BDD0 File Offset: 0x00079FD0
		public override void vmethod_11(HisData hisData_0)
		{
			this.vmethod_22(hisData_0);
			if (base.GraphPane != null)
			{
				base.GraphPane.YAxis.Scale.MajorStepAuto = true;
				base.ZedGraphControl.AxisChange();
				this.method_85();
				this.method_80();
			}
			base.vmethod_11(hisData_0);
		}

		// Token: 0x060011F5 RID: 4597 RVA: 0x0007BE24 File Offset: 0x0007A024
		private double method_84(HisData hisData_0)
		{
			ChartTickM.Class259 @class = new ChartTickM.Class259();
			@class.chartTickM_0 = this;
			@class.hisData_0 = hisData_0;
			double result = @class.hisData_0.Open;
			SortedList<DateTime, HisData> sortedList;
			if (base.SymbDataSet.Curr1hPeriodHisData != null)
			{
				sortedList = base.SymbDataSet.Curr1hPeriodHisData.PeriodHisDataList;
			}
			else
			{
				sortedList = base.SymbDataSet.HisDataList;
			}
			if (sortedList != null)
			{
				HisData hisData = null;
				try
				{
					hisData = sortedList.LastOrDefault(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_0)).Value;
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
				if (hisData != null)
				{
					result = base.SymbDataSet.method_91(hisData).Close;
				}
			}
			this.double_0 = result;
			return result;
		}

		// Token: 0x060011F6 RID: 4598 RVA: 0x0007BEDC File Offset: 0x0007A0DC
		protected override void vmethod_18()
		{
			base.vmethod_18();
			if (base.ChtCtrl.IsInCrossReviewMode && base.ChtCtrl.RevCrossXVal != null)
			{
				HisData hisData = this.method_87();
				if (hisData != null)
				{
					GraphPane graphPane = base.GraphPane;
					int num = Convert.ToInt32(Math.Round(base.ChtCtrl.RevCrossXVal.Value));
					if (num > this.lineItem_0.NPts)
					{
						num = this.lineItem_0.NPts;
					}
					else if (num < 1)
					{
						num = 1;
					}
					PointPair pointPair = this.lineItem_1.Points[num - 1];
					int num2 = base.ChtCtrl.IndexOfLastItemShown - (this.lineItem_0.NPts - num) - 1;
					string str = base.Symbol.DigitNb.ToString();
					if (base.RevCrossYVal == null && !base.ChtCtrl.ChartsHasRevCrossYVal)
					{
						Color color = Color.Gray;
						if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
						{
							color = Color.Silver;
						}
						double close = hisData.Close;
						LineObj lineObj = new LineObj(color, graphPane.XAxis.Scale.Min, close, graphPane.XAxis.Scale.Max, close);
						lineObj.Tag = ChartBase.string_1;
						graphPane.GraphObjList.Add(lineObj);
						base.method_56(1.0, close);
						ChartBase chartBase = base.IsXAxisVisible ? this : base.ChtCtrl.ChartWithXAxisShown;
						if (chartBase != null)
						{
							string string_ = hisData.Date.ToString("g");
							string_ = base.method_52(string_);
							base.method_59(chartBase, string_, (double)num);
						}
					}
					Color color2 = (Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.White : Color.MidnightBlue;
					if (base.RevInfoBox == null)
					{
						double num3 = this.vmethod_20();
						double num4 = -num3 / 2.0;
						double num5 = 0.08;
						double num6 = 0.03;
						this.textObj_6 = base.method_62("价    位", num4, num6);
						this.textObj_7 = base.method_63(string.Format("{0:F" + str + "}", hisData.Close), 0.0, 0.11);
						this.textObj_8 = base.method_62("均    价", num4, 0.19);
						this.textObj_9 = base.method_63(string.Format("{0:F" + str + "}", pointPair.Y), 0.0, 0.27);
						this.textObj_10 = base.method_62("涨    跌", num4, 0.35);
						this.textObj_11 = base.method_63(string.Format("{0:F" + str + "}", hisData.Close - this.double_0), 0.0, 0.43000000000000005);
						this.textObj_12 = base.method_62("幅    度", num4, 0.51);
						this.textObj_13 = base.method_63(((hisData.Close - this.double_0) / this.double_0).ToString("P"), 0.0, 0.5900000000000001);
						this.textObj_14 = base.method_62("成交量", num4, 0.67);
						this.textObj_15 = base.method_63((hisData.Volume != null) ? Convert.ToInt32(hisData.Volume.Value).ToString() : "0", 0.0, num6 + num5 * 9.0);
						this.textObj_16 = base.method_62("持仓量", num4, num6 + num5 * 10.0);
						this.textObj_17 = base.method_63((!base.Symbol.IsFutures || hisData.Amount == null) ? "N/A" : Convert.ToInt32(hisData.Amount.Value).ToString(), 0.0, num6 + num5 * 11.0);
						Color color_ = (Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.Silver : Color.MidnightBlue;
						base.method_65(this.textObj_6, color_);
						base.method_65(this.textObj_8, color_);
						base.method_65(this.textObj_16, color_);
						base.method_65(this.textObj_14, color_);
						base.method_65(this.textObj_10, color_);
						base.method_65(this.textObj_12, color_);
						base.method_65(this.textObj_7, (hisData.Close - this.double_0 > 0.0) ? Color.Red : ((hisData.Close - this.double_0 == 0.0) ? color2 : Color.Green));
						base.method_65(this.textObj_9, (pointPair.Y - this.double_0 > 0.0) ? Color.Red : ((pointPair.Y - this.double_0 == 0.0) ? color2 : Color.Green));
						base.method_65(this.textObj_11, (hisData.Close - this.double_0 > 0.0) ? Color.Red : ((hisData.Close - this.double_0 == 0.0) ? color2 : Color.Green));
						base.method_65(this.textObj_13, (hisData.Close - this.double_0 > 0.0) ? Color.Red : ((hisData.Close - this.double_0 == 0.0) ? color2 : Color.Green));
						base.method_65(this.textObj_15, (Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.Yellow : Color.MidnightBlue);
						base.method_65(this.textObj_17, (Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.Yellow : Color.MidnightBlue);
						graphPane.GraphObjList.Add(this.textObj_6);
						graphPane.GraphObjList.Add(this.textObj_7);
						graphPane.GraphObjList.Add(this.textObj_8);
						graphPane.GraphObjList.Add(this.textObj_9);
						graphPane.GraphObjList.Add(this.textObj_10);
						graphPane.GraphObjList.Add(this.textObj_11);
						graphPane.GraphObjList.Add(this.textObj_12);
						graphPane.GraphObjList.Add(this.textObj_13);
						graphPane.GraphObjList.Add(this.textObj_14);
						graphPane.GraphObjList.Add(this.textObj_15);
						graphPane.GraphObjList.Add(this.textObj_16);
						graphPane.GraphObjList.Add(this.textObj_17);
						base.RevInfoBox = base.method_64(num3, 1.0);
						graphPane.GraphObjList.Add(base.RevInfoBox);
						this.vmethod_15();
					}
					else if (!base.RevInfoBox.IsVisible)
					{
						base.method_66(true);
					}
					this.textObj_7.Text = base.method_54(hisData.Close);
					this.textObj_9.Text = base.method_54(pointPair.Y);
					this.textObj_17.Text = ((!base.Symbol.IsFutures || hisData.Amount == null) ? "N/A" : Convert.ToInt32(hisData.Amount.Value).ToString());
					this.textObj_15.Text = ((hisData.Volume != null) ? Convert.ToInt32(hisData.Volume.Value).ToString() : "0");
					this.textObj_11.Text = base.method_54(hisData.Close - this.double_0);
					this.textObj_13.Text = ((hisData.Close - this.double_0) / this.double_0).ToString("P");
					this.textObj_7.FontSpec.FontColor = ((hisData.Close - this.double_0 > 0.0) ? Color.Red : ((hisData.Close - this.double_0 == 0.0) ? color2 : Color.Green));
					this.textObj_9.FontSpec.FontColor = ((pointPair.Y - this.double_0 > 0.0) ? Color.Red : ((pointPair.Y - this.double_0 == 0.0) ? color2 : Color.Green));
					this.textObj_11.FontSpec.FontColor = ((hisData.Close - this.double_0 > 0.0) ? Color.Red : ((hisData.Close - this.double_0 == 0.0) ? color2 : Color.Green));
					this.textObj_13.FontSpec.FontColor = ((hisData.Close - this.double_0 > 0.0) ? Color.Red : ((hisData.Close - this.double_0 == 0.0) ? color2 : Color.Green));
				}
			}
		}

		// Token: 0x060011F7 RID: 4599 RVA: 0x0007C898 File Offset: 0x0007AA98
		private void method_85()
		{
			if (this.lineItem_0.NPts >= 1)
			{
				GraphPane graphPane = base.GraphPane;
				double y = this.lineItem_0[this.lineItem_0.NPts - 1].Y;
				double num = Math.Abs(y - this.double_0);
				base.ZedGraphControl.AxisChange();
				double num2 = base.GraphPane.YAxis.Scale.Max / y;
				double num4;
				if (num2 <= 2.0 && num2 >= 0.5)
				{
					double num3 = base.GraphPane.YAxis.Scale.Max / 300.0;
					num4 = (base.GraphPane.YAxis.Scale.Max - base.GraphPane.YAxis.Scale.Min) / 10.0;
					if (num4 < num3)
					{
						num4 = num3;
					}
				}
				else
				{
					num4 = y / 500.0;
				}
				if (this.lineItem_0.NPts == 1)
				{
					graphPane.YAxis.Scale.Max = this.double_0 + num + num4;
					graphPane.YAxis.Scale.Min = this.double_0 - num - num4;
					this.double_1 = num;
				}
				else if (this.double_1 == 0.0)
				{
					for (int i = 0; i < this.lineItem_0.NPts; i++)
					{
						double num5 = Math.Abs(this.lineItem_0[i].Y - this.double_0);
						if (num5 > this.double_1)
						{
							this.double_1 = num5;
						}
					}
				}
				else if (num > this.double_1)
				{
					this.double_1 = num;
				}
				graphPane.YAxis.Scale.Max = this.double_0 + this.double_1 + num4;
				graphPane.YAxis.Scale.Min = this.double_0 - this.double_1 - num4;
				double num6 = base.GraphPane.YAxis.Scale.Max - base.GraphPane.YAxis.Scale.Min;
				if (num6 < this.double_2 * 2.1)
				{
					base.GraphPane.YAxis.Scale.Min -= (this.double_2 * 2.1 - num6) / 2.0;
					base.GraphPane.YAxis.Scale.Max += (this.double_2 * 2.1 - num6) / 2.0;
				}
				graphPane.YAxis.Scale.MaxAuto = false;
				graphPane.YAxis.Scale.MinAuto = false;
				base.ZedGraphControl.AxisChange();
				graphPane.Y2Axis.Scale.Max = graphPane.YAxis.Scale.Max;
				graphPane.Y2Axis.Scale.Min = graphPane.YAxis.Scale.Min;
				graphPane.YAxis.Scale.BaseTic = this.method_86();
				graphPane.Y2Axis.Scale.BaseTic = graphPane.YAxis.Scale.BaseTic;
				base.ZedGraphControl.AxisChange();
			}
		}

		// Token: 0x060011F8 RID: 4600 RVA: 0x0007CBF8 File Offset: 0x0007ADF8
		private double method_86()
		{
			GraphPane graphPane = base.GraphPane;
			double majorStep = graphPane.YAxis.Scale.MajorStep;
			graphPane.YAxis.Scale.MajorStep = majorStep;
			graphPane.Y2Axis.Scale.MajorStep = majorStep;
			return (this.double_0 - base.GraphPane.YAxis.Scale.Min) % majorStep + base.GraphPane.YAxis.Scale.Min;
		}

		// Token: 0x060011F9 RID: 4601 RVA: 0x0007CC78 File Offset: 0x0007AE78
		protected override double vmethod_20()
		{
			return (double)base.ChtCtrl.ChartRect_LeftMargin / ((double)base.GraphPane.Rect.Width - (double)base.ChtCtrl.ChartRect_LeftMargin * 1.7);
		}

		// Token: 0x060011FA RID: 4602 RVA: 0x0007CCC4 File Offset: 0x0007AEC4
		public override void ApplyTheme(ChartTheme theme)
		{
			base.ApplyTheme(theme);
			this.textObj_3.FontSpec.FontColor = base.HeaderTextObj.FontSpec.FontColor;
			if (theme == ChartTheme.Classic)
			{
				this.lineItem_0.Line.Color = Color.White;
				this.lineItem_1.Line.Color = Color.Yellow;
				this.textObj_4.FontSpec.FontColor = Color.White;
				this.textObj_5.FontSpec.FontColor = Color.Yellow;
				this.ClosePriceLine.Line.Color = Color.DarkRed;
				if (this.lineObj_1 != null)
				{
					this.lineObj_1.Line.Color = Color.White;
				}
				base.GraphPane.Y2Axis.Scale.FontSpec.FontColor = Color.Silver;
				base.GraphPane.Y2Axis.MajorGrid.Color = Color.DarkRed;
				if (this.textObj_15 != null)
				{
					this.textObj_15.FontSpec.FontColor = Color.Yellow;
					this.textObj_17.FontSpec.FontColor = Color.Yellow;
				}
			}
			else
			{
				this.lineItem_0.Line.Color = this.color_0;
				this.lineItem_1.Line.Color = this.color_1;
				if (theme == ChartTheme.Modern)
				{
					base.GraphPane.Fill = new Fill(Color.White, Class179.color_15, 90f);
				}
				else if (theme == ChartTheme.Yellow)
				{
					base.GraphPane.Fill = new Fill(Class179.color_13);
				}
				this.textObj_4.FontSpec.FontColor = this.color_0;
				this.textObj_5.FontSpec.FontColor = this.color_1;
				this.ClosePriceLine.Line.Color = Color.Silver;
				if (this.lineObj_1 != null)
				{
					this.lineObj_1.Line.Color = this.color_0;
				}
				base.GraphPane.Y2Axis.Scale.FontSpec.FontColor = Color.Black;
				base.GraphPane.Y2Axis.MajorGrid.Color = Color.LightGray;
				if (this.textObj_15 != null)
				{
					this.textObj_15.FontSpec.FontColor = Color.MidnightBlue;
					this.textObj_17.FontSpec.FontColor = Color.MidnightBlue;
				}
			}
		}

		// Token: 0x060011FB RID: 4603 RVA: 0x0007CF30 File Offset: 0x0007B130
		private HisData method_87()
		{
			HisData result = null;
			if (base.ChtCtrl.IsInCrossReviewMode)
			{
				if (base.ChtCtrl.RevCrossXVal != null)
				{
					int num = Convert.ToInt32(Math.Round(base.ChtCtrl.RevCrossXVal.Value));
					if (num > this.lineItem_0.Points.Count)
					{
						num = this.lineItem_0.Points.Count;
					}
					else if (num < 1)
					{
						num = 1;
					}
					if (num == this.lineItem_0.NPts)
					{
						result = base.SymbDataSet.CurrHisDataSet.CurrHisData;
						goto IL_D2;
					}
					try
					{
						DateTime dateTime_ = XDate.XLDateToDateTime(base.method_53(num - 1));
						result = base.SymbDataSet.CurrHisDataSet.method_12(dateTime_);
						goto IL_D2;
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
						goto IL_D2;
					}
				}
				result = base.SymbDataSet.CurrHisDataSet.CurrHisData;
			}
			IL_D2:
			return result;
		}

		// Token: 0x060011FC RID: 4604 RVA: 0x0007D028 File Offset: 0x0007B228
		private string method_88(GraphPane graphPane_0, Axis axis_0, double double_3, int int_0)
		{
			if (this.double_0 != 0.0)
			{
				double num = (double_3 - this.double_0) / this.double_0;
				if (num < 1.7976931348623157E+308)
				{
					return num.ToString("P");
				}
			}
			return string.Empty;
		}

		// Token: 0x060011FD RID: 4605 RVA: 0x0007D07C File Offset: 0x0007B27C
		private string method_89(GraphPane graphPane_0, Axis axis_0, double double_3, int int_0)
		{
			string result;
			if ((double_3 > 50.0 && double_3 < 1.7976931348623157E+308) || base.Symbol.DigitNb < 2)
			{
				result = ((double)((int)double_3)).ToString();
			}
			else
			{
				result = base.method_70(double_3);
			}
			return result;
		}

		// Token: 0x1700029E RID: 670
		// (get) Token: 0x060011FE RID: 4606 RVA: 0x0007D0CC File Offset: 0x0007B2CC
		public LineObj ClosePriceLine
		{
			get
			{
				return base.GraphPane.GraphObjList.Single(new Func<GraphObj, bool>(ChartTickM.<>c.<>9.method_0)) as LineObj;
			}
		}

		// Token: 0x1700029F RID: 671
		// (get) Token: 0x060011FF RID: 4607 RVA: 0x0007D114 File Offset: 0x0007B314
		public LineItem TickCurve
		{
			get
			{
				return this.lineItem_0;
			}
		}

		// Token: 0x04000951 RID: 2385
		private LineItem lineItem_0;

		// Token: 0x04000952 RID: 2386
		private LineItem lineItem_1;

		// Token: 0x04000953 RID: 2387
		private TextObj textObj_3;

		// Token: 0x04000954 RID: 2388
		private TextObj textObj_4;

		// Token: 0x04000955 RID: 2389
		private TextObj textObj_5;

		// Token: 0x04000956 RID: 2390
		private double double_0;

		// Token: 0x04000957 RID: 2391
		private LineObj lineObj_0;

		// Token: 0x04000958 RID: 2392
		private static readonly string string_10 = "ClosePrc";

		// Token: 0x04000959 RID: 2393
		private LineObj lineObj_1;

		// Token: 0x0400095A RID: 2394
		private static readonly string string_11 = "1stTickLine";

		// Token: 0x0400095B RID: 2395
		private TextObj textObj_6;

		// Token: 0x0400095C RID: 2396
		private TextObj textObj_7;

		// Token: 0x0400095D RID: 2397
		private TextObj textObj_8;

		// Token: 0x0400095E RID: 2398
		private TextObj textObj_9;

		// Token: 0x0400095F RID: 2399
		private TextObj textObj_10;

		// Token: 0x04000960 RID: 2400
		private TextObj textObj_11;

		// Token: 0x04000961 RID: 2401
		private TextObj textObj_12;

		// Token: 0x04000962 RID: 2402
		private TextObj textObj_13;

		// Token: 0x04000963 RID: 2403
		private TextObj textObj_14;

		// Token: 0x04000964 RID: 2404
		private TextObj textObj_15;

		// Token: 0x04000965 RID: 2405
		private TextObj textObj_16;

		// Token: 0x04000966 RID: 2406
		private TextObj textObj_17;

		// Token: 0x04000967 RID: 2407
		private double double_1;

		// Token: 0x04000968 RID: 2408
		private double double_2;

		// Token: 0x020001CA RID: 458
		[CompilerGenerated]
		private sealed class Class259
		{
			// Token: 0x06001202 RID: 4610 RVA: 0x0007D12C File Offset: 0x0007B32C
			internal bool method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				bool result;
				if (keyValuePair_0.Key.TimeOfDay == this.chartTickM_0.SymbDataSet.CurrHisDataSet.CurrExchgOBT.DayCloseTime.Value.TimeOfDay)
				{
					result = (keyValuePair_0.Key < this.hisData_0.Date);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000969 RID: 2409
			public ChartTickM chartTickM_0;

			// Token: 0x0400096A RID: 2410
			public HisData hisData_0;
		}
	}
}
