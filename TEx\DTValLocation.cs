﻿using System;

namespace TEx
{
	// Token: 0x020001D6 RID: 470
	[Serializable]
	public sealed class DTValLocation
	{
		// Token: 0x06001262 RID: 4706 RVA: 0x00002D25 File Offset: 0x00000F25
		public DTValLocation()
		{
		}

		// Token: 0x06001263 RID: 4707 RVA: 0x00007A27 File Offset: 0x00005C27
		public DTValLocation(DateTime x1DT, double y1Val, DateTime x2DT, double y2Val)
		{
			this.X1DateTime = x1DT;
			this.Y1Value = y1Val;
			this.X2DateTime = x2DT;
			this.Y2Value = y2Val;
		}

		// Token: 0x170002B8 RID: 696
		// (get) Token: 0x06001264 RID: 4708 RVA: 0x0007F024 File Offset: 0x0007D224
		// (set) Token: 0x06001265 RID: 4709 RVA: 0x00007A4E File Offset: 0x00005C4E
		public DateTime X1DateTime
		{
			get
			{
				return this._X1DateTime;
			}
			set
			{
				this._X1DateTime = value;
			}
		}

		// Token: 0x170002B9 RID: 697
		// (get) Token: 0x06001266 RID: 4710 RVA: 0x0007F03C File Offset: 0x0007D23C
		// (set) Token: 0x06001267 RID: 4711 RVA: 0x00007A59 File Offset: 0x00005C59
		public double Y1Value
		{
			get
			{
				return this._Y1Value;
			}
			set
			{
				this._Y1Value = value;
			}
		}

		// Token: 0x170002BA RID: 698
		// (get) Token: 0x06001268 RID: 4712 RVA: 0x0007F054 File Offset: 0x0007D254
		// (set) Token: 0x06001269 RID: 4713 RVA: 0x00007A64 File Offset: 0x00005C64
		public DateTime X2DateTime
		{
			get
			{
				return this._X2DateTime;
			}
			set
			{
				this._X2DateTime = value;
			}
		}

		// Token: 0x170002BB RID: 699
		// (get) Token: 0x0600126A RID: 4714 RVA: 0x0007F06C File Offset: 0x0007D26C
		// (set) Token: 0x0600126B RID: 4715 RVA: 0x00007A6F File Offset: 0x00005C6F
		public double Y2Value
		{
			get
			{
				return this._Y2Value;
			}
			set
			{
				this._Y2Value = value;
			}
		}

		// Token: 0x170002BC RID: 700
		// (get) Token: 0x0600126C RID: 4716 RVA: 0x0007F084 File Offset: 0x0007D284
		public bool IfPtsOverlap
		{
			get
			{
				bool result;
				if (this.X1DateTime == this.X2DateTime)
				{
					result = (this.Y1Value == this.Y2Value);
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x0600126D RID: 4717 RVA: 0x0007F0BC File Offset: 0x0007D2BC
		public DTValLocation method_0()
		{
			return new DTValLocation(this._X1DateTime, this._Y1Value, this._X2DateTime, this._Y2Value);
		}

		// Token: 0x04000998 RID: 2456
		private DateTime _X1DateTime;

		// Token: 0x04000999 RID: 2457
		private double _Y1Value;

		// Token: 0x0400099A RID: 2458
		private DateTime _X2DateTime;

		// Token: 0x0400099B RID: 2459
		private double _Y2Value;
	}
}
