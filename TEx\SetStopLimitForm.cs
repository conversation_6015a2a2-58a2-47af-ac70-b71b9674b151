﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns28;
using TEx.Comn;
using TEx.Trading;

namespace TEx
{
	// Token: 0x0200029E RID: 670
	internal sealed partial class SetStopLimitForm : Form
	{
		// Token: 0x1400009E RID: 158
		// (add) Token: 0x06001DA8 RID: 7592 RVA: 0x000C9628 File Offset: 0x000C7828
		// (remove) Token: 0x06001DA9 RID: 7593 RVA: 0x000C9660 File Offset: 0x000C7860
		public event EventHandler StopLimitUpdated
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06001DAA RID: 7594 RVA: 0x0000C6EF File Offset: 0x0000A8EF
		protected void method_0()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x06001DAB RID: 7595 RVA: 0x0000C70A File Offset: 0x0000A90A
		public SetStopLimitForm()
		{
			this.InitializeComponent();
		}

		// Token: 0x06001DAC RID: 7596 RVA: 0x000C9698 File Offset: 0x000C7898
		private void SetStopLimitForm_Load(object sender, EventArgs e)
		{
			if (base.Tag is ShownOpenTrans)
			{
				this.transaction_0 = (base.Tag as ShownOpenTrans);
			}
			else if (base.Tag is Transaction)
			{
				this.transaction_0 = (base.Tag as Transaction);
			}
			this.method_1();
			this.method_2();
			this.button_Del.Enabled = false;
			this.button_Edit.Enabled = false;
			if (Base.UI.Form.IsInBlindTestMode && !Base.UI.Form.IsSingleBlindTest)
			{
				this.label_Syml.Text = "●●";
			}
			else
			{
				StkSymbol stkSymbol = SymbMgr.smethod_3(this.transaction_0.SymbolID);
				this.label_Syml.Text = stkSymbol.CNName + "(" + stkSymbol.Code + ")";
			}
			base.Deactivate += this.SetStopLimitForm_Deactivate;
			base.Activated += this.SetStopLimitForm_Activated;
		}

		// Token: 0x06001DAD RID: 7597 RVA: 0x0000601C File Offset: 0x0000421C
		private void SetStopLimitForm_Deactivate(object sender, EventArgs e)
		{
			base.TopMost = false;
		}

		// Token: 0x06001DAE RID: 7598 RVA: 0x00006027 File Offset: 0x00004227
		private void SetStopLimitForm_Activated(object sender, EventArgs e)
		{
			base.TopMost = true;
		}

		// Token: 0x06001DAF RID: 7599 RVA: 0x000C9790 File Offset: 0x000C7990
		private void method_1()
		{
			if (this.transaction_0 != null)
			{
				if (this.transaction_0.ID < 0)
				{
					this.panel_TrailingStopCtrls.Visible = false;
					this.panel_GdvAndBtns.Top -= 35;
					base.Height -= 35;
				}
				if (this.transaction_0.OpenUnits != null)
				{
					this.numericUpDown_Units.Maximum = (int)this.transaction_0.OpenUnits.Value;
				}
				else
				{
					this.numericUpDown_Units.Maximum = (int)this.transaction_0.Units;
				}
				this.numericUpDown_Units.Value = this.numericUpDown_Units.Maximum;
				if (this.transaction_0 is ShownOpenTrans)
				{
					ShownOpenTrans shownOpenTrans = this.transaction_0 as ShownOpenTrans;
					if (shownOpenTrans.UsableUnits > 0L)
					{
						if (shownOpenTrans.UsableUnits <= this.numericUpDown_Units.Maximum)
						{
							this.numericUpDown_Units.Value = shownOpenTrans.UsableUnits;
						}
						else
						{
							this.numericUpDown_Units.Value = this.numericUpDown_Units.Maximum;
						}
					}
				}
				this.numericUpDown_Units.Minimum = 1m;
				this.chkBox_StopLoss.CheckedChanged += this.chkBox_StopLoss_CheckedChanged;
				this.chkBox_TakePrft.CheckedChanged += this.chkBox_TakePrft_CheckedChanged;
				this.chkBox_StopLoss.Checked = true;
				this.chkBox_TakePrft.Checked = false;
				this.numericUpDown_PrftTakePrice.Enabled = false;
				this.label_prftTake.Enabled = false;
				StkSymbol stkSymbol = Base.Acct.smethod_48(this.transaction_0.SymbolID);
				this.hisData_0 = Base.Data.smethod_52(this.transaction_0.SymbolID);
				if (this.hisData_0 == null)
				{
					throw new Exception("_currHisData值为空!");
				}
				decimal num = (stkSymbol.LeastPriceVar != null) ? stkSymbol.LeastPriceVar.Value : 1m;
				decimal num2 = Convert.ToDecimal(this.hisData_0.Close);
				decimal num3 = num2;
				decimal num4 = num * 100m;
				if (stkSymbol.AutoStopLossPoints != null)
				{
					if (this.transaction_0.TransType == 3)
					{
						num3 = this.transaction_0.Price + stkSymbol.AutoStopLossPoints.Value;
						if (num3 <= num2)
						{
							num3 = num2 + num4;
						}
					}
					else if (this.transaction_0.TransType == 1)
					{
						num3 = this.transaction_0.Price - stkSymbol.AutoStopLossPoints.Value;
						if (num3 >= num2)
						{
							num3 = num2 - num4;
						}
					}
				}
				else if (this.transaction_0.TransType == 3)
				{
					num3 = this.transaction_0.Price + num4;
					if (num3 <= num2)
					{
						num3 = num2 + num4;
					}
				}
				else if (this.transaction_0.TransType == 1)
				{
					num3 = this.transaction_0.Price - num4;
					if (num3 >= num2)
					{
						num3 = num2 - num4;
					}
				}
				decimal num5 = num2;
				if (stkSymbol.AutoLimitTakePoints != null)
				{
					if (this.transaction_0.TransType == 3)
					{
						num5 = this.transaction_0.Price - stkSymbol.AutoLimitTakePoints.Value;
						if (num5 >= num2)
						{
							num5 = num2 - num4;
						}
					}
					else if (this.transaction_0.TransType == 1)
					{
						num5 = this.transaction_0.Price + stkSymbol.AutoLimitTakePoints.Value;
						if (num5 <= num2)
						{
							num5 = num2 + num4;
						}
					}
				}
				else if (this.transaction_0.TransType == 3)
				{
					num5 = this.transaction_0.Price - num4;
					if (num5 >= num2)
					{
						num5 = num2 - num4;
					}
				}
				else if (this.transaction_0.TransType == 1)
				{
					num5 = this.transaction_0.Price + num4;
					if (num5 <= num2)
					{
						num5 = num2 + num4;
					}
				}
				this.numericUpDown_StopPrice.DecimalPlaces = stkSymbol.DigitNb;
				this.numericUpDown_StopPrice.Increment = stkSymbol.LeastPriceVar.Value;
				this.numericUpDown_PrftTakePrice.DecimalPlaces = stkSymbol.DigitNb;
				this.numericUpDown_PrftTakePrice.Increment = stkSymbol.LeastPriceVar.Value;
				num3 = Math.Round(num3, stkSymbol.DigitNb);
				num5 = Math.Round(num5, stkSymbol.DigitNb);
				decimal num6 = Math.Round(this.transaction_0.Price, stkSymbol.DigitNb);
				if (this.transaction_0.TransType == 3)
				{
					if (num2 < this.transaction_0.Price)
					{
						this.numericUpDown_StopPrice.Minimum = num2;
					}
					else
					{
						this.numericUpDown_StopPrice.Minimum = num6;
					}
					this.numericUpDown_StopPrice.Maximum = 1000000m;
					if (num3 > 0m)
					{
						this.numericUpDown_StopPrice.Value = num3;
					}
					this.numericUpDown_PrftTakePrice.Minimum = 0m;
					this.numericUpDown_PrftTakePrice.Maximum = num6;
					if (num5 > 0m)
					{
						this.numericUpDown_PrftTakePrice.Value = num5;
					}
				}
				else
				{
					this.numericUpDown_StopPrice.Minimum = 0m;
					if (num2 > this.transaction_0.Price)
					{
						this.numericUpDown_StopPrice.Maximum = num2;
					}
					else
					{
						this.numericUpDown_StopPrice.Maximum = num6;
					}
					if (num3 > 0m)
					{
						this.numericUpDown_StopPrice.Value = num3;
					}
					this.numericUpDown_PrftTakePrice.Minimum = num6;
					this.numericUpDown_PrftTakePrice.Maximum = 1000000m;
					if (num5 > 0m)
					{
						this.numericUpDown_PrftTakePrice.Value = num5;
					}
				}
				this.numericUpDown_StopPrice.ValueChanged += this.numericUpDown_StopPrice_ValueChanged;
				this.numericUpDown_PrftTakePrice.ValueChanged += this.numericUpDown_PrftTakePrice_ValueChanged;
				this.chkBox_TrailingStop.CheckedChanged += this.chkBox_TrailingStop_CheckedChanged;
				this.label_TrailingStop.Enabled = false;
				this.numericUpDown_TrailingStopPts.Enabled = false;
				this.numericUpDown_TrailingStopPts.DecimalPlaces = stkSymbol.DigitNb;
				this.numericUpDown_TrailingStopPts.Minimum = num;
				this.numericUpDown_TrailingStopPts.Maximum = 1000000m;
				this.numericUpDown_TrailingStopPts.Increment = num;
				if (num4 > 0m)
				{
					this.numericUpDown_TrailingStopPts.Value = num4;
				}
				this.button_OK.Focus();
			}
			else
			{
				Class182.smethod_0(new Exception("_soTrans should not be null!"));
			}
		}

		// Token: 0x06001DB0 RID: 7600 RVA: 0x000C9E80 File Offset: 0x000C8080
		private void method_2()
		{
			this.bindingList_0 = Base.Trading.smethod_101(this.transaction_0.ID);
			this.list_0 = new List<int>();
			this.dataGridView_SLOdr = new DataGridView();
			this.dataGridView_SLOdr.Location = new Point(28, 82);
			this.dataGridView_SLOdr.Name = "dataGridView_SLOdr";
			this.dataGridView_SLOdr.Size = new Size(350, 115);
			this.dataGridView_SLOdr.RowTemplate.Height = 22;
			this.dataGridView_SLOdr.DataSource = this.bindingList_0;
			this.panel1.Controls.Add(this.dataGridView_SLOdr);
			this.method_3(this.dataGridView_SLOdr);
			this.dataGridView_SLOdr.Columns[0].Visible = false;
			this.dataGridView_SLOdr.Columns[1].Visible = false;
			this.dataGridView_SLOdr.Columns[2].Visible = false;
			this.dataGridView_SLOdr.Columns[3].Visible = false;
			this.dataGridView_SLOdr.Columns[4].Visible = true;
			this.dataGridView_SLOdr.Columns[5].Visible = true;
			this.dataGridView_SLOdr.Columns[6].Visible = true;
			this.dataGridView_SLOdr.Columns[7].Visible = false;
			this.dataGridView_SLOdr.Columns[8].Visible = false;
			this.dataGridView_SLOdr.Columns[9].Visible = false;
			this.dataGridView_SLOdr.Columns[10].Visible = false;
			this.dataGridView_SLOdr.Columns[11].Visible = false;
			this.dataGridView_SLOdr.Columns[4].HeaderText = "数量";
			this.dataGridView_SLOdr.Columns[5].HeaderText = "止损价格";
			this.dataGridView_SLOdr.Columns[6].HeaderText = "止盈价格";
			int width = this.dataGridView_SLOdr.Width;
			if (this.transaction_0.ID < 0)
			{
				this.dataGridView_SLOdr.Columns[12].Visible = false;
				this.dataGridView_SLOdr.Columns[4].Width = Convert.ToInt32(Math.Round((double)width * 0.275));
				this.dataGridView_SLOdr.Columns[5].Width = Convert.ToInt32(Math.Round((double)width * 0.362));
				this.dataGridView_SLOdr.Columns[6].Width = Convert.ToInt32(Math.Round((double)width * 0.362));
			}
			else
			{
				this.dataGridView_SLOdr.Columns[12].Visible = true;
				this.dataGridView_SLOdr.Columns[12].HeaderText = "跟踪止损点数";
				this.dataGridView_SLOdr.Columns[4].Width = Convert.ToInt32(Math.Round((double)width * 0.201));
				this.dataGridView_SLOdr.Columns[5].Width = Convert.ToInt32(Math.Round((double)width * 0.262));
				this.dataGridView_SLOdr.Columns[6].Width = Convert.ToInt32(Math.Round((double)width * 0.262));
				this.dataGridView_SLOdr.Columns[12].Width = Convert.ToInt32(Math.Round((double)width * 0.272));
			}
			this.dataGridView_SLOdr.RowEnter += this.dataGridView_SLOdr_RowEnter;
			this.dataGridView_SLOdr.RowLeave += this.dataGridView_SLOdr_RowLeave;
			this.dataGridView_SLOdr.CellBeginEdit += this.dataGridView_SLOdr_CellBeginEdit;
			this.dataGridView_SLOdr.CellEndEdit += this.dataGridView_SLOdr_CellEndEdit;
			this.dataGridView_SLOdr.DataError += this.dataGridView_SLOdr_DataError;
			this.dataGridView_SLOdr.CellFormatting += this.dataGridView_SLOdr_CellFormatting;
		}

		// Token: 0x06001DB1 RID: 7601 RVA: 0x000CA2D0 File Offset: 0x000C84D0
		private void method_3(DataGridView dataGridView_0)
		{
			dataGridView_0.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;
			dataGridView_0.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
			dataGridView_0.ColumnHeadersDefaultCellStyle.WrapMode = DataGridViewTriState.False;
			dataGridView_0.ColumnHeadersDefaultCellStyle.Font = new Font("SimSun", 9f, FontStyle.Regular);
			dataGridView_0.RowHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;
			dataGridView_0.BackgroundColor = Color.FromKnownColor(KnownColor.Control);
			dataGridView_0.BorderStyle = BorderStyle.None;
			dataGridView_0.CellBorderStyle = DataGridViewCellBorderStyle.None;
			dataGridView_0.AlternatingRowsDefaultCellStyle.BackColor = Color.FloralWhite;
			dataGridView_0.DefaultCellStyle.Alignment = DataGridViewContentAlignment.BottomRight;
			dataGridView_0.RowHeadersVisible = false;
			dataGridView_0.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			dataGridView_0.AllowUserToAddRows = false;
			dataGridView_0.AllowUserToDeleteRows = false;
			dataGridView_0.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
			dataGridView_0.Dock = DockStyle.Fill;
		}

		// Token: 0x06001DB2 RID: 7602 RVA: 0x000CA388 File Offset: 0x000C8588
		private void chkBox_StopLoss_CheckedChanged(object sender, EventArgs e)
		{
			bool flag = false;
			if (this.chkBox_StopLoss.Checked)
			{
				this.numericUpDown_StopPrice.Enabled = true;
				this.label_stopLoss.Enabled = true;
				if (this.chkBox_TrailingStop.Checked)
				{
					this.chkBox_TrailingStop.Checked = false;
					flag = true;
				}
			}
			else
			{
				this.numericUpDown_StopPrice.Enabled = false;
				this.label_stopLoss.Enabled = false;
			}
			if (flag)
			{
				this.chkBox_TrailingStop_CheckedChanged(this.chkBox_TrailingStop, null);
			}
		}

		// Token: 0x06001DB3 RID: 7603 RVA: 0x000CA404 File Offset: 0x000C8604
		private void chkBox_TakePrft_CheckedChanged(object sender, EventArgs e)
		{
			if (this.chkBox_TakePrft.Checked)
			{
				this.numericUpDown_PrftTakePrice.Enabled = true;
				this.label_prftTake.Enabled = true;
			}
			else
			{
				this.numericUpDown_PrftTakePrice.Enabled = false;
				this.label_prftTake.Enabled = false;
			}
		}

		// Token: 0x06001DB4 RID: 7604 RVA: 0x000CA454 File Offset: 0x000C8654
		private void chkBox_TrailingStop_CheckedChanged(object sender, EventArgs e)
		{
			if (this.chkBox_TrailingStop.Checked)
			{
				this.numericUpDown_TrailingStopPts.Enabled = true;
				this.label_TrailingStop.Enabled = true;
				this.chkBox_StopLoss.Checked = false;
			}
			else
			{
				this.numericUpDown_TrailingStopPts.Enabled = false;
				this.label_TrailingStop.Enabled = false;
			}
			this.chkBox_StopLoss_CheckedChanged(this.chkBox_StopLoss, null);
		}

		// Token: 0x06001DB5 RID: 7605 RVA: 0x0000C71A File Offset: 0x0000A91A
		private void dataGridView_SLOdr_RowEnter(object sender, DataGridViewCellEventArgs e)
		{
			this.button_Del.Enabled = true;
			this.button_Edit.Enabled = true;
		}

		// Token: 0x06001DB6 RID: 7606 RVA: 0x0000C736 File Offset: 0x0000A936
		private void dataGridView_SLOdr_RowLeave(object sender, DataGridViewCellEventArgs e)
		{
			if (!this.button_Del.Focused)
			{
				this.button_Del.Enabled = false;
			}
			if (!this.button_Edit.Focused)
			{
				this.button_Edit.Enabled = false;
			}
		}

		// Token: 0x06001DB7 RID: 7607 RVA: 0x000CA4BC File Offset: 0x000C86BC
		private void dataGridView_SLOdr_CellBeginEdit(object sender, DataGridViewCellCancelEventArgs e)
		{
			if (e.RowIndex >= 0)
			{
				if ((this.dataGridView_SLOdr.Rows[e.RowIndex].DataBoundItem as ShownSLOrder).TrailingStopPts != null)
				{
					if (e.ColumnIndex != 12 && e.ColumnIndex != 4)
					{
						e.Cancel = true;
					}
				}
				else if (e.ColumnIndex == 12)
				{
					e.Cancel = true;
				}
			}
		}

		// Token: 0x06001DB8 RID: 7608 RVA: 0x000CA530 File Offset: 0x000C8730
		private void dataGridView_SLOdr_CellEndEdit(object sender, DataGridViewCellEventArgs e)
		{
			if (e.ColumnIndex == 4)
			{
				long num = this.bindingList_0.Select(new Func<ShownSLOrder, long>(SetStopLimitForm.<>c.<>9.method_0)).Sum();
				long? openUnits = this.transaction_0.OpenUnits;
				if (num > openUnits.GetValueOrDefault() & openUnits != null)
				{
					Base.UI.smethod_161();
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
				long num2 = -1L;
				try
				{
					num2 = Convert.ToInt64(this.dataGridView_SLOdr.Rows[e.RowIndex].Cells[e.ColumnIndex].Value);
				}
				catch
				{
					Base.UI.smethod_160();
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
				if (num2 <= 0L)
				{
					Base.UI.smethod_160();
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
			}
			if (e.ColumnIndex == 5)
			{
				decimal d = -1m;
				object value = this.dataGridView_SLOdr.Rows[e.RowIndex].Cells[e.ColumnIndex].Value;
				try
				{
					if (value != null && !string.IsNullOrEmpty(value.ToString()))
					{
						d = Convert.ToDecimal(value);
					}
					else
					{
						object value2 = this.dataGridView_SLOdr.Rows[e.RowIndex].Cells[6].Value;
						if (value2 != null && !string.IsNullOrEmpty(value2.ToString()))
						{
							return;
						}
					}
				}
				catch
				{
					Base.UI.smethod_160();
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
				if (d <= 0m)
				{
					Base.UI.smethod_160();
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
				decimal d2 = Convert.ToDecimal(this.hisData_0.Close);
				if (this.transaction_0.TransType == 1 && d >= d2)
				{
					MessageBox.Show("止损价应小于当前价！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
				if (this.transaction_0.TransType == 3 && d <= d2)
				{
					MessageBox.Show("止损价应大于当前价！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
			}
			if (e.ColumnIndex == 6)
			{
				decimal d3 = -1m;
				object value3 = this.dataGridView_SLOdr.Rows[e.RowIndex].Cells[e.ColumnIndex].Value;
				try
				{
					if (value3 != null && !string.IsNullOrEmpty(value3.ToString()))
					{
						d3 = Convert.ToDecimal(value3);
					}
					else
					{
						object value4 = this.dataGridView_SLOdr.Rows[e.RowIndex].Cells[5].Value;
						if (value4 != null && !string.IsNullOrEmpty(value4.ToString()))
						{
							return;
						}
					}
				}
				catch
				{
					Base.UI.smethod_160();
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
				if (d3 <= 0m)
				{
					Base.UI.smethod_160();
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
				decimal d4 = Convert.ToDecimal(this.hisData_0.Close);
				if (this.transaction_0.TransType == 1 && d3 <= d4)
				{
					MessageBox.Show("止盈价应大于当前价！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
				if (this.transaction_0.TransType == 3 && d3 >= d4)
				{
					MessageBox.Show("止盈价应小于当前价！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
			}
			if (e.ColumnIndex == 12)
			{
				decimal d5 = -1m;
				object value5 = this.dataGridView_SLOdr.Rows[e.RowIndex].Cells[e.ColumnIndex].Value;
				try
				{
					if (value5 == null || string.IsNullOrEmpty(value5.ToString()))
					{
						Base.UI.smethod_160();
						this.method_4(e.ColumnIndex, e.RowIndex);
						return;
					}
					d5 = Convert.ToDecimal(value5);
				}
				catch
				{
					Base.UI.smethod_160();
					this.method_4(e.ColumnIndex, e.RowIndex);
					return;
				}
				if (d5 <= 0m)
				{
					Base.UI.smethod_160();
					this.method_4(e.ColumnIndex, e.RowIndex);
				}
			}
		}

		// Token: 0x06001DB9 RID: 7609 RVA: 0x000CAA14 File Offset: 0x000C8C14
		private void method_4(int int_0, int int_1)
		{
			this.dataGridView_SLOdr.Focus();
			this.dataGridView_SLOdr.CurrentCell = this.dataGridView_SLOdr.Rows[int_1].Cells[int_0];
			this.dataGridView_SLOdr.BeginEdit(true);
		}

		// Token: 0x06001DBA RID: 7610 RVA: 0x000CAA64 File Offset: 0x000C8C64
		private void button_Add_Click(object sender, EventArgs e)
		{
			if (this.chkBox_TakePrft.Checked || this.chkBox_StopLoss.Checked || this.chkBox_TrailingStop.Checked)
			{
				decimal value = this.numericUpDown_StopPrice.Value;
				decimal value2 = this.numericUpDown_PrftTakePrice.Value;
				decimal value3 = this.numericUpDown_TrailingStopPts.Value;
				decimal d = Convert.ToDecimal(this.hisData_0.Close);
				if (this.chkBox_StopLoss.Checked)
				{
					if (value == 0m)
					{
						Base.UI.smethod_162();
						this.numericUpDown_StopPrice.Focus();
						return;
					}
					if (this.transaction_0.TransType == 1 && value >= d)
					{
						MessageBox.Show("止损价应小于当前价！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
						this.numericUpDown_StopPrice.Focus();
						return;
					}
					if (this.transaction_0.TransType == 3 && value <= d)
					{
						MessageBox.Show("止损价应大于当前价！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
						this.numericUpDown_StopPrice.Focus();
						return;
					}
				}
				if (this.chkBox_TakePrft.Checked)
				{
					if (value2 == 0m)
					{
						Base.UI.smethod_162();
						this.numericUpDown_PrftTakePrice.Focus();
						return;
					}
					if (this.transaction_0.TransType == 1 && value2 <= d)
					{
						MessageBox.Show("止盈价应大于当前价！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
						this.numericUpDown_PrftTakePrice.Focus();
						return;
					}
					if (this.transaction_0.TransType == 3 && value2 >= d)
					{
						MessageBox.Show("止盈价应小于当前价！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
						this.numericUpDown_PrftTakePrice.Focus();
						return;
					}
				}
				long num = 0L;
				long num2 = 0L;
				if (this.bindingList_0 != null)
				{
					num = this.bindingList_0.Where(new Func<ShownSLOrder, bool>(SetStopLimitForm.<>c.<>9.method_1)).Sum(new Func<ShownSLOrder, long>(SetStopLimitForm.<>c.<>9.method_2));
					num2 = this.bindingList_0.Where(new Func<ShownSLOrder, bool>(SetStopLimitForm.<>c.<>9.method_3)).Sum(new Func<ShownSLOrder, long>(SetStopLimitForm.<>c.<>9.method_4));
				}
				long num3 = Convert.ToInt64(this.numericUpDown_Units.Value);
				if (this.chkBox_StopLoss.Checked)
				{
					long num4 = num3 + num;
					long? openUnits = this.transaction_0.OpenUnits;
					if (num4 > openUnits.GetValueOrDefault() & openUnits != null)
					{
						goto IL_2EA;
					}
				}
				if (this.chkBox_TakePrft.Checked)
				{
					long num5 = num3 + num2;
					long? openUnits = this.transaction_0.OpenUnits;
					if (num5 > openUnits.GetValueOrDefault() & openUnits != null)
					{
						goto IL_2EA;
					}
				}
				ShownSLOrder shownSLOrder = new ShownSLOrder();
				shownSLOrder.SymbID = this.transaction_0.SymbolID;
				shownSLOrder.SymbCode = SymbMgr.smethod_3(this.transaction_0.SymbolID).Code;
				shownSLOrder.Units = Convert.ToInt64(this.numericUpDown_Units.Value);
				shownSLOrder.TransID = this.transaction_0.ID;
				if (this.chkBox_StopLoss.Checked)
				{
					shownSLOrder.StopPrice = new decimal?(value);
				}
				else if (this.chkBox_TrailingStop.Checked)
				{
					shownSLOrder.TrailingStopPts = new decimal?(value3);
				}
				if (this.chkBox_TakePrft.Checked)
				{
					shownSLOrder.LimitPrice = new decimal?(value2);
				}
				if (this.bindingList_0 == null)
				{
					this.bindingList_0 = new BindingList<ShownSLOrder>();
				}
				this.bindingList_0.Add(shownSLOrder);
				return;
				IL_2EA:
				Base.UI.smethod_161();
				this.numericUpDown_StopPrice.Focus();
			}
		}

		// Token: 0x06001DBB RID: 7611 RVA: 0x000CAE44 File Offset: 0x000C9044
		private void button_OK_Click(object sender, EventArgs e)
		{
			bool flag = false;
			if (this.bindingList_0 != null && this.bindingList_0.Any<ShownSLOrder>())
			{
				foreach (ShownSLOrder shownSLOrder in this.bindingList_0)
				{
					if (shownSLOrder.LimitCondOdrId == null && shownSLOrder.StopCondOdrId == null && shownSLOrder.TrailingStopCondOdrId == null)
					{
						if (shownSLOrder.StopPrice != null)
						{
							Base.Trading.smethod_85(this.transaction_0, new decimal?(shownSLOrder.StopPrice.Value), null, shownSLOrder.Units);
						}
						else if (shownSLOrder.TrailingStopPts != null)
						{
							Base.Trading.smethod_85(this.transaction_0, null, new decimal?(shownSLOrder.TrailingStopPts.Value), shownSLOrder.Units);
						}
						if (shownSLOrder.LimitPrice != null)
						{
							Base.Trading.smethod_85(this.transaction_0, new decimal?(shownSLOrder.LimitPrice.Value), null, shownSLOrder.Units);
						}
					}
					else
					{
						decimal num = Convert.ToDecimal(shownSLOrder.Units);
						if (shownSLOrder.LimitCondOdrId != null && shownSLOrder.LimitPrice != null)
						{
							Base.Trading.smethod_92(shownSLOrder.LimitCondOdrId.Value, shownSLOrder.LimitPrice.Value, 0m, num);
						}
						if (shownSLOrder.StopCondOdrId != null)
						{
							if (shownSLOrder.StopPrice != null)
							{
								Base.Trading.smethod_92(shownSLOrder.StopCondOdrId.Value, shownSLOrder.StopPrice.Value, 0m, num);
							}
						}
						else if (shownSLOrder.TrailingStopCondOdrId != null && shownSLOrder.TrailingStopPts != null)
						{
							Base.Trading.smethod_93(shownSLOrder.TrailingStopCondOdrId.Value, null, null, shownSLOrder.TrailingStopPts, num, true);
						}
					}
				}
				flag = true;
			}
			if (this.list_0 != null)
			{
				foreach (int int_ in this.list_0)
				{
					Base.Trading.smethod_94(int_, OrderStatus.Canceled);
					flag = true;
				}
			}
			if (flag)
			{
				this.method_0();
			}
			base.Dispose();
		}

		// Token: 0x06001DBC RID: 7612 RVA: 0x000CB134 File Offset: 0x000C9334
		private void button_Edit_Click(object sender, EventArgs e)
		{
			this.dataGridView_SLOdr.Focus();
			try
			{
				this.dataGridView_SLOdr.BeginEdit(true);
			}
			catch
			{
			}
		}

		// Token: 0x06001DBD RID: 7613 RVA: 0x000CB174 File Offset: 0x000C9374
		private void button_Del_Click(object sender, EventArgs e)
		{
			if (this.dataGridView_SLOdr != null)
			{
				if (this.dataGridView_SLOdr.SelectedRows.Count > 0)
				{
					using (IEnumerator enumerator = this.dataGridView_SLOdr.SelectedRows.GetEnumerator())
					{
						while (enumerator.MoveNext())
						{
							object obj = enumerator.Current;
							ShownSLOrder shownSLOrder = ((DataGridViewRow)obj).DataBoundItem as ShownSLOrder;
							if (shownSLOrder.LimitCondOdrId != null)
							{
								this.list_0.Add(shownSLOrder.LimitCondOdrId.Value);
							}
							if (shownSLOrder.StopCondOdrId != null)
							{
								this.list_0.Add(shownSLOrder.StopCondOdrId.Value);
							}
							this.bindingList_0.Remove(shownSLOrder);
						}
						goto IL_17A;
					}
				}
				if (this.dataGridView_SLOdr.SelectedCells.Count > 0)
				{
					int rowIndex = this.dataGridView_SLOdr.SelectedCells[0].RowIndex;
					ShownSLOrder shownSLOrder2 = this.dataGridView_SLOdr.Rows[rowIndex].DataBoundItem as ShownSLOrder;
					if (shownSLOrder2.LimitCondOdrId != null)
					{
						this.list_0.Add(shownSLOrder2.LimitCondOdrId.Value);
					}
					if (shownSLOrder2.StopCondOdrId != null)
					{
						this.list_0.Add(shownSLOrder2.StopCondOdrId.Value);
					}
					this.bindingList_0.Remove(shownSLOrder2);
				}
				IL_17A:
				this.dataGridView_SLOdr.Refresh();
			}
		}

		// Token: 0x06001DBE RID: 7614 RVA: 0x0000C76C File Offset: 0x0000A96C
		private void button_Cancel_Click(object sender, EventArgs e)
		{
			base.Dispose();
		}

		// Token: 0x06001DBF RID: 7615 RVA: 0x0000C776 File Offset: 0x0000A976
		private void dataGridView_SLOdr_DataError(object sender, DataGridViewDataErrorEventArgs e)
		{
			Base.UI.smethod_160();
		}

		// Token: 0x06001DC0 RID: 7616 RVA: 0x0000C77F File Offset: 0x0000A97F
		private void dataGridView_SLOdr_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
		{
			e.CellStyle.SelectionBackColor = Color.Moccasin;
			e.CellStyle.SelectionForeColor = e.CellStyle.ForeColor;
		}

		// Token: 0x06001DC1 RID: 7617 RVA: 0x0000C7A9 File Offset: 0x0000A9A9
		private void numericUpDown_StopPrice_ValueChanged(object sender, EventArgs e)
		{
			if (!this.chkBox_StopLoss.Checked)
			{
				this.chkBox_StopLoss.Checked = true;
			}
		}

		// Token: 0x06001DC2 RID: 7618 RVA: 0x0000C7C6 File Offset: 0x0000A9C6
		private void numericUpDown_PrftTakePrice_ValueChanged(object sender, EventArgs e)
		{
			if (!this.chkBox_TakePrft.Checked)
			{
				this.chkBox_TakePrft.Checked = true;
			}
		}

		// Token: 0x06001DC3 RID: 7619 RVA: 0x0000C7E3 File Offset: 0x0000A9E3
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000E92 RID: 3730
		private Transaction transaction_0;

		// Token: 0x04000E93 RID: 3731
		private BindingList<ShownSLOrder> bindingList_0;

		// Token: 0x04000E94 RID: 3732
		private DataGridView dataGridView_SLOdr;

		// Token: 0x04000E95 RID: 3733
		private List<int> list_0;

		// Token: 0x04000E96 RID: 3734
		private HisData hisData_0;

		// Token: 0x04000E97 RID: 3735
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000E98 RID: 3736
		private IContainer icontainer_0;
	}
}
