﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using System.Xml.Linq;
using TEx.Inds;

namespace TEx.SIndicator
{
	// Token: 0x0200033C RID: 828
	public sealed class UserDefineFileMgr
	{
		// Token: 0x060022E7 RID: 8935 RVA: 0x000ECD04 File Offset: 0x000EAF04
		private static void smethod_0(List<UserDefineIndGroup> list_1)
		{
			foreach (UserDefineIndGroup userDefineIndGroup in list_1)
			{
				userDefineIndGroup.UDSList.Sort();
			}
		}

		// Token: 0x060022E8 RID: 8936 RVA: 0x000ECD58 File Offset: 0x000EAF58
		private static List<UserDefineIndGroup> smethod_1()
		{
			List<UserDefineIndGroup> list = UserDefineFileMgr.smethod_8(UserDefineFileMgr.string_1);
			UserDefineFileMgr.smethod_2(list, true);
			List<UserDefineIndGroup> list_ = UserDefineFileMgr.smethod_8(UserDefineFileMgr.string_0);
			UserDefineFileMgr.smethod_2(list_, false);
			return UserDefineFileMgr.smethod_3(list_, list);
		}

		// Token: 0x060022E9 RID: 8937 RVA: 0x000ECD94 File Offset: 0x000EAF94
		private static void smethod_2(List<UserDefineIndGroup> list_1, bool bool_0)
		{
			foreach (UserDefineIndGroup userDefineIndGroup in list_1)
			{
				foreach (UserDefineIndScript userDefineIndScript in userDefineIndGroup.UDSList)
				{
					userDefineIndScript.UserIndFlag = bool_0;
				}
			}
		}

		// Token: 0x060022EA RID: 8938 RVA: 0x000ECE20 File Offset: 0x000EB020
		private static List<UserDefineIndGroup> smethod_3(List<UserDefineIndGroup> list_1, List<UserDefineIndGroup> list_2)
		{
			List<UserDefineIndGroup> list = new List<UserDefineIndGroup>(list_1);
			foreach (UserDefineIndGroup userDefineIndGroup in list_2)
			{
				UserDefineFileMgr.Class445 @class = new UserDefineFileMgr.Class445();
				@class.string_0 = userDefineIndGroup.Group;
				if (!list.Any(new Func<UserDefineIndGroup, bool>(@class.method_0)))
				{
					UserDefineIndGroup item = new UserDefineIndGroup(@class.string_0, new List<UserDefineIndScript>());
					list.Add(item);
				}
				foreach (UserDefineIndScript userDefineIndScript_ in userDefineIndGroup.UDSList)
				{
					UserDefineFileMgr.smethod_4(userDefineIndScript_, @class.string_0, list);
				}
			}
			return list;
		}

		// Token: 0x060022EB RID: 8939 RVA: 0x000ECF04 File Offset: 0x000EB104
		private static void smethod_4(UserDefineIndScript userDefineIndScript_0, string string_2, List<UserDefineIndGroup> list_1)
		{
			UserDefineFileMgr.smethod_12(string_2, list_1);
			UserDefineFileMgr.smethod_5(userDefineIndScript_0, list_1);
			foreach (UserDefineIndGroup userDefineIndGroup in list_1)
			{
				if (userDefineIndGroup.Group == string_2)
				{
					userDefineIndGroup.UDSList.Add(userDefineIndScript_0);
					UserDefineFileMgr.smethod_0(list_1);
					break;
				}
			}
		}

		// Token: 0x060022EC RID: 8940 RVA: 0x000ECF80 File Offset: 0x000EB180
		private static void smethod_5(UserDefineIndScript userDefineIndScript_0, List<UserDefineIndGroup> list_1)
		{
			foreach (UserDefineIndGroup userDefineIndGroup in list_1)
			{
				foreach (UserDefineIndScript userDefineIndScript in userDefineIndGroup.UDSList)
				{
					if (userDefineIndScript.Name == userDefineIndScript_0.Name)
					{
						userDefineIndGroup.UDSList.Remove(userDefineIndScript);
						UserDefineFileMgr.smethod_5(userDefineIndScript_0, list_1);
						return;
					}
				}
			}
		}

		// Token: 0x060022ED RID: 8941 RVA: 0x000ED030 File Offset: 0x000EB230
		private static List<UserDefineIndScript> smethod_6(List<UserDefineIndGroup> list_1)
		{
			List<UserDefineIndScript> list = new List<UserDefineIndScript>();
			foreach (UserDefineIndGroup userDefineIndGroup in list_1)
			{
				list.AddRange(userDefineIndGroup.UDSList);
			}
			return list;
		}

		// Token: 0x060022EE RID: 8942 RVA: 0x000ED090 File Offset: 0x000EB290
		private static bool smethod_7(string string_2, List<UserDefineIndGroup> list_1)
		{
			UserDefineFileMgr.Class446 @class = new UserDefineFileMgr.Class446();
			@class.string_0 = string_2;
			return UserDefineFileMgr.smethod_6(list_1).Any(new Func<UserDefineIndScript, bool>(@class.method_0));
		}

		// Token: 0x060022EF RID: 8943 RVA: 0x000ED0C8 File Offset: 0x000EB2C8
		public static List<UserDefineIndGroup> smethod_8(string string_2)
		{
			List<UserDefineIndGroup> result;
			try
			{
				XDocument xdocument_ = new XDocument();
				if (File.Exists(string_2))
				{
					xdocument_ = XDocument.Load(string_2);
				}
				else
				{
					xdocument_ = null;
				}
				result = UserDefineFileMgr.smethod_10(xdocument_);
			}
			catch (Exception)
			{
				MessageBox.Show("读取指标配置文件错误。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Hand);
				result = new List<UserDefineIndGroup>();
			}
			return result;
		}

		// Token: 0x060022F0 RID: 8944 RVA: 0x000ED128 File Offset: 0x000EB328
		public static List<UserDefineIndGroup> smethod_9(string string_2)
		{
			return UserDefineFileMgr.smethod_10(XDocument.Parse(string_2.Trim()));
		}

		// Token: 0x060022F1 RID: 8945 RVA: 0x000ED14C File Offset: 0x000EB34C
		private static List<UserDefineIndGroup> smethod_10(XDocument xdocument_0)
		{
			List<UserDefineIndGroup> list = new List<UserDefineIndGroup>();
			List<UserDefineIndGroup> result;
			try
			{
				if (xdocument_0 == null)
				{
					result = list;
				}
				else
				{
					XElement xelement = xdocument_0.Element("Namespace");
					if (xelement == null)
					{
						result = list;
					}
					else
					{
						XElement xelement2 = xelement.Element("Programs");
						if (xelement2 == null)
						{
							result = list;
						}
						else
						{
							foreach (XElement xelement3 in xelement2.Elements("Group"))
							{
								if (xelement3.Attribute("Name") == null)
								{
									throw new Exception("读取自定义指标文件时没有找到分组名称。");
								}
								string value = xelement3.Attribute("Name").Value;
								List<UserDefineIndScript> list2 = new List<UserDefineIndScript>();
								foreach (XElement xelement_ in xelement3.Elements("Program"))
								{
									UserDefineIndScript item = UserDefineFileMgr.smethod_14(xelement_);
									list2.Add(item);
								}
								UserDefineIndGroup item2 = new UserDefineIndGroup(value, list2);
								list.Add(item2);
							}
							result = list;
						}
					}
				}
			}
			catch (Exception)
			{
				result = list;
			}
			return result;
		}

		// Token: 0x060022F2 RID: 8946 RVA: 0x0000DC58 File Offset: 0x0000BE58
		public static void smethod_11(string string_2)
		{
			if (UserDefineFileMgr.smethod_12(string_2, UserDefineFileMgr.UDGList))
			{
				UserDefineFileMgr.smethod_24();
			}
		}

		// Token: 0x060022F3 RID: 8947 RVA: 0x000ED2D4 File Offset: 0x000EB4D4
		private static bool smethod_12(string string_2, List<UserDefineIndGroup> list_1)
		{
			UserDefineFileMgr.Class447 @class = new UserDefineFileMgr.Class447();
			@class.string_0 = string_2;
			bool result;
			if (list_1.Any(new Func<UserDefineIndGroup, bool>(@class.method_0)))
			{
				result = false;
			}
			else
			{
				List<UserDefineIndScript> udsList = new List<UserDefineIndScript>();
				UserDefineIndGroup item = new UserDefineIndGroup(@class.string_0, udsList);
				list_1.Add(item);
				result = true;
			}
			return result;
		}

		// Token: 0x060022F4 RID: 8948 RVA: 0x000ED328 File Offset: 0x000EB528
		public static List<UserDefineIndScript> smethod_13(string string_2)
		{
			List<UserDefineIndScript> list = new List<UserDefineIndScript>();
			foreach (UserDefineIndGroup userDefineIndGroup in UserDefineFileMgr.smethod_8(string_2))
			{
				list.AddRange(userDefineIndGroup.UDSList);
			}
			return list;
		}

		// Token: 0x060022F5 RID: 8949 RVA: 0x000ED38C File Offset: 0x000EB58C
		private static UserDefineIndScript smethod_14(XElement xelement_0)
		{
			XElement xelement = xelement_0.Element("Descript");
			string descript = (xelement != null) ? xelement.Value : "";
			XAttribute xattribute = xelement_0.Attribute("Name");
			string text = (xattribute != null) ? xattribute.Value : "";
			if (text == "")
			{
				throw new Exception("名称为空");
			}
			XAttribute xattribute2 = xelement_0.Attribute("MainK");
			bool mainK = xattribute2 == null || bool.Parse(xattribute2.Value);
			double ver = 4.5;
			XAttribute xattribute3 = xelement_0.Attribute("Ver");
			if (xattribute3 != null)
			{
				double.TryParse(xattribute3.Value, out ver);
			}
			XAttribute xattribute4 = xelement_0.Attribute("Check");
			bool check = xattribute4 == null || bool.Parse(xattribute4.Value);
			XAttribute xattribute5 = xelement_0.Attribute("File");
			if (xattribute5 != null)
			{
				string value = xattribute5.Value;
			}
			XElement xelement2 = xelement_0.Element("YLine");
			string yLine = (xelement2 != null) ? xelement2.Value : "";
			XElement xelement3 = xelement_0.Element("Instruction");
			string instruction = (xelement3 != null) ? xelement3.Value : "";
			XElement xelement4 = xelement_0.Element("Code");
			string text2 = (xelement4 != null) ? xelement4.Value : "";
			if (text2 == "")
			{
				throw new Exception("没有找到代码");
			}
			List<UserDefineParam> uparams = UserDefineFileMgr.smethod_15(xelement_0.Element("Params"));
			return new UserDefineIndScript(text, text2, uparams, mainK, descript, check, yLine, instruction)
			{
				Ver = ver
			};
		}

		// Token: 0x060022F6 RID: 8950 RVA: 0x000ED550 File Offset: 0x000EB750
		private static List<UserDefineParam> smethod_15(XElement xelement_0)
		{
			List<UserDefineParam> list = new List<UserDefineParam>();
			try
			{
				foreach (XElement xelement in xelement_0.Elements("Param"))
				{
					string value = xelement.Attribute("Name").Value;
					double max = double.Parse(xelement.Attribute("Max").Value);
					double min = double.Parse(xelement.Attribute("Min").Value);
					double value2 = double.Parse(xelement.Attribute("Value").Value);
					double step = double.Parse(xelement.Attribute("Step").Value);
					UserDefineParam item = new UserDefineParam(value, max, min, value2, step);
					list.Add(item);
				}
			}
			catch (Exception)
			{
				MessageBox.Show("读取参数错误。", "错误", MessageBoxButtons.OKCancel, MessageBoxIcon.Hand);
			}
			return list;
		}

		// Token: 0x060022F7 RID: 8951 RVA: 0x000ED674 File Offset: 0x000EB874
		private static List<NameDoubleValue> smethod_16(XElement xelement_0)
		{
			List<NameDoubleValue> list = new List<NameDoubleValue>();
			try
			{
				foreach (XElement xelement in xelement_0.Elements("Param"))
				{
					string value = xelement.Attribute("Name").Value;
					double value2 = double.Parse(xelement.Attribute("Value").Value);
					NameDoubleValue item = new NameDoubleValue(value, value2);
					list.Add(item);
				}
			}
			catch (Exception)
			{
				MessageBox.Show("读取参数错误。", "错误", MessageBoxButtons.OKCancel, MessageBoxIcon.Hand);
			}
			return list;
		}

		// Token: 0x060022F8 RID: 8952 RVA: 0x000ED738 File Offset: 0x000EB938
		private static XElement smethod_17(UserDefineIndScript userDefineIndScript_0)
		{
			XElement xelement = new XElement("Program", new object[]
			{
				new XAttribute("Name", userDefineIndScript_0.Name),
				new XAttribute("MainK", userDefineIndScript_0.MainK),
				new XAttribute("Ver", userDefineIndScript_0.Ver),
				new XAttribute("Check", userDefineIndScript_0.Check)
			});
			XElement content = new XElement("Descript", userDefineIndScript_0.Script);
			xelement.Add(content);
			XElement content2 = new XElement("YLine", userDefineIndScript_0.YLine);
			xelement.Add(content2);
			XElement content3 = new XElement("Instruction", userDefineIndScript_0.Instruction);
			xelement.Add(content3);
			XElement content4 = UserDefineFileMgr.smethod_18(userDefineIndScript_0.UserDefineParams, false);
			xelement.Add(content4);
			XElement content5 = new XElement("Code", userDefineIndScript_0.Code);
			xelement.Add(content5);
			return xelement;
		}

		// Token: 0x060022F9 RID: 8953 RVA: 0x000ED85C File Offset: 0x000EBA5C
		private static XElement smethod_18(List<UserDefineParam> list_1, bool bool_0)
		{
			XElement xelement = new XElement("Params");
			for (int i = 0; i < list_1.Count; i++)
			{
				UserDefineParam userDefineParam = list_1[i];
				if (bool_0)
				{
					XElement content = new XElement("Param", new object[]
					{
						new XAttribute("Name", userDefineParam.Name),
						new XAttribute("Value", userDefineParam.Value)
					});
					xelement.Add(content);
				}
				else
				{
					XElement content2 = new XElement("Param", new object[]
					{
						new XAttribute("Name", userDefineParam.Name),
						new XAttribute("Max", userDefineParam.Max),
						new XAttribute("Min", userDefineParam.Min),
						new XAttribute("Value", userDefineParam.Value),
						new XAttribute("Step", userDefineParam.Step)
					});
					xelement.Add(content2);
				}
			}
			return xelement;
		}

		// Token: 0x060022FA RID: 8954 RVA: 0x000ED9A4 File Offset: 0x000EBBA4
		public static string smethod_19(UserDefineIndScript userDefineIndScript_0, string string_2)
		{
			string result = "";
			foreach (UserDefineIndGroup userDefineIndGroup in UserDefineFileMgr.UDGList)
			{
				for (int i = 0; i < userDefineIndGroup.UDSList.Count; i++)
				{
					UserDefineIndScript userDefineIndScript = userDefineIndGroup.UDSList[i];
					if (userDefineIndScript.Name == userDefineIndScript_0.Name)
					{
						if (i - 1 >= 0)
						{
							result = userDefineIndGroup.UDSList[i - 1].Name;
						}
						else if (i + 1 <= userDefineIndGroup.UDSList.Count - 1)
						{
							result = userDefineIndGroup.UDSList[i + 1].Name;
						}
						userDefineIndGroup.UDSList.Remove(userDefineIndScript);
					}
				}
			}
			UserDefineFileMgr.smethod_24();
			return result;
		}

		// Token: 0x060022FB RID: 8955 RVA: 0x000EDA94 File Offset: 0x000EBC94
		public static string smethod_20(string string_2)
		{
			string result = "";
			for (int i = 0; i < UserDefineFileMgr.UDGList.Count; i++)
			{
				if (UserDefineFileMgr.UDGList[i].Group == string_2)
				{
					if (i - 1 >= 0)
					{
						result = UserDefineFileMgr.UDGList[i - 1].Group;
					}
					else if (i + 1 <= UserDefineFileMgr.UDGList.Count - 1)
					{
						result = UserDefineFileMgr.UDGList[i + 1].Group;
					}
					else
					{
						result = "";
					}
					UserDefineFileMgr.UDGList.RemoveAt(i);
					IL_87:
					UserDefineFileMgr.smethod_24();
					return result;
				}
			}
			goto IL_87;
		}

		// Token: 0x060022FC RID: 8956 RVA: 0x000EDB34 File Offset: 0x000EBD34
		private static XElement smethod_21(XDocument xdocument_0)
		{
			XElement xelement = xdocument_0.Element("Namespace");
			if (xelement == null)
			{
				xelement = new XElement("Namespace", new XAttribute("Name", "自定义指标"));
				xdocument_0.Add(xelement);
			}
			XElement xelement2 = xelement.Element("Programs");
			if (xelement2 == null)
			{
				xelement2 = new XElement("Programs");
				xelement.Add(xelement2);
			}
			return xelement2;
		}

		// Token: 0x060022FD RID: 8957 RVA: 0x000EDBB0 File Offset: 0x000EBDB0
		public static void smethod_22(UserDefineIndScript userDefineIndScript_0, string string_2)
		{
			UserDefineFileMgr.smethod_11(string_2);
			foreach (UserDefineIndGroup userDefineIndGroup in UserDefineFileMgr.UDGList)
			{
				string group = userDefineIndGroup.Group;
				int i = 0;
				while (i < userDefineIndGroup.UDSList.Count)
				{
					if (!(userDefineIndGroup.UDSList[i].Name == userDefineIndScript_0.Name))
					{
						i++;
					}
					else
					{
						if (string_2 == group)
						{
							userDefineIndGroup.UDSList[i].method_2(userDefineIndScript_0);
							break;
						}
						userDefineIndGroup.UDSList.RemoveAt(i);
						UserDefineFileMgr.smethod_23(userDefineIndScript_0, string_2);
						break;
					}
				}
			}
			UserDefineFileMgr.smethod_24();
		}

		// Token: 0x060022FE RID: 8958 RVA: 0x0000DC6E File Offset: 0x0000BE6E
		public static void smethod_23(UserDefineIndScript userDefineIndScript_0, string string_2)
		{
			UserDefineFileMgr.smethod_4(userDefineIndScript_0, string_2, UserDefineFileMgr.UDGList);
			UserDefineFileMgr.smethod_24();
		}

		// Token: 0x060022FF RID: 8959 RVA: 0x000EDC78 File Offset: 0x000EBE78
		public static void smethod_24()
		{
			List<UserDefineIndGroup> list = new List<UserDefineIndGroup>();
			foreach (UserDefineIndGroup userDefineIndGroup in UserDefineFileMgr.UDGList)
			{
				IEnumerable<UserDefineIndScript> source = userDefineIndGroup.UDSList.Where(new Func<UserDefineIndScript, bool>(UserDefineFileMgr.<>c.<>9.method_0));
				if (source.Any<UserDefineIndScript>())
				{
					UserDefineIndGroup item = new UserDefineIndGroup(userDefineIndGroup.Group, source.ToList<UserDefineIndScript>());
					list.Add(item);
				}
				else if (!userDefineIndGroup.UDSList.Any<UserDefineIndScript>())
				{
					list.Add(userDefineIndGroup);
				}
			}
			UserDefineFileMgr.smethod_25(list);
		}

		// Token: 0x06002300 RID: 8960 RVA: 0x000EDD38 File Offset: 0x000EBF38
		public static void smethod_25(List<UserDefineIndGroup> list_1)
		{
			XDocument xdocument = new XDocument();
			XElement xelement = UserDefineFileMgr.smethod_21(xdocument);
			foreach (UserDefineIndGroup userDefineIndGroup in list_1)
			{
				string group = userDefineIndGroup.Group;
				XElement xelement2 = new XElement("Group", new XAttribute("Name", group));
				xelement.Add(xelement2);
				foreach (UserDefineIndScript userDefineIndScript in userDefineIndGroup.UDSList)
				{
					if (userDefineIndScript.UserIndFlag)
					{
						XElement content = UserDefineFileMgr.smethod_17(userDefineIndScript);
						xelement2.Add(content);
					}
				}
			}
			xdocument.Save(UserDefineFileMgr.string_1);
		}

		// Token: 0x06002301 RID: 8961 RVA: 0x000EDE28 File Offset: 0x000EC028
		public static XElement smethod_26(UserDefineIndParams userDefineIndParams_0)
		{
			XElement xelement = new XElement("Program", new XAttribute("Name", userDefineIndParams_0.IndName));
			XElement content = UserDefineFileMgr.smethod_18(userDefineIndParams_0.ParamList, true);
			xelement.Add(content);
			return xelement;
		}

		// Token: 0x06002302 RID: 8962 RVA: 0x000EDE74 File Offset: 0x000EC074
		public static IndParamsValue smethod_27(XElement xelement_0)
		{
			string value = xelement_0.Attribute("Name").Value;
			List<NameDoubleValue> values = UserDefineFileMgr.smethod_16(xelement_0.Element("Params"));
			return new IndParamsValue(value, values);
		}

		// Token: 0x17000601 RID: 1537
		// (get) Token: 0x06002303 RID: 8963 RVA: 0x000EDEB8 File Offset: 0x000EC0B8
		// (set) Token: 0x06002304 RID: 8964 RVA: 0x0000DC83 File Offset: 0x0000BE83
		public static List<UserDefineIndGroup> UDGList
		{
			get
			{
				List<UserDefineIndGroup> result;
				if (UserDefineFileMgr.list_0 == null)
				{
					UserDefineFileMgr.list_0 = UserDefineFileMgr.smethod_1();
					UserDefineFileMgr.smethod_0(UserDefineFileMgr.list_0);
					result = UserDefineFileMgr.list_0;
				}
				else
				{
					result = UserDefineFileMgr.list_0;
				}
				return result;
			}
			set
			{
				UserDefineFileMgr.list_0 = value;
			}
		}

		// Token: 0x06002305 RID: 8965 RVA: 0x000EDEF4 File Offset: 0x000EC0F4
		public static void smethod_28(UserDefineIndScript userDefineIndScript_0)
		{
			foreach (UserDefineIndScript userDefineIndScript in UserDefineFileMgr.UDSList)
			{
				if (userDefineIndScript.Name == userDefineIndScript_0.Name)
				{
					userDefineIndScript.method_2(userDefineIndScript_0);
					break;
				}
			}
		}

		// Token: 0x17000602 RID: 1538
		// (get) Token: 0x06002306 RID: 8966 RVA: 0x000EDF60 File Offset: 0x000EC160
		public static List<UserDefineIndScript> UDSList
		{
			get
			{
				List<UserDefineIndScript> list = new List<UserDefineIndScript>();
				foreach (UserDefineIndGroup userDefineIndGroup in UserDefineFileMgr.UDGList)
				{
					list.AddRange(userDefineIndGroup.UDSList);
				}
				return list;
			}
		}

		// Token: 0x17000603 RID: 1539
		// (get) Token: 0x06002307 RID: 8967 RVA: 0x000EDFC4 File Offset: 0x000EC1C4
		public static List<UserDefineIndScript> UDSListChecked
		{
			get
			{
				return UserDefineFileMgr.UDSList.Where(new Func<UserDefineIndScript, bool>(UserDefineFileMgr.<>c.<>9.method_1)).ToList<UserDefineIndScript>();
			}
		}

		// Token: 0x040010EA RID: 4330
		private static readonly string string_0 = TApp.string_6 + "\\INDSYS.DAT";

		// Token: 0x040010EB RID: 4331
		private static readonly string string_1 = TApp.UserAcctFolder + "\\indusr.dat";

		// Token: 0x040010EC RID: 4332
		private static List<UserDefineIndGroup> list_0 = null;

		// Token: 0x0200033D RID: 829
		[CompilerGenerated]
		private sealed class Class445
		{
			// Token: 0x0600230B RID: 8971 RVA: 0x000EE004 File Offset: 0x000EC204
			internal bool method_0(UserDefineIndGroup userDefineIndGroup_0)
			{
				return userDefineIndGroup_0.Group == this.string_0;
			}

			// Token: 0x040010ED RID: 4333
			public string string_0;
		}

		// Token: 0x0200033E RID: 830
		[CompilerGenerated]
		private sealed class Class446
		{
			// Token: 0x0600230D RID: 8973 RVA: 0x000EE028 File Offset: 0x000EC228
			internal bool method_0(UserDefineIndScript userDefineIndScript_0)
			{
				return userDefineIndScript_0.Name == this.string_0;
			}

			// Token: 0x040010EE RID: 4334
			public string string_0;
		}

		// Token: 0x0200033F RID: 831
		[CompilerGenerated]
		private sealed class Class447
		{
			// Token: 0x0600230F RID: 8975 RVA: 0x000EE04C File Offset: 0x000EC24C
			internal bool method_0(UserDefineIndGroup userDefineIndGroup_0)
			{
				return userDefineIndGroup_0.Group == this.string_0;
			}

			// Token: 0x040010EF RID: 4335
			public string string_0;
		}
	}
}
