﻿namespace TEx
{
	// Token: 0x02000156 RID: 342
	public sealed partial class DataMgmtForm : global::System.Windows.Forms.Form
	{
		// Token: 0x06000D3C RID: 3388 RVA: 0x0004ED80 File Offset: 0x0004CF80
		private void InitializeComponent()
		{
			this.groupBox_SelSymb = new global::System.Windows.Forms.GroupBox();
			this.label2 = new global::System.Windows.Forms.Label();
			this.comboBox_HDExpSymbl = new global::System.Windows.Forms.ComboBox();
			this.label1 = new global::System.Windows.Forms.Label();
			this.comboBox_Exchg = new global::System.Windows.Forms.ComboBox();
			this.groupBox_HDExpFormat = new global::System.Windows.Forms.GroupBox();
			this.checkBox_IfCombinedDT = new global::System.Windows.Forms.CheckBox();
			this.checkBox_inclFirstLine = new global::System.Windows.Forms.CheckBox();
			this.label5 = new global::System.Windows.Forms.Label();
			this.comboBox_PeriodType = new global::System.Windows.Forms.ComboBox();
			this.comboBox_HDExpDataFormat = new global::System.Windows.Forms.ComboBox();
			this.label4 = new global::System.Windows.Forms.Label();
			this.label3 = new global::System.Windows.Forms.Label();
			this.dateTimeInput_HDExpEnd = new global::DevComponents.Editors.DateTimeAdv.DateTimeInput();
			this.dateTimeInput_HDExpStart = new global::DevComponents.Editors.DateTimeAdv.DateTimeInput();
			this.label7 = new global::System.Windows.Forms.Label();
			this.textBox_HDExpFilePath = new global::System.Windows.Forms.TextBox();
			this.btn_HDExpOpenDFolder = new global::System.Windows.Forms.Button();
			this.label6 = new global::System.Windows.Forms.Label();
			this.progressBar_HDExp = new global::System.Windows.Forms.ProgressBar();
			this.btn_HDExport = new global::System.Windows.Forms.Button();
			this.groupBox_HDExp = new global::System.Windows.Forms.GroupBox();
			this.label_HDExpStatus = new global::System.Windows.Forms.Label();
			this.tabControl1 = new global::System.Windows.Forms.TabControl();
			this.tabPage_Acct = new global::System.Windows.Forms.TabPage();
			this.groupBox4 = new global::System.Windows.Forms.GroupBox();
			this.groupBox3 = new global::System.Windows.Forms.GroupBox();
			this.comboBox_AcctToReceiveTrans = new global::System.Windows.Forms.ComboBox();
			this.btn_MoveTrans = new global::System.Windows.Forms.Button();
			this.btn_CopyTrans = new global::System.Windows.Forms.Button();
			this.label9 = new global::System.Windows.Forms.Label();
			this.btn_DelAcctTrans = new global::System.Windows.Forms.Button();
			this.comboBox_AcctToOpt = new global::System.Windows.Forms.ComboBox();
			this.label1_AcctSymbTransTotal = new global::System.Windows.Forms.Label();
			this.comboBox_AcctSymbToOpt = new global::System.Windows.Forms.ComboBox();
			this.label8 = new global::System.Windows.Forms.Label();
			this.label_AccTransTotal = new global::System.Windows.Forms.Label();
			this.checkBox_IfAllAcctSymbForOpt = new global::System.Windows.Forms.CheckBox();
			this.groupBox5 = new global::System.Windows.Forms.GroupBox();
			this.label13 = new global::System.Windows.Forms.Label();
			this.textBox_TransImpFilePath = new global::System.Windows.Forms.TextBox();
			this.btn_TransImpOpenDFolder = new global::System.Windows.Forms.Button();
			this.button_LoadTrans = new global::System.Windows.Forms.Button();
			this.groupBox2 = new global::System.Windows.Forms.GroupBox();
			this.label12 = new global::System.Windows.Forms.Label();
			this.btn_TransExp = new global::System.Windows.Forms.Button();
			this.textBox_TransExpFilePath = new global::System.Windows.Forms.TextBox();
			this.btn_TransExpOpenDFolder = new global::System.Windows.Forms.Button();
			this.tabPage_HisData = new global::System.Windows.Forms.TabPage();
			this.groupBox_HDExpDates = new global::System.Windows.Forms.GroupBox();
			this.tabPage_Cfmmc = new global::System.Windows.Forms.TabPage();
			this.pictureBox1 = new global::System.Windows.Forms.PictureBox();
			this.label_cfmmcNotice = new global::DevComponents.DotNetBar.LabelX();
			this.groupBox7 = new global::System.Windows.Forms.GroupBox();
			this.radioBtn_PeriodlyDnldCfmmc = new global::System.Windows.Forms.RadioButton();
			this.radioBtn_StartUpDnldCfmmc = new global::System.Windows.Forms.RadioButton();
			this.groupBox_CfmmcDnldTime = new global::System.Windows.Forms.GroupBox();
			this.TimePicker_AutoDown = new global::System.Windows.Forms.DateTimePicker();
			this.groupBox_CfmmcDnldFrequency = new global::System.Windows.Forms.GroupBox();
			this.comboBox_DayOfWeekDnCfmmc = new global::System.Windows.Forms.ComboBox();
			this.radio_autoWeek = new global::System.Windows.Forms.RadioButton();
			this.radio_autoDay = new global::System.Windows.Forms.RadioButton();
			this.groupBox6 = new global::System.Windows.Forms.GroupBox();
			this.groupBox1 = new global::System.Windows.Forms.GroupBox();
			this.btn_CfmmcDownBg = new global::System.Windows.Forms.Button();
			this.label_DnldCfmmcRecNb = new global::System.Windows.Forms.Label();
			this.progressBar_Cfmmc = new global::System.Windows.Forms.ProgressBar();
			this.label_CfmmcDnStatus = new global::System.Windows.Forms.Label();
			this.label_cfmmcAcctNote = new global::System.Windows.Forms.Label();
			this.btn_EditCfmmcAcct = new global::System.Windows.Forms.Button();
			this.btn_DelCfmmcAcct = new global::System.Windows.Forms.Button();
			this.btn_AddCfmmcAcct = new global::System.Windows.Forms.Button();
			this.comboBox_CfmmcAcct = new global::System.Windows.Forms.ComboBox();
			this.button_Cancel = new global::System.Windows.Forms.Button();
			this.button_OK = new global::System.Windows.Forms.Button();
			this.groupBox_SelSymb.SuspendLayout();
			this.groupBox_HDExpFormat.SuspendLayout();
			((global::System.ComponentModel.ISupportInitialize)this.dateTimeInput_HDExpEnd).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.dateTimeInput_HDExpStart).BeginInit();
			this.groupBox_HDExp.SuspendLayout();
			this.tabControl1.SuspendLayout();
			this.tabPage_Acct.SuspendLayout();
			this.groupBox4.SuspendLayout();
			this.groupBox3.SuspendLayout();
			this.groupBox5.SuspendLayout();
			this.groupBox2.SuspendLayout();
			this.tabPage_HisData.SuspendLayout();
			this.groupBox_HDExpDates.SuspendLayout();
			this.tabPage_Cfmmc.SuspendLayout();
			((global::System.ComponentModel.ISupportInitialize)this.pictureBox1).BeginInit();
			this.groupBox7.SuspendLayout();
			this.groupBox_CfmmcDnldTime.SuspendLayout();
			this.groupBox_CfmmcDnldFrequency.SuspendLayout();
			this.groupBox6.SuspendLayout();
			base.SuspendLayout();
			this.groupBox_SelSymb.Controls.Add(this.label2);
			this.groupBox_SelSymb.Controls.Add(this.comboBox_HDExpSymbl);
			this.groupBox_SelSymb.Controls.Add(this.label1);
			this.groupBox_SelSymb.Controls.Add(this.comboBox_Exchg);
			this.groupBox_SelSymb.Location = new global::System.Drawing.Point(19, 15);
			this.groupBox_SelSymb.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_SelSymb.Name = "groupBox_SelSymb";
			this.groupBox_SelSymb.Padding = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_SelSymb.Size = new global::System.Drawing.Size(300, 155);
			this.groupBox_SelSymb.TabIndex = 0;
			this.groupBox_SelSymb.TabStop = false;
			this.groupBox_SelSymb.Text = "选择品种";
			this.label2.AutoSize = true;
			this.label2.Location = new global::System.Drawing.Point(39, 82);
			this.label2.Name = "label2";
			this.label2.Size = new global::System.Drawing.Size(52, 15);
			this.label2.TabIndex = 3;
			this.label2.Text = "品种：";
			this.comboBox_HDExpSymbl.FormattingEnabled = true;
			this.comboBox_HDExpSymbl.Location = new global::System.Drawing.Point(42, 102);
			this.comboBox_HDExpSymbl.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.comboBox_HDExpSymbl.Name = "comboBox_HDExpSymbl";
			this.comboBox_HDExpSymbl.Size = new global::System.Drawing.Size(216, 23);
			this.comboBox_HDExpSymbl.TabIndex = 2;
			this.label1.AutoSize = true;
			this.label1.Location = new global::System.Drawing.Point(39, 28);
			this.label1.Name = "label1";
			this.label1.Size = new global::System.Drawing.Size(67, 15);
			this.label1.TabIndex = 1;
			this.label1.Text = "交易所：";
			this.comboBox_Exchg.FormattingEnabled = true;
			this.comboBox_Exchg.Location = new global::System.Drawing.Point(42, 48);
			this.comboBox_Exchg.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.comboBox_Exchg.Name = "comboBox_Exchg";
			this.comboBox_Exchg.Size = new global::System.Drawing.Size(216, 23);
			this.comboBox_Exchg.TabIndex = 0;
			this.groupBox_HDExpFormat.Controls.Add(this.checkBox_IfCombinedDT);
			this.groupBox_HDExpFormat.Controls.Add(this.checkBox_inclFirstLine);
			this.groupBox_HDExpFormat.Controls.Add(this.label5);
			this.groupBox_HDExpFormat.Controls.Add(this.comboBox_PeriodType);
			this.groupBox_HDExpFormat.Controls.Add(this.comboBox_HDExpDataFormat);
			this.groupBox_HDExpFormat.Controls.Add(this.label4);
			this.groupBox_HDExpFormat.Location = new global::System.Drawing.Point(342, 15);
			this.groupBox_HDExpFormat.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_HDExpFormat.Name = "groupBox_HDExpFormat";
			this.groupBox_HDExpFormat.Padding = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_HDExpFormat.Size = new global::System.Drawing.Size(319, 155);
			this.groupBox_HDExpFormat.TabIndex = 13;
			this.groupBox_HDExpFormat.TabStop = false;
			this.groupBox_HDExpFormat.Text = "导出设置";
			this.checkBox_IfCombinedDT.AutoSize = true;
			this.checkBox_IfCombinedDT.Location = new global::System.Drawing.Point(113, 121);
			this.checkBox_IfCombinedDT.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.checkBox_IfCombinedDT.Name = "checkBox_IfCombinedDT";
			this.checkBox_IfCombinedDT.Size = new global::System.Drawing.Size(164, 19);
			this.checkBox_IfCombinedDT.TabIndex = 11;
			this.checkBox_IfCombinedDT.Text = "日期与时间字段合并";
			this.checkBox_IfCombinedDT.UseVisualStyleBackColor = true;
			this.checkBox_inclFirstLine.AutoSize = true;
			this.checkBox_inclFirstLine.Location = new global::System.Drawing.Point(113, 96);
			this.checkBox_inclFirstLine.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.checkBox_inclFirstLine.Name = "checkBox_inclFirstLine";
			this.checkBox_inclFirstLine.Size = new global::System.Drawing.Size(149, 19);
			this.checkBox_inclFirstLine.TabIndex = 10;
			this.checkBox_inclFirstLine.Text = "包含字段名头记录";
			this.checkBox_inclFirstLine.UseVisualStyleBackColor = true;
			this.label5.AutoSize = true;
			this.label5.Location = new global::System.Drawing.Point(24, 31);
			this.label5.Name = "label5";
			this.label5.Size = new global::System.Drawing.Size(82, 15);
			this.label5.TabIndex = 5;
			this.label5.Text = "数据周期：";
			this.comboBox_PeriodType.FormattingEnabled = true;
			this.comboBox_PeriodType.Location = new global::System.Drawing.Point(112, 29);
			this.comboBox_PeriodType.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.comboBox_PeriodType.Name = "comboBox_PeriodType";
			this.comboBox_PeriodType.Size = new global::System.Drawing.Size(177, 23);
			this.comboBox_PeriodType.TabIndex = 4;
			this.comboBox_HDExpDataFormat.FormattingEnabled = true;
			this.comboBox_HDExpDataFormat.Location = new global::System.Drawing.Point(112, 61);
			this.comboBox_HDExpDataFormat.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.comboBox_HDExpDataFormat.Name = "comboBox_HDExpDataFormat";
			this.comboBox_HDExpDataFormat.Size = new global::System.Drawing.Size(177, 23);
			this.comboBox_HDExpDataFormat.TabIndex = 9;
			this.label4.AutoSize = true;
			this.label4.Location = new global::System.Drawing.Point(24, 63);
			this.label4.Name = "label4";
			this.label4.Size = new global::System.Drawing.Size(82, 15);
			this.label4.TabIndex = 8;
			this.label4.Text = "导出格式：";
			this.label3.AutoSize = true;
			this.label3.Location = new global::System.Drawing.Point(27, 35);
			this.label3.Name = "label3";
			this.label3.Size = new global::System.Drawing.Size(82, 15);
			this.label3.TabIndex = 0;
			this.label3.Text = "起始日期：";
			this.dateTimeInput_HDExpEnd.BackgroundStyle.Class = "DateTimeInputBackground";
			this.dateTimeInput_HDExpEnd.BackgroundStyle.CornerType = global::DevComponents.DotNetBar.eCornerType.Square;
			this.dateTimeInput_HDExpEnd.ButtonDropDown.Shortcut = global::DevComponents.DotNetBar.eShortcut.AltDown;
			this.dateTimeInput_HDExpEnd.ButtonDropDown.Visible = true;
			this.dateTimeInput_HDExpEnd.Format = global::DevComponents.Editors.eDateTimePickerFormat.Long;
			this.dateTimeInput_HDExpEnd.IsPopupCalendarOpen = false;
			this.dateTimeInput_HDExpEnd.Location = new global::System.Drawing.Point(446, 30);
			this.dateTimeInput_HDExpEnd.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.dateTimeInput_HDExpEnd.MonthCalendar.AnnuallyMarkedDates = new global::System.DateTime[0];
			this.dateTimeInput_HDExpEnd.MonthCalendar.BackgroundStyle.BackColor = global::System.Drawing.SystemColors.Window;
			this.dateTimeInput_HDExpEnd.MonthCalendar.BackgroundStyle.Class = "";
			this.dateTimeInput_HDExpEnd.MonthCalendar.BackgroundStyle.CornerType = global::DevComponents.DotNetBar.eCornerType.Square;
			this.dateTimeInput_HDExpEnd.MonthCalendar.CommandsBackgroundStyle.BackColor2SchemePart = global::DevComponents.DotNetBar.eColorSchemePart.BarBackground2;
			this.dateTimeInput_HDExpEnd.MonthCalendar.CommandsBackgroundStyle.BackColorGradientAngle = 90;
			this.dateTimeInput_HDExpEnd.MonthCalendar.CommandsBackgroundStyle.BackColorSchemePart = global::DevComponents.DotNetBar.eColorSchemePart.BarBackground;
			this.dateTimeInput_HDExpEnd.MonthCalendar.CommandsBackgroundStyle.BorderTop = global::DevComponents.DotNetBar.eStyleBorderType.Solid;
			this.dateTimeInput_HDExpEnd.MonthCalendar.CommandsBackgroundStyle.BorderTopColorSchemePart = global::DevComponents.DotNetBar.eColorSchemePart.BarDockedBorder;
			this.dateTimeInput_HDExpEnd.MonthCalendar.CommandsBackgroundStyle.BorderTopWidth = 1;
			this.dateTimeInput_HDExpEnd.MonthCalendar.CommandsBackgroundStyle.Class = "";
			this.dateTimeInput_HDExpEnd.MonthCalendar.CommandsBackgroundStyle.CornerType = global::DevComponents.DotNetBar.eCornerType.Square;
			this.dateTimeInput_HDExpEnd.MonthCalendar.DaySize = new global::System.Drawing.Size(30, 20);
			this.dateTimeInput_HDExpEnd.MonthCalendar.DisplayMonth = new global::System.DateTime(2013, 9, 1, 0, 0, 0, 0);
			this.dateTimeInput_HDExpEnd.MonthCalendar.MarkedDates = new global::System.DateTime[0];
			this.dateTimeInput_HDExpEnd.MonthCalendar.MonthlyMarkedDates = new global::System.DateTime[0];
			this.dateTimeInput_HDExpEnd.MonthCalendar.NavigationBackgroundStyle.BackColor2SchemePart = global::DevComponents.DotNetBar.eColorSchemePart.PanelBackground2;
			this.dateTimeInput_HDExpEnd.MonthCalendar.NavigationBackgroundStyle.BackColorGradientAngle = 90;
			this.dateTimeInput_HDExpEnd.MonthCalendar.NavigationBackgroundStyle.BackColorSchemePart = global::DevComponents.DotNetBar.eColorSchemePart.PanelBackground;
			this.dateTimeInput_HDExpEnd.MonthCalendar.NavigationBackgroundStyle.Class = "";
			this.dateTimeInput_HDExpEnd.MonthCalendar.NavigationBackgroundStyle.CornerType = global::DevComponents.DotNetBar.eCornerType.Square;
			this.dateTimeInput_HDExpEnd.MonthCalendar.WeeklyMarkedDays = new global::System.DayOfWeek[0];
			this.dateTimeInput_HDExpEnd.Name = "dateTimeInput_HDExpEnd";
			this.dateTimeInput_HDExpEnd.Size = new global::System.Drawing.Size(170, 26);
			this.dateTimeInput_HDExpEnd.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.dateTimeInput_HDExpEnd.TabIndex = 7;
			this.dateTimeInput_HDExpStart.BackgroundStyle.Class = "DateTimeInputBackground";
			this.dateTimeInput_HDExpStart.BackgroundStyle.CornerType = global::DevComponents.DotNetBar.eCornerType.Square;
			this.dateTimeInput_HDExpStart.ButtonClear.Text = "清除";
			this.dateTimeInput_HDExpStart.ButtonDropDown.Shortcut = global::DevComponents.DotNetBar.eShortcut.AltDown;
			this.dateTimeInput_HDExpStart.ButtonDropDown.Visible = true;
			this.dateTimeInput_HDExpStart.Format = global::DevComponents.Editors.eDateTimePickerFormat.Long;
			this.dateTimeInput_HDExpStart.IsPopupCalendarOpen = false;
			this.dateTimeInput_HDExpStart.Location = new global::System.Drawing.Point(113, 30);
			this.dateTimeInput_HDExpStart.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.dateTimeInput_HDExpStart.MonthCalendar.AnnuallyMarkedDates = new global::System.DateTime[0];
			this.dateTimeInput_HDExpStart.MonthCalendar.BackgroundStyle.BackColor = global::System.Drawing.SystemColors.Window;
			this.dateTimeInput_HDExpStart.MonthCalendar.BackgroundStyle.Class = "";
			this.dateTimeInput_HDExpStart.MonthCalendar.BackgroundStyle.CornerType = global::DevComponents.DotNetBar.eCornerType.Square;
			this.dateTimeInput_HDExpStart.MonthCalendar.CommandsBackgroundStyle.BackColor2SchemePart = global::DevComponents.DotNetBar.eColorSchemePart.BarBackground2;
			this.dateTimeInput_HDExpStart.MonthCalendar.CommandsBackgroundStyle.BackColorGradientAngle = 90;
			this.dateTimeInput_HDExpStart.MonthCalendar.CommandsBackgroundStyle.BackColorSchemePart = global::DevComponents.DotNetBar.eColorSchemePart.BarBackground;
			this.dateTimeInput_HDExpStart.MonthCalendar.CommandsBackgroundStyle.BorderTop = global::DevComponents.DotNetBar.eStyleBorderType.Solid;
			this.dateTimeInput_HDExpStart.MonthCalendar.CommandsBackgroundStyle.BorderTopColorSchemePart = global::DevComponents.DotNetBar.eColorSchemePart.BarDockedBorder;
			this.dateTimeInput_HDExpStart.MonthCalendar.CommandsBackgroundStyle.BorderTopWidth = 1;
			this.dateTimeInput_HDExpStart.MonthCalendar.CommandsBackgroundStyle.Class = "";
			this.dateTimeInput_HDExpStart.MonthCalendar.CommandsBackgroundStyle.CornerType = global::DevComponents.DotNetBar.eCornerType.Square;
			this.dateTimeInput_HDExpStart.MonthCalendar.DaySize = new global::System.Drawing.Size(30, 20);
			this.dateTimeInput_HDExpStart.MonthCalendar.DisplayMonth = new global::System.DateTime(2013, 9, 1, 0, 0, 0, 0);
			this.dateTimeInput_HDExpStart.MonthCalendar.MarkedDates = new global::System.DateTime[0];
			this.dateTimeInput_HDExpStart.MonthCalendar.MonthlyMarkedDates = new global::System.DateTime[0];
			this.dateTimeInput_HDExpStart.MonthCalendar.NavigationBackgroundStyle.BackColor2SchemePart = global::DevComponents.DotNetBar.eColorSchemePart.PanelBackground2;
			this.dateTimeInput_HDExpStart.MonthCalendar.NavigationBackgroundStyle.BackColorGradientAngle = 90;
			this.dateTimeInput_HDExpStart.MonthCalendar.NavigationBackgroundStyle.BackColorSchemePart = global::DevComponents.DotNetBar.eColorSchemePart.PanelBackground;
			this.dateTimeInput_HDExpStart.MonthCalendar.NavigationBackgroundStyle.Class = "";
			this.dateTimeInput_HDExpStart.MonthCalendar.NavigationBackgroundStyle.CornerType = global::DevComponents.DotNetBar.eCornerType.Square;
			this.dateTimeInput_HDExpStart.MonthCalendar.WeeklyMarkedDays = new global::System.DayOfWeek[0];
			this.dateTimeInput_HDExpStart.Name = "dateTimeInput_HDExpStart";
			this.dateTimeInput_HDExpStart.Size = new global::System.Drawing.Size(170, 26);
			this.dateTimeInput_HDExpStart.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.dateTimeInput_HDExpStart.TabIndex = 6;
			this.label7.AutoSize = true;
			this.label7.Location = new global::System.Drawing.Point(353, 35);
			this.label7.Name = "label7";
			this.label7.Size = new global::System.Drawing.Size(82, 15);
			this.label7.TabIndex = 1;
			this.label7.Text = "结束日期：";
			this.textBox_HDExpFilePath.Location = new global::System.Drawing.Point(129, 29);
			this.textBox_HDExpFilePath.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.textBox_HDExpFilePath.Name = "textBox_HDExpFilePath";
			this.textBox_HDExpFilePath.Size = new global::System.Drawing.Size(447, 25);
			this.textBox_HDExpFilePath.TabIndex = 14;
			this.btn_HDExpOpenDFolder.Image = global::ns28.Class372.openHS;
			this.btn_HDExpOpenDFolder.Location = new global::System.Drawing.Point(582, 27);
			this.btn_HDExpOpenDFolder.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.btn_HDExpOpenDFolder.Name = "btn_HDExpOpenDFolder";
			this.btn_HDExpOpenDFolder.Size = new global::System.Drawing.Size(31, 29);
			this.btn_HDExpOpenDFolder.TabIndex = 15;
			this.btn_HDExpOpenDFolder.UseVisualStyleBackColor = true;
			this.label6.AutoSize = true;
			this.label6.Location = new global::System.Drawing.Point(27, 34);
			this.label6.Name = "label6";
			this.label6.Size = new global::System.Drawing.Size(82, 15);
			this.label6.TabIndex = 16;
			this.label6.Text = "文件目录：";
			this.progressBar_HDExp.Location = new global::System.Drawing.Point(31, 91);
			this.progressBar_HDExp.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.progressBar_HDExp.Name = "progressBar_HDExp";
			this.progressBar_HDExp.Size = new global::System.Drawing.Size(479, 10);
			this.progressBar_HDExp.TabIndex = 17;
			this.btn_HDExport.Location = new global::System.Drawing.Point(525, 72);
			this.btn_HDExport.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.btn_HDExport.Name = "btn_HDExport";
			this.btn_HDExport.Size = new global::System.Drawing.Size(88, 30);
			this.btn_HDExport.TabIndex = 18;
			this.btn_HDExport.Text = "导出";
			this.btn_HDExport.UseVisualStyleBackColor = true;
			this.groupBox_HDExp.Controls.Add(this.label_HDExpStatus);
			this.groupBox_HDExp.Controls.Add(this.label6);
			this.groupBox_HDExp.Controls.Add(this.btn_HDExport);
			this.groupBox_HDExp.Controls.Add(this.textBox_HDExpFilePath);
			this.groupBox_HDExp.Controls.Add(this.progressBar_HDExp);
			this.groupBox_HDExp.Controls.Add(this.btn_HDExpOpenDFolder);
			this.groupBox_HDExp.Location = new global::System.Drawing.Point(19, 266);
			this.groupBox_HDExp.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_HDExp.Name = "groupBox_HDExp";
			this.groupBox_HDExp.Padding = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_HDExp.Size = new global::System.Drawing.Size(642, 117);
			this.groupBox_HDExp.TabIndex = 19;
			this.groupBox_HDExp.TabStop = false;
			this.groupBox_HDExp.Text = "导出数据";
			this.label_HDExpStatus.AutoSize = true;
			this.label_HDExpStatus.Font = new global::System.Drawing.Font("SimSun", 8f);
			this.label_HDExpStatus.Location = new global::System.Drawing.Point(28, 72);
			this.label_HDExpStatus.Name = "label_HDExpStatus";
			this.label_HDExpStatus.Size = new global::System.Drawing.Size(112, 14);
			this.label_HDExpStatus.TabIndex = 19;
			this.label_HDExpStatus.Text = "正在准备数据...";
			this.tabControl1.Controls.Add(this.tabPage_Acct);
			this.tabControl1.Controls.Add(this.tabPage_HisData);
			this.tabControl1.Controls.Add(this.tabPage_Cfmmc);
			this.tabControl1.Location = new global::System.Drawing.Point(24, 12);
			this.tabControl1.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.tabControl1.Name = "tabControl1";
			this.tabControl1.SelectedIndex = 0;
			this.tabControl1.Size = new global::System.Drawing.Size(690, 432);
			this.tabControl1.TabIndex = 20;
			this.tabPage_Acct.BackColor = global::System.Drawing.SystemColors.Control;
			this.tabPage_Acct.Controls.Add(this.groupBox4);
			this.tabPage_Acct.Controls.Add(this.groupBox5);
			this.tabPage_Acct.Controls.Add(this.groupBox2);
			this.tabPage_Acct.Location = new global::System.Drawing.Point(4, 25);
			this.tabPage_Acct.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.tabPage_Acct.Name = "tabPage_Acct";
			this.tabPage_Acct.Padding = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.tabPage_Acct.Size = new global::System.Drawing.Size(682, 403);
			this.tabPage_Acct.TabIndex = 0;
			this.tabPage_Acct.Text = "交易数据";
			this.groupBox4.Controls.Add(this.groupBox3);
			this.groupBox4.Controls.Add(this.label9);
			this.groupBox4.Controls.Add(this.btn_DelAcctTrans);
			this.groupBox4.Controls.Add(this.comboBox_AcctToOpt);
			this.groupBox4.Controls.Add(this.label1_AcctSymbTransTotal);
			this.groupBox4.Controls.Add(this.comboBox_AcctSymbToOpt);
			this.groupBox4.Controls.Add(this.label8);
			this.groupBox4.Controls.Add(this.label_AccTransTotal);
			this.groupBox4.Controls.Add(this.checkBox_IfAllAcctSymbForOpt);
			this.groupBox4.Location = new global::System.Drawing.Point(19, 16);
			this.groupBox4.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox4.Name = "groupBox4";
			this.groupBox4.Padding = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox4.Size = new global::System.Drawing.Size(643, 179);
			this.groupBox4.TabIndex = 0;
			this.groupBox4.TabStop = false;
			this.groupBox4.Text = "交易记录操作";
			this.groupBox3.Controls.Add(this.comboBox_AcctToReceiveTrans);
			this.groupBox3.Controls.Add(this.btn_MoveTrans);
			this.groupBox3.Controls.Add(this.btn_CopyTrans);
			this.groupBox3.Location = new global::System.Drawing.Point(354, 23);
			this.groupBox3.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox3.Name = "groupBox3";
			this.groupBox3.Padding = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox3.Size = new global::System.Drawing.Size(254, 123);
			this.groupBox3.TabIndex = 4;
			this.groupBox3.TabStop = false;
			this.groupBox3.Text = "至指定账户";
			this.comboBox_AcctToReceiveTrans.FormattingEnabled = true;
			this.comboBox_AcctToReceiveTrans.Location = new global::System.Drawing.Point(33, 29);
			this.comboBox_AcctToReceiveTrans.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.comboBox_AcctToReceiveTrans.Name = "comboBox_AcctToReceiveTrans";
			this.comboBox_AcctToReceiveTrans.Size = new global::System.Drawing.Size(189, 23);
			this.comboBox_AcctToReceiveTrans.TabIndex = 0;
			this.btn_MoveTrans.Location = new global::System.Drawing.Point(33, 73);
			this.btn_MoveTrans.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.btn_MoveTrans.Name = "btn_MoveTrans";
			this.btn_MoveTrans.Size = new global::System.Drawing.Size(88, 30);
			this.btn_MoveTrans.TabIndex = 1;
			this.btn_MoveTrans.Text = "移动";
			this.btn_MoveTrans.UseVisualStyleBackColor = true;
			this.btn_CopyTrans.Location = new global::System.Drawing.Point(134, 73);
			this.btn_CopyTrans.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.btn_CopyTrans.Name = "btn_CopyTrans";
			this.btn_CopyTrans.Size = new global::System.Drawing.Size(88, 30);
			this.btn_CopyTrans.TabIndex = 27;
			this.btn_CopyTrans.Text = "复制";
			this.btn_CopyTrans.UseVisualStyleBackColor = true;
			this.label9.AutoSize = true;
			this.label9.Location = new global::System.Drawing.Point(19, 30);
			this.label9.Name = "label9";
			this.label9.Size = new global::System.Drawing.Size(52, 15);
			this.label9.TabIndex = 1;
			this.label9.Text = "账户：";
			this.btn_DelAcctTrans.Location = new global::System.Drawing.Point(191, 133);
			this.btn_DelAcctTrans.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.btn_DelAcctTrans.Name = "btn_DelAcctTrans";
			this.btn_DelAcctTrans.Size = new global::System.Drawing.Size(88, 30);
			this.btn_DelAcctTrans.TabIndex = 3;
			this.btn_DelAcctTrans.Text = "删除记录";
			this.btn_DelAcctTrans.UseVisualStyleBackColor = true;
			this.comboBox_AcctToOpt.FormattingEnabled = true;
			this.comboBox_AcctToOpt.Location = new global::System.Drawing.Point(21, 48);
			this.comboBox_AcctToOpt.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.comboBox_AcctToOpt.Name = "comboBox_AcctToOpt";
			this.comboBox_AcctToOpt.Size = new global::System.Drawing.Size(185, 23);
			this.comboBox_AcctToOpt.TabIndex = 0;
			this.label1_AcctSymbTransTotal.AutoSize = true;
			this.label1_AcctSymbTransTotal.Location = new global::System.Drawing.Point(216, 102);
			this.label1_AcctSymbTransTotal.Name = "label1_AcctSymbTransTotal";
			this.label1_AcctSymbTransTotal.Size = new global::System.Drawing.Size(63, 14);
			this.label1_AcctSymbTransTotal.TabIndex = 7;
			this.label1_AcctSymbTransTotal.Text = "记录数:0";
			this.comboBox_AcctSymbToOpt.FormattingEnabled = true;
			this.comboBox_AcctSymbToOpt.Location = new global::System.Drawing.Point(21, 97);
			this.comboBox_AcctSymbToOpt.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.comboBox_AcctSymbToOpt.Name = "comboBox_AcctSymbToOpt";
			this.comboBox_AcctSymbToOpt.Size = new global::System.Drawing.Size(185, 23);
			this.comboBox_AcctSymbToOpt.TabIndex = 1;
			this.label8.AutoSize = true;
			this.label8.Location = new global::System.Drawing.Point(19, 79);
			this.label8.Name = "label8";
			this.label8.Size = new global::System.Drawing.Size(52, 15);
			this.label8.TabIndex = 3;
			this.label8.Text = "品种：";
			this.label_AccTransTotal.AutoSize = true;
			this.label_AccTransTotal.Location = new global::System.Drawing.Point(216, 53);
			this.label_AccTransTotal.Name = "label_AccTransTotal";
			this.label_AccTransTotal.Size = new global::System.Drawing.Size(63, 14);
			this.label_AccTransTotal.TabIndex = 6;
			this.label_AccTransTotal.Text = "记录数:0";
			this.checkBox_IfAllAcctSymbForOpt.AutoSize = true;
			this.checkBox_IfAllAcctSymbForOpt.Location = new global::System.Drawing.Point(21, 140);
			this.checkBox_IfAllAcctSymbForOpt.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.checkBox_IfAllAcctSymbForOpt.Name = "checkBox_IfAllAcctSymbForOpt";
			this.checkBox_IfAllAcctSymbForOpt.Size = new global::System.Drawing.Size(89, 19);
			this.checkBox_IfAllAcctSymbForOpt.TabIndex = 2;
			this.checkBox_IfAllAcctSymbForOpt.Text = "所有品种";
			this.checkBox_IfAllAcctSymbForOpt.UseVisualStyleBackColor = true;
			this.groupBox5.Controls.Add(this.label13);
			this.groupBox5.Controls.Add(this.textBox_TransImpFilePath);
			this.groupBox5.Controls.Add(this.btn_TransImpOpenDFolder);
			this.groupBox5.Controls.Add(this.button_LoadTrans);
			this.groupBox5.Location = new global::System.Drawing.Point(19, 300);
			this.groupBox5.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox5.Name = "groupBox5";
			this.groupBox5.Padding = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox5.Size = new global::System.Drawing.Size(643, 80);
			this.groupBox5.TabIndex = 2;
			this.groupBox5.TabStop = false;
			this.groupBox5.Text = "交易记录导入";
			this.label13.AutoSize = true;
			this.label13.Location = new global::System.Drawing.Point(9, 35);
			this.label13.Name = "label13";
			this.label13.Size = new global::System.Drawing.Size(82, 15);
			this.label13.TabIndex = 25;
			this.label13.Text = "文件路径：";
			this.textBox_TransImpFilePath.Location = new global::System.Drawing.Point(102, 32);
			this.textBox_TransImpFilePath.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.textBox_TransImpFilePath.Name = "textBox_TransImpFilePath";
			this.textBox_TransImpFilePath.Size = new global::System.Drawing.Size(391, 25);
			this.textBox_TransImpFilePath.TabIndex = 0;
			this.btn_TransImpOpenDFolder.Image = global::ns28.Class372.openHS;
			this.btn_TransImpOpenDFolder.Location = new global::System.Drawing.Point(499, 30);
			this.btn_TransImpOpenDFolder.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.btn_TransImpOpenDFolder.Name = "btn_TransImpOpenDFolder";
			this.btn_TransImpOpenDFolder.Size = new global::System.Drawing.Size(31, 29);
			this.btn_TransImpOpenDFolder.TabIndex = 1;
			this.btn_TransImpOpenDFolder.UseVisualStyleBackColor = true;
			this.button_LoadTrans.Location = new global::System.Drawing.Point(536, 29);
			this.button_LoadTrans.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_LoadTrans.Name = "button_LoadTrans";
			this.button_LoadTrans.Size = new global::System.Drawing.Size(88, 30);
			this.button_LoadTrans.TabIndex = 2;
			this.button_LoadTrans.Text = "导入...";
			this.button_LoadTrans.UseVisualStyleBackColor = true;
			this.groupBox2.Controls.Add(this.label12);
			this.groupBox2.Controls.Add(this.btn_TransExp);
			this.groupBox2.Controls.Add(this.textBox_TransExpFilePath);
			this.groupBox2.Controls.Add(this.btn_TransExpOpenDFolder);
			this.groupBox2.Location = new global::System.Drawing.Point(19, 208);
			this.groupBox2.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox2.Name = "groupBox2";
			this.groupBox2.Padding = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox2.Size = new global::System.Drawing.Size(643, 80);
			this.groupBox2.TabIndex = 2;
			this.groupBox2.TabStop = false;
			this.groupBox2.Text = "交易记录导出";
			this.label12.AutoSize = true;
			this.label12.Location = new global::System.Drawing.Point(8, 35);
			this.label12.Name = "label12";
			this.label12.Size = new global::System.Drawing.Size(82, 15);
			this.label12.TabIndex = 21;
			this.label12.Text = "文件路径：";
			this.btn_TransExp.Location = new global::System.Drawing.Point(536, 28);
			this.btn_TransExp.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.btn_TransExp.Name = "btn_TransExp";
			this.btn_TransExp.Size = new global::System.Drawing.Size(88, 30);
			this.btn_TransExp.TabIndex = 2;
			this.btn_TransExp.Text = "导出";
			this.btn_TransExp.UseVisualStyleBackColor = true;
			this.textBox_TransExpFilePath.Location = new global::System.Drawing.Point(102, 32);
			this.textBox_TransExpFilePath.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.textBox_TransExpFilePath.Name = "textBox_TransExpFilePath";
			this.textBox_TransExpFilePath.Size = new global::System.Drawing.Size(391, 25);
			this.textBox_TransExpFilePath.TabIndex = 0;
			this.btn_TransExpOpenDFolder.Image = global::ns28.Class372.openHS;
			this.btn_TransExpOpenDFolder.Location = new global::System.Drawing.Point(498, 29);
			this.btn_TransExpOpenDFolder.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.btn_TransExpOpenDFolder.Name = "btn_TransExpOpenDFolder";
			this.btn_TransExpOpenDFolder.Size = new global::System.Drawing.Size(31, 29);
			this.btn_TransExpOpenDFolder.TabIndex = 1;
			this.btn_TransExpOpenDFolder.UseVisualStyleBackColor = true;
			this.tabPage_HisData.BackColor = global::System.Drawing.SystemColors.Control;
			this.tabPage_HisData.Controls.Add(this.groupBox_HDExpDates);
			this.tabPage_HisData.Controls.Add(this.groupBox_SelSymb);
			this.tabPage_HisData.Controls.Add(this.groupBox_HDExp);
			this.tabPage_HisData.Controls.Add(this.groupBox_HDExpFormat);
			this.tabPage_HisData.Location = new global::System.Drawing.Point(4, 25);
			this.tabPage_HisData.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.tabPage_HisData.Name = "tabPage_HisData";
			this.tabPage_HisData.Padding = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.tabPage_HisData.Size = new global::System.Drawing.Size(682, 403);
			this.tabPage_HisData.TabIndex = 1;
			this.tabPage_HisData.Text = "行情数据";
			this.groupBox_HDExpDates.Controls.Add(this.dateTimeInput_HDExpStart);
			this.groupBox_HDExpDates.Controls.Add(this.label3);
			this.groupBox_HDExpDates.Controls.Add(this.label7);
			this.groupBox_HDExpDates.Controls.Add(this.dateTimeInput_HDExpEnd);
			this.groupBox_HDExpDates.Location = new global::System.Drawing.Point(19, 181);
			this.groupBox_HDExpDates.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_HDExpDates.Name = "groupBox_HDExpDates";
			this.groupBox_HDExpDates.Padding = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox_HDExpDates.Size = new global::System.Drawing.Size(642, 75);
			this.groupBox_HDExpDates.TabIndex = 20;
			this.groupBox_HDExpDates.TabStop = false;
			this.groupBox_HDExpDates.Text = "数据时间";
			this.tabPage_Cfmmc.BackColor = global::System.Drawing.SystemColors.Control;
			this.tabPage_Cfmmc.Controls.Add(this.pictureBox1);
			this.tabPage_Cfmmc.Controls.Add(this.label_cfmmcNotice);
			this.tabPage_Cfmmc.Controls.Add(this.groupBox7);
			this.tabPage_Cfmmc.Controls.Add(this.groupBox6);
			this.tabPage_Cfmmc.Location = new global::System.Drawing.Point(4, 25);
			this.tabPage_Cfmmc.Margin = new global::System.Windows.Forms.Padding(4);
			this.tabPage_Cfmmc.Name = "tabPage_Cfmmc";
			this.tabPage_Cfmmc.Padding = new global::System.Windows.Forms.Padding(4);
			this.tabPage_Cfmmc.Size = new global::System.Drawing.Size(682, 403);
			this.tabPage_Cfmmc.TabIndex = 2;
			this.tabPage_Cfmmc.Text = "监控中心数据";
			this.pictureBox1.BackgroundImageLayout = global::System.Windows.Forms.ImageLayout.None;
			this.pictureBox1.Image = global::ns28.Class372._1683_Lightbulb_32x32;
			this.pictureBox1.Location = new global::System.Drawing.Point(22, 274);
			this.pictureBox1.Name = "pictureBox1";
			this.pictureBox1.Size = new global::System.Drawing.Size(20, 20);
			this.pictureBox1.SizeMode = global::System.Windows.Forms.PictureBoxSizeMode.StretchImage;
			this.pictureBox1.TabIndex = 23;
			this.pictureBox1.TabStop = false;
			this.label_cfmmcNotice.BackgroundStyle.Class = "";
			this.label_cfmmcNotice.BackgroundStyle.CornerType = global::DevComponents.DotNetBar.eCornerType.Square;
			this.label_cfmmcNotice.Location = new global::System.Drawing.Point(46, 275);
			this.label_cfmmcNotice.Name = "label_cfmmcNotice";
			this.label_cfmmcNotice.Size = new global::System.Drawing.Size(611, 121);
			this.label_cfmmcNotice.TabIndex = 1;
			this.label_cfmmcNotice.Text = "通过添加中国期货市场监控中心(CFMMC)查询账号，您可以下载近6个月内的实盘交易记录到本地模拟交易账户，进行实盘交易统计和复盘分析。此查询账号和密码请咨询您的期货公司获得。\r\n";
			this.label_cfmmcNotice.TextLineAlignment = global::System.Drawing.StringAlignment.Near;
			this.label_cfmmcNotice.WordWrap = true;
			this.groupBox7.Controls.Add(this.radioBtn_PeriodlyDnldCfmmc);
			this.groupBox7.Controls.Add(this.radioBtn_StartUpDnldCfmmc);
			this.groupBox7.Controls.Add(this.groupBox_CfmmcDnldTime);
			this.groupBox7.Controls.Add(this.groupBox_CfmmcDnldFrequency);
			this.groupBox7.Location = new global::System.Drawing.Point(363, 29);
			this.groupBox7.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox7.Name = "groupBox7";
			this.groupBox7.Padding = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox7.Size = new global::System.Drawing.Size(294, 226);
			this.groupBox7.TabIndex = 1;
			this.groupBox7.TabStop = false;
			this.groupBox7.Text = "自动下载交易数据";
			this.radioBtn_PeriodlyDnldCfmmc.AutoSize = true;
			this.radioBtn_PeriodlyDnldCfmmc.Location = new global::System.Drawing.Point(25, 65);
			this.radioBtn_PeriodlyDnldCfmmc.Name = "radioBtn_PeriodlyDnldCfmmc";
			this.radioBtn_PeriodlyDnldCfmmc.Size = new global::System.Drawing.Size(118, 19);
			this.radioBtn_PeriodlyDnldCfmmc.TabIndex = 3;
			this.radioBtn_PeriodlyDnldCfmmc.TabStop = true;
			this.radioBtn_PeriodlyDnldCfmmc.Text = "定时自动下载";
			this.radioBtn_PeriodlyDnldCfmmc.UseVisualStyleBackColor = true;
			this.radioBtn_StartUpDnldCfmmc.AutoSize = true;
			this.radioBtn_StartUpDnldCfmmc.Location = new global::System.Drawing.Point(25, 35);
			this.radioBtn_StartUpDnldCfmmc.Name = "radioBtn_StartUpDnldCfmmc";
			this.radioBtn_StartUpDnldCfmmc.Size = new global::System.Drawing.Size(148, 19);
			this.radioBtn_StartUpDnldCfmmc.TabIndex = 2;
			this.radioBtn_StartUpDnldCfmmc.TabStop = true;
			this.radioBtn_StartUpDnldCfmmc.Text = "程序启动自动下载";
			this.radioBtn_StartUpDnldCfmmc.UseVisualStyleBackColor = true;
			this.groupBox_CfmmcDnldTime.Controls.Add(this.TimePicker_AutoDown);
			this.groupBox_CfmmcDnldTime.Location = new global::System.Drawing.Point(48, 154);
			this.groupBox_CfmmcDnldTime.Margin = new global::System.Windows.Forms.Padding(4);
			this.groupBox_CfmmcDnldTime.Name = "groupBox_CfmmcDnldTime";
			this.groupBox_CfmmcDnldTime.Padding = new global::System.Windows.Forms.Padding(4);
			this.groupBox_CfmmcDnldTime.Size = new global::System.Drawing.Size(221, 55);
			this.groupBox_CfmmcDnldTime.TabIndex = 1;
			this.groupBox_CfmmcDnldTime.TabStop = false;
			this.groupBox_CfmmcDnldTime.Text = "时间";
			this.TimePicker_AutoDown.Format = global::System.Windows.Forms.DateTimePickerFormat.Time;
			this.TimePicker_AutoDown.Location = new global::System.Drawing.Point(52, 19);
			this.TimePicker_AutoDown.Margin = new global::System.Windows.Forms.Padding(4);
			this.TimePicker_AutoDown.Name = "TimePicker_AutoDown";
			this.TimePicker_AutoDown.ShowUpDown = true;
			this.TimePicker_AutoDown.Size = new global::System.Drawing.Size(119, 25);
			this.TimePicker_AutoDown.TabIndex = 1;
			this.groupBox_CfmmcDnldFrequency.Controls.Add(this.comboBox_DayOfWeekDnCfmmc);
			this.groupBox_CfmmcDnldFrequency.Controls.Add(this.radio_autoWeek);
			this.groupBox_CfmmcDnldFrequency.Controls.Add(this.radio_autoDay);
			this.groupBox_CfmmcDnldFrequency.Location = new global::System.Drawing.Point(48, 94);
			this.groupBox_CfmmcDnldFrequency.Margin = new global::System.Windows.Forms.Padding(4);
			this.groupBox_CfmmcDnldFrequency.Name = "groupBox_CfmmcDnldFrequency";
			this.groupBox_CfmmcDnldFrequency.Padding = new global::System.Windows.Forms.Padding(4);
			this.groupBox_CfmmcDnldFrequency.Size = new global::System.Drawing.Size(221, 55);
			this.groupBox_CfmmcDnldFrequency.TabIndex = 0;
			this.groupBox_CfmmcDnldFrequency.TabStop = false;
			this.groupBox_CfmmcDnldFrequency.Text = "频率";
			this.comboBox_DayOfWeekDnCfmmc.DropDownStyle = global::System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_DayOfWeekDnCfmmc.FormattingEnabled = true;
			this.comboBox_DayOfWeekDnCfmmc.Items.AddRange(new object[]
			{
				"六",
				"日",
				"一",
				"二",
				"三",
				"四",
				"五"
			});
			this.comboBox_DayOfWeekDnCfmmc.Location = new global::System.Drawing.Point(81, 20);
			this.comboBox_DayOfWeekDnCfmmc.Name = "comboBox_DayOfWeekDnCfmmc";
			this.comboBox_DayOfWeekDnCfmmc.Size = new global::System.Drawing.Size(48, 23);
			this.comboBox_DayOfWeekDnCfmmc.TabIndex = 5;
			this.radio_autoWeek.AutoSize = true;
			this.radio_autoWeek.Location = new global::System.Drawing.Point(21, 22);
			this.radio_autoWeek.Margin = new global::System.Windows.Forms.Padding(4);
			this.radio_autoWeek.Name = "radio_autoWeek";
			this.radio_autoWeek.Size = new global::System.Drawing.Size(58, 19);
			this.radio_autoWeek.TabIndex = 3;
			this.radio_autoWeek.TabStop = true;
			this.radio_autoWeek.Text = "每周";
			this.radio_autoWeek.UseVisualStyleBackColor = true;
			this.radio_autoDay.AutoSize = true;
			this.radio_autoDay.Location = new global::System.Drawing.Point(144, 22);
			this.radio_autoDay.Margin = new global::System.Windows.Forms.Padding(4);
			this.radio_autoDay.Name = "radio_autoDay";
			this.radio_autoDay.Size = new global::System.Drawing.Size(58, 19);
			this.radio_autoDay.TabIndex = 4;
			this.radio_autoDay.TabStop = true;
			this.radio_autoDay.Text = "每天";
			this.radio_autoDay.UseVisualStyleBackColor = true;
			this.groupBox6.Controls.Add(this.groupBox1);
			this.groupBox6.Controls.Add(this.btn_CfmmcDownBg);
			this.groupBox6.Controls.Add(this.label_DnldCfmmcRecNb);
			this.groupBox6.Controls.Add(this.progressBar_Cfmmc);
			this.groupBox6.Controls.Add(this.label_CfmmcDnStatus);
			this.groupBox6.Controls.Add(this.label_cfmmcAcctNote);
			this.groupBox6.Controls.Add(this.btn_EditCfmmcAcct);
			this.groupBox6.Controls.Add(this.btn_DelCfmmcAcct);
			this.groupBox6.Controls.Add(this.btn_AddCfmmcAcct);
			this.groupBox6.Controls.Add(this.comboBox_CfmmcAcct);
			this.groupBox6.Location = new global::System.Drawing.Point(22, 29);
			this.groupBox6.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox6.Name = "groupBox6";
			this.groupBox6.Padding = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.groupBox6.Size = new global::System.Drawing.Size(321, 226);
			this.groupBox6.TabIndex = 1;
			this.groupBox6.TabStop = false;
			this.groupBox6.Text = "期货(保证金)监控中心账号";
			this.groupBox1.Location = new global::System.Drawing.Point(22, 149);
			this.groupBox1.Name = "groupBox1";
			this.groupBox1.Size = new global::System.Drawing.Size(279, 2);
			this.groupBox1.TabIndex = 17;
			this.groupBox1.TabStop = false;
			this.btn_CfmmcDownBg.Location = new global::System.Drawing.Point(227, 159);
			this.btn_CfmmcDownBg.Margin = new global::System.Windows.Forms.Padding(4);
			this.btn_CfmmcDownBg.Name = "btn_CfmmcDownBg";
			this.btn_CfmmcDownBg.Size = new global::System.Drawing.Size(75, 29);
			this.btn_CfmmcDownBg.TabIndex = 3;
			this.btn_CfmmcDownBg.Text = "下载";
			this.btn_CfmmcDownBg.UseVisualStyleBackColor = true;
			this.label_DnldCfmmcRecNb.AutoSize = true;
			this.label_DnldCfmmcRecNb.Location = new global::System.Drawing.Point(18, 120);
			this.label_DnldCfmmcRecNb.Name = "label_DnldCfmmcRecNb";
			this.label_DnldCfmmcRecNb.Size = new global::System.Drawing.Size(120, 15);
			this.label_DnldCfmmcRecNb.TabIndex = 9;
			this.label_DnldCfmmcRecNb.Text = "已下载记录数：0";
			this.progressBar_Cfmmc.Location = new global::System.Drawing.Point(22, 166);
			this.progressBar_Cfmmc.Margin = new global::System.Windows.Forms.Padding(4);
			this.progressBar_Cfmmc.Name = "progressBar_Cfmmc";
			this.progressBar_Cfmmc.Size = new global::System.Drawing.Size(191, 10);
			this.progressBar_Cfmmc.TabIndex = 2;
			this.label_CfmmcDnStatus.AutoSize = true;
			this.label_CfmmcDnStatus.Location = new global::System.Drawing.Point(19, 196);
			this.label_CfmmcDnStatus.Name = "label_CfmmcDnStatus";
			this.label_CfmmcDnStatus.Size = new global::System.Drawing.Size(129, 15);
			this.label_CfmmcDnStatus.TabIndex = 1;
			this.label_CfmmcDnStatus.Text = "开始下载数据....";
			this.label_cfmmcAcctNote.Location = new global::System.Drawing.Point(17, 70);
			this.label_cfmmcAcctNote.Name = "label_cfmmcAcctNote";
			this.label_cfmmcAcctNote.Size = new global::System.Drawing.Size(169, 44);
			this.label_cfmmcAcctNote.TabIndex = 8;
			this.label_cfmmcAcctNote.Text = "备注：（无）";
			this.btn_EditCfmmcAcct.Location = new global::System.Drawing.Point(227, 71);
			this.btn_EditCfmmcAcct.Margin = new global::System.Windows.Forms.Padding(4);
			this.btn_EditCfmmcAcct.Name = "btn_EditCfmmcAcct";
			this.btn_EditCfmmcAcct.Size = new global::System.Drawing.Size(75, 29);
			this.btn_EditCfmmcAcct.TabIndex = 7;
			this.btn_EditCfmmcAcct.Text = "修改";
			this.btn_EditCfmmcAcct.UseVisualStyleBackColor = true;
			this.btn_DelCfmmcAcct.Location = new global::System.Drawing.Point(227, 110);
			this.btn_DelCfmmcAcct.Margin = new global::System.Windows.Forms.Padding(4);
			this.btn_DelCfmmcAcct.Name = "btn_DelCfmmcAcct";
			this.btn_DelCfmmcAcct.Size = new global::System.Drawing.Size(75, 29);
			this.btn_DelCfmmcAcct.TabIndex = 6;
			this.btn_DelCfmmcAcct.Text = "删除";
			this.btn_DelCfmmcAcct.UseVisualStyleBackColor = true;
			this.btn_AddCfmmcAcct.Location = new global::System.Drawing.Point(227, 33);
			this.btn_AddCfmmcAcct.Margin = new global::System.Windows.Forms.Padding(4);
			this.btn_AddCfmmcAcct.Name = "btn_AddCfmmcAcct";
			this.btn_AddCfmmcAcct.Size = new global::System.Drawing.Size(75, 29);
			this.btn_AddCfmmcAcct.TabIndex = 5;
			this.btn_AddCfmmcAcct.Text = "新增";
			this.btn_AddCfmmcAcct.UseVisualStyleBackColor = true;
			this.comboBox_CfmmcAcct.DropDownStyle = global::System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_CfmmcAcct.FormattingEnabled = true;
			this.comboBox_CfmmcAcct.Location = new global::System.Drawing.Point(20, 36);
			this.comboBox_CfmmcAcct.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.comboBox_CfmmcAcct.Name = "comboBox_CfmmcAcct";
			this.comboBox_CfmmcAcct.Size = new global::System.Drawing.Size(190, 23);
			this.comboBox_CfmmcAcct.TabIndex = 0;
			this.button_Cancel.DialogResult = global::System.Windows.Forms.DialogResult.Cancel;
			this.button_Cancel.Location = new global::System.Drawing.Point(590, 452);
			this.button_Cancel.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_Cancel.Name = "button_Cancel";
			this.button_Cancel.Size = new global::System.Drawing.Size(120, 30);
			this.button_Cancel.TabIndex = 22;
			this.button_Cancel.Text = "取消";
			this.button_Cancel.UseVisualStyleBackColor = true;
			this.button_OK.DialogResult = global::System.Windows.Forms.DialogResult.OK;
			this.button_OK.Location = new global::System.Drawing.Point(456, 452);
			this.button_OK.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.button_OK.Name = "button_OK";
			this.button_OK.Size = new global::System.Drawing.Size(120, 30);
			this.button_OK.TabIndex = 21;
			this.button_OK.Text = "确定";
			this.button_OK.UseVisualStyleBackColor = true;
			base.AcceptButton = this.button_OK;
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(8f, 15f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			this.BackColor = global::System.Drawing.SystemColors.Control;
			base.ClientSize = new global::System.Drawing.Size(737, 493);
			base.Controls.Add(this.button_Cancel);
			base.Controls.Add(this.button_OK);
			base.Controls.Add(this.tabControl1);
			base.FormBorderStyle = global::System.Windows.Forms.FormBorderStyle.FixedDialog;
			base.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = "DataMgmtForm";
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			this.Text = "数据管理";
			this.groupBox_SelSymb.ResumeLayout(false);
			this.groupBox_SelSymb.PerformLayout();
			this.groupBox_HDExpFormat.ResumeLayout(false);
			this.groupBox_HDExpFormat.PerformLayout();
			((global::System.ComponentModel.ISupportInitialize)this.dateTimeInput_HDExpEnd).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.dateTimeInput_HDExpStart).EndInit();
			this.groupBox_HDExp.ResumeLayout(false);
			this.groupBox_HDExp.PerformLayout();
			this.tabControl1.ResumeLayout(false);
			this.tabPage_Acct.ResumeLayout(false);
			this.groupBox4.ResumeLayout(false);
			this.groupBox4.PerformLayout();
			this.groupBox3.ResumeLayout(false);
			this.groupBox5.ResumeLayout(false);
			this.groupBox5.PerformLayout();
			this.groupBox2.ResumeLayout(false);
			this.groupBox2.PerformLayout();
			this.tabPage_HisData.ResumeLayout(false);
			this.groupBox_HDExpDates.ResumeLayout(false);
			this.groupBox_HDExpDates.PerformLayout();
			this.tabPage_Cfmmc.ResumeLayout(false);
			((global::System.ComponentModel.ISupportInitialize)this.pictureBox1).EndInit();
			this.groupBox7.ResumeLayout(false);
			this.groupBox7.PerformLayout();
			this.groupBox_CfmmcDnldTime.ResumeLayout(false);
			this.groupBox_CfmmcDnldFrequency.ResumeLayout(false);
			this.groupBox_CfmmcDnldFrequency.PerformLayout();
			this.groupBox6.ResumeLayout(false);
			this.groupBox6.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x04000661 RID: 1633
		private global::System.Windows.Forms.GroupBox groupBox_SelSymb;

		// Token: 0x04000662 RID: 1634
		private global::System.Windows.Forms.ComboBox comboBox_Exchg;

		// Token: 0x04000663 RID: 1635
		private global::System.Windows.Forms.Label label2;

		// Token: 0x04000664 RID: 1636
		private global::System.Windows.Forms.ComboBox comboBox_HDExpSymbl;

		// Token: 0x04000665 RID: 1637
		private global::System.Windows.Forms.Label label1;

		// Token: 0x04000666 RID: 1638
		private global::System.Windows.Forms.GroupBox groupBox_HDExpFormat;

		// Token: 0x04000667 RID: 1639
		private global::System.Windows.Forms.ComboBox comboBox_HDExpDataFormat;

		// Token: 0x04000668 RID: 1640
		private global::System.Windows.Forms.Label label4;

		// Token: 0x04000669 RID: 1641
		private global::System.Windows.Forms.Label label3;

		// Token: 0x0400066A RID: 1642
		private global::DevComponents.Editors.DateTimeAdv.DateTimeInput dateTimeInput_HDExpEnd;

		// Token: 0x0400066B RID: 1643
		private global::DevComponents.Editors.DateTimeAdv.DateTimeInput dateTimeInput_HDExpStart;

		// Token: 0x0400066C RID: 1644
		private global::System.Windows.Forms.Label label7;

		// Token: 0x0400066D RID: 1645
		private global::System.Windows.Forms.Label label5;

		// Token: 0x0400066E RID: 1646
		private global::System.Windows.Forms.ComboBox comboBox_PeriodType;

		// Token: 0x0400066F RID: 1647
		private global::System.Windows.Forms.CheckBox checkBox_inclFirstLine;

		// Token: 0x04000670 RID: 1648
		private global::System.Windows.Forms.TextBox textBox_HDExpFilePath;

		// Token: 0x04000671 RID: 1649
		private global::System.Windows.Forms.Button btn_HDExpOpenDFolder;

		// Token: 0x04000672 RID: 1650
		private global::System.Windows.Forms.Label label6;

		// Token: 0x04000673 RID: 1651
		private global::System.Windows.Forms.ProgressBar progressBar_HDExp;

		// Token: 0x04000674 RID: 1652
		private global::System.Windows.Forms.Button btn_HDExport;

		// Token: 0x04000675 RID: 1653
		private global::System.Windows.Forms.GroupBox groupBox_HDExp;

		// Token: 0x04000676 RID: 1654
		private global::System.Windows.Forms.TabControl tabControl1;

		// Token: 0x04000677 RID: 1655
		private global::System.Windows.Forms.TabPage tabPage_Acct;

		// Token: 0x04000678 RID: 1656
		private global::System.Windows.Forms.TabPage tabPage_HisData;

		// Token: 0x04000679 RID: 1657
		private global::System.Windows.Forms.Button button_Cancel;

		// Token: 0x0400067A RID: 1658
		private global::System.Windows.Forms.Button button_OK;

		// Token: 0x0400067B RID: 1659
		private global::System.Windows.Forms.Label label_HDExpStatus;

		// Token: 0x0400067C RID: 1660
		private global::System.Windows.Forms.CheckBox checkBox_IfCombinedDT;

		// Token: 0x0400067D RID: 1661
		private global::System.Windows.Forms.GroupBox groupBox_HDExpDates;

		// Token: 0x0400067E RID: 1662
		private global::System.Windows.Forms.GroupBox groupBox2;

		// Token: 0x0400067F RID: 1663
		private global::System.Windows.Forms.Label label8;

		// Token: 0x04000680 RID: 1664
		private global::System.Windows.Forms.ComboBox comboBox_AcctSymbToOpt;

		// Token: 0x04000681 RID: 1665
		private global::System.Windows.Forms.Label label9;

		// Token: 0x04000682 RID: 1666
		private global::System.Windows.Forms.ComboBox comboBox_AcctToOpt;

		// Token: 0x04000683 RID: 1667
		private global::System.Windows.Forms.Label label12;

		// Token: 0x04000684 RID: 1668
		private global::System.Windows.Forms.Button btn_TransExp;

		// Token: 0x04000685 RID: 1669
		private global::System.Windows.Forms.TextBox textBox_TransExpFilePath;

		// Token: 0x04000686 RID: 1670
		private global::System.Windows.Forms.Button btn_TransExpOpenDFolder;

		// Token: 0x04000687 RID: 1671
		private global::System.Windows.Forms.CheckBox checkBox_IfAllAcctSymbForOpt;

		// Token: 0x04000688 RID: 1672
		private global::System.Windows.Forms.Label label1_AcctSymbTransTotal;

		// Token: 0x04000689 RID: 1673
		private global::System.Windows.Forms.Label label_AccTransTotal;

		// Token: 0x0400068A RID: 1674
		private global::System.Windows.Forms.Button btn_DelAcctTrans;

		// Token: 0x0400068B RID: 1675
		private global::System.Windows.Forms.Button btn_MoveTrans;

		// Token: 0x0400068C RID: 1676
		private global::System.Windows.Forms.ComboBox comboBox_AcctToReceiveTrans;

		// Token: 0x0400068D RID: 1677
		private global::System.Windows.Forms.Button btn_CopyTrans;

		// Token: 0x0400068E RID: 1678
		private global::System.Windows.Forms.GroupBox groupBox4;

		// Token: 0x0400068F RID: 1679
		private global::System.Windows.Forms.GroupBox groupBox3;

		// Token: 0x04000690 RID: 1680
		private global::System.Windows.Forms.GroupBox groupBox5;

		// Token: 0x04000691 RID: 1681
		private global::System.Windows.Forms.Button button_LoadTrans;

		// Token: 0x04000692 RID: 1682
		private global::System.Windows.Forms.TabPage tabPage_Cfmmc;

		// Token: 0x04000693 RID: 1683
		private global::System.Windows.Forms.GroupBox groupBox7;

		// Token: 0x04000694 RID: 1684
		private global::System.Windows.Forms.GroupBox groupBox6;

		// Token: 0x04000695 RID: 1685
		private global::System.Windows.Forms.ComboBox comboBox_CfmmcAcct;

		// Token: 0x04000696 RID: 1686
		private global::DevComponents.DotNetBar.LabelX label_cfmmcNotice;

		// Token: 0x04000697 RID: 1687
		private global::System.Windows.Forms.Button btn_CfmmcDownBg;

		// Token: 0x04000698 RID: 1688
		private global::System.Windows.Forms.ProgressBar progressBar_Cfmmc;

		// Token: 0x04000699 RID: 1689
		private global::System.Windows.Forms.Label label_CfmmcDnStatus;

		// Token: 0x0400069A RID: 1690
		private global::System.Windows.Forms.RadioButton radio_autoWeek;

		// Token: 0x0400069B RID: 1691
		private global::System.Windows.Forms.RadioButton radio_autoDay;

		// Token: 0x0400069C RID: 1692
		private global::System.Windows.Forms.DateTimePicker TimePicker_AutoDown;

		// Token: 0x0400069D RID: 1693
		private global::System.Windows.Forms.GroupBox groupBox_CfmmcDnldTime;

		// Token: 0x0400069E RID: 1694
		private global::System.Windows.Forms.GroupBox groupBox_CfmmcDnldFrequency;

		// Token: 0x0400069F RID: 1695
		private global::System.Windows.Forms.Label label13;

		// Token: 0x040006A0 RID: 1696
		private global::System.Windows.Forms.TextBox textBox_TransImpFilePath;

		// Token: 0x040006A1 RID: 1697
		private global::System.Windows.Forms.Button btn_TransImpOpenDFolder;

		// Token: 0x040006A2 RID: 1698
		private global::System.Windows.Forms.Button btn_AddCfmmcAcct;

		// Token: 0x040006A3 RID: 1699
		private global::System.Windows.Forms.Button btn_DelCfmmcAcct;

		// Token: 0x040006A4 RID: 1700
		private global::System.Windows.Forms.PictureBox pictureBox1;

		// Token: 0x040006A5 RID: 1701
		private global::System.Windows.Forms.Button btn_EditCfmmcAcct;

		// Token: 0x040006A6 RID: 1702
		private global::System.Windows.Forms.Label label_cfmmcAcctNote;

		// Token: 0x040006A7 RID: 1703
		private global::System.Windows.Forms.ComboBox comboBox_DayOfWeekDnCfmmc;

		// Token: 0x040006A8 RID: 1704
		private global::System.Windows.Forms.Label label_DnldCfmmcRecNb;

		// Token: 0x040006A9 RID: 1705
		private global::System.Windows.Forms.GroupBox groupBox1;

		// Token: 0x040006AA RID: 1706
		private global::System.Windows.Forms.RadioButton radioBtn_PeriodlyDnldCfmmc;

		// Token: 0x040006AB RID: 1707
		private global::System.Windows.Forms.RadioButton radioBtn_StartUpDnldCfmmc;
	}
}
