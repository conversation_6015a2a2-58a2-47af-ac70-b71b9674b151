﻿using System;
using System.Runtime.CompilerServices;

namespace ns17
{
	// Token: 0x020003CD RID: 973
	internal abstract class Class513 : IDisposable
	{
		// Token: 0x060026F2 RID: 9970 RVA: 0x0000EEA8 File Offset: 0x0000D0A8
		public Class513(UIntPtr uintptr_1)
		{
			this.HKey = uintptr_1;
		}

		// Token: 0x170006C0 RID: 1728
		// (get) Token: 0x060026F3 RID: 9971 RVA: 0x0000EEB7 File Offset: 0x0000D0B7
		// (set) Token: 0x060026F4 RID: 9972 RVA: 0x0000EEBF File Offset: 0x0000D0BF
		public UIntPtr HKey { get; set; }

		// Token: 0x060026F5 RID: 9973
		public abstract object vmethod_0(string string_0);

		// Token: 0x060026F6 RID: 9974
		public abstract bool vmethod_1(string string_0, out object object_0);

		// Token: 0x060026F7 RID: 9975
		public abstract void Dispose();

		// Token: 0x040012DE RID: 4830
		[CompilerGenerated]
		private UIntPtr uintptr_0;
	}
}
