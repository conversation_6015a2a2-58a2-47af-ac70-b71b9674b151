﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns10;
using ns11;
using ns12;
using ns13;
using ns16;
using ns2;
using ns20;
using ns28;
using ns6;
using ns8;
using TEx;
using TEx.Chart;
using TEx.Comn;
using TEx.Util;

namespace ns15
{
	// Token: 0x02000219 RID: 537
	[Docking(DockingBehavior.AutoDock)]
	internal sealed class FnRptAnlysPanel : UserControl
	{
		// Token: 0x06001610 RID: 5648 RVA: 0x00008E6E File Offset: 0x0000706E
		public FnRptAnlysPanel()
		{
			this.InitializeComponent();
		}

		// Token: 0x06001611 RID: 5649 RVA: 0x000932D8 File Offset: 0x000914D8
		public void method_0()
		{
			this.btn_ChangeDate.Click += this.btn_ChangeDate_Click;
			if (!TApp.IsHighDpiScreen)
			{
				float emSize = TApp.smethod_4(9.3f, false);
				Font font = new Font("SimSun", emSize, FontStyle.Bold);
				this.superTabCtrl_RptAnlys.TabFont = font;
				this.superTabCtrl_RptAnlys.SelectedTabFont = font;
			}
			this.superTabCtrl_RptAnlys.SelectedTabChanged += this.method_13;
			this.tabControl_FnRpts.SelectedTabChanged += this.tabControl_FnRpts_SelectedTabChanged;
			if (!TApp.IsHighDpiScreen)
			{
				float emSize2 = TApp.smethod_4(9f, false);
				Font font2 = new Font("SimSun", emSize2, FontStyle.Regular);
				this.tabControl_FnRpts.Font = font2;
				this.tabControl_FnRpts.SelectedTabFont = font2;
				emSize2 = TApp.smethod_4(9f, true);
				this.btn_ChangeDate.Font = new Font("SimSun", emSize2);
			}
			this.tabCtrlPanel_FnData_byYr.Padding = new System.Windows.Forms.Padding(0);
			this.tabCtrlPanel_FnData_byQt.Padding = new System.Windows.Forms.Padding(0);
			if (this.rptChart_TabItem_SixInds != null)
			{
				this.rptChart_TabItem_SixInds.Visible = false;
			}
			this.method_6();
			this.class557_0 = new Class557();
			this.class557_0.ResultReceived += this.class557_0_ResultReceived;
			this.class557_0.RequestError += this.class557_0_RequestError;
			this.dictionary_0 = new Dictionary<string, KeyValuePair<string, string>>();
			Base.Data.CurrSymblChanging += this.method_22;
			Base.Data.CurrSymblChanged += this.method_21;
			Base.Data.DateSelectionChanged += this.method_20;
			Base.UI.ChartThemeChanged += this.method_4;
			Base.UI.Form.BlindTestModeOn += this.method_8;
			Base.UI.Form.BlindTestModeOff += this.method_9;
			this.method_23();
			this.method_25();
			this.method_27();
			this.method_3();
			this.list_0 = new List<Class15>();
			this.method_29();
		}

		// Token: 0x06001612 RID: 5650 RVA: 0x000934D8 File Offset: 0x000916D8
		private DevComponents.DotNetBar.TabControl method_1(string[] string_0, bool bool_0 = true)
		{
			DevComponents.DotNetBar.TabControl tabControl = new DevComponents.DotNetBar.TabControl();
			tabControl.BackColor = Color.Transparent;
			tabControl.CanReorderTabs = true;
			tabControl.Dock = DockStyle.Fill;
			tabControl.Location = new Point(0, 0);
			tabControl.Margin = new System.Windows.Forms.Padding(0);
			tabControl.Padding = new System.Windows.Forms.Padding(0);
			if (!TApp.IsHighDpiScreen)
			{
				Font font = new Font("SimSun", (float)(11.25 / TApp.DpiScale), FontStyle.Regular);
				tabControl.Font = font;
				tabControl.SelectedTabFont = font;
			}
			tabControl.SelectedTabIndex = 0;
			tabControl.Style = eTabStripStyle.Flat;
			tabControl.TabAlignment = eTabStripAlignment.Bottom;
			tabControl.TabIndex = 0;
			tabControl.TabLayoutType = eTabLayoutType.FixedWithNavigationBox;
			foreach (string string_ in string_0)
			{
				TabItem tabItem = this.method_2(string_);
				tabControl.Tabs.Add(tabItem);
				TabControlPanel tabControlPanel = tabItem.AttachedControl as TabControlPanel;
				tabControl.Controls.Add(tabControlPanel);
				if (bool_0)
				{
					this.method_11(tabControlPanel);
				}
			}
			tabControl.ColorScheme = Base.UI.smethod_72();
			tabControl.SelectedTabChanged += this.method_34;
			return tabControl;
		}

		// Token: 0x06001613 RID: 5651 RVA: 0x000935F0 File Offset: 0x000917F0
		private TabItem method_2(string string_0)
		{
			TabItem tabItem = new TabItem();
			tabItem.Text = string_0;
			tabItem.AttachedControl = new TabControlPanel
			{
				Dock = DockStyle.Fill,
				Location = new Point(0, 0),
				Padding = new System.Windows.Forms.Padding(0),
				Style = 
				{
					BackColor1 = 
					{
						Color = SystemColors.Control
					},
					Border = eBorderType.SingleLine,
					BorderColor = 
					{
						Color = Color.FromArgb(93, 93, 93)
					},
					BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top),
					GradientAngle = -90
				},
				TabItem = tabItem
			};
			return tabItem;
		}

		// Token: 0x06001614 RID: 5652 RVA: 0x00093698 File Offset: 0x00091898
		private void method_3()
		{
			this.smethod_0();
			Color backColor = Base.UI.smethod_34();
			Color foreColor = Base.UI.smethod_35();
			this.tablePanel_bg.BackColor = backColor;
			this.label_当前日期.ForeColor = foreColor;
			this.label_CurrDate.ForeColor = foreColor;
			this.label_CurrSymb.ForeColor = foreColor;
			this.method_5();
			this.method_7();
			this.smethod_1();
		}

		// Token: 0x06001615 RID: 5653 RVA: 0x00008E89 File Offset: 0x00007089
		private void method_4(object sender, EventArgs e)
		{
			this.method_3();
		}

		// Token: 0x06001616 RID: 5654 RVA: 0x000936FC File Offset: 0x000918FC
		private void method_5()
		{
			eSuperTabStyle tabStyle = Base.UI.smethod_71();
			this.superTabCtrl_RptAnlys.TabStyle = tabStyle;
			TabColorScheme colorScheme = Base.UI.smethod_72();
			this.tabControl_FnRpts.ColorScheme = colorScheme;
			this.btn_ChangeDate.ForeColor = Color.Black;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.tablePanel_Header.BackColor = Class179.color_3;
			}
			else
			{
				this.tablePanel_Header.BackColor = Class179.color_9;
			}
			foreach (object obj in this.superTabCtrl_RptAnlys.Controls)
			{
				if (obj is SuperTabControlPanel)
				{
					SuperTabControlPanel superTabControlPanel = obj as SuperTabControlPanel;
					if (superTabControlPanel.Controls.Count > 0)
					{
						DevComponents.DotNetBar.TabControl tabControl = superTabControlPanel.Controls[0] as DevComponents.DotNetBar.TabControl;
						if (tabControl != null)
						{
							tabControl.ColorScheme = colorScheme;
						}
					}
				}
			}
		}

		// Token: 0x06001617 RID: 5655 RVA: 0x00008E93 File Offset: 0x00007093
		private void method_6()
		{
			this.method_10(new Action<Panel>(this.method_11));
		}

		// Token: 0x06001618 RID: 5656 RVA: 0x00008EA9 File Offset: 0x000070A9
		private void method_7()
		{
			this.method_10(new Action<Panel>(this.method_12));
		}

		// Token: 0x06001619 RID: 5657 RVA: 0x00008EBF File Offset: 0x000070BF
		private void method_8(object sender, EventArgs e)
		{
			this.method_25();
			this.method_27();
		}

		// Token: 0x0600161A RID: 5658 RVA: 0x00008EBF File Offset: 0x000070BF
		private void method_9(object sender, EventArgs e)
		{
			this.method_25();
			this.method_27();
		}

		// Token: 0x0600161B RID: 5659 RVA: 0x000937F8 File Offset: 0x000919F8
		private void method_10(Action<Panel> action_0)
		{
			foreach (object obj in this.tabControl_FnRpts.Controls)
			{
				if (obj is TabControlPanel)
				{
					action_0(obj as TabControlPanel);
				}
			}
			foreach (object obj2 in this.superTabCtrl_RptAnlys.Controls)
			{
				if (obj2 is SuperTabControlPanel)
				{
					SuperTabControlPanel superTabControlPanel = obj2 as SuperTabControlPanel;
					if (superTabControlPanel.Controls.Count > 0)
					{
						foreach (object obj3 in (superTabControlPanel.Controls[0] as DevComponents.DotNetBar.TabControl).Controls)
						{
							if (obj3 is TabControlPanel)
							{
								action_0(obj3 as TabControlPanel);
							}
						}
					}
				}
			}
		}

		// Token: 0x0600161C RID: 5660 RVA: 0x00093934 File Offset: 0x00091B34
		private void method_11(Panel panel_0)
		{
			bool flag = false;
			StkSymbol stkSymbol = this.method_26();
			if (stkSymbol != null && !stkSymbol.IsStockCompany)
			{
				flag = true;
			}
			Color backColor;
			Color foreColor;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				backColor = Class179.color_3;
				foreColor = Class179.color_4;
			}
			else
			{
				backColor = Color.FromKnownColor(KnownColor.Control);
				foreColor = Color.LightGray;
			}
			Panel panel = new Panel();
			panel.BackColor = backColor;
			panel.Dock = DockStyle.Fill;
			if (flag)
			{
				System.Windows.Forms.Label label = new System.Windows.Forms.Label();
				label.Text = "无相应数据";
				label.ForeColor = foreColor;
				label.Font = new Font("Microsoft Sans Serif", 40f, FontStyle.Regular);
				label.TextAlign = ContentAlignment.MiddleCenter;
				label.Dock = DockStyle.Fill;
				panel.Controls.Add(label);
			}
			panel_0.Controls.Clear();
			panel_0.Controls.Add(panel);
		}

		// Token: 0x0600161D RID: 5661 RVA: 0x00093A04 File Offset: 0x00091C04
		private void method_12(Panel panel_0)
		{
			if (panel_0.Controls.Count > 0)
			{
				Control control = panel_0.Controls[0];
				if (control is Panel && !(control is TabControlPanel))
				{
					Panel panel = control as Panel;
					if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
					{
						panel.BackColor = Class179.color_3;
					}
					else
					{
						panel.BackColor = Class179.color_9;
					}
					if (panel.Controls.Count > 0)
					{
						System.Windows.Forms.Label label = panel.Controls[0] as System.Windows.Forms.Label;
						if (label != null)
						{
							if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
							{
								label.ForeColor = Class179.color_4;
							}
							else
							{
								label.ForeColor = Color.LightGray;
							}
						}
					}
				}
			}
		}

		// Token: 0x0600161E RID: 5662 RVA: 0x00093AB4 File Offset: 0x00091CB4
		private void method_13(object sender, SuperTabStripSelectedTabChangedEventArgs e)
		{
			SuperTabItem superTabItem = e.NewValue as SuperTabItem;
			SuperTabControlPanel superTabControlPanel_ = superTabItem.AttachedControl as SuperTabControlPanel;
			if (superTabItem.Text == "六大指标")
			{
				this.method_14(superTabControlPanel_);
			}
			else if (superTabItem.Text == "资产负债表")
			{
				this.method_15(superTabControlPanel_);
			}
			else if (superTabItem.Text == "损益表")
			{
				this.method_16(superTabControlPanel_);
			}
			else if (superTabItem.Text == "现金流量表")
			{
				this.method_17(superTabControlPanel_);
			}
			this.method_29();
		}

		// Token: 0x0600161F RID: 5663 RVA: 0x00093B50 File Offset: 0x00091D50
		private DevComponents.DotNetBar.TabControl method_14(SuperTabControlPanel superTabControlPanel_0 = null)
		{
			if (superTabControlPanel_0 == null)
			{
				superTabControlPanel_0 = this.tabCtrlPanel_Main6Ind;
			}
			string[] string_ = new string[]
			{
				"利润总额",
				"每股净资产",
				"净资产收益率",
				"产品毛利率",
				"应收账款",
				"预收款",
				"扣非后净利润(附)"
			};
			return this.method_18(superTabControlPanel_0, string_);
		}

		// Token: 0x06001620 RID: 5664 RVA: 0x00093BB4 File Offset: 0x00091DB4
		private DevComponents.DotNetBar.TabControl method_15(SuperTabControlPanel superTabControlPanel_0 = null)
		{
			if (superTabControlPanel_0 == null)
			{
				superTabControlPanel_0 = this.tabCtrlPanel_BalSheet;
			}
			string[] string_ = "应收账款,存货,流动资产,固定资产,资产总计,应付账款,流动负债,负债合计,所有者权益".Split(new string[]
			{
				","
			}, StringSplitOptions.None);
			return this.method_18(superTabControlPanel_0, string_);
		}

		// Token: 0x06001621 RID: 5665 RVA: 0x00093BF4 File Offset: 0x00091DF4
		private DevComponents.DotNetBar.TabControl method_16(SuperTabControlPanel superTabControlPanel_0 = null)
		{
			if (superTabControlPanel_0 == null)
			{
				superTabControlPanel_0 = this.tabCtrlPanel_Income;
			}
			string[] string_ = new string[]
			{
				"营业收入",
				"营业成本",
				"营业利润",
				"销售费用",
				"利润总额",
				"净利润"
			};
			return this.method_18(superTabControlPanel_0, string_);
		}

		// Token: 0x06001622 RID: 5666 RVA: 0x00093C50 File Offset: 0x00091E50
		private DevComponents.DotNetBar.TabControl method_17(SuperTabControlPanel superTabControlPanel_0 = null)
		{
			if (superTabControlPanel_0 == null)
			{
				superTabControlPanel_0 = this.tabCtrlPanel_CashFlow;
			}
			string[] string_ = new string[]
			{
				"净利润",
				"经营活动现金流入",
				"企业自由现金流量",
				"支付的各项税费",
				"投资损失",
				"存货的减少"
			};
			return this.method_18(superTabControlPanel_0, string_);
		}

		// Token: 0x06001623 RID: 5667 RVA: 0x00093CAC File Offset: 0x00091EAC
		private DevComponents.DotNetBar.TabControl method_18(SuperTabControlPanel superTabControlPanel_0, string[] string_0)
		{
			DevComponents.DotNetBar.TabControl tabControl;
			if (superTabControlPanel_0.Controls.Count < 1)
			{
				tabControl = this.method_1(string_0, true);
				superTabControlPanel_0.Controls.Add(tabControl);
			}
			else
			{
				tabControl = (superTabControlPanel_0.Controls[0] as DevComponents.DotNetBar.TabControl);
			}
			return tabControl;
		}

		// Token: 0x06001624 RID: 5668 RVA: 0x00008ECF File Offset: 0x000070CF
		private void method_19(object sender, EventArgs e)
		{
			this.method_27();
			this.method_29();
		}

		// Token: 0x06001625 RID: 5669 RVA: 0x00008ECF File Offset: 0x000070CF
		private void method_20(object sender, EventArgs e)
		{
			this.method_27();
			this.method_29();
		}

		// Token: 0x06001626 RID: 5670 RVA: 0x00093CF8 File Offset: 0x00091EF8
		private void method_21(EventArgs1 eventArgs1_0)
		{
			this.method_23();
			this.method_25();
			StkSymbol stkSymbol = this.method_26();
			if (stkSymbol != null)
			{
				if (!stkSymbol.IsStockCompany)
				{
					this.method_6();
					this.class285_0 = null;
				}
				else
				{
					this.method_29();
				}
			}
		}

		// Token: 0x06001627 RID: 5671 RVA: 0x00008EDF File Offset: 0x000070DF
		private void method_22(EventArgs1 eventArgs1_0)
		{
			this.method_24();
		}

		// Token: 0x06001628 RID: 5672 RVA: 0x00008EE9 File Offset: 0x000070E9
		private void method_23()
		{
			if (Base.Data.CurrSymbDataSet != null)
			{
				Base.Data.CurrSymbDataSet.CurrDateChanged += this.method_19;
			}
		}

		// Token: 0x06001629 RID: 5673 RVA: 0x00008F0A File Offset: 0x0000710A
		private void method_24()
		{
			if (Base.Data.CurrSymbDataSet != null)
			{
				Base.Data.CurrSymbDataSet.CurrDateChanged -= this.method_19;
			}
		}

		// Token: 0x0600162A RID: 5674 RVA: 0x00093D3C File Offset: 0x00091F3C
		public void method_25()
		{
			if (Base.UI.Form.IsInBlindTestMode)
			{
				this.label_CurrSymb.Text = "●●●●(●●●●●●)";
				string caption = "双盲模式下隐藏品种名";
				this.toolTip_0.SetToolTip(this.label_CurrSymb, caption);
			}
			else
			{
				StkSymbol stkSymbol = this.method_26();
				if (stkSymbol != null)
				{
					this.label_CurrSymb.Text = stkSymbol.CNName + "(" + stkSymbol.Code + ")";
				}
				else
				{
					this.label_CurrSymb.Text = "";
				}
				this.toolTip_0.SetToolTip(this.label_CurrSymb, "");
			}
		}

		// Token: 0x0600162B RID: 5675 RVA: 0x00093DDC File Offset: 0x00091FDC
		private StkSymbol method_26()
		{
			StkSymbol stkSymbol = Base.Data.CurrSelectedSymbol;
			if (stkSymbol == null && Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl.SymbDataSet != null)
			{
				stkSymbol = Base.UI.SelectedChtCtrl.SymbDataSet.CurrSymbol;
			}
			return stkSymbol;
		}

		// Token: 0x0600162C RID: 5676 RVA: 0x00093E1C File Offset: 0x0009201C
		public void method_27()
		{
			if (Base.UI.Form.IsInBlindTestMode)
			{
				this.label_CurrDate.Text = "●●●●-●●-●●";
				string caption = "双盲模式下隐藏时间";
				this.toolTip_0.SetToolTip(this.label_CurrDate, caption);
			}
			else
			{
				string text = string.Empty;
				DateTime? dateTime = Base.Data.smethod_54();
				if (dateTime != null)
				{
					text = dateTime.Value.ToString("yyyy-MM-dd");
				}
				if (!string.IsNullOrEmpty(text))
				{
					this.label_CurrDate.Text = text;
				}
				else
				{
					this.label_CurrDate.Text = "";
				}
				this.toolTip_0.SetToolTip(this.label_CurrDate, "");
			}
		}

		// Token: 0x0600162D RID: 5677 RVA: 0x00093EC8 File Offset: 0x000920C8
		public void method_28()
		{
			foreach (object obj in this.superTabCtrl_RptAnlys.Controls)
			{
				if (obj is SuperTabControlPanel)
				{
					SuperTabControlPanel superTabControlPanel = obj as SuperTabControlPanel;
					if (superTabControlPanel.Controls.Count > 0)
					{
						Base.UI.smethod_69(superTabControlPanel.Controls[0] as DevComponents.DotNetBar.TabControl);
					}
				}
			}
		}

		// Token: 0x0600162E RID: 5678 RVA: 0x00093F50 File Offset: 0x00092150
		private void method_29()
		{
			if (this.class557_0 != null)
			{
				StkSymbol stkSymbol = this.method_26();
				if (stkSymbol != null && stkSymbol.IsStockCompany)
				{
					string text = this.superTabCtrl_RptAnlys.SelectedTab.Text;
					Dictionary<string, object> dictionary = new Dictionary<string, object>();
					string text2 = this.method_32();
					DateTime? dateTime = Base.Data.smethod_54();
					if (!string.IsNullOrEmpty(text2) && dateTime != null)
					{
						string quarterEndDateStr = Utility.GetQuarterEndDateStr(dateTime.Value, -1, "");
						Enum2 @enum;
						string gparam_;
						if (text == "六大指标")
						{
							@enum = Enum2.const_0;
							gparam_ = "GetMain6Inds";
						}
						else if (text == "资产负债表")
						{
							@enum = Enum2.const_1;
							gparam_ = "GetBalSheet";
						}
						else if (text == "损益表")
						{
							@enum = Enum2.const_2;
							gparam_ = "GetIncome";
						}
						else
						{
							@enum = Enum2.const_3;
							gparam_ = "GetCashFlow";
						}
						Class15 @class = this.method_31(@enum, text2, quarterEndDateStr);
						if (@class == null)
						{
							if (this.method_30(text, text2, quarterEndDateStr))
							{
								string yearEndDateStr = Utility.GetYearEndDateStr(dateTime.Value, -10, "");
								Class1<string, string, int, Class9<string, string, string>> value = new Class1<string, string, int, Class9<string, string, string>>(gparam_, TApp.LoginCode, this.class557_0.ResultCompressed ? 1 : 0, new Class9<string, string, string>(text2, yearEndDateStr, quarterEndDateStr));
								dictionary["postData"] = value;
								dictionary["rptAnlysType"] = @enum;
								dictionary["tsCode"] = text2;
								dictionary["endDate"] = quarterEndDateStr;
								this.class557_0.GetHttpResponseInBackground(dictionary);
								this.dictionary_0[text] = new KeyValuePair<string, string>(text2, quarterEndDateStr);
							}
						}
						else
						{
							this.method_33(@class.ReportAnlysType, @class.SrcYrDataTable, @class.SrcQtDataTable);
							this.method_37(null, null);
						}
					}
				}
			}
		}

		// Token: 0x0600162F RID: 5679 RVA: 0x0009410C File Offset: 0x0009230C
		private bool method_30(string string_0, string string_1, string string_2)
		{
			bool result = true;
			if (this.dictionary_0 != null && this.dictionary_0.ContainsKey(string_0))
			{
				KeyValuePair<string, string> keyValuePair = this.dictionary_0[string_0];
				if (keyValuePair.Key == string_1 && keyValuePair.Value == string_2)
				{
					result = false;
				}
			}
			return result;
		}

		// Token: 0x06001630 RID: 5680 RVA: 0x00094164 File Offset: 0x00092364
		private Class15 method_31(Enum2 enum2_0, string string_0, string string_1)
		{
			FnRptAnlysPanel.Class295 @class = new FnRptAnlysPanel.Class295();
			@class.enum2_0 = enum2_0;
			@class.string_0 = string_0;
			@class.string_1 = string_1;
			Class15 result = null;
			if (this.list_0 != null)
			{
				result = this.list_0.SingleOrDefault(new Func<Class15, bool>(@class.method_0));
			}
			return result;
		}

		// Token: 0x06001631 RID: 5681 RVA: 0x000941B4 File Offset: 0x000923B4
		private string method_32()
		{
			string result = null;
			StkSymbol stkSymbol = this.method_26();
			if (stkSymbol != null)
			{
				result = TExRoutine.GetCodeForFnDataApi(stkSymbol.Code, stkSymbol.ExchangeID);
			}
			return result;
		}

		// Token: 0x06001632 RID: 5682 RVA: 0x000941E4 File Offset: 0x000923E4
		private void class557_0_ResultReceived(object sender, WebApiEventArgs e)
		{
			if (e.ApiResult != null)
			{
				Dictionary<string, object> requestDict = e.RequestDict;
				if (requestDict.ContainsKey("yrDataTbl"))
				{
					Enum2 @enum = (Enum2)requestDict["rptAnlysType"];
					DataTable dataTable = requestDict["yrDataTbl"] as DataTable;
					DataTable dataTable2 = requestDict["qtDataTbl"] as DataTable;
					this.method_33(@enum, dataTable, dataTable2);
					this.method_37(null, null);
					string string_ = requestDict["tsCode"] as string;
					string string_2 = requestDict["endDate"] as string;
					DataTable dataTable_ = requestDict["dataTbl"] as DataTable;
					Class15 item = new Class15(@enum, string_, string_2, dataTable_, dataTable, dataTable2);
					this.list_0.Add(item);
				}
			}
		}

		// Token: 0x06001633 RID: 5683 RVA: 0x00008F2B File Offset: 0x0000712B
		private void class557_0_RequestError(object sender, ErrorEventArgs e)
		{
			Class182.smethod_0(e.Exception);
		}

		// Token: 0x06001634 RID: 5684 RVA: 0x000942AC File Offset: 0x000924AC
		private void method_33(Enum2 enum2_0, DataTable dataTable_0, DataTable dataTable_1)
		{
			if (this.tabCtrlPanel_FnData_byYr.Controls.Count > 0 && this.tabCtrlPanel_FnData_byYr.Controls[0] is Class285)
			{
				this.class285_0 = (this.tabCtrlPanel_FnData_byYr.Controls[0] as Class285);
			}
			else
			{
				this.class285_0 = new Class285();
				this.class285_0.SelectionChanged += this.class285_1_SelectionChanged;
				this.tabCtrlPanel_FnData_byYr.Controls.Clear();
				this.tabCtrlPanel_FnData_byYr.Controls.Add(this.class285_0);
			}
			DataTable rotatedDataTable = Utility.GetRotatedDataTable(dataTable_0, true, new int?(7));
			this.class285_0.ReportAnlysType = enum2_0;
			this.class285_0.SrcFnDataTable = dataTable_0;
			this.class285_0.method_1(rotatedDataTable);
			if (this.tabCtrlPanel_FnData_byQt.Controls.Count > 0 && this.tabCtrlPanel_FnData_byQt.Controls[0] is Class285)
			{
				this.class285_1 = (this.tabCtrlPanel_FnData_byQt.Controls[0] as Class285);
			}
			else
			{
				this.class285_1 = new Class285();
				this.class285_1.SelectionChanged += this.class285_1_SelectionChanged;
				this.tabCtrlPanel_FnData_byQt.Controls.Clear();
				this.tabCtrlPanel_FnData_byQt.Controls.Add(this.class285_1);
			}
			DataTable rotatedDataTable2 = Utility.GetRotatedDataTable(dataTable_1, true, new int?(7));
			this.class285_1.ReportAnlysType = enum2_0;
			this.class285_1.SrcFnDataTable = dataTable_1;
			this.class285_1.method_1(rotatedDataTable2);
		}

		// Token: 0x06001635 RID: 5685 RVA: 0x00094444 File Offset: 0x00092644
		private void tabControl_FnRpts_SelectedTabChanged(object sender, TabStripTabChangedEventArgs e)
		{
			Class285 class285_ = this.method_35();
			this.method_37(class285_, null);
		}

		// Token: 0x06001636 RID: 5686 RVA: 0x00094464 File Offset: 0x00092664
		private void method_34(object sender, TabStripTabChangedEventArgs e)
		{
			DevComponents.DotNetBar.TabControl tabControl = sender as DevComponents.DotNetBar.TabControl;
			bool flag = false;
			if (tabControl.SelectedPanel.Controls.Count > 0 && tabControl.SelectedPanel.Controls[0] is TabControlPanel)
			{
				flag = true;
			}
			string text = e.NewTab.Text;
			if (!flag)
			{
				Class285 class285_ = this.method_35();
				this.method_37(class285_, text);
			}
			Class285 @class = this.method_35();
			if (@class != null)
			{
				foreach (object obj in ((IEnumerable)@class.Rows))
				{
					DataGridViewRow dataGridViewRow = (DataGridViewRow)obj;
					if (((string)dataGridViewRow.Cells[0].Value).StartsWith(text))
					{
						@class.IsSettingDataSource = true;
						if (!dataGridViewRow.Selected)
						{
							dataGridViewRow.Selected = true;
						}
						@class.IsSettingDataSource = false;
						break;
					}
				}
			}
		}

		// Token: 0x06001637 RID: 5687 RVA: 0x00094564 File Offset: 0x00092764
		private Class285 method_35()
		{
			Class285 result = this.class285_0;
			if (this.tabControl_FnRpts.SelectedTabIndex == 1)
			{
				result = this.class285_1;
			}
			return result;
		}

		// Token: 0x06001638 RID: 5688 RVA: 0x00094594 File Offset: 0x00092794
		private void class285_1_SelectionChanged(object sender, EventArgs e)
		{
			Class285 @class = sender as Class285;
			if (@class != null && !@class.IsSettingDataSource && @class.SelectedRows != null && @class.SelectedRows.Count > 0)
			{
				TabControlPanel tabControlPanel = @class.Parent as TabControlPanel;
				if (tabControlPanel != null && tabControlPanel.TabItem.IsSelected && @class.method_3() != null)
				{
					this.method_37(@class, null);
				}
			}
		}

		// Token: 0x06001639 RID: 5689 RVA: 0x000945F8 File Offset: 0x000927F8
		private DevComponents.DotNetBar.TabControl method_36(Enum2 enum2_0)
		{
			DevComponents.DotNetBar.TabControl result = null;
			if (enum2_0 == Enum2.const_0)
			{
				result = this.method_14(null);
			}
			else if (enum2_0 == Enum2.const_1)
			{
				result = this.method_15(null);
			}
			else if (enum2_0 == Enum2.const_2)
			{
				result = this.method_16(null);
			}
			else if (enum2_0 == Enum2.const_3)
			{
				result = this.method_17(null);
			}
			return result;
		}

		// Token: 0x0600163A RID: 5690 RVA: 0x00008F3A File Offset: 0x0000713A
		private void btn_ChangeDate_Click(object sender, EventArgs e)
		{
			Base.UI.MainForm.method_81();
		}

		// Token: 0x0600163B RID: 5691 RVA: 0x00094644 File Offset: 0x00092844
		private void method_37(Class285 class285_2 = null, string string_0 = null)
		{
			if (class285_2 == null)
			{
				class285_2 = this.class285_0;
			}
			if (class285_2 != null && class285_2.Rows.Count >= 1)
			{
				if (string.IsNullOrEmpty(string_0))
				{
					DataGridViewRow dataGridViewRow = class285_2.method_3();
					if (dataGridViewRow != null)
					{
						string_0 = (string)dataGridViewRow.Cells[0].Value;
					}
				}
				if (!string.IsNullOrEmpty(string_0))
				{
					DevComponents.DotNetBar.TabControl tabControl = this.method_36(class285_2.ReportAnlysType);
					TabControlPanel tabControlPanel = null;
					foreach (object obj in tabControl.Controls)
					{
						if (obj is TabControlPanel)
						{
							TabControlPanel tabControlPanel2 = obj as TabControlPanel;
							if (string_0.Contains(tabControlPanel2.TabItem.Text))
							{
								tabControlPanel = tabControlPanel2;
								break;
							}
						}
					}
					if (tabControlPanel == null)
					{
						TabItem tabItem = new TabItem();
						tabItem.Text = string_0;
						tabControlPanel = new TabControlPanel();
						tabControlPanel.Dock = DockStyle.Fill;
						tabControlPanel.Location = new Point(0, 0);
						tabControlPanel.Padding = new System.Windows.Forms.Padding(0);
						tabControlPanel.Size = new Size(596, 278);
						tabControlPanel.Style.BackColor1.Color = SystemColors.Control;
						tabControlPanel.Style.Border = eBorderType.SingleLine;
						tabControlPanel.Style.BorderColor.Color = Color.FromArgb(93, 93, 93);
						tabControlPanel.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
						tabControlPanel.Style.GradientAngle = -90;
						tabControlPanel.TabItem = tabItem;
						tabItem.AttachedControl = tabControlPanel;
						tabControl.Tabs.Add(tabItem);
					}
					else if (tabControlPanel.Controls.Count > 0)
					{
						Control control = tabControlPanel.Controls[0];
						if (control is GraphCtrlStat)
						{
							(control as GraphCtrlStat).method_3();
						}
						tabControlPanel.Controls.Clear();
					}
					DataTable rotatedDataTable = Utility.GetRotatedDataTable(class285_2.SrcFnDataTable, true, new int?(10));
					this.method_38(tabControlPanel, rotatedDataTable);
					tabControl.SelectedPanel = tabControlPanel;
				}
			}
		}

		// Token: 0x0600163C RID: 5692 RVA: 0x00094854 File Offset: 0x00092A54
		private void method_38(TabControlPanel tabControlPanel_0, DataTable dataTable_0)
		{
			string text = tabControlPanel_0.TabItem.Text;
			DataRow dataRow = dataTable_0.Select(string.Concat(new object[]
			{
				dataTable_0.Columns[0],
				" LIKE '",
				text,
				"%'"
			})).FirstOrDefault<DataRow>();
			if (dataRow != null)
			{
				DataColumnCollection columns = dataTable_0.Columns;
				object[] itemArray = dataRow.ItemArray;
				this.method_39(tabControlPanel_0, columns, itemArray);
			}
		}

		// Token: 0x0600163D RID: 5693 RVA: 0x000948C4 File Offset: 0x00092AC4
		private void method_39(TabControlPanel tabControlPanel_0, DataColumnCollection dataColumnCollection_0, object[] object_0)
		{
			string text = tabControlPanel_0.TabItem.Text;
			GraphCtrlStat graphCtrlStat = new GraphCtrlStat(false);
			GraphPane graphPane = graphCtrlStat.GraphPane;
			graphPane.Title.Text = text;
			graphPane.XAxis.Type = AxisType.Text;
			graphPane.XAxis.Title.IsVisible = false;
			graphPane.YAxis.Title.Text = text;
			graphPane.Legend.IsVisible = false;
			graphPane.Chart.Border.IsVisible = false;
			if (object_0 != null)
			{
				List<string> list = new List<string>();
				List<double> list2 = new List<double>();
				int i = object_0.Length - 1;
				while (i > 0)
				{
					double item = double.NaN;
					object obj = object_0[i];
					if (obj == null || obj.GetType() != typeof(string))
					{
						goto IL_ED;
					}
					string text2 = obj as string;
					if (!string.IsNullOrEmpty(text2) && text2 != "非数字")
					{
						try
						{
							item = Convert.ToDouble(obj);
							goto IL_FC;
						}
						catch (Exception exception_)
						{
							Class182.smethod_0(exception_);
							goto IL_FC;
						}
						goto IL_ED;
					}
					IL_FC:
					list2.Add(item);
					list.Add(dataColumnCollection_0[i].ColumnName);
					i--;
					continue;
					IL_ED:
					try
					{
						item = Convert.ToDouble(obj);
					}
					catch
					{
					}
					goto IL_FC;
				}
				double[] array = list2.ToArray();
				Color color_ = Class179.color_17;
				graphPane.AddBar(text, null, array, color_).Bar.Border.IsVisible = false;
				graphCtrlStat.method_1(array);
				graphPane.XAxis.Scale.TextLabels = list.ToArray();
			}
			else
			{
				graphPane.YAxis.Scale.IsVisible = false;
			}
			graphCtrlStat.AxisChange();
			graphCtrlStat.Refresh();
			try
			{
				tabControlPanel_0.smethod_0();
				tabControlPanel_0.SuspendLayout();
				if (tabControlPanel_0.Controls.Count > 0)
				{
					Control control = tabControlPanel_0.Controls[0];
					if (control is GraphCtrlStat)
					{
						(control as GraphCtrlStat).method_3();
					}
					tabControlPanel_0.Controls.Clear();
				}
				tabControlPanel_0.Controls.Add(graphCtrlStat);
				tabControlPanel_0.ResumeLayout();
				tabControlPanel_0.smethod_1();
			}
			catch (Exception exception_2)
			{
				Class182.smethod_0(exception_2);
			}
		}

		// Token: 0x0600163E RID: 5694 RVA: 0x00094AF0 File Offset: 0x00092CF0
		protected void CreateHandle()
		{
			if (!base.IsHandleCreated)
			{
				try
				{
					base.CreateHandle();
				}
				catch
				{
				}
				finally
				{
					if (!base.IsHandleCreated)
					{
						base.RecreateHandle();
					}
				}
			}
		}

		// Token: 0x0600163F RID: 5695 RVA: 0x00008F48 File Offset: 0x00007148
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001640 RID: 5696 RVA: 0x00094B40 File Offset: 0x00092D40
		private void InitializeComponent()
		{
			this.icontainer_0 = new Container();
			this.tablePanel_bg = new TableLayoutPanel();
			this.tablePanel_Header = new TableLayoutPanel();
			this.label_CurrSymb = new System.Windows.Forms.Label();
			this.label_CurrDate = new System.Windows.Forms.Label();
			this.btn_ChangeDate = new Button();
			this.label_当前日期 = new System.Windows.Forms.Label();
			this.tablePanel_Bottom = new TableLayoutPanel();
			this.tabControl_FnRpts = new DevComponents.DotNetBar.TabControl();
			this.tabCtrlPanel_FnData_byYr = new TabControlPanel();
			this.tabItem_FnRpt_byYr = new TabItem(this.icontainer_0);
			this.tabCtrlPanel_FnData_byQt = new TabControlPanel();
			this.tabItem_FnRpt_byQt = new TabItem(this.icontainer_0);
			this.superTabCtrl_RptAnlys = new SuperTabControl();
			this.tabCtrlPanel_Main6Ind = new SuperTabControlPanel();
			this.rptChart_TabItem_SixInds = new SuperTabItem();
			this.tabCtrlPanel_CashFlow = new SuperTabControlPanel();
			this.rptChart_TabItem_CashFlow = new SuperTabItem();
			this.tabCtrlPanel_Income = new SuperTabControlPanel();
			this.rptChart_TabItem_Income = new SuperTabItem();
			this.tabCtrlPanel_BalSheet = new SuperTabControlPanel();
			this.rptChart_TabItem_BalSheet = new SuperTabItem();
			this.tablePanel_bg.SuspendLayout();
			this.tablePanel_Header.SuspendLayout();
			this.tablePanel_Bottom.SuspendLayout();
			((ISupportInitialize)this.tabControl_FnRpts).BeginInit();
			this.tabControl_FnRpts.SuspendLayout();
			((ISupportInitialize)this.superTabCtrl_RptAnlys).BeginInit();
			this.superTabCtrl_RptAnlys.SuspendLayout();
			base.SuspendLayout();
			this.tablePanel_bg.ColumnCount = 1;
			this.tablePanel_bg.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
			this.tablePanel_bg.Controls.Add(this.tablePanel_Header, 0, 0);
			this.tablePanel_bg.Controls.Add(this.tablePanel_Bottom, 0, 1);
			this.tablePanel_bg.Dock = DockStyle.Fill;
			this.tablePanel_bg.Location = new Point(0, 0);
			this.tablePanel_bg.Margin = new System.Windows.Forms.Padding(0);
			this.tablePanel_bg.Name = "tablePanel_bg";
			this.tablePanel_bg.RowCount = 2;
			this.tablePanel_bg.RowStyles.Add(new RowStyle(SizeType.Absolute, 31f));
			this.tablePanel_bg.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tablePanel_bg.Size = new Size(1324, 316);
			this.tablePanel_bg.TabIndex = 0;
			this.tablePanel_Header.ColumnCount = 4;
			this.tablePanel_Header.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 200f));
			this.tablePanel_Header.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 110f));
			this.tablePanel_Header.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 110f));
			this.tablePanel_Header.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
			this.tablePanel_Header.Controls.Add(this.label_CurrSymb, 0, 0);
			this.tablePanel_Header.Controls.Add(this.label_CurrDate, 2, 0);
			this.tablePanel_Header.Controls.Add(this.btn_ChangeDate, 3, 0);
			this.tablePanel_Header.Controls.Add(this.label_当前日期, 1, 0);
			this.tablePanel_Header.Dock = DockStyle.Fill;
			this.tablePanel_Header.Location = new Point(0, 0);
			this.tablePanel_Header.Margin = new System.Windows.Forms.Padding(0);
			this.tablePanel_Header.Name = "tablePanel_Header";
			this.tablePanel_Header.RowCount = 1;
			this.tablePanel_Header.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tablePanel_Header.Size = new Size(1324, 31);
			this.tablePanel_Header.TabIndex = 0;
			this.label_CurrSymb.Anchor = AnchorStyles.None;
			this.label_CurrSymb.AutoSize = true;
			this.label_CurrSymb.BackColor = Color.Transparent;
			this.label_CurrSymb.Location = new Point(34, 8);
			this.label_CurrSymb.Name = "label_CurrSymb";
			this.label_CurrSymb.Size = new Size(131, 15);
			this.label_CurrSymb.TabIndex = 0;
			this.label_CurrSymb.Text = "贵州茅台(600519)";
			this.label_CurrDate.Anchor = AnchorStyles.Left;
			this.label_CurrDate.AutoSize = true;
			this.label_CurrDate.BackColor = Color.Transparent;
			this.label_CurrDate.Location = new Point(313, 8);
			this.label_CurrDate.Name = "label_CurrDate";
			this.label_CurrDate.Size = new Size(87, 15);
			this.label_CurrDate.TabIndex = 1;
			this.label_CurrDate.Text = "2005-06-30";
			this.btn_ChangeDate.Anchor = AnchorStyles.Left;
			this.btn_ChangeDate.BackColor = Color.Transparent;
			this.btn_ChangeDate.Location = new Point(422, 2);
			this.btn_ChangeDate.Margin = new System.Windows.Forms.Padding(2);
			this.btn_ChangeDate.Name = "btn_ChangeDate";
			this.btn_ChangeDate.Size = new Size(87, 27);
			this.btn_ChangeDate.TabIndex = 2;
			this.btn_ChangeDate.Text = "切换日期";
			this.btn_ChangeDate.UseVisualStyleBackColor = false;
			this.label_当前日期.Anchor = AnchorStyles.Right;
			this.label_当前日期.AutoSize = true;
			this.label_当前日期.BackColor = Color.Transparent;
			this.label_当前日期.Location = new Point(232, 8);
			this.label_当前日期.Name = "label_当前日期";
			this.label_当前日期.Size = new Size(75, 15);
			this.label_当前日期.TabIndex = 3;
			this.label_当前日期.Text = "当前日期:";
			this.tablePanel_Bottom.BackColor = Color.Transparent;
			this.tablePanel_Bottom.ColumnCount = 2;
			this.tablePanel_Bottom.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50f));
			this.tablePanel_Bottom.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50f));
			this.tablePanel_Bottom.Controls.Add(this.tabControl_FnRpts, 1, 0);
			this.tablePanel_Bottom.Controls.Add(this.superTabCtrl_RptAnlys, 0, 0);
			this.tablePanel_Bottom.Dock = DockStyle.Fill;
			this.tablePanel_Bottom.Location = new Point(0, 31);
			this.tablePanel_Bottom.Margin = new System.Windows.Forms.Padding(0);
			this.tablePanel_Bottom.Name = "tablePanel_Bottom";
			this.tablePanel_Bottom.RowCount = 1;
			this.tablePanel_Bottom.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tablePanel_Bottom.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
			this.tablePanel_Bottom.Size = new Size(1324, 285);
			this.tablePanel_Bottom.TabIndex = 1;
			this.tabControl_FnRpts.BackColor = Color.Transparent;
			this.tabControl_FnRpts.CanReorderTabs = true;
			this.tabControl_FnRpts.Controls.Add(this.tabCtrlPanel_FnData_byYr);
			this.tabControl_FnRpts.Controls.Add(this.tabCtrlPanel_FnData_byQt);
			this.tabControl_FnRpts.Dock = DockStyle.Fill;
			this.tabControl_FnRpts.Location = new Point(662, 0);
			this.tabControl_FnRpts.Margin = new System.Windows.Forms.Padding(0);
			this.tabControl_FnRpts.Name = "tabControl_FnRpts";
			this.tabControl_FnRpts.SelectedTabFont = new Font("SimSun", 9f, FontStyle.Bold);
			this.tabControl_FnRpts.SelectedTabIndex = 0;
			this.tabControl_FnRpts.Size = new Size(662, 285);
			this.tabControl_FnRpts.Style = eTabStripStyle.Flat;
			this.tabControl_FnRpts.TabAlignment = eTabStripAlignment.Bottom;
			this.tabControl_FnRpts.TabIndex = 0;
			this.tabControl_FnRpts.TabLayoutType = eTabLayoutType.FixedWithNavigationBox;
			this.tabControl_FnRpts.Tabs.Add(this.tabItem_FnRpt_byYr);
			this.tabControl_FnRpts.Tabs.Add(this.tabItem_FnRpt_byQt);
			this.tabControl_FnRpts.Text = "tabControl1";
			this.tabCtrlPanel_FnData_byYr.Dock = DockStyle.Fill;
			this.tabCtrlPanel_FnData_byYr.Location = new Point(0, 0);
			this.tabCtrlPanel_FnData_byYr.Name = "tabCtrlPanel_FnData_byYr";
			this.tabCtrlPanel_FnData_byYr.Padding = new System.Windows.Forms.Padding(1);
			this.tabCtrlPanel_FnData_byYr.Size = new Size(662, 257);
			this.tabCtrlPanel_FnData_byYr.Style.BackColor1.Color = SystemColors.Control;
			this.tabCtrlPanel_FnData_byYr.Style.Border = eBorderType.SingleLine;
			this.tabCtrlPanel_FnData_byYr.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabCtrlPanel_FnData_byYr.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabCtrlPanel_FnData_byYr.Style.GradientAngle = -90;
			this.tabCtrlPanel_FnData_byYr.TabIndex = 1;
			this.tabCtrlPanel_FnData_byYr.TabItem = this.tabItem_FnRpt_byYr;
			this.tabItem_FnRpt_byYr.AttachedControl = this.tabCtrlPanel_FnData_byYr;
			this.tabItem_FnRpt_byYr.Name = "tabItem_FnRpt_byYr";
			this.tabItem_FnRpt_byYr.Text = "按年度";
			this.tabCtrlPanel_FnData_byQt.Dock = DockStyle.Fill;
			this.tabCtrlPanel_FnData_byQt.Location = new Point(0, 0);
			this.tabCtrlPanel_FnData_byQt.Name = "tabCtrlPanel_FnData_byQt";
			this.tabCtrlPanel_FnData_byQt.Padding = new System.Windows.Forms.Padding(1);
			this.tabCtrlPanel_FnData_byQt.Size = new Size(662, 257);
			this.tabCtrlPanel_FnData_byQt.Style.BackColor1.Color = SystemColors.Control;
			this.tabCtrlPanel_FnData_byQt.Style.Border = eBorderType.SingleLine;
			this.tabCtrlPanel_FnData_byQt.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabCtrlPanel_FnData_byQt.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabCtrlPanel_FnData_byQt.Style.GradientAngle = -90;
			this.tabCtrlPanel_FnData_byQt.TabIndex = 2;
			this.tabCtrlPanel_FnData_byQt.TabItem = this.tabItem_FnRpt_byQt;
			this.tabItem_FnRpt_byQt.AttachedControl = this.tabCtrlPanel_FnData_byQt;
			this.tabItem_FnRpt_byQt.Name = "tabItem_FnRpt_byQt";
			this.tabItem_FnRpt_byQt.Text = "按季度";
			this.superTabCtrl_RptAnlys.ControlBox.CloseBox.Name = "";
			this.superTabCtrl_RptAnlys.ControlBox.MenuBox.Name = "";
			this.superTabCtrl_RptAnlys.ControlBox.Name = "";
			this.superTabCtrl_RptAnlys.ControlBox.SubItems.AddRange(new BaseItem[]
			{
				this.superTabCtrl_RptAnlys.ControlBox.MenuBox,
				this.superTabCtrl_RptAnlys.ControlBox.CloseBox
			});
			this.superTabCtrl_RptAnlys.Controls.Add(this.tabCtrlPanel_Main6Ind);
			this.superTabCtrl_RptAnlys.Controls.Add(this.tabCtrlPanel_CashFlow);
			this.superTabCtrl_RptAnlys.Controls.Add(this.tabCtrlPanel_Income);
			this.superTabCtrl_RptAnlys.Controls.Add(this.tabCtrlPanel_BalSheet);
			this.superTabCtrl_RptAnlys.Dock = DockStyle.Fill;
			this.superTabCtrl_RptAnlys.Location = new Point(0, 0);
			this.superTabCtrl_RptAnlys.Margin = new System.Windows.Forms.Padding(0);
			this.superTabCtrl_RptAnlys.Name = "superTabCtrl_RptAnlys";
			this.superTabCtrl_RptAnlys.ReorderTabsEnabled = true;
			this.superTabCtrl_RptAnlys.SelectedTabFont = new Font("SimSun", 9.5f, FontStyle.Bold);
			this.superTabCtrl_RptAnlys.SelectedTabIndex = 0;
			this.superTabCtrl_RptAnlys.Size = new Size(662, 285);
			this.superTabCtrl_RptAnlys.TabAlignment = eTabStripAlignment.Left;
			this.superTabCtrl_RptAnlys.TabFont = new Font("SimSun", 9.5f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.superTabCtrl_RptAnlys.TabIndex = 1;
			this.superTabCtrl_RptAnlys.Tabs.AddRange(new BaseItem[]
			{
				this.rptChart_TabItem_SixInds,
				this.rptChart_TabItem_BalSheet,
				this.rptChart_TabItem_Income,
				this.rptChart_TabItem_CashFlow
			});
			this.superTabCtrl_RptAnlys.TabStyle = eSuperTabStyle.Office2010BackstageBlue;
			this.superTabCtrl_RptAnlys.Text = "superTabControl1";
			this.tabCtrlPanel_Main6Ind.Dock = DockStyle.Fill;
			this.tabCtrlPanel_Main6Ind.Location = new Point(109, 0);
			this.tabCtrlPanel_Main6Ind.Margin = new System.Windows.Forms.Padding(0);
			this.tabCtrlPanel_Main6Ind.Name = "tabCtrlPanel_Main6Ind";
			this.tabCtrlPanel_Main6Ind.Size = new Size(553, 285);
			this.tabCtrlPanel_Main6Ind.TabIndex = 1;
			this.tabCtrlPanel_Main6Ind.TabItem = this.rptChart_TabItem_SixInds;
			this.rptChart_TabItem_SixInds.AttachedControl = this.tabCtrlPanel_Main6Ind;
			this.rptChart_TabItem_SixInds.GlobalItem = false;
			this.rptChart_TabItem_SixInds.Name = "rptChart_TabItem_SixInds";
			this.rptChart_TabItem_SixInds.Text = "六大指标";
			this.tabCtrlPanel_CashFlow.Dock = DockStyle.Fill;
			this.tabCtrlPanel_CashFlow.Location = new Point(109, 0);
			this.tabCtrlPanel_CashFlow.Name = "tabCtrlPanel_CashFlow";
			this.tabCtrlPanel_CashFlow.Size = new Size(553, 285);
			this.tabCtrlPanel_CashFlow.TabIndex = 0;
			this.tabCtrlPanel_CashFlow.TabItem = this.rptChart_TabItem_CashFlow;
			this.rptChart_TabItem_CashFlow.AttachedControl = this.tabCtrlPanel_CashFlow;
			this.rptChart_TabItem_CashFlow.GlobalItem = false;
			this.rptChart_TabItem_CashFlow.Name = "rptChart_TabItem_CashFlow";
			this.rptChart_TabItem_CashFlow.Text = "现金流量表";
			this.tabCtrlPanel_Income.Dock = DockStyle.Fill;
			this.tabCtrlPanel_Income.Location = new Point(109, 0);
			this.tabCtrlPanel_Income.Name = "tabCtrlPanel_Income";
			this.tabCtrlPanel_Income.Size = new Size(553, 285);
			this.tabCtrlPanel_Income.TabIndex = 0;
			this.tabCtrlPanel_Income.TabItem = this.rptChart_TabItem_Income;
			this.rptChart_TabItem_Income.AttachedControl = this.tabCtrlPanel_Income;
			this.rptChart_TabItem_Income.GlobalItem = false;
			this.rptChart_TabItem_Income.Name = "rptChart_TabItem_Income";
			this.rptChart_TabItem_Income.Text = "损益表";
			this.tabCtrlPanel_BalSheet.Dock = DockStyle.Fill;
			this.tabCtrlPanel_BalSheet.Location = new Point(109, 0);
			this.tabCtrlPanel_BalSheet.Name = "tabCtrlPanel_BalSheet";
			this.tabCtrlPanel_BalSheet.Size = new Size(553, 285);
			this.tabCtrlPanel_BalSheet.TabIndex = 0;
			this.tabCtrlPanel_BalSheet.TabItem = this.rptChart_TabItem_BalSheet;
			this.rptChart_TabItem_BalSheet.AttachedControl = this.tabCtrlPanel_BalSheet;
			this.rptChart_TabItem_BalSheet.GlobalItem = false;
			this.rptChart_TabItem_BalSheet.Name = "rptChart_TabItem_BalSheet";
			this.rptChart_TabItem_BalSheet.Text = "资产负债表";
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.tablePanel_bg);
			base.Name = "FnRptAnlysPanel";
			base.Size = new Size(1324, 316);
			this.tablePanel_bg.ResumeLayout(false);
			this.tablePanel_Header.ResumeLayout(false);
			this.tablePanel_Header.PerformLayout();
			this.tablePanel_Bottom.ResumeLayout(false);
			((ISupportInitialize)this.tabControl_FnRpts).EndInit();
			this.tabControl_FnRpts.ResumeLayout(false);
			((ISupportInitialize)this.superTabCtrl_RptAnlys).EndInit();
			this.superTabCtrl_RptAnlys.ResumeLayout(false);
			base.ResumeLayout(false);
		}

		// Token: 0x04000B22 RID: 2850
		private Class557 class557_0;

		// Token: 0x04000B23 RID: 2851
		private Class285 class285_0;

		// Token: 0x04000B24 RID: 2852
		private Class285 class285_1;

		// Token: 0x04000B25 RID: 2853
		private System.Windows.Forms.ToolTip toolTip_0 = new System.Windows.Forms.ToolTip();

		// Token: 0x04000B26 RID: 2854
		private const int int_0 = 10;

		// Token: 0x04000B27 RID: 2855
		private List<Class15> list_0;

		// Token: 0x04000B28 RID: 2856
		private Dictionary<string, KeyValuePair<string, string>> dictionary_0;

		// Token: 0x04000B29 RID: 2857
		private IContainer icontainer_0;

		// Token: 0x04000B2A RID: 2858
		private TableLayoutPanel tablePanel_bg;

		// Token: 0x04000B2B RID: 2859
		private TableLayoutPanel tablePanel_Header;

		// Token: 0x04000B2C RID: 2860
		private System.Windows.Forms.Label label_CurrSymb;

		// Token: 0x04000B2D RID: 2861
		private System.Windows.Forms.Label label_CurrDate;

		// Token: 0x04000B2E RID: 2862
		private Button btn_ChangeDate;

		// Token: 0x04000B2F RID: 2863
		private SuperTabControl superTabCtrl_RptAnlys;

		// Token: 0x04000B30 RID: 2864
		private SuperTabControlPanel tabCtrlPanel_Main6Ind;

		// Token: 0x04000B31 RID: 2865
		private SuperTabControlPanel tabCtrlPanel_CashFlow;

		// Token: 0x04000B32 RID: 2866
		private SuperTabItem rptChart_TabItem_CashFlow;

		// Token: 0x04000B33 RID: 2867
		private SuperTabControlPanel tabCtrlPanel_Income;

		// Token: 0x04000B34 RID: 2868
		private SuperTabItem rptChart_TabItem_Income;

		// Token: 0x04000B35 RID: 2869
		private SuperTabControlPanel tabCtrlPanel_BalSheet;

		// Token: 0x04000B36 RID: 2870
		private SuperTabItem rptChart_TabItem_BalSheet;

		// Token: 0x04000B37 RID: 2871
		private DevComponents.DotNetBar.TabControl tabControl_FnRpts;

		// Token: 0x04000B38 RID: 2872
		private TabControlPanel tabCtrlPanel_FnData_byQt;

		// Token: 0x04000B39 RID: 2873
		private TabItem tabItem_FnRpt_byQt;

		// Token: 0x04000B3A RID: 2874
		private TabControlPanel tabCtrlPanel_FnData_byYr;

		// Token: 0x04000B3B RID: 2875
		private TabItem tabItem_FnRpt_byYr;

		// Token: 0x04000B3C RID: 2876
		private TableLayoutPanel tablePanel_Bottom;

		// Token: 0x04000B3D RID: 2877
		private System.Windows.Forms.Label label_当前日期;

		// Token: 0x04000B3E RID: 2878
		private SuperTabItem rptChart_TabItem_SixInds;

		// Token: 0x0200021A RID: 538
		[CompilerGenerated]
		private sealed class Class295
		{
			// Token: 0x06001642 RID: 5698 RVA: 0x00095AE4 File Offset: 0x00093CE4
			internal bool method_0(Class15 class15_0)
			{
				bool result;
				if (class15_0.ReportAnlysType == this.enum2_0 && class15_0.ts_code == this.string_0)
				{
					result = (class15_0.end_date == this.string_1);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000B3F RID: 2879
			public Enum2 enum2_0;

			// Token: 0x04000B40 RID: 2880
			public string string_0;

			// Token: 0x04000B41 RID: 2881
			public string string_1;
		}
	}
}
