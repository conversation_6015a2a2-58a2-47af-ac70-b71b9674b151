﻿using System;

namespace ns18
{
	// Token: 0x02000040 RID: 64
	internal sealed class Class22
	{
		// Token: 0x17000085 RID: 133
		// (get) Token: 0x060001E7 RID: 487 RVA: 0x00018588 File Offset: 0x00016788
		public static string AppLoggingIn
		{
			get
			{
				return "App Logging In";
			}
		}

		// Token: 0x17000086 RID: 134
		// (get) Token: 0x060001E8 RID: 488 RVA: 0x000185A0 File Offset: 0x000167A0
		public static string AppLoggedIn
		{
			get
			{
				return "App Logged In";
			}
		}

		// Token: 0x17000087 RID: 135
		// (get) Token: 0x060001E9 RID: 489 RVA: 0x000185B8 File Offset: 0x000167B8
		public static string AppLoading
		{
			get
			{
				return "App Loading...";
			}
		}

		// Token: 0x17000088 RID: 136
		// (get) Token: 0x060001EA RID: 490 RVA: 0x000185D0 File Offset: 0x000167D0
		public static string AppStarted
		{
			get
			{
				return "App Started";
			}
		}

		// Token: 0x17000089 RID: 137
		// (get) Token: 0x060001EB RID: 491 RVA: 0x000185E8 File Offset: 0x000167E8
		public static string AppExited
		{
			get
			{
				return "App Exited";
			}
		}

		// Token: 0x1700008A RID: 138
		// (get) Token: 0x060001EC RID: 492 RVA: 0x00018600 File Offset: 0x00016800
		public static string AutoPlayStarted
		{
			get
			{
				return "AutoPlay Started";
			}
		}

		// Token: 0x1700008B RID: 139
		// (get) Token: 0x060001ED RID: 493 RVA: 0x00018618 File Offset: 0x00016818
		public static string AutoPlayStopped
		{
			get
			{
				return "AutoPlay Stopped";
			}
		}

		// Token: 0x1700008C RID: 140
		// (get) Token: 0x060001EE RID: 494 RVA: 0x00018630 File Offset: 0x00016830
		public static string PeriodResetting
		{
			get
			{
				return "Period Resetting";
			}
		}

		// Token: 0x1700008D RID: 141
		// (get) Token: 0x060001EF RID: 495 RVA: 0x00018648 File Offset: 0x00016848
		public static string PeriodReset
		{
			get
			{
				return "Period Reset";
			}
		}

		// Token: 0x1700008E RID: 142
		// (get) Token: 0x060001F0 RID: 496 RVA: 0x00018660 File Offset: 0x00016860
		public static string OrderPlaced
		{
			get
			{
				return "Order Placed";
			}
		}

		// Token: 0x1700008F RID: 143
		// (get) Token: 0x060001F1 RID: 497 RVA: 0x00018678 File Offset: 0x00016878
		public static string PageChanging
		{
			get
			{
				return "Page Changing";
			}
		}

		// Token: 0x17000090 RID: 144
		// (get) Token: 0x060001F2 RID: 498 RVA: 0x00018690 File Offset: 0x00016890
		public static string PageChanged
		{
			get
			{
				return "Page Changed";
			}
		}

		// Token: 0x17000091 RID: 145
		// (get) Token: 0x060001F3 RID: 499 RVA: 0x000186A8 File Offset: 0x000168A8
		public static string SymblChanging
		{
			get
			{
				return "Symbl Changing";
			}
		}

		// Token: 0x17000092 RID: 146
		// (get) Token: 0x060001F4 RID: 500 RVA: 0x000186C0 File Offset: 0x000168C0
		public static string SymblChanged
		{
			get
			{
				return "Symbl Changed";
			}
		}

		// Token: 0x17000093 RID: 147
		// (get) Token: 0x060001F5 RID: 501 RVA: 0x000186D8 File Offset: 0x000168D8
		public static string AcctChanging
		{
			get
			{
				return "Acct Changing";
			}
		}

		// Token: 0x17000094 RID: 148
		// (get) Token: 0x060001F6 RID: 502 RVA: 0x000186F0 File Offset: 0x000168F0
		public static string AcctChanged
		{
			get
			{
				return "Acct Changed";
			}
		}

		// Token: 0x17000095 RID: 149
		// (get) Token: 0x060001F7 RID: 503 RVA: 0x00018708 File Offset: 0x00016908
		public static string BlindTestEntering
		{
			get
			{
				return "BlindTest Entering";
			}
		}

		// Token: 0x17000096 RID: 150
		// (get) Token: 0x060001F8 RID: 504 RVA: 0x00018720 File Offset: 0x00016920
		public static string BlindTestEntered
		{
			get
			{
				return "BlindTest Entered";
			}
		}

		// Token: 0x17000097 RID: 151
		// (get) Token: 0x060001F9 RID: 505 RVA: 0x00018738 File Offset: 0x00016938
		public static string BlindTestExiting
		{
			get
			{
				return "BlindTest Exiting";
			}
		}

		// Token: 0x17000098 RID: 152
		// (get) Token: 0x060001FA RID: 506 RVA: 0x00018750 File Offset: 0x00016950
		public static string BlindTestExited
		{
			get
			{
				return "BlindTest Exited";
			}
		}

		// Token: 0x17000099 RID: 153
		// (get) Token: 0x060001FB RID: 507 RVA: 0x00018768 File Offset: 0x00016968
		public static string SpanMoveNext
		{
			get
			{
				return "Span Move Next";
			}
		}

		// Token: 0x1700009A RID: 154
		// (get) Token: 0x060001FC RID: 508 RVA: 0x00018780 File Offset: 0x00016980
		public static string SpanMovePrev
		{
			get
			{
				return "Span Move Prev";
			}
		}

		// Token: 0x1700009B RID: 155
		// (get) Token: 0x060001FD RID: 509 RVA: 0x00018798 File Offset: 0x00016998
		public static string ExceptionOccurred
		{
			get
			{
				return "Exception Occurred";
			}
		}

		// Token: 0x1700009C RID: 156
		// (get) Token: 0x060001FE RID: 510 RVA: 0x000187B0 File Offset: 0x000169B0
		public static string ErrorOccurred
		{
			get
			{
				return "Error Occurred";
			}
		}

		// Token: 0x1700009D RID: 157
		// (get) Token: 0x060001FF RID: 511 RVA: 0x000187C8 File Offset: 0x000169C8
		public static string AppBackgroudUpdating
		{
			get
			{
				return "App Backgroud Updating";
			}
		}

		// Token: 0x1700009E RID: 158
		// (get) Token: 0x06000200 RID: 512 RVA: 0x000187E0 File Offset: 0x000169E0
		public static string AppBackgroudUpdated
		{
			get
			{
				return "App Backgroud Updated";
			}
		}
	}
}
