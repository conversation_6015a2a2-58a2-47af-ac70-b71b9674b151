﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns28;
using ns6;
using TEx.Comn;

namespace TEx
{
	// Token: 0x0200008D RID: 141
	public sealed partial class PageSelWnd : Form0
	{
		// Token: 0x1400001C RID: 28
		// (add) Token: 0x060004A1 RID: 1185 RVA: 0x00024C3C File Offset: 0x00022E3C
		// (remove) Token: 0x060004A2 RID: 1186 RVA: 0x00024C74 File Offset: 0x00022E74
		public event MsgEventHandler PageSelected
		{
			[CompilerGenerated]
			add
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Combine(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Remove(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
		}

		// Token: 0x060004A3 RID: 1187 RVA: 0x00003FF9 File Offset: 0x000021F9
		protected void method_1(string string_0)
		{
			MsgEventHandler msgEventHandler = this.msgEventHandler_0;
			if (msgEventHandler != null)
			{
				msgEventHandler(this, new MsgEventArgs(string_0, null));
			}
		}

		// Token: 0x060004A4 RID: 1188 RVA: 0x00024CAC File Offset: 0x00022EAC
		public PageSelWnd()
		{
			this.InitializeComponent();
			base.Icon = Class372.TExIcoBlue;
			this.BackColor = Color.White;
			base.ShowIcon = false;
			this.dictionary_0 = new Dictionary<string, string>();
			this.dictionary_0.Add("行情", "传统风格的行情页面，显示包含交易所品种列表的功能栏，可以切换到分时图和K线图");
			this.dictionary_0.Add("分时", "分时页面，显示经典分时图，可以随时切换到K线图或功能栏");
			this.dictionary_0.Add("简单", "简单页面，显示极简风格单周期K线图，除成交量外无其他指标");
			this.dictionary_0.Add("二图", "二图页面，两个不同周期K线图并列，页面可同时显示包含品种列表的功能栏");
			this.dictionary_0.Add("三图", "三图页面，包含分时图及不同周期的两个K线图，以及包含品种列表的功能栏");
			this.dictionary_0.Add("四图", "四图页面，包含分时图及不同周期的三个K线图，页面可同时显示包含品种列表的功能栏");
			this.pictureBox1.IsSelected = true;
			this.labelX_desc.Text = this.dictionary_0[this.pictureBox1.Tag as string];
			this.linkLabel_Done.LinkColor = Class179.color_22;
			foreach (ScrShotPictureBox scrShotPictureBox in this.PictureBoxList)
			{
				scrShotPictureBox.Selected += this.method_4;
				scrShotPictureBox.MouseEnter += this.method_2;
				scrShotPictureBox.MouseLeave += this.method_3;
			}
			this.linkLabel_Done.LinkClicked += this.linkLabel_Done_LinkClicked;
		}

		// Token: 0x060004A5 RID: 1189 RVA: 0x00024E3C File Offset: 0x0002303C
		private void method_2(object sender, EventArgs e)
		{
			ScrShotPictureBox scrShotPictureBox = sender as ScrShotPictureBox;
			this.labelX_desc.Text = this.dictionary_0[scrShotPictureBox.Tag as string];
		}

		// Token: 0x060004A6 RID: 1190 RVA: 0x00024E74 File Offset: 0x00023074
		private void method_3(object sender, EventArgs e)
		{
			ScrShotPictureBox scrShotPictureBox = this.PictureBoxList.SingleOrDefault(new Func<ScrShotPictureBox, bool>(PageSelWnd.<>c.<>9.method_0));
			this.labelX_desc.Text = this.dictionary_0[scrShotPictureBox.Tag as string];
		}

		// Token: 0x060004A7 RID: 1191 RVA: 0x00024ED0 File Offset: 0x000230D0
		private void linkLabel_Done_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			ScrShotPictureBox scrShotPictureBox = this.PictureBoxList.SingleOrDefault(new Func<ScrShotPictureBox, bool>(PageSelWnd.<>c.<>9.method_1));
			this.method_1(scrShotPictureBox.Tag as string);
			base.Close();
		}

		// Token: 0x060004A8 RID: 1192 RVA: 0x00024F24 File Offset: 0x00023124
		private void method_4(object sender, EventArgs e)
		{
			ScrShotPictureBox scrShotPictureBox = sender as ScrShotPictureBox;
			foreach (ScrShotPictureBox scrShotPictureBox2 in this.PictureBoxList)
			{
				if (scrShotPictureBox2 != scrShotPictureBox)
				{
					scrShotPictureBox2.IsSelected = false;
				}
			}
		}

		// Token: 0x170000F6 RID: 246
		// (get) Token: 0x060004A9 RID: 1193 RVA: 0x00024F88 File Offset: 0x00023188
		public List<ScrShotPictureBox> PictureBoxList
		{
			get
			{
				if (this.list_0 == null)
				{
					List<ScrShotPictureBox> list = new List<ScrShotPictureBox>();
					foreach (object obj in base.Controls)
					{
						if (obj is ScrShotPictureBox)
						{
							list.Add(obj as ScrShotPictureBox);
						}
					}
					this.list_0 = list;
				}
				return this.list_0;
			}
		}

		// Token: 0x060004AA RID: 1194 RVA: 0x00004016 File Offset: 0x00002216
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x040001D3 RID: 467
		[CompilerGenerated]
		private MsgEventHandler msgEventHandler_0;

		// Token: 0x040001D4 RID: 468
		private Dictionary<string, string> dictionary_0;

		// Token: 0x040001D5 RID: 469
		private List<ScrShotPictureBox> list_0;

		// Token: 0x040001D6 RID: 470
		private IContainer icontainer_0;
	}
}
