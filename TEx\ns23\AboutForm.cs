﻿using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Windows.Forms;
using ns28;
using TEx;

namespace ns23
{
	// Token: 0x02000199 RID: 409
	internal sealed partial class AboutForm : Form0
	{
		// Token: 0x06000FBE RID: 4030 RVA: 0x00062418 File Offset: 0x00060618
		public AboutForm()
		{
			if (!TApp.IsHighDpiScreen)
			{
				base.AutoScaleMode = AutoScaleMode.Dpi;
			}
			this.InitializeComponent();
			this.BackColor = Color.White;
			this.pictureBox_Banner.Image = Class372.aboutBanner;
			this.label_Ver.Text = Base.UI.smethod_114() + "  v" + TApp.Ver;
			for (int i = 0; i < 2; i++)
			{
				if (this.label_Ver.Text.EndsWith(".0"))
				{
					this.label_Ver.Text = this.label_Ver.Text.Substring(0, this.label_Ver.Text.Length - 2);
				}
			}
			this.label_CR.Text = "© " + DateTime.Now.Year + " TEx Studio";
			this.label_otherInfo.Text = "";
			this.label_SysInfo.Text = TApp.OS;
			this.pictureBox_Banner.DoubleClick += this.pictureBox_Banner_DoubleClick;
			this.linkLabel_htmlHelp.LinkClicked += this.linkLabel_htmlHelp_LinkClicked;
			this.linkLabel_videoHelp.LinkClicked += this.linkLabel_videoHelp_LinkClicked;
			this.label_Weixin.Location = new Point(this.pictureBox_QrCode.Location.X + (this.pictureBox_QrCode.Width - this.label_Weixin.Width) / 2, this.pictureBox_QrCode.Location.Y + this.pictureBox_QrCode.Height + 2);
		}

		// Token: 0x06000FBF RID: 4031 RVA: 0x00004268 File Offset: 0x00002468
		private void button_OK_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x06000FC0 RID: 4032 RVA: 0x000625BC File Offset: 0x000607BC
		private void linkLabel_TExWebLink_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			LinkLabel linkLabel_ = sender as LinkLabel;
			this.method_1(linkLabel_);
		}

		// Token: 0x06000FC1 RID: 4033 RVA: 0x000625BC File Offset: 0x000607BC
		private void linkLabel_htmlHelp_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			LinkLabel linkLabel_ = sender as LinkLabel;
			this.method_1(linkLabel_);
		}

		// Token: 0x06000FC2 RID: 4034 RVA: 0x000625BC File Offset: 0x000607BC
		private void linkLabel_videoHelp_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			LinkLabel linkLabel_ = sender as LinkLabel;
			this.method_1(linkLabel_);
		}

		// Token: 0x06000FC3 RID: 4035 RVA: 0x000625DC File Offset: 0x000607DC
		private void method_1(LinkLabel linkLabel_0)
		{
			try
			{
				Process.Start(new ProcessStartInfo("https://" + linkLabel_0.Text));
			}
			catch
			{
			}
		}

		// Token: 0x06000FC4 RID: 4036 RVA: 0x00006C01 File Offset: 0x00004E01
		private void pictureBox_Banner_DoubleClick(object sender, EventArgs e)
		{
			this.label_otherInfo.Text = "HOST: " + TApp.HOST;
			MessageBox.Show(TApp.smethod_9(), "信息", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
		}

		// Token: 0x06000FC5 RID: 4037 RVA: 0x00006C32 File Offset: 0x00004E32
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x040007C7 RID: 1991
		private IContainer icontainer_0;
	}
}
