﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns26;
using ns32;
using TEx;
using TEx.Trading;
using TEx.Util;

namespace ns28
{
	// Token: 0x0200016C RID: 364
	internal sealed partial class SetCondOrdForm : Form
	{
		// Token: 0x06000DD2 RID: 3538 RVA: 0x00057494 File Offset: 0x00055694
		public SetCondOrdForm(CondOrder condOrder_1) : this(condOrder_1.TransID, condOrder_1.StkSymbol, condOrder_1.OrderType, new int?((int)condOrder_1.ComparisonOpt), condOrder_1.CondPrice, condOrder_1.ExePrice, condOrder_1.Units, condOrder_1.TrailingStopPts, null)
		{
			this.condOrder_0 = Base.Trading.smethod_114(condOrder_1.ID);
			this.Text = "修改条件单";
		}

		// Token: 0x06000DD3 RID: 3539 RVA: 0x000574FC File Offset: 0x000556FC
		public SetCondOrdForm(int? nullable_0, StkSymbol stkSymbol_1, OrderType orderType_0, int? nullable_1, decimal decimal_0, decimal decimal_1, decimal decimal_2, decimal? nullable_2, List<Transaction> list_0)
		{
			this.InitializeComponent();
			this.StkSymbol = stkSymbol_1;
			this.method_9(this.numericUpDown_Units);
			decimal decimal_3 = Convert.ToDecimal(1.0 / Math.Pow(10.0, (double)this.stkSymbol_0.DigitNb));
			this.method_8(this.numericUpDown_CondPrice, decimal_3);
			this.method_8(this.numericUpDown_ExPrice, 0m);
			this.method_3(nullable_1);
			this.method_4(orderType_0);
			this.CondPrice = decimal_0;
			this.ExPrice = decimal_1;
			this.Units = decimal_2;
			this.button_OK.Click += this.button_OK_Click;
			this.button_Cancel.Click += this.button_Cancel_Click;
			this.comboBox_LngOrSht.SelectedIndexChanged += this.comboBox_LngOrSht_SelectedIndexChanged;
			this.comboBox_OpenOrClose.SelectedIndexChanged += this.comboBox_OpenOrClose_SelectedIndexChanged;
			this.numericUpDown_ExPrice.BeforeValueDecrement += this.method_1;
			this.numericUpDown_ExPrice.BeforeValueIncrement += this.method_0;
			this.numericUpDown_ExPrice.ValueChanged += this.numericUpDown_ExPrice_ValueChanged;
			if (decimal_1 == 0m)
			{
				this.numericUpDown_ExPrice.DecimalPlaces = 0;
			}
			if (!stkSymbol_1.IsFutures)
			{
				this.label_unit单位.Text = "股";
			}
			decimal num = (stkSymbol_1.LeastPriceVar != null) ? stkSymbol_1.LeastPriceVar.Value : 1m;
			this.numericUpDown_TrailingStopPts.Minimum = num;
			this.numericUpDown_TrailingStopPts.Maximum = 100000m;
			this.numericUpDown_TrailingStopPts.Increment = num;
			this.numericUpDown_TrailingStopPts.DecimalPlaces = stkSymbol_1.DigitNb;
			if (nullable_0 != null && nullable_2 == null)
			{
				this.groupBox_TrailingStop.Enabled = false;
			}
			else if (nullable_0 != null && nullable_2 != null)
			{
				ShownOpenTrans shownOpenTrans = Base.Trading.smethod_145(nullable_0.Value);
				if (shownOpenTrans != null)
				{
					ComboBoxItem item = this.method_5(shownOpenTrans);
					this.comboBox_currTrans.Items.Add(item);
					this.comboBox_currTrans.SelectedIndex = 0;
					this.numericUpDown_TrailingStopPts.Value = nullable_2.Value;
					this.chkBox_TrailingStop.Enabled = true;
					this.chkBox_TrailingStop.Checked = true;
				}
				this.groupBox_Cond.Enabled = false;
			}
			else if (list_0 != null && list_0.Any<Transaction>())
			{
				foreach (Transaction transaction_ in list_0)
				{
					ComboBoxItem item2 = this.method_5(transaction_);
					this.comboBox_currTrans.Items.Add(item2);
				}
				this.comboBox_currTrans.SelectedIndex = 0;
				decimal value = num * 100m;
				this.numericUpDown_TrailingStopPts.Value = value;
			}
			else
			{
				this.groupBox_TrailingStop.Enabled = false;
			}
			if (this.chkBox_TrailingStop.Enabled)
			{
				this.chkBox_TrailingStop.CheckedChanged += this.chkBox_TrailingStop_CheckedChanged;
				if (!this.chkBox_TrailingStop.Checked)
				{
					this.method_7(false);
				}
			}
		}

		// Token: 0x06000DD4 RID: 3540 RVA: 0x00057850 File Offset: 0x00055A50
		private void method_0(object sender, CancelEventArgs e)
		{
			if (this.numericUpDown_ExPrice.Value == 0m)
			{
				StkSymbol stkSymbol = this.method_2();
				SymbDataSet symbDataSet = Base.Data.smethod_50(stkSymbol.ID, false, false);
				if (symbDataSet != null)
				{
					decimal d = Convert.ToDecimal(symbDataSet.LastHisData.Close);
					this.numericUpDown_ExPrice.Value = d + this.numericUpDown_ExPrice.Increment;
				}
				this.numericUpDown_ExPrice.DecimalPlaces = stkSymbol.DigitNb;
			}
		}

		// Token: 0x06000DD5 RID: 3541 RVA: 0x000578CC File Offset: 0x00055ACC
		private void method_1(object sender, CancelEventArgs e)
		{
			if (this.numericUpDown_ExPrice.Value == 0m)
			{
				StkSymbol stkSymbol = this.method_2();
				if (stkSymbol != null)
				{
					this.numericUpDown_ExPrice.DecimalPlaces = stkSymbol.DigitNb;
				}
				SymbDataSet symbDataSet = Base.Data.smethod_50(stkSymbol.ID, false, false);
				if (symbDataSet != null && symbDataSet.LastHisData != null)
				{
					decimal d = Convert.ToDecimal(symbDataSet.LastHisData.Close);
					this.numericUpDown_ExPrice.Value = d - this.numericUpDown_ExPrice.Increment;
				}
			}
		}

		// Token: 0x06000DD6 RID: 3542 RVA: 0x00057954 File Offset: 0x00055B54
		private void numericUpDown_ExPrice_ValueChanged(object sender, EventArgs e)
		{
			if (this.numericUpDown_ExPrice.Value == 0m)
			{
				this.numericUpDown_ExPrice.DecimalPlaces = 0;
			}
			else
			{
				StkSymbol stkSymbol = this.method_2();
				if (stkSymbol != null && this.numericUpDown_ExPrice.DecimalPlaces != stkSymbol.DigitNb)
				{
					this.numericUpDown_ExPrice.DecimalPlaces = stkSymbol.DigitNb;
				}
			}
		}

		// Token: 0x06000DD7 RID: 3543 RVA: 0x000579B8 File Offset: 0x00055BB8
		private StkSymbol method_2()
		{
			int int_;
			if (this.CondOrder != null)
			{
				int_ = this.CondOrder.SymbID;
			}
			else
			{
				int_ = this.StkSymbol.ID;
			}
			return SymbMgr.smethod_3(int_);
		}

		// Token: 0x06000DD8 RID: 3544 RVA: 0x00006263 File Offset: 0x00004463
		private void method_3(int? nullable_0)
		{
			this.comboBox_CondOpt.SelectedIndex = ((nullable_0 != null) ? nullable_0.Value : 0);
		}

		// Token: 0x06000DD9 RID: 3545 RVA: 0x000579F4 File Offset: 0x00055BF4
		private void method_4(OrderType orderType_0)
		{
			this.IsLong = (orderType_0 == OrderType.Order_OpenLong || orderType_0 == OrderType.Order_CloseShort);
			if (orderType_0 != OrderType.Order_OpenLong)
			{
				if (orderType_0 != OrderType.Order_OpenShort)
				{
					if (orderType_0 != OrderType.Order_CloseLong)
					{
						if (orderType_0 != OrderType.Order_CloseShort)
						{
							this.comboBox_OpenOrClose.SelectedIndex = 2;
							return;
						}
					}
					this.comboBox_OpenOrClose.SelectedIndex = 1;
					return;
				}
			}
			this.comboBox_OpenOrClose.SelectedIndex = 0;
		}

		// Token: 0x06000DDA RID: 3546 RVA: 0x00057A50 File Offset: 0x00055C50
		private ComboBoxItem method_5(Transaction transaction_0)
		{
			string text = this.method_6(transaction_0);
			return new ComboBoxItem
			{
				Text = text,
				Value = transaction_0
			};
		}

		// Token: 0x06000DDB RID: 3547 RVA: 0x00057A7C File Offset: 0x00055C7C
		private string method_6(Transaction transaction_0)
		{
			StkSymbol stkSymbol = SymbMgr.smethod_3(transaction_0.SymbolID);
			return string.Concat(new string[]
			{
				Base.Trading.smethod_149((Enum17)transaction_0.TransType).Replace("开", "单"),
				"/",
				transaction_0.OpenUnits.ToString(),
				stkSymbol.IsFutures ? "手" : "股",
				"/",
				(transaction_0.Price / 1.0000000000000000000m).ToString()
			});
		}

		// Token: 0x06000DDC RID: 3548 RVA: 0x00006285 File Offset: 0x00004485
		private void method_7(bool bool_0)
		{
			this.label_TrailingStop.Enabled = bool_0;
			this.numericUpDown_TrailingStopPts.Enabled = bool_0;
			this.comboBox_currTrans.Enabled = bool_0;
			this.label_trans.Enabled = bool_0;
		}

		// Token: 0x06000DDD RID: 3549 RVA: 0x00057B28 File Offset: 0x00055D28
		private void chkBox_TrailingStop_CheckedChanged(object sender, EventArgs e)
		{
			if (this.chkBox_TrailingStop.Checked)
			{
				this.groupBox_Cond.Enabled = false;
				this.method_7(true);
			}
			else
			{
				this.groupBox_Cond.Enabled = true;
				this.method_7(false);
				if (this.CondOrder != null)
				{
					this.method_3(new int?((int)this.CondOrder.ComparisonOpt));
					this.method_4(this.CondOrder.OrderType);
					this.CondPrice = this.CondOrder.CondPrice;
					this.ExPrice = this.CondPrice;
					this.Units = this.CondOrder.Units;
				}
			}
		}

		// Token: 0x06000DDE RID: 3550 RVA: 0x00056D28 File Offset: 0x00054F28
		private void SetCondOrdForm_Load(object sender, EventArgs e)
		{
			if (base.Owner != null)
			{
				base.Location = new Point(base.Owner.Location.X + base.Owner.Width / 2 - base.Width / 2, base.Owner.Location.Y + base.Owner.Height / 2 - base.Height / 2);
			}
		}

		// Token: 0x06000DDF RID: 3551 RVA: 0x00057BCC File Offset: 0x00055DCC
		private void method_8(NumericUpDown numericUpDown_0, decimal decimal_0)
		{
			numericUpDown_0.Maximum = 9999999m;
			numericUpDown_0.Minimum = decimal_0;
			numericUpDown_0.DecimalPlaces = this.stkSymbol_0.DigitNb;
			numericUpDown_0.Increment = this.stkSymbol_0.LeastPriceVar.Value;
		}

		// Token: 0x06000DE0 RID: 3552 RVA: 0x0000620D File Offset: 0x0000440D
		private void method_9(NumericUpDown numericUpDown_0)
		{
			numericUpDown_0.Increment = 1m;
			numericUpDown_0.Maximum = 9999999m;
			numericUpDown_0.Minimum = 1m;
		}

		// Token: 0x06000DE1 RID: 3553 RVA: 0x000062B9 File Offset: 0x000044B9
		private void comboBox_LngOrSht_SelectedIndexChanged(object sender, EventArgs e)
		{
			if (this.comboBox_LngOrSht.SelectedIndex == 0)
			{
				this.IsLong = true;
			}
			else
			{
				this.IsLong = false;
			}
		}

		// Token: 0x06000DE2 RID: 3554 RVA: 0x000041AE File Offset: 0x000023AE
		private void comboBox_OpenOrClose_SelectedIndexChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x06000DE3 RID: 3555 RVA: 0x00057C1C File Offset: 0x00055E1C
		private void button_OK_Click(object sender, EventArgs e)
		{
			if (this.chkBox_TrailingStop.Enabled && this.chkBox_TrailingStop.Checked)
			{
				if (this.condOrder_0 == null)
				{
					Transaction transaction = (this.comboBox_currTrans.SelectedItem as ComboBoxItem).Value as Transaction;
					decimal value = this.numericUpDown_TrailingStopPts.Value;
					long value2 = (transaction.OpenUnits != null) ? transaction.OpenUnits.Value : transaction.Units;
					Base.Trading.smethod_85(transaction, null, new decimal?(value), value2);
				}
				else
				{
					Base.Trading.smethod_93(this.condOrder_0.ID, null, null, new decimal?(this.numericUpDown_TrailingStopPts.Value), this.condOrder_0.Units, true);
				}
				base.Close();
			}
			else
			{
				SetCondOrdForm.Class203 @class = new SetCondOrdForm.Class203();
				@class.condOrder_0 = new CondOrder();
				@class.condOrder_0.AcctID = Base.Acct.CurrAccount.ID;
				@class.condOrder_0.SymbID = ((this.CondOrder == null) ? this.StkSymbol.ID : this.CondOrder.SymbID);
				@class.condOrder_0.CreateTime = DateTime.Now;
				@class.condOrder_0.ComparisonOpt = this.method_10();
				@class.condOrder_0.CondPrice = this.CondPrice;
				@class.condOrder_0.ExePrice = this.ExPrice;
				@class.condOrder_0.OrderType = this.method_11();
				@class.condOrder_0.Units = this.Units;
				@class.condOrder_0.OrderStatus = OrderStatus.Active;
				if (!Base.Data.smethod_124(SymbMgr.smethod_3(@class.condOrder_0.SymbID)) && (@class.condOrder_0.OrderType == OrderType.Order_OpenShort || @class.condOrder_0.OrderType == OrderType.Order_CloseLongRevOpen))
				{
					MessageBox.Show("当前系统设置该品种不允许卖出开仓（或平仓反手开空）。", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
				else
				{
					int selectedIndex = this.comboBox_OpenOrClose.SelectedIndex;
					if (selectedIndex > 0 && Base.Trading.CurrOpenTransList != null)
					{
						SetCondOrdForm.Class204 class2 = new SetCondOrdForm.Class204();
						class2.class203_0 = @class;
						class2.enum17_0 = Base.Trading.smethod_175(class2.class203_0.condOrder_0.OrderType);
						if (!Base.Trading.CurrOpenTransList.Where(new Func<ShownOpenTrans, bool>(class2.method_0)).Any<ShownOpenTrans>() && MessageBox.Show("无满足条件的持仓，仍按该条件设定平仓" + ((selectedIndex == 1) ? "" : "反手") + "条件单吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
						{
							return;
						}
					}
					bool flag = false;
					SymbDataSet symbDataSet = Base.Data.smethod_50(this.StkSymbol.ID, false, false);
					if (symbDataSet != null)
					{
						decimal d = Convert.ToDecimal(symbDataSet.CurrHisDataSet.CurrHisData.Close);
						if (@class.condOrder_0.SymbID == this.StkSymbol.ID && ((@class.condOrder_0.ComparisonOpt == ComparisonOpt.Less && @class.condOrder_0.CondPrice > d) || (@class.condOrder_0.ComparisonOpt == ComparisonOpt.LessOrEqual && @class.condOrder_0.CondPrice >= d) || (@class.condOrder_0.ComparisonOpt == ComparisonOpt.Bigger && @class.condOrder_0.CondPrice < d) || (@class.condOrder_0.ComparisonOpt == ComparisonOpt.BiggerOrEqual && @class.condOrder_0.CondPrice <= d)))
						{
							flag = true;
						}
					}
					if (flag)
					{
						if (MessageBox.Show("价格条件当前已满足，按所设条件发出委托单吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
						{
							if (this.CondOrder != null)
							{
								Base.Trading.smethod_94(this.CondOrder.ID, OrderStatus.Executed);
							}
							Base.Trading.smethod_199(@class.condOrder_0.SymbID, @class.condOrder_0.OrderType, (long)Convert.ToInt32(@class.condOrder_0.Units), new decimal?(@class.condOrder_0.ExePrice));
							base.Close();
						}
					}
					else
					{
						if (this.CondOrder != null)
						{
							Base.Trading.smethod_91(this.CondOrder.ID, @class.condOrder_0.ComparisonOpt, @class.condOrder_0.CondPrice, @class.condOrder_0.ExePrice, @class.condOrder_0.OrderType, @class.condOrder_0.Units, null);
						}
						else
						{
							Base.Trading.smethod_86(this.StkSymbol.ID, this.ComparisonOpt, this.CondPrice, this.ExPrice, this.OrderType, this.Units);
						}
						base.Close();
					}
				}
			}
		}

		// Token: 0x06000DE4 RID: 3556 RVA: 0x00004268 File Offset: 0x00002468
		public void button_Cancel_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x1700022B RID: 555
		// (get) Token: 0x06000DE5 RID: 3557 RVA: 0x000580C0 File Offset: 0x000562C0
		public ComparisonOpt ComparisonOpt
		{
			get
			{
				return this.method_10();
			}
		}

		// Token: 0x06000DE6 RID: 3558 RVA: 0x000580D8 File Offset: 0x000562D8
		private ComparisonOpt method_10()
		{
			int selectedIndex = this.comboBox_CondOpt.SelectedIndex;
			ComparisonOpt result;
			if (selectedIndex == 0)
			{
				result = ComparisonOpt.Bigger;
			}
			else if (selectedIndex == 1)
			{
				result = ComparisonOpt.BiggerOrEqual;
			}
			else if (selectedIndex == 2)
			{
				result = ComparisonOpt.Less;
			}
			else
			{
				result = ComparisonOpt.LessOrEqual;
			}
			return result;
		}

		// Token: 0x1700022C RID: 556
		// (get) Token: 0x06000DE7 RID: 3559 RVA: 0x00058110 File Offset: 0x00056310
		public OrderType OrderType
		{
			get
			{
				return this.method_11();
			}
		}

		// Token: 0x06000DE8 RID: 3560 RVA: 0x00058128 File Offset: 0x00056328
		private OrderType method_11()
		{
			int selectedIndex = this.comboBox_OpenOrClose.SelectedIndex;
			OrderType result;
			if (this.IsLong)
			{
				if (selectedIndex == 0)
				{
					result = OrderType.Order_OpenLong;
				}
				else if (selectedIndex == 1)
				{
					result = OrderType.Order_CloseShort;
				}
				else
				{
					result = OrderType.Order_CloseShortRevOpen;
				}
			}
			else if (selectedIndex == 0)
			{
				result = OrderType.Order_OpenShort;
			}
			else if (selectedIndex == 1)
			{
				result = OrderType.Order_CloseLong;
			}
			else
			{
				result = OrderType.Order_CloseLongRevOpen;
			}
			return result;
		}

		// Token: 0x1700022D RID: 557
		// (get) Token: 0x06000DE9 RID: 3561 RVA: 0x00058170 File Offset: 0x00056370
		// (set) Token: 0x06000DEA RID: 3562 RVA: 0x000062DA File Offset: 0x000044DA
		public bool IsLong
		{
			get
			{
				return this.comboBox_LngOrSht.SelectedIndex == 0;
			}
			set
			{
				if (value)
				{
					this.comboBox_LngOrSht.SelectedIndex = 0;
				}
				else
				{
					this.comboBox_LngOrSht.SelectedIndex = 1;
				}
			}
		}

		// Token: 0x1700022E RID: 558
		// (get) Token: 0x06000DEB RID: 3563 RVA: 0x00058190 File Offset: 0x00056390
		// (set) Token: 0x06000DEC RID: 3564 RVA: 0x000581A8 File Offset: 0x000563A8
		public StkSymbol StkSymbol
		{
			get
			{
				return this.stkSymbol_0;
			}
			set
			{
				this.stkSymbol_0 = value;
				if (Base.UI.Form.IsInBlindTestMode && !Base.UI.Form.IsSingleBlindTest)
				{
					this.label_Symb.Text = "●●";
				}
				else
				{
					this.label_Symb.Text = this.stkSymbol_0.CNName + "(" + this.stkSymbol_0.Code + ")";
				}
			}
		}

		// Token: 0x1700022F RID: 559
		// (get) Token: 0x06000DED RID: 3565 RVA: 0x00058218 File Offset: 0x00056418
		// (set) Token: 0x06000DEE RID: 3566 RVA: 0x000062FB File Offset: 0x000044FB
		public decimal CondPrice
		{
			get
			{
				decimal result;
				if (this.numericUpDown_CondPrice.Enabled)
				{
					result = this.numericUpDown_CondPrice.Value;
				}
				else
				{
					result = 0m;
				}
				return result;
			}
			set
			{
				if (value > 0m)
				{
					this.numericUpDown_CondPrice.Enabled = true;
					this.numericUpDown_CondPrice.Value = value;
				}
				else
				{
					this.numericUpDown_CondPrice.Enabled = false;
				}
			}
		}

		// Token: 0x17000230 RID: 560
		// (get) Token: 0x06000DEF RID: 3567 RVA: 0x0005824C File Offset: 0x0005644C
		// (set) Token: 0x06000DF0 RID: 3568 RVA: 0x00006332 File Offset: 0x00004532
		public decimal ExPrice
		{
			get
			{
				return this.numericUpDown_ExPrice.Value;
			}
			set
			{
				if (value >= 0m)
				{
					this.numericUpDown_ExPrice.Enabled = true;
					this.numericUpDown_ExPrice.Value = value;
				}
				else
				{
					this.numericUpDown_ExPrice.Enabled = false;
				}
			}
		}

		// Token: 0x17000231 RID: 561
		// (get) Token: 0x06000DF1 RID: 3569 RVA: 0x00058268 File Offset: 0x00056468
		// (set) Token: 0x06000DF2 RID: 3570 RVA: 0x00006369 File Offset: 0x00004569
		public decimal Units
		{
			get
			{
				return this.numericUpDown_Units.Value;
			}
			set
			{
				if (value > 0m)
				{
					this.numericUpDown_Units.Value = value;
				}
			}
		}

		// Token: 0x17000232 RID: 562
		// (get) Token: 0x06000DF3 RID: 3571 RVA: 0x00058284 File Offset: 0x00056484
		// (set) Token: 0x06000DF4 RID: 3572 RVA: 0x00006386 File Offset: 0x00004586
		public CondOrder CondOrder
		{
			get
			{
				return this.condOrder_0;
			}
			set
			{
				this.condOrder_0 = value;
			}
		}

		// Token: 0x06000DF5 RID: 3573 RVA: 0x00006391 File Offset: 0x00004591
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0400071D RID: 1821
		private StkSymbol stkSymbol_0;

		// Token: 0x0400071E RID: 1822
		private CondOrder condOrder_0;

		// Token: 0x0400071F RID: 1823
		private IContainer icontainer_0;

		// Token: 0x0200016D RID: 365
		[CompilerGenerated]
		private sealed class Class203
		{
			// Token: 0x04000738 RID: 1848
			public CondOrder condOrder_0;
		}

		// Token: 0x0200016E RID: 366
		[CompilerGenerated]
		private sealed class Class204
		{
			// Token: 0x06000DF9 RID: 3577 RVA: 0x00059044 File Offset: 0x00057244
			internal bool method_0(ShownOpenTrans shownOpenTrans_0)
			{
				bool result;
				if (shownOpenTrans_0.SymbolID == this.class203_0.condOrder_0.SymbID)
				{
					result = (shownOpenTrans_0.TransType == (int)this.enum17_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000739 RID: 1849
			public Enum17 enum17_0;

			// Token: 0x0400073A RID: 1850
			public SetCondOrdForm.Class203 class203_0;
		}
	}
}
