﻿using System;
using System.Drawing;
using System.Runtime.Serialization;
using ns28;

namespace TEx
{
	// Token: 0x020001DB RID: 475
	[Serializable]
	internal sealed class DrawRedArwLUp : DrawRedArwUp, ISerializable
	{
		// Token: 0x06001286 RID: 4742 RVA: 0x00006B34 File Offset: 0x00004D34
		public DrawRedArwLUp()
		{
		}

		// Token: 0x06001287 RID: 4743 RVA: 0x00007AFC File Offset: 0x00005CFC
		public DrawRedArwLUp(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = "左上箭头";
		}

		// Token: 0x06001288 RID: 4744 RVA: 0x00006B58 File Offset: 0x00004D58
		protected DrawRedArwLUp(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x06001289 RID: 4745 RVA: 0x00006B69 File Offset: 0x00004D69
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x0600128A RID: 4746 RVA: 0x0007F2C8 File Offset: 0x0007D4C8
		protected override Image vmethod_24()
		{
			return Class372.RedArrow_LUp;
		}
	}
}
