﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Runtime.Serialization;
using TEx.Chart;

namespace TEx
{
	// Token: 0x020001DD RID: 477
	[Serializable]
	internal sealed class DrawText : DrawObj, ISerializable
	{
		// Token: 0x06001290 RID: 4752 RVA: 0x000037AA File Offset: 0x000019AA
		public DrawText()
		{
		}

		// Token: 0x06001291 RID: 4753 RVA: 0x0007F2F8 File Offset: 0x0007D4F8
		public DrawText(ChartCS chart, double x1, double y1, string text, Color? color, object font) : base(chart, x1, y1, x1, y1)
		{
			base.Name = "文字";
			base.CanChgColor = true;
			base.IsOneClickLoc = true;
			this.Text = text;
			if (font != null)
			{
				this.Font = (font as Font);
			}
			base.LineColor = color;
			base.vmethod_17(chart, x1, y1, x1, y1);
		}

		// Token: 0x06001292 RID: 4754 RVA: 0x0007F358 File Offset: 0x0007D558
		protected DrawText(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
			this.string_5 = info.GetString("Text");
			if (this.method_39(info).Contains("Font"))
			{
				try
				{
					object value = info.GetValue("Font", typeof(Font));
					if (value != null)
					{
						this.font_0 = (Font)value;
					}
				}
				catch
				{
				}
			}
		}

		// Token: 0x06001293 RID: 4755 RVA: 0x0007F3D4 File Offset: 0x0007D5D4
		private List<string> method_39(SerializationInfo serializationInfo_0)
		{
			SerializationInfoEnumerator enumerator = serializationInfo_0.GetEnumerator();
			List<string> list = new List<string>();
			while (enumerator.MoveNext())
			{
				list.Add(enumerator.Name);
			}
			return list;
		}

		// Token: 0x06001294 RID: 4756 RVA: 0x00007B34 File Offset: 0x00005D34
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
			info.AddValue("Text", this.string_5);
			if (this.Font != null)
			{
				info.AddValue("Font", this.font_0);
			}
		}

		// Token: 0x06001295 RID: 4757 RVA: 0x0007F40C File Offset: 0x0007D60C
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_6)
		{
			List<GraphObj> list = new List<GraphObj>();
			TextObj item = this.method_40(chartCS_1, double_1, double_2, double_3, double_4, string_6);
			list.Add(item);
			return list;
		}

		// Token: 0x06001296 RID: 4758 RVA: 0x0007F43C File Offset: 0x0007D63C
		private TextObj method_40(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_6)
		{
			base.method_26();
			FontSpec fontSpec = null;
			if (this.Font != null)
			{
				fontSpec = new FontSpec();
				fontSpec.Size = this.Font.Size;
				fontSpec.Family = this.Font.FontFamily.Name;
				fontSpec.IsBold = this.Font.Bold;
				fontSpec.IsItalic = this.Font.Italic;
				fontSpec.IsUnderline = this.Font.Underline;
			}
			return base.method_27(chartCS_1, double_1, double_2, this.Text, fontSpec, string_6);
		}

		// Token: 0x06001297 RID: 4759 RVA: 0x000041AE File Offset: 0x000023AE
		protected override void vmethod_12(ChartCS chartCS_1)
		{
		}

		// Token: 0x06001298 RID: 4760 RVA: 0x0001FB74 File Offset: 0x0001DD74
		protected override DrawLineStyle vmethod_23()
		{
			return null;
		}

		// Token: 0x170002C7 RID: 711
		// (get) Token: 0x06001299 RID: 4761 RVA: 0x0007F4D0 File Offset: 0x0007D6D0
		// (set) Token: 0x0600129A RID: 4762 RVA: 0x00007B6A File Offset: 0x00005D6A
		public string Text
		{
			get
			{
				return this.string_5;
			}
			set
			{
				this.string_5 = value;
			}
		}

		// Token: 0x170002C8 RID: 712
		// (get) Token: 0x0600129B RID: 4763 RVA: 0x0007F4E8 File Offset: 0x0007D6E8
		// (set) Token: 0x0600129C RID: 4764 RVA: 0x00007B75 File Offset: 0x00005D75
		public Font Font
		{
			get
			{
				return this.font_0;
			}
			set
			{
				this.font_0 = value;
			}
		}

		// Token: 0x040009B1 RID: 2481
		private string string_5;

		// Token: 0x040009B2 RID: 2482
		private Font font_0;
	}
}
