﻿using System;
using ns28;

namespace ns6
{
	// Token: 0x02000400 RID: 1024
	internal sealed class EventArgs37 : EventArgs
	{
		// Token: 0x170006DB RID: 1755
		// (get) Token: 0x060027CF RID: 10191 RVA: 0x0000F4B4 File Offset: 0x0000D6B4
		public Enum35 Step
		{
			get
			{
				return this.enum35_0;
			}
		}

		// Token: 0x170006DC RID: 1756
		// (get) Token: 0x060027D0 RID: 10192 RVA: 0x0000F4BC File Offset: 0x0000D6BC
		public bool Failed
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x170006DD RID: 1757
		// (get) Token: 0x060027D1 RID: 10193 RVA: 0x0000F4C4 File Offset: 0x0000D6C4
		public string ErrorMessage
		{
			get
			{
				return this.string_0;
			}
		}

		// Token: 0x170006DE RID: 1758
		// (get) Token: 0x060027D2 RID: 10194 RVA: 0x0000F4CC File Offset: 0x0000D6CC
		public string ReportID
		{
			get
			{
				return this.string_1;
			}
		}

		// Token: 0x060027D3 RID: 10195 RVA: 0x0000F4D4 File Offset: 0x0000D6D4
		internal EventArgs37(Enum35 enum35_1) : this(enum35_1, string.Empty)
		{
		}

		// Token: 0x060027D4 RID: 10196 RVA: 0x0000F4E2 File Offset: 0x0000D6E2
		internal EventArgs37(Enum35 enum35_1, string string_2) : this(enum35_1, string_2, string.Empty)
		{
		}

		// Token: 0x060027D5 RID: 10197 RVA: 0x001029CC File Offset: 0x00100BCC
		internal EventArgs37(Enum35 enum35_1, string string_2, string string_3)
		{
			this.enum35_0 = enum35_1;
			this.bool_0 = (string_2 != null && string_2.Length > 0);
			this.string_0 = string_2;
			this.string_1 = string_3;
		}

		// Token: 0x040013BE RID: 5054
		private Enum35 enum35_0;

		// Token: 0x040013BF RID: 5055
		private readonly bool bool_0;

		// Token: 0x040013C0 RID: 5056
		private readonly string string_0 = string.Empty;

		// Token: 0x040013C1 RID: 5057
		private readonly string string_1 = string.Empty;
	}
}
