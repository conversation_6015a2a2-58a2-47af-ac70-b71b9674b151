﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using ns26;
using TEx;
using TEx.Trading;

namespace ns30
{
	// Token: 0x020000AF RID: 175
	internal sealed class Class68 : TOdrLine
	{
		// Token: 0x0600073E RID: 1854 RVA: 0x00004F59 File Offset: 0x00003159
		public Class68(ChartCS chartCS_0, Transaction transaction_1) : base(chartCS_0, transaction_1.Price)
		{
			this.OpenTrans = transaction_1;
			this.vmethod_1();
			base.method_0();
		}

		// Token: 0x0600073F RID: 1855 RVA: 0x00004F7D File Offset: 0x0000317D
		public override void vmethod_1()
		{
			base.Text = this.method_9();
			if (base.TextBox != null)
			{
				base.TextBox.Text = base.Text;
			}
		}

		// Token: 0x06000740 RID: 1856 RVA: 0x0002E0EC File Offset: 0x0002C2EC
		private string method_8()
		{
			return Base.Trading.smethod_149((Enum17)this.OpenTrans.TransType).Replace("开", "持") + this.OpenTrans.OpenUnits.ToString() + (base.Chart.Symbol.IsFutures ? "手" : "股");
		}

		// Token: 0x06000741 RID: 1857 RVA: 0x0002E158 File Offset: 0x0002C358
		private string method_9()
		{
			return this.method_8() + base.method_5(this.OpenTrans.Price);
		}

		// Token: 0x06000742 RID: 1858 RVA: 0x0002E188 File Offset: 0x0002C388
		private string method_10()
		{
			return string.Concat(new string[]
			{
				this.method_8(),
				"(价",
				base.method_5(this.OpenTrans.Price),
				", 盈亏",
				Base.Trading.smethod_154(this.OpenTrans).ToString(),
				")"
			});
		}

		// Token: 0x06000743 RID: 1859 RVA: 0x0002E1F0 File Offset: 0x0002C3F0
		protected override double vmethod_3()
		{
			double result;
			if (this.OpenTrans is TranStock)
			{
				TranStock tranStock_ = this.OpenTrans as TranStock;
				result = base.Chart.method_220(tranStock_);
			}
			else
			{
				result = Convert.ToDouble(this.OpenTrans.Price);
			}
			return result;
		}

		// Token: 0x06000744 RID: 1860 RVA: 0x0002E23C File Offset: 0x0002C43C
		protected override Color vmethod_7()
		{
			Color result;
			if (this.OpenTrans.TransType == 1)
			{
				result = Color.Red;
			}
			else if (this.OpenTrans.TransType == 3)
			{
				result = Color.Green;
			}
			else
			{
				result = default(Color);
			}
			return result;
		}

		// Token: 0x06000745 RID: 1861 RVA: 0x0002E284 File Offset: 0x0002C484
		protected override DashStyle vmethod_8()
		{
			return DashStyle.Solid;
		}

		// Token: 0x170001B0 RID: 432
		// (get) Token: 0x06000746 RID: 1862 RVA: 0x0002E298 File Offset: 0x0002C498
		// (set) Token: 0x06000747 RID: 1863 RVA: 0x00004FA6 File Offset: 0x000031A6
		public Transaction OpenTrans
		{
			get
			{
				return this.transaction_0;
			}
			set
			{
				this.transaction_0 = value;
			}
		}

		// Token: 0x170001B1 RID: 433
		// (get) Token: 0x06000748 RID: 1864 RVA: 0x0002E2B0 File Offset: 0x0002C4B0
		public override bool IsCurrent
		{
			get
			{
				bool result;
				if (this.OpenTrans.AcctID == Base.Acct.CurrAccount.ID)
				{
					result = (this.OpenTrans.SymbolID == base.Chart.Symbol.ID);
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x170001B2 RID: 434
		// (get) Token: 0x06000749 RID: 1865 RVA: 0x0002E2FC File Offset: 0x0002C4FC
		// (set) Token: 0x0600074A RID: 1866 RVA: 0x0002E354 File Offset: 0x0002C554
		public override bool HighLighted
		{
			get
			{
				bool result;
				if (this.OpenTrans.TransType == 1)
				{
					result = (base.Line.Line.Color != Color.Red);
				}
				else
				{
					result = (base.Line.Line.Color != Color.Green);
				}
				return result;
			}
			set
			{
				Color color;
				if (this.OpenTrans.TransType == 1)
				{
					color = (value ? Color.Coral : Color.Red);
				}
				else
				{
					color = (value ? Color.LimeGreen : Color.Green);
				}
				base.Line.Line.Color = color;
				base.TextBox.FontSpec.FontColor = color;
				if (value)
				{
					base.TextBox.Text = this.method_10();
				}
				else
				{
					base.TextBox.Text = this.method_9();
				}
			}
		}

		// Token: 0x0400032B RID: 811
		private Transaction transaction_0;
	}
}
