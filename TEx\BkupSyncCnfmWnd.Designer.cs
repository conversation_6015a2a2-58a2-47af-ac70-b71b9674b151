﻿namespace TEx
{
	// Token: 0x020000A3 RID: 163
	internal sealed partial class BkupSyncCnfmWnd : global::System.Windows.Forms.Form
	{
		// Token: 0x06000575 RID: 1397 RVA: 0x0002A5E0 File Offset: 0x000287E0
		private void InitializeComponent()
		{
			global::System.ComponentModel.ComponentResourceManager componentResourceManager = new global::System.ComponentModel.ComponentResourceManager(typeof(global::TEx.BkupSyncCnfmWnd));
			this.btnCancel = new global::System.Windows.Forms.Button();
			this.btnOK = new global::System.Windows.Forms.Button();
			this.pictureBox1 = new global::System.Windows.Forms.PictureBox();
			this.pictureBox2 = new global::System.Windows.Forms.PictureBox();
			this.pictureBox4 = new global::System.Windows.Forms.PictureBox();
			this.pictureBox3 = new global::System.Windows.Forms.PictureBox();
			this.label_ask = new global::System.Windows.Forms.Label();
			this.chkBox_noAskNextTime = new global::System.Windows.Forms.CheckBox();
			this.label1 = new global::System.Windows.Forms.Label();
			this.label2 = new global::System.Windows.Forms.Label();
			this.label3 = new global::System.Windows.Forms.Label();
			this.label4 = new global::System.Windows.Forms.Label();
			this.chkBox_ToSv_AcctTrans = new global::System.Windows.Forms.CheckBox();
			this.groupBox1 = new global::System.Windows.Forms.GroupBox();
			this.chkBox_ToLc_AcctTrans = new global::System.Windows.Forms.CheckBox();
			this.label5 = new global::System.Windows.Forms.Label();
			this.label6 = new global::System.Windows.Forms.Label();
			this.label7 = new global::System.Windows.Forms.Label();
			this.label8 = new global::System.Windows.Forms.Label();
			this.label9 = new global::System.Windows.Forms.Label();
			this.chkBox_ToSv_UISettings = new global::System.Windows.Forms.CheckBox();
			this.chkBox_ToLc_UISettings = new global::System.Windows.Forms.CheckBox();
			this.chkBox_ToSv_SymbParams = new global::System.Windows.Forms.CheckBox();
			this.chkBox_ToLc_SymbParams = new global::System.Windows.Forms.CheckBox();
			this.chkBox_ToSv_Pages = new global::System.Windows.Forms.CheckBox();
			this.chkBox_ToLc_Pages = new global::System.Windows.Forms.CheckBox();
			this.chkBox_ToSv_ZiXuan = new global::System.Windows.Forms.CheckBox();
			this.chkBox_ToLc_ZiXuan = new global::System.Windows.Forms.CheckBox();
			this.chkBox_ToSv_DrwObj = new global::System.Windows.Forms.CheckBox();
			this.chkBox_ToLc_DrwObj = new global::System.Windows.Forms.CheckBox();
			this.groupBox2 = new global::System.Windows.Forms.GroupBox();
			((global::System.ComponentModel.ISupportInitialize)this.pictureBox1).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.pictureBox2).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.pictureBox4).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.pictureBox3).BeginInit();
			base.SuspendLayout();
			this.btnCancel.DialogResult = global::System.Windows.Forms.DialogResult.Cancel;
			this.btnCancel.Location = new global::System.Drawing.Point(365, 304);
			this.btnCancel.Name = "btnCancel";
			this.btnCancel.Size = new global::System.Drawing.Size(110, 30);
			this.btnCancel.TabIndex = 7;
			this.btnCancel.Text = "取消";
			this.btnCancel.UseVisualStyleBackColor = true;
			this.btnOK.Location = new global::System.Drawing.Point(241, 304);
			this.btnOK.Name = "btnOK";
			this.btnOK.Size = new global::System.Drawing.Size(110, 30);
			this.btnOK.TabIndex = 6;
			this.btnOK.Text = "确定";
			this.btnOK.UseVisualStyleBackColor = true;
			this.pictureBox1.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("pictureBox1.Image");
			this.pictureBox1.Location = new global::System.Drawing.Point(370, 13);
			this.pictureBox1.Name = "pictureBox1";
			this.pictureBox1.Size = new global::System.Drawing.Size(56, 56);
			this.pictureBox1.SizeMode = global::System.Windows.Forms.PictureBoxSizeMode.StretchImage;
			this.pictureBox1.TabIndex = 8;
			this.pictureBox1.TabStop = false;
			this.pictureBox2.Image = global::ns28.Class372.notebook;
			this.pictureBox2.Location = new global::System.Drawing.Point(101, 18);
			this.pictureBox2.Name = "pictureBox2";
			this.pictureBox2.Size = new global::System.Drawing.Size(48, 48);
			this.pictureBox2.SizeMode = global::System.Windows.Forms.PictureBoxSizeMode.StretchImage;
			this.pictureBox2.TabIndex = 9;
			this.pictureBox2.TabStop = false;
			this.pictureBox4.Image = global::ns28.Class372.biArrow;
			this.pictureBox4.Location = new global::System.Drawing.Point(155, 40);
			this.pictureBox4.Name = "pictureBox4";
			this.pictureBox4.Size = new global::System.Drawing.Size(210, 10);
			this.pictureBox4.SizeMode = global::System.Windows.Forms.PictureBoxSizeMode.StretchImage;
			this.pictureBox4.TabIndex = 11;
			this.pictureBox4.TabStop = false;
			this.pictureBox3.Image = global::ns28.Class372.files;
			this.pictureBox3.Location = new global::System.Drawing.Point(244, 29);
			this.pictureBox3.Name = "pictureBox3";
			this.pictureBox3.Size = new global::System.Drawing.Size(28, 28);
			this.pictureBox3.SizeMode = global::System.Windows.Forms.PictureBoxSizeMode.StretchImage;
			this.pictureBox3.TabIndex = 12;
			this.pictureBox3.TabStop = false;
			this.label_ask.Location = new global::System.Drawing.Point(113, 269);
			this.label_ask.Name = "label_ask";
			this.label_ask.Size = new global::System.Drawing.Size(318, 23);
			this.label_ask.TabIndex = 14;
			this.label_ask.Text = "同步以上所选择内容并覆盖相应文件吗？";
			this.chkBox_noAskNextTime.AutoSize = true;
			this.chkBox_noAskNextTime.Location = new global::System.Drawing.Point(64, 311);
			this.chkBox_noAskNextTime.Name = "chkBox_noAskNextTime";
			this.chkBox_noAskNextTime.Size = new global::System.Drawing.Size(119, 19);
			this.chkBox_noAskNextTime.TabIndex = 15;
			this.chkBox_noAskNextTime.Text = "下次自动同步";
			this.chkBox_noAskNextTime.UseVisualStyleBackColor = true;
			this.label1.AutoSize = true;
			this.label1.Location = new global::System.Drawing.Point(224, 75);
			this.label1.Name = "label1";
			this.label1.Size = new global::System.Drawing.Size(67, 15);
			this.label1.TabIndex = 16;
			this.label1.Text = "文件类型";
			this.label2.AutoSize = true;
			this.label2.Location = new global::System.Drawing.Point(358, 75);
			this.label2.Name = "label2";
			this.label2.Size = new global::System.Drawing.Size(82, 15);
			this.label2.TabIndex = 17;
			this.label2.Text = "同步至云端";
			this.label3.AutoSize = true;
			this.label3.Location = new global::System.Drawing.Point(84, 75);
			this.label3.Name = "label3";
			this.label3.Size = new global::System.Drawing.Size(82, 15);
			this.label3.TabIndex = 18;
			this.label3.Text = "同步至本地";
			this.label4.AutoSize = true;
			this.label4.Location = new global::System.Drawing.Point(202, 108);
			this.label4.Name = "label4";
			this.label4.Size = new global::System.Drawing.Size(112, 15);
			this.label4.TabIndex = 19;
			this.label4.Text = "账户及交易记录";
			this.chkBox_ToSv_AcctTrans.AutoSize = true;
			this.chkBox_ToSv_AcctTrans.Enabled = false;
			this.chkBox_ToSv_AcctTrans.Location = new global::System.Drawing.Point(391, 108);
			this.chkBox_ToSv_AcctTrans.Name = "chkBox_ToSv_AcctTrans";
			this.chkBox_ToSv_AcctTrans.Size = new global::System.Drawing.Size(18, 17);
			this.chkBox_ToSv_AcctTrans.TabIndex = 20;
			this.chkBox_ToSv_AcctTrans.UseVisualStyleBackColor = true;
			this.groupBox1.Location = new global::System.Drawing.Point(87, 90);
			this.groupBox1.Name = "groupBox1";
			this.groupBox1.Size = new global::System.Drawing.Size(350, 10);
			this.groupBox1.TabIndex = 21;
			this.groupBox1.TabStop = false;
			this.chkBox_ToLc_AcctTrans.AutoSize = true;
			this.chkBox_ToLc_AcctTrans.Enabled = false;
			this.chkBox_ToLc_AcctTrans.Location = new global::System.Drawing.Point(116, 107);
			this.chkBox_ToLc_AcctTrans.Name = "chkBox_ToLc_AcctTrans";
			this.chkBox_ToLc_AcctTrans.Size = new global::System.Drawing.Size(18, 17);
			this.chkBox_ToLc_AcctTrans.TabIndex = 22;
			this.chkBox_ToLc_AcctTrans.UseVisualStyleBackColor = true;
			this.label5.AutoSize = true;
			this.label5.Location = new global::System.Drawing.Point(225, 132);
			this.label5.Name = "label5";
			this.label5.Size = new global::System.Drawing.Size(67, 15);
			this.label5.TabIndex = 23;
			this.label5.Text = "系统参数";
			this.label6.AutoSize = true;
			this.label6.Location = new global::System.Drawing.Point(225, 156);
			this.label6.Name = "label6";
			this.label6.Size = new global::System.Drawing.Size(67, 15);
			this.label6.TabIndex = 24;
			this.label6.Text = "品种设置";
			this.label7.AutoSize = true;
			this.label7.Location = new global::System.Drawing.Point(185, 180);
			this.label7.Name = "label7";
			this.label7.Size = new global::System.Drawing.Size(150, 15);
			this.label7.TabIndex = 25;
			this.label7.Text = "页面设置/自定义指标";
			this.label8.AutoSize = true;
			this.label8.Location = new global::System.Drawing.Point(210, 204);
			this.label8.Name = "label8";
			this.label8.Size = new global::System.Drawing.Size(97, 15);
			this.label8.TabIndex = 26;
			this.label8.Text = "自选品种列表";
			this.label9.AutoSize = true;
			this.label9.Location = new global::System.Drawing.Point(240, 228);
			this.label9.Name = "label9";
			this.label9.Size = new global::System.Drawing.Size(37, 15);
			this.label9.TabIndex = 27;
			this.label9.Text = "画线";
			this.chkBox_ToSv_UISettings.AutoSize = true;
			this.chkBox_ToSv_UISettings.Enabled = false;
			this.chkBox_ToSv_UISettings.Location = new global::System.Drawing.Point(391, 132);
			this.chkBox_ToSv_UISettings.Name = "chkBox_ToSv_UISettings";
			this.chkBox_ToSv_UISettings.Size = new global::System.Drawing.Size(18, 17);
			this.chkBox_ToSv_UISettings.TabIndex = 28;
			this.chkBox_ToSv_UISettings.UseVisualStyleBackColor = true;
			this.chkBox_ToLc_UISettings.AutoSize = true;
			this.chkBox_ToLc_UISettings.Enabled = false;
			this.chkBox_ToLc_UISettings.Location = new global::System.Drawing.Point(116, 131);
			this.chkBox_ToLc_UISettings.Name = "chkBox_ToLc_UISettings";
			this.chkBox_ToLc_UISettings.Size = new global::System.Drawing.Size(18, 17);
			this.chkBox_ToLc_UISettings.TabIndex = 29;
			this.chkBox_ToLc_UISettings.UseVisualStyleBackColor = true;
			this.chkBox_ToSv_SymbParams.AutoSize = true;
			this.chkBox_ToSv_SymbParams.Enabled = false;
			this.chkBox_ToSv_SymbParams.Location = new global::System.Drawing.Point(391, 156);
			this.chkBox_ToSv_SymbParams.Name = "chkBox_ToSv_SymbParams";
			this.chkBox_ToSv_SymbParams.Size = new global::System.Drawing.Size(18, 17);
			this.chkBox_ToSv_SymbParams.TabIndex = 30;
			this.chkBox_ToSv_SymbParams.UseVisualStyleBackColor = true;
			this.chkBox_ToLc_SymbParams.AutoSize = true;
			this.chkBox_ToLc_SymbParams.Enabled = false;
			this.chkBox_ToLc_SymbParams.Location = new global::System.Drawing.Point(116, 155);
			this.chkBox_ToLc_SymbParams.Name = "chkBox_ToLc_SymbParams";
			this.chkBox_ToLc_SymbParams.Size = new global::System.Drawing.Size(18, 17);
			this.chkBox_ToLc_SymbParams.TabIndex = 31;
			this.chkBox_ToLc_SymbParams.UseVisualStyleBackColor = true;
			this.chkBox_ToSv_Pages.AutoSize = true;
			this.chkBox_ToSv_Pages.Enabled = false;
			this.chkBox_ToSv_Pages.Location = new global::System.Drawing.Point(391, 180);
			this.chkBox_ToSv_Pages.Name = "chkBox_ToSv_Pages";
			this.chkBox_ToSv_Pages.Size = new global::System.Drawing.Size(18, 17);
			this.chkBox_ToSv_Pages.TabIndex = 32;
			this.chkBox_ToSv_Pages.UseVisualStyleBackColor = true;
			this.chkBox_ToLc_Pages.AutoSize = true;
			this.chkBox_ToLc_Pages.Enabled = false;
			this.chkBox_ToLc_Pages.Location = new global::System.Drawing.Point(116, 179);
			this.chkBox_ToLc_Pages.Name = "chkBox_ToLc_Pages";
			this.chkBox_ToLc_Pages.Size = new global::System.Drawing.Size(18, 17);
			this.chkBox_ToLc_Pages.TabIndex = 33;
			this.chkBox_ToLc_Pages.UseVisualStyleBackColor = true;
			this.chkBox_ToSv_ZiXuan.AutoSize = true;
			this.chkBox_ToSv_ZiXuan.Enabled = false;
			this.chkBox_ToSv_ZiXuan.Location = new global::System.Drawing.Point(391, 204);
			this.chkBox_ToSv_ZiXuan.Name = "chkBox_ToSv_ZiXuan";
			this.chkBox_ToSv_ZiXuan.Size = new global::System.Drawing.Size(18, 17);
			this.chkBox_ToSv_ZiXuan.TabIndex = 34;
			this.chkBox_ToSv_ZiXuan.UseVisualStyleBackColor = true;
			this.chkBox_ToLc_ZiXuan.AutoSize = true;
			this.chkBox_ToLc_ZiXuan.Enabled = false;
			this.chkBox_ToLc_ZiXuan.Location = new global::System.Drawing.Point(116, 203);
			this.chkBox_ToLc_ZiXuan.Name = "chkBox_ToLc_ZiXuan";
			this.chkBox_ToLc_ZiXuan.Size = new global::System.Drawing.Size(18, 17);
			this.chkBox_ToLc_ZiXuan.TabIndex = 35;
			this.chkBox_ToLc_ZiXuan.UseVisualStyleBackColor = true;
			this.chkBox_ToSv_DrwObj.AutoSize = true;
			this.chkBox_ToSv_DrwObj.Enabled = false;
			this.chkBox_ToSv_DrwObj.Location = new global::System.Drawing.Point(391, 228);
			this.chkBox_ToSv_DrwObj.Name = "chkBox_ToSv_DrwObj";
			this.chkBox_ToSv_DrwObj.Size = new global::System.Drawing.Size(18, 17);
			this.chkBox_ToSv_DrwObj.TabIndex = 36;
			this.chkBox_ToSv_DrwObj.UseVisualStyleBackColor = true;
			this.chkBox_ToLc_DrwObj.AutoSize = true;
			this.chkBox_ToLc_DrwObj.Enabled = false;
			this.chkBox_ToLc_DrwObj.Location = new global::System.Drawing.Point(116, 227);
			this.chkBox_ToLc_DrwObj.Name = "chkBox_ToLc_DrwObj";
			this.chkBox_ToLc_DrwObj.Size = new global::System.Drawing.Size(18, 17);
			this.chkBox_ToLc_DrwObj.TabIndex = 37;
			this.chkBox_ToLc_DrwObj.UseVisualStyleBackColor = true;
			this.groupBox2.Location = new global::System.Drawing.Point(87, 247);
			this.groupBox2.Name = "groupBox2";
			this.groupBox2.Size = new global::System.Drawing.Size(350, 10);
			this.groupBox2.TabIndex = 38;
			this.groupBox2.TabStop = false;
			base.AcceptButton = this.btnOK;
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(8f, 15f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			this.BackColor = global::System.Drawing.SystemColors.Control;
			base.ClientSize = new global::System.Drawing.Size(528, 346);
			base.Controls.Add(this.groupBox2);
			base.Controls.Add(this.chkBox_ToLc_DrwObj);
			base.Controls.Add(this.chkBox_ToSv_DrwObj);
			base.Controls.Add(this.chkBox_ToLc_ZiXuan);
			base.Controls.Add(this.chkBox_ToSv_ZiXuan);
			base.Controls.Add(this.chkBox_ToLc_Pages);
			base.Controls.Add(this.chkBox_ToSv_Pages);
			base.Controls.Add(this.chkBox_ToLc_SymbParams);
			base.Controls.Add(this.chkBox_ToSv_SymbParams);
			base.Controls.Add(this.chkBox_ToLc_UISettings);
			base.Controls.Add(this.chkBox_ToSv_UISettings);
			base.Controls.Add(this.label9);
			base.Controls.Add(this.label8);
			base.Controls.Add(this.label7);
			base.Controls.Add(this.label6);
			base.Controls.Add(this.label5);
			base.Controls.Add(this.chkBox_ToLc_AcctTrans);
			base.Controls.Add(this.groupBox1);
			base.Controls.Add(this.chkBox_ToSv_AcctTrans);
			base.Controls.Add(this.label4);
			base.Controls.Add(this.label3);
			base.Controls.Add(this.label2);
			base.Controls.Add(this.label1);
			base.Controls.Add(this.chkBox_noAskNextTime);
			base.Controls.Add(this.label_ask);
			base.Controls.Add(this.pictureBox3);
			base.Controls.Add(this.pictureBox4);
			base.Controls.Add(this.pictureBox2);
			base.Controls.Add(this.pictureBox1);
			base.Controls.Add(this.btnCancel);
			base.Controls.Add(this.btnOK);
			this.DoubleBuffered = true;
			base.FormBorderStyle = global::System.Windows.Forms.FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = "BkupSyncCnfmWnd";
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.StartPosition = global::System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "同步确认";
			base.TopMost = true;
			base.Load += new global::System.EventHandler(this.BkupSyncCnfmWnd_Load);
			((global::System.ComponentModel.ISupportInitialize)this.pictureBox1).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.pictureBox2).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.pictureBox4).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.pictureBox3).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000259 RID: 601
		private global::System.Windows.Forms.Button btnCancel;

		// Token: 0x0400025A RID: 602
		private global::System.Windows.Forms.Button btnOK;

		// Token: 0x0400025B RID: 603
		private global::System.Windows.Forms.PictureBox pictureBox1;

		// Token: 0x0400025C RID: 604
		private global::System.Windows.Forms.PictureBox pictureBox2;

		// Token: 0x0400025D RID: 605
		private global::System.Windows.Forms.PictureBox pictureBox4;

		// Token: 0x0400025E RID: 606
		private global::System.Windows.Forms.PictureBox pictureBox3;

		// Token: 0x0400025F RID: 607
		private global::System.Windows.Forms.Label label_ask;

		// Token: 0x04000260 RID: 608
		private global::System.Windows.Forms.CheckBox chkBox_noAskNextTime;

		// Token: 0x04000261 RID: 609
		private global::System.Windows.Forms.Label label1;

		// Token: 0x04000262 RID: 610
		private global::System.Windows.Forms.Label label2;

		// Token: 0x04000263 RID: 611
		private global::System.Windows.Forms.Label label3;

		// Token: 0x04000264 RID: 612
		private global::System.Windows.Forms.Label label4;

		// Token: 0x04000265 RID: 613
		private global::System.Windows.Forms.CheckBox chkBox_ToSv_AcctTrans;

		// Token: 0x04000266 RID: 614
		private global::System.Windows.Forms.GroupBox groupBox1;

		// Token: 0x04000267 RID: 615
		private global::System.Windows.Forms.CheckBox chkBox_ToLc_AcctTrans;

		// Token: 0x04000268 RID: 616
		private global::System.Windows.Forms.Label label5;

		// Token: 0x04000269 RID: 617
		private global::System.Windows.Forms.Label label6;

		// Token: 0x0400026A RID: 618
		private global::System.Windows.Forms.Label label7;

		// Token: 0x0400026B RID: 619
		private global::System.Windows.Forms.Label label8;

		// Token: 0x0400026C RID: 620
		private global::System.Windows.Forms.Label label9;

		// Token: 0x0400026D RID: 621
		private global::System.Windows.Forms.CheckBox chkBox_ToSv_UISettings;

		// Token: 0x0400026E RID: 622
		private global::System.Windows.Forms.CheckBox chkBox_ToLc_UISettings;

		// Token: 0x0400026F RID: 623
		private global::System.Windows.Forms.CheckBox chkBox_ToSv_SymbParams;

		// Token: 0x04000270 RID: 624
		private global::System.Windows.Forms.CheckBox chkBox_ToLc_SymbParams;

		// Token: 0x04000271 RID: 625
		private global::System.Windows.Forms.CheckBox chkBox_ToSv_Pages;

		// Token: 0x04000272 RID: 626
		private global::System.Windows.Forms.CheckBox chkBox_ToLc_Pages;

		// Token: 0x04000273 RID: 627
		private global::System.Windows.Forms.CheckBox chkBox_ToSv_ZiXuan;

		// Token: 0x04000274 RID: 628
		private global::System.Windows.Forms.CheckBox chkBox_ToLc_ZiXuan;

		// Token: 0x04000275 RID: 629
		private global::System.Windows.Forms.CheckBox chkBox_ToSv_DrwObj;

		// Token: 0x04000276 RID: 630
		private global::System.Windows.Forms.CheckBox chkBox_ToLc_DrwObj;

		// Token: 0x04000277 RID: 631
		private global::System.Windows.Forms.GroupBox groupBox2;
	}
}
