﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns28;

namespace ns10
{
	// Token: 0x02000410 RID: 1040
	[DesignerCategory("Code")]
	internal sealed class Control3 : Control
	{
		// Token: 0x170006E5 RID: 1765
		// (get) Token: 0x06002827 RID: 10279 RVA: 0x0000F8C9 File Offset: 0x0000DAC9
		// (set) Token: 0x06002828 RID: 10280 RVA: 0x0000F8D1 File Offset: 0x0000DAD1
		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		public string Text
		{
			get
			{
				return base.Text;
			}
			set
			{
				base.Text = value;
				this.Refresh();
			}
		}

		// Token: 0x06002829 RID: 10281 RVA: 0x0000F8E0 File Offset: 0x0000DAE0
		public void method_0()
		{
			this.timer_0.Enabled = false;
			this.image_0 = null;
			this.bool_0 = false;
			this.string_0 = string.Empty;
			this.Refresh();
			base.Height = 16;
		}

		// Token: 0x0600282A RID: 10282 RVA: 0x0000F915 File Offset: 0x0000DB15
		public void method_1()
		{
			this.timer_0.Enabled = true;
			this.image_0 = Class537.smethod_0("current");
			this.bool_0 = true;
			this.Refresh();
		}

		// Token: 0x0600282B RID: 10283 RVA: 0x0000F940 File Offset: 0x0000DB40
		public void method_2()
		{
			this.method_3(string.Empty);
		}

		// Token: 0x0600282C RID: 10284 RVA: 0x00103764 File Offset: 0x00101964
		public void method_3(string string_1)
		{
			this.string_0 = string_1;
			this.timer_0.Enabled = false;
			this.image_0 = Class537.smethod_0((string_1.Length > 0) ? "error" : "ok");
			this.bool_1 = true;
			this.bool_0 = true;
			if (string_1.Length > 0)
			{
				base.Height = 100;
			}
			this.Refresh();
		}

		// Token: 0x0600282D RID: 10285 RVA: 0x001037CC File Offset: 0x001019CC
		protected void OnResize(EventArgs e)
		{
			this.label_0.SetBounds(Convert.ToInt32(22f * this.float_0), Convert.ToInt32(this.float_1), base.Width - Convert.ToInt32(22f * this.float_0), base.Height - Convert.ToInt32(this.float_1));
			base.OnResize(e);
		}

		// Token: 0x0600282E RID: 10286 RVA: 0x0000F94D File Offset: 0x0000DB4D
		protected void ScaleCore(float dx, float dy)
		{
			this.float_0 = dx;
			this.float_1 = dy;
			base.ScaleCore(dx, dy);
			this.OnResize(EventArgs.Empty);
		}

		// Token: 0x0600282F RID: 10287 RVA: 0x00103834 File Offset: 0x00101A34
		protected void OnPaint(PaintEventArgs e)
		{
			base.OnPaint(e);
			if (base.DesignMode)
			{
				this.image_0 = Class537.smethod_0("current");
				this.bool_0 = true;
			}
			if (this.image_0 != null && this.bool_1)
			{
				e.Graphics.DrawImage(this.image_0, new Rectangle(0, 0, Convert.ToInt32(16f * this.float_0), Convert.ToInt32(16f * this.float_1)), new Rectangle(0, 0, 16, 16), GraphicsUnit.Pixel);
			}
			if (this.bool_0)
			{
				this.label_0.Text = ((this.string_0.Length > 0) ? (base.Text + " (" + this.string_0 + ")") : base.Text);
				return;
			}
			this.label_0.Text = string.Empty;
		}

		// Token: 0x06002830 RID: 10288 RVA: 0x00103914 File Offset: 0x00101B14
		public Control3()
		{
			this.timer_0.Interval = 250;
			this.timer_0.Tick += this.timer_0_Tick;
			this.label_0.FlatStyle = FlatStyle.System;
			base.Controls.Add(this.label_0);
			base.SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.SupportsTransparentBackColor | ControlStyles.AllPaintingInWmPaint | ControlStyles.DoubleBuffer, true);
			base.TabStop = false;
		}

		// Token: 0x06002831 RID: 10289 RVA: 0x0000F970 File Offset: 0x0000DB70
		public Control3(string string_1) : this()
		{
			base.Text = " " + string_1;
		}

		// Token: 0x06002832 RID: 10290 RVA: 0x0000F989 File Offset: 0x0000DB89
		protected void Dispose(bool disposing)
		{
			if (disposing)
			{
				if (this.image_0 != null)
				{
					this.image_0.Dispose();
				}
				this.timer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06002833 RID: 10291 RVA: 0x0000F9B3 File Offset: 0x0000DBB3
		private void timer_0_Tick(object sender, EventArgs e)
		{
			this.bool_1 = !this.bool_1;
			this.Refresh();
		}

		// Token: 0x040013FE RID: 5118
		private readonly Label label_0 = new Label();

		// Token: 0x040013FF RID: 5119
		private Image image_0;

		// Token: 0x04001400 RID: 5120
		private bool bool_0;

		// Token: 0x04001401 RID: 5121
		private readonly Timer timer_0 = new Timer();

		// Token: 0x04001402 RID: 5122
		private bool bool_1 = true;

		// Token: 0x04001403 RID: 5123
		private string string_0 = string.Empty;

		// Token: 0x04001404 RID: 5124
		private float float_0 = 1f;

		// Token: 0x04001405 RID: 5125
		private float float_1 = 1f;
	}
}
