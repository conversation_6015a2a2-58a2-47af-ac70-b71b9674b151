﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using DevComponents.DotNetBar.Rendering;
using ns11;
using ns13;
using ns28;
using ns6;
using TEx;

namespace ns33
{
	// Token: 0x02000163 RID: 355
	internal sealed partial class DrawOdrWnd : Form
	{
		// Token: 0x06000D79 RID: 3449 RVA: 0x00005FAE File Offset: 0x000041AE
		public DrawOdrWnd()
		{
			this.InitializeComponent();
			base.Load += this.DrawOdrWnd_Load;
			base.Shown += this.DrawOdrWnd_Shown;
		}

		// Token: 0x06000D7A RID: 3450 RVA: 0x0005482C File Offset: 0x00052A2C
		private void DrawOdrWnd_Load(object sender, EventArgs e)
		{
			this.method_0(this.btnX_clsRevOpen);
			this.method_0(this.btnX_delOdrLine);
			this.method_0(this.btnX_Long);
			this.method_0(this.btnX_Shrt);
			this.method_0(this.btnX_condClose);
			this.method_0(this.btnX_ROpen);
			this.btnX_clsRevOpen.AutoCheckOnClick = true;
			this.btnX_delOdrLine.AutoCheckOnClick = true;
			this.btnX_Long.AutoCheckOnClick = true;
			this.btnX_Shrt.AutoCheckOnClick = true;
			this.btnX_condClose.AutoCheckOnClick = true;
			this.btnX_ROpen.AutoCheckOnClick = true;
			this.btnX_clsRevOpen.Click += this.btnX_clsRevOpen_Click;
			this.btnX_delOdrLine.Click += this.btnX_delOdrLine_Click;
			this.btnX_Long.Click += this.btnX_Long_Click;
			this.btnX_Shrt.Click += this.btnX_Shrt_Click;
			this.btnX_condClose.Click += this.btnX_condClose_Click;
			this.btnX_ROpen.Click += this.btnX_ROpen_Click;
			Base.UI.DrawModeSetOff += this.method_5;
			Base.UI.DrawOdrWnd = this;
			base.FormClosed += this.DrawOdrWnd_FormClosed;
			base.Deactivate += this.DrawOdrWnd_Deactivate;
			base.Activated += this.DrawOdrWnd_Activated;
			base.Disposed += this.DrawOdrWnd_Disposed;
			this.method_2();
			Base.Data.CurrSymblChanged += this.method_1;
			base.Focus();
		}

		// Token: 0x06000D7B RID: 3451 RVA: 0x00005FE2 File Offset: 0x000041E2
		private void method_0(ButtonX buttonX_0)
		{
			buttonX_0.RenderMode = eRenderMode.Custom;
			buttonX_0.Renderer = new Office2007Renderer();
			buttonX_0.BackColor = Class179.color_10;
		}

		// Token: 0x06000D7C RID: 3452 RVA: 0x000549D4 File Offset: 0x00052BD4
		protected bool ProcessCmdKey(ref Message msg, Keys keyData)
		{
			if (msg.WParam.ToInt32() == 27)
			{
				Base.UI.smethod_156();
			}
			return base.ProcessCmdKey(ref msg, keyData);
		}

		// Token: 0x06000D7D RID: 3453 RVA: 0x00006003 File Offset: 0x00004203
		private void method_1(EventArgs1 eventArgs1_0)
		{
			this.method_2();
		}

		// Token: 0x06000D7E RID: 3454 RVA: 0x00054A04 File Offset: 0x00052C04
		private void method_2()
		{
			bool enabled = true;
			if ((Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl.SymbDataSet != null && Base.UI.SelectedChtCtrl.SymbDataSet.CurrSymbol != null && !Base.UI.SelectedChtCtrl.SymbDataSet.CurrSymblSupportsShort) || (Base.Data.CurrSymbDataSet != null && Base.Data.CurrSymbDataSet.CurrSymbol != null && !Base.Data.CurrSymbDataSet.CurrSymblSupportsShort))
			{
				enabled = false;
			}
			this.btnX_Shrt.Enabled = enabled;
		}

		// Token: 0x06000D7F RID: 3455 RVA: 0x0000600D File Offset: 0x0000420D
		private void DrawOdrWnd_Shown(object sender, EventArgs e)
		{
			this.Text = "画线下单";
		}

		// Token: 0x06000D80 RID: 3456 RVA: 0x0000601C File Offset: 0x0000421C
		private void DrawOdrWnd_Deactivate(object sender, EventArgs e)
		{
			base.TopMost = false;
		}

		// Token: 0x06000D81 RID: 3457 RVA: 0x00006027 File Offset: 0x00004227
		private void DrawOdrWnd_Activated(object sender, EventArgs e)
		{
			base.TopMost = true;
		}

		// Token: 0x06000D82 RID: 3458 RVA: 0x00054A78 File Offset: 0x00052C78
		private void btnX_clsRevOpen_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_3(buttonX_, TEx.DrawMode.RevTransOdr);
		}

		// Token: 0x06000D83 RID: 3459 RVA: 0x00054A98 File Offset: 0x00052C98
		private void btnX_delOdrLine_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_3(buttonX_, TEx.DrawMode.EraseAllDrawOdr);
		}

		// Token: 0x06000D84 RID: 3460 RVA: 0x00054AB8 File Offset: 0x00052CB8
		private void btnX_Long_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_3(buttonX_, TEx.DrawMode.OpenLongOdr);
		}

		// Token: 0x06000D85 RID: 3461 RVA: 0x00054AD8 File Offset: 0x00052CD8
		private void btnX_Shrt_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_3(buttonX_, TEx.DrawMode.OpenShrtOdr);
		}

		// Token: 0x06000D86 RID: 3462 RVA: 0x00054AF8 File Offset: 0x00052CF8
		private void btnX_condClose_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_3(buttonX_, TEx.DrawMode.StopLimitOdr);
		}

		// Token: 0x06000D87 RID: 3463 RVA: 0x00054B18 File Offset: 0x00052D18
		private void btnX_ROpen_Click(object sender, EventArgs e)
		{
			ButtonX buttonX = (ButtonX)sender;
			if (Base.UI.Form.IfROpenNoShowCnfmDlg)
			{
				this.method_3(buttonX, TEx.DrawMode.ROpenOdr);
			}
			else if (new ROpenCnfmWnd().ShowDialog() == DialogResult.OK)
			{
				this.method_3(buttonX, TEx.DrawMode.ROpenOdr);
			}
			else
			{
				buttonX.Checked = false;
			}
		}

		// Token: 0x06000D88 RID: 3464 RVA: 0x00054B64 File Offset: 0x00052D64
		private void method_3(ButtonX buttonX_0, TEx.DrawMode drawMode_0)
		{
			if (buttonX_0.Checked)
			{
				this.method_4(buttonX_0);
				if (Base.Acct.CurrAccount.IsReadOnly)
				{
					Base.Acct.smethod_50();
					buttonX_0.Checked = false;
					Base.UI.DrawMode = TEx.DrawMode.Off;
					return;
				}
				Base.UI.DrawMode = drawMode_0;
				if (drawMode_0 == TEx.DrawMode.EraseAllDrawOdr || Base.UI.Form.StockRestorationMethod == null || Base.UI.Form.StockRestorationMethod.Value != StockRestorationMethod.Later)
				{
					return;
				}
				using (List<ChtCtrl>.Enumerator enumerator = Base.UI.ChtCtrlList.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						ChtCtrl chtCtrl = enumerator.Current;
						SymbDataSet symbDataSet = chtCtrl.SymbDataSet;
						if (symbDataSet.CurrSymbol.IsStock && symbDataSet.CurrSymbStSpltList != null)
						{
							chtCtrl.method_31();
						}
					}
					return;
				}
			}
			Base.UI.DrawMode = TEx.DrawMode.Off;
		}

		// Token: 0x06000D89 RID: 3465 RVA: 0x00054C48 File Offset: 0x00052E48
		private void method_4(ButtonX buttonX_0)
		{
			foreach (object obj in base.Controls)
			{
				if (obj is ButtonX && obj != buttonX_0)
				{
					ButtonX buttonX = (ButtonX)obj;
					if (buttonX.Checked)
					{
						buttonX.Checked = false;
					}
				}
			}
		}

		// Token: 0x06000D8A RID: 3466 RVA: 0x00006032 File Offset: 0x00004232
		private void DrawOdrWnd_FormClosed(object sender, FormClosedEventArgs e)
		{
			Base.UI.DrawMode = TEx.DrawMode.Off;
			Base.UI.DrawOdrWnd = null;
			Base.UI.smethod_156();
		}

		// Token: 0x06000D8B RID: 3467 RVA: 0x00006048 File Offset: 0x00004248
		private void method_5(object sender, EventArgs e)
		{
			this.method_6();
		}

		// Token: 0x06000D8C RID: 3468 RVA: 0x00054CBC File Offset: 0x00052EBC
		private void method_6()
		{
			foreach (object obj in base.Controls)
			{
				if (obj is ButtonX)
				{
					ButtonX buttonX = (ButtonX)obj;
					if (buttonX.Checked)
					{
						buttonX.Checked = false;
					}
				}
			}
		}

		// Token: 0x06000D8D RID: 3469 RVA: 0x00006052 File Offset: 0x00004252
		private void DrawOdrWnd_Disposed(object sender, EventArgs e)
		{
			Base.UI.DrawModeSetOff -= this.method_5;
			Base.Data.CurrSymblChanged -= this.method_1;
		}

		// Token: 0x06000D8E RID: 3470 RVA: 0x00006078 File Offset: 0x00004278
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x040006DD RID: 1757
		private IContainer icontainer_0;
	}
}
