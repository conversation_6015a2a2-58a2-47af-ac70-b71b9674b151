﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Runtime.Serialization;

namespace TEx.Trading
{
	// Token: 0x020003B0 RID: 944
	[Serializable]
	internal sealed class Account : INotifyPropertyChanging, INotifyPropertyChanged
	{
		// Token: 0x06002648 RID: 9800 RVA: 0x00002D25 File Offset: 0x00000F25
		public Account()
		{
		}

		// Token: 0x06002649 RID: 9801 RVA: 0x000FB6EC File Offset: 0x000F98EC
		protected Account(SerializationInfo info, StreamingContext context)
		{
			this._ID = info.GetInt32("ID");
			this._AcctName = info.GetString("AcctName");
			this._IniBal = info.GetDecimal("IniBal");
			this._EndingBal = (decimal?)info.GetValue("EndingBal", typeof(decimal?));
			this._LastSymbID = (int?)info.GetValue("LastSymbID", typeof(int?));
			this._LastSymbDT = (DateTime?)info.GetValue("LastSymbDT", typeof(DateTime?));
			this._IsReadOnly = info.GetBoolean("IsReadOnly");
			this._OpenDate = (DateTime?)info.GetValue("OpenDate", typeof(DateTime?));
			this._UpdateTime = (DateTime?)info.GetValue("UpdateTime", typeof(DateTime?));
			this._Notes = info.GetString("Notes");
			try
			{
				this._LastSymbIdAndDtList = (List<KeyValuePair<int, DateTime>>)info.GetValue("LastSymbIdAndDtList", typeof(List<KeyValuePair<int, DateTime>>));
			}
			catch
			{
			}
		}

		// Token: 0x0600264A RID: 9802 RVA: 0x000FB82C File Offset: 0x000F9A2C
		public void vmethod_0(SerializationInfo serializationInfo_0, StreamingContext streamingContext_0)
		{
			serializationInfo_0.AddValue("ID", this._ID);
			serializationInfo_0.AddValue("AcctName", this._AcctName);
			serializationInfo_0.AddValue("IniBal", this._IniBal);
			serializationInfo_0.AddValue("EndingBal", this._EndingBal);
			serializationInfo_0.AddValue("IsReadOnly", this._IsReadOnly);
			serializationInfo_0.AddValue("LastSymbID", this._LastSymbID);
			serializationInfo_0.AddValue("LastSymbDT", this._LastSymbDT);
			serializationInfo_0.AddValue("LastSymbIdAndDtList", this._LastSymbIdAndDtList);
			serializationInfo_0.AddValue("OpenDate", this._OpenDate);
			serializationInfo_0.AddValue("UpdateTime", this._UpdateTime);
			serializationInfo_0.AddValue("Notes", this._Notes);
		}

		// Token: 0x17000681 RID: 1665
		// (get) Token: 0x0600264B RID: 9803 RVA: 0x000FB910 File Offset: 0x000F9B10
		// (set) Token: 0x0600264C RID: 9804 RVA: 0x0000E964 File Offset: 0x0000CB64
		public int ID
		{
			get
			{
				return this._ID;
			}
			set
			{
				if (this._ID != value)
				{
					this.vmethod_1();
					this._ID = value;
					this.vmethod_2("ID");
				}
			}
		}

		// Token: 0x17000682 RID: 1666
		// (get) Token: 0x0600264D RID: 9805 RVA: 0x000FB928 File Offset: 0x000F9B28
		// (set) Token: 0x0600264E RID: 9806 RVA: 0x0000E989 File Offset: 0x0000CB89
		public string AcctName
		{
			get
			{
				return this._AcctName;
			}
			set
			{
				if (this._AcctName != value)
				{
					this.vmethod_1();
					this._AcctName = value;
					this.vmethod_2("AcctName");
				}
			}
		}

		// Token: 0x17000683 RID: 1667
		// (get) Token: 0x0600264F RID: 9807 RVA: 0x000FB940 File Offset: 0x000F9B40
		// (set) Token: 0x06002650 RID: 9808 RVA: 0x0000E9B3 File Offset: 0x0000CBB3
		public decimal IniBal
		{
			get
			{
				return this._IniBal;
			}
			set
			{
				if (this._IniBal != value)
				{
					this.vmethod_1();
					this._IniBal = value;
					this.vmethod_2("IniBal");
				}
			}
		}

		// Token: 0x17000684 RID: 1668
		// (get) Token: 0x06002651 RID: 9809 RVA: 0x000FB958 File Offset: 0x000F9B58
		// (set) Token: 0x06002652 RID: 9810 RVA: 0x000FB970 File Offset: 0x000F9B70
		public decimal? EndingBal
		{
			get
			{
				return this._EndingBal;
			}
			set
			{
				decimal? endingBal = this._EndingBal;
				decimal? num = value;
				if (!(endingBal.GetValueOrDefault() == num.GetValueOrDefault() & endingBal != null == (num != null)))
				{
					this.vmethod_1();
					this._EndingBal = value;
					this.vmethod_2("EndingBal");
				}
			}
		}

		// Token: 0x17000685 RID: 1669
		// (get) Token: 0x06002653 RID: 9811 RVA: 0x000FB9C8 File Offset: 0x000F9BC8
		// (set) Token: 0x06002654 RID: 9812 RVA: 0x000FB9E0 File Offset: 0x000F9BE0
		public DateTime? OpenDate
		{
			get
			{
				return this._OpenDate;
			}
			set
			{
				if (this._OpenDate != value)
				{
					this.vmethod_1();
					this._OpenDate = value;
					this.vmethod_2("OpenDate");
				}
			}
		}

		// Token: 0x17000686 RID: 1670
		// (get) Token: 0x06002655 RID: 9813 RVA: 0x000FBA44 File Offset: 0x000F9C44
		// (set) Token: 0x06002656 RID: 9814 RVA: 0x000FBA5C File Offset: 0x000F9C5C
		public int? LastSymbID
		{
			get
			{
				return this._LastSymbID;
			}
			set
			{
				int? lastSymbID = this._LastSymbID;
				int? num = value;
				if (!(lastSymbID.GetValueOrDefault() == num.GetValueOrDefault() & lastSymbID != null == (num != null)))
				{
					this.vmethod_1();
					this._LastSymbID = value;
					this.vmethod_2("LastSymbID");
				}
			}
		}

		// Token: 0x17000687 RID: 1671
		// (get) Token: 0x06002657 RID: 9815 RVA: 0x000FBAB0 File Offset: 0x000F9CB0
		// (set) Token: 0x06002658 RID: 9816 RVA: 0x000FBAC8 File Offset: 0x000F9CC8
		public DateTime? LastSymbDT
		{
			get
			{
				return this._LastSymbDT;
			}
			set
			{
				if (this._LastSymbDT != value)
				{
					DateTime? dateTime = value;
					DateTime d = default(DateTime);
					if (dateTime != null && (dateTime == null || dateTime.GetValueOrDefault() == d))
					{
						throw new Exception("LastSymbDT error!");
					}
					this.vmethod_1();
					this._LastSymbDT = value;
					this.vmethod_2("LastSymbDT");
				}
			}
		}

		// Token: 0x17000688 RID: 1672
		// (get) Token: 0x06002659 RID: 9817 RVA: 0x000FBB68 File Offset: 0x000F9D68
		// (set) Token: 0x0600265A RID: 9818 RVA: 0x0000E9DD File Offset: 0x0000CBDD
		public List<KeyValuePair<int, DateTime>> LastSymbIdAndDtList
		{
			get
			{
				return this._LastSymbIdAndDtList;
			}
			set
			{
				if (this._LastSymbIdAndDtList != value)
				{
					this._LastSymbIdAndDtList = value;
				}
			}
		}

		// Token: 0x17000689 RID: 1673
		// (get) Token: 0x0600265B RID: 9819 RVA: 0x000FBB80 File Offset: 0x000F9D80
		// (set) Token: 0x0600265C RID: 9820 RVA: 0x0000E9F1 File Offset: 0x0000CBF1
		public string Notes
		{
			get
			{
				return this._Notes;
			}
			set
			{
				if (this._Notes != value)
				{
					this.vmethod_1();
					this._Notes = value;
					this.vmethod_2("Notes");
				}
			}
		}

		// Token: 0x1700068A RID: 1674
		// (get) Token: 0x0600265D RID: 9821 RVA: 0x000FBB98 File Offset: 0x000F9D98
		// (set) Token: 0x0600265E RID: 9822 RVA: 0x000FBBB0 File Offset: 0x000F9DB0
		public DateTime? UpdateTime
		{
			get
			{
				return this._UpdateTime;
			}
			set
			{
				if (this._UpdateTime != value)
				{
					this.vmethod_1();
					this._UpdateTime = value;
					this.vmethod_2("UpdateTime");
				}
			}
		}

		// Token: 0x1700068B RID: 1675
		// (get) Token: 0x0600265F RID: 9823 RVA: 0x000FBC14 File Offset: 0x000F9E14
		// (set) Token: 0x06002660 RID: 9824 RVA: 0x0000EA1B File Offset: 0x0000CC1B
		public bool IsReadOnly
		{
			get
			{
				return this._IsReadOnly;
			}
			set
			{
				if (this._IsReadOnly != value)
				{
					this.vmethod_1();
					this._IsReadOnly = value;
					this.vmethod_2("IsReadOnly");
				}
			}
		}

		// Token: 0x1700068C RID: 1676
		// (get) Token: 0x06002661 RID: 9825 RVA: 0x000FBC2C File Offset: 0x000F9E2C
		// (set) Token: 0x06002662 RID: 9826 RVA: 0x0000EA40 File Offset: 0x0000CC40
		public string UserName
		{
			get
			{
				return this._UserName;
			}
			set
			{
				this._UserName = value;
			}
		}

		// Token: 0x140000BA RID: 186
		// (add) Token: 0x06002663 RID: 9827 RVA: 0x000FBC44 File Offset: 0x000F9E44
		// (remove) Token: 0x06002664 RID: 9828 RVA: 0x000FBC7C File Offset: 0x000F9E7C
		public event PropertyChangingEventHandler PropertyChanging;

		// Token: 0x140000BB RID: 187
		// (add) Token: 0x06002665 RID: 9829 RVA: 0x000FBCB4 File Offset: 0x000F9EB4
		// (remove) Token: 0x06002666 RID: 9830 RVA: 0x000FBCEC File Offset: 0x000F9EEC
		public event PropertyChangedEventHandler PropertyChanged;

		// Token: 0x06002667 RID: 9831 RVA: 0x0000EA4B File Offset: 0x0000CC4B
		protected void vmethod_1()
		{
			if (this.PropertyChanging != null)
			{
				this.PropertyChanging(this, Account.emptyChangingEventArgs);
			}
		}

		// Token: 0x06002668 RID: 9832 RVA: 0x0000EA68 File Offset: 0x0000CC68
		protected void vmethod_2(string string_0)
		{
			if (this.PropertyChanged != null)
			{
				this.PropertyChanged(this, new PropertyChangedEventArgs(string_0));
			}
		}

		// Token: 0x04001272 RID: 4722
		private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(string.Empty);

		// Token: 0x04001273 RID: 4723
		private int _ID;

		// Token: 0x04001274 RID: 4724
		private string _AcctName;

		// Token: 0x04001275 RID: 4725
		private decimal _IniBal;

		// Token: 0x04001276 RID: 4726
		private decimal? _EndingBal;

		// Token: 0x04001277 RID: 4727
		private DateTime? _OpenDate;

		// Token: 0x04001278 RID: 4728
		private int? _LastSymbID;

		// Token: 0x04001279 RID: 4729
		private DateTime? _LastSymbDT;

		// Token: 0x0400127A RID: 4730
		private List<KeyValuePair<int, DateTime>> _LastSymbIdAndDtList;

		// Token: 0x0400127B RID: 4731
		private string _Notes;

		// Token: 0x0400127C RID: 4732
		private DateTime? _UpdateTime;

		// Token: 0x0400127D RID: 4733
		private bool _IsReadOnly;

		// Token: 0x0400127E RID: 4734
		private string _UserName;

		// Token: 0x0400127F RID: 4735
		[CompilerGenerated]
		private PropertyChangingEventHandler PropertyChanging;

		// Token: 0x04001280 RID: 4736
		[CompilerGenerated]
		private PropertyChangedEventHandler PropertyChanged;
	}
}
