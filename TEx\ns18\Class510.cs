﻿using System;
using ns15;
using ns17;
using ns19;
using ns22;

namespace ns18
{
	// Token: 0x020003C4 RID: 964
	internal sealed class Class510
	{
		// Token: 0x060026D8 RID: 9944 RVA: 0x000FC550 File Offset: 0x000FA750
		public static string smethod_0()
		{
			string result;
			try
			{
				object obj;
				Enum33 @enum = Class510.smethod_1("Path", Class511.SubkeyApplication, out obj);
				if (@enum == Enum33.const_1)
				{
					result = null;
				}
				else
				{
					if (@enum == Enum33.const_0)
					{
						@enum = Class510.smethod_1("Path", Class511.WowSubkeyApplication, out obj);
					}
					result = (string)obj;
				}
			}
			catch
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060026D9 RID: 9945 RVA: 0x000FC5B0 File Offset: 0x000FA7B0
		public static Enum33 smethod_1(string string_0, string string_1, out object object_0)
		{
			object_0 = null;
			try
			{
				Enum33 @enum;
				using (Class513 @class = Class515.smethod_0(Class512.uintptr_2, Enum34.const_0, Enum32.const_0, string_1, out @enum))
				{
					if (@enum == Enum33.const_1)
					{
						return Enum33.const_1;
					}
					if (@enum != Enum33.const_2)
					{
						return Enum33.const_0;
					}
					object_0 = @class.vmethod_0(string_0);
				}
			}
			catch
			{
				return Enum33.const_0;
			}
			return Enum33.const_2;
		}
	}
}
