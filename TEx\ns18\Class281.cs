﻿using System;
using System.Management;
using ns28;

namespace ns18
{
	// Token: 0x020001FA RID: 506
	internal static class Class281
	{
		// Token: 0x06001433 RID: 5171 RVA: 0x000857E0 File Offset: 0x000839E0
		public static string smethod_0()
		{
			string result;
			try
			{
				string str = "C";
				ManagementObject managementObject = new ManagementObject("Win32_LogicalDisk.DeviceID=\"" + str + ":\"");
				managementObject.Get();
				result = managementObject.GetPropertyValue("VolumeSerialNumber").ToString().Trim();
			}
			catch (Exception exception_)
			{
				Class182.smethod_2(exception_, true);
				throw;
			}
			return result;
		}

		// Token: 0x06001434 RID: 5172 RVA: 0x00085844 File Offset: 0x00083A44
		public static string smethod_1(bool bool_0 = true)
		{
			string text = string.Empty;
			try
			{
				ManagementObjectCollection managementObjectCollection = Class281.smethod_2();
				if (managementObjectCollection != null && managementObjectCollection.Count > 0)
				{
					foreach (ManagementBaseObject managementBaseObject in managementObjectCollection)
					{
						object obj = ((ManagementObject)managementBaseObject)["SerialNumber"];
						if (obj != null)
						{
							text = obj.ToString().Trim();
							if (bool_0 && !string.IsNullOrEmpty(text))
							{
								break;
							}
						}
					}
				}
				if (string.IsNullOrEmpty(text))
				{
					text = Class281.smethod_0();
				}
			}
			catch (Exception exception_)
			{
				Class182.smethod_1(exception_, new bool?(false));
				text = Class281.smethod_0();
			}
			return text;
		}

		// Token: 0x06001435 RID: 5173 RVA: 0x00085900 File Offset: 0x00083B00
		private static ManagementObjectCollection smethod_2()
		{
			ManagementObjectCollection result;
			try
			{
				result = new ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMedia").Get();
			}
			catch
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001436 RID: 5174 RVA: 0x0008593C File Offset: 0x00083B3C
		public static string smethod_3()
		{
			string result = null;
			ManagementObjectCollection instances = new ManagementClass("win32_Processor").GetInstances();
			if (instances != null)
			{
				foreach (ManagementBaseObject managementBaseObject in instances)
				{
					foreach (PropertyData propertyData in ((ManagementObject)managementBaseObject).Properties)
					{
						if (propertyData.Name == "Processorid" || propertyData.Name == "ProcessorId")
						{
							result = propertyData.Value.ToString();
							break;
						}
					}
				}
			}
			return result;
		}

		// Token: 0x06001437 RID: 5175 RVA: 0x00085A18 File Offset: 0x00083C18
		public static string smethod_4()
		{
			string result = "Unknown";
			ManagementObjectCollection instances = new ManagementClass("Win32_Processor").GetInstances();
			if (instances != null)
			{
				foreach (ManagementBaseObject managementBaseObject in instances)
				{
					object propertyValue = ((ManagementObject)managementBaseObject).GetPropertyValue("Name");
					if (propertyValue != null)
					{
						result = propertyValue.ToString();
						break;
					}
				}
			}
			return result;
		}

		// Token: 0x06001438 RID: 5176 RVA: 0x00085A98 File Offset: 0x00083C98
		public static string smethod_5()
		{
			string text = string.Empty;
			string result;
			try
			{
				foreach (ManagementBaseObject managementBaseObject in new ManagementClass("Win32_NetworkAdapterConfiguration").GetInstances())
				{
					if ((bool)managementBaseObject["IPEnabled"])
					{
						text = managementBaseObject["MacAddress"].ToString();
						break;
					}
				}
				result = text;
			}
			catch
			{
				result = string.Empty;
			}
			return result;
		}

		// Token: 0x06001439 RID: 5177 RVA: 0x00085B34 File Offset: 0x00083D34
		public static string smethod_6()
		{
			return Class281.smethod_0();
		}

		// Token: 0x0600143A RID: 5178 RVA: 0x00085B4C File Offset: 0x00083D4C
		public static void smethod_7()
		{
			for (int i = 1; i < Class281.int_0.Length; i++)
			{
				Class281.int_0[i] = i % 9;
			}
		}

		// Token: 0x0600143B RID: 5179 RVA: 0x00085B78 File Offset: 0x00083D78
		public static string smethod_8()
		{
			Class281.smethod_7();
			string text = Class281.smethod_6();
			for (int i = 1; i < Class281.char_0.Length; i++)
			{
				Class281.char_0[i] = Convert.ToChar(text.Substring(i - 1, 1));
			}
			for (int j = 1; j < Class281.int_1.Length; j++)
			{
				Class281.int_1[j] = Convert.ToInt32(Class281.char_0[j]) + Class281.int_0[Convert.ToInt32(Class281.char_0[j])];
			}
			string text2 = "";
			for (int k = 1; k < Class281.int_1.Length; k++)
			{
				if ((Class281.int_1[k] >= 48 && Class281.int_1[k] <= 57) || (Class281.int_1[k] >= 65 && Class281.int_1[k] <= 90) || (Class281.int_1[k] >= 97 && Class281.int_1[k] <= 122))
				{
					text2 += Convert.ToChar(Class281.int_1[k]).ToString();
				}
				else if (Class281.int_1[k] > 122)
				{
					text2 += Convert.ToChar(Class281.int_1[k] - 10).ToString();
				}
				else
				{
					text2 += Convert.ToChar(Class281.int_1[k] - 9).ToString();
				}
			}
			return text2;
		}

		// Token: 0x04000A79 RID: 2681
		public static int[] int_0 = new int[127];

		// Token: 0x04000A7A RID: 2682
		public static char[] char_0 = new char[25];

		// Token: 0x04000A7B RID: 2683
		public static int[] int_1 = new int[25];
	}
}
