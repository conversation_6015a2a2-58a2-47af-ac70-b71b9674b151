﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using TEx;

namespace ns9
{
	// Token: 0x02000088 RID: 136
	internal sealed class EventArgs3 : EventArgs
	{
		// Token: 0x06000483 RID: 1155 RVA: 0x00003F0D File Offset: 0x0000210D
		public EventArgs3(Indicator indicator_1, List<IndParam> list_1, ChartKLine chartKLine_1, ToolStripMenuItem toolStripMenuItem_1)
		{
			this.list_0 = list_1;
			this.indicator_0 = indicator_1;
			this.chartKLine_0 = chartKLine_1;
			this.toolStripMenuItem_0 = toolStripMenuItem_1;
		}

		// Token: 0x170000EA RID: 234
		// (get) Token: 0x06000484 RID: 1156 RVA: 0x00023F64 File Offset: 0x00022164
		public List<IndParam> IndParamList
		{
			get
			{
				return this.list_0;
			}
		}

		// Token: 0x170000EB RID: 235
		// (get) Token: 0x06000485 RID: 1157 RVA: 0x00023F7C File Offset: 0x0002217C
		public Indicator Ind
		{
			get
			{
				return this.indicator_0;
			}
		}

		// Token: 0x170000EC RID: 236
		// (get) Token: 0x06000486 RID: 1158 RVA: 0x00023F94 File Offset: 0x00022194
		public ChartKLine Chart
		{
			get
			{
				return this.chartKLine_0;
			}
		}

		// Token: 0x170000ED RID: 237
		// (get) Token: 0x06000487 RID: 1159 RVA: 0x00023FAC File Offset: 0x000221AC
		public ToolStripMenuItem OwnerItem
		{
			get
			{
				return this.toolStripMenuItem_0;
			}
		}

		// Token: 0x040001B8 RID: 440
		private readonly List<IndParam> list_0;

		// Token: 0x040001B9 RID: 441
		private readonly Indicator indicator_0;

		// Token: 0x040001BA RID: 442
		private readonly ChartKLine chartKLine_0;

		// Token: 0x040001BB RID: 443
		private readonly ToolStripMenuItem toolStripMenuItem_0;
	}
}
