﻿using System;
using System.CodeDom.Compiler;
using System.IO;
using System.Text;
using Microsoft.CSharp;

namespace ns11
{
	// Token: 0x02000307 RID: 775
	internal sealed class Class423
	{
		// Token: 0x0600216D RID: 8557 RVA: 0x000E4BC0 File Offset: 0x000E2DC0
		public static void smethod_0()
		{
			CodeDomProvider codeDomProvider = new CSharpCodeProvider();
			CompilerParameters compilerParameters = new CompilerParameters();
			compilerParameters.ReferencedAssemblies.Add("System.dll");
			string value = Directory.GetCurrentDirectory() + "\\IndicatorLib.dll";
			compilerParameters.ReferencedAssemblies.Add(value);
			compilerParameters.GenerateExecutable = false;
			compilerParameters.OutputAssembly = Directory.GetCurrentDirectory() + "\\UserDefined.dll";
			string text = Class423.smethod_1();
			CompilerResults compilerResults = codeDomProvider.CompileAssemblyFromSource(compilerParameters, new string[]
			{
				text
			});
			if (compilerResults.Errors.HasErrors)
			{
				throw new Exception(compilerResults.Errors.ToString());
			}
		}

		// Token: 0x0600216E RID: 8558 RVA: 0x000E4C5C File Offset: 0x000E2E5C
		private static string smethod_1()
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("using System;");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("using TEx.Inds;");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("namespace UserDefined");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("{");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("    public class MAes:SIndicatorBase");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("    {");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("     public  MAes(DataProvider dp):base(dp)");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("               {}");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("     public void Run(DataProvider dp)");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("         {");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("           if (!CheckIFRun(dp))");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("            return;");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("          GetIndDatas(Code());");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("           }");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("     private SIndData[] Code()");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("        {");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("          SIndData Ma60 = MA(C, 60); Ma60.Name = \"Ma60\";");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("          return new SIndData[] { Ma60};");
			stringBuilder.Append("         }");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("    }");
			stringBuilder.Append(Environment.NewLine);
			stringBuilder.Append("}");
			return stringBuilder.ToString();
		}
	}
}
