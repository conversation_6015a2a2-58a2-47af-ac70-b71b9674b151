﻿using System;
using System.Windows.Forms;
using TEx;

namespace ns32
{
	// Token: 0x020001F1 RID: 497
	internal sealed class Class278
	{
		// Token: 0x0600137C RID: 4988 RVA: 0x00002D25 File Offset: 0x00000F25
		public Class278()
		{
		}

		// Token: 0x0600137D RID: 4989 RVA: 0x00007DB6 File Offset: 0x00005FB6
		public Class278(int int_1, string string_2, string string_3, KeyModifiers keyModifiers_1, Keys keys_1) : this(int_1, string_2, string_3, keyModifiers_1, keys_1, false)
		{
		}

		// Token: 0x0600137E RID: 4990 RVA: 0x00007DC6 File Offset: 0x00005FC6
		public Class278(int int_1, string string_2, string string_3, KeyModifiers keyModifiers_1, Keys keys_1, bool bool_1)
		{
			this.Id = int_1;
			this.EnName = string_2;
			this.CnName = string_3;
			this.KeyModifier = keyModifiers_1;
			this.Key = keys_1;
			this.IfDispInQuickWnd = bool_1;
		}

		// Token: 0x0600137F RID: 4991 RVA: 0x00083970 File Offset: 0x00081B70
		private Keys method_0()
		{
			Keys result;
			if (this.keyModifiers_0 == KeyModifiers.None)
			{
				result = this.keys_0;
			}
			else
			{
				Keys keys = Keys.None;
				string text = this.keyModifiers_0.ToString();
				if (text.Contains("Ctrl"))
				{
					keys = Keys.Control;
				}
				if (text.Contains("Alt"))
				{
					if (keys == Keys.None)
					{
						keys = Keys.Alt;
					}
					else
					{
						keys |= Keys.Alt;
					}
				}
				if (text.Contains("Shift"))
				{
					if (keys == Keys.None)
					{
						keys = Keys.Shift;
					}
					else
					{
						keys |= Keys.Shift;
					}
				}
				if (text.Contains("WindowsKey"))
				{
					if (keys == Keys.None)
					{
						keys = Keys.LWin;
					}
					else
					{
						keys |= Keys.LWin;
					}
				}
				keys |= this.keys_0;
				result = keys;
			}
			return result;
		}

		// Token: 0x170002E8 RID: 744
		// (get) Token: 0x06001380 RID: 4992 RVA: 0x00083A20 File Offset: 0x00081C20
		// (set) Token: 0x06001381 RID: 4993 RVA: 0x00007DFD File Offset: 0x00005FFD
		public int Id
		{
			get
			{
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
			}
		}

		// Token: 0x170002E9 RID: 745
		// (get) Token: 0x06001382 RID: 4994 RVA: 0x00083A38 File Offset: 0x00081C38
		// (set) Token: 0x06001383 RID: 4995 RVA: 0x00007E08 File Offset: 0x00006008
		public string EnName
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x170002EA RID: 746
		// (get) Token: 0x06001384 RID: 4996 RVA: 0x00083A50 File Offset: 0x00081C50
		// (set) Token: 0x06001385 RID: 4997 RVA: 0x00007E13 File Offset: 0x00006013
		public string CnName
		{
			get
			{
				return this.string_1;
			}
			set
			{
				this.string_1 = value;
			}
		}

		// Token: 0x170002EB RID: 747
		// (get) Token: 0x06001386 RID: 4998 RVA: 0x00083A68 File Offset: 0x00081C68
		// (set) Token: 0x06001387 RID: 4999 RVA: 0x00007E1E File Offset: 0x0000601E
		public KeyModifiers KeyModifier
		{
			get
			{
				return this.keyModifiers_0;
			}
			set
			{
				this.keyModifiers_0 = value;
			}
		}

		// Token: 0x170002EC RID: 748
		// (get) Token: 0x06001388 RID: 5000 RVA: 0x00083A80 File Offset: 0x00081C80
		// (set) Token: 0x06001389 RID: 5001 RVA: 0x00007E29 File Offset: 0x00006029
		public Keys Key
		{
			get
			{
				return this.keys_0;
			}
			set
			{
				this.keys_0 = value;
			}
		}

		// Token: 0x170002ED RID: 749
		// (get) Token: 0x0600138A RID: 5002 RVA: 0x00083A98 File Offset: 0x00081C98
		// (set) Token: 0x0600138B RID: 5003 RVA: 0x00007E34 File Offset: 0x00006034
		public bool IfDispInQuickWnd
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x170002EE RID: 750
		// (get) Token: 0x0600138C RID: 5004 RVA: 0x00083AB0 File Offset: 0x00081CB0
		public Keys KeyCode
		{
			get
			{
				return this.method_0();
			}
		}

		// Token: 0x170002EF RID: 751
		// (get) Token: 0x0600138D RID: 5005 RVA: 0x00083AC8 File Offset: 0x00081CC8
		public string ShortCutKeyString
		{
			get
			{
				string str = "";
				if (this.KeyModifier != KeyModifiers.None)
				{
					str = str + this.KeyModifier.ToString() + "+";
				}
				return str + this.Key.ToString();
			}
		}

		// Token: 0x04000A2F RID: 2607
		private int int_0;

		// Token: 0x04000A30 RID: 2608
		private string string_0;

		// Token: 0x04000A31 RID: 2609
		private string string_1;

		// Token: 0x04000A32 RID: 2610
		private KeyModifiers keyModifiers_0;

		// Token: 0x04000A33 RID: 2611
		private Keys keys_0;

		// Token: 0x04000A34 RID: 2612
		private bool bool_0;
	}
}
