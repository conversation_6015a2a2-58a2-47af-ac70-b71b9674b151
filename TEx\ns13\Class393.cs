﻿using System;
using System.Drawing;
using ns28;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns13
{
	// Token: 0x020002F8 RID: 760
	internal sealed class Class393 : ShapeCurve
	{
		// Token: 0x0600212E RID: 8494 RVA: 0x000E360C File Offset: 0x000E180C
		public override void vmethod_6(string string_1, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			if (base.IndData.SingleData.Contains("TEXT"))
			{
				this.string_0 = (string)base.IndData.SingleData["TEXT"];
			}
			Ind_TextItem ind_TextItem = zedGraphControl_0.GraphPane.AddTextItem(base.IndData.Name, base.DataView, SymbolType.None, this.string_0);
			ind_TextItem.Symbol.Border.Color = color_0;
			ind_TextItem.Tag = string_1 + "_" + base.IndData.Name;
			this.curveItem_0 = ind_TextItem;
			base.method_3(string_1, ind_TextItem);
		}

		// Token: 0x0600212F RID: 8495 RVA: 0x0000D5BB File Offset: 0x0000B7BB
		public Class393(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}

		// Token: 0x06002130 RID: 8496 RVA: 0x000E3168 File Offset: 0x000E1368
		protected override PointPair vmethod_0(int int_0, DataArray dataArray_1)
		{
			DateTime dateTime = base.method_0(int_0);
			if (dataArray_1.Data.Length < int_0 + 1)
			{
				Class182.smethod_0(new Exception("数据长度溢出。"));
				int_0 = dataArray_1.Data.Length - 1;
			}
			double y = dataArray_1.Data[int_0];
			if (dataArray_1.OtherDataArrayList.Count != 1)
			{
				throw new Exception("文字字段包含数据不足，请检查。");
			}
			PointPair result;
			if ((int)dataArray_1.OtherDataArrayList[0].Data[int_0] == 1)
			{
				result = new PointPair(new XDate(dateTime), y, 1.0);
			}
			else
			{
				result = new PointPair(new XDate(dateTime), double.NaN, double.NaN);
			}
			return result;
		}

		// Token: 0x04001031 RID: 4145
		private string string_0 = "";
	}
}
