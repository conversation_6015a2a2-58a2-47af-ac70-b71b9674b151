﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Xml.Linq;
using ns30;
using ns5;
using TEx.Util;

namespace TEx.ImportTrans
{
	// Token: 0x02000371 RID: 881
	public sealed class CWrongNameStore : Class471, Interface4
	{
		// Token: 0x060024B4 RID: 9396 RVA: 0x0000E3C8 File Offset: 0x0000C5C8
		public CWrongNameStore()
		{
			this.string_0 = CfmmcRecImporter.smethod_34(CfmmcRecImporter.string_2);
		}

		// Token: 0x060024B5 RID: 9397 RVA: 0x000F5408 File Offset: 0x000F3608
		private void method_2(List<IStoreElement> list_0)
		{
			File.Exists(this.string_0);
			XDocument xdocument = new XDocument();
			XElement xelement = new XElement("Root");
			xdocument.Add(xelement);
			for (int i = 0; i < list_0.Count; i++)
			{
				CWrongUserName cwrongUserName_ = (CWrongUserName)list_0[i];
				XElement content = this.method_3(cwrongUserName_);
				xelement.Add(content);
			}
			xdocument.Save(this.string_0);
		}

		// Token: 0x060024B6 RID: 9398 RVA: 0x000F547C File Offset: 0x000F367C
		private XElement method_3(CWrongUserName cwrongUserName_0)
		{
			XElement xelement = new XElement("错误");
			xelement.SetAttributeValue("name", cwrongUserName_0.name);
			xelement.SetAttributeValue("passWord", cwrongUserName_0.password);
			xelement.SetAttributeValue("time", cwrongUserName_0.date);
			return xelement;
		}

		// Token: 0x060024B7 RID: 9399 RVA: 0x000F54E4 File Offset: 0x000F36E4
		public void imethod_2(IStoreElement istoreElement_0)
		{
			CWrongNameStore.Class474 @class = new CWrongNameStore.Class474();
			@class.istoreElement_0 = istoreElement_0;
			List<IStoreElement> list = base.imethod_3();
			list.RemoveAll(new Predicate<IStoreElement>(@class.method_0));
			this.method_2(list);
		}

		// Token: 0x060024B8 RID: 9400 RVA: 0x000F5524 File Offset: 0x000F3724
		public void imethod_1(IStoreElement istoreElement_0)
		{
			CWrongNameStore.Class475 @class = new CWrongNameStore.Class475();
			@class.istoreElement_0 = istoreElement_0;
			List<IStoreElement> list = base.imethod_3();
			int num = list.FindIndex(new Predicate<IStoreElement>(@class.method_0));
			if (num != -1)
			{
				list[num] = @class.istoreElement_0;
				this.method_2(list);
			}
		}

		// Token: 0x060024B9 RID: 9401 RVA: 0x000F5574 File Offset: 0x000F3774
		public void imethod_0(IStoreElement istoreElement_0)
		{
			CWrongNameStore.Class476 @class = new CWrongNameStore.Class476();
			@class.istoreElement_0 = istoreElement_0;
			List<IStoreElement> list = base.imethod_3();
			if (list != null && !list.Any(new Func<IStoreElement, bool>(@class.method_0)))
			{
				list.Add(@class.istoreElement_0);
				this.method_2(list);
			}
		}

		// Token: 0x060024BA RID: 9402 RVA: 0x000F55C4 File Offset: 0x000F37C4
		private CWrongUserName method_4(XElement xelement_0)
		{
			string value = xelement_0.Attribute("name").Value;
			string value2 = xelement_0.Attribute("passWord").Value;
			DateTime date = Convert.ToDateTime(xelement_0.Attribute("time").Value);
			return new CWrongUserName(value, value2, date);
		}

		// Token: 0x060024BB RID: 9403 RVA: 0x000F5624 File Offset: 0x000F3824
		private bool method_5(IStoreElement istoreElement_0)
		{
			return istoreElement_0 is CWrongUserName;
		}

		// Token: 0x060024BC RID: 9404 RVA: 0x000F5640 File Offset: 0x000F3840
		protected override List<IStoreElement> vmethod_0()
		{
			List<IStoreElement> result;
			if (Utility.FileExists(this.string_0))
			{
				List<CWrongUserName> list = new List<CWrongUserName>();
				foreach (XElement xelement_ in XDocument.Load(this.string_0).Element("Root").Elements("错误"))
				{
					CWrongUserName item = this.method_4(xelement_);
					list.Add(item);
				}
				result = list.Select(new Func<CWrongUserName, IStoreElement>(CWrongNameStore.<>c.<>9.method_0)).ToList<IStoreElement>();
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060024BD RID: 9405 RVA: 0x000F5704 File Offset: 0x000F3904
		public IStoreElement imethod_4(string string_1)
		{
			CWrongNameStore.Class477 @class = new CWrongNameStore.Class477();
			@class.string_0 = string_1;
			List<IStoreElement> list = base.imethod_3();
			IStoreElement result;
			if (list != null)
			{
				IStoreElement storeElement;
				try
				{
					storeElement = list.Single(new Func<IStoreElement, bool>(@class.method_0));
				}
				catch (Exception)
				{
					throw;
				}
				result = storeElement;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x02000372 RID: 882
		[CompilerGenerated]
		private sealed class Class474
		{
			// Token: 0x060024BF RID: 9407 RVA: 0x000F575C File Offset: 0x000F395C
			internal bool method_0(IStoreElement istoreElement_1)
			{
				return istoreElement_1.ID == this.istoreElement_0.ID;
			}

			// Token: 0x040011B1 RID: 4529
			public IStoreElement istoreElement_0;
		}

		// Token: 0x02000373 RID: 883
		[CompilerGenerated]
		private sealed class Class475
		{
			// Token: 0x060024C1 RID: 9409 RVA: 0x000F5784 File Offset: 0x000F3984
			internal bool method_0(IStoreElement istoreElement_1)
			{
				return istoreElement_1.ID == this.istoreElement_0.ID;
			}

			// Token: 0x040011B2 RID: 4530
			public IStoreElement istoreElement_0;
		}

		// Token: 0x02000374 RID: 884
		[CompilerGenerated]
		private sealed class Class476
		{
			// Token: 0x060024C3 RID: 9411 RVA: 0x000F57AC File Offset: 0x000F39AC
			internal bool method_0(IStoreElement istoreElement_1)
			{
				return istoreElement_1.ID == this.istoreElement_0.ID;
			}

			// Token: 0x040011B3 RID: 4531
			public IStoreElement istoreElement_0;
		}

		// Token: 0x02000376 RID: 886
		[CompilerGenerated]
		private sealed class Class477
		{
			// Token: 0x060024C8 RID: 9416 RVA: 0x000F57D4 File Offset: 0x000F39D4
			internal bool method_0(IStoreElement istoreElement_0)
			{
				return istoreElement_0.ID == this.string_0;
			}

			// Token: 0x040011B6 RID: 4534
			public string string_0;
		}
	}
}
