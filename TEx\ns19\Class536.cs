﻿using System;

namespace ns19
{
	// Token: 0x020003F8 RID: 1016
	internal sealed class Class536
	{
		// Token: 0x060027A0 RID: 10144 RVA: 0x0000F2EE File Offset: 0x0000D4EE
		public Class536(object object_1, bool bool_1) : this(object_1, (object_1 != null) ? object_1.GetType() : null, bool_1)
		{
		}

		// Token: 0x060027A1 RID: 10145 RVA: 0x0000F304 File Offset: 0x0000D504
		public Class536(object object_1, Type type_1, bool bool_1)
		{
			this.object_0 = object_1;
			this.type_0 = type_1;
			this.bool_0 = bool_1;
		}

		// Token: 0x170006CE RID: 1742
		// (get) Token: 0x060027A2 RID: 10146 RVA: 0x0000F321 File Offset: 0x0000D521
		public bool FirstLevel
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x060027A3 RID: 10147 RVA: 0x0000F329 File Offset: 0x0000D529
		public object method_0()
		{
			return this.object_0;
		}

		// Token: 0x060027A4 RID: 10148 RVA: 0x0000F331 File Offset: 0x0000D531
		public Type method_1()
		{
			return this.type_0;
		}

		// Token: 0x040013AF RID: 5039
		private readonly Type type_0;

		// Token: 0x040013B0 RID: 5040
		private readonly object object_0;

		// Token: 0x040013B1 RID: 5041
		private readonly bool bool_0;
	}
}
