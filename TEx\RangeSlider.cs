﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns26;
using ns4;

namespace TEx
{
	// Token: 0x0200021E RID: 542
	[ToolboxBitmap(typeof(RangeSlider), "RangeScale.bmp")]
	public sealed class RangeSlider : UserControl
	{
		// Token: 0x14000081 RID: 129
		// (add) Token: 0x06001651 RID: 5713 RVA: 0x00095C94 File Offset: 0x00093E94
		// (remove) Token: 0x06001652 RID: 5714 RVA: 0x00095CCC File Offset: 0x00093ECC
		public event Delegate14 RangeChanged
		{
			[CompilerGenerated]
			add
			{
				Delegate14 @delegate = this.delegate14_0;
				Delegate14 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate14 value2 = (Delegate14)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate14>(ref this.delegate14_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate14 @delegate = this.delegate14_0;
				Delegate14 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate14 value2 = (Delegate14)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate14>(ref this.delegate14_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06001653 RID: 5715 RVA: 0x00095D04 File Offset: 0x00093F04
		protected void method_0(DateTime dateTime_4, DateTime dateTime_5)
		{
			Delegate14 @delegate = this.delegate14_0;
			if (@delegate != null)
			{
				@delegate(this, new EventArgs11(dateTime_4, dateTime_5));
			}
		}

		// Token: 0x06001654 RID: 5716 RVA: 0x00095D2C File Offset: 0x00093F2C
		public RangeSlider()
		{
			this.InitializeComponent();
			if (this.string_1 != null)
			{
				this.image_0 = Image.FromFile(this.string_1);
			}
			if (this.string_2 != null)
			{
				this.image_1 = Image.FromFile(this.string_2);
			}
			this.list_1 = new List<DateTime>();
			this.list_1.Add(new DateTime(2006, 1, 1));
			this.list_1.Add(new DateTime(2011, 1, 1));
			this.list_1.Add(new DateTime(2016, 1, 1));
			this.dateTime_0 = this.list_1.First<DateTime>();
			this.dateTime_1 = this.list_1.Last<DateTime>();
			this.string_1 = null;
			this.string_2 = null;
			this.float_1 = 20f;
			this.float_2 = 10f;
			this.BackColor = Color.LightBlue;
			this.color_1 = Color.Magenta;
			this.color_2 = Color.Gray;
			this.color_3 = Color.Green;
			this.color_4 = Color.Gray;
			this.color_0 = Color.Purple;
			this.color_6 = Color.LightCyan;
			this.float_3 = 10f;
			this.color_5 = Color.Black;
			this.fontFamily_1 = FontFamily.GenericSerif;
			this.font_1 = new Font(this.fontFamily_1, this.float_3, FontStyle.Bold);
			this.uint_0 = 3U;
			this.uint_1 = 10U;
			this.uint_2 = 10U;
			this.fontFamily_0 = FontFamily.GenericSansSerif;
			this.float_0 = 8.25f;
			this.fontStyle_0 = FontStyle.Regular;
			this.font_0 = new Font(this.fontFamily_0, this.float_0, this.fontStyle_0);
			this.pointF_0 = new PointF[3];
			this.pointF_1 = new PointF[3];
			this.bool_2 = false;
			this.bool_3 = false;
			this.bool_4 = false;
			this.bool_5 = true;
		}

		// Token: 0x17000396 RID: 918
		// (get) Token: 0x06001656 RID: 5718 RVA: 0x00096130 File Offset: 0x00094330
		// (set) Token: 0x06001655 RID: 5717 RVA: 0x00095F28 File Offset: 0x00094128
		public List<DateTime> RangeValues
		{
			get
			{
				return this.list_1;
			}
			set
			{
				if (this.list_1 != value)
				{
					this.list_1 = value;
					this.int_0 = this.list_1.Count;
					this.dateTime_0 = this.dateTime_2;
					this.dateTime_1 = this.dateTime_3;
					List<DateTime> list = this.list_1.GroupBy(new Func<DateTime, int>(RangeSlider.<>c.<>9.method_0)).Select(new Func<IGrouping<int, DateTime>, DateTime>(RangeSlider.<>c.<>9.method_1)).ToList<DateTime>();
					if (list.Count > 1)
					{
						list.RemoveAt(list.Count - 1);
					}
					else
					{
						this.string_0 = "yyyy/MM";
						this.GapFromLeftMargin = 30U;
						this.GapFromRightMargin = 35U;
					}
					if (this.list_1.Count > 0)
					{
						list.Add(this.list_1.Last<DateTime>());
					}
					if (list.Count > 2)
					{
						this.list_0 = new List<DateTime>();
						double totalDays = (list.Last<DateTime>() - list.First<DateTime>()).TotalDays;
						double num = 0.075;
						this.list_0.Add(list.First<DateTime>());
						for (int i = 1; i < list.Count; i++)
						{
							DateTime dateTime = list[i];
							if ((dateTime - this.list_0.Last<DateTime>()).TotalDays / totalDays > num || i == list.Count - 1)
							{
								this.list_0.Add(dateTime);
							}
						}
						int count = this.list_0.Count;
						if ((this.list_0[count - 1] - this.list_0[count - 2]).TotalDays / totalDays < num)
						{
							this.list_0.RemoveAt(count - 2);
						}
					}
					else
					{
						this.list_0 = list;
					}
					this.method_2();
					this.Refresh();
					this.OnPaint(this.paintEventArgs_0);
					this.bool_5 = false;
				}
			}
		}

		// Token: 0x17000397 RID: 919
		// (get) Token: 0x06001658 RID: 5720 RVA: 0x00096148 File Offset: 0x00094348
		// (set) Token: 0x06001657 RID: 5719 RVA: 0x00008FB9 File Offset: 0x000071B9
		public Font LabelFont
		{
			get
			{
				return this.font_0;
			}
			set
			{
				if (this.font_0 != value)
				{
					this.font_0 = value;
					this.method_2();
					this.Refresh();
					this.OnPaint(this.paintEventArgs_0);
				}
			}
		}

		// Token: 0x17000398 RID: 920
		// (get) Token: 0x0600165A RID: 5722 RVA: 0x000961C4 File Offset: 0x000943C4
		// (set) Token: 0x06001659 RID: 5721 RVA: 0x00096160 File Offset: 0x00094360
		public string LeftThumbImagePath
		{
			get
			{
				return this.string_1;
			}
			set
			{
				try
				{
					if (this.string_1 != value)
					{
						this.string_1 = value;
						this.method_2();
						this.Refresh();
						this.OnPaint(this.paintEventArgs_0);
					}
				}
				catch
				{
					MessageBox.Show("Invalid Image Path.  Please Re-Enter", "Error!");
				}
			}
		}

		// Token: 0x17000399 RID: 921
		// (get) Token: 0x0600165C RID: 5724 RVA: 0x00096244 File Offset: 0x00094444
		// (set) Token: 0x0600165B RID: 5723 RVA: 0x000961DC File Offset: 0x000943DC
		public string RightThumbImagePath
		{
			get
			{
				return this.string_2;
			}
			set
			{
				try
				{
					if (this.string_2 != value)
					{
						this.string_2 = value;
						this.method_2();
						this.Refresh();
						this.OnPaint(this.paintEventArgs_0);
					}
				}
				catch
				{
					this.string_2 = null;
					MessageBox.Show("Invalid Image Path.  Please Re-Enter", "Error!");
				}
			}
		}

		// Token: 0x1700039A RID: 922
		// (get) Token: 0x0600165E RID: 5726 RVA: 0x0009625C File Offset: 0x0009445C
		// (set) Token: 0x0600165D RID: 5725 RVA: 0x00008FE5 File Offset: 0x000071E5
		public float HeightOfThumb
		{
			get
			{
				return this.float_1;
			}
			set
			{
				if (this.float_1 != value)
				{
					this.float_1 = value;
					this.method_2();
					this.Refresh();
					this.OnPaint(this.paintEventArgs_0);
				}
			}
		}

		// Token: 0x1700039B RID: 923
		// (get) Token: 0x06001660 RID: 5728 RVA: 0x00096274 File Offset: 0x00094474
		// (set) Token: 0x0600165F RID: 5727 RVA: 0x00009011 File Offset: 0x00007211
		public float WidthOfThumb
		{
			get
			{
				return this.float_2;
			}
			set
			{
				if (this.float_2 != value)
				{
					this.float_2 = value;
					this.method_2();
					this.Refresh();
					this.OnPaint(this.paintEventArgs_0);
				}
			}
		}

		// Token: 0x1700039C RID: 924
		// (get) Token: 0x06001662 RID: 5730 RVA: 0x0009628C File Offset: 0x0009448C
		// (set) Token: 0x06001661 RID: 5729 RVA: 0x0000903D File Offset: 0x0000723D
		public Color InFocusBarColor
		{
			get
			{
				return this.color_1;
			}
			set
			{
				if (this.color_1 != value)
				{
					this.color_1 = value;
					this.method_2();
					this.Refresh();
					this.OnPaint(this.paintEventArgs_0);
				}
			}
		}

		// Token: 0x1700039D RID: 925
		// (get) Token: 0x06001664 RID: 5732 RVA: 0x000962A4 File Offset: 0x000944A4
		// (set) Token: 0x06001663 RID: 5731 RVA: 0x0000906E File Offset: 0x0000726E
		public Color DisabledBarColor
		{
			get
			{
				return this.color_2;
			}
			set
			{
				if (this.color_2 != value)
				{
					this.color_2 = value;
					this.method_2();
					this.Refresh();
					this.OnPaint(this.paintEventArgs_0);
				}
			}
		}

		// Token: 0x1700039E RID: 926
		// (get) Token: 0x06001666 RID: 5734 RVA: 0x000962BC File Offset: 0x000944BC
		// (set) Token: 0x06001665 RID: 5733 RVA: 0x0000909F File Offset: 0x0000729F
		public Color ThumbColor
		{
			get
			{
				return this.color_0;
			}
			set
			{
				if (this.color_0 != value)
				{
					this.color_0 = value;
					this.method_2();
					this.Refresh();
					this.OnPaint(this.paintEventArgs_0);
				}
			}
		}

		// Token: 0x1700039F RID: 927
		// (get) Token: 0x06001668 RID: 5736 RVA: 0x000962D4 File Offset: 0x000944D4
		// (set) Token: 0x06001667 RID: 5735 RVA: 0x000090D0 File Offset: 0x000072D0
		public Color ThumbColorHighlighted
		{
			get
			{
				return this.color_6;
			}
			set
			{
				if (this.color_6 != value)
				{
					this.color_6 = value;
					this.method_2();
					this.Refresh();
					this.OnPaint(this.paintEventArgs_0);
				}
			}
		}

		// Token: 0x170003A0 RID: 928
		// (get) Token: 0x0600166A RID: 5738 RVA: 0x000962EC File Offset: 0x000944EC
		// (set) Token: 0x06001669 RID: 5737 RVA: 0x00009101 File Offset: 0x00007301
		public Color InFocusRangeLabelColor
		{
			get
			{
				return this.color_3;
			}
			set
			{
				if (this.color_3 != value)
				{
					this.color_3 = value;
					this.method_2();
					this.Refresh();
					this.OnPaint(this.paintEventArgs_0);
				}
			}
		}

		// Token: 0x170003A1 RID: 929
		// (get) Token: 0x0600166C RID: 5740 RVA: 0x00096304 File Offset: 0x00094504
		// (set) Token: 0x0600166B RID: 5739 RVA: 0x00009132 File Offset: 0x00007332
		public Color DisabledRangeLabelColor
		{
			get
			{
				return this.color_4;
			}
			set
			{
				if (this.color_4 != value)
				{
					this.color_4 = value;
					this.method_2();
					this.Refresh();
					this.OnPaint(this.paintEventArgs_0);
				}
			}
		}

		// Token: 0x170003A2 RID: 930
		// (get) Token: 0x0600166E RID: 5742 RVA: 0x0009631C File Offset: 0x0009451C
		// (set) Token: 0x0600166D RID: 5741 RVA: 0x00009163 File Offset: 0x00007363
		public uint MiddleBarWidth
		{
			get
			{
				return this.uint_0;
			}
			set
			{
				if (this.uint_0 != value)
				{
					this.uint_0 = value;
					this.method_2();
					this.Refresh();
					this.OnPaint(this.paintEventArgs_0);
				}
			}
		}

		// Token: 0x170003A3 RID: 931
		// (get) Token: 0x06001670 RID: 5744 RVA: 0x00096334 File Offset: 0x00094534
		// (set) Token: 0x0600166F RID: 5743 RVA: 0x0000918F File Offset: 0x0000738F
		public uint GapFromLeftMargin
		{
			get
			{
				return this.uint_1;
			}
			set
			{
				if (this.uint_1 != value)
				{
					this.uint_1 = value;
					this.method_2();
					this.Refresh();
					this.OnPaint(this.paintEventArgs_0);
				}
			}
		}

		// Token: 0x170003A4 RID: 932
		// (get) Token: 0x06001672 RID: 5746 RVA: 0x0009634C File Offset: 0x0009454C
		// (set) Token: 0x06001671 RID: 5745 RVA: 0x000091BB File Offset: 0x000073BB
		public uint GapFromRightMargin
		{
			get
			{
				return this.uint_2;
			}
			set
			{
				if (this.uint_2 != value)
				{
					this.uint_2 = value;
					this.method_2();
					this.Refresh();
					this.OnPaint(this.paintEventArgs_0);
				}
			}
		}

		// Token: 0x170003A5 RID: 933
		// (get) Token: 0x06001674 RID: 5748 RVA: 0x000963FC File Offset: 0x000945FC
		// (set) Token: 0x06001673 RID: 5747 RVA: 0x00096364 File Offset: 0x00094564
		public DateTime Range1
		{
			get
			{
				return this.dateTime_2;
			}
			set
			{
				if (this.dateTime_0 != value && value != DateTime.MinValue && value != DateTime.MaxValue)
				{
					this.dateTime_2 = value;
					if (this.list_1.Count > 0)
					{
						this.dateTime_0 = value;
						this.bool_5 = true;
						this.bool_6 = true;
						this.bool_0 = true;
						this.method_2();
						this.bool_6 = false;
						this.Refresh();
						this.OnPaint(this.paintEventArgs_0);
						this.bool_5 = false;
						this.bool_0 = false;
					}
				}
			}
		}

		// Token: 0x170003A6 RID: 934
		// (get) Token: 0x06001676 RID: 5750 RVA: 0x000964AC File Offset: 0x000946AC
		// (set) Token: 0x06001675 RID: 5749 RVA: 0x00096414 File Offset: 0x00094614
		public DateTime Range2
		{
			get
			{
				return this.dateTime_3;
			}
			set
			{
				if (this.dateTime_1 != value && value != DateTime.MinValue && value != DateTime.MaxValue)
				{
					this.dateTime_3 = value;
					if (this.list_1.Count > 0)
					{
						this.dateTime_1 = value;
						this.bool_5 = true;
						this.bool_7 = true;
						this.bool_1 = true;
						this.method_2();
						this.bool_7 = false;
						this.Refresh();
						this.OnPaint(this.paintEventArgs_0);
						this.bool_5 = false;
						this.bool_1 = false;
					}
				}
			}
		}

		// Token: 0x170003A7 RID: 935
		// (get) Token: 0x06001678 RID: 5752 RVA: 0x000964C4 File Offset: 0x000946C4
		// (set) Token: 0x06001677 RID: 5751 RVA: 0x000091E7 File Offset: 0x000073E7
		public Color OutputStringFontColor
		{
			get
			{
				return this.color_5;
			}
			set
			{
				this.color_5 = value;
				this.method_2();
				this.Refresh();
				this.OnPaint(this.paintEventArgs_0);
			}
		}

		// Token: 0x06001679 RID: 5753 RVA: 0x0000920A File Offset: 0x0000740A
		public void method_1(out string string_3, out string string_4)
		{
			string_3 = this.dateTime_0.ToString();
			string_4 = this.dateTime_1.ToString();
		}

		// Token: 0x0600167A RID: 5754 RVA: 0x000964DC File Offset: 0x000946DC
		private void method_2()
		{
			try
			{
				Graphics graphics = base.CreateGraphics();
				this.int_0 = this.list_1.Count;
				if (this.string_1 != null)
				{
					this.image_0 = Image.FromFile(this.string_1);
				}
				if (this.string_2 != null)
				{
					this.image_1 = Image.FromFile(this.string_2);
				}
				RectangleF visibleClipBounds = graphics.VisibleClipBounds;
				this.float_6 = this.uint_1;
				this.float_7 = visibleClipBounds.Height / 2f;
				this.float_8 = visibleClipBounds.Width - this.uint_2;
				this.float_9 = this.float_7;
				this.float_10 = visibleClipBounds.Width - (this.uint_2 + this.uint_1);
				this.float_11 = this.float_10 / (float)(this.int_0 - 1);
				bool flag = false;
				for (int i = 0; i < this.int_0; i++)
				{
					if (this.bool_5 && !this.bool_7 && this.dateTime_0.Date.Equals(this.list_1[i].Date))
					{
						this.float_4 = this.float_6 + this.float_11 * (float)i;
					}
					if (this.bool_5 && !this.bool_6 && this.dateTime_1.Date.Equals(this.list_1[i].Date))
					{
						this.float_5 = this.float_6 + this.float_11 * (float)i;
						flag = true;
					}
				}
				if (!flag && this.int_0 > 0)
				{
					this.float_5 = this.float_6 + this.float_11 * (float)(this.int_0 - 1);
				}
				this.pointF_0[0].X = this.float_4 - this.float_2;
				this.pointF_0[0].Y = this.float_7 - 3f;
				this.pointF_0[1].X = this.float_4 - this.float_2;
				this.pointF_0[1].Y = this.float_7 - 3f - this.float_1;
				this.pointF_0[2].X = this.float_4;
				this.pointF_0[2].Y = this.float_7 - 3f - this.float_1 / 2f;
				this.pointF_1[0].X = this.float_5 + this.float_2;
				this.pointF_1[0].Y = this.float_9 - 3f;
				this.pointF_1[1].X = this.float_5 + this.float_2;
				this.pointF_1[1].Y = this.float_9 - 3f - this.float_1;
				this.pointF_1[2].X = this.float_5;
				this.pointF_1[2].Y = this.float_9 - 3f - this.float_1 / 2f;
			}
			catch
			{
				throw;
			}
		}

		// Token: 0x0600167B RID: 5755 RVA: 0x00096834 File Offset: 0x00094A34
		private void method_3(Graphics graphics_0, PaintEventArgs paintEventArgs_1)
		{
			Brush brush;
			if (this.bool_2)
			{
				brush = new SolidBrush(this.BackColor);
				if (this.string_1 != null)
				{
					graphics_0.FillRectangle(brush, this.pointF_0[0].X, this.pointF_0[1].Y, this.float_2, this.float_1);
				}
				else
				{
					graphics_0.FillClosedCurve(brush, this.pointF_0, FillMode.Winding, 0f);
				}
			}
			if (this.bool_3)
			{
				brush = new SolidBrush(this.BackColor);
				if (this.string_2 != null)
				{
					graphics_0.FillRectangle(brush, this.pointF_1[2].X, this.pointF_1[1].Y, this.float_2, this.float_1);
				}
				else
				{
					graphics_0.FillClosedCurve(brush, this.pointF_1, FillMode.Winding, 0f);
				}
			}
			brush = new SolidBrush(this.color_3);
			Pen pen = new Pen(this.color_3, this.uint_0);
			this.pointF_0[0].X = this.float_4 - this.float_2;
			this.pointF_0[1].X = this.float_4 - this.float_2;
			this.pointF_0[2].X = this.float_4;
			this.pointF_1[0].X = this.float_5 + this.float_2;
			this.pointF_1[1].X = this.float_5 + this.float_2;
			this.pointF_1[2].X = this.float_5;
			pen = new Pen(this.color_2, this.uint_0);
			graphics_0.DrawLine(pen, this.float_6, this.pointF_0[2].Y, this.float_4, this.pointF_0[2].Y);
			float num = this.font_0.SizeInPoints * 0.8f;
			graphics_0.DrawLine(pen, this.float_6, this.pointF_0[2].Y - num, this.float_6, this.pointF_0[2].Y + num);
			graphics_0.DrawLine(pen, this.float_8, this.pointF_0[2].Y - num, this.float_8, this.pointF_0[2].Y + num);
			pen = new Pen(this.color_1, this.uint_0);
			graphics_0.DrawLine(pen, this.pointF_0[2].X, this.pointF_0[2].Y, this.float_5, this.pointF_0[2].Y);
			pen = new Pen(this.color_2, this.uint_0);
			graphics_0.DrawLine(pen, this.float_5, this.pointF_1[2].Y, this.float_8, this.pointF_1[2].Y);
			if (this.string_1 != null)
			{
				graphics_0.DrawImage(this.image_0, this.pointF_0[0].X, this.pointF_0[1].Y, this.float_2, this.float_1);
			}
			else
			{
				Color color = this.color_0;
				if (this.bool_8 || this.bool_6)
				{
					color = this.color_6;
				}
				brush = new SolidBrush(color);
				graphics_0.FillClosedCurve(brush, this.pointF_0, FillMode.Winding, 0f);
			}
			if (this.string_2 != null)
			{
				graphics_0.DrawImage(this.image_1, this.pointF_1[2].X, this.pointF_1[1].Y, this.float_2, this.float_1);
			}
			else
			{
				Color color2 = this.color_0;
				if (this.bool_9 || this.bool_7)
				{
					color2 = this.color_6;
				}
				brush = new SolidBrush(color2);
				graphics_0.FillClosedCurve(brush, this.pointF_1, FillMode.Winding, 0f);
			}
		}

		// Token: 0x0600167C RID: 5756 RVA: 0x00096C3C File Offset: 0x00094E3C
		protected void OnPaint(PaintEventArgs e)
		{
			try
			{
				Graphics graphics = base.CreateGraphics();
				this.paintEventArgs_0 = e;
				Brush brush = new SolidBrush(this.color_4);
				DateTime? dateTime = null;
				DateTime? dateTime2 = null;
				for (int i = 0; i < this.int_0; i++)
				{
					int length = this.string_0.Length;
					float num = (float)length + this.float_11 * (float)i;
					float num2 = num + (float)length * this.font_0.SizeInPoints / 2f;
					float num3 = num + this.uint_1 - (float)(length - 1) * this.font_0.SizeInPoints / 2f;
					DateTime dateTime3 = this.list_1[i];
					if (this.bool_5 && !this.bool_7 && num2 >= this.float_4 && dateTime == null)
					{
						brush = new SolidBrush(this.color_3);
						dateTime = new DateTime?(dateTime3);
					}
					if (this.bool_5 && !this.bool_6)
					{
						if (num3 > this.float_5)
						{
							brush = new SolidBrush(this.color_4);
						}
						else
						{
							dateTime2 = new DateTime?(this.list_1[i]);
						}
					}
					if (this.list_0 != null && this.list_0.Contains(dateTime3))
					{
						float num4 = num;
						if (i == this.int_0 - 1)
						{
							num4 -= 20f;
						}
						graphics.DrawString(dateTime3.ToString(this.string_0), this.font_0, brush, num4, this.float_7);
					}
				}
				bool flag = (this.bool_5 && !this.bool_7 && !this.bool_0 && dateTime != null) || (!this.bool_0 && dateTime != null && !this.dateTime_0.Equals(dateTime.Value));
				bool flag2 = (this.bool_5 && !this.bool_6 && !this.bool_1 && dateTime2 != null) || (!this.bool_1 && dateTime2 != null && !this.dateTime_1.Equals(dateTime2.Value));
				bool flag3;
				if ((flag3 = (flag || flag2)) || (!this.bool_2 && !this.bool_3))
				{
					if (flag)
					{
						this.dateTime_0 = dateTime.Value;
					}
					if (flag2)
					{
						this.dateTime_1 = dateTime2.Value;
					}
					if (flag3)
					{
						this.method_0(this.dateTime_0, this.dateTime_1);
					}
				}
				if (this.bool_5 && this.bool_4)
				{
					float num5 = this.float_4;
					float num6 = this.float_5;
					int tickCount = Environment.TickCount;
					this.float_4 = this.float_12;
					this.float_5 = this.float_13;
					while (this.float_4 <= num5 || this.float_5 >= num6)
					{
						this.bool_2 = true;
						this.bool_3 = true;
						if (this.float_4 > num5)
						{
							this.float_4 = num5;
						}
						if (this.float_5 < num6)
						{
							this.float_5 = num6;
						}
						this.method_3(graphics, e);
						if (Environment.TickCount - tickCount >= 1000)
						{
							IL_342:
							this.float_4 = num5;
							this.float_5 = num6;
							this.bool_2 = true;
							this.bool_3 = true;
							this.method_3(graphics, e);
							this.bool_4 = false;
							this.bool_2 = false;
							this.bool_3 = false;
							this.method_3(graphics, e);
							goto IL_392;
						}
						Thread.Sleep(1);
						this.float_4 += 3f;
						this.float_5 -= 3f;
					}
					goto IL_342;
				}
				this.method_3(graphics, e);
				IL_392:
				base.OnPaint(e);
			}
			catch
			{
				throw;
			}
		}

		// Token: 0x0600167D RID: 5757 RVA: 0x00009228 File Offset: 0x00007428
		protected void Dispose(bool disposing)
		{
			if (disposing && this.container_0 != null)
			{
				this.container_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600167E RID: 5758 RVA: 0x00097008 File Offset: 0x00095208
		private void InitializeComponent()
		{
			base.Name = "RangeSelectorControl";
			base.Size = new Size(360, 80);
			base.Resize += this.RangeSlider_Resize;
			base.Load += this.RangeSlider_Load;
			base.MouseUp += this.RangeSlider_MouseUp;
			base.MouseMove += this.RangeSlider_MouseMove;
			base.MouseDown += this.RangeSlider_MouseDown;
			base.MouseLeave += this.RangeSlider_MouseLeave;
		}

		// Token: 0x0600167F RID: 5759 RVA: 0x00009249 File Offset: 0x00007449
		private void RangeSlider_Load(object sender, EventArgs e)
		{
			this.method_2();
		}

		// Token: 0x06001680 RID: 5760 RVA: 0x000970A0 File Offset: 0x000952A0
		private void RangeSlider_MouseUp(object sender, MouseEventArgs e)
		{
			this.bool_2 = false;
			this.bool_3 = false;
			this.float_12 = this.float_4;
			this.float_13 = this.float_5;
			this.method_2();
			this.bool_4 = true;
			this.Refresh();
			this.bool_6 = false;
			this.bool_7 = false;
		}

		// Token: 0x06001681 RID: 5761 RVA: 0x00009253 File Offset: 0x00007453
		private void RangeSlider_MouseDown(object sender, MouseEventArgs e)
		{
			if (this.method_4(e))
			{
				this.bool_2 = true;
			}
			else if (this.method_5(e))
			{
				this.bool_3 = true;
			}
			this.bool_4 = false;
		}

		// Token: 0x06001682 RID: 5762 RVA: 0x000970F8 File Offset: 0x000952F8
		private void RangeSlider_MouseMove(object sender, MouseEventArgs e)
		{
			this.IsMouseOnThumb1 = this.method_4(e);
			this.IsMouseOnThumb2 = this.method_5(e);
			if (this.IsMouseOnThumb1 || this.IsMouseOnThumb2)
			{
				this.bool_5 = true;
			}
			if (this.bool_2 && e.Button == MouseButtons.Left && (float)e.X >= this.float_6)
			{
				this.bool_6 = true;
				this.Cursor = Cursors.VSplit;
				this.bool_5 = true;
				if (this.dateTime_0.Date == this.dateTime_1.Date)
				{
					if ((float)e.X < this.float_4)
					{
						this.float_4 = (float)e.X;
						this.OnPaint(this.paintEventArgs_0);
					}
				}
				else if (this.float_5 > (float)e.X)
				{
					this.float_4 = (float)e.X;
					this.OnPaint(this.paintEventArgs_0);
				}
				else
				{
					this.bool_2 = false;
				}
				this.bool_5 = false;
			}
			else if (this.bool_3 && e.Button == MouseButtons.Left && (float)e.X <= this.float_8)
			{
				this.bool_7 = true;
				this.Cursor = Cursors.VSplit;
				this.bool_5 = true;
				if (this.dateTime_0.Date == this.dateTime_1.Date)
				{
					if ((float)e.X > this.float_5)
					{
						this.float_5 = (float)e.X;
						this.OnPaint(this.paintEventArgs_0);
					}
				}
				else if (this.float_4 < (float)e.X)
				{
					this.float_5 = (float)e.X;
					this.OnPaint(this.paintEventArgs_0);
				}
				else
				{
					this.bool_3 = false;
				}
				this.bool_5 = false;
			}
			else
			{
				this.Cursor = Cursors.Default;
				this.bool_6 = false;
				this.bool_7 = false;
				this.bool_5 = false;
			}
		}

		// Token: 0x06001683 RID: 5763 RVA: 0x00009280 File Offset: 0x00007480
		private void RangeSlider_MouseLeave(object sender, EventArgs e)
		{
			this.bool_6 = false;
			this.bool_7 = false;
			this.IsMouseOnThumb1 = false;
			this.IsMouseOnThumb2 = false;
			this.Refresh();
		}

		// Token: 0x170003A8 RID: 936
		// (get) Token: 0x06001685 RID: 5765 RVA: 0x000972EC File Offset: 0x000954EC
		// (set) Token: 0x06001684 RID: 5764 RVA: 0x000092A6 File Offset: 0x000074A6
		private bool IsMouseOnThumb1
		{
			get
			{
				return this.bool_8;
			}
			set
			{
				if (this.bool_8 != value)
				{
					this.bool_8 = value;
					this.Refresh();
					this.OnPaint(this.paintEventArgs_0);
				}
			}
		}

		// Token: 0x06001686 RID: 5766 RVA: 0x00097304 File Offset: 0x00095504
		private bool method_4(MouseEventArgs mouseEventArgs_0)
		{
			bool result;
			if ((float)mouseEventArgs_0.X >= this.pointF_0[0].X && (float)mouseEventArgs_0.X <= this.pointF_0[2].X && (float)mouseEventArgs_0.Y >= this.pointF_0[1].Y && (float)mouseEventArgs_0.Y <= this.pointF_0[0].Y)
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x170003A9 RID: 937
		// (get) Token: 0x06001688 RID: 5768 RVA: 0x00097384 File Offset: 0x00095584
		// (set) Token: 0x06001687 RID: 5767 RVA: 0x000092CC File Offset: 0x000074CC
		private bool IsMouseOnThumb2
		{
			get
			{
				return this.bool_9;
			}
			set
			{
				if (this.bool_9 != value)
				{
					this.bool_9 = value;
					this.Refresh();
					this.OnPaint(this.paintEventArgs_0);
				}
			}
		}

		// Token: 0x170003AA RID: 938
		// (get) Token: 0x06001689 RID: 5769 RVA: 0x0009739C File Offset: 0x0009559C
		public bool IsDraggingThumb
		{
			get
			{
				bool result;
				if (!this.bool_6)
				{
					result = this.bool_7;
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x0600168A RID: 5770 RVA: 0x000973C0 File Offset: 0x000955C0
		private bool method_5(MouseEventArgs mouseEventArgs_0)
		{
			bool result;
			if ((float)mouseEventArgs_0.X >= this.pointF_1[2].X && (float)mouseEventArgs_0.X <= this.pointF_1[0].X && (float)mouseEventArgs_0.Y >= this.pointF_1[1].Y && (float)mouseEventArgs_0.Y <= this.pointF_1[0].Y)
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600168B RID: 5771 RVA: 0x000092F2 File Offset: 0x000074F2
		private void RangeSlider_Resize(object sender, EventArgs e)
		{
			this.method_2();
			this.Refresh();
			this.OnPaint(this.paintEventArgs_0);
		}

		// Token: 0x04000B45 RID: 2885
		[CompilerGenerated]
		private Delegate14 delegate14_0;

		// Token: 0x04000B46 RID: 2886
		private Container container_0;

		// Token: 0x04000B47 RID: 2887
		private string string_0 = "yyyy";

		// Token: 0x04000B48 RID: 2888
		private List<DateTime> list_0;

		// Token: 0x04000B49 RID: 2889
		private List<DateTime> list_1;

		// Token: 0x04000B4A RID: 2890
		private Font font_0;

		// Token: 0x04000B4B RID: 2891
		private FontStyle fontStyle_0;

		// Token: 0x04000B4C RID: 2892
		private float float_0;

		// Token: 0x04000B4D RID: 2893
		private FontFamily fontFamily_0;

		// Token: 0x04000B4E RID: 2894
		private string string_1;

		// Token: 0x04000B4F RID: 2895
		private string string_2;

		// Token: 0x04000B50 RID: 2896
		private float float_1;

		// Token: 0x04000B51 RID: 2897
		private float float_2;

		// Token: 0x04000B52 RID: 2898
		private Color color_0;

		// Token: 0x04000B53 RID: 2899
		private Color color_1;

		// Token: 0x04000B54 RID: 2900
		private Color color_2;

		// Token: 0x04000B55 RID: 2901
		private Color color_3;

		// Token: 0x04000B56 RID: 2902
		private Color color_4;

		// Token: 0x04000B57 RID: 2903
		private uint uint_0;

		// Token: 0x04000B58 RID: 2904
		private uint uint_1;

		// Token: 0x04000B59 RID: 2905
		private uint uint_2;

		// Token: 0x04000B5A RID: 2906
		private DateTime dateTime_0;

		// Token: 0x04000B5B RID: 2907
		private DateTime dateTime_1;

		// Token: 0x04000B5C RID: 2908
		private DateTime dateTime_2;

		// Token: 0x04000B5D RID: 2909
		private DateTime dateTime_3;

		// Token: 0x04000B5E RID: 2910
		private Font font_1;

		// Token: 0x04000B5F RID: 2911
		private float float_3;

		// Token: 0x04000B60 RID: 2912
		private Color color_5;

		// Token: 0x04000B61 RID: 2913
		private FontFamily fontFamily_1;

		// Token: 0x04000B62 RID: 2914
		private Color color_6;

		// Token: 0x04000B63 RID: 2915
		private bool bool_0;

		// Token: 0x04000B64 RID: 2916
		private bool bool_1;

		// Token: 0x04000B65 RID: 2917
		private Image image_0;

		// Token: 0x04000B66 RID: 2918
		private Image image_1;

		// Token: 0x04000B67 RID: 2919
		private bool bool_2;

		// Token: 0x04000B68 RID: 2920
		private bool bool_3;

		// Token: 0x04000B69 RID: 2921
		private float float_4;

		// Token: 0x04000B6A RID: 2922
		private float float_5;

		// Token: 0x04000B6B RID: 2923
		private float float_6;

		// Token: 0x04000B6C RID: 2924
		private float float_7;

		// Token: 0x04000B6D RID: 2925
		private float float_8;

		// Token: 0x04000B6E RID: 2926
		private float float_9;

		// Token: 0x04000B6F RID: 2927
		private float float_10;

		// Token: 0x04000B70 RID: 2928
		private float float_11;

		// Token: 0x04000B71 RID: 2929
		private PaintEventArgs paintEventArgs_0;

		// Token: 0x04000B72 RID: 2930
		private int int_0;

		// Token: 0x04000B73 RID: 2931
		private PointF[] pointF_0;

		// Token: 0x04000B74 RID: 2932
		private PointF[] pointF_1;

		// Token: 0x04000B75 RID: 2933
		private bool bool_4;

		// Token: 0x04000B76 RID: 2934
		private float float_12;

		// Token: 0x04000B77 RID: 2935
		private float float_13;

		// Token: 0x04000B78 RID: 2936
		private bool bool_5;

		// Token: 0x04000B79 RID: 2937
		private bool bool_6;

		// Token: 0x04000B7A RID: 2938
		private bool bool_7;

		// Token: 0x04000B7B RID: 2939
		private bool bool_8;

		// Token: 0x04000B7C RID: 2940
		private bool bool_9;
	}
}
