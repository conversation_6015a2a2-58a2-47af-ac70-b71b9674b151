﻿using System;
using System.Drawing;
using ns5;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns12
{
	// Token: 0x020002EB RID: 747
	internal sealed class Class382 : Class381
	{
		// Token: 0x0600210D RID: 8461 RVA: 0x000E29D0 File Offset: 0x000E0BD0
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			Ind_GoldenLine lineItem_ = zedGraphControl_0.GraphPane.AddGoldenLine(base.IndData.Name, base.DataView, color_0, SymbolType.None, this.double_0);
			base.method_3(string_0, lineItem_);
		}

		// Token: 0x0600210E RID: 8462 RVA: 0x0000D560 File Offset: 0x0000B760
		public Class382(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}
	}
}
