﻿using System;
using System.Xml.Linq;

namespace TEx
{
	// Token: 0x02000032 RID: 50
	[Serializable]
	internal sealed class TransTabCtrlParam
	{
		// Token: 0x06000150 RID: 336 RVA: 0x000169C8 File Offset: 0x00014BC8
		public XElement GetXElement()
		{
			XElement xelement = new XElement("TransTabCtrlParam");
			xelement.SetAttributeValue("HasParentSpContainer", Convert.ToInt32(this.HasParentSpContainer));
			xelement.SetAttributeValue("IsInParentSpContainerPanel1", Convert.ToInt32(this.IsInParentSpContainerPanel1));
			xelement.SetAttributeValue("ParentSpContainerTag", this.ParentSpContainerTag);
			return xelement;
		}

		// Token: 0x17000056 RID: 86
		// (get) Token: 0x06000151 RID: 337 RVA: 0x00016A40 File Offset: 0x00014C40
		// (set) Token: 0x06000152 RID: 338 RVA: 0x00002F5D File Offset: 0x0000115D
		public bool HasParentSpContainer
		{
			get
			{
				return this._HasParentSpContainer;
			}
			set
			{
				this._HasParentSpContainer = value;
			}
		}

		// Token: 0x17000057 RID: 87
		// (get) Token: 0x06000153 RID: 339 RVA: 0x00016A58 File Offset: 0x00014C58
		// (set) Token: 0x06000154 RID: 340 RVA: 0x00002F68 File Offset: 0x00001168
		public string ParentSpContainerTag
		{
			get
			{
				return this._ParentSpContainerTag;
			}
			set
			{
				this._ParentSpContainerTag = value;
			}
		}

		// Token: 0x17000058 RID: 88
		// (get) Token: 0x06000155 RID: 341 RVA: 0x00016A70 File Offset: 0x00014C70
		// (set) Token: 0x06000156 RID: 342 RVA: 0x00002F73 File Offset: 0x00001173
		public bool IsInParentSpContainerPanel1
		{
			get
			{
				return this._IsInParentSpContainerPanel1;
			}
			set
			{
				this._IsInParentSpContainerPanel1 = value;
			}
		}

		// Token: 0x04000082 RID: 130
		private bool _HasParentSpContainer;

		// Token: 0x04000083 RID: 131
		private string _ParentSpContainerTag = "";

		// Token: 0x04000084 RID: 132
		private bool _IsInParentSpContainerPanel1 = true;
	}
}
