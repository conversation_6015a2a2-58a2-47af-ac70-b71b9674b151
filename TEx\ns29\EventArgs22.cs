﻿using System;
using ns26;
using TEx;
using TEx.Trading;

namespace ns29
{
	// Token: 0x02000279 RID: 633
	internal sealed class EventArgs22 : EventArgs
	{
		// Token: 0x06001B85 RID: 7045 RVA: 0x0000B59A File Offset: 0x0000979A
		public EventArgs22(Enum17 enum17_1, TranStock tranStock_1, StSplit stSplit_1)
		{
			this.enum17_0 = enum17_1;
			this.tranStock_0 = tranStock_1;
			this.stSplit_0 = stSplit_1;
		}

		// Token: 0x1700047F RID: 1151
		// (get) Token: 0x06001B86 RID: 7046 RVA: 0x000B9BFC File Offset: 0x000B7DFC
		public Enum17 TransType
		{
			get
			{
				return this.enum17_0;
			}
		}

		// Token: 0x17000480 RID: 1152
		// (get) Token: 0x06001B87 RID: 7047 RVA: 0x000B9C14 File Offset: 0x000B7E14
		public TranStock TranStock
		{
			get
			{
				return this.tranStock_0;
			}
		}

		// Token: 0x17000481 RID: 1153
		// (get) Token: 0x06001B88 RID: 7048 RVA: 0x000B9C2C File Offset: 0x000B7E2C
		public StSplit StSplit
		{
			get
			{
				return this.stSplit_0;
			}
		}

		// Token: 0x17000482 RID: 1154
		// (get) Token: 0x06001B89 RID: 7049 RVA: 0x000B9C44 File Offset: 0x000B7E44
		// (set) Token: 0x06001B8A RID: 7050 RVA: 0x0000B5B9 File Offset: 0x000097B9
		public bool Cancel
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x17000483 RID: 1155
		// (get) Token: 0x06001B8B RID: 7051 RVA: 0x000B9C5C File Offset: 0x000B7E5C
		// (set) Token: 0x06001B8C RID: 7052 RVA: 0x0000B5C4 File Offset: 0x000097C4
		public long? RationedShareBuyUnits
		{
			get
			{
				return this.nullable_0;
			}
			set
			{
				this.nullable_0 = value;
			}
		}

		// Token: 0x04000D9B RID: 3483
		private Enum17 enum17_0;

		// Token: 0x04000D9C RID: 3484
		private TranStock tranStock_0;

		// Token: 0x04000D9D RID: 3485
		private StSplit stSplit_0;

		// Token: 0x04000D9E RID: 3486
		private bool bool_0;

		// Token: 0x04000D9F RID: 3487
		private long? nullable_0;
	}
}
