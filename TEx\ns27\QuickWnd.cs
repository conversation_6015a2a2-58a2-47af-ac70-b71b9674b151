﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns30;
using ns9;
using TEx;

namespace ns27
{
	// Token: 0x020001F3 RID: 499
	internal sealed partial class QuickWnd : Form
	{
		// Token: 0x1400007C RID: 124
		// (add) Token: 0x06001392 RID: 5010 RVA: 0x00083B24 File Offset: 0x00081D24
		// (remove) Token: 0x06001393 RID: 5011 RVA: 0x00083B5C File Offset: 0x00081D5C
		public event Delegate13 QuickWndItemSelected
		{
			[CompilerGenerated]
			add
			{
				Delegate13 @delegate = this.delegate13_0;
				Delegate13 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate13 value2 = (Delegate13)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate13>(ref this.delegate13_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate13 @delegate = this.delegate13_0;
				Delegate13 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate13 value2 = (Delegate13)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate13>(ref this.delegate13_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06001394 RID: 5012 RVA: 0x00083B94 File Offset: 0x00081D94
		protected void method_0(EventArgs10 eventArgs10_0)
		{
			Delegate13 @delegate = this.delegate13_0;
			if (@delegate != null)
			{
				@delegate(this, eventArgs10_0);
			}
		}

		// Token: 0x06001395 RID: 5013 RVA: 0x00007E3F File Offset: 0x0000603F
		public QuickWnd()
		{
			this.InitializeComponent();
			base.Load += this.QuickWnd_Load;
			base.Shown += this.QuickWnd_Shown;
		}

		// Token: 0x06001396 RID: 5014 RVA: 0x00007E73 File Offset: 0x00006073
		public QuickWnd(List<QuickWndItem> list_1, string string_1) : this(list_1, null, string_1)
		{
		}

		// Token: 0x06001397 RID: 5015 RVA: 0x00083BB8 File Offset: 0x00081DB8
		public QuickWnd(List<QuickWndItem> list_1, IEnumerable<QuickWndItem> ienumerable_0, string string_1) : this()
		{
			this.method_3(list_1, ienumerable_0, string_1);
			this.dataGridView.ColumnHeadersVisible = false;
			this.dataGridView.RowHeadersVisible = false;
			this.dataGridView.CellBorderStyle = DataGridViewCellBorderStyle.None;
			this.dataGridView.Columns[0].Width = (int)Convert.ToInt16(Math.Ceiling(this.dataGridView.Width * 0.35m));
			this.dataGridView.Columns[1].Width = this.dataGridView.Width - this.dataGridView.Columns[0].Width;
			this.dataGridView.Columns[2].Visible = false;
			this.dataGridView.Columns[3].Visible = false;
			this.dataGridView.RowsDefaultCellStyle.Alignment = DataGridViewContentAlignment.BottomLeft;
			this.dataGridView.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
			this.dataGridView.AllowUserToAddRows = false;
			this.dataGridView.AllowUserToDeleteRows = false;
			this.dataGridView.MultiSelect = false;
			this.dataGridView.ReadOnly = true;
			this.dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
			this.dataGridView.DefaultCellStyle.Font = new Font("SimSun", 8.6f, FontStyle.Regular);
			this.dataGridView.Rows[0].Selected = true;
			this.dataGridView.ScrollBars = ScrollBars.Vertical;
			this.dataGridView.DoubleClick += this.dataGridView_DoubleClick;
			this.textBox.TextChanged += this.textBox_TextChanged;
		}

		// Token: 0x06001398 RID: 5016 RVA: 0x000041AE File Offset: 0x000023AE
		private void QuickWnd_Load(object sender, EventArgs e)
		{
		}

		// Token: 0x06001399 RID: 5017 RVA: 0x00007E7E File Offset: 0x0000607E
		private void QuickWnd_Shown(object sender, EventArgs e)
		{
			this.textBox.Focus();
			this.textBox.ImeMode = ImeMode.Disable;
			this.method_1();
		}

		// Token: 0x0600139A RID: 5018 RVA: 0x00007EA0 File Offset: 0x000060A0
		private void method_1()
		{
			this.textBox.SelectionStart = this.textBox.Text.Length;
			this.textBox.SelectionLength = 0;
		}

		// Token: 0x0600139B RID: 5019 RVA: 0x00083D70 File Offset: 0x00081F70
		protected bool ProcessCmdKey(ref Message msg, Keys keyData)
		{
			if (keyData == Keys.Escape)
			{
				base.Close();
			}
			else if (keyData == Keys.Return)
			{
				this.method_4();
			}
			else if (keyData == Keys.Up)
			{
				if (this.dataGridView.RowCount > 1)
				{
					int index = this.dataGridView.SelectedRows[0].Index;
					if (index > 0)
					{
						this.dataGridView.Rows[index - 1].Selected = true;
						this.dataGridView.DisplayedRowCount(false);
						if (this.dataGridView.FirstDisplayedScrollingRowIndex > 0 && index - 1 < this.dataGridView.FirstDisplayedScrollingRowIndex)
						{
							this.dataGridView.FirstDisplayedScrollingRowIndex--;
						}
						return true;
					}
				}
			}
			else if (keyData == Keys.Down && this.dataGridView.RowCount > 1)
			{
				int index2 = this.dataGridView.SelectedRows[0].Index;
				if (index2 < this.dataGridView.RowCount - 1)
				{
					this.dataGridView.Rows[index2 + 1].Selected = true;
					int num = this.dataGridView.DisplayedRowCount(false);
					if (index2 + 1 >= num)
					{
						this.dataGridView.FirstDisplayedScrollingRowIndex = index2 + 2 - num;
					}
					return true;
				}
				return true;
			}
			return base.ProcessCmdKey(ref msg, keyData);
		}

		// Token: 0x0600139C RID: 5020 RVA: 0x00083EBC File Offset: 0x000820BC
		private void textBox_TextChanged(object sender, EventArgs e)
		{
			if (string.IsNullOrEmpty(this.textBox.Text))
			{
				int count = 200;
				if (200 > this.list_0.Count)
				{
					count = this.list_0.Count;
				}
				this.bindingSource_0.DataSource = this.list_0.Take(count);
			}
			else
			{
				QuickWnd.Class279 @class = new QuickWnd.Class279();
				@class.string_0 = this.textBox.Text.Trim().ToLower();
				IEnumerable<QuickWndItem> dataSource = this.list_0.Where(new Func<QuickWndItem, bool>(@class.method_0));
				this.bindingSource_0.DataSource = dataSource;
			}
		}

		// Token: 0x0600139D RID: 5021 RVA: 0x00007ECB File Offset: 0x000060CB
		public void method_2(List<QuickWndItem> list_1, string string_1)
		{
			this.method_3(list_1, null, string_1);
		}

		// Token: 0x0600139E RID: 5022 RVA: 0x00083F60 File Offset: 0x00082160
		public void method_3(List<QuickWndItem> list_1, IEnumerable<QuickWndItem> ienumerable_0, string string_1)
		{
			QuickWnd.Class280 @class = new QuickWnd.Class280();
			@class.string_0 = string_1;
			this.list_0 = list_1;
			this.string_0 = @class.string_0;
			this.bindingSource_0 = new BindingSource();
			if (ienumerable_0 != null && ienumerable_0.Any<QuickWndItem>())
			{
				this.bindingSource_0.DataSource = ienumerable_0;
			}
			else
			{
				IEnumerable<QuickWndItem> dataSource = list_1.Where(new Func<QuickWndItem, bool>(@class.method_0));
				this.bindingSource_0.DataSource = dataSource;
			}
			this.dataGridView.DataSource = this.bindingSource_0;
			this.textBox.Text = @class.string_0;
		}

		// Token: 0x0600139F RID: 5023 RVA: 0x00007ED8 File Offset: 0x000060D8
		private void dataGridView_DoubleClick(object sender, EventArgs e)
		{
			this.method_4();
		}

		// Token: 0x060013A0 RID: 5024 RVA: 0x00083FF4 File Offset: 0x000821F4
		private void method_4()
		{
			if (this.dataGridView.SelectedRows != null && this.dataGridView.SelectedRows.Count >= 1)
			{
				base.Owner = null;
				base.DialogResult = DialogResult.OK;
				QuickWndItem quickWndItem_ = this.dataGridView.SelectedRows[0].DataBoundItem as QuickWndItem;
				this.method_0(new EventArgs10(quickWndItem_));
			}
		}

		// Token: 0x170002F0 RID: 752
		// (get) Token: 0x060013A1 RID: 5025 RVA: 0x000566C0 File Offset: 0x000548C0
		protected CreateParams CreateParams
		{
			get
			{
				CreateParams createParams = base.CreateParams;
				createParams.ExStyle |= 33554432;
				return createParams;
			}
		}

		// Token: 0x170002F1 RID: 753
		// (get) Token: 0x060013A2 RID: 5026 RVA: 0x00023060 File Offset: 0x00021260
		protected bool ShowWithoutActivation
		{
			get
			{
				return true;
			}
		}

		// Token: 0x170002F2 RID: 754
		// (get) Token: 0x060013A3 RID: 5027 RVA: 0x0008405C File Offset: 0x0008225C
		// (set) Token: 0x060013A4 RID: 5028 RVA: 0x00007EE2 File Offset: 0x000060E2
		public List<QuickWndItem> Items
		{
			get
			{
				return this.list_0;
			}
			set
			{
				this.list_0 = value;
			}
		}

		// Token: 0x170002F3 RID: 755
		// (get) Token: 0x060013A5 RID: 5029 RVA: 0x00084074 File Offset: 0x00082274
		// (set) Token: 0x060013A6 RID: 5030 RVA: 0x00007EED File Offset: 0x000060ED
		public string KeyInStr
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x060013A7 RID: 5031 RVA: 0x00007EF8 File Offset: 0x000060F8
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000A35 RID: 2613
		[CompilerGenerated]
		private Delegate13 delegate13_0;

		// Token: 0x04000A36 RID: 2614
		private List<QuickWndItem> list_0;

		// Token: 0x04000A37 RID: 2615
		private string string_0;

		// Token: 0x04000A38 RID: 2616
		private BindingSource bindingSource_0;

		// Token: 0x04000A39 RID: 2617
		private IContainer icontainer_0;

		// Token: 0x020001F4 RID: 500
		[CompilerGenerated]
		private sealed class Class279
		{
			// Token: 0x060013AA RID: 5034 RVA: 0x00084224 File Offset: 0x00082424
			internal bool method_0(QuickWndItem quickWndItem_0)
			{
				bool result;
				if (!quickWndItem_0.Code.ToLower().StartsWith(this.string_0))
				{
					if (!string.IsNullOrEmpty(quickWndItem_0.HidenCode))
					{
						result = quickWndItem_0.HidenCode.StartsWith(this.string_0);
					}
					else
					{
						result = false;
					}
				}
				else
				{
					result = true;
				}
				return result;
			}

			// Token: 0x04000A3C RID: 2620
			public string string_0;
		}

		// Token: 0x020001F5 RID: 501
		[CompilerGenerated]
		private sealed class Class280
		{
			// Token: 0x060013AC RID: 5036 RVA: 0x00084274 File Offset: 0x00082474
			internal bool method_0(QuickWndItem quickWndItem_0)
			{
				bool result;
				if (!quickWndItem_0.Code.ToLower().StartsWith(this.string_0.ToLower()))
				{
					if (!string.IsNullOrEmpty(quickWndItem_0.HidenCode))
					{
						result = quickWndItem_0.HidenCode.StartsWith(this.string_0);
					}
					else
					{
						result = false;
					}
				}
				else
				{
					result = true;
				}
				return result;
			}

			// Token: 0x04000A3D RID: 2621
			public string string_0;
		}
	}
}
