﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using TEx;
using TEx.Trading;

namespace ns9
{
	// Token: 0x020000AB RID: 171
	internal sealed class Class66 : TOdrLine
	{
		// Token: 0x060005D7 RID: 1495 RVA: 0x0000472D File Offset: 0x0000292D
		public Class66(ChartCS chartCS_0, CondOrder condOrder_1) : base(chartCS_0, condOrder_1.CondPrice)
		{
			this.condOrder_0 = condOrder_1;
			this.vmethod_1();
			base.method_0();
			Base.UI.ChartThemeChanged += this.method_13;
		}

		// Token: 0x060005D8 RID: 1496 RVA: 0x0002BEB8 File Offset: 0x0002A0B8
		public override void vmethod_1()
		{
			if (this.CondOrder != null)
			{
				if (this.bool_1)
				{
					base.Text = "以损定量-止损单，止损价：" + base.method_5(base.Price);
				}
				else
				{
					base.Text = this.method_9();
				}
				if (base.TextBox != null)
				{
					base.TextBox.Text = base.Text;
				}
			}
		}

		// Token: 0x060005D9 RID: 1497 RVA: 0x0002BF1C File Offset: 0x0002A11C
		private string method_8()
		{
			string text = this.string_2;
			if (this.CondOrder != null)
			{
				text += Base.Trading.smethod_35(this.CondOrder.OrderType);
				text += this.method_12();
			}
			return text;
		}

		// Token: 0x060005DA RID: 1498 RVA: 0x0002BF64 File Offset: 0x0002A164
		private string method_9()
		{
			string text = this.method_8();
			if (this.CondOrder != null)
			{
				decimal decimal_ = this.method_11();
				text += base.method_5(decimal_);
				if (this.CondOrder.TrailingStopPts != null)
				{
					text += " (跟踪止损)";
				}
			}
			return text;
		}

		// Token: 0x060005DB RID: 1499 RVA: 0x0002BFBC File Offset: 0x0002A1BC
		private string method_10()
		{
			string text = this.method_8();
			if (this.CondOrder != null)
			{
				decimal decimal_ = this.method_11();
				text = string.Concat(new string[]
				{
					text,
					"(条件",
					Base.Data.smethod_130(this.CondOrder.ComparisonOpt),
					base.method_5(base.Price),
					", 委价",
					base.method_5(decimal_),
					")"
				});
				if (this.CondOrder.TrailingStopPts != null)
				{
					text = text.Remove(text.Length - 1, 1) + ", 跟踪止损点数" + this.CondOrder.TrailingStopPts.Value.ToString() + ")";
				}
			}
			return text;
		}

		// Token: 0x060005DC RID: 1500 RVA: 0x0002C08C File Offset: 0x0002A28C
		private decimal method_11()
		{
			decimal result = -1m;
			if (this.CondOrder != null)
			{
				result = this.CondOrder.ExePrice;
				if (this.CondOrder.TrailingStopPts != null)
				{
					result = this.CondOrder.CondPrice;
				}
			}
			return result;
		}

		// Token: 0x060005DD RID: 1501 RVA: 0x0002C0DC File Offset: 0x0002A2DC
		private string method_12()
		{
			string result = string.Empty;
			if (this.CondOrder != null)
			{
				result = this.CondOrder.Units.ToString() + (base.Chart.Symbol.IsFutures ? "手" : "股");
			}
			return result;
		}

		// Token: 0x060005DE RID: 1502 RVA: 0x0002C134 File Offset: 0x0002A334
		protected override Color vmethod_7()
		{
			Color result;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				result = Color.LightGray;
			}
			else
			{
				result = Color.Gray;
			}
			return result;
		}

		// Token: 0x060005DF RID: 1503 RVA: 0x0002C160 File Offset: 0x0002A360
		protected override DashStyle vmethod_8()
		{
			return DashStyle.Dot;
		}

		// Token: 0x060005E0 RID: 1504 RVA: 0x0002C174 File Offset: 0x0002A374
		private void method_13(object sender, EventArgs e)
		{
			Color color = this.vmethod_7();
			if (base.Line != null && base.Line.Line != null)
			{
				base.Line.Line.Color = color;
			}
			if (base.TextBox != null && base.TextBox.FontSpec != null)
			{
				base.TextBox.FontSpec.FontColor = color;
			}
		}

		// Token: 0x060005E1 RID: 1505 RVA: 0x0002C1D8 File Offset: 0x0002A3D8
		public override void vmethod_4(double double_0)
		{
			if (this.CondOrder != null)
			{
				SymbDataSet symbDataSet = base.Chart.SymbDataSet;
				decimal num = symbDataSet.method_69(double_0);
				decimal? nullable_ = null;
				if (this.CondOrder.TrailingStopPts != null)
				{
					int orderType = (int)this.CondOrder.OrderType;
					decimal d = Convert.ToDecimal(symbDataSet.CurrHisData.Close);
					decimal value = this.CondOrder.TrailingStopPts.Value;
					decimal condPrice = this.CondOrder.CondPrice;
					if (orderType == 1)
					{
						decimal num2 = condPrice + value;
						if (num >= d)
						{
							return;
						}
						nullable_ = new decimal?(num2 - num);
					}
					else
					{
						decimal num2 = condPrice - value;
						if (num <= d)
						{
							return;
						}
						nullable_ = new decimal?(num - num2);
					}
				}
				if (Base.Trading.smethod_93(this.condOrder_0.ID, new decimal?(num), new decimal?(num), nullable_, this.condOrder_0.Units, false))
				{
					base.Price = num;
					base.vmethod_4(Convert.ToDouble(num));
				}
			}
		}

		// Token: 0x060005E2 RID: 1506 RVA: 0x0002C2F8 File Offset: 0x0002A4F8
		protected override double vmethod_3()
		{
			return Convert.ToDouble(this.condOrder_0.CondPrice);
		}

		// Token: 0x060005E3 RID: 1507 RVA: 0x0000476D File Offset: 0x0000296D
		public override void vmethod_2()
		{
			base.vmethod_2();
			this.vmethod_1();
		}

		// Token: 0x060005E4 RID: 1508 RVA: 0x0000477D File Offset: 0x0000297D
		protected override void vmethod_0(bool bool_2)
		{
			Base.UI.ChartThemeChanged -= this.method_13;
			base.vmethod_0(bool_2);
		}

		// Token: 0x17000125 RID: 293
		// (get) Token: 0x060005E5 RID: 1509 RVA: 0x0002C31C File Offset: 0x0002A51C
		// (set) Token: 0x060005E6 RID: 1510 RVA: 0x00004799 File Offset: 0x00002999
		public CondOrder CondOrder
		{
			get
			{
				return this.condOrder_0;
			}
			set
			{
				this.condOrder_0 = value;
			}
		}

		// Token: 0x17000126 RID: 294
		// (get) Token: 0x060005E7 RID: 1511 RVA: 0x0002C334 File Offset: 0x0002A534
		public override bool IsCurrent
		{
			get
			{
				bool result;
				if (this.CondOrder.AcctID == Base.Acct.CurrAccount.ID)
				{
					result = (this.CondOrder.SymbID == base.Chart.Symbol.ID);
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x17000127 RID: 295
		// (get) Token: 0x060005E8 RID: 1512 RVA: 0x0002C380 File Offset: 0x0002A580
		// (set) Token: 0x060005E9 RID: 1513 RVA: 0x0002C3D4 File Offset: 0x0002A5D4
		public override bool HighLighted
		{
			get
			{
				bool result;
				if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
				{
					result = (base.Line.Line.Color != Color.LightGray);
				}
				else
				{
					result = (base.Line.Line.Color != Color.Gray);
				}
				return result;
			}
			set
			{
				Color color;
				if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
				{
					color = (value ? Color.White : Color.LightGray);
				}
				else
				{
					color = (value ? Color.DarkGray : Color.Gray);
				}
				base.Line.Line.Color = color;
				base.TextBox.FontSpec.FontColor = color;
				if (this.bool_1)
				{
					this.vmethod_1();
				}
				else if (value)
				{
					base.TextBox.Text = this.method_10();
				}
				else
				{
					base.TextBox.Text = this.method_9();
				}
			}
		}

		// Token: 0x17000128 RID: 296
		// (get) Token: 0x060005EA RID: 1514 RVA: 0x0002C46C File Offset: 0x0002A66C
		// (set) Token: 0x060005EB RID: 1515 RVA: 0x000047A4 File Offset: 0x000029A4
		public bool IsInROpenState
		{
			get
			{
				return this.bool_1;
			}
			set
			{
				if (this.bool_1 != value)
				{
					this.bool_1 = value;
					this.vmethod_1();
					base.Chart.ZedGraphControl.Refresh();
				}
			}
		}

		// Token: 0x04000299 RID: 665
		private CondOrder condOrder_0;

		// Token: 0x0400029A RID: 666
		private string string_2 = "";

		// Token: 0x0400029B RID: 667
		private bool bool_1;
	}
}
