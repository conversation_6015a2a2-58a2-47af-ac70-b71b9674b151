﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using DevComponents.Editors;
using DevComponents.Editors.DateTimeAdv;
using ns28;
using ns4;
using ns6;
using TEx.Comn;

namespace TEx
{
	// Token: 0x02000249 RID: 585
	internal sealed partial class DateSelectForm : Form
	{
		// Token: 0x060018D3 RID: 6355 RVA: 0x0000A320 File Offset: 0x00008520
		public DateSelectForm()
		{
			this.InitializeComponent();
			base.AutoScaleMode = AutoScaleMode.Dpi;
			this.method_1();
		}

		// Token: 0x060018D4 RID: 6356 RVA: 0x000A5728 File Offset: 0x000A3928
		private void DateSelectForm_Load(object sender, EventArgs e)
		{
			this.labelX_info.ForeColor = Class179.color_4;
			this.comboBox_Hour.Enabled = false;
			this.comboBox_Min.Enabled = false;
			this.radioBtn_NightMktOpen.Enabled = this.method_4();
			this.radioBtn_DayMktOpen.Checked = true;
			Base.UI.smethod_80(this.dateTimeInput_start);
			Base.UI.smethod_80(this.dateTimeInput_end);
			if (Base.Data.SymbDataSets.Where(new Func<SymbDataSet, bool>(DateSelectForm.<>c.<>9.method_0)).Any<SymbDataSet>())
			{
				if (Base.Data.SymbDataSets.Where(new Func<SymbDataSet, bool>(DateSelectForm.<>c.<>9.method_1)).Any<SymbDataSet>())
				{
					DateTime dateTime = Base.Data.SymbDataSets.Where(new Func<SymbDataSet, bool>(DateSelectForm.<>c.<>9.method_2)).Min(new Func<SymbDataSet, DateTime>(DateSelectForm.<>c.<>9.method_3));
					DateTime dateTime2 = Base.Data.SymbDataSets.Where(new Func<SymbDataSet, bool>(DateSelectForm.<>c.<>9.method_4)).Max(new Func<SymbDataSet, DateTime>(DateSelectForm.<>c.<>9.method_5));
					while (!this.method_0(dateTime))
					{
						dateTime = dateTime.AddDays(1.0);
					}
					while (!this.method_0(dateTime2))
					{
						dateTime2 = dateTime2.AddDays(-1.0);
					}
					this.dateTimeInput_start.MinDate = dateTime;
					this.dateTimeInput_start.MaxDate = dateTime2;
					this.dateTimeInput_end.MinDate = dateTime;
					this.dateTimeInput_end.MaxDate = dateTime2;
					if (Base.Data.CurrDate >= dateTime && Base.Data.CurrDate.Date <= dateTime2.Date)
					{
						this.dateTimeInput_start.Value = Base.Data.CurrDate;
					}
					else
					{
						this.dateTimeInput_start.Value = dateTime;
					}
					this.dateTimeInput_end.Value = dateTime2;
					this.labelX_info.Text = string.Concat(new string[]
					{
						TApp.IsTrialUser ? "作为免费版用户，您可复盘" : "您可复盘",
						"当前页面品种从",
						dateTime.ToLongDateString(),
						"至",
						dateTime2.ToLongDateString(),
						"的数据。选择任意时间段，点击[确定]后行情会自动跳转至所选起始时间。"
					});
					for (int i = 0; i <= 24; i++)
					{
						this.comboBox_Hour.Items.Add(i);
					}
					for (int j = 0; j <= 60; j++)
					{
						this.comboBox_Min.Items.Add(j);
					}
					if (dateTime != DateTime.MinValue)
					{
						this.comboBox_Hour.Text = dateTime.Hour.ToString();
					}
					if (dateTime2 != DateTime.MaxValue)
					{
						this.comboBox_Min.Text = dateTime2.Minute.ToString();
						goto IL_342;
					}
					goto IL_342;
				}
			}
			this.dateTimeInput_start.Enabled = false;
			this.dateTimeInput_start.Enabled = false;
			this.labelX_info.Text = "当前页面品种在您拥有的数据期间内无有效数据。";
			this.btnOK.Enabled = false;
			IL_342:
			this.dateTimeInput_start.ValueChanged += this.dateTimeInput_start_ValueChanged;
			this.dateTimeInput_end.ValueChanged += this.dateTimeInput_end_ValueChanged;
		}

		// Token: 0x060018D5 RID: 6357 RVA: 0x000A5AA8 File Offset: 0x000A3CA8
		private void DateSelectForm_Shown(object sender, EventArgs e)
		{
			List<DateTime> list = new List<DateTime>();
			DateTime dateTime;
			DateTime dateTime2;
			if (Base.Data.SymbDataSets.Exists(new Predicate<SymbDataSet>(DateSelectForm.<>c.<>9.method_6)))
			{
				dateTime = Base.Data.SymbDataSets.Where(new Func<SymbDataSet, bool>(DateSelectForm.<>c.<>9.method_7)).Min(new Func<SymbDataSet, DateTime>(DateSelectForm.<>c.<>9.method_8));
				dateTime2 = Base.Data.SymbDataSets.Where(new Func<SymbDataSet, bool>(DateSelectForm.<>c.<>9.method_9)).Max(new Func<SymbDataSet, DateTime>(DateSelectForm.<>c.<>9.method_10));
			}
			else
			{
				dateTime = this.dateTimeInput_start.Value;
				dateTime2 = this.dateTimeInput_end.Value;
			}
			while (!this.method_0(dateTime2))
			{
				dateTime2 = dateTime2.AddDays(-1.0);
			}
			while (dateTime <= dateTime2)
			{
				if (this.method_0(dateTime))
				{
					list.Add(dateTime);
				}
				dateTime = dateTime.AddDays(1.0);
			}
			this.rangeSlider1.RangeValues = list;
			this.rangeSlider1.Range2 = this.dateTimeInput_end.Value;
			this.rangeSlider1.Range1 = this.dateTimeInput_start.Value;
			this.rangeSlider1.RangeChanged += this.method_2;
		}

		// Token: 0x060018D6 RID: 6358 RVA: 0x000A5C38 File Offset: 0x000A3E38
		private bool method_0(DateTime dateTime_0)
		{
			bool result;
			if (dateTime_0.DayOfWeek != DayOfWeek.Saturday && dateTime_0.DayOfWeek != DayOfWeek.Sunday && (dateTime_0.Day != 1 || (dateTime_0.Month != 1 && dateTime_0.Month != 5 && dateTime_0.Month != 10)) && ((dateTime_0.Day != 2 && dateTime_0.Day != 3) || (dateTime_0.Month != 5 && dateTime_0.Month != 10)))
			{
				if (dateTime_0.Day != 4 && dateTime_0.Day != 5 && dateTime_0.Day != 6)
				{
					if (dateTime_0.Day != 7)
					{
						return true;
					}
				}
				result = (dateTime_0.Month != 10);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060018D7 RID: 6359 RVA: 0x000A5CF4 File Offset: 0x000A3EF4
		private void method_1()
		{
			float num = TApp.smethod_4(9f, false);
			if (this.Font.Size != num)
			{
				this.Font = new Font("Microsoft YaHei", num);
			}
			foreach (object obj in base.Controls)
			{
				Control control = (Control)obj;
				if (control.Font.Size != num)
				{
					control.Font = new Font("Microsoft YaHei", num);
				}
			}
			foreach (object obj2 in this.panel1.Controls)
			{
				Control control2 = (Control)obj2;
				if (control2.Font.Size != num)
				{
					control2.Font = new Font("Microsoft YaHei", num);
				}
			}
			if (this.rangeSlider1.LabelFont.Size != num)
			{
				this.rangeSlider1.LabelFont = new Font("Microsoft Sans Serif", num);
			}
		}

		// Token: 0x060018D8 RID: 6360 RVA: 0x0000A33D File Offset: 0x0000853D
		private void method_2(object sender, EventArgs11 e)
		{
			this.dateTimeInput_start.Value = e.Range1;
			this.dateTimeInput_end.Value = e.Range2;
		}

		// Token: 0x060018D9 RID: 6361 RVA: 0x000A5E28 File Offset: 0x000A4028
		private void dateTimeInput_start_ValueChanged(object sender, EventArgs e)
		{
			if (!this.method_4())
			{
				if (this.radioBtn_NightMktOpen.Checked)
				{
					this.radioBtn_DayMktOpen.Checked = true;
				}
				this.radioBtn_NightMktOpen.Enabled = false;
			}
			else
			{
				this.radioBtn_NightMktOpen.Enabled = true;
			}
			if (this.dateTimeInput_end.Value < this.dateTimeInput_start.Value)
			{
				if (!this.rangeSlider1.IsDraggingThumb)
				{
					this.method_3();
				}
				this.dateTimeInput_start.Value = this.dateTimeInput_end.Value;
			}
			this.rangeSlider1.Range1 = this.dateTimeInput_start.Value;
		}

		// Token: 0x060018DA RID: 6362 RVA: 0x000A5ED0 File Offset: 0x000A40D0
		private void dateTimeInput_end_ValueChanged(object sender, EventArgs e)
		{
			if (this.dateTimeInput_end.Value < this.dateTimeInput_start.Value)
			{
				if (!this.rangeSlider1.IsDraggingThumb)
				{
					this.method_3();
				}
				this.dateTimeInput_end.Value = this.dateTimeInput_start.Value;
			}
			this.rangeSlider1.Range2 = this.dateTimeInput_end.Value;
		}

		// Token: 0x060018DB RID: 6363 RVA: 0x0000A363 File Offset: 0x00008563
		private void method_3()
		{
			MessageBox.Show("行情起始日期应小于结束日期。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
		}

		// Token: 0x060018DC RID: 6364 RVA: 0x000A5F3C File Offset: 0x000A413C
		private bool method_4()
		{
			bool result = false;
			foreach (SymbDataSet symbDataSet in Base.Data.SymbDataSets)
			{
				if (symbDataSet.CurrSymbol.IsFutures)
				{
					DateTime value = this.dateTimeInput_start.Value;
					if (Base.Data.smethod_112(symbDataSet.CurrSymbol, value) != null)
					{
						result = true;
						break;
					}
				}
			}
			return result;
		}

		// Token: 0x060018DD RID: 6365 RVA: 0x000A5FBC File Offset: 0x000A41BC
		private void btnOK_Click(object sender, EventArgs e)
		{
			if (!this.dateTimeInput_start.Enabled || !this.dateTimeInput_end.Enabled)
			{
				base.Close();
			}
			if (this.dateTimeInput_start.Value >= this.dateTimeInput_end.Value.Date.AddHours(16.0))
			{
				this.method_3();
			}
			else
			{
				TimeSpan? timeSpan = this.method_6();
				if (timeSpan != null)
				{
					if (!Base.UI.smethod_122(this.dateTimeInput_start.Value, "存在开仓时间晚于所选行情起始日期的未平仓交易，请先平仓或重新选择日期。"))
					{
						if (Base.UI.smethod_123())
						{
							if (Base.Data.smethod_129(this.dateTimeInput_start.Value.Date, this.dateTimeInput_end.Value.Date))
							{
								this.method_5();
								Base.Data.smethod_128(this.dateTimeInput_start.Value.Date.Add(timeSpan.Value), new DateTime?(this.dateTimeInput_end.Value));
								base.Dispose();
							}
							else
							{
								this.method_7();
							}
						}
					}
				}
			}
		}

		// Token: 0x060018DE RID: 6366 RVA: 0x0000A37A File Offset: 0x0000857A
		private void method_5()
		{
			this.labelX_info.Text = "正在提取所选日期数据，请稍等...";
			this.Refresh();
			this.Cursor = Cursors.WaitCursor;
			this.btnOK.Enabled = false;
			this.btnCancel.Enabled = false;
		}

		// Token: 0x060018DF RID: 6367 RVA: 0x000A60E0 File Offset: 0x000A42E0
		private TimeSpan? method_6()
		{
			int hours = 0;
			int minutes = 0;
			TimeSpan? result;
			if (this.radioBtn_DayMktOpen.Checked)
			{
				result = new TimeSpan?(Base.Data.SymbDataSets.Select(new Func<SymbDataSet, ExchgOBT>(this.method_8)).Min(new Func<ExchgOBT, TimeSpan>(DateSelectForm.<>c.<>9.method_11)));
			}
			else
			{
				TimeSpan? timeSpan;
				if (this.radioBtn_NightMktOpen.Checked)
				{
					try
					{
						timeSpan = new TimeSpan?(Base.Data.SymbDataSets.Select(new Func<SymbDataSet, ExchgOBT>(this.method_9)).Where(new Func<ExchgOBT, bool>(DateSelectForm.<>c.<>9.method_12)).Min(new Func<ExchgOBT, TimeSpan>(DateSelectForm.<>c.<>9.method_13)));
						goto IL_158;
					}
					catch (Exception)
					{
						MessageBox.Show("夜盘做为开始时间无法选取有效数据，请重新选择！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
						timeSpan = null;
						goto IL_158;
					}
				}
				try
				{
					hours = Convert.ToInt32(this.comboBox_Hour.Text);
				}
				catch
				{
					MessageBox.Show("请选择有效的开始时间小时数。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					timeSpan = null;
					goto IL_158;
				}
				try
				{
					minutes = Convert.ToInt32(this.comboBox_Min.Text);
					goto IL_15D;
				}
				catch
				{
					MessageBox.Show("请选择有效的开始时间分钟数。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					timeSpan = null;
				}
				goto IL_158;
				IL_15D:
				return new TimeSpan?(new TimeSpan(hours, minutes, 0));
				IL_158:
				result = timeSpan;
			}
			return result;
		}

		// Token: 0x060018E0 RID: 6368 RVA: 0x00005F5A File Offset: 0x0000415A
		private void method_7()
		{
			MessageBox.Show("所选期间内没有行情记录！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
		}

		// Token: 0x060018E1 RID: 6369 RVA: 0x000A6288 File Offset: 0x000A4488
		private void radioBtn_SelectTime_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioBtn_SelectTime.Checked)
			{
				this.comboBox_Hour.Enabled = true;
				this.comboBox_Min.Enabled = true;
			}
			else
			{
				this.comboBox_Hour.Enabled = false;
				this.comboBox_Min.Enabled = false;
			}
		}

		// Token: 0x060018E2 RID: 6370 RVA: 0x0000A3B7 File Offset: 0x000085B7
		private void DateSelectForm_FormClosing(object sender, FormClosingEventArgs e)
		{
			this.Cursor = Cursors.Default;
		}

		// Token: 0x060018E3 RID: 6371 RVA: 0x0000A3C6 File Offset: 0x000085C6
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060018E5 RID: 6373 RVA: 0x000A7350 File Offset: 0x000A5550
		[CompilerGenerated]
		private ExchgOBT method_8(SymbDataSet symbDataSet_0)
		{
			return Base.Data.smethod_110(symbDataSet_0.CurrSymbol, this.dateTimeInput_start.Value);
		}

		// Token: 0x060018E6 RID: 6374 RVA: 0x000A7350 File Offset: 0x000A5550
		[CompilerGenerated]
		private ExchgOBT method_9(SymbDataSet symbDataSet_0)
		{
			return Base.Data.smethod_110(symbDataSet_0.CurrSymbol, this.dateTimeInput_start.Value);
		}

		// Token: 0x04000C69 RID: 3177
		private IContainer icontainer_0;
	}
}
