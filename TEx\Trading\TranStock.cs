﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace TEx.Trading
{
	// Token: 0x020003AE RID: 942
	[Serializable]
	internal sealed class TranStock : Transaction
	{
		// Token: 0x0600261A RID: 9754 RVA: 0x0000E868 File Offset: 0x0000CA68
		public TranStock()
		{
		}

		// Token: 0x0600261B RID: 9755 RVA: 0x000FAB6C File Offset: 0x000F8D6C
		protected TranStock(SerializationInfo info, StreamingContext context)
		{
			base.ID = info.GetInt32("Transaction+_ID");
			base.AcctID = info.GetInt32("Transaction+_AcctID");
			base.TransType = info.GetInt32("Transaction+_TransType");
			base.SymbolID = info.GetInt32("Transaction+_SymbolID");
			base.Price = info.GetDecimal("Transaction+_Price");
			base.Units = Convert.ToInt64(info.GetInt32("Transaction+_Units"));
			int? num = (int?)info.GetValue("Transaction+_OpenUnits", typeof(int?));
			if (num != null)
			{
				base.OpenUnits = new long?(Convert.ToInt64(num.Value));
			}
			base.Fee = (decimal?)info.GetValue("Transaction+_Fee", typeof(decimal?));
			base.Profit = (decimal?)info.GetValue("Transaction+_Profit", typeof(decimal?));
			base.ClosedTransID = (int?)info.GetValue("Transaction+_ClosedTransID", typeof(int?));
			base.CreateTime = info.GetDateTime("Transaction+_CreateTime");
			base.UpdateTime = (DateTime?)info.GetValue("Transaction+_UpdateTime", typeof(DateTime?));
			try
			{
				base.Notes = info.GetString("Transaction+_Notes");
			}
			catch
			{
			}
			try
			{
				base.CreateTimeN = info.GetDateTime("Transaction+_CreateTimeN");
				base.UpdateTimeN = (DateTime?)info.GetValue("Transaction+_UpdateTimeN", typeof(DateTime?));
			}
			catch
			{
			}
			this._OriPrice = info.GetDecimal("_OriPrice");
			this._OriUnits = Convert.ToInt64(info.GetInt32("_OriUnits"));
			this._IsRational = info.GetBoolean("_IsRational");
			this._ProcessedStSplts = (List<StSplit>)info.GetValue("_ProcessedStSplts", typeof(List<StSplit>));
		}

		// Token: 0x0600261C RID: 9756 RVA: 0x000FAD7C File Offset: 0x000F8F7C
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			info.AddValue("Transaction+_ID", base.ID);
			info.AddValue("Transaction+_AcctID", base.AcctID);
			info.AddValue("Transaction+_TransType", base.TransType);
			info.AddValue("Transaction+_SymbolID", base.SymbolID);
			info.AddValue("Transaction+_Price", base.Price);
			int value;
			if (base.Units > 2147483647L)
			{
				value = int.MaxValue;
			}
			else
			{
				value = Convert.ToInt32(base.Units);
			}
			info.AddValue("Transaction+_Units", value);
			long? openUnits = base.OpenUnits;
			int value2;
			if (openUnits.GetValueOrDefault() > 2147483647L & openUnits != null)
			{
				value2 = int.MaxValue;
			}
			else
			{
				value2 = Convert.ToInt32(base.OpenUnits);
			}
			info.AddValue("Transaction+_OpenUnits", value2);
			info.AddValue("Transaction+_Fee", base.Fee);
			info.AddValue("Transaction+_Profit", base.Profit);
			info.AddValue("Transaction+_ClosedTransID", base.ClosedTransID);
			info.AddValue("Transaction+_Notes", base.Notes);
			info.AddValue("Transaction+_CreateTime", base.CreateTime);
			info.AddValue("Transaction+_UpdateTime", base.UpdateTime);
			info.AddValue("Transaction+_CreateTimeN", base.CreateTimeN);
			info.AddValue("Transaction+_UpdateTimeN", base.UpdateTimeN);
			int value3;
			if (this._OriUnits > 2147483647L)
			{
				value3 = int.MaxValue;
			}
			else
			{
				value3 = Convert.ToInt32(this._OriUnits);
			}
			info.AddValue("_OriUnits", value3);
			info.AddValue("_OriPrice", this._OriPrice);
			info.AddValue("_IsRational", this._IsRational);
			info.AddValue("_ProcessedStSplts", this._ProcessedStSplts);
		}

		// Token: 0x1700066D RID: 1645
		// (get) Token: 0x0600261D RID: 9757 RVA: 0x000FAF6C File Offset: 0x000F916C
		// (set) Token: 0x0600261E RID: 9758 RVA: 0x0000E870 File Offset: 0x0000CA70
		public decimal OriPrice
		{
			get
			{
				return this._OriPrice;
			}
			set
			{
				this._OriPrice = value;
			}
		}

		// Token: 0x1700066E RID: 1646
		// (get) Token: 0x0600261F RID: 9759 RVA: 0x000FAF84 File Offset: 0x000F9184
		// (set) Token: 0x06002620 RID: 9760 RVA: 0x0000E87B File Offset: 0x0000CA7B
		public long OriUnits
		{
			get
			{
				return this._OriUnits;
			}
			set
			{
				this._OriUnits = value;
			}
		}

		// Token: 0x1700066F RID: 1647
		// (get) Token: 0x06002621 RID: 9761 RVA: 0x000FAF9C File Offset: 0x000F919C
		// (set) Token: 0x06002622 RID: 9762 RVA: 0x0000E886 File Offset: 0x0000CA86
		public bool IsRational
		{
			get
			{
				return this._IsRational;
			}
			set
			{
				this._IsRational = value;
			}
		}

		// Token: 0x17000670 RID: 1648
		// (get) Token: 0x06002623 RID: 9763 RVA: 0x000FAFB4 File Offset: 0x000F91B4
		// (set) Token: 0x06002624 RID: 9764 RVA: 0x0000E891 File Offset: 0x0000CA91
		public List<StSplit> ProcessedStSplts
		{
			get
			{
				return this._ProcessedStSplts;
			}
			set
			{
				this._ProcessedStSplts = value;
			}
		}

		// Token: 0x06002625 RID: 9765 RVA: 0x000FAFCC File Offset: 0x000F91CC
		public void method_0()
		{
			decimal d = (base.OpenUnits != null) ? (base.OpenUnits.Value / Convert.ToDecimal(base.Units)) : 1m;
			base.Units = Convert.ToInt64(Math.Round(this.OriUnits * d));
			base.OpenUnits = new long?(base.Units);
			base.Price = this.OriPrice;
			this.ProcessedStSplts = null;
		}

		// Token: 0x0400125F RID: 4703
		private decimal _OriPrice;

		// Token: 0x04001260 RID: 4704
		private long _OriUnits;

		// Token: 0x04001261 RID: 4705
		private bool _IsRational;

		// Token: 0x04001262 RID: 4706
		private List<StSplit> _ProcessedStSplts;
	}
}
