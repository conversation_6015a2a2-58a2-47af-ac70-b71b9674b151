// 定义一个方法，用于根据股票代码、时间范围和是否需要筛选等条件获取历史数据
public List<HisData> method_29(StkSymbol stkSymbol_1, int int_1, bool bool_1)
{
    // 调用 method_55 方法获取与股票代码、时间范围和筛选条件相关的文件信息列表
    List<DatFileInfo> list = this.method_55(stkSymbol_1, int_1, bool_1);
    if (list != null) // 如果文件信息列表不为空
    {
        // 调用 method_30 方法从文件信息列表中获取一个特定的文件信息
        DatFileInfo? datFileInfo = this.method_30(list);
        if (datFileInfo != null) // 如果获取到的文件信息不为空
        {
            // 通过 Base.Data.smethod_45 方法根据文件名获取历史数据列表
            List<HisData> list2 = Base.Data.smethod_45(datFileInfo.Value.FileName);
            // 使用 LINQ 查询从文件信息列表中筛选出符合条件的文件信息
            IEnumerable<DatFileInfo> source = list.Where(new Func<DatFileInfo, bool>(SymbDataSet.<>c.<>9.method_0));
            if (source.Any<DatFileInfo>()) // 如果筛选后的文件信息列表不为空
            {
                // 通过 Base.Data.smethod_45 方法根据筛选后的第一个文件名获取历史数据列表
                List<HisData> list3 = Base.Data.smethod_45(source.First<DatFileInfo>().FileName);
                // 判断两个历史数据列表是否都不为空且筛选后的列表中有数据
                if (list2 != null && list3 != null && list3.Any<HisData>())
                {
                    // 如果第一个历史数据列表不为空
                    if (list2.Any<HisData>())
                    {
                        // 创建一个 SymbDataSet.Class24 类的实例
                        SymbDataSet.Class24 @class = new SymbDataSet.Class24();
                        // 设置实例的 dateTime_0 属性为第一个历史数据列表中最后一个数据的日期
                        @class.dateTime_0 = list2.Last<HisData>().Date;
                        // 判断筛选后的历史数据列表中第一个数据的日期是否大于第一个历史数据列表中最后一个数据的日期
                        if (list3.First<HisData>().Date > @class.dateTime_0)
                        {
                            // 如果是，则将筛选后的历史数据列表中的所有数据添加到第一个历史数据列表中
                            list2.AddRange(list3);
                        }
                        else
                        {
                            // 如果不是，则将筛选后的历史数据列表中满足条件的数据添加到第一个历史数据列表中
                            list2.AddRange(list3.Where(new Func<HisData, bool>(@class.method_0)));
                        }
                    }
                    else
                    {
                        // 如果第一个历史数据列表为空，则将筛选后的历史数据列表中的所有数据添加到第一个历史数据列表中
                        list2.AddRange(list3);
                    }
                }
                else if (list2 == null) // 如果第一个历史数据列表为空
                {
                    // 将筛选后的历史数据列表赋值给第一个历史数据列表
                    list2 = list3;
                }
            }
            // 返回第一个历史数据列表
            return list2;
        }
    }
    // 如果文件信息列表为空或获取到的文件信息为空，返回 null
    return null;
}