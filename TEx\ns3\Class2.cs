﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace ns3
{
	// Token: 0x02000003 RID: 3
	[CompilerGenerated]
	internal sealed class Class2<T>
	{
		// Token: 0x17000005 RID: 5
		// (get) Token: 0x06000009 RID: 9 RVA: 0x00010018 File Offset: 0x0000E218
		public T codes
		{
			get
			{
				return this.gparam_0;
			}
		}

		// Token: 0x0600000A RID: 10 RVA: 0x00002A7B File Offset: 0x00000C7B
		[DebuggerHidden]
		public Class2(T gparam_1)
		{
			this.gparam_0 = gparam_1;
		}

		// Token: 0x0600000B RID: 11 RVA: 0x00010030 File Offset: 0x0000E230
		[DebuggerHidden]
		public bool Equals(object obj)
		{
			Class2<T> @class = obj as Class2<T>;
			bool result;
			if (@class != null)
			{
				result = EqualityComparer<T>.Default.Equals(this.gparam_0, @class.gparam_0);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600000C RID: 12 RVA: 0x00010068 File Offset: 0x0000E268
		[DebuggerHidden]
		public int GetHashCode()
		{
			return 88452124 + EqualityComparer<T>.Default.GetHashCode(this.gparam_0);
		}

		// Token: 0x0600000D RID: 13 RVA: 0x00010090 File Offset: 0x0000E290
		[DebuggerHidden]
		public string ToString()
		{
			IFormatProvider provider = null;
			string format = "{{ codes = {0} }}";
			object[] array = new object[1];
			int num = 0;
			T t = this.gparam_0;
			ref T ptr = ref t;
			T t2 = default(T);
			object obj;
			if (t2 == null)
			{
				t2 = t;
				ptr = ref t2;
				if (t2 == null)
				{
					obj = null;
					goto IL_46;
				}
			}
			obj = ptr.ToString();
			IL_46:
			array[num] = obj;
			return string.Format(provider, format, array);
		}

		// Token: 0x04000005 RID: 5
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly T gparam_0;
	}
}
