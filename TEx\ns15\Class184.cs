﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using System.Xml.Linq;
using ns14;
using ns23;
using ns28;
using TEx;

namespace ns15
{
	// Token: 0x02000131 RID: 305
	internal sealed class Class184
	{
		// Token: 0x06000C90 RID: 3216 RVA: 0x00049430 File Offset: 0x00047630
		public static List<Class264> smethod_0()
		{
			List<Class264> list = new List<Class264>();
			Class264 item = new Class264(250, "1", "1m", "一分钟线");
			Class264 item2 = new Class264(251, "0", "3m", "三分钟线");
			Class264 item3 = new Class264(252, "2", "5m", "五分钟线");
			Class264 item4 = new Class264(253, "3", "15m", "十五分钟线");
			Class264 item5 = new Class264(254, "4", "30m", "三十分钟线");
			Class264 item6 = new Class264(255, "5", "60m", "小时线");
			Class264 item7 = new Class264(256, "52", "2h", "二小时线");
			Class264 item8 = new Class264(257, "54", "4h", "四小时线");
			Class264 item9 = new Class264(258, "6", "1d", "日线");
			Class264 item10 = new Class264(259, "7", "1w", "周线");
			Class264 item11 = new Class264(260, "8", "1M", "月线");
			Class264 item12 = new Class264(261, "51", "10m", "十分钟线");
			Class264 item13 = new Class264(270, "m", "N mins", "任意分钟");
			Class264 item14 = new Class264(271, "h", "N hours", "任意小时");
			Class264 item15 = new Class264(272, "d", "N days", "任意天");
			list.Add(item);
			list.Add(item2);
			list.Add(item3);
			list.Add(item12);
			list.Add(item4);
			list.Add(item5);
			list.Add(item6);
			list.Add(item7);
			list.Add(item8);
			list.Add(item9);
			list.Add(item10);
			list.Add(item11);
			list.Add(item13);
			list.Add(item14);
			list.Add(item15);
			return list;
		}

		// Token: 0x06000C91 RID: 3217 RVA: 0x00049650 File Offset: 0x00047850
		public static List<Class264> smethod_1()
		{
			List<Class264> list = Class184.smethod_2(Base.UI.smethod_53());
			if (list == null)
			{
				list = Class184.smethod_0();
			}
			else
			{
				using (List<Class264>.Enumerator enumerator = Class184.DefaultUsrFnKeyList.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						Class184.Class185 @class = new Class184.Class185();
						@class.class264_0 = enumerator.Current;
						if (!list.Exists(new Predicate<Class264>(@class.method_0)))
						{
							list.Add(@class.class264_0);
						}
					}
				}
			}
			return list;
		}

		// Token: 0x06000C92 RID: 3218 RVA: 0x000496E4 File Offset: 0x000478E4
		public static List<Class264> smethod_2(XDocument xdocument_0)
		{
			List<Class264> list = null;
			if (xdocument_0 != null)
			{
				try
				{
					list = new List<Class264>();
					foreach (XElement xelement in xdocument_0.Element("ShortCutKeys").Element("FnKeys").Elements("FnKey"))
					{
						Class264 @class = new Class264();
						@class.Id = Convert.ToInt32(xelement.Attribute("Id").Value);
						@class.KeyStr = xelement.Attribute("KeyStr").Value;
						Class264 class2 = Class184.smethod_3(@class.Id);
						@class.EnName = class2.EnName;
						@class.CnName = class2.CnName;
						list.Add(@class);
					}
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
			return list;
		}

		// Token: 0x06000C93 RID: 3219 RVA: 0x000497EC File Offset: 0x000479EC
		private static Class264 smethod_3(int int_0)
		{
			Class184.Class186 @class = new Class184.Class186();
			@class.int_0 = int_0;
			return Class184.DefaultUsrFnKeyList.Find(new Predicate<Class264>(@class.method_0));
		}

		// Token: 0x06000C94 RID: 3220 RVA: 0x00049820 File Offset: 0x00047A20
		public static Class264 smethod_4(Enum3 enum3_0)
		{
			Class184.Class187 @class = new Class184.Class187();
			@class.enum3_0 = enum3_0;
			return Class184.UsrFnKeyList.Find(new Predicate<Class264>(@class.method_0));
		}

		// Token: 0x06000C95 RID: 3221 RVA: 0x00049854 File Offset: 0x00047A54
		public static string smethod_5(Enum3 enum3_0)
		{
			Class264 @class = Class184.smethod_4(enum3_0);
			string result;
			if (@class != null)
			{
				result = @class.KeyStr;
			}
			else
			{
				result = string.Empty;
			}
			return result;
		}

		// Token: 0x17000201 RID: 513
		// (get) Token: 0x06000C96 RID: 3222 RVA: 0x00049880 File Offset: 0x00047A80
		public static List<Class264> DefaultUsrFnKeyList
		{
			get
			{
				if (Class184.list_0 == null || Class184.list_0.Count == 0)
				{
					Class184.list_0 = Class184.smethod_0();
				}
				return Class184.list_0;
			}
		}

		// Token: 0x17000202 RID: 514
		// (get) Token: 0x06000C97 RID: 3223 RVA: 0x000498B4 File Offset: 0x00047AB4
		// (set) Token: 0x06000C98 RID: 3224 RVA: 0x00005B38 File Offset: 0x00003D38
		public static List<Class264> UsrFnKeyList
		{
			get
			{
				if (Class184.list_1 == null || Class184.list_1.Count == 0)
				{
					Class184.list_1 = Class184.smethod_1();
				}
				return Class184.list_1;
			}
			set
			{
				Class184.list_1 = value;
			}
		}

		// Token: 0x0400052E RID: 1326
		private static List<Class264> list_0;

		// Token: 0x0400052F RID: 1327
		private static List<Class264> list_1;

		// Token: 0x02000132 RID: 306
		[CompilerGenerated]
		private sealed class Class185
		{
			// Token: 0x06000C9B RID: 3227 RVA: 0x000498E8 File Offset: 0x00047AE8
			internal bool method_0(Class264 class264_1)
			{
				return class264_1.Id == this.class264_0.Id;
			}

			// Token: 0x04000530 RID: 1328
			public Class264 class264_0;
		}

		// Token: 0x02000133 RID: 307
		[CompilerGenerated]
		private sealed class Class186
		{
			// Token: 0x06000C9D RID: 3229 RVA: 0x0004990C File Offset: 0x00047B0C
			internal bool method_0(Class264 class264_0)
			{
				return class264_0.Id == this.int_0;
			}

			// Token: 0x04000531 RID: 1329
			public int int_0;
		}

		// Token: 0x02000134 RID: 308
		[CompilerGenerated]
		private sealed class Class187
		{
			// Token: 0x06000C9F RID: 3231 RVA: 0x0004992C File Offset: 0x00047B2C
			internal bool method_0(Class264 class264_0)
			{
				return class264_0.Id == (int)this.enum3_0;
			}

			// Token: 0x04000532 RID: 1330
			public Enum3 enum3_0;
		}
	}
}
