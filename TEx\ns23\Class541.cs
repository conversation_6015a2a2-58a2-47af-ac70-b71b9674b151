﻿using System;
using System.IO;
using System.Security;
using System.Windows.Forms;
using ns11;
using ns13;
using ns16;
using ns24;
using ns30;
using ns31;

namespace ns23
{
	// Token: 0x0200041B RID: 1051
	internal sealed class Class541 : Class540
	{
		// Token: 0x06002870 RID: 10352 RVA: 0x00105DEC File Offset: 0x00103FEC
		protected override Guid vmethod_3()
		{
			Guid result;
			try
			{
				string text = Class547.smethod_0("AnonymousID");
				if (text.Length == 0)
				{
					Guid guid = Guid.NewGuid();
					Class547.smethod_1("AnonymousID", guid.ToString("B"));
					if (Class547.smethod_0("AnonymousID").Length > 0)
					{
						result = guid;
					}
					else
					{
						result = Guid.Empty;
					}
				}
				else
				{
					result = new Guid(text);
				}
			}
			catch
			{
				result = Guid.Empty;
			}
			return result;
		}

		// Token: 0x06002871 RID: 10353 RVA: 0x0000FD22 File Offset: 0x0000DF22
		protected override void vmethod_2(EventArgs36 eventArgs36_0)
		{
			new SecurityExceptionForm(eventArgs36_0).ShowDialog();
		}

		// Token: 0x06002872 RID: 10354 RVA: 0x00105E6C File Offset: 0x0010406C
		protected override void vmethod_0(EventArgs35 eventArgs35_0)
		{
			string text = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + "\\TradingExer\\Log\\TEx.log";
			if (new FileInfo(text).Exists)
			{
				eventArgs35_0.method_8("TEx Log File", text);
			}
			new ExceptionReportingForm(this, eventArgs35_0).ShowDialog();
		}

		// Token: 0x06002873 RID: 10355 RVA: 0x0000FD30 File Offset: 0x0000DF30
		protected override void vmethod_1(EventArgs34 eventArgs34_0)
		{
			MessageBox.Show(eventArgs34_0.FatalException.ToString(), string.Format("{0} Fatal Error", "『交易练习者』"), MessageBoxButtons.OK, MessageBoxIcon.Hand);
		}

		// Token: 0x06002874 RID: 10356 RVA: 0x00105EB4 File Offset: 0x001040B4
		public static bool smethod_5()
		{
			bool result;
			try
			{
				Class540.smethod_0(new Class541());
				result = true;
			}
			catch (SecurityException)
			{
				try
				{
					Application.EnableVisualStyles();
					new SecurityExceptionForm(new EventArgs36(string.Format("{0} cannot initialize itself because some permissions are not granted.\n\nYou probably try to launch {0} in a partial-trust situation. It's usually the case when the application is hosted on a network share.\n\nYou need to run {0} in full-trust, or at least grant it the UnmanagedCode security permission.\n\nTo grant this application the required permission, contact your system administrator, or use the Microsoft .NET Framework Configuration tool.", "『交易练习者』"), false))
					{
						ShowInTaskbar = true
					}.ShowDialog();
				}
				catch (Exception ex)
				{
					MessageBox.Show(ex.ToString(), string.Format("{0} Fatal Error", "『交易练习者』"), MessageBoxButtons.OK, MessageBoxIcon.Hand);
				}
				result = false;
			}
			return result;
		}
	}
}
