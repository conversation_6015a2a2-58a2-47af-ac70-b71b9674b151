﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns17;
using ns28;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000216 RID: 534
	public sealed class FilterCondItem : UserControl
	{
		// Token: 0x060015E8 RID: 5608 RVA: 0x00008D0D File Offset: 0x00006F0D
		public FilterCondItem()
		{
			this.InitializeComponent();
		}

		// Token: 0x060015E9 RID: 5609 RVA: 0x000924D4 File Offset: 0x000906D4
		public FilterCondItem(FilterCond cond)
		{
			this.InitializeComponent();
			this.method_0();
			this.tablePanel_bg.BackColor = Color.FromKnownColor(KnownColor.Transparent);
			this.BackColor = Color.FromKnownColor(KnownColor.Transparent);
			this.method_1();
			this.FilterCond = cond;
			this.CondName = cond.Name;
			this.UnitType = cond.UnitType;
			this.ComparisonOpt = cond.Opt;
			if (cond != null && cond.Value != null)
			{
				this.txtBox_CondVal.Text = Convert.ToString(cond.Value);
			}
			base.Margin = new Padding(1);
			base.Padding = new Padding(0);
		}

		// Token: 0x060015EA RID: 5610 RVA: 0x0009258C File Offset: 0x0009078C
		private void method_0()
		{
			if (!TApp.IsHighDpiScreen)
			{
				Font font = new Font("SimSun", (float)(11.25 / TApp.DpiScale), FontStyle.Regular, GraphicsUnit.Point, 134);
				this.label_Units.Font = font;
				this.label_CondName.Font = font;
				this.cmbBox_Ops.Font = font;
			}
		}

		// Token: 0x060015EB RID: 5611 RVA: 0x000925E8 File Offset: 0x000907E8
		private void method_1()
		{
			this.list_0 = new List<ComboBoxItem>
			{
				new ComboBoxItem("大于", ComparisonOpt.Bigger),
				new ComboBoxItem("小于", ComparisonOpt.Less),
				new ComboBoxItem("等于", ComparisonOpt.Equal),
				new ComboBoxItem("大于等于", ComparisonOpt.BiggerOrEqual),
				new ComboBoxItem("小于等于", ComparisonOpt.LessOrEqual)
			};
			this.cmbBox_Ops.Items.Clear();
			this.cmbBox_Ops.DataSource = this.list_0;
			this.cmbBox_Ops.SelectedIndex = 0;
			this.cmbBox_Ops.SelectedIndexChanged += this.cmbBox_Ops_SelectedIndexChanged;
			this.toolTip_0 = new ToolTip();
			this.panel_DelIcon.Click += this.panel_DelIcon_Click;
			this.panel_DelIcon.MouseEnter += this.panel_DelIcon_MouseEnter;
			this.panel_DelIcon.MouseLeave += this.panel_DelIcon_MouseLeave;
			base.ParentChanged += this.FilterCondItem_ParentChanged;
			this.txtBox_CondVal.Enter += this.txtBox_CondVal_Enter;
			this.txtBox_CondVal.MouseHover += this.txtBox_CondVal_MouseHover;
			this.txtBox_CondVal.Leave += this.txtBox_CondVal_Leave;
		}

		// Token: 0x060015EC RID: 5612 RVA: 0x0009275C File Offset: 0x0009095C
		private void cmbBox_Ops_SelectedIndexChanged(object sender, EventArgs e)
		{
			int selectedIndex = this.cmbBox_Ops.SelectedIndex;
			ComboBoxItem comboBoxItem = this.list_0[selectedIndex];
			this.FilterCond.Opt = (ComparisonOpt)comboBoxItem.Value;
		}

		// Token: 0x060015ED RID: 5613 RVA: 0x00008D1D File Offset: 0x00006F1D
		private void method_2(object sender, EventArgs e)
		{
			this.method_3();
		}

		// Token: 0x060015EE RID: 5614 RVA: 0x0009279C File Offset: 0x0009099C
		public void method_3()
		{
			Color foreColor = Base.UI.smethod_35();
			this.label_CondName.ForeColor = foreColor;
			this.label_Units.ForeColor = foreColor;
		}

		// Token: 0x060015EF RID: 5615 RVA: 0x000927CC File Offset: 0x000909CC
		private void panel_DelIcon_Click(object sender, EventArgs e)
		{
			Control parent = base.Parent;
			if (parent != null)
			{
				parent.SizeChanged -= this.method_5;
				base.ParentChanged -= this.FilterCondItem_ParentChanged;
				parent.Controls.Remove(this);
			}
		}

		// Token: 0x060015F0 RID: 5616 RVA: 0x00008D27 File Offset: 0x00006F27
		private void txtBox_CondVal_MouseHover(object sender, EventArgs e)
		{
			this.method_4();
		}

		// Token: 0x060015F1 RID: 5617 RVA: 0x00008D27 File Offset: 0x00006F27
		private void txtBox_CondVal_Enter(object sender, EventArgs e)
		{
			this.method_4();
		}

		// Token: 0x060015F2 RID: 5618 RVA: 0x00092818 File Offset: 0x00090A18
		private void txtBox_CondVal_Leave(object sender, EventArgs e)
		{
			try
			{
				double? value = null;
				string text = this.txtBox_CondVal.Text;
				if (!string.IsNullOrEmpty(text))
				{
					value = new double?(Convert.ToDouble(text));
					this.FilterCond.Value = value;
				}
				else
				{
					this.FilterCond.Value = null;
				}
			}
			catch
			{
				MessageBox.Show("请输入有效数字！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				this.txtBox_CondVal.Focus();
			}
		}

		// Token: 0x060015F3 RID: 5619 RVA: 0x00008D31 File Offset: 0x00006F31
		private void method_4()
		{
			if (!string.IsNullOrEmpty(this.FilterCond.Hint))
			{
				this.toolTip_0.SetToolTip(this.txtBox_CondVal, this.FilterCond.Hint);
			}
		}

		// Token: 0x060015F4 RID: 5620 RVA: 0x00008D63 File Offset: 0x00006F63
		private void panel_DelIcon_MouseEnter(object sender, EventArgs e)
		{
			this.toolTip_0.SetToolTip(this.panel_DelIcon, "移除条件");
			this.panel_DelIcon.BackgroundImage = Class372.remove_blue_16x;
		}

		// Token: 0x060015F5 RID: 5621 RVA: 0x00008D8D File Offset: 0x00006F8D
		private void panel_DelIcon_MouseLeave(object sender, EventArgs e)
		{
			this.toolTip_0.SetToolTip(this.panel_DelIcon, null);
			this.panel_DelIcon.BackgroundImage = Class372.remove_gray_16x;
		}

		// Token: 0x060015F6 RID: 5622 RVA: 0x000928A8 File Offset: 0x00090AA8
		private void FilterCondItem_ParentChanged(object sender, EventArgs e)
		{
			Control parent = base.Parent;
			if (parent != null)
			{
				int verticalScrollBarWidth = SystemInformation.VerticalScrollBarWidth;
				base.Width = parent.Width - SystemInformation.VerticalScrollBarWidth - 2;
				parent.SizeChanged += this.method_5;
			}
		}

		// Token: 0x060015F7 RID: 5623 RVA: 0x00008DB3 File Offset: 0x00006FB3
		private void method_5(object sender, EventArgs e)
		{
			base.Width = (sender as Control).Width - SystemInformation.VerticalScrollBarWidth - 2;
		}

		// Token: 0x1700038C RID: 908
		// (get) Token: 0x060015F8 RID: 5624 RVA: 0x000928F0 File Offset: 0x00090AF0
		// (set) Token: 0x060015F9 RID: 5625 RVA: 0x00008DD0 File Offset: 0x00006FD0
		public FilterCond FilterCond { get; set; }

		// Token: 0x1700038D RID: 909
		// (get) Token: 0x060015FA RID: 5626 RVA: 0x00092908 File Offset: 0x00090B08
		// (set) Token: 0x060015FB RID: 5627 RVA: 0x00008DDB File Offset: 0x00006FDB
		public string CondName
		{
			get
			{
				return this.label_CondName.Text;
			}
			set
			{
				this.label_CondName.Text = value;
			}
		}

		// Token: 0x1700038E RID: 910
		// (get) Token: 0x060015FC RID: 5628 RVA: 0x00092924 File Offset: 0x00090B24
		// (set) Token: 0x060015FD RID: 5629 RVA: 0x0009293C File Offset: 0x00090B3C
		public Enum20 UnitType
		{
			get
			{
				return this.enum20_0;
			}
			set
			{
				if (value == Enum20.const_1)
				{
					this.label_Units.Text = "(亿)";
				}
				else if (value == Enum20.const_2)
				{
					this.label_Units.Text = "(万)";
				}
				else if (value == Enum20.const_3)
				{
					this.label_Units.Text = "(%)";
				}
				else
				{
					this.label_Units.Text = "";
				}
				this.enum20_0 = value;
			}
		}

		// Token: 0x1700038F RID: 911
		// (get) Token: 0x060015FE RID: 5630 RVA: 0x000929A4 File Offset: 0x00090BA4
		// (set) Token: 0x060015FF RID: 5631 RVA: 0x000929D8 File Offset: 0x00090BD8
		public ComparisonOpt ComparisonOpt
		{
			get
			{
				int selectedIndex = this.cmbBox_Ops.SelectedIndex;
				return (ComparisonOpt)this.list_0[selectedIndex].Value;
			}
			set
			{
				FilterCondItem.Class293 @class = new FilterCondItem.Class293();
				@class.comparisonOpt_0 = value;
				int selectedIndex = this.list_0.FindIndex(new Predicate<ComboBoxItem>(@class.method_0));
				this.cmbBox_Ops.SelectedIndex = selectedIndex;
			}
		}

		// Token: 0x17000390 RID: 912
		// (get) Token: 0x06001600 RID: 5632 RVA: 0x00092A18 File Offset: 0x00090C18
		// (set) Token: 0x06001601 RID: 5633 RVA: 0x00008DEB File Offset: 0x00006FEB
		public double? CondValue
		{
			get
			{
				double? result = null;
				string text = this.txtBox_CondVal.Text;
				if (!string.IsNullOrEmpty(text))
				{
					result = new double?(Convert.ToDouble(text));
				}
				return result;
			}
			set
			{
				if (value != null)
				{
					this.txtBox_CondVal.Text = Convert.ToString(value);
				}
				this.FilterCond.Value = value;
			}
		}

		// Token: 0x17000391 RID: 913
		// (get) Token: 0x06001602 RID: 5634 RVA: 0x00092A54 File Offset: 0x00090C54
		public bool IsInInputState
		{
			get
			{
				return this.txtBox_CondVal.Focused;
			}
		}

		// Token: 0x06001603 RID: 5635 RVA: 0x00008E1A File Offset: 0x0000701A
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001604 RID: 5636 RVA: 0x00092A70 File Offset: 0x00090C70
		private void InitializeComponent()
		{
			this.tablePanel_bg = new TableLayoutPanel();
			this.label_CondName = new Label();
			this.cmbBox_Ops = new ComboBox();
			this.txtBox_CondVal = new TextBox();
			this.label_Units = new Label();
			this.panel_DelIcon = new Panel();
			this.tablePanel_bg.SuspendLayout();
			base.SuspendLayout();
			this.tablePanel_bg.ColumnCount = 5;
			this.tablePanel_bg.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 51f));
			this.tablePanel_bg.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 23f));
			this.tablePanel_bg.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 26f));
			this.tablePanel_bg.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 44f));
			this.tablePanel_bg.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 21f));
			this.tablePanel_bg.Controls.Add(this.label_CondName, 0, 0);
			this.tablePanel_bg.Controls.Add(this.cmbBox_Ops, 1, 0);
			this.tablePanel_bg.Controls.Add(this.txtBox_CondVal, 2, 0);
			this.tablePanel_bg.Controls.Add(this.label_Units, 3, 0);
			this.tablePanel_bg.Controls.Add(this.panel_DelIcon, 4, 0);
			this.tablePanel_bg.Dock = DockStyle.Fill;
			this.tablePanel_bg.Location = new Point(0, 0);
			this.tablePanel_bg.Margin = new Padding(0);
			this.tablePanel_bg.MaximumSize = new Size(500, 26);
			this.tablePanel_bg.MinimumSize = new Size(400, 26);
			this.tablePanel_bg.Name = "tablePanel_bg";
			this.tablePanel_bg.RowCount = 1;
			this.tablePanel_bg.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tablePanel_bg.Size = new Size(400, 26);
			this.tablePanel_bg.TabIndex = 0;
			this.label_CondName.Anchor = AnchorStyles.Left;
			this.label_CondName.AutoSize = true;
			this.label_CondName.Font = new Font("SimSun", 9.5f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_CondName.Location = new Point(3, 5);
			this.label_CondName.Name = "label_CondName";
			this.label_CondName.Size = new Size(120, 16);
			this.label_CondName.TabIndex = 0;
			this.label_CondName.Text = "label_CondName";
			this.cmbBox_Ops.Dock = DockStyle.Fill;
			this.cmbBox_Ops.DropDownStyle = ComboBoxStyle.DropDownList;
			this.cmbBox_Ops.FormattingEnabled = true;
			this.cmbBox_Ops.ItemHeight = 15;
			this.cmbBox_Ops.Location = new Point(170, 0);
			this.cmbBox_Ops.Margin = new Padding(0);
			this.cmbBox_Ops.MinimumSize = new Size(70, 0);
			this.cmbBox_Ops.Name = "cmbBox_Ops";
			this.cmbBox_Ops.Size = new Size(77, 23);
			this.cmbBox_Ops.TabIndex = 1;
			this.txtBox_CondVal.Anchor = (AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
			this.txtBox_CondVal.BorderStyle = BorderStyle.None;
			this.txtBox_CondVal.Font = new Font("Microsoft Sans Serif", 9f);
			this.txtBox_CondVal.Location = new Point(250, 0);
			this.txtBox_CondVal.Margin = new Padding(3, 0, 3, 0);
			this.txtBox_CondVal.MaximumSize = new Size(0, 28);
			this.txtBox_CondVal.MinimumSize = new Size(0, 23);
			this.txtBox_CondVal.Name = "txtBox_CondVal";
			this.txtBox_CondVal.RightToLeft = RightToLeft.No;
			this.txtBox_CondVal.Size = new Size(81, 23);
			this.txtBox_CondVal.TabIndex = 2;
			this.txtBox_CondVal.TextAlign = HorizontalAlignment.Right;
			this.label_Units.Anchor = AnchorStyles.Left;
			this.label_Units.AutoSize = true;
			this.label_Units.Location = new Point(337, 5);
			this.label_Units.Name = "label_Units";
			this.label_Units.Size = new Size(0, 15);
			this.label_Units.TabIndex = 3;
			this.panel_DelIcon.BackgroundImage = Class372.remove_gray_16x;
			this.panel_DelIcon.BackgroundImageLayout = ImageLayout.Center;
			this.panel_DelIcon.Location = new Point(378, 0);
			this.panel_DelIcon.Margin = new Padding(0);
			this.panel_DelIcon.Name = "panel_DelIcon";
			this.panel_DelIcon.Size = new Size(22, 26);
			this.panel_DelIcon.TabIndex = 4;
			base.AutoScaleMode = AutoScaleMode.None;
			this.BackColor = SystemColors.Control;
			base.Controls.Add(this.tablePanel_bg);
			this.DoubleBuffered = true;
			base.Margin = new Padding(0);
			this.MaximumSize = new Size(480, 25);
			this.MinimumSize = new Size(390, 25);
			base.Name = "FilterCondItem";
			base.Size = new Size(390, 25);
			this.tablePanel_bg.ResumeLayout(false);
			this.tablePanel_bg.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x04000B15 RID: 2837
		private List<ComboBoxItem> list_0;

		// Token: 0x04000B16 RID: 2838
		private ToolTip toolTip_0;

		// Token: 0x04000B17 RID: 2839
		[CompilerGenerated]
		private FilterCond filterCond_0;

		// Token: 0x04000B18 RID: 2840
		private Enum20 enum20_0;

		// Token: 0x04000B19 RID: 2841
		private IContainer icontainer_0;

		// Token: 0x04000B1A RID: 2842
		private TableLayoutPanel tablePanel_bg;

		// Token: 0x04000B1B RID: 2843
		private Label label_CondName;

		// Token: 0x04000B1C RID: 2844
		private ComboBox cmbBox_Ops;

		// Token: 0x04000B1D RID: 2845
		private TextBox txtBox_CondVal;

		// Token: 0x04000B1E RID: 2846
		private Label label_Units;

		// Token: 0x04000B1F RID: 2847
		private Panel panel_DelIcon;

		// Token: 0x02000217 RID: 535
		[CompilerGenerated]
		private sealed class Class293
		{
			// Token: 0x06001606 RID: 5638 RVA: 0x00092FF0 File Offset: 0x000911F0
			internal bool method_0(ComboBoxItem comboBoxItem_0)
			{
				return (ComparisonOpt)comboBoxItem_0.Value == this.comparisonOpt_0;
			}

			// Token: 0x04000B20 RID: 2848
			public ComparisonOpt comparisonOpt_0;
		}
	}
}
