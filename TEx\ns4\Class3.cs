﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace ns4
{
	// Token: 0x02000004 RID: 4
	[CompilerGenerated]
	internal sealed class Class3<T, U, V, W, X>
	{
		// Token: 0x17000006 RID: 6
		// (get) Token: 0x0600000E RID: 14 RVA: 0x000100F0 File Offset: 0x0000E2F0
		public T condCsv
		{
			get
			{
				return this.gparam_0;
			}
		}

		// Token: 0x17000007 RID: 7
		// (get) Token: 0x0600000F RID: 15 RVA: 0x00010108 File Offset: 0x0000E308
		public U currDate
		{
			get
			{
				return this.gparam_1;
			}
		}

		// Token: 0x17000008 RID: 8
		// (get) Token: 0x06000010 RID: 16 RVA: 0x00010120 File Offset: 0x0000E320
		public V codes
		{
			get
			{
				return this.gparam_2;
			}
		}

		// Token: 0x17000009 RID: 9
		// (get) Token: 0x06000011 RID: 17 RVA: 0x00010138 File Offset: 0x0000E338
		public W exchgType
		{
			get
			{
				return this.gparam_3;
			}
		}

		// Token: 0x1700000A RID: 10
		// (get) Token: 0x06000012 RID: 18 RVA: 0x00010150 File Offset: 0x0000E350
		public X inclQtRpt
		{
			get
			{
				return this.gparam_4;
			}
		}

		// Token: 0x06000013 RID: 19 RVA: 0x00002A8C File Offset: 0x00000C8C
		[DebuggerHidden]
		public Class3(T gparam_5, U gparam_6, V gparam_7, W gparam_8, X gparam_9)
		{
			this.gparam_0 = gparam_5;
			this.gparam_1 = gparam_6;
			this.gparam_2 = gparam_7;
			this.gparam_3 = gparam_8;
			this.gparam_4 = gparam_9;
		}

		// Token: 0x06000014 RID: 20 RVA: 0x00010168 File Offset: 0x0000E368
		[DebuggerHidden]
		public bool Equals(object obj)
		{
			Class3<T, U, V, W, X> @class = obj as Class3<T, U, V, W, X>;
			bool result;
			if (@class != null && EqualityComparer<T>.Default.Equals(this.gparam_0, @class.gparam_0) && EqualityComparer<U>.Default.Equals(this.gparam_1, @class.gparam_1) && EqualityComparer<V>.Default.Equals(this.gparam_2, @class.gparam_2) && EqualityComparer<W>.Default.Equals(this.gparam_3, @class.gparam_3))
			{
				result = EqualityComparer<X>.Default.Equals(this.gparam_4, @class.gparam_4);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06000015 RID: 21 RVA: 0x00010200 File Offset: 0x0000E400
		[DebuggerHidden]
		public int GetHashCode()
		{
			return ((((-********** + EqualityComparer<T>.Default.GetHashCode(this.gparam_0)) * -********** + EqualityComparer<U>.Default.GetHashCode(this.gparam_1)) * -********** + EqualityComparer<V>.Default.GetHashCode(this.gparam_2)) * -********** + EqualityComparer<W>.Default.GetHashCode(this.gparam_3)) * -********** + EqualityComparer<X>.Default.GetHashCode(this.gparam_4);
		}

		// Token: 0x06000016 RID: 22 RVA: 0x00010284 File Offset: 0x0000E484
		[DebuggerHidden]
		public string ToString()
		{
			IFormatProvider provider = null;
			string format = "{{ condCsv = {0}, currDate = {1}, codes = {2}, exchgType = {3}, inclQtRpt = {4} }}";
			object[] array = new object[5];
			int num = 0;
			T t = this.gparam_0;
			ref T ptr = ref t;
			T t2 = default(T);
			object obj;
			if (t2 == null)
			{
				t2 = t;
				ptr = ref t2;
				if (t2 == null)
				{
					obj = null;
					goto IL_46;
				}
			}
			obj = ptr.ToString();
			IL_46:
			array[num] = obj;
			int num2 = 1;
			U u = this.gparam_1;
			ref U ptr2 = ref u;
			U u2 = default(U);
			object obj2;
			if (u2 == null)
			{
				u2 = u;
				ptr2 = ref u2;
				if (u2 == null)
				{
					obj2 = null;
					goto IL_81;
				}
			}
			obj2 = ptr2.ToString();
			IL_81:
			array[num2] = obj2;
			int num3 = 2;
			V v = this.gparam_2;
			ref V ptr3 = ref v;
			V v2 = default(V);
			object obj3;
			if (v2 == null)
			{
				v2 = v;
				ptr3 = ref v2;
				if (v2 == null)
				{
					obj3 = null;
					goto IL_C0;
				}
			}
			obj3 = ptr3.ToString();
			IL_C0:
			array[num3] = obj3;
			int num4 = 3;
			W w = this.gparam_3;
			ref W ptr4 = ref w;
			W w2 = default(W);
			object obj4;
			if (w2 == null)
			{
				w2 = w;
				ptr4 = ref w2;
				if (w2 == null)
				{
					obj4 = null;
					goto IL_FF;
				}
			}
			obj4 = ptr4.ToString();
			IL_FF:
			array[num4] = obj4;
			int num5 = 4;
			X x = this.gparam_4;
			ref X ptr5 = ref x;
			X x2 = default(X);
			object obj5;
			if (x2 == null)
			{
				x2 = x;
				ptr5 = ref x2;
				if (x2 == null)
				{
					obj5 = null;
					goto IL_13E;
				}
			}
			obj5 = ptr5.ToString();
			IL_13E:
			array[num5] = obj5;
			return string.Format(provider, format, array);
		}

		// Token: 0x04000006 RID: 6
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly T gparam_0;

		// Token: 0x04000007 RID: 7
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly U gparam_1;

		// Token: 0x04000008 RID: 8
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly V gparam_2;

		// Token: 0x04000009 RID: 9
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly W gparam_3;

		// Token: 0x0400000A RID: 10
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly X gparam_4;
	}
}
