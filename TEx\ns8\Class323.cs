﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using TEx;
using TEx.Comn;
using TEx.Util;

namespace ns8
{
	// Token: 0x0200025C RID: 604
	internal sealed class Class323
	{
		// Token: 0x17000458 RID: 1112
		// (get) Token: 0x06001A8A RID: 6794 RVA: 0x000B5DF8 File Offset: 0x000B3FF8
		// (set) Token: 0x06001A8B RID: 6795 RVA: 0x0000B108 File Offset: 0x00009308
		public string UserName { get; set; }

		// Token: 0x17000459 RID: 1113
		// (get) Token: 0x06001A8C RID: 6796 RVA: 0x000B5E10 File Offset: 0x000B4010
		// (set) Token: 0x06001A8D RID: 6797 RVA: 0x0000B113 File Offset: 0x00009313
		public TExPackage? TExPkg { get; set; }

		// Token: 0x1700045A RID: 1114
		// (get) Token: 0x06001A8E RID: 6798 RVA: 0x000B5E28 File Offset: 0x000B4028
		// (set) Token: 0x06001A8F RID: 6799 RVA: 0x0000B11E File Offset: 0x0000931E
		public DateTime CurrDate { get; set; }

		// Token: 0x1700045B RID: 1115
		// (get) Token: 0x06001A90 RID: 6800 RVA: 0x000B5E40 File Offset: 0x000B4040
		// (set) Token: 0x06001A91 RID: 6801 RVA: 0x0000B129 File Offset: 0x00009329
		public Dictionary<string, SortableBindingList<ShowMktSymb>> DgvDataSources { get; set; }

		// Token: 0x1700045C RID: 1116
		// (get) Token: 0x06001A92 RID: 6802 RVA: 0x000B5E58 File Offset: 0x000B4058
		// (set) Token: 0x06001A93 RID: 6803 RVA: 0x0000B134 File Offset: 0x00009334
		public Dictionary<int, ShowMktSymb> ShowMktSymbDict { get; set; }

		// Token: 0x1700045D RID: 1117
		// (get) Token: 0x06001A94 RID: 6804 RVA: 0x000B5E70 File Offset: 0x000B4070
		// (set) Token: 0x06001A95 RID: 6805 RVA: 0x0000B13F File Offset: 0x0000933F
		public string ShowMktSymbCsv { get; set; }

		// Token: 0x04000D64 RID: 3428
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04000D65 RID: 3429
		[CompilerGenerated]
		private TExPackage? nullable_0;

		// Token: 0x04000D66 RID: 3430
		[CompilerGenerated]
		private DateTime dateTime_0;

		// Token: 0x04000D67 RID: 3431
		[CompilerGenerated]
		private Dictionary<string, SortableBindingList<ShowMktSymb>> dictionary_0;

		// Token: 0x04000D68 RID: 3432
		[CompilerGenerated]
		private Dictionary<int, ShowMktSymb> dictionary_1;

		// Token: 0x04000D69 RID: 3433
		[CompilerGenerated]
		private string string_1;
	}
}
