﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns11;
using ns19;
using ns23;
using TEx.Inds;

namespace TEx.SIndicator
{
	// Token: 0x020002D6 RID: 726
	public sealed partial class FormIndMgr : Form
	{
		// Token: 0x140000A0 RID: 160
		// (add) Token: 0x06002061 RID: 8289 RVA: 0x000DE2E0 File Offset: 0x000DC4E0
		// (remove) Token: 0x06002062 RID: 8290 RVA: 0x000DE318 File Offset: 0x000DC518
		public event EventHandler ShownIndEditer
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06002063 RID: 8291 RVA: 0x000DE350 File Offset: 0x000DC550
		private void method_0(FormIndEditer formIndEditer_0, Enum25 enum25_0)
		{
			EventArgs32 e = new EventArgs32(formIndEditer_0, enum25_0);
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x06002064 RID: 8292 RVA: 0x000DE37C File Offset: 0x000DC57C
		public FormIndMgr()
		{
			base.StartPosition = FormStartPosition.CenterScreen;
			this.InitializeComponent();
			Base.UI.smethod_54(this);
			Base.UI.smethod_55(this.dataGridViewAllInd);
			base.Load += this.FormIndMgr_Load;
		}

		// Token: 0x06002065 RID: 8293 RVA: 0x000DE3CC File Offset: 0x000DC5CC
		private void FormIndMgr_Load(object sender, EventArgs e)
		{
			string text = "添加分组";
			ToolStripMenuItem toolStripMenuItem = new ToolStripMenuItem();
			toolStripMenuItem.Text = text;
			toolStripMenuItem.Click += this.method_6;
			this.toolStripDropDownButton1.DropDownItems.Add(toolStripMenuItem);
			ToolStripMenuItem toolStripMenuItem2 = new ToolStripMenuItem();
			toolStripMenuItem2.Text = "删除分组";
			toolStripMenuItem2.Click += this.method_5;
			this.toolStripDropDownButton1.DropDownItems.Add(toolStripMenuItem2);
			this.dataGridViewAllInd.ColumnHeadersVisible = false;
			this.dataGridViewAllInd.RowHeadersVisible = false;
			this.dataGridViewAllInd.ReadOnly = true;
			this.dataGridViewAllInd.CellBorderStyle = DataGridViewCellBorderStyle.None;
			this.dataGridViewAllInd.DoubleClick += this.dataGridViewAllInd_DoubleClick;
			this.dataGridViewAllInd.Click += this.dataGridViewAllInd_Click;
			this.treeViewGroup.NodeMouseDoubleClick += this.treeViewGroup_NodeMouseDoubleClick;
			this.treeViewGroup.NodeMouseClick += this.treeViewGroup_NodeMouseClick;
			this.method_1("");
			this.textBoxInstruction.ReadOnly = true;
			this.buttonMdf.Enabled = false;
			this.buttonDel.Enabled = false;
		}

		// Token: 0x06002066 RID: 8294 RVA: 0x000DE504 File Offset: 0x000DC704
		private void method_1(string string_0)
		{
			this.treeViewGroup.Nodes.Clear();
			foreach (UserDefineIndGroup userDefineIndGroup in UserDefineFileMgr.UDGList)
			{
				TreeNode treeNode = new TreeNode(userDefineIndGroup.Group);
				treeNode.Name = userDefineIndGroup.Group;
				this.treeViewGroup.Nodes.Add(treeNode);
				foreach (UserDefineIndScript userDefineIndScript in userDefineIndGroup.UDSList)
				{
					TreeNode treeNode2 = new TreeNode(userDefineIndScript.NameAndScript);
					treeNode2.Name = userDefineIndScript.Name;
					treeNode.Nodes.Add(treeNode2);
					if (string_0 == userDefineIndScript.Name)
					{
						treeNode.Expand();
						this.treeViewGroup.SelectedNode = treeNode2;
						this.treeViewGroup.Focus();
					}
				}
			}
			this.method_9(string_0);
		}

		// Token: 0x06002067 RID: 8295 RVA: 0x000DE630 File Offset: 0x000DC830
		private void treeViewGroup_NodeMouseClick(object sender, TreeNodeMouseClickEventArgs e)
		{
			if (e.Clicks == 1 && e.Node.Level == 0 && e.Button == MouseButtons.Right)
			{
				this.treeViewGroup.SelectedNode = e.Node;
				this.treeViewGroup.ContextMenuStrip = null;
				ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
				ToolStripMenuItem toolStripMenuItem = new ToolStripMenuItem();
				toolStripMenuItem.Text = "新建公式";
				toolStripMenuItem.Tag = e.Node.Name;
				toolStripMenuItem.Click += this.method_3;
				toolStripMenuItem.VisibleChanged += this.method_2;
				contextMenuStrip.Items.Add(toolStripMenuItem);
				this.treeViewGroup.ContextMenuStrip = contextMenuStrip;
			}
			else if (e.Clicks == 1 && e.Node.Level == 1)
			{
				UserDefineIndScript userDefineIndScript = this.method_7(e.Node.Text.Split(new char[]
				{
					' '
				}).First<string>());
				if (userDefineIndScript != null)
				{
					this.buttonMdf.Enabled = true;
					this.buttonDel.Enabled = true;
					this.textBoxInstruction.Text = userDefineIndScript.Instruction.Replace("\n", "\r\n");
					if (this.textBoxInstruction.Text != "")
					{
						this.textBoxInstruction.SelectionStart = 0;
						this.textBoxInstruction.ScrollToCaret();
					}
				}
				else
				{
					this.buttonDel.Enabled = false;
					this.buttonMdf.Enabled = false;
				}
			}
			if (e.Button == MouseButtons.Left)
			{
				this.treeViewGroup.ContextMenuStrip = null;
			}
		}

		// Token: 0x06002068 RID: 8296 RVA: 0x0000D1C2 File Offset: 0x0000B3C2
		private void method_2(object sender, EventArgs e)
		{
			if (!(sender as ToolStripMenuItem).Visible)
			{
				this.treeViewGroup.ContextMenuStrip = null;
			}
		}

		// Token: 0x06002069 RID: 8297 RVA: 0x000DE7D4 File Offset: 0x000DC9D4
		private void method_3(object sender, EventArgs e)
		{
			string string_ = (sender as ToolStripMenuItem).Tag.ToString();
			this.method_4(null, string_);
			this.treeViewGroup.ContextMenuStrip = null;
		}

		// Token: 0x0600206A RID: 8298 RVA: 0x000DE808 File Offset: 0x000DCA08
		private void dataGridViewAllInd_Click(object sender, EventArgs e)
		{
			this.dataGridViewAllInd.CurrentRow.Selected = true;
			string string_ = this.dataGridViewAllInd.CurrentRow.Cells[0].Value.ToString();
			UserDefineIndScript userDefineIndScript = this.method_7(string_);
			if (userDefineIndScript != null)
			{
				this.textBoxInstruction.Text = "";
				foreach (string str in userDefineIndScript.Instruction.Split(new char[]
				{
					'\n'
				}))
				{
					this.textBoxInstruction.AppendText(str + "\r\n");
				}
			}
		}

		// Token: 0x0600206B RID: 8299 RVA: 0x000DE8A8 File Offset: 0x000DCAA8
		private void method_4(UserDefineIndScript userDefineIndScript_0, string string_0)
		{
			FormIndEditer formIndEditer = new FormIndEditer();
			if (userDefineIndScript_0 != null)
			{
				if (formIndEditer.method_8(userDefineIndScript_0))
				{
					this.method_0(formIndEditer, Enum25.flag_1);
					formIndEditer.IsAddNew = new bool?(false);
					formIndEditer.ShowDialog();
					this.method_1(userDefineIndScript_0.Name);
				}
			}
			else if (!string.IsNullOrEmpty(string_0))
			{
				formIndEditer.Group = string_0;
				this.method_0(formIndEditer, Enum25.flag_0);
				formIndEditer.IsAddNew = new bool?(true);
				formIndEditer.ShowDialog();
				this.method_1(formIndEditer.AddOrMdfIndName);
			}
		}

		// Token: 0x0600206C RID: 8300 RVA: 0x000DE928 File Offset: 0x000DCB28
		private void dataGridViewAllInd_DoubleClick(object sender, EventArgs e)
		{
			int index = this.dataGridViewAllInd.CurrentRow.Index;
			if (index < this.bindingList_0.Count)
			{
				string name = this.bindingList_0[index].Name;
				UserDefineIndScript userDefineIndScript = this.method_7(name);
				if (userDefineIndScript != null)
				{
					this.method_4(userDefineIndScript, "");
				}
				return;
			}
			throw new Exception(string.Format("选择的行号{0}不小于List的总数", index));
		}

		// Token: 0x0600206D RID: 8301 RVA: 0x000DE998 File Offset: 0x000DCB98
		private void method_5(object sender, EventArgs e)
		{
			TreeNode selectedNode = this.treeViewGroup.SelectedNode;
			if (selectedNode == null)
			{
				MessageBox.Show("请选择需要删除的分组。");
			}
			else if (selectedNode.Level == 0)
			{
				if (MessageBox.Show("您将要删除此分组内全部的指标，请确认选择。", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) == DialogResult.OK)
				{
					string name = selectedNode.Name;
					this.treeViewGroup.Nodes.Remove(selectedNode);
					string b = UserDefineFileMgr.smethod_20(name);
					for (int i = 0; i < this.treeViewGroup.Nodes.Count; i++)
					{
						if (this.treeViewGroup.Nodes[i].Name == b)
						{
							this.treeViewGroup.SelectedNode = this.treeViewGroup.Nodes[i];
						}
					}
				}
			}
			else
			{
				MessageBox.Show("请选择分组信息。");
			}
		}

		// Token: 0x0600206E RID: 8302 RVA: 0x000DEA6C File Offset: 0x000DCC6C
		private void method_6(object sender, EventArgs e)
		{
			IEnumerable<string> source = UserDefineFileMgr.UDGList.GroupBy(new Func<UserDefineIndGroup, string>(FormIndMgr.<>c.<>9.method_0)).Select(new Func<IGrouping<string, UserDefineIndGroup>, string>(FormIndMgr.<>c.<>9.method_1));
			FormGroupName formGroupName = new FormGroupName();
			formGroupName.list_0 = source.ToList<string>();
			formGroupName.ShowDialog();
			if (formGroupName.bool_0)
			{
				this.treeViewGroup.Nodes.Add(formGroupName.GroupName);
				UserDefineFileMgr.smethod_11(formGroupName.GroupName);
			}
		}

		// Token: 0x0600206F RID: 8303 RVA: 0x000DEB0C File Offset: 0x000DCD0C
		private void treeViewGroup_NodeMouseDoubleClick(object sender, TreeNodeMouseClickEventArgs e)
		{
			if (e.Clicks == 2 && e.Node.Level == 1)
			{
				UserDefineIndScript userDefineIndScript = this.method_7(e.Node.Text.Split(new char[]
				{
					' '
				}).First<string>());
				if (userDefineIndScript != null)
				{
					this.method_4(userDefineIndScript, "");
				}
			}
		}

		// Token: 0x06002070 RID: 8304 RVA: 0x000DEB68 File Offset: 0x000DCD68
		private void buttonAdd_Click(object sender, EventArgs e)
		{
			TreeNode selectedNode = this.treeViewGroup.SelectedNode;
			string string_;
			if (selectedNode != null)
			{
				if (selectedNode.Level == 0)
				{
					string_ = selectedNode.Text;
				}
				else
				{
					if (selectedNode.Level != 1)
					{
						throw new Exception(selectedNode.Name + "节点深度不正确");
					}
					string_ = selectedNode.Parent.Text;
				}
			}
			else
			{
				string_ = "自定义";
			}
			this.method_4(null, string_);
		}

		// Token: 0x06002071 RID: 8305 RVA: 0x000DEBD8 File Offset: 0x000DCDD8
		private void buttonMdf_Click(object sender, EventArgs e)
		{
			TreeNode selectedNode = this.treeViewGroup.SelectedNode;
			this.method_8(selectedNode, Enum25.flag_1);
		}

		// Token: 0x06002072 RID: 8306 RVA: 0x000DEBFC File Offset: 0x000DCDFC
		private UserDefineIndScript method_7(string string_0)
		{
			UserDefineIndScript result;
			foreach (UserDefineIndGroup userDefineIndGroup in UserDefineFileMgr.UDGList)
			{
				foreach (UserDefineIndScript userDefineIndScript in userDefineIndGroup.UDSList)
				{
					if (userDefineIndScript.Name == string_0)
					{
						result = userDefineIndScript;
						goto IL_74;
					}
				}
			}
			return null;
			IL_74:
			return result;
		}

		// Token: 0x06002073 RID: 8307 RVA: 0x000DECA0 File Offset: 0x000DCEA0
		private void method_8(TreeNode treeNode_0, Enum25 enum25_0)
		{
			if (treeNode_0 != null)
			{
				if (treeNode_0.Level != 1)
				{
					MessageBox.Show("请选择要修改的指标", "注意", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation);
				}
				else
				{
					UserDefineIndScript userDefineIndScript = this.method_7(treeNode_0.Text.Split(new char[]
					{
						' '
					}).First<string>());
					if (userDefineIndScript != null)
					{
						if (enum25_0 == Enum25.flag_2)
						{
							string string_ = UserDefineFileMgr.smethod_19(userDefineIndScript, treeNode_0.Parent.Text);
							this.method_1(string_);
						}
						else if (enum25_0 == Enum25.flag_1)
						{
							this.method_4(userDefineIndScript, "");
						}
					}
				}
			}
			else
			{
				MessageBox.Show("请选择要修改的指标", "注意", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation);
			}
		}

		// Token: 0x06002074 RID: 8308 RVA: 0x000DED3C File Offset: 0x000DCF3C
		private void buttonDel_Click(object sender, EventArgs e)
		{
			TreeNode selectedNode = this.treeViewGroup.SelectedNode;
			this.method_8(selectedNode, Enum25.flag_2);
		}

		// Token: 0x06002075 RID: 8309 RVA: 0x00004268 File Offset: 0x00002468
		private void buttonClose_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x06002076 RID: 8310 RVA: 0x000DED60 File Offset: 0x000DCF60
		private void method_9(string string_0)
		{
			this.bindingList_0.Clear();
			List<NameScript> list = new List<NameScript>();
			foreach (UserDefineIndGroup userDefineIndGroup in UserDefineFileMgr.UDGList)
			{
				foreach (UserDefineIndScript userDefineIndScript in userDefineIndGroup.UDSList)
				{
					NameScript item = new NameScript(userDefineIndScript.Name, userDefineIndScript.Script);
					list.Add(item);
				}
			}
			list.Sort();
			this.bindingList_0 = new BindingList<NameScript>(list);
			this.dataGridViewAllInd.DataSource = this.bindingList_0;
			if (this.dataGridViewAllInd.ColumnCount == 2)
			{
				this.dataGridViewAllInd.Columns[0].AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
				this.dataGridViewAllInd.Columns[1].AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
			}
			for (int i = 0; i < this.dataGridViewAllInd.RowCount; i++)
			{
				if (this.dataGridViewAllInd.Rows[i].Cells[0].Value as string == string_0)
				{
					this.dataGridViewAllInd.FirstDisplayedScrollingRowIndex = i;
					return;
				}
			}
		}

		// Token: 0x06002077 RID: 8311 RVA: 0x0000D1DF File Offset: 0x0000B3DF
		private void tabControl1_Selected(object sender, TabControlEventArgs e)
		{
			if (e.TabPage.Text == "全部")
			{
				this.method_9("");
			}
		}

		// Token: 0x06002078 RID: 8312 RVA: 0x0000D205 File Offset: 0x0000B405
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000FE7 RID: 4071
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000FE8 RID: 4072
		private BindingList<NameScript> bindingList_0 = new BindingList<NameScript>();

		// Token: 0x04000FE9 RID: 4073
		private IContainer icontainer_0;
	}
}
