﻿using System;
using System.Security;

namespace ns11
{
	// Token: 0x020003FE RID: 1022
	internal sealed class EventArgs36 : EventArgs
	{
		// Token: 0x170006D6 RID: 1750
		// (get) Token: 0x060027C1 RID: 10177 RVA: 0x0000F433 File Offset: 0x0000D633
		public SecurityException SecurityException
		{
			get
			{
				return this.securityException_0;
			}
		}

		// Token: 0x170006D7 RID: 1751
		// (get) Token: 0x060027C2 RID: 10178 RVA: 0x0000F43B File Offset: 0x0000D63B
		public string SecurityMessage
		{
			get
			{
				return this.string_0;
			}
		}

		// Token: 0x170006D8 RID: 1752
		// (get) Token: 0x060027C3 RID: 10179 RVA: 0x0000F443 File Offset: 0x0000D643
		public bool CanContinue
		{
			get
			{
				return this.bool_2;
			}
		}

		// Token: 0x170006D9 RID: 1753
		// (get) Token: 0x060027C4 RID: 10180 RVA: 0x0000F44B File Offset: 0x0000D64B
		// (set) Token: 0x060027C5 RID: 10181 RVA: 0x0000F453 File Offset: 0x0000D653
		public bool TryToContinue
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x170006DA RID: 1754
		// (get) Token: 0x060027C6 RID: 10182 RVA: 0x0000F45C File Offset: 0x0000D65C
		// (set) Token: 0x060027C7 RID: 10183 RVA: 0x0000F464 File Offset: 0x0000D664
		public bool ReportException
		{
			get
			{
				return this.bool_1;
			}
			set
			{
				this.bool_1 = value;
			}
		}

		// Token: 0x060027C8 RID: 10184 RVA: 0x0000F46D File Offset: 0x0000D66D
		public EventArgs36(SecurityException securityException_1)
		{
			this.securityException_0 = securityException_1;
		}

		// Token: 0x060027C9 RID: 10185 RVA: 0x0000F48E File Offset: 0x0000D68E
		public EventArgs36(SecurityException securityException_1, bool bool_3) : this(securityException_1)
		{
			this.bool_2 = bool_3;
		}

		// Token: 0x060027CA RID: 10186 RVA: 0x0000F49E File Offset: 0x0000D69E
		public EventArgs36(string string_1, bool bool_3) : this(new SecurityException(string_1), bool_3)
		{
			this.string_0 = string_1;
		}

		// Token: 0x040013B9 RID: 5049
		private SecurityException securityException_0;

		// Token: 0x040013BA RID: 5050
		private string string_0 = string.Empty;

		// Token: 0x040013BB RID: 5051
		private bool bool_0;

		// Token: 0x040013BC RID: 5052
		private bool bool_1;

		// Token: 0x040013BD RID: 5053
		private bool bool_2 = true;
	}
}
