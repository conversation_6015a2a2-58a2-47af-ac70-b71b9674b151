﻿using System;
using System.Collections.Generic;
using ns14;
using ns30;
using ns9;
using TEx.SIndicator;

namespace ns12
{
	// Token: 0x02000318 RID: 792
	internal sealed class Class418 : Class412
	{
		// Token: 0x06002201 RID: 8705 RVA: 0x0000D8A3 File Offset: 0x0000BAA3
		public Class418(HToken htoken_1, Class408 class408_2, Class408 class408_3) : base(htoken_1, class408_2, class408_3)
		{
		}

		// Token: 0x06002202 RID: 8706 RVA: 0x000E8C94 File Offset: 0x000E6E94
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			List<string> list = new List<string>();
			list.Add(this.Token.Symbol.Name);
			Class408 @class = this.Left;
			while (@class.Token.Symbol.HSymbolType != Enum26.const_30)
			{
				Class408 left = @class.Left;
				list.Add(left.Token.Symbol.Name);
				@class = @class.Right;
			}
			return list;
		}
	}
}
