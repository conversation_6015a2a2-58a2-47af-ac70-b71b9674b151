﻿using System;

namespace ns17
{
	// Token: 0x0200027B RID: 635
	internal sealed class EventArgs24 : EventArgs
	{
		// Token: 0x06001B93 RID: 7059 RVA: 0x0000B604 File Offset: 0x00009804
		public EventArgs24(DateTime dateTime_2, DateTime dateTime_3)
		{
			this.dateTime_0 = dateTime_2;
			this.dateTime_1 = dateTime_3;
		}

		// Token: 0x17000487 RID: 1159
		// (get) Token: 0x06001B94 RID: 7060 RVA: 0x000B9CBC File Offset: 0x000B7EBC
		// (set) Token: 0x06001B95 RID: 7061 RVA: 0x0000B61C File Offset: 0x0000981C
		public DateTime OldDate
		{
			get
			{
				return this.dateTime_0;
			}
			set
			{
				this.dateTime_0 = value;
			}
		}

		// Token: 0x17000488 RID: 1160
		// (get) Token: 0x06001B96 RID: 7062 RVA: 0x000B9CD4 File Offset: 0x000B7ED4
		// (set) Token: 0x06001B97 RID: 7063 RVA: 0x0000B627 File Offset: 0x00009827
		public DateTime NewDate
		{
			get
			{
				return this.dateTime_1;
			}
			set
			{
				this.dateTime_1 = value;
			}
		}

		// Token: 0x04000DA3 RID: 3491
		private DateTime dateTime_0;

		// Token: 0x04000DA4 RID: 3492
		private DateTime dateTime_1;
	}
}
