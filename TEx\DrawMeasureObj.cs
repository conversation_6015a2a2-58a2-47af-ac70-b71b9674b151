﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000078 RID: 120
	[Serializable]
	internal sealed class DrawMeasureObj : DrawObj, ISerializable
	{
		// Token: 0x0600044D RID: 1101 RVA: 0x000037AA File Offset: 0x000019AA
		public DrawMeasureObj()
		{
		}

		// Token: 0x0600044E RID: 1102 RVA: 0x00003DC1 File Offset: 0x00001FC1
		public DrawMeasureObj(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = "量度目标";
			base.CanChgColor = true;
			base.IsOneClickLoc = false;
		}

		// Token: 0x0600044F RID: 1103 RVA: 0x000037DC File Offset: 0x000019DC
		protected DrawMeasureObj(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x06000450 RID: 1104 RVA: 0x000037ED File Offset: 0x000019ED
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x06000451 RID: 1105 RVA: 0x0002308C File Offset: 0x0002128C
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			LineObj item = this.method_39(chartCS_1, double_2, string_5);
			list.Add(item);
			double max = chartCS_1.GraphPane.XAxis.Scale.Max;
			double min = chartCS_1.GraphPane.YAxis.Scale.Min;
			double max2 = chartCS_1.GraphPane.YAxis.Scale.Max;
			TextObj textObj = base.method_27(chartCS_1, max, double_2, double_2.ToString("F1"), null, string_5);
			textObj.Location.AlignV = AlignV.Bottom;
			textObj.Location.AlignH = AlignH.Right;
			list.Add(textObj);
			for (int i = 1; i < 10; i++)
			{
				double num = double_2 + (double_4 - double_2) * (double)i;
				if (num <= min || num >= max2)
				{
					break;
				}
				LineObj item2 = this.method_39(chartCS_1, num, string_5);
				list.Add(item2);
				textObj = base.method_27(chartCS_1, max, num, num.ToString("F1"), null, string_5);
				textObj.Location.AlignV = AlignV.Bottom;
				textObj.Location.AlignH = AlignH.Right;
				list.Add(textObj);
			}
			return list;
		}

		// Token: 0x06000452 RID: 1106 RVA: 0x000231B4 File Offset: 0x000213B4
		protected LineObj method_39(ChartCS chartCS_1, double double_1, string string_5)
		{
			double min = chartCS_1.GraphPane.XAxis.Scale.Min;
			double max = chartCS_1.GraphPane.XAxis.Scale.Max;
			return base.method_23(min, double_1, max, double_1, base.Tag);
		}
	}
}
