using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Threading;
using System.Xml;
using NAppUpdate.Framework;
using NAppUpdate.Framework.Sources;
using NAppUpdate.Framework.Tasks;
using ns23;
using ns25;
using TEx;
using TEx.Comn;
using TEx.Util;

namespace ns19
{
	// Token: 0x0200012C RID: 300
	internal sealed class Class180
	{
		// Token: 0x06000C6D RID: 3181 RVA: 0x00005A57 File Offset: 0x00003C57
		public Class180(int int_1)
		{
			this.int_0 = int_1;
		}

		// Token: 0x06000C6E RID: 3182 RVA: 0x00005A68 File Offset: 0x00003C68
		public Class180(int int_1, int? nullable_2, int? nullable_3) : this(int_1)
		{
			this.BeginYear = nullable_2;
			this.EndYear = nullable_3;
		}

		// Token: 0x06000C6F RID: 3183 RVA: 0x00048E30 File Offset: 0x00047030
		public void method_0()
		{
			this.method_1(new Class181
			{
				nullable_0 = this.BeginYear,
				nullable_1 = this.EndYear,
				int_0 = this.StkId,
				bool_0 = false,
				bool_1 = true
			});
		}

		// Token: 0x06000C70 RID: 3184 RVA: 0x00048E80 File Offset: 0x00047080
		private void method_1(Class181 class181_0)
		{
			this.backgroundWorker_0 = new BackgroundWorker();
			this.backgroundWorker_0.WorkerReportsProgress = true;
			this.backgroundWorker_0.WorkerSupportsCancellation = true;
			this.backgroundWorker_0.DoWork += this.backgroundWorker_0_DoWork;
			this.backgroundWorker_0.RunWorkerCompleted += this.backgroundWorker_0_RunWorkerCompleted;
			this.backgroundWorker_0.RunWorkerAsync(class181_0);
		}

		// Token: 0x06000C71 RID: 3185 RVA: 0x00048EEC File Offset: 0x000470EC
		private void backgroundWorker_0_DoWork(object sender, DoWorkEventArgs e)
		{
			Class181 class181_ = e.Argument as Class181;
			this.method_3(class181_);
		}

		// Token: 0x06000C72 RID: 3186 RVA: 0x00048F10 File Offset: 0x00047110
		public void method_2(int int_1, int int_2, bool bool_1 = true)
		{
			this.method_4(int_1, null, new int?(int_2), bool_1, false);
			if (this.AutoBgFetchFollowingYrData)
			{
				this.method_1(new Class181(int_1, new int?(int_2 + 1), null, false, false));
			}
		}

		// Token: 0x06000C73 RID: 3187 RVA: 0x00005A81 File Offset: 0x00003C81
		public void method_3(Class181 class181_0)
		{
			this.method_4(class181_0.int_0, class181_0.nullable_0, class181_0.nullable_1, class181_0.bool_0, class181_0.bool_1);
		}

		// Token: 0x06000C74 RID: 3188 RVA: 0x00048F60 File Offset: 0x00047160
		public void method_4(int int_1, int? nullable_2, int? nullable_3, bool bool_1, bool bool_2)
		{
			XmlDocument xmlDocument_ = this.method_8(int_1, nullable_2, nullable_3);
			this.nullable_0 = nullable_2;
			this.nullable_1 = nullable_3;
			this.method_5(xmlDocument_, bool_1, bool_2);
		}

		// Token: 0x06000C75 RID: 3189 RVA: 0x00048F94 File Offset: 0x00047194
		private void method_5(XmlDocument xmlDocument_0, bool bool_1, bool bool_2)
		{
			UpdateManager instance = UpdateManager.Instance;
			int num = 20000;
			int num2 = 0;
			while (instance.IsWorking)
			{
				num2 += 200;
				if (num2 > num)
				{
					if (bool_1)
					{
						Base.UI.smethod_178();
					}
					return;
				}
				Thread.Sleep(200);
			}
			if (!TApp.EnteredMainForm)
			{
				bool_1 = false;
			}
			string text = Utility.ConvertXMLDocToString(xmlDocument_0);
			Utility.CreateDir(TApp.string_7);
			if (Class183.smethod_0(new MemorySource(text.Replace(".\\Data\\", TApp.string_7))) > 0)
			{
				if (bool_1)
				{
					Base.UI.smethod_176(Base.Data.string_3);
				}
				try
				{
					instance.PrepareUpdates();
					List<string> list = new List<string>();
					new List<string>();
					foreach (IUpdateTask updateTask in instance.Tasks)
					{
						string fileName = Path.GetFileName(((FileUpdateTask)updateTask).LocalPath);
						string item = fileName.Substring(0, fileName.Length - 6);
						if (!list.Contains(item))
						{
							list.Add(item);
						}
					}
					instance.ApplyUpdates(false);
					instance.CleanUp();
				}
				catch (Exception)
				{
					instance.CleanUp();
					if (bool_1)
					{
						Base.UI.smethod_178();
					}
					throw;
				}
			}
			instance.CleanUp();
			if (bool_1)
			{
				Base.UI.smethod_178();
			}
		}

		// Token: 0x06000C76 RID: 3190 RVA: 0x000490E8 File Offset: 0x000472E8
		private XmlDocument method_6(int int_1)
		{
			return this.method_8(int_1, null, null);
		}

		// Token: 0x06000C77 RID: 3191 RVA: 0x00049114 File Offset: 0x00047314
		private XmlDocument method_7(int int_1, int int_2)
		{
			return this.method_8(int_1, null, new int?(int_2));
		}

		// Token: 0x06000C78 RID: 3192 RVA: 0x0004913C File Offset: 0x0004733C
		private XmlDocument method_8(int int_1, int? nullable_2, int? nullable_3)
		{
			if (TApp.SrvParams.GetHDFileInfoFromSrvApi)
			{
				this.ienumerable_0 = HDFileMgr.smethod_6(int_1, nullable_2, nullable_3, true);
			}
			else
			{
				this.ienumerable_0 = Base.Data.smethod_118(int_1, nullable_2, nullable_3);
			}
			return this.method_9(this.ienumerable_0);
		}

		// Token: 0x06000C79 RID: 3193 RVA: 0x00049184 File Offset: 0x00047384
		private XmlDocument method_9(IEnumerable<HDFileInfo> ienumerable_1)
		{
			XmlDocument xmlDocument = new XmlDocument();
			XmlDeclaration newChild = xmlDocument.CreateXmlDeclaration("1.0", "utf-8", null);
			xmlDocument.AppendChild(newChild);
			XmlElement xmlElement = xmlDocument.CreateElement("Feed");
			xmlElement.SetAttribute("BaseUrl", TApp.SrvParams.HdDatFileBaseUrl);
			xmlDocument.AppendChild(xmlElement);
			XmlElement xmlElement2 = xmlDocument.CreateElement("Tasks");
			xmlElement.AppendChild(xmlElement2);
			HDFileMgr.smethod_12(xmlDocument, xmlElement2, ienumerable_1);
			return xmlDocument;
		}

		// Token: 0x06000C7A RID: 3194 RVA: 0x000041AE File Offset: 0x000023AE
		private void backgroundWorker_0_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
		{
		}

		// Token: 0x170001FB RID: 507
		// (get) Token: 0x06000C7B RID: 3195 RVA: 0x000491FC File Offset: 0x000473FC
		// (set) Token: 0x06000C7C RID: 3196 RVA: 0x00005AA9 File Offset: 0x00003CA9
		public int StkId
		{
			get
			{
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
			}
		}

		// Token: 0x170001FC RID: 508
		// (get) Token: 0x06000C7D RID: 3197 RVA: 0x00049214 File Offset: 0x00047414
		// (set) Token: 0x06000C7E RID: 3198 RVA: 0x00005AB4 File Offset: 0x00003CB4
		public int? BeginYear
		{
			get
			{
				return this.nullable_0;
			}
			set
			{
				this.nullable_0 = value;
			}
		}

		// Token: 0x170001FD RID: 509
		// (get) Token: 0x06000C7F RID: 3199 RVA: 0x0004922C File Offset: 0x0004742C
		// (set) Token: 0x06000C80 RID: 3200 RVA: 0x00005ABF File Offset: 0x00003CBF
		public int? EndYear
		{
			get
			{
				return this.nullable_1;
			}
			set
			{
				this.nullable_1 = value;
			}
		}

		// Token: 0x170001FE RID: 510
		// (get) Token: 0x06000C81 RID: 3201 RVA: 0x00049244 File Offset: 0x00047444
		public BackgroundWorker BgWorker
		{
			get
			{
				return this.backgroundWorker_0;
			}
		}

		// Token: 0x170001FF RID: 511
		// (get) Token: 0x06000C82 RID: 3202 RVA: 0x0004925C File Offset: 0x0004745C
		// (set) Token: 0x06000C83 RID: 3203 RVA: 0x00005ACA File Offset: 0x00003CCA
		public bool AutoBgFetchFollowingYrData
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x04000522 RID: 1314
		private int int_0;

		// Token: 0x04000523 RID: 1315
		private int? nullable_0;

		// Token: 0x04000524 RID: 1316
		private int? nullable_1;

		// Token: 0x04000525 RID: 1317
		private bool bool_0;

		// Token: 0x04000526 RID: 1318
		private IEnumerable<HDFileInfo> ienumerable_0;

		// Token: 0x04000527 RID: 1319
		private BackgroundWorker backgroundWorker_0;
	}
}
