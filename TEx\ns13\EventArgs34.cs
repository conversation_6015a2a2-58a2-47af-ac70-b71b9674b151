﻿using System;

namespace ns13
{
	// Token: 0x020003F9 RID: 1017
	internal sealed class EventArgs34 : EventArgs
	{
		// Token: 0x170006CF RID: 1743
		// (get) Token: 0x060027A5 RID: 10149 RVA: 0x0000F339 File Offset: 0x0000D539
		public Exception FatalException
		{
			get
			{
				return this.exception_0;
			}
		}

		// Token: 0x060027A6 RID: 10150 RVA: 0x0000F341 File Offset: 0x0000D541
		internal EventArgs34(Exception exception_1)
		{
			this.exception_0 = exception_1;
		}

		// Token: 0x040013B2 RID: 5042
		private Exception exception_0;
	}
}
