﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns28;

namespace ns31
{
	// Token: 0x02000414 RID: 1044
	[DesignerCategory("Code")]
	internal sealed class Control6 : Control
	{
		// Token: 0x06002846 RID: 10310 RVA: 0x0000FAC6 File Offset: 0x0000DCC6
		protected void OnVisibleChanged(EventArgs e)
		{
			base.OnVisibleChanged(e);
			if (!base.DesignMode)
			{
				this.method_0(base.Visible);
			}
		}

		// Token: 0x06002847 RID: 10311 RVA: 0x0000FAE3 File Offset: 0x0000DCE3
		private void method_0(bool bool_0)
		{
			this.timer_0.Enabled = bool_0;
			this.int_0 = 0;
			this.Refresh();
		}

		// Token: 0x06002848 RID: 10312 RVA: 0x0000FAFE File Offset: 0x0000DCFE
		protected void OnResize(EventArgs e)
		{
			base.Size = new Size(Convert.ToInt32(250f * this.float_0), Convert.ToInt32(42f * this.float_1));
			base.OnResize(e);
		}

		// Token: 0x06002849 RID: 10313 RVA: 0x0000FB34 File Offset: 0x0000DD34
		protected void ScaleCore(float dx, float dy)
		{
			this.float_0 = dx;
			this.float_1 = dy;
			base.ScaleCore(dx, dy);
			this.OnResize(EventArgs.Empty);
		}

		// Token: 0x0600284A RID: 10314 RVA: 0x0000FB57 File Offset: 0x0000DD57
		protected void Dispose(bool disposing)
		{
			if (disposing)
			{
				if (this.bitmap_0 != null)
				{
					this.bitmap_0.Dispose();
				}
				this.timer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600284B RID: 10315 RVA: 0x00103F7C File Offset: 0x0010217C
		protected void OnPaint(PaintEventArgs e)
		{
			base.OnPaint(e);
			if (this.bitmap_1 != null)
			{
				e.Graphics.DrawImage(this.bitmap_1, new Rectangle(0, 0, Convert.ToInt32(250f * this.float_0), Convert.ToInt32(42f * this.float_1)), new Rectangle(0, 0, 250, 42), GraphicsUnit.Pixel);
			}
			if (this.bitmap_0 != null && this.int_0 > 0)
			{
				e.Graphics.SetClip(new Rectangle(Convert.ToInt32(46f * this.float_0), 0, Convert.ToInt32(165f * this.float_0), Convert.ToInt32(34f * this.float_1)));
				e.Graphics.DrawImage(this.bitmap_0, new Rectangle(Convert.ToInt32((float)(this.int_0 - 6) * this.float_0), Convert.ToInt32(16f * this.float_1), Convert.ToInt32(40f * this.float_0), Convert.ToInt32(12f * this.float_1)), 0, 0, 40, 12, GraphicsUnit.Pixel);
			}
		}

		// Token: 0x0600284C RID: 10316 RVA: 0x0000FB81 File Offset: 0x0000DD81
		private void timer_0_Tick(object sender, EventArgs e)
		{
			this.int_0 += 11;
			if (this.int_0 > 198)
			{
				this.int_0 = 0;
			}
			this.Refresh();
		}

		// Token: 0x0600284D RID: 10317 RVA: 0x001040A4 File Offset: 0x001022A4
		public Control6()
		{
			this.timer_0.Interval = 85;
			this.timer_0.Tick += this.timer_0_Tick;
			base.Size = new Size(250, 42);
			base.TabStop = false;
			base.SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.SupportsTransparentBackColor | ControlStyles.AllPaintingInWmPaint | ControlStyles.DoubleBuffer, true);
		}

		// Token: 0x04001418 RID: 5144
		private int int_0 = 99;

		// Token: 0x04001419 RID: 5145
		private readonly Bitmap bitmap_0 = Class537.smethod_0("data");

		// Token: 0x0400141A RID: 5146
		private readonly Bitmap bitmap_1 = Class537.smethod_0("network");

		// Token: 0x0400141B RID: 5147
		private readonly Timer timer_0 = new Timer();

		// Token: 0x0400141C RID: 5148
		private float float_0 = 1f;

		// Token: 0x0400141D RID: 5149
		private float float_1 = 1f;
	}
}
