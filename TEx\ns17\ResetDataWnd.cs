﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using TEx;

namespace ns17
{
	// Token: 0x02000096 RID: 150
	internal sealed partial class ResetDataWnd : Form
	{
		// Token: 0x14000020 RID: 32
		// (add) Token: 0x060004F5 RID: 1269 RVA: 0x00026AD8 File Offset: 0x00024CD8
		// (remove) Token: 0x060004F6 RID: 1270 RVA: 0x00026B10 File Offset: 0x00024D10
		public event EventHandler ResetAllDataConfirmed
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060004F7 RID: 1271 RVA: 0x000042FD File Offset: 0x000024FD
		protected void method_0()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, null);
			}
		}

		// Token: 0x060004F8 RID: 1272 RVA: 0x00026B48 File Offset: 0x00024D48
		public ResetDataWnd(string string_1)
		{
			this.InitializeComponent();
			if (!TApp.IsHighDpiScreen)
			{
				base.AutoScaleMode = AutoScaleMode.Dpi;
			}
			this.method_1();
			this.string_0 = string_1;
			this.BackColor = Color.White;
			this.button_OK.Click += this.button_OK_Click;
			this.radioBtn_FmUISettings.CheckedChanged += this.radioBtn_FmUISettings_CheckedChanged;
			this.radioBtn_Pages.CheckedChanged += this.radioBtn_Pages_CheckedChanged;
			this.radioBtn_DrawObjs.CheckedChanged += this.radioBtn_DrawObjs_CheckedChanged;
			this.radioBtn_LocalHdData.CheckedChanged += this.radioBtn_LocalHdData_CheckedChanged;
			this.radioBtn_AllData.CheckedChanged += this.radioBtn_AllData_CheckedChanged;
		}

		// Token: 0x060004F9 RID: 1273 RVA: 0x00026C14 File Offset: 0x00024E14
		private void method_1()
		{
			float num = TApp.smethod_4(9f, false);
			if (this.Font.Size != num)
			{
				this.Font = new Font("SimSun", num);
			}
			foreach (object obj in base.Controls)
			{
				Control control = (Control)obj;
				if (control.Font.Size != num)
				{
					control.Font = new Font("SimSun", num);
				}
			}
		}

		// Token: 0x060004FA RID: 1274 RVA: 0x00026CB4 File Offset: 0x00024EB4
		private void button_OK_Click(object sender, EventArgs e)
		{
			if (MessageBox.Show("确定清除并重置" + this.method_2() + "吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
			{
				if (this.radioBtn_FmUISettings.Checked)
				{
					Base.UI.smethod_52();
				}
				else if (this.radioBtn_Pages.Checked)
				{
					Base.UI.smethod_87();
				}
				else if (this.radioBtn_DrawObjs.Checked)
				{
					Base.UI.smethod_140();
				}
				else if (this.radioBtn_LocalHdData.Checked)
				{
					TApp.smethod_3(this.string_0);
				}
				else if (this.radioBtn_AllData.Checked)
				{
					this.method_0();
				}
				base.Close();
			}
		}

		// Token: 0x060004FB RID: 1275 RVA: 0x00026D58 File Offset: 0x00024F58
		private string method_2()
		{
			string text;
			foreach (object obj in this.panel1.Controls)
			{
				RadioButton radioButton = (RadioButton)obj;
				if (radioButton.Checked)
				{
					text = radioButton.Text;
					goto IL_55;
				}
			}
			return string.Empty;
			IL_55:
			return text;
		}

		// Token: 0x060004FC RID: 1276 RVA: 0x00004314 File Offset: 0x00002514
		private void radioBtn_FmUISettings_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioBtn_FmUISettings.Checked)
			{
				this.label_Notes.Text = "重置账户系统参数设置为系统默认值，包括图表显示、界面及交易等相关参数。";
			}
		}

		// Token: 0x060004FD RID: 1277 RVA: 0x00004335 File Offset: 0x00002535
		private void radioBtn_Pages_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioBtn_Pages.Checked)
			{
				this.label_Notes.Text = "重置账户页面设置为系统默认值，自建页面将被清除并重置为系统默认内置页面（包括简单、分时、二图、三图及四图页面等）。";
			}
		}

		// Token: 0x060004FE RID: 1278 RVA: 0x00004356 File Offset: 0x00002556
		private void radioBtn_DrawObjs_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioBtn_DrawObjs.Checked)
			{
				this.label_Notes.Text = "重置账户画线数据为系统默认值，这将清除账户下所有模拟账户中的画线数据。";
			}
		}

		// Token: 0x060004FF RID: 1279 RVA: 0x00004377 File Offset: 0x00002577
		private void radioBtn_LocalHdData_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioBtn_LocalHdData.Checked)
			{
				this.label_Notes.Text = "清除已下载的本地所有历史行情缓存数据。";
			}
		}

		// Token: 0x06000500 RID: 1280 RVA: 0x00004398 File Offset: 0x00002598
		private void radioBtn_AllData_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioBtn_AllData.Checked)
			{
				this.label_Notes.Text = "确认后将清除并重置所有账户数据及云同步数据，包括所有模拟账户及交易记录数据，以及系统参数、页面设置和画线等数据。请谨慎选择！";
			}
		}

		// Token: 0x06000501 RID: 1281 RVA: 0x000043B9 File Offset: 0x000025B9
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000206 RID: 518
		private string string_0;

		// Token: 0x04000207 RID: 519
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000208 RID: 520
		private IContainer icontainer_0;
	}
}
