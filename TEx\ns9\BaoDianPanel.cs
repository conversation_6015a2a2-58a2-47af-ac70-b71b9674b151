﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using DevComponents.AdvTree;
using DevComponents.DotNetBar;
using DevComponents.DotNetBar.Controls;
using ns22;
using ns28;
using ns6;
using TEx;

namespace ns9
{
	// Token: 0x02000207 RID: 519
	[Docking(DockingBehavior.AutoDock)]
	internal sealed class BaoDianPanel : UserControl
	{
		// Token: 0x06001520 RID: 5408 RVA: 0x00008774 File Offset: 0x00006974
		public BaoDianPanel()
		{
			this.InitializeComponent();
		}

		// Token: 0x06001521 RID: 5409 RVA: 0x0008A668 File Offset: 0x00088868
		public void method_0()
		{
			Base.UI.ChartThemeChanged += this.method_1;
			BaoDianMgr.action_0 = (Action<BaoDian>)Delegate.Combine(BaoDianMgr.action_0, new Action<BaoDian>(this.method_8));
			BaoDianMgr.action_1 = (Action<Node>)Delegate.Combine(BaoDianMgr.action_1, new Action<Node>(this.method_9));
			this.panel_BaoDian.ClientSizeChanged += this.panel_BaoDian_ClientSizeChanged;
			this.panel_BaoDianPicNote_Splt_Below.ClientSizeChanged += this.panel_BaoDianPicNote_Splt_Below_ClientSizeChanged;
			this.txtBoxX_BaoDianNote.TextChanged += this.txtBoxX_BaoDianNote_TextChanged;
			this.txtBoxX_BaoDianNote.Leave += this.txtBoxX_BaoDianNote_Leave;
			this.txtBoxX_BaoDianNote.Enter += this.txtBoxX_BaoDianNote_Enter;
			this.advTree_BaoDian.HideBorder = true;
			this.advTree_BaoDian.PathSeparator = "➔";
			this.advTree_BaoDian.MouseDown += this.advTree_BaoDian_MouseDown;
			this.advTree_BaoDian.CellEditEnding += this.advTree_BaoDian_CellEditEnding;
			this.advTree_BaoDian.AfterCellEditComplete += this.advTree_BaoDian_AfterCellEditComplete;
			this.tabCtrlPanel_ScrShot.Padding = new System.Windows.Forms.Padding(0);
			this.tabControl_ScrShot.Padding = new System.Windows.Forms.Padding(0);
			this.panel_BaoDianScrShot.Padding = new System.Windows.Forms.Padding(0);
			this.panel_BaoDianScrShot.Margin = new System.Windows.Forms.Padding(0);
			this.panel_BaoDianScrShot.BorderStyle = BorderStyle.None;
			this.pictureBox_BaoDianScrShot.Padding = new System.Windows.Forms.Padding(0);
			this.pictureBox_BaoDianScrShot.BorderStyle = BorderStyle.None;
			if (!TApp.IsHighDpiScreen)
			{
				Font font = new Font("SimSun", (float)(11.25 / TApp.DpiScale));
				this.tabControl_ScrShot.Font = font;
				this.tabControl_ScrShot.SelectedTabFont = font;
				this.tabControl_Notes.Font = font;
				this.tabControl_Notes.SelectedTabFont = font;
				this.txtBoxX_BaoDianNote.Font = font;
			}
			this.method_10();
			this.method_2();
		}

		// Token: 0x06001522 RID: 5410 RVA: 0x00008784 File Offset: 0x00006984
		private void method_1(object sender, EventArgs e)
		{
			this.method_2();
		}

		// Token: 0x06001523 RID: 5411 RVA: 0x0008A874 File Offset: 0x00088A74
		private void method_2()
		{
			Base.UI.smethod_74(this.expSpliter);
			Color backColor = Base.UI.smethod_34();
			Color color = Base.UI.smethod_35();
			this.pictureBox_BaoDianScrShot.BackColor = backColor;
			this.panel_BaoDianScrShot.BackColor = backColor;
			this.advTree_BaoDian.BackColor = backColor;
			this.advTree_BaoDian.ForeColor = color;
			Color backColor2;
			Color textColor;
			Color backColor3;
			Color color2;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				color = Class179.color_9;
				backColor = Class179.color_3;
				backColor2 = Class179.color_2;
				textColor = color;
				backColor3 = Class179.color_4;
				color2 = Color.FromArgb(69, 69, 69);
				this.BackColor = Class179.color_3;
				this.panel_BaoDian.BackColor = Class179.color_3;
			}
			else
			{
				color = Class179.color_3;
				backColor = Color.White;
				backColor2 = Class179.color_9;
				textColor = color;
				backColor3 = Class179.color_12;
				Color white = Color.White;
				color2 = Color.FromArgb(255, 250, 240);
				this.BackColor = Class179.color_9;
				this.panel_BaoDian.BackColor = Class179.color_9;
			}
			this.advTree_BaoDian.ColumnsBackgroundStyle = new ElementStyle();
			this.advTree_BaoDian.ColumnsBackgroundStyle.BackColor = backColor2;
			this.advTree_BaoDian.ColumnsBackgroundStyle.BackColor2 = color2;
			this.advTree_BaoDian.ColumnsBackgroundStyle.Border = eStyleBorderType.Solid;
			this.advTree_BaoDian.ColumnsBackgroundStyle.BorderBottomColor = color2;
			this.advTree_BaoDian.ColumnsBackgroundStyle.BorderColor = color2;
			this.advTree_BaoDian.ColumnStyleNormal = new ElementStyle();
			this.advTree_BaoDian.ColumnStyleNormal.TextColor = textColor;
			this.advTree_BaoDian.ColumnStyleNormal.PaddingTop = 4;
			this.advTree_BaoDian.ColumnStyleNormal.PaddingBottom = 4;
			this.advTree_BaoDian.NodeStyleSelected = new ElementStyle();
			this.advTree_BaoDian.NodeStyleSelected.BackColor = backColor3;
			this.advTree_BaoDian.CellStyleDefault = new ElementStyle();
			this.advTree_BaoDian.CellStyleDefault.PaddingTop = 3;
			this.advTree_BaoDian.CellStyleDefault.PaddingBottom = 3;
			this.advTree_BaoDian.CellStyleDefault.TextColor = color;
			this.advTree_BaoDian.ExpandBorderColor = color2;
			this.advTree_BaoDian.GridColumnLines = false;
			this.method_3();
		}

		// Token: 0x06001524 RID: 5412 RVA: 0x0008AA9C File Offset: 0x00088C9C
		private void method_3()
		{
			TabColorScheme colorScheme = Base.UI.smethod_72();
			this.tabControl_ScrShot.ColorScheme = colorScheme;
			this.tabControl_Notes.ColorScheme = colorScheme;
		}

		// Token: 0x06001525 RID: 5413 RVA: 0x0008AACC File Offset: 0x00088CCC
		private void advTree_BaoDian_CellEditEnding(object sender, CellEditEventArgs e)
		{
			Node parent = e.Cell.Parent;
			NodeCollection nodes = this.advTree_BaoDian.Nodes;
			if (parent.Parent != null)
			{
				nodes = parent.Parent.Nodes;
			}
			List<Node> list = new List<Node>();
			for (int i = 0; i < nodes.Count; i++)
			{
				Node node = nodes[i];
				if (node != parent)
				{
					list.Add(node);
				}
			}
			if (this.method_23(e.NewText, list))
			{
				MessageBox.Show("该名称已存在，请重新修改！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				e.Cancel = true;
			}
		}

		// Token: 0x06001526 RID: 5414 RVA: 0x0008AB60 File Offset: 0x00088D60
		private void advTree_BaoDian_AfterCellEditComplete(object sender, CellEditEventArgs e)
		{
			this.bool_1 = false;
			bool flag = false;
			Node parent = e.Cell.Parent;
			BaoDian baoDian = parent.Tag as BaoDian;
			if (baoDian == null)
			{
				using (IEnumerator enumerator = parent.Nodes.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						object obj = enumerator.Current;
						Node node = (Node)obj;
						BaoDian baoDian2 = node.Tag as BaoDian;
						if (baoDian2 != null)
						{
							if (baoDian2.Group != parent.FullPath)
							{
								flag = true;
								baoDian2.Group = parent.FullPath;
							}
						}
						else
						{
							foreach (object obj2 in node.Nodes)
							{
								BaoDian baoDian3 = ((Node)obj2).Tag as BaoDian;
								if (baoDian3 != null && baoDian3.Group != node.FullPath)
								{
									flag = true;
									baoDian3.Group = node.FullPath;
								}
							}
						}
					}
					goto IL_134;
				}
			}
			if (baoDian.Name != e.NewText)
			{
				flag = true;
				baoDian.Name = e.NewText;
			}
			IL_134:
			if (flag)
			{
				BaoDianMgr.smethod_8();
			}
		}

		// Token: 0x06001527 RID: 5415 RVA: 0x0008ACC8 File Offset: 0x00088EC8
		private void panel_BaoDian_ClientSizeChanged(object sender, EventArgs e)
		{
			int num = 0;
			try
			{
				foreach (object obj in this.advTree_BaoDian.Columns)
				{
					DevComponents.AdvTree.ColumnHeader columnHeader = (DevComponents.AdvTree.ColumnHeader)obj;
					int num2 = columnHeader.MinimumWidth;
					if (columnHeader.Width.AutoSize && columnHeader.Width.AutoSizeWidth > 0)
					{
						num2 = columnHeader.Width.AutoSizeWidth;
					}
					num += num2;
				}
				int num3 = 35;
				if (this.advTree_BaoDian.VerticalScroll.Visible)
				{
					num3 = 50;
				}
				this.treePanel.Width = num + num3;
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x06001528 RID: 5416 RVA: 0x0008AD98 File Offset: 0x00088F98
		private void panel_BaoDianPicNote_Splt_Below_ClientSizeChanged(object sender, EventArgs e)
		{
			try
			{
				Size clientSize = this.panel_BaoDianPicNote_Splt_Below.ClientSize;
				if ((double)(clientSize.Width / clientSize.Height) > 1.5)
				{
					this.splitContainer_BaoDianPicNote.Orientation = Orientation.Vertical;
					this.splitContainer_BaoDianPicNote.SplitterDistance = Convert.ToInt32(Math.Round((double)clientSize.Width * 0.6));
				}
				else
				{
					this.splitContainer_BaoDianPicNote.Orientation = Orientation.Horizontal;
					this.splitContainer_BaoDianPicNote.SplitterDistance = Convert.ToInt32(Math.Round((double)clientSize.Height * 0.7));
				}
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x06001529 RID: 5417 RVA: 0x0008AE50 File Offset: 0x00089050
		private void advTree_BaoDian_MouseDown(object sender, MouseEventArgs e)
		{
			this.advTree_BaoDian.ContextMenuStrip = null;
			ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
			if (e.Button == MouseButtons.Right)
			{
				ToolStripMenuItem value = this.method_4(null);
				contextMenuStrip.Items.Add(value);
				if (Base.UI.smethod_37() != null)
				{
					Node node = this.advTree_BaoDian.Nodes[0];
					if (this.advTree_BaoDian.SelectedNode != node)
					{
						foreach (object obj in this.advTree_BaoDian.Nodes)
						{
							Node node2 = (Node)obj;
							if (this.advTree_BaoDian.SelectedNode == node2)
							{
								node = node2;
							}
						}
					}
					ToolStripMenuItem value2 = this.method_5(node);
					contextMenuStrip.Items.Add(value2);
				}
			}
			Base.UI.smethod_73(contextMenuStrip);
			this.advTree_BaoDian.ContextMenuStrip = contextMenuStrip;
		}

		// Token: 0x0600152A RID: 5418 RVA: 0x0008AF4C File Offset: 0x0008914C
		private ToolStripMenuItem method_4(Node node_0)
		{
			ToolStripMenuItem toolStripMenuItem = new ToolStripMenuItem();
			toolStripMenuItem.Text = "新建分组";
			toolStripMenuItem.Tag = node_0;
			toolStripMenuItem.Image = Class372.NewFolder;
			toolStripMenuItem.Click += this.method_19;
			return toolStripMenuItem;
		}

		// Token: 0x0600152B RID: 5419 RVA: 0x0008AF94 File Offset: 0x00089194
		private ToolStripMenuItem method_5(Node node_0)
		{
			ToolStripMenuItem toolStripMenuItem = new ToolStripMenuItem();
			toolStripMenuItem.Text = "新建宝典";
			toolStripMenuItem.Tag = node_0;
			toolStripMenuItem.Image = Class372.NewBook;
			toolStripMenuItem.Click += this.method_24;
			return toolStripMenuItem;
		}

		// Token: 0x0600152C RID: 5420 RVA: 0x0000878E File Offset: 0x0000698E
		private void txtBoxX_BaoDianNote_Enter(object sender, EventArgs e)
		{
			this.bool_2 = true;
			this.string_0 = this.txtBoxX_BaoDianNote.Text;
		}

		// Token: 0x0600152D RID: 5421 RVA: 0x0008AFDC File Offset: 0x000891DC
		private void txtBoxX_BaoDianNote_Leave(object sender, EventArgs e)
		{
			if (this.bool_0 && !this.string_0.Equals(this.txtBoxX_BaoDianNote.Text))
			{
				if (this.pictureBox_BaoDianScrShot.Tag == null)
				{
					return;
				}
				if (MessageBox.Show("保存备注修改吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
				{
					(this.pictureBox_BaoDianScrShot.Tag as BaoDian).Note = this.txtBoxX_BaoDianNote.Text;
					BaoDianMgr.smethod_8();
				}
				this.bool_0 = false;
			}
			this.bool_2 = false;
		}

		// Token: 0x0600152E RID: 5422 RVA: 0x000087AA File Offset: 0x000069AA
		private void txtBoxX_BaoDianNote_TextChanged(object sender, EventArgs e)
		{
			this.bool_0 = true;
		}

		// Token: 0x0600152F RID: 5423 RVA: 0x000087B5 File Offset: 0x000069B5
		public void method_6(BaoDian baoDian_0)
		{
			if (Base.UI.Form.IsInBlindTestMode)
			{
				MessageBox.Show("请先退出双盲模式后再加载宝典行情。", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Asterisk);
			}
			else
			{
				this.method_7(baoDian_0);
			}
		}

		// Token: 0x06001530 RID: 5424 RVA: 0x0008B064 File Offset: 0x00089264
		private void method_7(BaoDian baoDian_0)
		{
			StkSymbol stkSymbol = Base.Data.UsrStkSymbols[baoDian_0.SymbolID];
			StkSymbol stkSymbol2 = Base.Data.CurrSelectedSymbol;
			if (stkSymbol2 == null && Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl.SymbDataSet != null)
			{
				stkSymbol2 = Base.UI.SelectedChtCtrl.SymbDataSet.CurrSymbol;
			}
			DateTime symbolTime = baoDian_0.SymbolTime;
			if (stkSymbol2 != null)
			{
				if (stkSymbol2.ID == stkSymbol.ID)
				{
					if (Base.UI.smethod_122(symbolTime, "存在开仓时间晚于所选宝典行情日期的未平仓交易，请先平仓再加载该宝典。"))
					{
						return;
					}
					if (!Base.UI.smethod_123())
					{
						return;
					}
					if (!Base.Data.smethod_129(symbolTime, DateTime.Now))
					{
						return;
					}
					Base.UI.smethod_176("加载宝典历史行情...");
					Base.Data.smethod_128(symbolTime, null);
				}
				else
				{
					Base.UI.smethod_176("加载宝典历史行情...");
					Base.Data.smethod_66(baoDian_0.SymbolID, baoDian_0.SymbolTime, false, false);
				}
			}
			else
			{
				Base.UI.smethod_176("加载宝典历史行情...");
				Base.Data.smethod_66(baoDian_0.SymbolID, baoDian_0.SymbolTime, false, false);
			}
			Base.UI.smethod_27(baoDian_0.PeriodType, baoDian_0.PeriodUnit);
			Base.UI.smethod_178();
		}

		// Token: 0x06001531 RID: 5425 RVA: 0x0008B168 File Offset: 0x00089368
		private void method_8(BaoDian baoDian_0)
		{
			Node node = this.method_12(baoDian_0.Group);
			Node node2 = BaoDianMgr.smethod_4(baoDian_0);
			this.advTree_BaoDian.BeginUpdate();
			node.Nodes.Add(node2);
			node.Expand();
			this.method_13(node2);
			this.method_16(this.method_17());
			this.advTree_BaoDian.EndUpdate();
			this.panel_BaoDian_ClientSizeChanged(null, null);
			try
			{
				this.method_30(baoDian_0);
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x06001532 RID: 5426 RVA: 0x0008B1F0 File Offset: 0x000893F0
		private void method_9(Node node_0)
		{
			BaoDian baoDian = node_0.Tag as BaoDian;
			if (node_0.FullPath != baoDian.Group)
			{
				this.advTree_BaoDian.BeginUpdate();
				try
				{
					Node node = this.method_12(baoDian.Group);
					node_0.Parent.Nodes.Remove(node_0);
					node.Nodes.Add(node_0);
					this.method_16(true);
					this.advTree_BaoDian.EndUpdate();
					this.panel_BaoDian_ClientSizeChanged(null, null);
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
			this.method_30(baoDian);
		}

		// Token: 0x06001533 RID: 5427 RVA: 0x0008B28C File Offset: 0x0008948C
		private void method_10()
		{
			this.advTree_BaoDian.Nodes.Clear();
			this.advTree_BaoDian.BeginUpdate();
			BaoDian baoDian = null;
			foreach (Node node in BaoDianMgr.smethod_0())
			{
				this.advTree_BaoDian.Nodes.Add(node);
			}
			Node node2 = this.method_11();
			if (node2 != null)
			{
				baoDian = (node2.Tag as BaoDian);
			}
			this.advTree_BaoDian.NodeDoubleClick += this.advTree_BaoDian_NodeDoubleClick;
			this.advTree_BaoDian.NodeMouseDown += this.advTree_BaoDian_NodeMouseDown;
			this.advTree_BaoDian.NodeMouseUp += this.advTree_BaoDian_NodeMouseUp;
			this.method_16(baoDian != null);
			this.advTree_BaoDian.ExpandAll();
			this.method_13(node2);
			this.advTree_BaoDian.EndUpdate();
			this.panel_BaoDian_ClientSizeChanged(null, null);
			this.method_30(baoDian);
		}

		// Token: 0x06001534 RID: 5428 RVA: 0x0008B39C File Offset: 0x0008959C
		private Node method_11()
		{
			Node result;
			foreach (object obj in this.advTree_BaoDian.Nodes)
			{
				foreach (object obj2 in ((Node)obj).Nodes)
				{
					Node node = (Node)obj2;
					if (node.Tag is BaoDian)
					{
						result = node;
						goto IL_E2;
					}
					foreach (object obj3 in node.Nodes)
					{
						Node node2 = (Node)obj3;
						if (node2.Tag is BaoDian)
						{
							result = node2;
							goto IL_E2;
						}
					}
				}
			}
			return null;
			IL_E2:
			return result;
		}

		// Token: 0x06001535 RID: 5429 RVA: 0x0008B4BC File Offset: 0x000896BC
		private Node method_12(string string_1)
		{
			Node result;
			foreach (object obj in this.advTree_BaoDian.Nodes)
			{
				Node node = (Node)obj;
				if (!(node.Tag is BaoDian))
				{
					if (node.FullPath == string_1)
					{
						result = node;
						goto IL_C4;
					}
					foreach (object obj2 in node.Nodes)
					{
						Node node2 = (Node)obj2;
						if (!(node2.Tag is BaoDian) && node2.FullPath == string_1)
						{
							result = node2;
							goto IL_C4;
						}
					}
				}
			}
			return null;
			IL_C4:
			return result;
		}

		// Token: 0x06001536 RID: 5430 RVA: 0x0008B5B0 File Offset: 0x000897B0
		private void method_13(Node node_0)
		{
			if (node_0 != null)
			{
				BaoDian baoDian_ = node_0.Tag as BaoDian;
				Node selectedNode = this.advTree_BaoDian.SelectedNode;
				if (selectedNode != null)
				{
					if (selectedNode != node_0)
					{
						this.method_30(baoDian_);
					}
				}
				else
				{
					this.method_30(baoDian_);
				}
				this.method_14();
				node_0.Image = Class372.Book_openHS;
				this.advTree_BaoDian.SelectedNode = node_0;
			}
		}

		// Token: 0x06001537 RID: 5431 RVA: 0x0008B610 File Offset: 0x00089810
		private void method_14()
		{
			foreach (object obj in this.advTree_BaoDian.Nodes)
			{
				foreach (object obj2 in ((Node)obj).Nodes)
				{
					Node node = (Node)obj2;
					if (node.Tag is BaoDian)
					{
						node.Image = Class372.Book_angleHS;
					}
					else
					{
						foreach (object obj3 in node.Nodes)
						{
							((Node)obj3).Image = Class372.Book_angleHS;
						}
					}
				}
			}
		}

		// Token: 0x06001538 RID: 5432 RVA: 0x0008B720 File Offset: 0x00089920
		private void advTree_BaoDian_NodeMouseUp(object sender, TreeNodeMouseEventArgs e)
		{
			BaoDian baoDian = e.Node.Tag as BaoDian;
			if (e.Button == MouseButtons.Left && baoDian == null)
			{
				this.method_15();
			}
		}

		// Token: 0x06001539 RID: 5433 RVA: 0x000087E1 File Offset: 0x000069E1
		private void method_15()
		{
			this.advTree_BaoDian.BeginUpdate();
			this.method_16(this.method_17());
			this.advTree_BaoDian.EndUpdate();
			this.panel_BaoDian_ClientSizeChanged(null, null);
		}

		// Token: 0x0600153A RID: 5434 RVA: 0x0008B758 File Offset: 0x00089958
		private void method_16(bool bool_3)
		{
			foreach (object obj in this.advTree_BaoDian.Columns)
			{
				DevComponents.AdvTree.ColumnHeader columnHeader = (DevComponents.AdvTree.ColumnHeader)obj;
				if (columnHeader.Width.AutoSize != bool_3)
				{
					columnHeader.Width.AutoSize = bool_3;
				}
			}
		}

		// Token: 0x0600153B RID: 5435 RVA: 0x0008B7CC File Offset: 0x000899CC
		private bool method_17()
		{
			bool result = false;
			foreach (object obj in this.advTree_BaoDian.Nodes)
			{
				Node node = (Node)obj;
				foreach (object obj2 in node.Nodes)
				{
					Node node2 = (Node)obj2;
					if (!(node2.Tag is BaoDian))
					{
						if (node2.Expanded && node2.Nodes.Count > 0)
						{
							result = true;
							break;
						}
					}
					else if (node.Expanded)
					{
						result = true;
						break;
					}
				}
			}
			return result;
		}

		// Token: 0x0600153C RID: 5436 RVA: 0x0008B8B4 File Offset: 0x00089AB4
		private void advTree_BaoDian_NodeMouseDown(object sender, TreeNodeMouseEventArgs e)
		{
			Node node = e.Node;
			Node selectedNode = this.advTree_BaoDian.SelectedNode;
			if (node.Tag != null)
			{
				this.method_13(node);
			}
			if (e.Button == MouseButtons.Right)
			{
				BaoDian baoDian = node.Tag as BaoDian;
				this.advTree_BaoDian.ContextMenuStrip = null;
				ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
				if (baoDian == null)
				{
					if (!node.FullPath.Contains(this.advTree_BaoDian.PathSeparator))
					{
						ToolStripMenuItem value = this.method_4(node);
						contextMenuStrip.Items.Add(value);
					}
					if (Base.UI.smethod_37() != null)
					{
						ToolStripMenuItem value2 = this.method_5(node);
						contextMenuStrip.Items.Add(value2);
					}
					contextMenuStrip.Items.Add(new ToolStripSeparator());
				}
				else
				{
					ToolStripMenuItem toolStripMenuItem = new ToolStripMenuItem();
					toolStripMenuItem.Text = "加载";
					toolStripMenuItem.Tag = baoDian;
					toolStripMenuItem.Image = Class372.flash_16x16;
					toolStripMenuItem.Click += this.method_28;
					if (Base.UI.IsInCreateNewPageState)
					{
						toolStripMenuItem.Enabled = false;
					}
					contextMenuStrip.Items.Add(toolStripMenuItem);
					contextMenuStrip.Items.Add(new ToolStripSeparator());
					ToolStripMenuItem toolStripMenuItem2 = new ToolStripMenuItem();
					toolStripMenuItem2.Text = "修改";
					toolStripMenuItem2.Tag = node;
					toolStripMenuItem2.Image = Class372.EditHS;
					toolStripMenuItem2.Click += this.method_25;
					contextMenuStrip.Items.Add(toolStripMenuItem2);
				}
				ToolStripMenuItem toolStripMenuItem3 = new ToolStripMenuItem();
				toolStripMenuItem3.Text = "重命名";
				toolStripMenuItem3.Tag = node;
				toolStripMenuItem3.Image = Class372.RenameHS;
				toolStripMenuItem3.Click += this.method_18;
				contextMenuStrip.Items.Add(toolStripMenuItem3);
				bool flag = false;
				if (this.advTree_BaoDian.Nodes.Count == 1 && this.advTree_BaoDian.Nodes[0] == node)
				{
					flag = true;
				}
				if (!flag)
				{
					ToolStripMenuItem toolStripMenuItem4 = new ToolStripMenuItem();
					toolStripMenuItem4.Text = "删除";
					toolStripMenuItem4.Tag = node;
					toolStripMenuItem4.Image = Class372.DeleteHS;
					toolStripMenuItem4.Click += this.method_29;
					contextMenuStrip.Items.Add(toolStripMenuItem4);
				}
				this.advTree_BaoDian.ContextMenuStrip = contextMenuStrip;
			}
		}

		// Token: 0x0600153D RID: 5437 RVA: 0x0008BAF0 File Offset: 0x00089CF0
		private void advTree_BaoDian_NodeDoubleClick(object sender, TreeNodeMouseEventArgs e)
		{
			Node node = e.Node;
			BaoDian baoDian = node.Tag as BaoDian;
			if (baoDian != null)
			{
				this.method_13(node);
				if (!Base.UI.IsInCreateNewPageState)
				{
					this.method_6(baoDian);
				}
			}
		}

		// Token: 0x0600153E RID: 5438 RVA: 0x0008BB2C File Offset: 0x00089D2C
		private void method_18(object sender, EventArgs e)
		{
			Node node = (sender as ToolStripMenuItem).Tag as Node;
			if (node != null)
			{
				node.BeginEdit();
				this.bool_1 = true;
			}
		}

		// Token: 0x0600153F RID: 5439 RVA: 0x0008BB5C File Offset: 0x00089D5C
		private void method_19(object sender, EventArgs e)
		{
			Node node = (sender as ToolStripMenuItem).Tag as Node;
			this.advTree_BaoDian.BeginUpdate();
			Node node2;
			if (node == null)
			{
				node2 = this.method_20(null, null);
				this.advTree_BaoDian.Nodes.Add(node2);
			}
			else
			{
				node2 = this.method_20(node.Nodes, null);
				node.Nodes.Add(node2);
			}
			this.advTree_BaoDian.EndUpdate();
			this.advTree_BaoDian.SelectedNode = node2;
			node2.BeginEdit();
		}

		// Token: 0x06001540 RID: 5440 RVA: 0x0008BBE0 File Offset: 0x00089DE0
		public Node method_20(NodeCollection nodeCollection_0 = null, string string_1 = null)
		{
			if (string.IsNullOrEmpty(string_1))
			{
				string_1 = this.method_21(nodeCollection_0);
			}
			return BaoDianMgr.smethod_2(string_1);
		}

		// Token: 0x06001541 RID: 5441 RVA: 0x0008BC08 File Offset: 0x00089E08
		private string method_21(NodeCollection nodeCollection_0 = null)
		{
			if (nodeCollection_0 == null)
			{
				nodeCollection_0 = this.advTree_BaoDian.Nodes;
			}
			string text = "新建分组";
			if (this.method_22(text, nodeCollection_0))
			{
				for (int i = 1; i < 2147483647; i++)
				{
					string text2 = text + i.ToString();
					if (!this.method_22(text2, nodeCollection_0))
					{
						return text2;
					}
				}
			}
			return text;
		}

		// Token: 0x06001542 RID: 5442 RVA: 0x0008BC68 File Offset: 0x00089E68
		private bool method_22(string string_1, NodeCollection nodeCollection_0)
		{
			bool result;
			foreach (object obj in nodeCollection_0)
			{
				Node node = (Node)obj;
				if (!node.IsEditing && node.Text == string_1)
				{
					result = true;
					goto IL_50;
				}
			}
			return false;
			IL_50:
			return result;
		}

		// Token: 0x06001543 RID: 5443 RVA: 0x0008BCDC File Offset: 0x00089EDC
		private bool method_23(string string_1, IEnumerable<Node> ienumerable_0)
		{
			bool result;
			foreach (Node node in ienumerable_0)
			{
				if (!node.IsEditing && node.Text == string_1)
				{
					result = true;
					goto IL_43;
				}
			}
			return false;
			IL_43:
			return result;
		}

		// Token: 0x06001544 RID: 5444 RVA: 0x0008BD44 File Offset: 0x00089F44
		private void method_24(object sender, EventArgs e)
		{
			Node node = (sender as ToolStripMenuItem).Tag as Node;
			ChtCtrl chtCtrl = Base.UI.smethod_37();
			if (chtCtrl != null)
			{
				BaoDianMgr.smethod_6(chtCtrl, node.FullPath);
			}
		}

		// Token: 0x06001545 RID: 5445 RVA: 0x0008BD7C File Offset: 0x00089F7C
		private void method_25(object sender, EventArgs e)
		{
			Node node = (sender as ToolStripMenuItem).Tag as Node;
			if (node != null && node.Tag is BaoDian)
			{
				BaoDianMgr.smethod_7(node);
			}
		}

		// Token: 0x06001546 RID: 5446 RVA: 0x0008BDB4 File Offset: 0x00089FB4
		public List<string> method_26()
		{
			List<string> list = new List<string>();
			foreach (object obj in this.advTree_BaoDian.Nodes)
			{
				Node node = (Node)obj;
				if (node.Tag == null)
				{
					list.Add(node.FullPath);
				}
				foreach (object obj2 in node.Nodes)
				{
					Node node2 = (Node)obj2;
					if (node2.Tag == null)
					{
						list.Add(node2.FullPath);
					}
				}
			}
			return list;
		}

		// Token: 0x06001547 RID: 5447 RVA: 0x0008BE8C File Offset: 0x0008A08C
		public AdvTree method_27()
		{
			return this.advTree_BaoDian;
		}

		// Token: 0x06001548 RID: 5448 RVA: 0x0008BEA4 File Offset: 0x0008A0A4
		private void method_28(object sender, EventArgs e)
		{
			ToolStripMenuItem toolStripMenuItem = sender as ToolStripMenuItem;
			if (toolStripMenuItem != null)
			{
				BaoDian baoDian = toolStripMenuItem.Tag as BaoDian;
				if (baoDian != null)
				{
					this.method_6(baoDian);
				}
			}
		}

		// Token: 0x06001549 RID: 5449 RVA: 0x0008BED8 File Offset: 0x0008A0D8
		private void method_29(object sender, EventArgs e)
		{
			Node node = (sender as ToolStripMenuItem).Tag as Node;
			BaoDian baoDian = node.Tag as BaoDian;
			string str = (baoDian == null) ? "组" : "项";
			string text = "确定要删除此宝典" + str + "吗？";
			if (baoDian == null && node.Nodes.Count > 0)
			{
				text += "（其下所有宝典项也将被一并删除）";
			}
			if (MessageBox.Show(text, "请确认", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) == DialogResult.OK)
			{
				node.Tag = null;
				node.Remove();
				BaoDianMgr.smethod_8();
				Node node2 = this.method_11();
				if (node2 != null)
				{
					this.method_13(node2);
				}
				else
				{
					this.method_15();
					this.method_30(null);
				}
			}
		}

		// Token: 0x0600154A RID: 5450 RVA: 0x0008BF84 File Offset: 0x0008A184
		private void method_30(BaoDian baoDian_0)
		{
			if (baoDian_0 == null)
			{
				if (this.pictureBox_BaoDianScrShot != null && this.pictureBox_BaoDianScrShot.Image != null)
				{
					this.pictureBox_BaoDianScrShot.Image = null;
				}
				this.txtBoxX_BaoDianNote.WatermarkText = "说明：当前没有宝典项，请在图表上点击右键，然后选择\"加入宝典\"菜单项添加。";
			}
			else
			{
				try
				{
					this.pictureBox_BaoDianScrShot.Image = baoDian_0.ScreenShot;
					this.txtBoxX_BaoDianNote.Text = baoDian_0.Note;
					if (string.IsNullOrEmpty(baoDian_0.Note))
					{
						this.txtBoxX_BaoDianNote.WatermarkText = "点击添加备注";
					}
				}
				catch (Exception exception_)
				{
					Class46.smethod_4(exception_, true, null);
				}
			}
			if (this.pictureBox_BaoDianScrShot != null)
			{
				this.pictureBox_BaoDianScrShot.Tag = baoDian_0;
			}
			this.bool_0 = false;
		}

		// Token: 0x17000381 RID: 897
		// (get) Token: 0x0600154B RID: 5451 RVA: 0x0008C040 File Offset: 0x0008A240
		public bool IsInInputState
		{
			get
			{
				bool result;
				if (!this.bool_1)
				{
					result = this.bool_2;
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x17000382 RID: 898
		// (get) Token: 0x0600154C RID: 5452 RVA: 0x0008C064 File Offset: 0x0008A264
		public bool IsScrollableCtrlFocused
		{
			get
			{
				bool result;
				if (!this.advTree_BaoDian.Focused)
				{
					result = (this.advTree_BaoDian.MouseOverNode != null);
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x0600154D RID: 5453 RVA: 0x0000880F File Offset: 0x00006A0F
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600154E RID: 5454 RVA: 0x0008C094 File Offset: 0x0008A294
		private void InitializeComponent()
		{
			this.icontainer_0 = new Container();
			this.panel_BaoDian = new Panel();
			this.expSpliter = new ExpandableSplitter();
			this.treePanel = new Panel();
			this.advTree_BaoDian = new AdvTree();
			this.colHeader_BaoDianItem = new DevComponents.AdvTree.ColumnHeader();
			this.colHeader_BaoDianSymbl = new DevComponents.AdvTree.ColumnHeader();
			this.colHeader_BaoDianDate = new DevComponents.AdvTree.ColumnHeader();
			this.node1 = new Node();
			this.nodeConnector_0 = new NodeConnector();
			this.elementStyle1 = new ElementStyle();
			this.panel_BaoDianPicNote_Splt_Below = new Panel();
			this.splitContainer_BaoDianPicNote = new SplitContainer();
			this.tabControl_ScrShot = new DevComponents.DotNetBar.TabControl();
			this.tabCtrlPanel_ScrShot = new TabControlPanel();
			this.panel_BaoDianScrShot = new Panel();
			this.pictureBox_BaoDianScrShot = new PictureBox();
			this.tabItem_ScrShot = new TabItem(this.icontainer_0);
			this.tabControl_Notes = new DevComponents.DotNetBar.TabControl();
			this.tabControlPanel_Notes = new TabControlPanel();
			this.txtBoxX_BaoDianNote = new TextBoxX();
			this.tabItem_Notes = new TabItem(this.icontainer_0);
			this.panel_BaoDian.SuspendLayout();
			this.treePanel.SuspendLayout();
			((ISupportInitialize)this.advTree_BaoDian).BeginInit();
			this.panel_BaoDianPicNote_Splt_Below.SuspendLayout();
			this.splitContainer_BaoDianPicNote.Panel1.SuspendLayout();
			this.splitContainer_BaoDianPicNote.Panel2.SuspendLayout();
			this.splitContainer_BaoDianPicNote.SuspendLayout();
			((ISupportInitialize)this.tabControl_ScrShot).BeginInit();
			this.tabControl_ScrShot.SuspendLayout();
			this.tabCtrlPanel_ScrShot.SuspendLayout();
			this.panel_BaoDianScrShot.SuspendLayout();
			((ISupportInitialize)this.pictureBox_BaoDianScrShot).BeginInit();
			((ISupportInitialize)this.tabControl_Notes).BeginInit();
			this.tabControl_Notes.SuspendLayout();
			this.tabControlPanel_Notes.SuspendLayout();
			base.SuspendLayout();
			this.panel_BaoDian.Controls.Add(this.expSpliter);
			this.panel_BaoDian.Controls.Add(this.panel_BaoDianPicNote_Splt_Below);
			this.panel_BaoDian.Controls.Add(this.treePanel);
			this.panel_BaoDian.Dock = DockStyle.Fill;
			this.panel_BaoDian.Location = new Point(0, 0);
			this.panel_BaoDian.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.panel_BaoDian.Name = "panel_BaoDian";
			this.panel_BaoDian.Size = new Size(1214, 462);
			this.panel_BaoDian.TabIndex = 4;
			this.expSpliter.BackColor = SystemColors.ControlLight;
			this.expSpliter.BackColor2 = Color.Empty;
			this.expSpliter.BackColor2SchemePart = eColorSchemePart.None;
			this.expSpliter.BackColorSchemePart = eColorSchemePart.None;
			this.expSpliter.ExpandableControl = this.treePanel;
			this.expSpliter.ExpandFillColor = Color.FromArgb(254, 142, 75);
			this.expSpliter.ExpandFillColorSchemePart = eColorSchemePart.ItemPressedBackground;
			this.expSpliter.ExpandLineColor = Color.FromArgb(0, 0, 128);
			this.expSpliter.ExpandLineColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expSpliter.GripDarkColor = Color.FromArgb(0, 0, 128);
			this.expSpliter.GripDarkColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expSpliter.GripLightColor = Color.FromArgb(246, 246, 246);
			this.expSpliter.GripLightColorSchemePart = eColorSchemePart.MenuBackground;
			this.expSpliter.HotBackColor = Color.FromArgb(255, 213, 140);
			this.expSpliter.HotBackColor2 = Color.Empty;
			this.expSpliter.HotBackColor2SchemePart = eColorSchemePart.None;
			this.expSpliter.HotBackColorSchemePart = eColorSchemePart.ItemCheckedBackground;
			this.expSpliter.HotExpandFillColor = Color.FromArgb(254, 142, 75);
			this.expSpliter.HotExpandFillColorSchemePart = eColorSchemePart.ItemPressedBackground;
			this.expSpliter.HotExpandLineColor = Color.FromArgb(0, 0, 128);
			this.expSpliter.HotExpandLineColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expSpliter.HotGripDarkColor = Color.FromArgb(0, 0, 128);
			this.expSpliter.HotGripDarkColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expSpliter.HotGripLightColor = Color.FromArgb(246, 246, 246);
			this.expSpliter.HotGripLightColorSchemePart = eColorSchemePart.MenuBackground;
			this.expSpliter.Location = new Point(400, 0);
			this.expSpliter.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.expSpliter.Name = "expSpliter";
			this.expSpliter.Size = new Size(4, 462);
			this.expSpliter.Style = eSplitterStyle.Mozilla;
			this.expSpliter.TabIndex = 4;
			this.expSpliter.TabStop = false;
			this.treePanel.Controls.Add(this.advTree_BaoDian);
			this.treePanel.Dock = DockStyle.Left;
			this.treePanel.Location = new Point(0, 0);
			this.treePanel.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.treePanel.Name = "treePanel";
			this.treePanel.Size = new Size(400, 462);
			this.treePanel.TabIndex = 2;
			this.advTree_BaoDian.AccessibleRole = AccessibleRole.Outline;
			this.advTree_BaoDian.AllowDrop = true;
			this.advTree_BaoDian.BackColor = SystemColors.Window;
			this.advTree_BaoDian.BackgroundStyle.Class = "TreeBorderKey";
			this.advTree_BaoDian.BackgroundStyle.CornerType = eCornerType.Square;
			this.advTree_BaoDian.BackgroundStyle.Border = eStyleBorderType.None;
			this.advTree_BaoDian.Columns.Add(this.colHeader_BaoDianItem);
			this.advTree_BaoDian.Columns.Add(this.colHeader_BaoDianSymbl);
			this.advTree_BaoDian.Columns.Add(this.colHeader_BaoDianDate);
			this.advTree_BaoDian.Dock = DockStyle.Fill;
			this.advTree_BaoDian.Location = new Point(0, 0);
			this.advTree_BaoDian.Name = "advTree_BaoDian";
			this.advTree_BaoDian.Nodes.AddRange(new Node[]
			{
				this.node1
			});
			this.advTree_BaoDian.NodesConnector = this.nodeConnector_0;
			this.advTree_BaoDian.NodeStyle = this.elementStyle1;
			this.advTree_BaoDian.PathSeparator = ";";
			this.advTree_BaoDian.Size = new Size(400, 462);
			this.advTree_BaoDian.Styles.Add(this.elementStyle1);
			this.advTree_BaoDian.TabIndex = 0;
			this.advTree_BaoDian.Text = "advTree_BaoDian";
			this.colHeader_BaoDianItem.MinimumWidth = 200;
			this.colHeader_BaoDianItem.Name = "colHeader_BaoDianItem";
			this.colHeader_BaoDianItem.Text = "名称";
			this.colHeader_BaoDianItem.Width.Absolute = 200;
			this.colHeader_BaoDianSymbl.MinimumWidth = 90;
			this.colHeader_BaoDianSymbl.Name = "colHeader_BaoDianSymbl";
			this.colHeader_BaoDianSymbl.Text = "品种";
			this.colHeader_BaoDianSymbl.Width.Absolute = 90;
			this.colHeader_BaoDianDate.MinimumWidth = 90;
			this.colHeader_BaoDianDate.Name = "colHeader_BaoDianDate";
			this.colHeader_BaoDianDate.Text = "日期";
			this.colHeader_BaoDianDate.Width.Absolute = 90;
			this.node1.Expanded = true;
			this.node1.Name = "node1";
			this.node1.Text = "node1";
			this.nodeConnector_0.LineColor = SystemColors.ControlText;
			this.elementStyle1.CornerType = eCornerType.Square;
			this.elementStyle1.Name = "elementStyle1";
			this.elementStyle1.TextColor = SystemColors.ControlText;
			this.panel_BaoDianPicNote_Splt_Below.Controls.Add(this.splitContainer_BaoDianPicNote);
			this.panel_BaoDianPicNote_Splt_Below.Dock = DockStyle.Fill;
			this.panel_BaoDianPicNote_Splt_Below.Location = new Point(400, 0);
			this.panel_BaoDianPicNote_Splt_Below.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.panel_BaoDianPicNote_Splt_Below.Name = "panel_BaoDianPicNote_Splt_Below";
			this.panel_BaoDianPicNote_Splt_Below.Size = new Size(814, 462);
			this.panel_BaoDianPicNote_Splt_Below.TabIndex = 3;
			this.splitContainer_BaoDianPicNote.Dock = DockStyle.Fill;
			this.splitContainer_BaoDianPicNote.Location = new Point(0, 0);
			this.splitContainer_BaoDianPicNote.Name = "splitContainer_BaoDianPicNote";
			this.splitContainer_BaoDianPicNote.Orientation = Orientation.Horizontal;
			this.splitContainer_BaoDianPicNote.Panel1.Controls.Add(this.tabControl_ScrShot);
			this.splitContainer_BaoDianPicNote.Panel2.Controls.Add(this.tabControl_Notes);
			this.splitContainer_BaoDianPicNote.Size = new Size(814, 462);
			this.splitContainer_BaoDianPicNote.SplitterDistance = 335;
			this.splitContainer_BaoDianPicNote.SplitterWidth = 1;
			this.splitContainer_BaoDianPicNote.TabIndex = 0;
			this.tabControl_ScrShot.BackColor = Color.Transparent;
			this.tabControl_ScrShot.CanReorderTabs = true;
			this.tabControl_ScrShot.Controls.Add(this.tabCtrlPanel_ScrShot);
			this.tabControl_ScrShot.Dock = DockStyle.Fill;
			this.tabControl_ScrShot.Location = new Point(0, 0);
			this.tabControl_ScrShot.Margin = new System.Windows.Forms.Padding(0);
			this.tabControl_ScrShot.Name = "tabControl_ScrShot";
			this.tabControl_ScrShot.SelectedTabFont = new Font("SimSun", 9f, FontStyle.Bold);
			this.tabControl_ScrShot.SelectedTabIndex = 0;
			this.tabControl_ScrShot.Size = new Size(814, 335);
			this.tabControl_ScrShot.Style = eTabStripStyle.Flat;
			this.tabControl_ScrShot.TabAlignment = eTabStripAlignment.Bottom;
			this.tabControl_ScrShot.TabIndex = 1;
			this.tabControl_ScrShot.TabLayoutType = eTabLayoutType.FixedWithNavigationBox;
			this.tabControl_ScrShot.Tabs.Add(this.tabItem_ScrShot);
			this.tabControl_ScrShot.Text = "tabControl1";
			this.tabCtrlPanel_ScrShot.Controls.Add(this.panel_BaoDianScrShot);
			this.tabCtrlPanel_ScrShot.Dock = DockStyle.Fill;
			this.tabCtrlPanel_ScrShot.Location = new Point(0, 0);
			this.tabCtrlPanel_ScrShot.Name = "tabCtrlPanel_ScrShot";
			this.tabCtrlPanel_ScrShot.Padding = new System.Windows.Forms.Padding(1);
			this.tabCtrlPanel_ScrShot.Size = new Size(814, 307);
			this.tabCtrlPanel_ScrShot.Style.BackColor1.Color = SystemColors.Control;
			this.tabCtrlPanel_ScrShot.Style.Border = eBorderType.SingleLine;
			this.tabCtrlPanel_ScrShot.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabCtrlPanel_ScrShot.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabCtrlPanel_ScrShot.Style.GradientAngle = -90;
			this.tabCtrlPanel_ScrShot.TabIndex = 1;
			this.tabCtrlPanel_ScrShot.TabItem = this.tabItem_ScrShot;
			this.panel_BaoDianScrShot.Controls.Add(this.pictureBox_BaoDianScrShot);
			this.panel_BaoDianScrShot.Dock = DockStyle.Fill;
			this.panel_BaoDianScrShot.Location = new Point(1, 1);
			this.panel_BaoDianScrShot.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.panel_BaoDianScrShot.Name = "panel_BaoDianScrShot";
			this.panel_BaoDianScrShot.Size = new Size(812, 305);
			this.panel_BaoDianScrShot.TabIndex = 21;
			this.pictureBox_BaoDianScrShot.BackColor = Color.Transparent;
			this.pictureBox_BaoDianScrShot.Dock = DockStyle.Fill;
			this.pictureBox_BaoDianScrShot.Location = new Point(0, 0);
			this.pictureBox_BaoDianScrShot.Margin = new System.Windows.Forms.Padding(4);
			this.pictureBox_BaoDianScrShot.Name = "pictureBox_BaoDianScrShot";
			this.pictureBox_BaoDianScrShot.Size = new Size(812, 305);
			this.pictureBox_BaoDianScrShot.SizeMode = PictureBoxSizeMode.Zoom;
			this.pictureBox_BaoDianScrShot.TabIndex = 0;
			this.pictureBox_BaoDianScrShot.TabStop = false;
			this.tabItem_ScrShot.AttachedControl = this.tabCtrlPanel_ScrShot;
			this.tabItem_ScrShot.Name = "tabItem_ScrShot";
			this.tabItem_ScrShot.Text = "快照";
			this.tabControl_Notes.BackColor = Color.Transparent;
			this.tabControl_Notes.CanReorderTabs = true;
			this.tabControl_Notes.Controls.Add(this.tabControlPanel_Notes);
			this.tabControl_Notes.Dock = DockStyle.Fill;
			this.tabControl_Notes.Location = new Point(0, 0);
			this.tabControl_Notes.Margin = new System.Windows.Forms.Padding(0);
			this.tabControl_Notes.Name = "tabControl_Notes";
			this.tabControl_Notes.SelectedTabFont = new Font("SimSun", 9f, FontStyle.Bold);
			this.tabControl_Notes.SelectedTabIndex = 0;
			this.tabControl_Notes.Size = new Size(814, 126);
			this.tabControl_Notes.Style = eTabStripStyle.Flat;
			this.tabControl_Notes.TabAlignment = eTabStripAlignment.Bottom;
			this.tabControl_Notes.TabIndex = 2;
			this.tabControl_Notes.TabLayoutType = eTabLayoutType.FixedWithNavigationBox;
			this.tabControl_Notes.Tabs.Add(this.tabItem_Notes);
			this.tabControl_Notes.Text = "tabControl1";
			this.tabControlPanel_Notes.Controls.Add(this.txtBoxX_BaoDianNote);
			this.tabControlPanel_Notes.Dock = DockStyle.Fill;
			this.tabControlPanel_Notes.Location = new Point(0, 0);
			this.tabControlPanel_Notes.Name = "tabControlPanel_Notes";
			this.tabControlPanel_Notes.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_Notes.Size = new Size(814, 98);
			this.tabControlPanel_Notes.Style.Border = eBorderType.None;
			this.tabControlPanel_Notes.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_Notes.Style.GradientAngle = -90;
			this.tabControlPanel_Notes.TabIndex = 1;
			this.tabControlPanel_Notes.TabItem = this.tabItem_Notes;
			this.txtBoxX_BaoDianNote.Border.Border = eStyleBorderType.None;
			this.txtBoxX_BaoDianNote.Dock = DockStyle.Fill;
			this.txtBoxX_BaoDianNote.Location = new Point(1, 1);
			this.txtBoxX_BaoDianNote.Multiline = true;
			this.txtBoxX_BaoDianNote.Name = "txtBoxX_BaoDianNote";
			this.txtBoxX_BaoDianNote.Size = new Size(812, 96);
			this.txtBoxX_BaoDianNote.TabIndex = 0;
			this.tabItem_Notes.AttachedControl = this.tabControlPanel_Notes;
			this.tabItem_Notes.Name = "tabItem_Notes";
			this.tabItem_Notes.Text = "备注";
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.panel_BaoDian);
			base.Name = "BaoDianPanel";
			base.Size = new Size(1214, 462);
			this.panel_BaoDian.ResumeLayout(false);
			this.treePanel.ResumeLayout(false);
			((ISupportInitialize)this.advTree_BaoDian).EndInit();
			this.panel_BaoDianPicNote_Splt_Below.ResumeLayout(false);
			this.splitContainer_BaoDianPicNote.Panel1.ResumeLayout(false);
			this.splitContainer_BaoDianPicNote.Panel2.ResumeLayout(false);
			this.splitContainer_BaoDianPicNote.ResumeLayout(false);
			((ISupportInitialize)this.tabControl_ScrShot).EndInit();
			this.tabControl_ScrShot.ResumeLayout(false);
			this.tabCtrlPanel_ScrShot.ResumeLayout(false);
			this.panel_BaoDianScrShot.ResumeLayout(false);
			((ISupportInitialize)this.pictureBox_BaoDianScrShot).EndInit();
			((ISupportInitialize)this.tabControl_Notes).EndInit();
			this.tabControl_Notes.ResumeLayout(false);
			this.tabControlPanel_Notes.ResumeLayout(false);
			base.ResumeLayout(false);
		}

		// Token: 0x04000AE8 RID: 2792
		private bool bool_0;

		// Token: 0x04000AE9 RID: 2793
		private bool bool_1;

		// Token: 0x04000AEA RID: 2794
		private bool bool_2;

		// Token: 0x04000AEB RID: 2795
		private string string_0;

		// Token: 0x04000AEC RID: 2796
		private IContainer icontainer_0;

		// Token: 0x04000AED RID: 2797
		private Panel panel_BaoDian;

		// Token: 0x04000AEE RID: 2798
		private ExpandableSplitter expSpliter;

		// Token: 0x04000AEF RID: 2799
		private Panel treePanel;

		// Token: 0x04000AF0 RID: 2800
		private AdvTree advTree_BaoDian;

		// Token: 0x04000AF1 RID: 2801
		private DevComponents.AdvTree.ColumnHeader colHeader_BaoDianItem;

		// Token: 0x04000AF2 RID: 2802
		private DevComponents.AdvTree.ColumnHeader colHeader_BaoDianSymbl;

		// Token: 0x04000AF3 RID: 2803
		private DevComponents.AdvTree.ColumnHeader colHeader_BaoDianDate;

		// Token: 0x04000AF4 RID: 2804
		private Node node1;

		// Token: 0x04000AF5 RID: 2805
		private NodeConnector nodeConnector_0;

		// Token: 0x04000AF6 RID: 2806
		private ElementStyle elementStyle1;

		// Token: 0x04000AF7 RID: 2807
		private Panel panel_BaoDianPicNote_Splt_Below;

		// Token: 0x04000AF8 RID: 2808
		private SplitContainer splitContainer_BaoDianPicNote;

		// Token: 0x04000AF9 RID: 2809
		private Panel panel_BaoDianScrShot;

		// Token: 0x04000AFA RID: 2810
		private PictureBox pictureBox_BaoDianScrShot;

		// Token: 0x04000AFB RID: 2811
		private TextBoxX txtBoxX_BaoDianNote;

		// Token: 0x04000AFC RID: 2812
		private DevComponents.DotNetBar.TabControl tabControl_ScrShot;

		// Token: 0x04000AFD RID: 2813
		private TabControlPanel tabCtrlPanel_ScrShot;

		// Token: 0x04000AFE RID: 2814
		private TabItem tabItem_ScrShot;

		// Token: 0x04000AFF RID: 2815
		private DevComponents.DotNetBar.TabControl tabControl_Notes;

		// Token: 0x04000B00 RID: 2816
		private TabControlPanel tabControlPanel_Notes;

		// Token: 0x04000B01 RID: 2817
		private TabItem tabItem_Notes;
	}
}
