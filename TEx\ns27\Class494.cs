﻿using System;

namespace ns27
{
	// Token: 0x02000399 RID: 921
	internal sealed class Class494
	{
		// Token: 0x06002562 RID: 9570 RVA: 0x000F805C File Offset: 0x000F625C
		private int method_0(int int_0, int int_1, int int_2)
		{
			return Math.Min(Math.Min(int_0, int_1), int_2);
		}

		// Token: 0x06002563 RID: 9571 RVA: 0x000F807C File Offset: 0x000F627C
		private int method_1(string string_0, string string_1)
		{
			int length = string_0.Length;
			int length2 = string_1.Length;
			int result;
			if (length == 0)
			{
				result = length2;
			}
			else if (length2 == 0)
			{
				result = length;
			}
			else
			{
				int[,] array = new int[length + 1, length2 + 1];
				for (int i = 0; i <= length; i++)
				{
					array[i, 0] = i;
				}
				for (int j = 0; j <= length2; j++)
				{
					array[0, j] = j;
				}
				for (int i = 1; i <= length; i++)
				{
					char c = string_0[i - 1];
					for (int j = 1; j <= length2; j++)
					{
						char obj = string_1[j - 1];
						int num;
						if (c.Equals(obj))
						{
							num = 0;
						}
						else
						{
							num = 1;
						}
						array[i, j] = this.method_0(array[i - 1, j] + 1, array[i, j - 1] + 1, array[i - 1, j - 1] + num);
					}
				}
				result = array[length, length2];
			}
			return result;
		}

		// Token: 0x06002564 RID: 9572 RVA: 0x000F818C File Offset: 0x000F638C
		public double method_2(string string_0, string string_1)
		{
			int num = this.method_1(string_0, string_1);
			return 1.0 - (double)num / (double)Math.Max(string_0.Length, string_1.Length);
		}
	}
}
