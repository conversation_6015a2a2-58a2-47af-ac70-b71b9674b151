﻿using System;
using System.Runtime.InteropServices;

namespace SmartAssembly.Shared.ReportHelper
{
	// Token: 0x020003CA RID: 970
	public static class OsVersionInformation
	{
		// Token: 0x060026E9 RID: 9961
		[DllImport("kernel32.Dll")]
		private static extern short GetVersionEx(ref OsVersionInformation.Struct19 struct19_0);

		// Token: 0x060026EA RID: 9962 RVA: 0x000FCA74 File Offset: 0x000FAC74
		internal static bool smethod_0()
		{
			if (OsVersionInformation.nullable_0 == null)
			{
				try
				{
					OsVersionInformation.Struct19 @struct = default(OsVersionInformation.Struct19);
					@struct.int_0 = Marshal.SizeOf(typeof(OsVersionInformation.Struct19));
					OsVersionInformation.GetVersionEx(ref @struct);
					OsVersionInformation.nullable_0 = new bool?(false);
					OsVersionInformation.nullable_0 = new bool?(@struct.byte_0 == 1);
				}
				catch
				{
					OsVersionInformation.nullable_0 = new bool?(false);
				}
			}
			return OsVersionInformation.nullable_0.Value;
		}

		// Token: 0x170006BF RID: 1727
		// (get) Token: 0x060026EB RID: 9963 RVA: 0x000FCAFC File Offset: 0x000FACFC
		public static bool IsX64
		{
			get
			{
				if (OsVersionInformation.nullable_1 == null)
				{
					try
					{
						if (IntPtr.Size == 8)
						{
							OsVersionInformation.nullable_1 = new bool?(true);
						}
						else
						{
							bool flag;
							OsVersionInformation.nullable_1 = new bool?(OsVersionInformation.smethod_1("kernel32.dll", "IsWow64Process") && OsVersionInformation.IsWow64Process(OsVersionInformation.GetCurrentProcess(), out flag) && flag);
						}
					}
					catch
					{
						OsVersionInformation.nullable_1 = new bool?(false);
					}
				}
				return OsVersionInformation.nullable_1.Value;
			}
		}

		// Token: 0x060026EC RID: 9964 RVA: 0x000FCB80 File Offset: 0x000FAD80
		private static bool smethod_1(string string_0, string string_1)
		{
			IntPtr moduleHandle = OsVersionInformation.GetModuleHandle(string_0);
			return !(moduleHandle == IntPtr.Zero) && OsVersionInformation.GetProcAddress(moduleHandle, string_1) != IntPtr.Zero;
		}

		// Token: 0x060026ED RID: 9965
		[DllImport("kernel32.dll")]
		private static extern IntPtr GetCurrentProcess();

		// Token: 0x060026EE RID: 9966
		[DllImport("kernel32.dll", CharSet = CharSet.Auto)]
		private static extern IntPtr GetModuleHandle(string string_0);

		// Token: 0x060026EF RID: 9967
		[DllImport("kernel32", CharSet = CharSet.Auto, SetLastError = true)]
		private static extern IntPtr GetProcAddress(IntPtr intptr_0, [MarshalAs(UnmanagedType.LPStr)] string string_0);

		// Token: 0x060026F0 RID: 9968
		[DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
		[return: MarshalAs(UnmanagedType.Bool)]
		private static extern bool IsWow64Process(IntPtr intptr_0, out bool bool_0);

		// Token: 0x040012CA RID: 4810
		private static bool? nullable_0;

		// Token: 0x040012CB RID: 4811
		private static bool? nullable_1;

		// Token: 0x020003CB RID: 971
		private struct Struct19
		{
			// Token: 0x040012CC RID: 4812
			public int int_0;

			// Token: 0x040012CD RID: 4813
			public uint uint_0;

			// Token: 0x040012CE RID: 4814
			public uint uint_1;

			// Token: 0x040012CF RID: 4815
			public uint uint_2;

			// Token: 0x040012D0 RID: 4816
			public uint uint_3;

			// Token: 0x040012D1 RID: 4817
			[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 128)]
			public string string_0;

			// Token: 0x040012D2 RID: 4818
			public ushort ushort_0;

			// Token: 0x040012D3 RID: 4819
			public ushort ushort_1;

			// Token: 0x040012D4 RID: 4820
			public ushort ushort_2;

			// Token: 0x040012D5 RID: 4821
			public byte byte_0;

			// Token: 0x040012D6 RID: 4822
			private byte byte_1;
		}
	}
}
