﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using ns12;
using ns13;
using ns14;
using ns28;
using ns30;
using ns9;
using TEx.Inds;

namespace TEx.SIndicator
{
	// Token: 0x02000330 RID: 816
	public sealed class TreeSentence : Class412
	{
		// Token: 0x0600227A RID: 8826 RVA: 0x0000D8A3 File Offset: 0x0000BAA3
		public TreeSentence(HToken token, Class408 left, Class408 right) : base(token, left, right)
		{
		}

		// Token: 0x0600227B RID: 8827 RVA: 0x000E8564 File Offset: 0x000E6764
		public override string vmethod_0()
		{
			return base.vmethod_0();
		}

		// Token: 0x0600227C RID: 8828 RVA: 0x000EADF4 File Offset: 0x000E8FF4
		public static Class408 smethod_0(Tokenes tokenes_0, ParserEnvironment parserEnvironment_0)
		{
			string string_ = ":";
			HToken htoken;
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_4)
			{
				htoken = tokenes_0.Current;
				tokenes_0.method_1();
				if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_10 && tokenes_0.Current.Symbol.HSymbolType != Enum26.const_11)
				{
					throw new Exception(tokenes_0.Current.method_0("不是:或者:="));
				}
				if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_11)
				{
					string_ = ":=";
				}
				tokenes_0.method_1();
			}
			else
			{
				if (tokenes_0.Current.Symbol.Name != "" && tokenes_0.Current.Symbol.HSymbolType != Enum26.const_4)
				{
					HToken htoken2 = tokenes_0.Current;
					tokenes_0.method_1();
					if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_10)
					{
						if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_11)
						{
							tokenes_0.method_2();
							htoken = new HToken(tokenes_0.Current.Col, tokenes_0.Current.Line, new Class439(Enum26.const_4, ""));
							goto IL_169;
						}
					}
					throw new Exception(htoken2.method_0("此名称已被占用"));
				}
				htoken = new HToken(tokenes_0.Current.Col, tokenes_0.Current.Line, new Class439(Enum26.const_4, ""));
			}
			IL_169:
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
			{
				throw new Exception(tokenes_0.Current.method_0("赋值符后面没有内容。"));
			}
			Class408 @class = Class413.smethod_0(tokenes_0);
			if (@class.Token.Symbol.HSymbolType == Enum26.const_4 && !parserEnvironment_0.NewVarList.Contains(@class.Token.Symbol.Name))
			{
				throw new Exception(@class.Token.method_0("无法识别"));
			}
			tokenes_0.method_1();
			Class408 class408_;
			if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_30)
			{
				if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_22)
				{
					if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_21)
					{
						class408_ = TreeSentence.smethod_2(tokenes_0);
						goto IL_278;
					}
					throw new Exception(tokenes_0.Current.method_0("不是，号或者；号"));
				}
			}
			class408_ = new Class409(new HToken(tokenes_0.Current.Col, tokenes_0.Current.Line, new Class439(Enum26.const_30, "")));
			tokenes_0.method_2();
			IL_278:
			TreeSentence.smethod_1(htoken, parserEnvironment_0);
			Class409 class408_2 = new Class409(new HToken(htoken.Col, htoken.Line, new Class439(Enum26.const_30, "")));
			Class408 left = new Class418(htoken, class408_, class408_2);
			if (!string.IsNullOrEmpty(htoken.Symbol.Name))
			{
				parserEnvironment_0.method_2(htoken.Symbol.Name);
			}
			return new TreeSentence(new HToken(htoken.Col, htoken.Line, new Class439(Enum26.const_3, string_)), left, @class);
		}

		// Token: 0x0600227D RID: 8829 RVA: 0x000EB0F8 File Offset: 0x000E92F8
		private static void smethod_1(HToken htoken_1, ParserEnvironment parserEnvironment_0)
		{
			TreeSentence.Class442 @class = new TreeSentence.Class442();
			@class.string_0 = htoken_1.Symbol.Name;
			if (!parserEnvironment_0.Properties.Any(new Func<PropertyInfo, bool>(@class.method_0)) && !parserEnvironment_0.Functions.Any(new Func<MethodInfo, bool>(@class.method_1)) && !parserEnvironment_0.NewVarList.Any(new Func<string, bool>(@class.method_2)))
			{
				return;
			}
			throw new Exception(htoken_1.method_0("此变量名已经存在"));
		}

		// Token: 0x0600227E RID: 8830 RVA: 0x000EB17C File Offset: 0x000E937C
		public static Class408 smethod_2(Tokenes tokenes_0)
		{
			tokenes_0.method_1();
			if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_34 && tokenes_0.Current.Symbol.HSymbolType != Enum26.const_35 && tokenes_0.Current.Symbol.HSymbolType != Enum26.const_36)
			{
				if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_37)
				{
					throw new Exception(tokenes_0.Current.method_0("无法解析"));
				}
			}
			Class408 @class = new Class409(tokenes_0.Current);
			tokenes_0.method_1();
			Class408 class408_;
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_21)
			{
				class408_ = TreeSentence.smethod_2(tokenes_0);
			}
			else
			{
				if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_22)
				{
					throw new Exception(tokenes_0.Current.method_0("不是，号或者；号"));
				}
				tokenes_0.method_2();
				class408_ = new Class409(new HToken(tokenes_0.Current.Col, tokenes_0.Current.Line, new Class439(Enum26.const_30, "")));
			}
			return new Class412(new HToken(@class.Token.Col, @class.Token.Col, @class.Token.Symbol), @class, class408_);
		}

		// Token: 0x0600227F RID: 8831 RVA: 0x000EB2BC File Offset: 0x000E94BC
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			if (this.Token.Symbol.HSymbolType != Enum26.const_3)
			{
				throw new Exception(this.Token.method_0("无法解析的类型"));
			}
			object obj = this.Left.vmethod_1(parserEnvironment_0);
			if (obj.GetType() != typeof(List<string>))
			{
				throw new Exception(this.Left.Token.method_0("解析返回值错误"));
			}
			List<string> list = (List<string>)obj;
			if (!list.Any<string>())
			{
				throw new Exception(this.Left.Token.method_0("没有包含变量名称"));
			}
			object obj2 = this.Right.vmethod_1(parserEnvironment_0);
			string name = this.Token.Symbol.Name;
			DataArray dataArray;
			if (obj2.GetType() == typeof(DataArray))
			{
				dataArray = (obj2 as DataArray);
			}
			else
			{
				if (obj2.GetType() != typeof(double))
				{
					throw new Exception(this.Right.Token.method_0("解析返回值错误"));
				}
				if (name == ":=")
				{
					return new NameDoubleValue(list[0], (double)obj2);
				}
				PropertyInfo propertyInfo = parserEnvironment_0.Properties.SingleOrDefault(new Func<PropertyInfo, bool>(TreeSentence.<>c.<>9.method_0));
				if (propertyInfo == null)
				{
					throw new Exception("没有发现C属性。");
				}
				DataArray dataArray2 = propertyInfo.GetValue(parserEnvironment_0.UserDefineIns, null) as DataArray;
				if (dataArray2 == null)
				{
					throw new Exception("C属性转换DataArray错误。");
				}
				dataArray = new DataArray(dataArray2.Data.Length, (double)obj2);
			}
			dataArray.Visible = (name == ":");
			dataArray.NumVisibleLineNot = false;
			if (list.Any(new Func<string, bool>(TreeSentence.<>c.<>9.method_1)))
			{
				dataArray.Visible = false;
				dataArray.NumVisibleLineNot = true;
			}
			return this.method_2(dataArray, list);
		}

		// Token: 0x06002280 RID: 8832 RVA: 0x000EB4B8 File Offset: 0x000E96B8
		private DataArray method_2(object object_0, List<string> list_0)
		{
			if (object_0.GetType() != typeof(DataArray))
			{
				throw new Exception(this.Right.Token.method_0("无法解析此变量类型"));
			}
			DataArray dataArray = (DataArray)object_0;
			if (list_0.Count <= 0)
			{
				throw new Exception("没有数据");
			}
			dataArray.Name = list_0[0];
			for (int i = 1; i < list_0.Count; i++)
			{
				TreeSentence.Class443 @class = new TreeSentence.Class443();
				@class.string_0 = list_0[i].ToUpper();
				if (ParserEnvironment.smethod_4(@class.string_0))
				{
					dataArray.ColorStr = @class.string_0;
				}
				else if (ParserEnvironment.smethod_9(@class.string_0))
				{
					dataArray.LineTypeStr = @class.string_0;
				}
				else if (ParserEnvironment.smethod_10(@class.string_0))
				{
					dataArray.LineWithStr = @class.string_0;
				}
				else if (ParserEnvironment.string_0.Any(new Func<string, bool>(@class.method_0)))
				{
					dataArray.ShapeStr = @class.string_0;
				}
			}
			return dataArray;
		}

		// Token: 0x02000331 RID: 817
		[CompilerGenerated]
		private sealed class Class442
		{
			// Token: 0x06002282 RID: 8834 RVA: 0x000EB5C8 File Offset: 0x000E97C8
			internal bool method_0(PropertyInfo propertyInfo_0)
			{
				return propertyInfo_0.Name == this.string_0;
			}

			// Token: 0x06002283 RID: 8835 RVA: 0x000EB5C8 File Offset: 0x000E97C8
			internal bool method_1(MethodInfo methodInfo_0)
			{
				return methodInfo_0.Name == this.string_0;
			}

			// Token: 0x06002284 RID: 8836 RVA: 0x000EB5EC File Offset: 0x000E97EC
			internal bool method_2(string string_1)
			{
				return string_1 == this.string_0;
			}

			// Token: 0x040010C7 RID: 4295
			public string string_0;
		}

		// Token: 0x02000333 RID: 819
		[CompilerGenerated]
		private sealed class Class443
		{
			// Token: 0x0600228A RID: 8842 RVA: 0x000EB64C File Offset: 0x000E984C
			internal bool method_0(string string_1)
			{
				return string_1 == this.string_0;
			}

			// Token: 0x040010CB RID: 4299
			public string string_0;
		}
	}
}
