﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns11;
using ns18;
using ns20;
using ns22;
using ns23;
using ns28;
using TEx.Comn;

namespace TEx
{
	// Token: 0x02000161 RID: 353
	internal sealed partial class BlindTestForm : Form
	{
		// Token: 0x14000070 RID: 112
		// (add) Token: 0x06000D5B RID: 3419 RVA: 0x00052464 File Offset: 0x00050664
		// (remove) Token: 0x06000D5C RID: 3420 RVA: 0x0005249C File Offset: 0x0005069C
		public event Delegate3 SelectionChanged
		{
			[CompilerGenerated]
			add
			{
				Delegate3 @delegate = this.delegate3_0;
				Delegate3 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate3 value2 = (Delegate3)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate3>(ref this.delegate3_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate3 @delegate = this.delegate3_0;
				Delegate3 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate3 value2 = (Delegate3)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate3>(ref this.delegate3_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06000D5D RID: 3421 RVA: 0x000524D4 File Offset: 0x000506D4
		protected void method_0(int int_0, int int_1, DateTime? nullable_0)
		{
			EventArgs1 e = new EventArgs1(int_0, int_1, nullable_0, false, this.list_0);
			Delegate3 @delegate = this.delegate3_0;
			if (@delegate != null)
			{
				@delegate(e);
			}
		}

		// Token: 0x06000D5E RID: 3422 RVA: 0x00052508 File Offset: 0x00050708
		public BlindTestForm()
		{
			this.InitializeComponent();
			base.AutoScaleMode = AutoScaleMode.Dpi;
			this.method_1();
			base.Icon = (Icon)Class348.Resources.GetObject("BlindTestIcon");
			if (Base.UI.TransTabs != null)
			{
				this.list_1 = Base.UI.TransTabs.ZixuanStkSymbList;
			}
			if (Base.UI.Form.BlindTestForm_IfNoZixuan)
			{
				this.chkBox_Zixuan.Checked = false;
			}
			if (this.list_1 != null && this.list_1.Any<StkSymbol>())
			{
				this.chkBox_Zixuan.Click += this.chkBox_FtMths_Click;
			}
			if (!TApp.IsStIncluded)
			{
				this.chkBox_StIdx.Checked = false;
				this.chkBox_StFond.Checked = false;
				this.chkBox_StMainStk.Checked = false;
				this.chkBox_StZhongxiaoban.Checked = false;
				this.chkBox_StChuangyeban.Checked = false;
				this.chkBox_StKCB.Checked = false;
				this.chkBox_StConvBond.Checked = false;
				this.method_2(false);
				this.groupBox_St.Enabled = false;
			}
			else
			{
				if (!Base.UI.Form.BlindTestForm_IfStIdx)
				{
					this.chkBox_StIdx.Checked = false;
				}
				if (!Base.UI.Form.BlindTestForm_IfStFond)
				{
					this.chkBox_StFond.Checked = false;
				}
				if (Base.UI.Form.BlindTestForm_IfNoStMain)
				{
					this.chkBox_StMainStk.Checked = false;
				}
				if (Base.UI.Form.BlindTestForm_IfNoStZX)
				{
					this.chkBox_StZhongxiaoban.Checked = false;
				}
				if (Base.UI.Form.BlindTestForm_IfNoStCYB)
				{
					this.chkBox_StChuangyeban.Checked = false;
				}
				if (Base.UI.Form.BlindTestForm_IfNoStKCB)
				{
					this.chkBox_StKCB.Checked = false;
				}
				if (!Base.UI.Form.BlindTestForm_IfStConvBond)
				{
					this.chkBox_StConvBond.Checked = false;
				}
				this.chkBox_StIdx.Click += this.chkBox_FtMths_Click;
				this.chkBox_StFond.Click += this.chkBox_FtMths_Click;
				this.chkBox_StMainStk.Click += this.chkBox_FtMths_Click;
				this.chkBox_StZhongxiaoban.Click += this.chkBox_FtMths_Click;
				this.chkBox_StChuangyeban.Click += this.chkBox_FtMths_Click;
				this.chkBox_StKCB.Click += this.chkBox_FtMths_Click;
				this.chkBox_StConvBond.Click += this.chkBox_FtMths_Click;
			}
			if (!TApp.IsFtIncluded)
			{
				this.chkBox_MIs.Checked = false;
				this.chkBox_FtIdx.Checked = false;
				this.chkBox_FtMths.Checked = false;
				this.method_3(false);
				this.groupBox_Ft.Enabled = false;
			}
			else
			{
				if (Base.UI.Form.BlindTestForm_IfNoFtMI)
				{
					this.chkBox_MIs.Checked = false;
				}
				if (!Base.UI.Form.BlindTestForm_IfFtIdx)
				{
					this.chkBox_FtIdx.Checked = false;
				}
				if (!Base.UI.Form.BlindTestForm_IfFtMth)
				{
					this.chkBox_FtMths.Checked = false;
				}
				this.chkBox_MIs.Click += this.chkBox_FtMths_Click;
				this.chkBox_FtIdx.Click += this.chkBox_FtMths_Click;
				this.chkBox_FtMths.Click += this.chkBox_FtMths_Click;
			}
			this.comboBox_Symbls.SelectedIndexChanged += this.comboBox_Symbls_SelectedIndexChanged;
			this.chkBox_FtMths.CheckedChanged += this.chkBox_FtMths_CheckedChanged;
			this.random_0 = new Random();
		}

		// Token: 0x06000D5F RID: 3423 RVA: 0x0005286C File Offset: 0x00050A6C
		private void method_1()
		{
			float num = TApp.smethod_4(9f, false);
			if (this.Font.Size != num)
			{
				this.Font = new Font("Microsoft YaHei", num);
			}
			List<Control> list = new List<Control>();
			foreach (object obj in base.Controls)
			{
				Control item = (Control)obj;
				list.Add(item);
			}
			foreach (object obj2 in this.panel1.Controls)
			{
				Control item2 = (Control)obj2;
				list.Add(item2);
			}
			foreach (object obj3 in this.groupBox_Ft.Controls)
			{
				Control item3 = (Control)obj3;
				list.Add(item3);
			}
			foreach (object obj4 in this.groupBox_St.Controls)
			{
				Control item4 = (Control)obj4;
				list.Add(item4);
			}
			foreach (object obj5 in this.groupBox_Zixuan.Controls)
			{
				Control item5 = (Control)obj5;
				list.Add(item5);
			}
			foreach (Control control in list)
			{
				if (control.Font.Size != num)
				{
					control.Font = new Font("Microsoft YaHei", num);
				}
			}
		}

		// Token: 0x06000D60 RID: 3424 RVA: 0x00005EBF File Offset: 0x000040BF
		private void chkBox_FtMths_CheckedChanged(object sender, EventArgs e)
		{
			if (this.chkBox_FtMths.Checked && MessageBox.Show("期货品种建议在主连或指数合约范围内选取，月份合约很多时段成交量较少，行情不完全。仍然选择月份合约吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
			{
				this.chkBox_FtMths.Checked = false;
			}
		}

		// Token: 0x06000D61 RID: 3425 RVA: 0x00052AA4 File Offset: 0x00050CA4
		private void method_2(bool bool_0)
		{
			this.chkBox_StIdx.Enabled = bool_0;
			this.chkBox_StFond.Enabled = bool_0;
			this.chkBox_StMainStk.Enabled = bool_0;
			this.chkBox_StZhongxiaoban.Enabled = bool_0;
			this.chkBox_StChuangyeban.Enabled = bool_0;
			this.chkBox_StKCB.Enabled = bool_0;
			this.chkBox_StConvBond.Enabled = bool_0;
		}

		// Token: 0x06000D62 RID: 3426 RVA: 0x00005EF1 File Offset: 0x000040F1
		private void method_3(bool bool_0)
		{
			this.chkBox_MIs.Enabled = bool_0;
			this.chkBox_FtIdx.Enabled = bool_0;
			this.chkBox_FtMths.Enabled = bool_0;
		}

		// Token: 0x06000D63 RID: 3427 RVA: 0x00005F19 File Offset: 0x00004119
		private void BlindTestForm_Load(object sender, EventArgs e)
		{
			this.labelX_info.Text = BlindTestForm.string_0;
			this.radioBtn_RandomTime.Checked = true;
			this.list_0 = new List<StkSymbol>();
			this.method_4();
		}

		// Token: 0x06000D64 RID: 3428 RVA: 0x000041AE File Offset: 0x000023AE
		private void BlindTestForm_Shown(object sender, EventArgs e)
		{
		}

		// Token: 0x06000D65 RID: 3429 RVA: 0x00005F4A File Offset: 0x0000414A
		private void method_4()
		{
			this.method_5();
			this.method_6();
		}

		// Token: 0x06000D66 RID: 3430 RVA: 0x00052B08 File Offset: 0x00050D08
		private void method_5()
		{
			List<StkSymbol> list = Base.Data.smethod_85(this.IsZiXuanSelected, this.chkBox_MIs.Enabled && this.chkBox_MIs.Checked, this.chkBox_FtIdx.Enabled && this.chkBox_FtIdx.Checked, this.chkBox_FtMths.Enabled && this.chkBox_FtMths.Checked, this.chkBox_StIdx.Enabled && this.chkBox_StIdx.Checked, this.chkBox_StFond.Enabled && this.chkBox_StFond.Checked, this.chkBox_StMainStk.Enabled && this.chkBox_StMainStk.Checked, this.chkBox_StZhongxiaoban.Enabled && this.chkBox_StZhongxiaoban.Checked, this.chkBox_StChuangyeban.Enabled && this.chkBox_StChuangyeban.Checked, this.chkBox_StKCB.Enabled && this.chkBox_StKCB.Checked, this.chkBox_StConvBond.Enabled && this.chkBox_StConvBond.Checked);
			if (!list.Any<StkSymbol>())
			{
				if (this.groupBox_Ft.Enabled)
				{
					List<StkSymbol> collection = Base.Data.smethod_93();
					list.AddRange(collection);
					this.chkBox_MIs.Checked = true;
				}
				else
				{
					List<StkSymbol> collection2 = Base.Data.smethod_101(false);
					list.AddRange(collection2);
					this.chkBox_StMainStk.Checked = true;
				}
			}
			this.list_0 = list;
		}

		// Token: 0x06000D67 RID: 3431 RVA: 0x00052C84 File Offset: 0x00050E84
		private void method_6()
		{
			this.comboBox_Symbls.Items.Clear();
			if (this.list_0.Count > 1)
			{
				this.comboBox_Symbls.Items.Add("随机");
				using (List<StkSymbol>.Enumerator enumerator = this.list_0.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						StkSymbol stkSymbol = enumerator.Current;
						this.comboBox_Symbls.Items.Add(stkSymbol.Desc);
					}
					goto IL_9C;
				}
			}
			this.comboBox_Symbls.Items.Add(this.list_0.First<StkSymbol>().Code);
			IL_9C:
			this.comboBox_Symbls.SelectedItem = this.comboBox_Symbls.Items[0];
		}

		// Token: 0x06000D68 RID: 3432 RVA: 0x00052D5C File Offset: 0x00050F5C
		private void comboBox_Symbls_SelectedIndexChanged(object sender, EventArgs e)
		{
			if (this.comboBox_Symbls.Items.Count > 1)
			{
				if (this.comboBox_Symbls.SelectedIndex > 0)
				{
					if (this.groupBox_Ft.Enabled)
					{
						this.method_3(false);
					}
					if (this.groupBox_St.Enabled)
					{
						this.method_2(false);
					}
					if (this.groupBox_Zixuan.Enabled)
					{
						this.chkBox_Zixuan.Enabled = false;
					}
				}
				else
				{
					if (this.groupBox_Zixuan.Enabled)
					{
						this.chkBox_Zixuan.Enabled = true;
					}
					if (this.groupBox_Ft.Enabled)
					{
						this.method_3(true);
					}
					if (this.groupBox_St.Enabled)
					{
						this.method_2(true);
					}
				}
			}
		}

		// Token: 0x06000D69 RID: 3433 RVA: 0x00052E14 File Offset: 0x00051014
		private void chkBox_FtMths_Click(object sender, EventArgs e)
		{
			CheckBox checkBox = sender as CheckBox;
			if (!checkBox.Checked)
			{
				if (this.IfNoneSelected)
				{
					checkBox.Checked = true;
				}
				else
				{
					this.method_4();
				}
			}
			else
			{
				this.method_4();
			}
		}

		// Token: 0x06000D6A RID: 3434 RVA: 0x00052E54 File Offset: 0x00051054
		private void btnOK_Click(object sender, EventArgs e)
		{
			Class46.smethod_3(Class22.BlindTestEntering, "CurrSymb:" + Base.Data.CurrSelectedSymbol.Code);
			this.labelX_info.Text = "正在选取双盲测试品种数据...";
			this.btnOK.Enabled = false;
			this.btnCancel.Enabled = false;
			this.Refresh();
			StkSymbol stkSymbol = this.method_7();
			int num = 0;
			UsrStkMeta usrStkMeta;
			for (;;)
			{
				usrStkMeta = Base.Data.smethod_90(stkSymbol.ID);
				if (this.comboBox_Symbls.SelectedIndex > 0)
				{
					break;
				}
				if (usrStkMeta != null && (usrStkMeta.EndDate.Value.Date - usrStkMeta.BeginDate.Value.Date).TotalDays >= (double)SymbMgr.MinBlindTestDays)
				{
					goto IL_11E;
				}
				int seed = Environment.TickCount + num;
				this.random_0 = new Random(seed);
				stkSymbol = this.method_7();
				num++;
			}
			if (usrStkMeta == null)
			{
				this.labelX_info.Text = BlindTestForm.string_0;
				MessageBox.Show("测试品种在可选期间无可用行情记录，请重新选择。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
				this.btnOK.Enabled = true;
				this.btnCancel.Enabled = true;
				return;
			}
			IL_11E:
			this.method_8(usrStkMeta, stkSymbol);
			this.btnOK.Enabled = false;
			this.btnCancel.Enabled = false;
			this.labelX_info.Enabled = false;
			base.Close();
		}

		// Token: 0x06000D6B RID: 3435 RVA: 0x00052FB4 File Offset: 0x000511B4
		private StkSymbol method_7()
		{
			StkSymbol result = Base.Data.CurrSelectedSymbol;
			if (this.list_0.Count > 1)
			{
				if (this.comboBox_Symbls.SelectedIndex > 0)
				{
					result = this.list_0[this.comboBox_Symbls.SelectedIndex - 1];
					Base.UI.Form.IsSingleBlindTest = true;
				}
				else
				{
					int num = this.random_0.Next(0, this.list_0.Count);
					if (num == this.list_0.Count)
					{
						num = this.list_0.Count - 1;
					}
					result = this.list_0[num];
					Base.UI.Form.IsSingleBlindTest = false;
				}
			}
			else
			{
				Base.UI.Form.IsSingleBlindTest = true;
			}
			return result;
		}

		// Token: 0x06000D6C RID: 3436 RVA: 0x0005306C File Offset: 0x0005126C
		private void method_8(UsrStkMeta usrStkMeta_0, StkSymbol stkSymbol_0)
		{
			double num = (usrStkMeta_0.EndDate.Value.Date - usrStkMeta_0.BeginDate.Value.Date).TotalDays;
			if (num < 1.0)
			{
				num = 1.0;
			}
			double d = num;
			if (num > 480.0)
			{
				d = num - 240.0;
			}
			else if (num > 240.0)
			{
				d = num - 120.0;
			}
			else if (num > 120.0)
			{
				d = num - 120.0;
			}
			else if (num > 60.0)
			{
				d = num - 30.0;
			}
			DateTime dateTime = usrStkMeta_0.BeginDate.Value.AddDays((double)this.random_0.Next(0, Convert.ToInt32(Math.Floor(d))));
			ExchgOBT exchgOBT = Base.Data.smethod_110(stkSymbol_0, dateTime);
			SymbNtTrDate symbNtTrDate = Base.Data.smethod_112(stkSymbol_0, dateTime);
			if (this.radioBtn_RandomTime.Checked)
			{
				if (exchgOBT.DayCloseTime != null && exchgOBT.DayOpenTime != null)
				{
					bool withNightData = symbNtTrDate != null;
					dateTime = dateTime.Date.Add(exchgOBT.GetRandomTradingDT(withNightData, this.random_0).TimeOfDay);
				}
			}
			else
			{
				dateTime = dateTime.Date.Add(exchgOBT.DayOpenTime.Value.TimeOfDay);
			}
			if (!Base.UI.Form.IsInBlindTestMode)
			{
				Base.UI.Form.IsInBlindTestMode = true;
				Base.UI.Form.LastSymbIDPriorToBlindTest = new int?(Base.Data.CurrSelectedSymbol.ID);
				Base.UI.Form.LastSymbDTPriorToBlindTest = new DateTime?(Base.Data.CurrDate);
			}
			if (this.groupBox_Zixuan.Enabled)
			{
				Base.UI.Form.BlindTestForm_IfNoZixuan = !this.chkBox_Zixuan.Checked;
			}
			if (this.groupBox_St.Enabled)
			{
				Base.UI.Form.BlindTestForm_IfStIdx = this.chkBox_StIdx.Checked;
				Base.UI.Form.BlindTestForm_IfStFond = this.chkBox_StFond.Checked;
				Base.UI.Form.BlindTestForm_IfNoStMain = !this.chkBox_StMainStk.Checked;
				Base.UI.Form.BlindTestForm_IfNoStZX = !this.chkBox_StZhongxiaoban.Checked;
				Base.UI.Form.BlindTestForm_IfNoStCYB = !this.chkBox_StChuangyeban.Checked;
				Base.UI.Form.BlindTestForm_IfNoStKCB = !this.chkBox_StKCB.Checked;
				Base.UI.Form.BlindTestForm_IfStConvBond = this.chkBox_StConvBond.Checked;
			}
			if (this.groupBox_Ft.Enabled)
			{
				Base.UI.Form.BlindTestForm_IfNoFtMI = !this.chkBox_MIs.Checked;
				Base.UI.Form.BlindTestForm_IfFtIdx = this.chkBox_FtIdx.Checked;
				Base.UI.Form.BlindTestForm_IfFtMth = this.chkBox_FtMths.Checked;
			}
			Base.UI.smethod_47();
			this.method_0(Base.Data.CurrSelectedSymbol.ID, stkSymbol_0.ID, new DateTime?(dateTime));
		}

		// Token: 0x06000D6D RID: 3437 RVA: 0x00005F5A File Offset: 0x0000415A
		private void method_9()
		{
			MessageBox.Show("所选期间内没有行情记录！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
		}

		// Token: 0x06000D6E RID: 3438 RVA: 0x0005338C File Offset: 0x0005158C
		private int method_10(GroupBox groupBox_0)
		{
			return groupBox_0.Controls.OfType<CheckBox>().Where(new Func<CheckBox, bool>(BlindTestForm.<>c.<>9.method_0)).Count<CheckBox>();
		}

		// Token: 0x1700021B RID: 539
		// (get) Token: 0x06000D6F RID: 3439 RVA: 0x000533D4 File Offset: 0x000515D4
		private bool IsNoFtSeleted
		{
			get
			{
				return this.method_10(this.groupBox_Ft) == 0;
			}
		}

		// Token: 0x1700021C RID: 540
		// (get) Token: 0x06000D70 RID: 3440 RVA: 0x000533F4 File Offset: 0x000515F4
		private bool IsNoStSeleted
		{
			get
			{
				return this.method_10(this.groupBox_St) == 0;
			}
		}

		// Token: 0x1700021D RID: 541
		// (get) Token: 0x06000D71 RID: 3441 RVA: 0x00053414 File Offset: 0x00051614
		private bool IsZiXuanSelected
		{
			get
			{
				bool result;
				if (this.chkBox_Zixuan.Enabled)
				{
					result = this.chkBox_Zixuan.Checked;
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x1700021E RID: 542
		// (get) Token: 0x06000D72 RID: 3442 RVA: 0x00053444 File Offset: 0x00051644
		private bool IfNoneSelected
		{
			get
			{
				bool result;
				if (this.IsNoFtSeleted && this.IsNoStSeleted)
				{
					result = !this.IsZiXuanSelected;
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x06000D73 RID: 3443 RVA: 0x00005F71 File Offset: 0x00004171
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x040006BB RID: 1723
		[CompilerGenerated]
		private Delegate3 delegate3_0;

		// Token: 0x040006BC RID: 1724
		private static readonly string string_0 = "双盲测试默认随机选取交易品种并跳至随机日期，隐藏品种和日期信息，方便交易者不依赖于对行情的记忆，进行客观的交易水平测试。";

		// Token: 0x040006BD RID: 1725
		private List<StkSymbol> list_0;

		// Token: 0x040006BE RID: 1726
		private List<StkSymbol> list_1;

		// Token: 0x040006BF RID: 1727
		private Random random_0;

		// Token: 0x040006C0 RID: 1728
		private IContainer icontainer_0;
	}
}
