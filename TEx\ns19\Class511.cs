﻿using System;
using ns5;

namespace ns19
{
	// Token: 0x020003C5 RID: 965
	internal sealed class Class511
	{
		// Token: 0x170006BD RID: 1725
		// (get) Token: 0x060026DB RID: 9947 RVA: 0x0000EE40 File Offset: 0x0000D040
		public static string SubkeyApplication
		{
			get
			{
				return "Software\\Red Gate\\" + Class509.AppName;
			}
		}

		// Token: 0x170006BE RID: 1726
		// (get) Token: 0x060026DC RID: 9948 RVA: 0x0000EE51 File Offset: 0x0000D051
		public static string WowSubkeyApplication
		{
			get
			{
				return "Software\\Wow6432Node\\Red Gate\\" + Class509.AppName;
			}
		}

		// Token: 0x060026DD RID: 9949 RVA: 0x00002D25 File Offset: 0x00000F25
		private Class511()
		{
		}
	}
}
