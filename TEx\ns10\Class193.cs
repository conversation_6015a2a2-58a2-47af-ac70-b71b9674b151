﻿using System;
using System.Diagnostics;
using System.Runtime.InteropServices;

namespace ns10
{
	// Token: 0x0200013C RID: 316
	internal static class Class193
	{
		// Token: 0x1700020F RID: 527
		// (get) Token: 0x06000CD9 RID: 3289 RVA: 0x0004AAE8 File Offset: 0x00048CE8
		public static Class193.Enum9 ProgramBits
		{
			get
			{
				Environment.GetEnvironmentVariables();
				int num = IntPtr.Size * 8;
				Class193.Enum9 result;
				if (num != 32)
				{
					if (num == 64)
					{
						result = Class193.Enum9.const_2;
					}
					else
					{
						result = Class193.Enum9.const_0;
					}
				}
				else
				{
					result = Class193.Enum9.const_1;
				}
				return result;
			}
		}

		// Token: 0x17000210 RID: 528
		// (get) Token: 0x06000CDA RID: 3290 RVA: 0x0004AB20 File Offset: 0x00048D20
		public static Class193.Enum9 OSBits
		{
			get
			{
				int num = IntPtr.Size * 8;
				Class193.Enum9 result;
				if (num != 32)
				{
					if (num == 64)
					{
						result = Class193.Enum9.const_2;
					}
					else
					{
						result = Class193.Enum9.const_0;
					}
				}
				else if (Class193.smethod_1())
				{
					result = Class193.Enum9.const_2;
				}
				else
				{
					result = Class193.Enum9.const_1;
				}
				return result;
			}
		}

		// Token: 0x17000211 RID: 529
		// (get) Token: 0x06000CDB RID: 3291 RVA: 0x0004AB5C File Offset: 0x00048D5C
		public static Class193.Enum10 ProcessorBits
		{
			get
			{
				Class193.Enum10 result = Class193.Enum10.const_0;
				try
				{
					Class193.Struct1 @struct = default(Class193.Struct1);
					Class193.GetNativeSystemInfo(ref @struct);
					ushort ushort_ = @struct.struct2_0.ushort_0;
					if (ushort_ != 0)
					{
						if (ushort_ != 6)
						{
							if (ushort_ == 9)
							{
								result = Class193.Enum10.const_2;
							}
							else
							{
								result = Class193.Enum10.const_0;
							}
						}
						else
						{
							result = Class193.Enum10.const_3;
						}
					}
					else
					{
						result = Class193.Enum10.const_1;
					}
				}
				catch
				{
				}
				return result;
			}
		}

		// Token: 0x17000212 RID: 530
		// (get) Token: 0x06000CDC RID: 3292 RVA: 0x0004ABBC File Offset: 0x00048DBC
		public static string Edition
		{
			get
			{
				string result;
				if (Class193.string_0 != null)
				{
					result = Class193.string_0;
				}
				else
				{
					string text = string.Empty;
					OperatingSystem osversion = Environment.OSVersion;
					Class193.Struct0 @struct = default(Class193.Struct0);
					@struct.int_0 = Marshal.SizeOf(typeof(Class193.Struct0));
					if (Class193.GetVersionEx(ref @struct))
					{
						int major = osversion.Version.Major;
						int minor = osversion.Version.Minor;
						byte byte_ = @struct.byte_0;
						short short_ = @struct.short_2;
						int num;
						if (major == 4)
						{
							if (byte_ == 1)
							{
								text = "Workstation";
							}
							else if (byte_ == 3)
							{
								if ((short_ & 2) != 0)
								{
									text = "Enterprise Server";
								}
								else
								{
									text = "Standard Server";
								}
							}
						}
						else if (major == 5)
						{
							if (byte_ == 1)
							{
								if ((short_ & 512) != 0)
								{
									text = "Home";
								}
								else if (Class193.GetSystemMetrics(86) == 0)
								{
									text = "Professional";
								}
								else
								{
									text = "Tablet Edition";
								}
							}
							else if (byte_ == 3)
							{
								if (minor == 0)
								{
									if ((short_ & 128) != 0)
									{
										text = "Datacenter Server";
									}
									else if ((short_ & 2) != 0)
									{
										text = "Advanced Server";
									}
									else
									{
										text = "Server";
									}
								}
								else if ((short_ & 128) != 0)
								{
									text = "Datacenter";
								}
								else if ((short_ & 2) != 0)
								{
									text = "Enterprise";
								}
								else if ((short_ & 1024) != 0)
								{
									text = "Web Edition";
								}
								else
								{
									text = "Standard";
								}
							}
						}
						else if (major == 6 && Class193.GetProductInfo(major, minor, (int)@struct.short_0, (int)@struct.short_1, out num))
						{
							switch (num)
							{
							case 0:
								text = "Unknown product";
								break;
							case 1:
								text = "Ultimate";
								break;
							case 2:
								text = "Home Basic";
								break;
							case 3:
								text = "Home Premium";
								break;
							case 4:
								text = "Enterprise";
								break;
							case 5:
								text = "Home Basic N";
								break;
							case 6:
								text = "Business";
								break;
							case 7:
								text = "Standard Server";
								break;
							case 8:
								text = "Datacenter Server";
								break;
							case 9:
								text = "Windows Small Business Server";
								break;
							case 10:
								text = "Enterprise Server";
								break;
							case 11:
								text = "Starter";
								break;
							case 12:
								text = "Datacenter Server (core installation)";
								break;
							case 13:
								text = "Standard Server (core installation)";
								break;
							case 14:
								text = "Enterprise Server (core installation)";
								break;
							case 15:
								text = "Enterprise Server for Itanium-based Systems";
								break;
							case 16:
								text = "Business N";
								break;
							case 17:
								text = "Web Server";
								break;
							case 18:
								text = "HPC Edition";
								break;
							case 20:
								text = "Express Storage Server";
								break;
							case 21:
								text = "Standard Storage Server";
								break;
							case 22:
								text = "Workgroup Storage Server";
								break;
							case 23:
								text = "Enterprise Storage Server";
								break;
							case 24:
								text = "Windows Essential Server Solutions";
								break;
							case 25:
								text = "Windows Small Business Server Premium";
								break;
							case 26:
								text = "Home Premium N";
								break;
							case 27:
								text = "Enterprise N";
								break;
							case 28:
								text = "Ultimate N";
								break;
							case 29:
								text = "Web Server (core installation)";
								break;
							case 30:
								text = "Windows Essential Business Management Server";
								break;
							case 31:
								text = "Windows Essential Business Security Server";
								break;
							case 32:
								text = "Windows Essential Business Messaging Server";
								break;
							case 33:
								text = "Server Foundation";
								break;
							case 34:
								text = "Home Premium Server";
								break;
							case 35:
								text = "Windows Essential Server Solutions without Hyper-V";
								break;
							case 36:
								text = "Standard Server without Hyper-V";
								break;
							case 37:
								text = "Datacenter Server without Hyper-V";
								break;
							case 38:
								text = "Enterprise Server without Hyper-V";
								break;
							case 39:
								text = "Datacenter Server without Hyper-V (core installation)";
								break;
							case 40:
								text = "Standard Server without Hyper-V (core installation)";
								break;
							case 41:
								text = "Enterprise Server without Hyper-V (core installation)";
								break;
							case 42:
								text = "Microsoft Hyper-V Server";
								break;
							case 43:
								text = "Express Storage Server (core installation)";
								break;
							case 44:
								text = "Standard Storage Server (core installation)";
								break;
							case 45:
								text = "Workgroup Storage Server (core installation)";
								break;
							case 46:
								text = "Enterprise Storage Server (core installation)";
								break;
							case 47:
								text = "Starter N";
								break;
							case 48:
								text = "Professional";
								break;
							case 49:
								text = "Professional N";
								break;
							case 50:
								text = "SB Solution Server";
								break;
							case 51:
								text = "Server for SB Solutions";
								break;
							case 52:
								text = "Standard Server Solutions";
								break;
							case 53:
								text = "Standard Server Solutions (core installation)";
								break;
							case 54:
								text = "SB Solution Server EM";
								break;
							case 55:
								text = "Server for SB Solutions EM";
								break;
							case 56:
								text = "Solution Embedded Server";
								break;
							case 57:
								text = "Solution Embedded Server (core installation)";
								break;
							case 59:
								text = "Essential Business Server MGMT";
								break;
							case 60:
								text = "Essential Business Server ADDL";
								break;
							case 61:
								text = "Essential Business Server MGMTSVC";
								break;
							case 62:
								text = "Essential Business Server ADDLSVC";
								break;
							case 63:
								text = "Windows Small Business Server Premium (core installation)";
								break;
							case 64:
								text = "HPC Edition without Hyper-V";
								break;
							case 65:
								text = "Embedded";
								break;
							case 66:
								text = "Starter E";
								break;
							case 67:
								text = "Home Basic E";
								break;
							case 68:
								text = "Home Premium E";
								break;
							case 69:
								text = "Professional E";
								break;
							case 70:
								text = "Enterprise E";
								break;
							case 71:
								text = "Ultimate E";
								break;
							}
						}
					}
					Class193.string_0 = text;
					result = text;
				}
				return result;
			}
		}

		// Token: 0x17000213 RID: 531
		// (get) Token: 0x06000CDD RID: 3293 RVA: 0x0004B160 File Offset: 0x00049360
		public static string Name
		{
			get
			{
				string result;
				if (Class193.string_1 != null)
				{
					result = Class193.string_1;
				}
				else
				{
					string text = "unknown";
					OperatingSystem osversion = Environment.OSVersion;
					Class193.Struct0 @struct = default(Class193.Struct0);
					@struct.int_0 = Marshal.SizeOf(typeof(Class193.Struct0));
					if (Class193.GetVersionEx(ref @struct))
					{
						int major = osversion.Version.Major;
						int minor = osversion.Version.Minor;
						switch (osversion.Platform)
						{
						case PlatformID.Win32S:
							text = "Windows 3.1";
							break;
						case PlatformID.Win32Windows:
							if (major == 4)
							{
								string a = @struct.string_0;
								if (minor != 0)
								{
									if (minor != 10)
									{
										if (minor == 90)
										{
											text = "Windows Me";
										}
									}
									else if (a == "A")
									{
										text = "Windows 98 Second Edition";
									}
									else
									{
										text = "Windows 98";
									}
								}
								else if (!(a == "B") && !(a == "C"))
								{
									text = "Windows 95";
								}
								else
								{
									text = "Windows 95 OSR2";
								}
							}
							break;
						case PlatformID.Win32NT:
						{
							byte byte_ = @struct.byte_0;
							switch (major)
							{
							case 3:
								text = "Windows NT 3.51";
								break;
							case 4:
								if (byte_ != 1)
								{
									if (byte_ == 3)
									{
										text = "Windows NT 4.0 Server";
									}
								}
								else
								{
									text = "Windows NT 4.0";
								}
								break;
							case 5:
								switch (minor)
								{
								case 0:
									text = "Windows 2000";
									break;
								case 1:
									text = "Windows XP";
									break;
								case 2:
									text = "Windows Server 2003";
									break;
								}
								break;
							case 6:
								switch (minor)
								{
								case 0:
									if (byte_ != 1)
									{
										if (byte_ == 3)
										{
											text = "Windows Server 2008";
										}
									}
									else
									{
										text = "Windows Vista";
									}
									break;
								case 1:
									if (byte_ != 1)
									{
										if (byte_ == 3)
										{
											text = "Windows Server 2008 R2";
										}
									}
									else
									{
										text = "Windows 7";
									}
									break;
								case 2:
									if (byte_ != 1)
									{
										if (byte_ == 3)
										{
											text = "Windows Server 2012";
										}
									}
									else
									{
										text = "Windows 8";
									}
									break;
								}
								break;
							}
							break;
						}
						case PlatformID.WinCE:
							text = "Windows CE";
							break;
						}
					}
					Class193.string_1 = text;
					result = text;
				}
				return result;
			}
		}

		// Token: 0x06000CDE RID: 3294
		[DllImport("Kernel32.dll")]
		internal static extern bool GetProductInfo(int int_81, int int_82, int int_83, int int_84, out int int_85);

		// Token: 0x06000CDF RID: 3295
		[DllImport("kernel32.dll")]
		private static extern bool GetVersionEx(ref Class193.Struct0 struct0_0);

		// Token: 0x06000CE0 RID: 3296
		[DllImport("user32")]
		public static extern int GetSystemMetrics(int int_81);

		// Token: 0x06000CE1 RID: 3297
		[DllImport("kernel32.dll")]
		public static extern void GetSystemInfo([MarshalAs(UnmanagedType.Struct)] ref Class193.Struct1 struct1_0);

		// Token: 0x06000CE2 RID: 3298
		[DllImport("kernel32.dll")]
		public static extern void GetNativeSystemInfo([MarshalAs(UnmanagedType.Struct)] ref Class193.Struct1 struct1_0);

		// Token: 0x06000CE3 RID: 3299
		[DllImport("kernel32", SetLastError = true)]
		public static extern IntPtr LoadLibrary(string string_2);

		// Token: 0x06000CE4 RID: 3300
		[DllImport("kernel32", SetLastError = true)]
		public static extern IntPtr GetProcAddress(IntPtr intptr_0, string string_2);

		// Token: 0x17000214 RID: 532
		// (get) Token: 0x06000CE5 RID: 3301 RVA: 0x0004B384 File Offset: 0x00049584
		public static string ServicePack
		{
			get
			{
				string empty = string.Empty;
				Class193.Struct0 @struct = default(Class193.Struct0);
				@struct.int_0 = Marshal.SizeOf(typeof(Class193.Struct0));
				if (Class193.GetVersionEx(ref @struct))
				{
					empty = @struct.string_0;
				}
				return empty;
			}
		}

		// Token: 0x17000215 RID: 533
		// (get) Token: 0x06000CE6 RID: 3302 RVA: 0x0004B3CC File Offset: 0x000495CC
		public static int BuildVersion
		{
			get
			{
				return Environment.OSVersion.Version.Build;
			}
		}

		// Token: 0x17000216 RID: 534
		// (get) Token: 0x06000CE7 RID: 3303 RVA: 0x0004B3EC File Offset: 0x000495EC
		public static string VersionString
		{
			get
			{
				return Environment.OSVersion.Version.ToString();
			}
		}

		// Token: 0x17000217 RID: 535
		// (get) Token: 0x06000CE8 RID: 3304 RVA: 0x0004B40C File Offset: 0x0004960C
		public static Version Version
		{
			get
			{
				return Environment.OSVersion.Version;
			}
		}

		// Token: 0x17000218 RID: 536
		// (get) Token: 0x06000CE9 RID: 3305 RVA: 0x0004B428 File Offset: 0x00049628
		public static int MajorVersion
		{
			get
			{
				return Environment.OSVersion.Version.Major;
			}
		}

		// Token: 0x17000219 RID: 537
		// (get) Token: 0x06000CEA RID: 3306 RVA: 0x0004B448 File Offset: 0x00049648
		public static int MinorVersion
		{
			get
			{
				return Environment.OSVersion.Version.Minor;
			}
		}

		// Token: 0x1700021A RID: 538
		// (get) Token: 0x06000CEB RID: 3307 RVA: 0x0004B468 File Offset: 0x00049668
		public static int RevisionVersion
		{
			get
			{
				return Environment.OSVersion.Version.Revision;
			}
		}

		// Token: 0x06000CEC RID: 3308 RVA: 0x0004B488 File Offset: 0x00049688
		private static Class193.Delegate7 smethod_0()
		{
			IntPtr intPtr = Class193.LoadLibrary("kernel32");
			if (intPtr != IntPtr.Zero)
			{
				IntPtr procAddress = Class193.GetProcAddress(intPtr, "IsWow64Process");
				if (procAddress != IntPtr.Zero)
				{
					return (Class193.Delegate7)Marshal.GetDelegateForFunctionPointer(procAddress, typeof(Class193.Delegate7));
				}
			}
			return null;
		}

		// Token: 0x06000CED RID: 3309 RVA: 0x0004B4E4 File Offset: 0x000496E4
		private static bool smethod_1()
		{
			Class193.Delegate7 @delegate = Class193.smethod_0();
			bool result;
			bool flag;
			if (@delegate == null)
			{
				result = false;
			}
			else if (!@delegate(Process.GetCurrentProcess().Handle, out flag))
			{
				result = false;
			}
			else
			{
				result = flag;
			}
			return result;
		}

		// Token: 0x0400054C RID: 1356
		private static string string_0;

		// Token: 0x0400054D RID: 1357
		private static string string_1;

		// Token: 0x0400054E RID: 1358
		private const int int_0 = 0;

		// Token: 0x0400054F RID: 1359
		private const int int_1 = 1;

		// Token: 0x04000550 RID: 1360
		private const int int_2 = 2;

		// Token: 0x04000551 RID: 1361
		private const int int_3 = 3;

		// Token: 0x04000552 RID: 1362
		private const int int_4 = 4;

		// Token: 0x04000553 RID: 1363
		private const int int_5 = 5;

		// Token: 0x04000554 RID: 1364
		private const int int_6 = 6;

		// Token: 0x04000555 RID: 1365
		private const int int_7 = 7;

		// Token: 0x04000556 RID: 1366
		private const int int_8 = 8;

		// Token: 0x04000557 RID: 1367
		private const int int_9 = 9;

		// Token: 0x04000558 RID: 1368
		private const int int_10 = 10;

		// Token: 0x04000559 RID: 1369
		private const int int_11 = 11;

		// Token: 0x0400055A RID: 1370
		private const int int_12 = 12;

		// Token: 0x0400055B RID: 1371
		private const int int_13 = 13;

		// Token: 0x0400055C RID: 1372
		private const int int_14 = 14;

		// Token: 0x0400055D RID: 1373
		private const int int_15 = 15;

		// Token: 0x0400055E RID: 1374
		private const int int_16 = 16;

		// Token: 0x0400055F RID: 1375
		private const int int_17 = 17;

		// Token: 0x04000560 RID: 1376
		private const int int_18 = 18;

		// Token: 0x04000561 RID: 1377
		private const int int_19 = 19;

		// Token: 0x04000562 RID: 1378
		private const int int_20 = 20;

		// Token: 0x04000563 RID: 1379
		private const int int_21 = 21;

		// Token: 0x04000564 RID: 1380
		private const int int_22 = 22;

		// Token: 0x04000565 RID: 1381
		private const int int_23 = 23;

		// Token: 0x04000566 RID: 1382
		private const int int_24 = 24;

		// Token: 0x04000567 RID: 1383
		private const int int_25 = 25;

		// Token: 0x04000568 RID: 1384
		private const int int_26 = 26;

		// Token: 0x04000569 RID: 1385
		private const int int_27 = 27;

		// Token: 0x0400056A RID: 1386
		private const int int_28 = 28;

		// Token: 0x0400056B RID: 1387
		private const int int_29 = 29;

		// Token: 0x0400056C RID: 1388
		private const int int_30 = 30;

		// Token: 0x0400056D RID: 1389
		private const int int_31 = 31;

		// Token: 0x0400056E RID: 1390
		private const int int_32 = 32;

		// Token: 0x0400056F RID: 1391
		private const int int_33 = 33;

		// Token: 0x04000570 RID: 1392
		private const int int_34 = 34;

		// Token: 0x04000571 RID: 1393
		private const int int_35 = 35;

		// Token: 0x04000572 RID: 1394
		private const int int_36 = 36;

		// Token: 0x04000573 RID: 1395
		private const int int_37 = 37;

		// Token: 0x04000574 RID: 1396
		private const int int_38 = 38;

		// Token: 0x04000575 RID: 1397
		private const int int_39 = 39;

		// Token: 0x04000576 RID: 1398
		private const int int_40 = 40;

		// Token: 0x04000577 RID: 1399
		private const int int_41 = 41;

		// Token: 0x04000578 RID: 1400
		private const int int_42 = 42;

		// Token: 0x04000579 RID: 1401
		private const int int_43 = 43;

		// Token: 0x0400057A RID: 1402
		private const int int_44 = 44;

		// Token: 0x0400057B RID: 1403
		private const int int_45 = 45;

		// Token: 0x0400057C RID: 1404
		private const int int_46 = 46;

		// Token: 0x0400057D RID: 1405
		private const int int_47 = 47;

		// Token: 0x0400057E RID: 1406
		private const int int_48 = 48;

		// Token: 0x0400057F RID: 1407
		private const int int_49 = 49;

		// Token: 0x04000580 RID: 1408
		private const int int_50 = 50;

		// Token: 0x04000581 RID: 1409
		private const int int_51 = 51;

		// Token: 0x04000582 RID: 1410
		private const int int_52 = 52;

		// Token: 0x04000583 RID: 1411
		private const int int_53 = 53;

		// Token: 0x04000584 RID: 1412
		private const int int_54 = 54;

		// Token: 0x04000585 RID: 1413
		private const int int_55 = 55;

		// Token: 0x04000586 RID: 1414
		private const int int_56 = 56;

		// Token: 0x04000587 RID: 1415
		private const int int_57 = 57;

		// Token: 0x04000588 RID: 1416
		private const int int_58 = 59;

		// Token: 0x04000589 RID: 1417
		private const int int_59 = 60;

		// Token: 0x0400058A RID: 1418
		private const int int_60 = 61;

		// Token: 0x0400058B RID: 1419
		private const int int_61 = 62;

		// Token: 0x0400058C RID: 1420
		private const int int_62 = 63;

		// Token: 0x0400058D RID: 1421
		private const int int_63 = 64;

		// Token: 0x0400058E RID: 1422
		private const int int_64 = 65;

		// Token: 0x0400058F RID: 1423
		private const int int_65 = 66;

		// Token: 0x04000590 RID: 1424
		private const int int_66 = 67;

		// Token: 0x04000591 RID: 1425
		private const int int_67 = 68;

		// Token: 0x04000592 RID: 1426
		private const int int_68 = 69;

		// Token: 0x04000593 RID: 1427
		private const int int_69 = 70;

		// Token: 0x04000594 RID: 1428
		private const int int_70 = 71;

		// Token: 0x04000595 RID: 1429
		private const int int_71 = 1;

		// Token: 0x04000596 RID: 1430
		private const int int_72 = 2;

		// Token: 0x04000597 RID: 1431
		private const int int_73 = 3;

		// Token: 0x04000598 RID: 1432
		private const int int_74 = 1;

		// Token: 0x04000599 RID: 1433
		private const int int_75 = 2;

		// Token: 0x0400059A RID: 1434
		private const int int_76 = 16;

		// Token: 0x0400059B RID: 1435
		private const int int_77 = 128;

		// Token: 0x0400059C RID: 1436
		private const int int_78 = 256;

		// Token: 0x0400059D RID: 1437
		private const int int_79 = 512;

		// Token: 0x0400059E RID: 1438
		private const int int_80 = 1024;

		// Token: 0x0200013D RID: 317
		public enum Enum9
		{
			// Token: 0x040005A0 RID: 1440
			const_0,
			// Token: 0x040005A1 RID: 1441
			const_1,
			// Token: 0x040005A2 RID: 1442
			const_2
		}

		// Token: 0x0200013E RID: 318
		public enum Enum10
		{
			// Token: 0x040005A4 RID: 1444
			const_0,
			// Token: 0x040005A5 RID: 1445
			const_1,
			// Token: 0x040005A6 RID: 1446
			const_2,
			// Token: 0x040005A7 RID: 1447
			const_3
		}

		// Token: 0x0200013F RID: 319
		// (Invoke) Token: 0x06000CEF RID: 3311
		private delegate bool Delegate7([In] IntPtr handle, out bool isWow64Process);

		// Token: 0x02000140 RID: 320
		private struct Struct0
		{
			// Token: 0x040005A8 RID: 1448
			public int int_0;

			// Token: 0x040005A9 RID: 1449
			public int int_1;

			// Token: 0x040005AA RID: 1450
			public int int_2;

			// Token: 0x040005AB RID: 1451
			public int int_3;

			// Token: 0x040005AC RID: 1452
			public int int_4;

			// Token: 0x040005AD RID: 1453
			[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 128)]
			public string string_0;

			// Token: 0x040005AE RID: 1454
			public short short_0;

			// Token: 0x040005AF RID: 1455
			public short short_1;

			// Token: 0x040005B0 RID: 1456
			public short short_2;

			// Token: 0x040005B1 RID: 1457
			public byte byte_0;

			// Token: 0x040005B2 RID: 1458
			public byte byte_1;
		}

		// Token: 0x02000141 RID: 321
		public struct Struct1
		{
			// Token: 0x040005B3 RID: 1459
			internal Class193.Struct2 struct2_0;

			// Token: 0x040005B4 RID: 1460
			public uint uint_0;

			// Token: 0x040005B5 RID: 1461
			public IntPtr intptr_0;

			// Token: 0x040005B6 RID: 1462
			public IntPtr intptr_1;

			// Token: 0x040005B7 RID: 1463
			public IntPtr intptr_2;

			// Token: 0x040005B8 RID: 1464
			public uint uint_1;

			// Token: 0x040005B9 RID: 1465
			public uint uint_2;

			// Token: 0x040005BA RID: 1466
			public uint uint_3;

			// Token: 0x040005BB RID: 1467
			public ushort ushort_0;

			// Token: 0x040005BC RID: 1468
			public ushort ushort_1;
		}

		// Token: 0x02000142 RID: 322
		[StructLayout(LayoutKind.Explicit)]
		public struct Struct2
		{
			// Token: 0x040005BD RID: 1469
			[FieldOffset(0)]
			internal uint uint_0;

			// Token: 0x040005BE RID: 1470
			[FieldOffset(0)]
			internal ushort ushort_0;

			// Token: 0x040005BF RID: 1471
			[FieldOffset(2)]
			internal ushort ushort_1;
		}
	}
}
