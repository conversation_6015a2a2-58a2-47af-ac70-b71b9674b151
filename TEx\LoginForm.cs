﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Management;
using System.Net;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using System.Xml.Linq;
using NAppUpdate.Framework;
using NAppUpdate.Framework.Common;
using NAppUpdate.Framework.Sources;
using NAppUpdate.Framework.Tasks;
using Newtonsoft.Json;
using ns16;
using ns17;
using ns18;
using ns22;
using ns23;
using ns28;
using ns5;
using ns6;
using ns9;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x0200019B RID: 411
	internal sealed partial class LoginForm : Form0
	{
		// Token: 0x14000078 RID: 120
		// (add) Token: 0x06000FF2 RID: 4082 RVA: 0x00064CF4 File Offset: 0x00062EF4
		// (remove) Token: 0x06000FF3 RID: 4083 RVA: 0x00064D2C File Offset: 0x00062F2C
		public event Delegate12 LoggedIn
		{
			[CompilerGenerated]
			add
			{
				Delegate12 @delegate = this.delegate12_0;
				Delegate12 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate12 value2 = (Delegate12)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate12>(ref this.delegate12_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate12 @delegate = this.delegate12_0;
				Delegate12 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate12 value2 = (Delegate12)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate12>(ref this.delegate12_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06000FF4 RID: 4084 RVA: 0x00006CB4 File Offset: 0x00004EB4
		protected void method_1(EventArgs9 eventArgs9_0)
		{
			Delegate12 @delegate = this.delegate12_0;
			if (@delegate != null)
			{
				@delegate(this, eventArgs9_0);
			}
		}

		// Token: 0x14000079 RID: 121
		// (add) Token: 0x06000FF5 RID: 4085 RVA: 0x00064D64 File Offset: 0x00062F64
		// (remove) Token: 0x06000FF6 RID: 4086 RVA: 0x00064D9C File Offset: 0x00062F9C
		public event EventHandler Quit
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000FF7 RID: 4087 RVA: 0x00006CCB File Offset: 0x00004ECB
		protected void method_2(EventArgs eventArgs_0)
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, eventArgs_0);
			}
		}

		// Token: 0x06000FF8 RID: 4088 RVA: 0x00064DD4 File Offset: 0x00062FD4
		public LoginForm()
		{
			if (!TApp.IsHighDpiScreen)
			{
				base.AutoScaleMode = AutoScaleMode.Dpi;
			}
			this.InitializeComponent();
			base.FormBorderStyle = FormBorderStyle.None;
			this.linkLabel_Register.LinkBehavior = LinkBehavior.NeverUnderline;
			this.linkLabel_TExWebLink.LinkBehavior = LinkBehavior.NeverUnderline;
			this.linkLabel_PswdRcvr.LinkBehavior = LinkBehavior.NeverUnderline;
			this.linkLabel_ResetData.LinkBehavior = LinkBehavior.NeverUnderline;
			this.label_msg.ForeColor = Class179.color_10;
			this.label_账号.ForeColor = Class179.color_3;
			this.label_密码.ForeColor = Class179.color_3;
			this.checkBox_remPsw.ForeColor = Class179.color_3;
			this.class297_0 = new Class297(Color.White);
			this.class297_0.Anchor = (AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right);
			this.class297_0.BackColor = Color.White;
			this.class297_0.Size = new Size(base.Width, 4);
			this.class297_0.Style = ProgressBarStyle.Continuous;
			this.class297_0.TabIndex = 12;
			base.Controls.Add(this.class297_0);
			this.class297_0.BarColor = Color.FromArgb(59, 148, 208);
			this.pictureBox_Banner.Image = Class372.LoginBannerNew;
			if (!TApp.IsHighDpiScreen)
			{
				float emSize = TApp.smethod_4(9f, false);
				Font font = new Font("SimSun", emSize);
				this.label_账号.Font = font;
				this.label_密码.Font = font;
				this.linkLabel_Register.Font = font;
				this.linkLabel_PswdRcvr.Font = font;
				this.linkLabel_ResetData.Font = font;
				this.btn_Login.Font = font;
				this.checkBox_remPsw.Font = font;
				font = new Font("Arial", emSize);
				this.textBox_UsrName.Font = font;
				this.maskedTextBox_PW.Font = font;
				Font font2 = new Font("Microsoft Sans Serif", TApp.smethod_4(8f, false));
				this.label_ver.Font = font2;
				this.label_msg.Font = font2;
				this.linkLabel_TExWebLink.Font = new Font("Microsoft Sans Serif", emSize);
			}
			this.Text = TApp.AppName;
		}

		// Token: 0x06000FF9 RID: 4089 RVA: 0x00064FF8 File Offset: 0x000631F8
		private void LoginForm_Load(object sender, EventArgs e)
		{
			this.splashScreen_0 = new SplashScreen();
			this.splashScreen_0.UpdateDispMsg("正在启动程序...");
			base.Icon = Class372.TExIcoBlue;
			this.label_msg.Visible = false;
			this.class297_0.Visible = false;
			this.backgroundWorker_0 = new BackgroundWorker();
			this.backgroundWorker_0.WorkerReportsProgress = true;
			this.backgroundWorker_0.WorkerSupportsCancellation = true;
			this.backgroundWorker_0.DoWork += this.backgroundWorker_0_DoWork;
			this.backgroundWorker_0.ProgressChanged += this.backgroundWorker_0_ProgressChanged;
			this.backgroundWorker_0.RunWorkerCompleted += this.backgroundWorker_0_RunWorkerCompleted;
			UpdateManager instance = UpdateManager.Instance;
			instance.UpdateSource = new SimpleWebSource();
			instance.Config.TempFolder = Path.Combine(TApp.string_10, "Updates");
			instance.Config.BackupFolder = Path.Combine(TApp.string_10, "Backup" + DateTime.Now.Ticks);
			instance.ReinstateIfRestarted();
			this.label_ver.Text = "v" + TApp.Ver;
			for (int i = 0; i < 2; i++)
			{
				if (this.label_ver.Text.EndsWith(".0"))
				{
					this.label_ver.Text = this.label_ver.Text.Substring(0, this.label_ver.Text.Length - 2);
				}
			}
			this.pictureBox_Banner.Location = new Point(0, 0);
			this.pictureBox_Banner.Width = base.Width;
			this.pictureBox_Banner.MouseDown += this.pictureBox_Banner_MouseDown;
			this.linkLabel_Register.LinkClicked += this.linkLabel_Register_LinkClicked;
			this.linkLabel_PswdRcvr.LinkClicked += this.linkLabel_PswdRcvr_LinkClicked;
			this.linkLabel_ResetData.LinkClicked += this.linkLabel_ResetData_LinkClicked;
			this.linkLabel_ResetData.Parent = this.pictureBox_Banner;
			this.linkLabel_ResetData.LinkColor = Class179.color_6;
			int x = this.pictureBox_Banner.Width - 230;
			if (TApp.IsHighDpiScreen)
			{
				x = this.pictureBox_Banner.Width - 190;
			}
			this.linkLabel_ResetData.Location = new Point(x, 14);
			this.linkLabel_PswdRcvr.Parent = this.pictureBox_Banner;
			this.linkLabel_PswdRcvr.LinkColor = Class179.color_6;
			x = this.pictureBox_Banner.Width - 140;
			if (TApp.IsHighDpiScreen)
			{
				x = this.pictureBox_Banner.Width - 115;
			}
			this.linkLabel_PswdRcvr.Location = new Point(x, 14);
			this.label_ver.Parent = this.pictureBox_Banner;
			this.label_ver.BackColor = Color.Transparent;
			this.label_ver.ForeColor = Class179.color_8;
			this.label_ver.Location = new Point(this.pictureBox_Banner.Width - this.label_ver.Width, this.pictureBox_Banner.Height - this.label_ver.Height - this.linkLabel_TExWebLink.Height + 1);
			this.linkLabel_TExWebLink.Parent = this.pictureBox_Banner;
			this.linkLabel_TExWebLink.BackColor = Color.Transparent;
			this.linkLabel_TExWebLink.LinkColor = Class179.color_19;
			this.linkLabel_TExWebLink.Location = new Point(this.pictureBox_Banner.Width - this.linkLabel_TExWebLink.Width, this.pictureBox_Banner.Height - this.linkLabel_TExWebLink.Height - 4);
			this.linkLabel_TExWebLink.LinkClicked += this.linkLabel_TExWebLink_LinkClicked;
			this.label_msg.Parent = this.pictureBox_Banner;
			this.label_msg.Location = new Point(39, this.pictureBox_Banner.Height - this.label_msg.Height - 5);
			this.class297_0.Width = base.Width;
			this.class297_0.Location = new Point(0, this.pictureBox_Banner.Height);
			this.maskedTextBox_PW.GotFocus += this.maskedTextBox_PW_GotFocus;
			this.timer_0 = new System.Windows.Forms.Timer();
			this.timer_0.Interval = 1500;
			this.timer_0.Tick += this.timer_0_Tick;
			this.imgCloseBtn.MouseClick += this.imgCloseBtn_MouseClick;
			base.KeyPreview = true;
			base.KeyDown += this.LoginForm_KeyDown;
			base.Activate();
			this.splashScreen_0.Close();
			this.splashScreen_0 = null;
			TApp.smethod_2();
			TApp.StartedUp = true;
		}

		// Token: 0x06000FFA RID: 4090 RVA: 0x00006CE2 File Offset: 0x00004EE2
		private void imgCloseBtn_MouseClick(object sender, MouseEventArgs e)
		{
			if (e.Button == MouseButtons.Left)
			{
				base.Close();
			}
		}

		// Token: 0x06000FFB RID: 4091 RVA: 0x0000406F File Offset: 0x0000226F
		private void pictureBox_Banner_MouseDown(object sender, MouseEventArgs e)
		{
			base.method_0(e);
		}

		// Token: 0x06000FFC RID: 4092 RVA: 0x00006CF9 File Offset: 0x00004EF9
		private void maskedTextBox_PW_GotFocus(object sender, EventArgs e)
		{
			if (this.keys_0 == Keys.Tab)
			{
				this.maskedTextBox_PW.SelectAll();
			}
		}

		// Token: 0x06000FFD RID: 4093 RVA: 0x000654BC File Offset: 0x000636BC
		protected bool ProcessCmdKey(ref Message msg, Keys keyData)
		{
			Keys keys = (Keys)msg.WParam.ToInt32();
			this.keys_0 = keys;
			return base.ProcessCmdKey(ref msg, keyData);
		}

		// Token: 0x06000FFE RID: 4094 RVA: 0x000654EC File Offset: 0x000636EC
		private void LoginForm_KeyDown(object sender, KeyEventArgs e)
		{
			if (this.btn_Login.Enabled)
			{
				if (e.KeyCode == Keys.F5)
				{
					if (MessageBox.Show("重置账户参数设置为系统默认值吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
					{
						Base.UI.smethod_52();
					}
				}
				else if (e.KeyCode == Keys.F6)
				{
					if (MessageBox.Show("重置账户页面设置为系统默认值吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
					{
						Base.UI.smethod_87();
					}
				}
				else if (e.KeyCode == Keys.F7)
				{
					if (MessageBox.Show("清除账户所有画线数据吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
					{
						Base.UI.smethod_140();
					}
				}
				else if (e.KeyCode == Keys.F8)
				{
					if (MessageBox.Show("清除本地所有历史行情缓存数据吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
					{
						TApp.smethod_3(this.textBox_UsrName.Text);
					}
				}
				else if (e.KeyCode == Keys.F9)
				{
					if (!string.IsNullOrEmpty(this.textBox_UsrName.Text) && MessageBox.Show("清除并重置所有用户数据（包括系统参数、页面及画线数据，及所有模拟账户和交易记录等）及行情缓存数据吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
					{
						this.method_4();
					}
				}
				else if (e.Control && e.KeyCode == Keys.I)
				{
					MessageBox.Show(TApp.smethod_9(), "信息", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
				}
			}
		}

		// Token: 0x06000FFF RID: 4095 RVA: 0x00065620 File Offset: 0x00063820
		private void LoginForm_Shown(object sender, EventArgs e)
		{
			string text = "www.tradingexer.com";
			this.linkLabel_TExWebLink.Text = text;
			if (new DirectoryInfo(TApp.string_8).Exists)
			{
				LoginForm.Class231 @class = new LoginForm.Class231();
				@class.list_0 = TApp.smethod_6();
				if (@class.list_0 != null && @class.list_0.Any<KeyValuePair<string, DateTime>>())
				{
					string key = @class.list_0.SingleOrDefault(new Func<KeyValuePair<string, DateTime>, bool>(@class.method_0)).Key;
					if (!string.IsNullOrEmpty(key))
					{
						TApp.UserName = key;
					}
				}
			}
			else
			{
				DirectoryInfo directoryInfo = new DirectoryInfo(TApp.string_6);
				if (directoryInfo.Exists)
				{
					FileInfo fileInfo = new FileInfo(Path.Combine(directoryInfo.FullName, "fmui.cfg"));
					if (!fileInfo.Exists)
					{
						goto IL_2C0;
					}
					object obj = null;
					try
					{
						obj = Utility.DeserializeFile(fileInfo.FullName);
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
					if (obj == null)
					{
						goto IL_2C0;
					}
					try
					{
						FormUISettings formUISettings = (FormUISettings)obj;
						if (formUISettings != null)
						{
							TApp.UserName = formUISettings.UserID;
							Utility.CreateDir(TApp.string_8);
							string text2 = Path.Combine(TApp.string_8, TApp.UserName);
							Utility.CreateDir(text2);
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, "accts.dat"), Path.Combine(text2, "accts.dat"));
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, "acsdts.dat"), Path.Combine(text2, "acsdts.dat"));
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, "acsmbls.dat"), Path.Combine(text2, "acsmbls.dat"));
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, "baodian.dat"), Path.Combine(text2, "baodian.dat"));
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, "cfmmcacct.dat"), Path.Combine(text2, "cfmmcacct.dat"));
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, "chtpg.dat"), Path.Combine(text2, "chtpg.dat"));
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, "dwobjs.dat"), Path.Combine(text2, "dwobjs.dat"));
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, "fmui.cfg"), Path.Combine(text2, "fmui.cfg"));
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, "indusr.dat"), Path.Combine(text2, "indusr.dat"));
							Utility.ChkCopyFiles(Path.Combine(TApp.string_6, "tsmbls.dat"), Path.Combine(text2, "tsmbls.dat"));
							Utility.DirectoryCopy(Path.Combine(TApp.string_6, "Trading"), Path.Combine(text2, "Trading"), true);
							this.textBox_UsrName.Text = formUISettings.UserID;
							this.maskedTextBox_PW.Text = formUISettings.Pswd;
						}
						goto IL_2C0;
					}
					catch (Exception exception_2)
					{
						Class182.smethod_0(exception_2);
						goto IL_2C0;
					}
				}
				TApp.IsFirstRun = true;
			}
			IL_2C0:
			if (Base.UI.Form.IfSaveUsrID && string.IsNullOrEmpty(this.textBox_UsrName.Text) && !string.IsNullOrEmpty(Base.UI.Form.UserID))
			{
				this.textBox_UsrName.Text = Base.UI.Form.UserID;
			}
			if (Base.UI.Form.IfSavePswd)
			{
				this.checkBox_remPsw.Checked = true;
				if (string.IsNullOrEmpty(this.maskedTextBox_PW.Text) && !string.IsNullOrEmpty(Base.UI.Form.Pswd))
				{
					this.maskedTextBox_PW.Text = Base.UI.Form.Pswd;
				}
			}
			if (string.IsNullOrEmpty(this.textBox_UsrName.Text))
			{
				this.textBox_UsrName.Focus();
			}
			else if (string.IsNullOrEmpty(this.maskedTextBox_PW.Text))
			{
				this.maskedTextBox_PW.Focus();
			}
			else
			{
				this.btn_Login.Focus();
			}
		}

		// Token: 0x06001000 RID: 4096 RVA: 0x00065A04 File Offset: 0x00063C04
		private void linkLabel_Register_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			try
			{
				Process.Start(new ProcessStartInfo("https://www.tradingexer.com/register"));
			}
			catch
			{
			}
		}

		// Token: 0x06001001 RID: 4097 RVA: 0x00006D12 File Offset: 0x00004F12
		private void linkLabel_ResetData_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			ResetDataWnd resetDataWnd = new ResetDataWnd(this.textBox_UsrName.Text);
			resetDataWnd.ResetAllDataConfirmed += this.method_3;
			resetDataWnd.ShowDialog();
		}

		// Token: 0x06001002 RID: 4098 RVA: 0x00006D3E File Offset: 0x00004F3E
		private void method_3(object sender, EventArgs e)
		{
			this.method_4();
		}

		// Token: 0x06001003 RID: 4099 RVA: 0x00065A38 File Offset: 0x00063C38
		private void method_4()
		{
			string text = this.textBox_UsrName.Text;
			if (!string.IsNullOrEmpty(text))
			{
				text = text.Trim();
				TApp.smethod_3(text);
				Utility.DeleteDir(TApp.string_8 + text);
				this.bool_0 = true;
			}
		}

		// Token: 0x06001004 RID: 4100 RVA: 0x00006D48 File Offset: 0x00004F48
		private void timer_0_Tick(object sender, EventArgs e)
		{
			if (!this.backgroundWorker_0.IsBusy)
			{
				this.method_23();
			}
			this.timer_0.Stop();
		}

		// Token: 0x06001005 RID: 4101 RVA: 0x00006D6A File Offset: 0x00004F6A
		private void btn_Login_Click(object sender, EventArgs e)
		{
			this.textBox_UsrName.Enabled = false;
			this.maskedTextBox_PW.Enabled = false;
			this.btn_Login.Enabled = false;
			this.timer_0.Start();
			this.method_5();
		}

		// Token: 0x06001006 RID: 4102 RVA: 0x00065A80 File Offset: 0x00063C80
		private void method_5()
		{
			if (string.IsNullOrEmpty(this.textBox_UsrName.Text))
			{
				this.textBox_UsrName.Focus();
			}
			else if (string.IsNullOrEmpty(this.maskedTextBox_PW.Text))
			{
				this.maskedTextBox_PW.Focus();
			}
			else
			{
				Keys modifierKeys = Control.ModifierKeys;
				this.btn_Login.Enabled = false;
				string text = this.textBox_UsrName.Text.Trim();
				if (!string.IsNullOrEmpty(TApp.UserName) && !TApp.UserName.Equals(text, StringComparison.InvariantCultureIgnoreCase))
				{
					TApp.UserName = text;
					Base.UI.smethod_172();
				}
				this.backgroundWorker_0.RunWorkerAsync(this);
			}
		}

		// Token: 0x06001007 RID: 4103 RVA: 0x00065B24 File Offset: 0x00063D24
		private void backgroundWorker_0_DoWork(object sender, DoWorkEventArgs e)
		{
			LoginForm.Class232 @class = new LoginForm.Class232();
			string text = this.textBox_UsrName.Text.Trim();
			TApp.UserName = text;
			Class46.smethod_3(Class22.AppLoggingIn, "UsrName:" + text);
			@class.backgroundWorker_0 = (sender as BackgroundWorker);
			object argument = e.Argument;
			@class.backgroundWorker_0.ReportProgress(2, "正在连接远程主机...");
			this.itexSrv_0 = ConnMgr.smethod_0();
			try
			{
				if (this.itexSrv_0 != null)
				{
					@class.backgroundWorker_0.ReportProgress(5, "正在提交验证...");
					string text2 = this.maskedTextBox_PW.Text;
					string app = TApp.App;
					string text3 = string.Empty;
					bool flag;
					if (!(flag = (text.Equals("test", StringComparison.InvariantCultureIgnoreCase) || text.Equals("tradingexer", StringComparison.InvariantCultureIgnoreCase))))
					{
						this.method_6(@class.backgroundWorker_0, 5, "正在取HdSn...");
						text3 = Class281.smethod_1(true);
						if (!string.IsNullOrEmpty(text3))
						{
							this.method_6(@class.backgroundWorker_0, 6, "正在整理HdSn...");
							text3 = Utility.ReplaceLowOrderASCIICharacters(text3);
						}
					}
					this.method_6(@class.backgroundWorker_0, 6, "正在取OSDesc...");
					string text4 = "UNKNOWN OS";
					try
					{
						text4 = Utility.GetOSDesc(true, false);
						TApp.OS = text4;
					}
					catch (Exception exception_)
					{
						Class46.smethod_4(exception_, true, null);
					}
					this.method_6(@class.backgroundWorker_0, 7, "正在取系统信息...");
					string text5 = string.Empty;
					string text6 = string.Empty;
					try
					{
						foreach (ManagementBaseObject managementBaseObject in new ManagementObjectSearcher(new SelectQuery("Select * from Win32_ComputerSystem")).Get())
						{
							ManagementObject managementObject = (ManagementObject)managementBaseObject;
							managementObject.Get();
							string text7 = managementObject.GetPropertyValue("Manufacturer").ToString().Trim();
							if (!string.IsNullOrEmpty(text7))
							{
								text5 = text7;
								text6 = managementObject.GetPropertyValue("Model").ToString().TrimEnd(new char[0]);
							}
							bool flag2 = text5.StartsWith("To ") || text5.StartsWith("System manufacturer");
							if (text5 != string.Empty && !flag2)
							{
								break;
							}
						}
					}
					catch
					{
					}
					string text8 = Class281.smethod_4();
					string text9 = Class281.smethod_5();
					string userName = Environment.UserName;
					string text10 = this.method_27();
					Size size = TApp.smethod_5();
					int num = 0;
					int num2 = 0;
					try
					{
						Rectangle bounds = Screen.PrimaryScreen.Bounds;
						num = bounds.Width;
						num2 = bounds.Height;
					}
					catch (Exception exception_2)
					{
						Class182.smethod_0(exception_2);
					}
					object obj = string.Concat(new string[]
					{
						"<LoginValues><UsrName><![CDATA[",
						text,
						"]]></UsrName><PsWd Encrypted=\"true\"><![CDATA[",
						Utility.Encrypt(text2, TApp.string_12),
						"]]></PsWd><App><![CDATA[",
						app,
						"]]></App><cpu><![CDATA[",
						text8,
						"]]></cpu><dpi><![CDATA[",
						text10,
						"]]></dpi><dpiScale><![CDATA[",
						TApp.DpiScale.ToString("P0", new NumberFormatInfo
						{
							PercentPositivePattern = 1,
							PercentNegativePattern = 1
						}),
						"]]></dpiScale><windowWidth><![CDATA[",
						TApp.WindowWidth.ToString(),
						"]]></windowWidth><windowHeight><![CDATA[",
						TApp.WindowHeight.ToString(),
						"]]></windowHeight>",
						(num == TApp.WindowWidth) ? "" : ("<screenWidth><![CDATA[" + num.ToString() + "]]></screenWidth>"),
						(num2 == TApp.WindowHeight) ? "" : ("<screenHeight><![CDATA[" + num2.ToString() + "]]></screenHeight>"),
						(TApp.WindowWidth == size.Width) ? "" : ("<maxWindowWidth><![CDATA[" + size.Width.ToString() + "]]></maxWindowWidth>"),
						(TApp.WindowHeight == size.Height) ? "" : ("<maxWindowHeight><![CDATA[" + size.Height.ToString() + "]]></maxWindowHeight>"),
						TApp.IsHighDpiScreen ? "<highDpiScreen><![CDATA[true]]></highDpiScreen>" : "",
						"<manufacturer><![CDATA[",
						text5,
						"]]></manufacturer><model><![CDATA[",
						text6,
						"]]></model><computerType><![CDATA[",
						Enum.GetName(typeof(ComputerType), TApp.ComputerType),
						"]]></computerType><macAddress><![CDATA[",
						text9,
						"]]></macAddress><hardDiskSn><![CDATA[",
						text3,
						"]]></hardDiskSn><sysUsrName><![CDATA[",
						userName,
						"]]></sysUsrName><os>",
						text4,
						"</os></LoginValues>"
					});
					this.method_6(@class.backgroundWorker_0, 9, "正在生成待提交数据...");
					byte[] values = Utility.GenCompressedBinaryArray(obj, CompressAlgm.LZMA, null, true);
					@class.backgroundWorker_0.ReportProgress(10, "正在验证用户...");
					string text11 = this.itexSrv_0.ValidateUser(TApp.Ver, values);
					if (!string.IsNullOrEmpty(text11))
					{
						if (text11.Contains("无法登录"))
						{
							ConnMgr.smethod_3();
							@class.backgroundWorker_0.ReportProgress(-1, text11);
						}
						else
						{
							LoginRslt loginRslt = JsonConvert.DeserializeObject<LoginRslt>(Utility.DecompressString(text11, CompressAlgm.LZMA));
							if (loginRslt != null && loginRslt.Validated)
							{
								@class.backgroundWorker_0.ReportProgress(15, "正在准备通信...");
								TApp.SrvParams = new SrvParams();
								TApp.UserName = text;
								TApp.LoginCode = loginRslt.LoginCode;
								Utility.CreateDir(TApp.UserAcctFolder);
								this.method_6(@class.backgroundWorker_0, 15, "提取本地需要传递给服务器的参数...");
								XDocument xdocument = new XDocument();
								XElement xelement = new XElement("LocalInfo");
								xelement.Add(new XElement("UsrName", text));
								this.method_6(@class.backgroundWorker_0, 15, "添加本地EXCHGLST.MST的SHA...");
								string content = string.Empty;
								string text12 = Path.Combine(TApp.string_6, "EXCHGS.MST");
								FileInfo fi = new FileInfo(text12);
								this.method_6(@class.backgroundWorker_0, 15, "添加本地EXCHGLST.MST的SHA(GetBytesFromFile)...");
								byte[] bytesFromFile = Utility.GetBytesFromFile(fi);
								this.method_6(@class.backgroundWorker_0, 16, "添加本地EXCHGLST.MST的SHA(GetChecksum)...");
								if (bytesFromFile != null && bytesFromFile.Length != 0)
								{
									content = Utility.GetChecksum(bytesFromFile);
								}
								xelement.Add(new XElement("ExchgsSHA", content));
								@class.backgroundWorker_0.ReportProgress(16, "");
								this.method_6(@class.backgroundWorker_0, 16, "添加本地TSMBLS.MST的SHA");
								string content2 = string.Empty;
								string text13 = Path.Combine(TApp.string_6, "SMBLSFT.MST");
								byte[] bytesFromFile2 = Utility.GetBytesFromFile(new FileInfo(text13));
								if (bytesFromFile2 != null && bytesFromFile2.Length != 0)
								{
									content2 = Utility.GetChecksum(bytesFromFile2);
								}
								xelement.Add(new XElement("SymblsFtSHA", content2));
								string content3 = string.Empty;
								string text14 = Path.Combine(TApp.string_6, "SMBLSST.MST");
								byte[] bytesFromFile3 = Utility.GetBytesFromFile(new FileInfo(text14));
								if (bytesFromFile3 != null && bytesFromFile3.Length != 0)
								{
									content3 = Utility.GetChecksum(bytesFromFile3);
								}
								xelement.Add(new XElement("SymblsStSHA", content3));
								@class.backgroundWorker_0.ReportProgress(17, "");
								this.method_6(@class.backgroundWorker_0, 17, "添加本地EXCHGOBT.MST的SHA...");
								string content4 = string.Empty;
								string text15 = Path.Combine(TApp.string_6, "EXCHGOBT.MST");
								byte[] bytesFromFile4 = Utility.GetBytesFromFile(new FileInfo(text15));
								if (bytesFromFile4 != null && bytesFromFile4.Length != 0)
								{
									content4 = Utility.GetChecksum(bytesFromFile4);
								}
								xelement.Add(new XElement("ExchgOBTsSHA", content4));
								this.method_6(@class.backgroundWorker_0, 17, "添加本地SYMBNTDT.MST的SHA...");
								string content5 = string.Empty;
								string text16 = Path.Combine(TApp.string_6, "SYMBNTDT.MST");
								byte[] bytesFromFile5 = Utility.GetBytesFromFile(new FileInfo(text16));
								if (bytesFromFile5 != null && bytesFromFile5.Length != 0)
								{
									content5 = Utility.GetChecksum(bytesFromFile5);
								}
								xelement.Add(new XElement("SymbNtTrDatesSHA", content5));
								@class.backgroundWorker_0.ReportProgress(18, "");
								this.method_6(@class.backgroundWorker_0, 18, "添加本地FTCODES.MST(期货)的SHA...");
								string content6 = string.Empty;
								string text17 = Path.Combine(TApp.string_6, "FTCODES.MST");
								byte[] bytesFromFile6 = Utility.GetBytesFromFile(new FileInfo(text17));
								if (bytesFromFile6 != null && bytesFromFile6.Length != 0)
								{
									content6 = Utility.GetChecksum(bytesFromFile6);
								}
								xelement.Add(new XElement("FtCodesSHA", content6));
								@class.backgroundWorker_0.ReportProgress(19, "");
								this.method_6(@class.backgroundWorker_0, 19, "添加本地STCODES.MST(股票)的SHA...");
								string content7 = string.Empty;
								string text18 = Path.Combine(TApp.string_6, "STCODES.MST");
								byte[] bytesFromFile7 = Utility.GetBytesFromFile(new FileInfo(text18));
								if (bytesFromFile7 != null && bytesFromFile7.Length != 0)
								{
									content7 = Utility.GetChecksum(bytesFromFile7);
								}
								xelement.Add(new XElement("StCodesSHA", content7));
								@class.backgroundWorker_0.ReportProgress(20, "");
								this.method_6(@class.backgroundWorker_0, 20, "添加本地FTPYTR.HFI的SHA...");
								string content8 = string.Empty;
								string text19 = Path.Combine(TApp.string_6, "FTPYTR.HFI");
								byte[] bytesFromFile8 = Utility.GetBytesFromFile(new FileInfo(text19));
								if (bytesFromFile8 != null && bytesFromFile8.Length != 0)
								{
									content8 = Utility.GetChecksum(bytesFromFile8);
								}
								xelement.Add(new XElement("FtTrPYrHfiSHA", content8));
								@class.backgroundWorker_0.ReportProgress(21, "");
								this.method_6(@class.backgroundWorker_0, 21, "添加本地FTCYTR.HFI的SHA...");
								string content9 = string.Empty;
								string text20 = Path.Combine(TApp.string_6, "FTCYTR.HFI");
								byte[] bytesFromFile9 = Utility.GetBytesFromFile(new FileInfo(text20));
								if (bytesFromFile9 != null && bytesFromFile9.Length != 0)
								{
									content9 = Utility.GetChecksum(bytesFromFile9);
								}
								xelement.Add(new XElement("FtTrCYrHfiSHA", content9));
								this.method_6(@class.backgroundWorker_0, 21, "添加本地FTCMTR.HFI的SHA...");
								string content10 = string.Empty;
								string text21 = Path.Combine(TApp.string_6, "FTCMTR.HFI");
								byte[] bytesFromFile10 = Utility.GetBytesFromFile(new FileInfo(text21));
								if (bytesFromFile10 != null && bytesFromFile10.Length != 0)
								{
									content10 = Utility.GetChecksum(bytesFromFile10);
								}
								xelement.Add(new XElement("FtTrCMthHfiSHA", content10));
								this.method_6(@class.backgroundWorker_0, 21, "添加本地STPYTR.HFI的SHA...");
								string content11 = string.Empty;
								string text22 = Path.Combine(TApp.string_6, "STPYTR.HFI");
								byte[] bytesFromFile11 = Utility.GetBytesFromFile(new FileInfo(text22));
								if (bytesFromFile11 != null && bytesFromFile11.Length != 0)
								{
									content11 = Utility.GetChecksum(bytesFromFile11);
								}
								xelement.Add(new XElement("StTrPYrHfiSHA", content11));
								@class.backgroundWorker_0.ReportProgress(22, "");
								this.method_6(@class.backgroundWorker_0, 22, "添加本地STCYTR.HFI的SHA...");
								string content12 = string.Empty;
								string text23 = Path.Combine(TApp.string_6, "STCYTR.HFI");
								byte[] bytesFromFile12 = Utility.GetBytesFromFile(new FileInfo(text23));
								if (bytesFromFile12 != null && bytesFromFile12.Length != 0)
								{
									content12 = Utility.GetChecksum(bytesFromFile12);
								}
								xelement.Add(new XElement("StTrCYrHfiSHA", content12));
								@class.backgroundWorker_0.ReportProgress(23, "");
								this.method_6(@class.backgroundWorker_0, 23, "添加本地STCMTR.HFI的SHA...");
								string content13 = string.Empty;
								string text24 = Path.Combine(TApp.string_6, "STCMTR.HFI");
								byte[] bytesFromFile13 = Utility.GetBytesFromFile(new FileInfo(text24));
								if (bytesFromFile13 != null && bytesFromFile13.Length != 0)
								{
									content13 = Utility.GetChecksum(bytesFromFile13);
								}
								xelement.Add(new XElement("StTrCMthHfiSHA", content13));
								this.method_6(@class.backgroundWorker_0, 23, "添加本地FTPY.HFI的SHA...");
								string content14 = string.Empty;
								string text25 = Path.Combine(TApp.string_6, "FTPY.HFI");
								byte[] bytesFromFile14 = Utility.GetBytesFromFile(new FileInfo(text25));
								if (bytesFromFile14 != null && bytesFromFile14.Length != 0)
								{
									content14 = Utility.GetChecksum(bytesFromFile14);
								}
								xelement.Add(new XElement("FtPYrHfiSHA", content14));
								@class.backgroundWorker_0.ReportProgress(24, "");
								this.method_6(@class.backgroundWorker_0, 24, "添加本地FTCY.HFI的SHA...");
								string content15 = string.Empty;
								string text26 = Path.Combine(TApp.string_6, "FTCY.HFI");
								byte[] bytesFromFile15 = Utility.GetBytesFromFile(new FileInfo(text26));
								if (bytesFromFile15 != null && bytesFromFile15.Length != 0)
								{
									content15 = Utility.GetChecksum(bytesFromFile15);
								}
								xelement.Add(new XElement("FtCYrHfiSHA", content15));
								this.method_6(@class.backgroundWorker_0, 24, "添加本地FTCM.HFI的SHA...");
								string content16 = string.Empty;
								string text27 = Path.Combine(TApp.string_6, "FTCM.HFI");
								byte[] bytesFromFile16 = Utility.GetBytesFromFile(new FileInfo(text27));
								if (bytesFromFile16 != null && bytesFromFile16.Length != 0)
								{
									content16 = Utility.GetChecksum(bytesFromFile16);
								}
								xelement.Add(new XElement("FtCMthHfiSHA", content16));
								@class.backgroundWorker_0.ReportProgress(25, "");
								this.method_6(@class.backgroundWorker_0, 25, "添加本地STPY.HFI的SHA...");
								string content17 = string.Empty;
								string text28 = Path.Combine(TApp.string_6, "STPY.HFI");
								byte[] bytesFromFile17 = Utility.GetBytesFromFile(new FileInfo(text28));
								if (bytesFromFile17 != null && bytesFromFile17.Length != 0)
								{
									content17 = Utility.GetChecksum(bytesFromFile17);
								}
								xelement.Add(new XElement("StPYrHfiSHA", content17));
								@class.backgroundWorker_0.ReportProgress(26, "");
								this.method_6(@class.backgroundWorker_0, 26, "添加本地STCY.HFI的SHA...");
								string content18 = string.Empty;
								string text29 = Path.Combine(TApp.string_6, "STCY.HFI");
								byte[] bytesFromFile18 = Utility.GetBytesFromFile(new FileInfo(text29));
								if (bytesFromFile18 != null && bytesFromFile18.Length != 0)
								{
									content18 = Utility.GetChecksum(bytesFromFile18);
								}
								xelement.Add(new XElement("StCYrHfiSHA", content18));
								this.method_6(@class.backgroundWorker_0, 26, "添加本地STCM.HFI的SHA...");
								string content19 = string.Empty;
								string text30 = Path.Combine(TApp.string_6, "STCM.HFI");
								byte[] bytesFromFile19 = Utility.GetBytesFromFile(new FileInfo(text30));
								if (bytesFromFile19 != null && bytesFromFile19.Length != 0)
								{
									content19 = Utility.GetChecksum(bytesFromFile19);
								}
								xelement.Add(new XElement("StCMthHfiSHA", content19));
								@class.backgroundWorker_0.ReportProgress(27, "");
								this.method_6(@class.backgroundWorker_0, 27, "添加本地STSPLT.MST(StSplit)的SHA...");
								string content20 = string.Empty;
								string text31 = Path.Combine(TApp.string_6, "STSPLT.MST");
								byte[] bytesFromFile20 = Utility.GetBytesFromFile(new FileInfo(text31));
								if (bytesFromFile20 != null && bytesFromFile20.Length != 0)
								{
									content20 = Utility.GetChecksum(bytesFromFile20);
								}
								xelement.Add(new XElement("StSpltSHA", content20));
								@class.backgroundWorker_0.ReportProgress(28, "");
								this.method_6(@class.backgroundWorker_0, 28, "添加本地STSPLTTR.MST的SHA...");
								string content21 = string.Empty;
								string string_ = Path.Combine(TApp.string_6, "STSPLTTR.MST");
								byte[] bytesFromFile21 = Utility.GetBytesFromFile(new FileInfo(text31));
								if (bytesFromFile21 != null && bytesFromFile21.Length != 0)
								{
									content21 = Utility.GetChecksum(bytesFromFile21);
								}
								xelement.Add(new XElement("StSpltTrSHA", content21));
								this.method_6(@class.backgroundWorker_0, 28, "添加本地INDSYS.DAT的SHA...");
								string content22 = string.Empty;
								string text32 = Path.Combine(TApp.string_6, "INDSYS.DAT");
								byte[] bytesFromFile22 = Utility.GetBytesFromFile(new FileInfo(text32));
								if (bytesFromFile22 != null && bytesFromFile22.Length != 0)
								{
									content22 = Utility.GetChecksum(bytesFromFile22);
								}
								xelement.Add(new XElement("IndSysSHA", content22));
								this.method_6(@class.backgroundWorker_0, 28, "检查添加本地备份同步文件的信息...");
								bool flag3 = false;
								if (this.bool_0)
								{
									XElement content23 = new XElement("ClearBkSync");
									xelement.Add(content23);
								}
								else if (flag3 = (!flag && !Base.UI.Form.BackupSyncDisabled && !Base.UI.Form.BackupSyncNotOnStartup))
								{
									XElement xelement2 = new XElement("BkupSync");
									xelement2 = BkupSyncMgr.smethod_12(xelement2);
									xelement.Add(xelement2);
								}
								xdocument.Add(xelement);
								@class.backgroundWorker_0.ReportProgress(29, "");
								this.method_6(@class.backgroundWorker_0, 29, "转换本地信息为XML并压缩...");
								byte[] info = Utility.GenCompressedBinaryArray(Utility.ConvertXMLDocToString(xdocument), CompressAlgm.LZMA, null, true);
								@class.backgroundWorker_0.ReportProgress(30, "正在从服务器更新数据...");
								List<SrvParam> list = Utility.DeserializeBytes(this.itexSrv_0.GetSrvParams(TApp.Ver, info), SerializationType.BinaryFormatter) as List<SrvParam>;
								@class.backgroundWorker_0.ReportProgress(35, "");
								this.method_6(@class.backgroundWorker_0, 35, "从服务器取得与登录用户有关的参数...");
								TExPackage? texPackage = new TExPackage?(TExPackage.TRL);
								object obj2 = this.method_13(list, "TExPackage", "3.0");
								if (obj2 != null)
								{
									texPackage = new TExPackage?((TExPackage)Convert.ToInt32(obj2));
								}
								TApp.SrvParams.TExPkg = texPackage;
								XDocument xdocument2 = this.method_12(list, "AppUpdInfo", "3.0");
								AppUpdInfo appUpdInfo = new AppUpdInfo();
								if (xdocument2 != null)
								{
									XElement xelement3 = xdocument2.Element("AppUpdInfo");
									appUpdInfo.Feed = xelement3.Element("Feed").Value;
									appUpdInfo.DateTime = Convert.ToDateTime(xelement3.Element("DateTime").Value);
									appUpdInfo.Ver = xelement3.Element("Ver").Value;
									appUpdInfo.Notes = xelement3.Element("Notes").Value;
								}
								TApp.SrvParams.AppUpdInfo = appUpdInfo;
								XDocument xdocument3 = this.method_12(list, "LogonNoticeInfo", "3.5");
								LogonNoticeInfo logonNoticeInfo = new LogonNoticeInfo();
								if (xdocument3 != null)
								{
									XElement xelement4 = xdocument3.Element("LogonNoticeInfo");
									logonNoticeInfo.HTMLUrl = xelement4.Element("HTMLUrl").Value.Replace(TApp.string_13, TApp.FULLHOST);
									logonNoticeInfo.DateTime = Convert.ToDateTime(xelement4.Element("DateTime").Value);
									logonNoticeInfo.Notes = xelement4.Element("Notes").Value;
								}
								TApp.SrvParams.LogonNoticeInfo = logonNoticeInfo;
								string infoHTML = this.method_13(list, "WelComeMsg", "3.0") as string;
								bool isNewMachine = Convert.ToBoolean(Convert.ToInt32(this.method_13(list, "IsNewMachine", "3.0")));
								bool isFirstLogin = Convert.ToBoolean(Convert.ToInt32(this.method_13(list, "IsFirstLogin", "3.0")));
								TApp.SrvParams.InfoHTML = infoHTML;
								TApp.SrvParams.IsNewMachine = isNewMachine;
								TApp.SrvParams.IsFirstLogin = isFirstLogin;
								object obj3 = this.method_13(list, "MinHDExpPeriodUnits", "3.5");
								if (obj3 != null)
								{
									TApp.SrvParams.MinHDExpPeriodUnits = Convert.ToInt32(obj3);
								}
								else
								{
									TApp.SrvParams.MinHDExpPeriodUnits = 5;
								}
								bool isTrialUser = TApp.IsTrialUser;
								@class.backgroundWorker_0.ReportProgress(36, "");
								this.method_6(@class.backgroundWorker_0, 36, "从服务器更新并提取ExchgHsList主数据...");
								XDocument xdocument4 = this.method_10(list, "ExchgHouses", "3.0", text12, bytesFromFile);
								List<ExchgHouse> list2 = new List<ExchgHouse>();
								if (xdocument4 != null)
								{
									foreach (XElement xelement5 in xdocument4.Element("ExchgHouses").Elements("Exchg"))
									{
										list2.Add(new ExchgHouse
										{
											ID = Convert.ToInt32(xelement5.Attribute("ID").Value),
											Name_EN = xelement5.Attribute("Name_EN").Value,
											Name_CN = xelement5.Attribute("Name_CN").Value,
											AbbrName_EN = xelement5.Attribute("AbbrName_EN").Value,
											AbbrName_CN = xelement5.Attribute("AbbrName_CN").Value,
											TableNamePrefix = xelement5.Attribute("TableNamePrefix").Value,
											Country = xelement5.Attribute("Country").Value
										});
									}
								}
								TApp.SrvParams.ExchgHsList = list2;
								this.method_6(@class.backgroundWorker_0, 36, "从服务器更新并提取SymblsList主数据...");
								XDocument xdocument_ = this.method_10(list, "Symbols_Future", "3.0", text13, bytesFromFile2);
								XDocument xdocument_2 = this.method_10(list, "Symbols_Stock", "4.0", text14, bytesFromFile3);
								List<TradingSymbol> list3 = new List<TradingSymbol>();
								List<TradingSymbol> list4 = this.method_18(xdocument_);
								if (list4 != null && list4.Any<TradingSymbol>())
								{
									list3.AddRange(list4);
								}
								List<TradingSymbol> list5 = this.method_18(xdocument_2);
								if (list5 != null && list5.Any<TradingSymbol>())
								{
									list3.AddRange(list5);
								}
								TApp.SrvParams.MstSymblList = list3;
								@class.backgroundWorker_0.ReportProgress(37, "");
								this.method_6(@class.backgroundWorker_0, 37, "从服务器更新并提取ExchgOBTList主数据...");
								XDocument xdocument5 = this.method_10(list, "ExchgOBTs", "3.0", text15, bytesFromFile4);
								List<ExchgOBT> list6 = new List<ExchgOBT>();
								if (xdocument5 != null)
								{
									foreach (XElement xelement6 in xdocument5.Element("ExchgOBTs").Elements("ExchgOBT"))
									{
										ExchgOBT exchgOBT = new ExchgOBT();
										exchgOBT.ID = Convert.ToInt32(xelement6.Attribute("ID").Value);
										exchgOBT.ExchgID = Convert.ToInt32(xelement6.Attribute("ExchgID").Value);
										string value = xelement6.Attribute("FromDate").Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.FromDate = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute("ToDate").Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.ToDate = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute("DayOpenTime").Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.DayOpenTime = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute("DayCloseTime").Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.DayCloseTime = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute("AMRestStartTime").Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.AMRestStartTime = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute("AMRestEndTime").Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.AMRestEndTime = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute("NoonBreakStartTime").Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.NoonBreakStartTime = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute("NoonBreakEndTime").Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.NoonBreakEndTime = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute("PMRestStartTime").Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.PMRestStartTime = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute("PMRestEndTime").Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.PMRestEndTime = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute("NightOpenTime").Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.NightOpenTime = new DateTime?(Convert.ToDateTime(value));
										}
										value = xelement6.Attribute("NightCloseTime").Value;
										if (!string.IsNullOrEmpty(value))
										{
											exchgOBT.NightCloseTime = new DateTime?(Convert.ToDateTime(value));
										}
										list6.Add(exchgOBT);
									}
								}
								TApp.SrvParams.ExchgOBTList = list6;
								this.method_6(@class.backgroundWorker_0, 37, "从服务器更新并提取SymbNtTrDates主数据...");
								XDocument xdocument6 = this.method_10(list, "SymbNtTrDates", "3.0", text16, bytesFromFile5);
								List<SymbNtTrDate> list7 = new List<SymbNtTrDate>();
								if (xdocument6 != null)
								{
									foreach (XElement xelement7 in xdocument6.Element("SymbNtTrDates").Elements("SymbNtTrDate"))
									{
										SymbNtTrDate symbNtTrDate = new SymbNtTrDate();
										symbNtTrDate.SymbID = Convert.ToInt32(xelement7.Attribute("SymbID").Value);
										symbNtTrDate.ExchgOBTID = Convert.ToInt32(xelement7.Attribute("ExchgOBTID").Value);
										string value2 = xelement7.Attribute("FromDate").Value;
										if (!string.IsNullOrEmpty(value2))
										{
											symbNtTrDate.FromDate = new DateTime?(Convert.ToDateTime(value2));
										}
										value2 = xelement7.Attribute("ToDate").Value;
										if (!string.IsNullOrEmpty(value2))
										{
											symbNtTrDate.ToDate = new DateTime?(Convert.ToDateTime(value2));
										}
										list7.Add(symbNtTrDate);
									}
								}
								TApp.SrvParams.SymbNtTrDateList = list7;
								@class.backgroundWorker_0.ReportProgress(38, "");
								string string_2 = null;
								string string_3 = null;
								string string_4 = null;
								string string_5 = null;
								string string_6 = null;
								string string_7 = null;
								@class.backgroundWorker_0.ReportProgress(38, "正在提取本地数据列表...");
								this.method_6(@class.backgroundWorker_0, 38, "正在提取本地PY HFI数据...");
								if (isTrialUser)
								{
									string_2 = this.method_11(list, "FtTrPYrHfis", "5.0", text19, bytesFromFile8, true);
									@class.backgroundWorker_0.ReportProgress(41, "");
									string_5 = this.method_11(list, "StTrPYrHfis", "5.0", text22, bytesFromFile11, true);
								}
								else
								{
									if (TApp.IsFtIncluded)
									{
										string_2 = this.method_11(list, "FtPYrHfis", "5.0", text25, bytesFromFile14, true);
									}
									@class.backgroundWorker_0.ReportProgress(41, "");
									if (TApp.IsStIncluded)
									{
										string_5 = this.method_11(list, "StPYrHfis", "5.0", text28, bytesFromFile17, true);
									}
								}
								@class.backgroundWorker_0.ReportProgress(46, "");
								this.method_6(@class.backgroundWorker_0, 46, "正在提取本地CY HFI数据...");
								if (isTrialUser)
								{
									string_3 = this.method_11(list, "FtTrCYrHfis", "5.0", text20, bytesFromFile9, true);
									@class.backgroundWorker_0.ReportProgress(47, "");
									string_6 = this.method_11(list, "StTrCYrHfis", "5.0", text23, bytesFromFile12, true);
								}
								else
								{
									if (TApp.IsFtIncluded)
									{
										string_3 = this.method_11(list, "FtCYrHfis", "5.0", text26, bytesFromFile15, true);
									}
									@class.backgroundWorker_0.ReportProgress(47, "");
									if (TApp.IsStIncluded)
									{
										string_6 = this.method_11(list, "StCYrHfis", "5.0", text29, bytesFromFile18, true);
									}
								}
								@class.backgroundWorker_0.ReportProgress(49, "正在从服务器更新数据列表...");
								this.method_6(@class.backgroundWorker_0, 49, "正在提取本地CM HFI数据...");
								if (isTrialUser)
								{
									string_4 = this.method_11(list, "FtTrCMthHfis", "5.0", text21, bytesFromFile10, true);
									string_7 = this.method_11(list, "StTrCMthHfis", "5.0", text24, bytesFromFile13, true);
								}
								else
								{
									if (TApp.IsFtIncluded)
									{
										string_4 = this.method_11(list, "FtCMthHfis", "5.0", text27, bytesFromFile16, true);
									}
									if (TApp.IsStIncluded)
									{
										string_7 = this.method_11(list, "StCMthHfis", "5.0", text30, bytesFromFile19, true);
									}
								}
								List<HDFileInfo> list8 = new List<HDFileInfo>();
								@class.backgroundWorker_0.ReportProgress(50, "");
								this.method_6(@class.backgroundWorker_0, 50, "从服务器更新并提取PY HFI数据...");
								HDFileMgr.smethod_14(string_2, list8);
								HDFileMgr.smethod_14(string_5, list8);
								@class.backgroundWorker_0.ReportProgress(54, "");
								this.method_6(@class.backgroundWorker_0, 54, "从服务器更新并提取CY HFI数据...");
								HDFileMgr.smethod_14(string_3, list8);
								HDFileMgr.smethod_14(string_6, list8);
								@class.backgroundWorker_0.ReportProgress(56, "");
								this.method_6(@class.backgroundWorker_0, 56, "从服务器更新并提取CM HFI数据...");
								HDFileMgr.smethod_14(string_4, list8);
								HDFileMgr.smethod_14(string_7, list8);
								TApp.SrvParams.HFileInfoList = list8;
								object obj4 = this.method_13(list, "GetHDFileInfoFromSrvApi", "5.0");
								if (obj4 != null)
								{
									TApp.SrvParams.GetHDFileInfoFromSrvApi = Convert.ToBoolean(obj4);
								}
								@class.backgroundWorker_0.ReportProgress(57, "");
								this.method_6(@class.backgroundWorker_0, 57, "从服务器更新并提取UsrStkMetaLst...");
								List<UsrStkMeta> list9;
								if (isTrialUser)
								{
									int[] int_ = this.method_13(list, "TrlUsrMIStkSymbIDs", "3.5") as int[];
									DateTime? nullable_ = null;
									object obj5 = this.method_13(list, "TrlUsrMetaDataBeginDT", "3.5");
									if (obj5 != null)
									{
										nullable_ = new DateTime?(Convert.ToDateTime(obj5));
									}
									DateTime dateTime = Convert.ToDateTime(this.method_13(list, "HDFileInfoTblMaxEndDT", "5.0"));
									if (nullable_ != null)
									{
										DateTime now = DateTime.Now;
										if (dateTime.Year != now.Year || dateTime.Month != now.Month)
										{
											int num3 = now.Year * 12 + now.Month - (dateTime.Year * 12 + dateTime.Month);
											if (num3 > 0)
											{
												nullable_ = new DateTime?(nullable_.Value.AddMonths(-num3));
											}
										}
									}
									DateTime? nullable_2 = null;
									object obj6 = this.method_13(list, "TrlUsr1mDataBeginDT", "3.5");
									if (obj6 != null)
									{
										nullable_2 = new DateTime?(Convert.ToDateTime(obj6));
									}
									list9 = this.method_16(int_, list8, nullable_, nullable_2);
									int[] int_2 = this.method_13(list, "TrlUsrStStkSymbIDs", "4.0") as int[];
									List<UsrStkMeta> collection = this.method_16(int_2, list8, nullable_, nullable_2);
									list9.AddRange(collection);
								}
								else
								{
									XDocument xdocument_3 = this.method_12(list, "UsrStkMetas", "3.5");
									list9 = this.method_15(xdocument_3);
								}
								TApp.SrvParams.UsrStkMetaList = list9;
								@class.backgroundWorker_0.ReportProgress(59, "正在从服务器更新品种参数...");
								this.method_6(@class.backgroundWorker_0, 59, "从服务器更新并提取FtCodes/StCodes主数据...");
								if (TApp.IsFtIncluded)
								{
									string string_8 = this.method_11(list, "FtCodes", "5.0", text17, bytesFromFile6, true);
									Dictionary<int, StkSymbol> dictionary_ = this.method_8(string_8);
									this.method_9(dictionary_);
									this.method_7(dictionary_, texPackage, list9, list3, true);
								}
								if (TApp.IsStIncluded)
								{
									string string_9 = this.method_11(list, "StCodes", "5.0", text18, bytesFromFile7, true);
									Dictionary<int, StkSymbol> dictionary_2 = this.method_8(string_9);
									this.method_9(dictionary_2);
									this.method_7(dictionary_2, texPackage, list9, list3, false);
								}
								TApp.SrvParams.StaticSrvAddr = (this.method_13(list, "StaticSrvAddr", "3.5") as string);
								TApp.SrvParams.HdDatFileBaseUrlRawString = (this.method_13(list, "HdDatFileBaseUrl", "3.5") as string);
								this.method_13(list, "HdDatFileBaseUrl", "3.5");
								TApp.SrvParams.method_1();
								@class.backgroundWorker_0.ReportProgress(61, "");
								this.method_6(@class.backgroundWorker_0, 61, "从服务器更新并提取StSplitLst主数据...");
								if (TApp.IsStIncluded)
								{
									string string_10 = null;
									if (isTrialUser)
									{
										string_10 = this.method_11(list, "StSplitTr", "5.0", string_, bytesFromFile21, true);
									}
									else
									{
										TExPackage? texPackage2 = texPackage;
										if (!(texPackage2.GetValueOrDefault() == TExPackage.SVIP_St & texPackage2 != null))
										{
											texPackage2 = texPackage;
											if (!(texPackage2.GetValueOrDefault() == TExPackage.SVIP_StFt & texPackage2 != null))
											{
												goto IL_1FD0;
											}
										}
										string_10 = this.method_11(list, "StSplit", "5.0", text31, bytesFromFile20, true);
									}
									IL_1FD0:
									List<StSplit> stSplitList = this.method_19(string_10);
									TApp.SrvParams.StSplitList = stSplitList;
								}
								this.method_6(@class.backgroundWorker_0, 63, "从服务器更新并提取IndSys数据...");
								this.method_10(list, "IndSys", "4.5", text32, bytesFromFile22);
								if (!isTrialUser && flag3)
								{
									@class.backgroundWorker_0.ReportProgress(63, "正在检查云备份同步...");
									IEnumerable<SrvParam> source = list.Where(new Func<SrvParam, bool>(LoginForm.<>c.<>9.method_1));
									if (source.Any<SrvParam>())
									{
										TApp.ReqSyncFileSpms = BkupSyncMgr.smethod_15(source.First<SrvParam>().Value as List<SrvParam>);
										if (TApp.ReqSyncFileSpms != null && TApp.ReqSyncFileSpms.Any<SrvParam>())
										{
											@class.backgroundWorker_0.ReportProgress(65, "正在进行云备份同步...");
											BkupSyncMgr.smethod_20(TApp.ReqSyncFileSpms, this.itexSrv_0, text, TApp.Ver);
										}
									}
								}
								@class.backgroundWorker_0.ReportProgress(67, "正在准备数据列表...");
								string text33 = string.Empty;
								if (!TApp.smethod_1(texPackage))
								{
									object obj7 = this.method_13(list, "HDFileUpdFeed", "3.5");
									if (obj7 != null)
									{
										text33 = (obj7 as string);
									}
								}
								if (string.IsNullOrEmpty(text33))
								{
									StkSymbol startUpSymbl = SymbMgr.StartUpSymbl;
									List<UsrStkMeta> list10 = new List<UsrStkMeta>();
									UsrStkMeta item = SymbMgr.smethod_17(TApp.SrvParams.UsrStkSymbols, list9, TApp.SrvParams.StkBegEndDtLst, texPackage, startUpSymbl);
									list10.Add(item);
									if (TApp.SrvParams.GetHDFileInfoFromSrvApi)
									{
										text33 = HDFileMgr.smethod_4(list10, TApp.SrvParams.HdDatFileBaseUrl);
									}
									else
									{
										text33 = HDFileMgr.smethod_0(list10, list8, TApp.SrvParams.HdDatFileBaseUrl);
									}
								}
								if (!string.IsNullOrEmpty(text33))
								{
									LoginForm.Class233 class2 = new LoginForm.Class233();
									class2.class232_0 = @class;
									class2.class232_0.backgroundWorker_0.ReportProgress(68, "正在加载行情数据...");
									UpdateManager instance = UpdateManager.Instance;
									Utility.CreateDir(TApp.string_7);
									IUpdateSource iupdateSource_ = new MemorySource(text33.Replace(".\\Data\\", TApp.string_7));
									class2.int_0 = 0;
									try
									{
										class2.int_0 = Class183.smethod_0(iupdateSource_);
									}
									catch
									{
										throw;
									}
									if (class2.int_0 > 0)
									{
										class2.class232_0.backgroundWorker_0.ReportProgress(69, "正在更新行情数据...");
										try
										{
											instance.ReportProgress += class2.method_0;
											instance.PrepareUpdates();
											List<string> list11 = new List<string>();
											new List<string>();
											foreach (IUpdateTask updateTask in instance.Tasks)
											{
												string fileName = Path.GetFileName(((FileUpdateTask)updateTask).LocalPath);
												string item2 = fileName.Substring(0, fileName.Length - 6);
												if (!list11.Contains(item2))
												{
													list11.Add(item2);
												}
											}
											instance.ApplyUpdates(false);
											instance.CleanUp();
										}
										catch (Exception)
										{
											instance.CleanUp();
											throw;
										}
									}
									class2.class232_0.backgroundWorker_0.ReportProgress(90, "");
									this.method_6(class2.class232_0.backgroundWorker_0, 90, "清理更新临时文件目录...");
									try
									{
										if (Directory.Exists(instance.Config.TempFolder))
										{
											Utility.DeleteDir(instance.Config.TempFolder);
										}
									}
									catch
									{
									}
									try
									{
										if (Directory.Exists(instance.Config.BackupFolder))
										{
											Utility.DeleteDir(instance.Config.BackupFolder);
										}
									}
									catch
									{
									}
									instance.CleanUp();
								}
								else
								{
									@class.backgroundWorker_0.ReportProgress(91, "正在更新行情数据...");
								}
								SymbMgr.smethod_0();
								Base.Data.GettingLocalHisDataStarted += this.method_21;
								@class.backgroundWorker_0.ReportProgress(100, "登录完成。");
								TApp.IsLoggedIn = true;
								TApp.SrvParams.EnableRefreshHdDatUrlTimer = true;
								Thread.Sleep(100);
								ConnMgr.smethod_3();
								e.Result = new EventArgs9("");
								base.DialogResult = DialogResult.OK;
							}
							else
							{
								ConnMgr.smethod_3();
								@class.backgroundWorker_0.ReportProgress(-1, (loginRslt != null) ? loginRslt.Msg : "无法登录，请联系客服或稍后再试。");
							}
						}
					}
					else
					{
						ConnMgr.smethod_3();
						@class.backgroundWorker_0.ReportProgress(-1, "无法登录，请联系客服或稍后再试。");
					}
				}
			}
			catch (Exception ex)
			{
				Class46.smethod_4(ex, true, "Login Error");
				if (ex is WebException)
				{
					if (!ConnMgr.smethod_5())
					{
						@class.backgroundWorker_0.ReportProgress(-1, "无法连接远程主机，请检查是否已连接到互联网。");
					}
					else
					{
						@class.backgroundWorker_0.ReportProgress(-1, "无法连接远程主机，请稍后再试。");
					}
				}
				else if (ex is NAppUpdateException)
				{
					MessageBox.Show(string.Concat(new string[]
					{
						ex.InnerException.Message,
						Environment.NewLine,
						ex.InnerException.Source,
						Environment.NewLine,
						ex.InnerException.StackTrace
					}));
					if (MessageBox.Show("更新数据文件失败，继续启动程序吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
					{
						@class.backgroundWorker_0.ReportProgress(-1, "更新数据文件失败，请稍后再试。");
						return;
					}
				}
				else if (!(ex is SqlException) && (ex.InnerException == null || ex.InnerException.InnerException == null || !(ex.InnerException.InnerException.Source == ".Net SqlClient Data Provider")))
				{
					@class.backgroundWorker_0.ReportProgress(-1, "错误：" + ex.Message);
				}
				else
				{
					@class.backgroundWorker_0.ReportProgress(-1, "远程数据库访问遇到问题，请稍后再试。");
				}
				ConnMgr.smethod_3();
			}
		}

		// Token: 0x06001008 RID: 4104 RVA: 0x000041AE File Offset: 0x000023AE
		private void method_6(BackgroundWorker backgroundWorker_1, int int_0, string string_0)
		{
		}

		// Token: 0x06001009 RID: 4105 RVA: 0x000681B0 File Offset: 0x000663B0
		private void method_7(Dictionary<int, StkSymbol> dictionary_0, TExPackage? nullable_0, List<UsrStkMeta> list_0, List<TradingSymbol> list_1, bool bool_1)
		{
			Dictionary<int, StkSymbol> dictionary = new Dictionary<int, StkSymbol>();
			if (dictionary_0 != null)
			{
				using (Dictionary<int, StkSymbol>.Enumerator enumerator = dictionary_0.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						LoginForm.Class234 @class = new LoginForm.Class234();
						@class.keyValuePair_0 = enumerator.Current;
						bool flag = false;
						if (nullable_0 != null && nullable_0.Value > TExPackage.TRL && list_0 != null && list_0.Count == 1)
						{
							UsrStkMeta usrStkMeta = list_0.First<UsrStkMeta>();
							int exchangeID = @class.keyValuePair_0.Value.ExchangeID;
							if (usrStkMeta.StkId == -100)
							{
								if (exchangeID < 5)
								{
									flag = true;
								}
							}
							else if (usrStkMeta.StkId == -70)
							{
								if (exchangeID == 1)
								{
									flag = true;
								}
							}
							else if (usrStkMeta.StkId == -80)
							{
								if (exchangeID > 1 && exchangeID < 5)
								{
									flag = true;
								}
							}
							else if (usrStkMeta.StkId == -200)
							{
								if (exchangeID == 5 || exchangeID == 6)
								{
									flag = true;
								}
							}
							else if (usrStkMeta.StkId == -300)
							{
								flag = true;
							}
						}
						else
						{
							IEnumerable<UsrStkMeta> source = list_0.Where(new Func<UsrStkMeta, bool>(@class.method_0));
							if (source.Any<UsrStkMeta>())
							{
								source.First<UsrStkMeta>().StkCode = @class.keyValuePair_0.Value.Code;
								flag = true;
							}
							else if (bool_1)
							{
								LoginForm.Class235 class2 = new LoginForm.Class235();
								class2.string_0 = @class.keyValuePair_0.Value.Code;
								class2.string_0 = class2.string_0.Substring(0, class2.string_0.Length - 2) + "00";
								if (list_0.Exists(new Predicate<UsrStkMeta>(class2.method_0)))
								{
									flag = true;
								}
							}
						}
						if (flag)
						{
							DateTime? dateTime = null;
							StkBegEndDate stkBegEndDate = TApp.SrvParams.StkBegEndDtLst.SingleOrDefault(new Func<StkBegEndDate, bool>(@class.method_1));
							if (stkBegEndDate != null)
							{
								dateTime = new DateTime?(stkBegEndDate.MinBegDate_1m);
							}
							if (dateTime != null && (TApp.smethod_1(nullable_0) || !(list_0.First<UsrStkMeta>().EndDate < dateTime.Value)))
							{
								StkSymbol stkSymbol = new StkSymbol();
								stkSymbol.ID = @class.keyValuePair_0.Key;
								stkSymbol.Code = @class.keyValuePair_0.Value.Code;
								stkSymbol.ExchangeID = @class.keyValuePair_0.Value.ExchangeID;
								stkSymbol.CNName = @class.keyValuePair_0.Value.CNName;
								stkSymbol.IdxClassLv1 = @class.keyValuePair_0.Value.IdxClassLv1;
								stkSymbol.IdxClassLv2 = @class.keyValuePair_0.Value.IdxClassLv2;
								stkSymbol.IdxClassLv3 = @class.keyValuePair_0.Value.IdxClassLv3;
								stkSymbol.MstSymbol = SymbMgr.smethod_35(list_1, stkSymbol.ExchangeID, stkSymbol.Code);
								if (bool_1 && stkSymbol.MstSymbol.BeginDate == null)
								{
									SymbMgr.smethod_15(dictionary, list_0, TApp.SrvParams.StkBegEndDtLst, nullable_0, stkSymbol, stkSymbol.MstSymbol);
								}
								if (stkSymbol.MstSymbol.IsStock || stkSymbol.MstSymbol.BeginDate != null)
								{
									dictionary.Add(stkSymbol.ID, stkSymbol);
								}
							}
						}
					}
				}
			}
			if (TApp.SrvParams.UsrStkSymbols == null)
			{
				TApp.SrvParams.UsrStkSymbols = new Dictionary<int, StkSymbol>();
			}
			if (bool_1)
			{
				using (IEnumerator<StkSymbol> enumerator2 = dictionary.Values.OrderBy(new Func<StkSymbol, int>(LoginForm.<>c.<>9.method_2)).ThenBy(new Func<StkSymbol, DateTime?>(LoginForm.<>c.<>9.method_3)).GetEnumerator())
				{
					while (enumerator2.MoveNext())
					{
						StkSymbol stkSymbol2 = enumerator2.Current;
						TApp.SrvParams.UsrStkSymbols.Add(stkSymbol2.ID, stkSymbol2);
					}
					return;
				}
			}
			foreach (StkSymbol stkSymbol3 in dictionary.Values.OrderBy(new Func<StkSymbol, int>(LoginForm.<>c.<>9.method_4)).ThenBy(new Func<StkSymbol, string>(LoginForm.<>c.<>9.method_5)))
			{
				TApp.SrvParams.UsrStkSymbols.Add(stkSymbol3.ID, stkSymbol3);
			}
		}

		// Token: 0x0600100A RID: 4106 RVA: 0x000686C4 File Offset: 0x000668C4
		private Dictionary<int, StkSymbol> method_8(string string_0)
		{
			Dictionary<int, StkSymbol> result;
			if (string_0 != null)
			{
				Dictionary<int, StkSymbol> dictionary = new Dictionary<int, StkSymbol>();
				string[] array = string_0.Split(new string[]
				{
					Environment.NewLine
				}, StringSplitOptions.None);
				for (int i = 0; i < array.Length; i++)
				{
					string[] array2 = array[i].Split(new char[]
					{
						','
					});
					StkSymbol stkSymbol = new StkSymbol();
					stkSymbol.ID = Convert.ToInt32(array2[0].Trim());
					stkSymbol.ExchangeID = Convert.ToInt32(array2[1]);
					stkSymbol.Code = array2[2];
					stkSymbol.CNName = array2[3];
					if (array2.Length > 4)
					{
						stkSymbol.IdxClassLv1 = array2[4];
						stkSymbol.IdxClassLv2 = array2[5];
						stkSymbol.IdxClassLv3 = array2[6];
					}
					dictionary.Add(stkSymbol.ID, stkSymbol);
				}
				result = dictionary;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x0600100B RID: 4107 RVA: 0x00068798 File Offset: 0x00066998
		private void method_9(Dictionary<int, StkSymbol> dictionary_0)
		{
			if (TApp.SrvParams.StkSymbols == null)
			{
				TApp.SrvParams.StkSymbols = dictionary_0;
			}
			else
			{
				foreach (KeyValuePair<int, StkSymbol> keyValuePair in dictionary_0)
				{
					TApp.SrvParams.StkSymbols.Add(keyValuePair.Key, keyValuePair.Value);
				}
			}
		}

		// Token: 0x0600100C RID: 4108 RVA: 0x00068818 File Offset: 0x00066A18
		private XDocument method_10(List<SrvParam> list_0, string string_0, string string_1, string string_2, byte[] byte_0)
		{
			XDocument xdocument = null;
			SrvParam srvParam = this.method_14(list_0, string_0, string_1);
			if (srvParam != null)
			{
				if (srvParam.Value == null)
				{
					if (srvParam.Note != null && srvParam.Note.Equals("SrvSpmNotExist"))
					{
						return null;
					}
				}
				else
				{
					try
					{
						byte[] bytes = srvParam.Value as byte[];
						Utility.GenFileFromBytes(bytes, string_2);
						xdocument = Utility.GetXDocFromBytes(bytes);
					}
					catch
					{
					}
				}
			}
			if (xdocument == null && byte_0 != null && byte_0.Length != 0)
			{
				xdocument = XDocument.Load(string_2);
			}
			return xdocument;
		}

		// Token: 0x0600100D RID: 4109 RVA: 0x000688A4 File Offset: 0x00066AA4
		private string method_11(List<SrvParam> list_0, string string_0, string string_1, string string_2, byte[] byte_0, bool bool_1 = true)
		{
			string text = null;
			SrvParam srvParam = this.method_14(list_0, string_0, string_1);
			if (srvParam != null)
			{
				if (srvParam.Value == null)
				{
					if (srvParam.Note != null && srvParam.Note.Equals("SrvSpmNotExist"))
					{
						return null;
					}
				}
				else
				{
					try
					{
						byte[] bytes = srvParam.Value as byte[];
						Utility.GenFileFromBytes(bytes, string_2);
						text = Utility.GetStringFromBytes(bytes, bool_1, null, CompressAlgm.LZMA);
					}
					catch
					{
					}
				}
			}
			if (text == null && byte_0 != null && byte_0.Length != 0)
			{
				try
				{
					text = Utility.GetStringFromBytes(byte_0, bool_1, null, CompressAlgm.LZMA);
				}
				catch (Exception ex)
				{
					throw ex;
				}
			}
			return text;
		}

		// Token: 0x0600100E RID: 4110 RVA: 0x00068948 File Offset: 0x00066B48
		private XDocument method_12(List<SrvParam> list_0, string string_0, string string_1)
		{
			XDocument result = null;
			object obj = this.method_13(list_0, string_0, string_1);
			if (obj != null)
			{
				try
				{
					result = XDocument.Parse(obj as string);
				}
				catch
				{
				}
			}
			return result;
		}

		// Token: 0x0600100F RID: 4111 RVA: 0x0006898C File Offset: 0x00066B8C
		private object method_13(List<SrvParam> list_0, string string_0, string string_1)
		{
			SrvParam srvParam = this.method_14(list_0, string_0, string_1);
			object result;
			if (srvParam != null)
			{
				result = srvParam.Value;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001010 RID: 4112 RVA: 0x000689B4 File Offset: 0x00066BB4
		private SrvParam method_14(List<SrvParam> list_0, string string_0, string string_1)
		{
			LoginForm.Class236 @class = new LoginForm.Class236();
			@class.string_0 = string_0;
			@class.string_1 = string_1;
			return list_0.FirstOrDefault(new Func<SrvParam, bool>(@class.method_0));
		}

		// Token: 0x06001011 RID: 4113 RVA: 0x000689EC File Offset: 0x00066BEC
		private List<UsrStkMeta> method_15(XDocument xdocument_0)
		{
			List<UsrStkMeta> list = new List<UsrStkMeta>();
			if (xdocument_0 != null)
			{
				foreach (XElement xelement in xdocument_0.Element("UsrStkMetas").Elements("UsrStkMeta"))
				{
					int stkId = Convert.ToInt32(xelement.Element("StkId").Value);
					XElement xelement2 = xelement.Element("BeginDate");
					DateTime? beginDate = null;
					if (xelement2 != null && !string.IsNullOrEmpty(xelement2.Value))
					{
						beginDate = new DateTime?(Convert.ToDateTime(xelement2.Value));
					}
					XElement xelement3 = xelement.Element("EndDate");
					DateTime? endDate = null;
					if (xelement3 != null && !string.IsNullOrEmpty(xelement3.Value))
					{
						endDate = new DateTime?(Convert.ToDateTime(xelement3.Value));
					}
					List<DatInfo> list2 = null;
					XElement xelement4 = xelement.Element("DatInfoList");
					if (xelement4 != null)
					{
						IEnumerable<XElement> enumerable = xelement4.Elements("DatInfo");
						if (enumerable != null)
						{
							list2 = new List<DatInfo>();
							foreach (XElement xelement5 in enumerable)
							{
								DatInfo datInfo = new DatInfo();
								datInfo.BeginDate = new DateTime?(Convert.ToDateTime(xelement5.Attribute("BeginDate").Value));
								datInfo.EndDate = new DateTime?(Convert.ToDateTime(xelement5.Attribute("EndDate").Value));
								datInfo.PeriodType = (PeriodType)Convert.ToInt32(xelement5.Attribute("PeriodType").Value);
								int? periodUnits = null;
								XAttribute xattribute = xelement5.Attribute("PeriodUnits");
								if (xattribute != null && !string.IsNullOrEmpty(xattribute.Value))
								{
									periodUnits = new int?(Convert.ToInt32(xattribute.Value));
								}
								datInfo.PeriodUnits = periodUnits;
								list2.Add(datInfo);
							}
						}
					}
					UsrStkMeta item = new UsrStkMeta(stkId, beginDate, endDate, list2);
					list.Add(item);
				}
			}
			return list;
		}

		// Token: 0x06001012 RID: 4114 RVA: 0x00068C6C File Offset: 0x00066E6C
		private List<UsrStkMeta> method_16(int[] int_0, List<HDFileInfo> list_0, DateTime? nullable_0, DateTime? nullable_1)
		{
			LoginForm.Class237 @class = new LoginForm.Class237();
			@class.nullable_0 = nullable_1;
			List<UsrStkMeta> list = new List<UsrStkMeta>();
			if (int_0 != null)
			{
				for (int i = 0; i < int_0.Length; i++)
				{
					LoginForm.Class238 class2 = new LoginForm.Class238();
					class2.class237_0 = @class;
					class2.int_0 = int_0[i];
					IEnumerable<HDFileInfo> source = list_0.Where(new Func<HDFileInfo, bool>(class2.method_0));
					UsrStkMeta usrStkMeta = new UsrStkMeta();
					usrStkMeta.StkId = class2.int_0;
					usrStkMeta.BeginDate = nullable_0;
					if (source.Any<HDFileInfo>())
					{
						DateTime dateTime = source.Min(new Func<HDFileInfo, DateTime>(LoginForm.<>c.<>9.method_6));
						if (nullable_0 == null || dateTime > nullable_0.Value)
						{
							usrStkMeta.BeginDate = new DateTime?(dateTime);
						}
						usrStkMeta.EndDate = new DateTime?(source.Max(new Func<HDFileInfo, DateTime>(LoginForm.<>c.<>9.method_7)));
					}
					else
					{
						usrStkMeta.EndDate = new DateTime?(DateTime.Now.Date.AddDays(-1.0));
					}
					List<DatInfo> list2 = new List<DatInfo>();
					DatInfo datInfo = new DatInfo();
					datInfo.PeriodType = PeriodType.ByMins;
					datInfo.PeriodUnits = new int?(1);
					if (class2.class237_0.nullable_0 != null)
					{
						IEnumerable<HDFileInfo> source2 = list_0.Where(new Func<HDFileInfo, bool>(class2.method_1));
						if (source2.Any<HDFileInfo>())
						{
							datInfo.BeginDate = new DateTime?(source2.Min(new Func<HDFileInfo, DateTime>(LoginForm.<>c.<>9.method_8)));
						}
						else
						{
							datInfo.BeginDate = class2.class237_0.nullable_0;
						}
					}
					else
					{
						datInfo.BeginDate = usrStkMeta.BeginDate;
					}
					datInfo.EndDate = usrStkMeta.EndDate;
					list2.Add(datInfo);
					usrStkMeta.DatInfoList = list2;
					list.Add(usrStkMeta);
				}
			}
			return list;
		}

		// Token: 0x06001013 RID: 4115 RVA: 0x00068E8C File Offset: 0x0006708C
		private List<UsrStkMeta> method_17(int[] int_0, DateTime dateTime_0, DateTime dateTime_1, DateTime dateTime_2)
		{
			List<UsrStkMeta> list = new List<UsrStkMeta>();
			if (int_0 != null)
			{
				foreach (int stkId in int_0)
				{
					list.Add(new UsrStkMeta
					{
						StkId = stkId,
						BeginDate = new DateTime?(dateTime_0),
						EndDate = new DateTime?(dateTime_1),
						DatInfoList = new List<DatInfo>
						{
							new DatInfo
							{
								PeriodType = PeriodType.ByMins,
								PeriodUnits = new int?(1),
								BeginDate = new DateTime?(dateTime_2),
								EndDate = new DateTime?(dateTime_1)
							}
						}
					});
				}
			}
			return list;
		}

		// Token: 0x06001014 RID: 4116 RVA: 0x00068F48 File Offset: 0x00067148
		private List<TradingSymbol> method_18(XDocument xdocument_0)
		{
			List<TradingSymbol> list = new List<TradingSymbol>();
			if (xdocument_0 != null)
			{
				foreach (XElement xelement in xdocument_0.Element("TradingSymbols").Elements("Symbl"))
				{
					TradingSymbol tradingSymbol = new TradingSymbol();
					tradingSymbol.ID = Convert.ToInt32(xelement.Attribute("ID").Value);
					tradingSymbol.Code = xelement.Attribute("Code").Value;
					tradingSymbol.CNName = xelement.Attribute("CNName").Value;
					tradingSymbol.ENName = xelement.Attribute("ENName").Value;
					tradingSymbol.ExchangeID = Convert.ToInt32(xelement.Attribute("ExchangeID").Value);
					tradingSymbol.Type = (TradingSymbolType)Convert.ToInt32(xelement.Attribute("Type").Value);
					string value = xelement.Attribute("TonsPerUnit").Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.TonsPerUnit = new int?(Convert.ToInt32(value));
					}
					value = xelement.Attribute("LeastPriceVar").Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.LeastPriceVar = new decimal?(Convert.ToDecimal(value));
					}
					tradingSymbol.DigitNb = Convert.ToInt32(xelement.Attribute("DigitNb").Value);
					tradingSymbol.FeeType = (FeeType)Convert.ToInt32(xelement.Attribute("FeeType").Value);
					value = xelement.Attribute("IsOneSideFee").Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.IsOneSideFee = new bool?(Convert.ToBoolean(Convert.ToInt32(value)));
					}
					value = xelement.Attribute("MarginRate").Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.MarginRate = new decimal?(Convert.ToDecimal(value));
					}
					value = xelement.Attribute("AvgSlipg").Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.AvgSlipg = new decimal?(Convert.ToDecimal(value));
					}
					value = xelement.Attribute("FeePerUnit").Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.FeePerUnit = new decimal?(Convert.ToDecimal(value));
					}
					value = xelement.Attribute("FeeRate").Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.FeeRate = new decimal?(Convert.ToDecimal(value));
					}
					value = xelement.Attribute("AutoLimitTakePoints").Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.AutoLimitTakePoints = new decimal?(Convert.ToDecimal(value));
					}
					value = xelement.Attribute("AutoStopLossPoints").Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.AutoStopLossPoints = new decimal?(Convert.ToDecimal(value));
					}
					value = xelement.Attribute("DefaultUnits").Value;
					if (!string.IsNullOrEmpty(value))
					{
						tradingSymbol.DefaultUnits = new int?(Convert.ToInt32(value));
					}
					list.Add(tradingSymbol);
				}
			}
			return list;
		}

		// Token: 0x06001015 RID: 4117 RVA: 0x000692C4 File Offset: 0x000674C4
		private List<StSplit> method_19(string string_0)
		{
			List<StSplit> list = null;
			if (string_0 != null)
			{
				list = new List<StSplit>();
				string[] array = string_0.Split(new string[]
				{
					Environment.NewLine
				}, StringSplitOptions.None);
				for (int i = 0; i < array.Length; i++)
				{
					string[] array2 = array[i].Split(new char[]
					{
						','
					});
					list.Add(new StSplit
					{
						StockId = Convert.ToInt32(array2[0].Trim()),
						Date = Convert.ToDateTime(array2[1]),
						BonusShares = this.method_20(array2[2]),
						RationedShares = this.method_20(array2[3]),
						RationedSharePrice = this.method_20(array2[4]),
						Divident = this.method_20(array2[5])
					});
				}
			}
			return list;
		}

		// Token: 0x06001016 RID: 4118 RVA: 0x00069398 File Offset: 0x00067598
		private decimal? method_20(string string_0)
		{
			decimal? result = null;
			if (!string.IsNullOrEmpty(string_0))
			{
				result = new decimal?(0m);
				try
				{
					result = new decimal?(Convert.ToDecimal(string_0.Trim()));
				}
				catch
				{
				}
			}
			return result;
		}

		// Token: 0x06001017 RID: 4119 RVA: 0x000041AE File Offset: 0x000023AE
		private void method_21(object sender, EventArgs e)
		{
		}

		// Token: 0x06001018 RID: 4120 RVA: 0x000693F0 File Offset: 0x000675F0
		private void backgroundWorker_0_ProgressChanged(object sender, ProgressChangedEventArgs e)
		{
			int num = e.ProgressPercentage;
			if (num >= 0)
			{
				if (num > 100)
				{
					num = 100;
				}
				this.class297_0.Value = num;
				if (!this.class297_0.Visible)
				{
					this.class297_0.Visible = true;
				}
			}
			else
			{
				this.class297_0.Visible = false;
			}
			if (!this.label_msg.Visible)
			{
				this.label_msg.Visible = true;
			}
			if (e.UserState != null)
			{
				string text = e.UserState.ToString();
				if (!string.IsNullOrEmpty(text))
				{
					this.label_msg.Text = text;
					this.textBox_UsrName.Enabled = true;
					this.maskedTextBox_PW.Enabled = true;
				}
			}
			this.Refresh();
		}

		// Token: 0x06001019 RID: 4121 RVA: 0x000694A4 File Offset: 0x000676A4
		private void backgroundWorker_0_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
		{
			if (e.Error == null && !e.Cancelled)
			{
				if (this.class297_0.Visible)
				{
					this.class297_0.Visible = false;
				}
				if (!this.timer_0.Enabled)
				{
					this.method_23();
				}
				if (e.Result != null)
				{
					this.method_25();
					this.method_1((EventArgs9)e.Result);
				}
			}
		}

		// Token: 0x0600101A RID: 4122 RVA: 0x00006DA3 File Offset: 0x00004FA3
		private void method_22(object sender, EventArgs e)
		{
			this.method_2(new EventArgs());
			base.Close();
			Application.Exit();
		}

		// Token: 0x0600101B RID: 4123 RVA: 0x00006DBD File Offset: 0x00004FBD
		private void method_23()
		{
			if (!this.btn_Login.Enabled)
			{
				this.btn_Login.Enabled = true;
			}
		}

		// Token: 0x0600101C RID: 4124 RVA: 0x00006DDA File Offset: 0x00004FDA
		private void method_24()
		{
			this.class297_0.Visible = false;
			this.label_msg.Visible = false;
		}

		// Token: 0x0600101D RID: 4125 RVA: 0x00069510 File Offset: 0x00067710
		public void method_25()
		{
			base.Hide();
			base.ShowInTaskbar = false;
			Base.UI.Form.UserID = this.textBox_UsrName.Text;
			if (this.checkBox_remPsw.Checked)
			{
				if (!Base.UI.Form.IfSavePswd)
				{
					Base.UI.Form.IfSavePswd = true;
				}
				Base.UI.Form.Pswd = this.maskedTextBox_PW.Text;
			}
			else
			{
				if (Base.UI.Form.IfSavePswd)
				{
					Base.UI.Form.IfSavePswd = false;
				}
				Base.UI.Form.Pswd = string.Empty;
			}
		}

		// Token: 0x0600101E RID: 4126 RVA: 0x000695A4 File Offset: 0x000677A4
		protected void WndProc(ref Message m)
		{
			if (m.Msg == 274 && (int)m.WParam == 61536)
			{
				try
				{
					base.CancelButton.PerformClick();
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
			else
			{
				base.WndProc(ref m);
			}
		}

		// Token: 0x0600101F RID: 4127 RVA: 0x00006DF6 File Offset: 0x00004FF6
		private void linkLabel_TExWebLink_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			this.method_26(this.linkLabel_TExWebLink.Text);
		}

		// Token: 0x06001020 RID: 4128 RVA: 0x00069600 File Offset: 0x00067800
		private void linkLabel_PswdRcvr_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			if (MessageBox.Show("将访问交易练习者网站重置密码页面，继续吗？" + Environment.NewLine + Environment.NewLine + "注：如网站为已登录状态，需先退出登录才能访问该页面。", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
			{
				this.method_26(this.linkLabel_TExWebLink.Text + "/forgotpswd");
			}
		}

		// Token: 0x06001021 RID: 4129 RVA: 0x00069654 File Offset: 0x00067854
		private void method_26(string string_0)
		{
			try
			{
				string str = "https://";
				if (!string_0.StartsWith("www"))
				{
					str = "http://";
				}
				Process.Start(new ProcessStartInfo(str + string_0));
			}
			catch
			{
			}
		}

		// Token: 0x06001022 RID: 4130 RVA: 0x00006E0B File Offset: 0x0000500B
		private void LoginForm_FormClosing(object sender, FormClosingEventArgs e)
		{
			if (this.backgroundWorker_0.IsBusy)
			{
				this.backgroundWorker_0.CancelAsync();
			}
		}

		// Token: 0x06001023 RID: 4131 RVA: 0x000696A4 File Offset: 0x000678A4
		private string method_27()
		{
			string result = "";
			try
			{
				using (Graphics graphics = base.CreateGraphics())
				{
					result = graphics.DpiX.ToString();
				}
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
			return result;
		}

		// Token: 0x06001024 RID: 4132 RVA: 0x00006E27 File Offset: 0x00005027
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x040007F6 RID: 2038
		private Class297 class297_0;

		// Token: 0x040007F7 RID: 2039
		private BackgroundWorker backgroundWorker_0;

		// Token: 0x040007F8 RID: 2040
		private ITExSrv itexSrv_0;

		// Token: 0x040007F9 RID: 2041
		private SplashScreen splashScreen_0;

		// Token: 0x040007FA RID: 2042
		private System.Windows.Forms.Timer timer_0;

		// Token: 0x040007FB RID: 2043
		private bool bool_0;

		// Token: 0x040007FC RID: 2044
		private Keys keys_0;

		// Token: 0x040007FD RID: 2045
		[CompilerGenerated]
		private Delegate12 delegate12_0;

		// Token: 0x040007FE RID: 2046
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x040007FF RID: 2047
		private IContainer icontainer_0;

		// Token: 0x0200019C RID: 412
		[CompilerGenerated]
		private sealed class Class231
		{
			// Token: 0x06001027 RID: 4135 RVA: 0x0006A198 File Offset: 0x00068398
			internal bool method_0(KeyValuePair<string, DateTime> keyValuePair_0)
			{
				return keyValuePair_0.Value == this.list_0.Max(new Func<KeyValuePair<string, DateTime>, DateTime>(LoginForm.<>c.<>9.method_0));
			}

			// Token: 0x0400080E RID: 2062
			public List<KeyValuePair<string, DateTime>> list_0;
		}

		// Token: 0x0200019E RID: 414
		[CompilerGenerated]
		private sealed class Class232
		{
			// Token: 0x04000819 RID: 2073
			public BackgroundWorker backgroundWorker_0;
		}

		// Token: 0x0200019F RID: 415
		[CompilerGenerated]
		private sealed class Class233
		{
			// Token: 0x06001035 RID: 4149 RVA: 0x0006A284 File Offset: 0x00068484
			internal void method_0(UpdateProgressInfo updateProgressInfo_0)
			{
				if (updateProgressInfo_0.StillWorking)
				{
					try
					{
						if (this.class232_0.backgroundWorker_0.IsBusy)
						{
							this.class232_0.backgroundWorker_0.ReportProgress(69 + Convert.ToInt32(Math.Floor(21.0 * ((double)updateProgressInfo_0.TaskId / (double)this.int_0))), "正在更新行情数据，请稍候...");
						}
					}
					catch
					{
					}
				}
			}

			// Token: 0x0400081A RID: 2074
			public int int_0;

			// Token: 0x0400081B RID: 2075
			public LoginForm.Class232 class232_0;
		}

		// Token: 0x020001A0 RID: 416
		[CompilerGenerated]
		private sealed class Class234
		{
			// Token: 0x06001037 RID: 4151 RVA: 0x0006A300 File Offset: 0x00068500
			internal bool method_0(UsrStkMeta usrStkMeta_0)
			{
				return usrStkMeta_0.StkId == this.keyValuePair_0.Key;
			}

			// Token: 0x06001038 RID: 4152 RVA: 0x0006A324 File Offset: 0x00068524
			internal bool method_1(StkBegEndDate stkBegEndDate_0)
			{
				return stkBegEndDate_0.StkId == this.keyValuePair_0.Value.ID;
			}

			// Token: 0x0400081C RID: 2076
			public KeyValuePair<int, StkSymbol> keyValuePair_0;
		}

		// Token: 0x020001A1 RID: 417
		[CompilerGenerated]
		private sealed class Class235
		{
			// Token: 0x0600103A RID: 4154 RVA: 0x0006A350 File Offset: 0x00068550
			internal bool method_0(UsrStkMeta usrStkMeta_0)
			{
				return usrStkMeta_0.StkCode == this.string_0;
			}

			// Token: 0x0400081D RID: 2077
			public string string_0;
		}

		// Token: 0x020001A2 RID: 418
		[CompilerGenerated]
		private sealed class Class236
		{
			// Token: 0x0600103C RID: 4156 RVA: 0x0006A374 File Offset: 0x00068574
			internal bool method_0(SrvParam srvParam_0)
			{
				bool result;
				if (srvParam_0.Name == this.string_0)
				{
					result = (srvParam_0.Ver == this.string_1);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400081E RID: 2078
			public string string_0;

			// Token: 0x0400081F RID: 2079
			public string string_1;
		}

		// Token: 0x020001A3 RID: 419
		[CompilerGenerated]
		private sealed class Class237
		{
			// Token: 0x04000820 RID: 2080
			public DateTime? nullable_0;
		}

		// Token: 0x020001A4 RID: 420
		[CompilerGenerated]
		private sealed class Class238
		{
			// Token: 0x0600103F RID: 4159 RVA: 0x0006A3B0 File Offset: 0x000685B0
			internal bool method_0(HDFileInfo hdfileInfo_0)
			{
				bool result;
				if (hdfileInfo_0.StkID == this.int_0)
				{
					result = hdfileInfo_0.IsPeriod_1m();
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x06001040 RID: 4160 RVA: 0x0006A3DC File Offset: 0x000685DC
			internal bool method_1(HDFileInfo hdfileInfo_0)
			{
				bool result;
				if (hdfileInfo_0.StkID == this.int_0 && hdfileInfo_0.IsPeriod_1m())
				{
					result = (hdfileInfo_0.BeginDate.Year == this.class237_0.nullable_0.Value.Year);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000821 RID: 2081
			public int int_0;

			// Token: 0x04000822 RID: 2082
			public LoginForm.Class237 class237_0;
		}
	}
}
