﻿using System;
using System.Windows.Forms;
using ns28;
using ns7;
using TEx;

namespace ns11
{
	// Token: 0x0200029A RID: 666
	internal sealed class Class53 : Class51
	{
		// Token: 0x06001D91 RID: 7569 RVA: 0x0000C56B File Offset: 0x0000A76B
		public Class53(Panel panel_1, int int_4, int int_5, ChtCtrl_KLine chtCtrl_KLine_0, bool bool_3) : base(panel_1, int_4, int_5, chtCtrl_KLine_0, bool_3)
		{
			base.method_0(Class372.fast_forward);
		}

		// Token: 0x06001D92 RID: 7570 RVA: 0x0000C587 File Offset: 0x0000A787
		public Class53(Panel panel_1, int int_4, int int_5, ChtCtrl_KLine chtCtrl_KLine_0) : this(panel_1, int_4, int_5, chtCtrl_KLine_0, true)
		{
		}

		// Token: 0x06001D93 RID: 7571 RVA: 0x0000C595 File Offset: 0x0000A795
		protected override void Class48_MouseEnter(object sender, EventArgs e)
		{
			base.Class48_MouseEnter(sender, e);
			base.method_0(Class372.fast_forward_red);
		}

		// Token: 0x06001D94 RID: 7572 RVA: 0x0000C5AC File Offset: 0x0000A7AC
		protected override void Class48_MouseLeave(object sender, EventArgs e)
		{
			base.Class48_MouseLeave(sender, e);
			base.method_0(Class372.fast_forward);
		}

		// Token: 0x06001D95 RID: 7573 RVA: 0x000C9318 File Offset: 0x000C7518
		protected override void Class48_Click(object sender, EventArgs e)
		{
			base.Class48_Click(sender, e);
			ChtCtrl_KLine chtCtrl_KLine = (ChtCtrl_KLine)base.ChtCtrl;
			if (chtCtrl_KLine.IsInCrossReviewMode)
			{
				chtCtrl_KLine.IsInCrossReviewMode = false;
			}
			Base.UI.smethod_117();
		}
	}
}
