﻿using System;
using ns24;
using TEx.SIndicator;

namespace ns21
{
	// Token: 0x020002E1 RID: 737
	internal sealed class Class347 : IndEx
	{
		// Token: 0x060020CD RID: 8397 RVA: 0x0000D3E4 File Offset: 0x0000B5E4
		public Class347(UserDefineInd userDefineInd_2) : base(userDefineInd_2)
		{
		}

		// Token: 0x060020CE RID: 8398 RVA: 0x000E1654 File Offset: 0x000DF854
		protected override void vmethod_0()
		{
			base.vmethod_0();
			foreach (ShapeCurve shapeCurve in this.list_0)
			{
				if (shapeCurve is Class387)
				{
					shapeCurve.Curve.IsY2Axis = true;
				}
			}
		}

		// Token: 0x060020CF RID: 8399 RVA: 0x0000D3ED File Offset: 0x0000B5ED
		public override void RescaleAxis()
		{
			base.GraphControl.GraphPane.YAxis.Scale.MagAuto = false;
		}
	}
}
