﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns19;
using TEx;
using TEx.Inds;

namespace ns13
{
	// Token: 0x02000335 RID: 821
	internal sealed class UserControlIndParam : UserControl
	{
		// Token: 0x06002299 RID: 8857 RVA: 0x000EB8CC File Offset: 0x000E9ACC
		public UserControlIndParam()
		{
			this.InitializeComponent();
			Base.UI.smethod_55(this.dataGridView1);
			this.dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
			this.dataGridView1.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.None;
			this.dataGridView1.SelectionMode = DataGridViewSelectionMode.CellSelect;
			this.dataGridView1.AutoGenerateColumns = true;
			this.dataGridView1.Enabled = true;
			this.dataGridView1.EditingControlShowing += this.dataGridView1_EditingControlShowing;
			this.dataGridView1.DataError += this.dataGridView1_DataError;
			this.dataGridView1.BackgroundColor = Color.White;
			this.dataGridView1.Leave += this.dataGridView1_Leave;
			this.dataGridView1.Refresh();
			this.dataGridView1.Invalidated += this.dataGridView1_Invalidated;
			this.dataGridView1.AllowUserToAddRows = false;
			int index = this.dataGridView1.Columns.Add("Name", "名称");
			this.dataGridView1.Columns[index].ValueType = typeof(string);
			index = this.dataGridView1.Columns.Add("Min", "最小值");
			this.dataGridView1.Columns[index].ValueType = typeof(double);
			index = this.dataGridView1.Columns.Add("Max", "最大值");
			this.dataGridView1.Columns[index].ValueType = typeof(double);
			index = this.dataGridView1.Columns.Add("Value", "当前值");
			this.dataGridView1.Columns[index].ValueType = typeof(double);
			for (int i = 0; i < 16; i++)
			{
				this.dataGridView1.Rows.Add();
			}
			for (int j = 0; j < this.dataGridView1.ColumnCount; j++)
			{
				this.dataGridView1.Columns[j].SortMode = DataGridViewColumnSortMode.NotSortable;
			}
		}

		// Token: 0x0600229A RID: 8858 RVA: 0x000EBAF8 File Offset: 0x000E9CF8
		private void dataGridView1_DataError(object sender, DataGridViewDataErrorEventArgs e)
		{
			string text;
			if (e.ColumnIndex != 0)
			{
				text = "请输入有效数字。";
			}
			else
			{
				text = e.Exception.Message;
			}
			MessageBox.Show(text, "错误");
			e.Cancel = true;
		}

		// Token: 0x140000A5 RID: 165
		// (add) Token: 0x0600229B RID: 8859 RVA: 0x000EBB3C File Offset: 0x000E9D3C
		// (remove) Token: 0x0600229C RID: 8860 RVA: 0x000EBB74 File Offset: 0x000E9D74
		public event EventHandler OnEndEdit
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600229D RID: 8861 RVA: 0x000EBBAC File Offset: 0x000E9DAC
		private void dataGridView1_Leave(object sender, EventArgs e)
		{
			if (this.dataGridView1.IsCurrentCellInEditMode)
			{
				try
				{
					this.dataGridView1.CurrentCell = null;
				}
				catch (Exception)
				{
				}
			}
			this.method_0();
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x0600229E RID: 8862 RVA: 0x000EBC04 File Offset: 0x000E9E04
		private void dataGridView1_EditingControlShowing(object sender, DataGridViewEditingControlShowingEventArgs e)
		{
			DataGridView dataGridView = (DataGridView)sender;
			if (e.Control is DataGridViewTextBoxEditingControl)
			{
				DataGridViewTextBoxEditingControl dataGridViewTextBoxEditingControl = (DataGridViewTextBoxEditingControl)e.Control;
				if (dataGridView.CurrentCell.OwningColumn.Name == "Name")
				{
					dataGridViewTextBoxEditingControl.CharacterCasing = CharacterCasing.Upper;
				}
				else
				{
					dataGridViewTextBoxEditingControl.CharacterCasing = CharacterCasing.Normal;
				}
			}
		}

		// Token: 0x170005EB RID: 1515
		// (get) Token: 0x0600229F RID: 8863 RVA: 0x000EBC60 File Offset: 0x000E9E60
		public List<UserDefineParam> ShowList
		{
			get
			{
				return this.list_0;
			}
		}

		// Token: 0x060022A0 RID: 8864 RVA: 0x000EBC78 File Offset: 0x000E9E78
		private void method_0()
		{
			this.list_0 = new List<UserDefineParam>();
			for (int i = 0; i < this.dataGridView1.RowCount; i++)
			{
				UserDefineParam userDefineParam = this.method_1(i);
				if (userDefineParam != null)
				{
					this.list_0.Add(userDefineParam);
				}
			}
		}

		// Token: 0x060022A1 RID: 8865 RVA: 0x000EBCC0 File Offset: 0x000E9EC0
		private UserDefineParam method_1(int int_1)
		{
			UserDefineParam result;
			if (this.dataGridView1.Rows[int_1].Cells["Name"].Value == null)
			{
				result = null;
			}
			else
			{
				string name = this.dataGridView1.Rows[int_1].Cells["Name"].Value as string;
				object value = this.dataGridView1.Rows[int_1].Cells["Min"].Value;
				if (!(value is double))
				{
					this.dataGridView1.Rows[int_1].Cells["Min"].Selected = true;
					MessageBox.Show("第" + (int_1 + 1).ToString() + "行请输入最小值。", "错误");
					result = null;
				}
				else
				{
					double min = (double)value;
					value = this.dataGridView1.Rows[int_1].Cells["Max"].Value;
					if (!(value is double))
					{
						this.dataGridView1.Rows[int_1].Cells["Max"].Selected = true;
						MessageBox.Show("第" + (int_1 + 1).ToString() + "行请输入最大值。", "错误");
						result = null;
					}
					else
					{
						double max = (double)value;
						value = this.dataGridView1.Rows[int_1].Cells["Value"].Value;
						if (!(value is double))
						{
							this.dataGridView1.Rows[int_1].Cells["Value"].Selected = true;
							MessageBox.Show("第" + (int_1 + 1).ToString() + "行请输入数值。", "错误");
							result = null;
						}
						else
						{
							double value2 = (double)value;
							result = new UserDefineParam(name, max, min, value2, 1.0);
						}
					}
				}
			}
			return result;
		}

		// Token: 0x170005EC RID: 1516
		// (get) Token: 0x060022A2 RID: 8866 RVA: 0x000EBEFC File Offset: 0x000EA0FC
		// (set) Token: 0x060022A3 RID: 8867 RVA: 0x0000DA8B File Offset: 0x0000BC8B
		private Enum25 DoType { get; set; }

		// Token: 0x060022A4 RID: 8868 RVA: 0x000EBF14 File Offset: 0x000EA114
		public void method_2(List<UserDefineParam> list_1, Enum25 enum25_1, UserControlIndParam.Enum27 enum27_1)
		{
			this.dataGridView1.Rows.Clear();
			if (enum27_1 == UserControlIndParam.Enum27.const_1)
			{
				if (list_1 != null)
				{
					for (int i = 0; i < list_1.Count; i++)
					{
						this.dataGridView1.Rows.Add();
					}
				}
				if (this.dataGridView1.ColumnCount >= 4)
				{
					this.dataGridView1.Columns[3].HeaderText = "当前值";
				}
			}
			else if (enum27_1 == UserControlIndParam.Enum27.const_0)
			{
				for (int j = 0; j < 16; j++)
				{
					this.dataGridView1.Rows.Add();
				}
				if (this.dataGridView1.ColumnCount >= 4)
				{
					this.dataGridView1.Columns[3].HeaderText = "默认值";
				}
			}
			this.enum27_0 = enum27_1;
			if (list_1 != null && list_1.Any<UserDefineParam>())
			{
				for (int k = 0; k < list_1.Count; k++)
				{
					if (k >= 16)
					{
						break;
					}
					this.dataGridView1.Rows[k].Cells["Name"].Value = list_1[k].Name;
					this.dataGridView1.Rows[k].Cells["Min"].Value = list_1[k].Min;
					this.dataGridView1.Rows[k].Cells["Max"].Value = list_1[k].Max;
					this.dataGridView1.Rows[k].Cells["Value"].Value = list_1[k].Value;
				}
			}
			else if (list_1 != null && !list_1.Any<UserDefineParam>())
			{
				this.dataGridView1.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.DisplayedCells;
			}
			this.DoType = enum25_1;
			this.method_3();
			this.list_0 = list_1;
		}

		// Token: 0x060022A5 RID: 8869 RVA: 0x000EC10C File Offset: 0x000EA30C
		private void method_3()
		{
			for (int i = 0; i < this.dataGridView1.Columns.Count; i++)
			{
				DataGridViewColumn dataGridViewColumn = this.dataGridView1.Columns[i];
				if (dataGridViewColumn.Name == "Name")
				{
					if (this.dataGridView1.RowCount > 0 && this.enum27_0 == UserControlIndParam.Enum27.const_0)
					{
						this.dataGridView1.Rows[0].Cells[dataGridViewColumn.Name].Selected = true;
					}
					if ((this.DoType & Enum25.flag_0) != Enum25.flag_0)
					{
						dataGridViewColumn.ReadOnly = true;
						dataGridViewColumn.DefaultCellStyle.BackColor = Color.Silver;
					}
				}
				else if (dataGridViewColumn.Name == "Value")
				{
					if (this.dataGridView1.RowCount > 0 && this.enum27_0 == UserControlIndParam.Enum27.const_1)
					{
						this.dataGridView1.Rows[0].Cells[dataGridViewColumn.Name].Selected = true;
					}
				}
				else if (dataGridViewColumn.Name == "Max")
				{
					if ((this.DoType & Enum25.flag_0) != Enum25.flag_0)
					{
						dataGridViewColumn.ReadOnly = true;
						dataGridViewColumn.DefaultCellStyle.BackColor = Color.Silver;
					}
				}
				else if (dataGridViewColumn.Name == "Min")
				{
					if ((this.DoType & Enum25.flag_0) != Enum25.flag_0)
					{
						dataGridViewColumn.ReadOnly = true;
						dataGridViewColumn.DefaultCellStyle.BackColor = Color.Silver;
					}
				}
				else
				{
					if (!(dataGridViewColumn.Name == "Step"))
					{
						throw new Exception(string.Format("{0}无法解析为显示文字", dataGridViewColumn.Name));
					}
					if ((this.DoType & Enum25.flag_0) != Enum25.flag_0)
					{
						dataGridViewColumn.ReadOnly = true;
					}
					dataGridViewColumn.Visible = false;
				}
			}
		}

		// Token: 0x060022A6 RID: 8870 RVA: 0x000041AE File Offset: 0x000023AE
		private void dataGridView1_Invalidated(object sender, InvalidateEventArgs e)
		{
		}

		// Token: 0x060022A7 RID: 8871 RVA: 0x0000DA96 File Offset: 0x0000BC96
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060022A8 RID: 8872 RVA: 0x000EC2D0 File Offset: 0x000EA4D0
		private void InitializeComponent()
		{
			this.dataGridView1 = new DataGridView();
			((ISupportInitialize)this.dataGridView1).BeginInit();
			base.SuspendLayout();
			this.dataGridView1.BorderStyle = BorderStyle.None;
			this.dataGridView1.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.dataGridView1.Dock = DockStyle.Fill;
			this.dataGridView1.GridColor = SystemColors.ControlLight;
			this.dataGridView1.Location = new Point(0, 0);
			this.dataGridView1.Margin = new Padding(3, 2, 3, 2);
			this.dataGridView1.Name = "dataGridView1";
			this.dataGridView1.RowTemplate.Height = 20;
			this.dataGridView1.Size = new Size(385, 220);
			this.dataGridView1.TabIndex = 0;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.dataGridView1);
			base.Margin = new Padding(3, 2, 3, 2);
			base.Name = "UserControlIndParam";
			base.Size = new Size(385, 220);
			((ISupportInitialize)this.dataGridView1).EndInit();
			base.ResumeLayout(false);
		}

		// Token: 0x040010CC RID: 4300
		private const int int_0 = 16;

		// Token: 0x040010CD RID: 4301
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x040010CE RID: 4302
		private List<UserDefineParam> list_0 = new List<UserDefineParam>();

		// Token: 0x040010CF RID: 4303
		[CompilerGenerated]
		private Enum25 enum25_0;

		// Token: 0x040010D0 RID: 4304
		private UserControlIndParam.Enum27 enum27_0;

		// Token: 0x040010D1 RID: 4305
		private IContainer icontainer_0;

		// Token: 0x040010D2 RID: 4306
		private DataGridView dataGridView1;

		// Token: 0x02000336 RID: 822
		public enum Enum27
		{
			// Token: 0x040010D4 RID: 4308
			const_0,
			// Token: 0x040010D5 RID: 4309
			const_1
		}
	}
}
