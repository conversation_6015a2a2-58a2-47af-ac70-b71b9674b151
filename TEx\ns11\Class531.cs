﻿using System;
using System.Net;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using ns15;
using ns19;
using ns22;
using ns28;
using ns30;
using ns32;
using ns6;

namespace ns11
{
	// Token: 0x020003F4 RID: 1012
	internal class Class531
	{
		// Token: 0x0600278F RID: 10127 RVA: 0x0000F20C File Offset: 0x0000D40C
		public void method_0(IWebProxy iwebProxy_1)
		{
			this.iwebProxy_0 = iwebProxy_1;
		}

		// Token: 0x06002790 RID: 10128 RVA: 0x00102704 File Offset: 0x00100904
		internal bool method_1(byte[] byte_0, Class531.Class535 class535_0)
		{
			byte[] byte_;
			bool result;
			try
			{
				byte_ = Class518.smethod_4(byte_0);
				goto IL_1E;
			}
			catch (Exception)
			{
				this.method_3(Enum35.const_0, Class518.string_0);
				result = false;
			}
			return result;
			IL_1E:
			byte[] array = Class530.smethod_0(byte_, "<RSAKeyValue><Modulus>yQ2ConS5yOgbrcyEORGVmInrvFP5w6o7sazmQbVv+6zQy2jZIyOTO9IGZLSL3u1SOOyw1/Bl0b8VinqMKIocyDWI0FQe3cChNRue5/otHubE9DplNtBZHWm1OQ263cVUcudufVjHy38OGFNAoLoDY4Fl3ZMsbiERNrKQEnpaqA0=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>");
			if (array == null)
			{
				this.method_3(Enum35.const_0, Class530.string_0);
				return false;
			}
			this.method_4(Enum35.const_1);
			Class542 @class = new Class542("dfe5495a-62b7-4129-ead4-35d316d50a78");
			if (this.iwebProxy_0 != null)
			{
				@class.method_0(this.iwebProxy_0);
			}
			Class531.Class533 class2 = new Class531.Class533(this, array, @class, class535_0);
			@class.method_1(new Delegate39(class2.method_0));
			return class2.bool_0;
		}

		// Token: 0x140000C0 RID: 192
		// (add) Token: 0x06002791 RID: 10129 RVA: 0x001027A4 File Offset: 0x001009A4
		// (remove) Token: 0x06002792 RID: 10130 RVA: 0x001027DC File Offset: 0x001009DC
		public event Delegate38 SendingReportFeedback
		{
			[CompilerGenerated]
			add
			{
				Delegate38 @delegate = this.delegate38_0;
				Delegate38 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate38 value2 = (Delegate38)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate38>(ref this.delegate38_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate38 @delegate = this.delegate38_0;
				Delegate38 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate38 value2 = (Delegate38)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate38>(ref this.delegate38_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06002793 RID: 10131 RVA: 0x00102814 File Offset: 0x00100A14
		protected void method_2(Enum35 enum35_0, string string_3, string string_4)
		{
			Delegate38 @delegate = this.delegate38_0;
			if (@delegate != null)
			{
				@delegate(this, new EventArgs37(enum35_0, string_3, string_4));
			}
		}

		// Token: 0x06002794 RID: 10132 RVA: 0x0000F215 File Offset: 0x0000D415
		protected void method_3(Enum35 enum35_0, string string_3)
		{
			this.method_2(enum35_0, string_3, string.Empty);
		}

		// Token: 0x06002795 RID: 10133 RVA: 0x0000F224 File Offset: 0x0000D424
		protected void method_4(Enum35 enum35_0)
		{
			this.method_3(enum35_0, string.Empty);
		}

		// Token: 0x0400139F RID: 5023
		protected const string string_0 = "{100fd8cd-4fe2-410e-8c33-ae1af08ef31d}";

		// Token: 0x040013A0 RID: 5024
		private const string string_1 = "{be78a0c5-c47c-4127-a428-52bdc580a02f}";

		// Token: 0x040013A1 RID: 5025
		private const string string_2 = "{bf13b64c-b3d2-4165-b3f5-7f852d4744cf}";

		// Token: 0x040013A2 RID: 5026
		private IWebProxy iwebProxy_0;

		// Token: 0x040013A3 RID: 5027
		[CompilerGenerated]
		private Delegate38 delegate38_0;

		// Token: 0x020003F5 RID: 1013
		private sealed class Class533
		{
			// Token: 0x06002797 RID: 10135 RVA: 0x0000F232 File Offset: 0x0000D432
			public Class533(Class531 class531_1, byte[] byte_1, Class542 class542_1, Class531.Class535 class535_1)
			{
				this.class531_0 = class531_1;
				this.class535_0 = class535_1;
				this.class542_0 = class542_1;
				this.byte_0 = byte_1;
			}

			// Token: 0x06002798 RID: 10136 RVA: 0x0010283C File Offset: 0x00100A3C
			public void method_0(string string_0)
			{
				if (string_0 == "OK")
				{
					this.class531_0.method_4(Enum35.const_2);
					byte[] bytes = Encoding.UTF8.GetBytes("{8615E982-79D1-4651-9862-40779F762E5C}");
					byte[] destinationArray = new byte[bytes.Length + this.byte_0.Length];
					Array.Copy(bytes, destinationArray, bytes.Length);
					Array.Copy(this.byte_0, 0, destinationArray, bytes.Length, this.byte_0.Length);
					Class531.Class534 @class = new Class531.Class534(this.class531_0);
					this.class542_0.method_2(destinationArray, this.class535_0.EmailAddress, this.class535_0.AppFriendlyName, this.class535_0.BuildFriendlyNumber, new Delegate39(@class.method_0));
					this.bool_0 = @class.bool_0;
					return;
				}
				if (this.class531_0.delegate38_0 != null)
				{
					this.class531_0.delegate38_0(this, new EventArgs37(Enum35.const_1, string_0));
				}
				this.bool_0 = false;
			}

			// Token: 0x040013A4 RID: 5028
			private readonly Class531 class531_0;

			// Token: 0x040013A5 RID: 5029
			private readonly byte[] byte_0;

			// Token: 0x040013A6 RID: 5030
			private readonly Class542 class542_0;

			// Token: 0x040013A7 RID: 5031
			private readonly Class531.Class535 class535_0;

			// Token: 0x040013A8 RID: 5032
			public bool bool_0 = true;
		}

		// Token: 0x020003F6 RID: 1014
		private sealed class Class534
		{
			// Token: 0x06002799 RID: 10137 RVA: 0x0000F25E File Offset: 0x0000D45E
			public Class534(Class531 class531_1)
			{
				this.class531_0 = class531_1;
			}

			// Token: 0x0600279A RID: 10138 RVA: 0x0000F26D File Offset: 0x0000D46D
			public void method_0(string string_0)
			{
				if (string_0.StartsWith("ERR"))
				{
					this.class531_0.method_3(Enum35.const_2, string_0);
					this.bool_0 = false;
					return;
				}
				this.class531_0.method_2(Enum35.const_3, string.Empty, string_0);
				this.bool_0 = true;
			}

			// Token: 0x040013A9 RID: 5033
			private readonly Class531 class531_0;

			// Token: 0x040013AA RID: 5034
			public bool bool_0;
		}

		// Token: 0x020003F7 RID: 1015
		internal sealed class Class535
		{
			// Token: 0x0600279B RID: 10139 RVA: 0x0000F2AA File Offset: 0x0000D4AA
			public Class535(string string_3, string string_4, string string_5)
			{
				this.string_0 = string_3;
				this.string_2 = string_5;
				this.string_1 = string_4;
			}

			// Token: 0x170006CB RID: 1739
			// (get) Token: 0x0600279C RID: 10140 RVA: 0x0000F2C7 File Offset: 0x0000D4C7
			public string BuildFriendlyNumber
			{
				get
				{
					return this.string_2;
				}
			}

			// Token: 0x170006CC RID: 1740
			// (get) Token: 0x0600279D RID: 10141 RVA: 0x0000F2CF File Offset: 0x0000D4CF
			public string AppFriendlyName
			{
				get
				{
					return this.string_1;
				}
			}

			// Token: 0x170006CD RID: 1741
			// (get) Token: 0x0600279E RID: 10142 RVA: 0x0000F2D7 File Offset: 0x0000D4D7
			public string EmailAddress
			{
				get
				{
					return this.string_0;
				}
			}

			// Token: 0x040013AB RID: 5035
			public static Class531.Class535 class535_0 = new Class531.Class535(null, null, null);

			// Token: 0x040013AC RID: 5036
			private readonly string string_0;

			// Token: 0x040013AD RID: 5037
			private readonly string string_1;

			// Token: 0x040013AE RID: 5038
			private readonly string string_2;
		}
	}
}
