﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using ns32;
using TEx.Comn;

namespace ns12
{
	// Token: 0x0200013A RID: 314
	internal sealed class Class191
	{
		// Token: 0x06000CC1 RID: 3265 RVA: 0x00005B70 File Offset: 0x00003D70
		public Class191(int int_1, IEnumerable<HDFileInfo> ienumerable_1, Class192 class192_1)
		{
			this.StkId = this.StkId;
			this.HDFileInfos = ienumerable_1;
			this.ApiParam = class192_1;
			this.FetchTime = DateTime.Now;
		}

		// Token: 0x17000204 RID: 516
		// (get) Token: 0x06000CC2 RID: 3266 RVA: 0x0004A9E0 File Offset: 0x00048BE0
		// (set) Token: 0x06000CC3 RID: 3267 RVA: 0x00005B9F File Offset: 0x00003D9F
		public int StkId { get; set; }

		// Token: 0x17000205 RID: 517
		// (get) Token: 0x06000CC4 RID: 3268 RVA: 0x0004A9F8 File Offset: 0x00048BF8
		// (set) Token: 0x06000CC5 RID: 3269 RVA: 0x00005BAA File Offset: 0x00003DAA
		public IEnumerable<HDFileInfo> HDFileInfos { get; set; }

		// Token: 0x17000206 RID: 518
		// (get) Token: 0x06000CC6 RID: 3270 RVA: 0x0004AA10 File Offset: 0x00048C10
		// (set) Token: 0x06000CC7 RID: 3271 RVA: 0x00005BB5 File Offset: 0x00003DB5
		public Class192 ApiParam { get; set; }

		// Token: 0x17000207 RID: 519
		// (get) Token: 0x06000CC8 RID: 3272 RVA: 0x0004AA28 File Offset: 0x00048C28
		// (set) Token: 0x06000CC9 RID: 3273 RVA: 0x00005BC0 File Offset: 0x00003DC0
		public DateTime FetchTime { get; set; }

		// Token: 0x04000541 RID: 1345
		[CompilerGenerated]
		private int int_0;

		// Token: 0x04000542 RID: 1346
		[CompilerGenerated]
		private IEnumerable<HDFileInfo> ienumerable_0;

		// Token: 0x04000543 RID: 1347
		[CompilerGenerated]
		private Class192 class192_0;

		// Token: 0x04000544 RID: 1348
		[CompilerGenerated]
		private DateTime dateTime_0;
	}
}
