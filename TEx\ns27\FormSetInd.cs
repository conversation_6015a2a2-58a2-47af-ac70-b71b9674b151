﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using ns13;
using ns19;
using TEx;
using TEx.Inds;
using TEx.SIndicator;

namespace ns27
{
	// Token: 0x020002D9 RID: 729
	internal sealed partial class FormSetInd : Form
	{
		// Token: 0x06002084 RID: 8324 RVA: 0x000DF9EC File Offset: 0x000DDBEC
		public FormSetInd()
		{
			this.InitializeComponent();
			base.StartPosition = FormStartPosition.CenterScreen;
			base.Load += this.FormSetInd_Load;
			base.FormClosed += this.FormSetInd_FormClosed;
			Base.UI.smethod_54(this);
			Base.UI.smethod_55(this.dataGridView1);
			this.userControlIndParam_0 = new UserControlIndParam();
			this.userControlIndParam_0.Parent = this.groupBox1;
			this.userControlIndParam_0.Dock = DockStyle.Fill;
			this.userControlIndParam_0.OnEndEdit += this.method_0;
			this.dataGridView1.Dock = DockStyle.Fill;
			this.dataGridView1.EditMode = DataGridViewEditMode.EditProgrammatically;
			this.dataGridView1.Click += this.dataGridView1_Click;
			this.dataGridView1.BackgroundColor = Color.White;
			this.dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
			this.dataGridView1.ColumnHeadersVisible = false;
			this.dataGridView1.RowHeadersVisible = false;
			this.dataGridView1.BorderStyle = BorderStyle.None;
			this.dataGridView1.DataSource = this.bindingList_0;
			this.dataGridView1.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
		}

		// Token: 0x06002085 RID: 8325 RVA: 0x0000D282 File Offset: 0x0000B482
		private void FormSetInd_FormClosed(object sender, FormClosedEventArgs e)
		{
			this.userControlIndParam_0.OnEndEdit -= this.method_0;
		}

		// Token: 0x06002086 RID: 8326 RVA: 0x000DFB28 File Offset: 0x000DDD28
		private void method_0(object sender, EventArgs e)
		{
			if (this.dataGridView1.CurrentRow != null)
			{
				int index = this.dataGridView1.CurrentRow.Index;
				IndEx indEx = this.method_1(index);
				List<UserDefineParam> showList = this.userControlIndParam_0.ShowList;
				if (!FormIndEditer.smethod_0(showList))
				{
					MessageBox.Show("参数设置错误，请检查。");
				}
				else if (indEx != null && showList != null && indEx.UDInd.UDS.UserDefineParams.Count == this.userControlIndParam_0.ShowList.Count)
				{
					indEx.UDInd.UDS.UserDefineParams = this.userControlIndParam_0.ShowList;
				}
			}
		}

		// Token: 0x06002087 RID: 8327 RVA: 0x000DFBCC File Offset: 0x000DDDCC
		private void dataGridView1_Click(object sender, EventArgs e)
		{
			if (this.dataGridView1.CurrentRow != null)
			{
				this.dataGridView1.CurrentRow.Selected = true;
				int index = this.dataGridView1.CurrentRow.Index;
				IndEx indEx = this.method_1(index);
				if (indEx == null)
				{
					this.userControlIndParam_0.method_2(null, Enum25.flag_1, UserControlIndParam.Enum27.const_1);
				}
				else if (indEx.UDInd.UDS.UserDefineParams.Any<UserDefineParam>())
				{
					this.userControlIndParam_0.method_2(indEx.UDInd.UDS.UserDefineParams, Enum25.flag_1, UserControlIndParam.Enum27.const_1);
				}
				else
				{
					this.userControlIndParam_0.method_2(null, Enum25.flag_1, UserControlIndParam.Enum27.const_1);
				}
			}
		}

		// Token: 0x06002088 RID: 8328 RVA: 0x000DFC70 File Offset: 0x000DDE70
		private IndEx method_1(int int_0)
		{
			IndEx result;
			if (int_0 < this.list_0.Count)
			{
				result = this.list_0[int_0];
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06002089 RID: 8329 RVA: 0x000041AE File Offset: 0x000023AE
		private void FormSetInd_Load(object sender, EventArgs e)
		{
		}

		// Token: 0x0600208A RID: 8330 RVA: 0x000DFCA0 File Offset: 0x000DDEA0
		public void method_2(List<IndEx> list_1)
		{
			if (list_1 != null)
			{
				this.bindingList_0.Clear();
				this.list_0 = list_1;
				foreach (IndEx indEx in this.list_0)
				{
					this.bindingList_0.Add(new NameScript(indEx.EnName, indEx.UDInd.UDS.Script));
					indEx.UDInd.method_6(indEx.UDInd.UDS.System.ICloneable.Clone() as UserDefineIndScript);
				}
				if (this.dataGridView1.ColumnCount == 2)
				{
					this.dataGridView1.Columns["Name"].AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
					this.dataGridView1.Columns["Script"].AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
				}
				if (this.dataGridView1.RowCount > 0)
				{
					this.dataGridView1.Rows[0].Selected = true;
				}
				if (this.dataGridView1.CurrentRow != null)
				{
					int index = this.dataGridView1.CurrentRow.Index;
					if (index < this.list_0.Count)
					{
						IndEx indEx2 = this.list_0[index];
						this.userControlIndParam_0.method_2(indEx2.UDInd.UDS.UserDefineParams, Enum25.flag_1, UserControlIndParam.Enum27.const_1);
						return;
					}
				}
				this.userControlIndParam_0.method_2(null, Enum25.flag_1, UserControlIndParam.Enum27.const_1);
			}
		}

		// Token: 0x170005B2 RID: 1458
		// (get) Token: 0x0600208B RID: 8331 RVA: 0x000DFE20 File Offset: 0x000DE020
		public List<UserDefineParam> UdPList
		{
			get
			{
				return this.userControlIndParam_0.ShowList.ToList<UserDefineParam>();
			}
		}

		// Token: 0x0600208C RID: 8332 RVA: 0x00004268 File Offset: 0x00002468
		private void buttonCancel_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x0600208D RID: 8333 RVA: 0x000DFE44 File Offset: 0x000DE044
		private void buttonOK_Click(object sender, EventArgs e)
		{
			foreach (IndEx indEx in this.list_0)
			{
				if (indEx.InitInd(indEx.Chart))
				{
					indEx.Chart.method_123(indEx, indEx.Chart);
				}
			}
			base.Close();
		}

		// Token: 0x0600208E RID: 8334 RVA: 0x0000D29D File Offset: 0x0000B49D
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000FFD RID: 4093
		private BindingList<NameScript> bindingList_0 = new BindingList<NameScript>();

		// Token: 0x04000FFE RID: 4094
		private UserControlIndParam userControlIndParam_0;

		// Token: 0x04000FFF RID: 4095
		private List<IndEx> list_0 = new List<IndEx>();

		// Token: 0x04001000 RID: 4096
		private IContainer icontainer_0;
	}
}
