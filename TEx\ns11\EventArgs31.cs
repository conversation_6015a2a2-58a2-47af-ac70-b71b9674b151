﻿using System;
using System.Runtime.CompilerServices;

namespace ns11
{
	// Token: 0x02000302 RID: 770
	internal sealed class EventArgs31 : EventArgs
	{
		// Token: 0x170005C1 RID: 1473
		// (get) Token: 0x06002152 RID: 8530 RVA: 0x000E44AC File Offset: 0x000E26AC
		// (set) Token: 0x06002153 RID: 8531 RVA: 0x0000D630 File Offset: 0x0000B830
		public object Obj { get; private set; }

		// Token: 0x06002154 RID: 8532 RVA: 0x0000D63B File Offset: 0x0000B83B
		public EventArgs31(object object_1)
		{
			this.Obj = object_1;
		}

		// Token: 0x04001036 RID: 4150
		[CompilerGenerated]
		private object object_0;
	}
}
