﻿using System;
using System.Drawing;
using ns5;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns14
{
	// Token: 0x020002FF RID: 767
	internal sealed class Class384 : Class381
	{
		// Token: 0x0600214A RID: 8522 RVA: 0x000E4250 File Offset: 0x000E2450
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			Ind_WaveRuler lineItem_ = zedGraphControl_0.GraphPane.AddWaveRuler(base.IndData.Name, base.DataView, color_0, SymbolType.None, this.double_0);
			base.method_3(string_0, lineItem_);
			this.int_0 = 1;
		}

		// Token: 0x0600214B RID: 8523 RVA: 0x000E42B4 File Offset: 0x000E24B4
		protected override PointPair vmethod_0(int int_1, DataArray dataArray_1)
		{
			DateTime dateTime = base.method_0(int_1);
			if (dataArray_1.Data.Length < int_1 + 1)
			{
				throw new Exception("数据长度溢出。");
			}
			double y = dataArray_1.Data[int_1];
			int num = (int)dataArray_1.OtherDataArrayList[0].Data[int_1];
			int num2 = (int)dataArray_1.OtherDataArrayList[1].Data[int_1];
			double y2 = dataArray_1.OtherDataArrayList[2].Data[int_1];
			int num3 = (int)dataArray_1.OtherDataArrayList[3].Data[int_1];
			double y3 = dataArray_1.OtherDataArrayList[4].Data[int_1];
			PointPair result;
			if (num == 1 && this.int_0 == 1)
			{
				this.int_0 = 2;
				result = new PointPair(new XDate(dateTime), y, 1.0);
			}
			else if (num2 == 1 && this.int_0 == 2)
			{
				this.int_0 = 3;
				result = new PointPair(new XDate(dateTime), y2, 2.0);
			}
			else if (num3 == 1 && this.int_0 == 3)
			{
				this.int_0 = 1;
				result = new PointPair(new XDate(dateTime), y3, 3.0);
			}
			else
			{
				result = new PointPair(new XDate(dateTime), double.NaN, double.NaN);
			}
			return result;
		}

		// Token: 0x0600214C RID: 8524 RVA: 0x0000D560 File Offset: 0x0000B760
		public Class384(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}
	}
}
