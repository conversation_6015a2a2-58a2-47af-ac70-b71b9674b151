﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns23;
using ns28;
using TEx;

namespace ns6
{
	// Token: 0x02000251 RID: 593
	[Docking(DockingBehavior.AutoDock)]
	internal class TransTabCtrl_1 : UserControl
	{
		// Token: 0x06001959 RID: 6489 RVA: 0x0000A846 File Offset: 0x00008A46
		public TransTabCtrl_1()
		{
			this.InitializeComponent();
			this.PanelHeaderFont = new Font("Microsoft Sans Serif", TApp.smethod_4(8.4f, false));
			this.Dock = DockStyle.Fill;
		}

		// Token: 0x0600195A RID: 6490 RVA: 0x0000A878 File Offset: 0x00008A78
		public TransTabCtrl_1(SplitterPanel splitterPanel_1) : this()
		{
			if (splitterPanel_1 != null)
			{
				splitterPanel_1.Controls.Add(this);
				this.ParentSplitPanel = splitterPanel_1;
			}
		}

		// Token: 0x0600195B RID: 6491 RVA: 0x0000A898 File Offset: 0x00008A98
		protected virtual void button_Max_Click(object sender, EventArgs e)
		{
			this.SetTransTabsMaximization();
		}

		// Token: 0x0600195C RID: 6492 RVA: 0x000AA8F8 File Offset: 0x000A8AF8
		public virtual void SetTransTabsMaximization()
		{
			if (!this.bool_0)
			{
				string name = "restore_small_darkgray";
				if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
				{
					name = "restore_small_white";
				}
				this.button_Max.Image = (Image)Class348.Resources.GetObject(name);
				if (Base.UI.Form.IfAutoSavePageOnExit)
				{
					Base.UI.smethod_81();
				}
			}
			else
			{
				string name2 = "max_small_darkgray";
				if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
				{
					name2 = "max_small_white";
				}
				this.button_Max.Image = (Image)Class348.Resources.GetObject(name2);
			}
			SplitterPanel splitterPanel = (SplitterPanel)base.Parent;
			this.method_0(splitterPanel);
			SplitContainer splitContainer = (SplitContainer)splitterPanel.Parent;
			while (splitContainer.Parent is SplitterPanel)
			{
				splitterPanel = (SplitterPanel)splitContainer.Parent;
				this.method_0(splitterPanel);
				splitContainer = (SplitContainer)splitterPanel.Parent;
			}
			this.bool_0 = !this.bool_0;
		}

		// Token: 0x0600195D RID: 6493 RVA: 0x000AA9E4 File Offset: 0x000A8BE4
		private void method_0(SplitterPanel splitterPanel_1)
		{
			if (splitterPanel_1 != null)
			{
				SplitContainer splitContainer = (SplitContainer)splitterPanel_1.Parent;
				if (splitterPanel_1 == splitContainer.Panel1)
				{
					if (this.bool_0)
					{
						if (splitContainer.Tag == null || (splitContainer.Tag != null && splitContainer.Tag.ToString() != "ParentSpC"))
						{
							splitContainer.Panel2Collapsed = !this.bool_0;
						}
					}
					else
					{
						splitContainer.Panel2Collapsed = !this.bool_0;
					}
				}
				else
				{
					splitContainer.Panel1Collapsed = !this.bool_0;
				}
			}
		}

		// Token: 0x0600195E RID: 6494 RVA: 0x000AAA6C File Offset: 0x000A8C6C
		public virtual void vmethod_0()
		{
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.splitContainer2.BackColor = Class179.color_3;
				this.splitContainer2.Panel1.BackColor = Class179.color_3;
				this.panelEx1.CanvasColor = Class179.color_3;
				this.panelEx1.Style.BackColor1.Color = Class179.color_2;
				this.panelEx1.Style.BackColor2.Color = Class179.color_2;
				this.panelEx1.Style.BorderColor.Color = Class179.color_2;
				this.panelEx1.Style.ForeColor.ColorSchemePart = eColorSchemePart.ItemText;
				if (this.bool_0)
				{
					this.button_Max.Image = (Image)Class348.Resources.GetObject("restore_small_white");
				}
				else
				{
					this.button_Max.Image = (Image)Class348.Resources.GetObject("max_small_white");
				}
			}
			else
			{
				this.splitContainer2.BackColor = Class179.color_9;
				this.splitContainer2.Panel1.BackColor = Class179.color_9;
				this.panelEx1.CanvasColor = Class179.color_9;
				this.panelEx1.Style.BackColor1.Color = Class179.color_10;
				this.panelEx1.Style.BackColor2.Color = Class179.color_10;
				this.panelEx1.Style.BorderColor.Color = Class179.color_9;
				this.panelEx1.Style.ForeColor.ColorSchemePart = eColorSchemePart.PanelText;
				if (this.bool_0)
				{
					this.button_Max.Image = (Image)Class348.Resources.GetObject("restore_small_darkgray");
				}
				else
				{
					this.button_Max.Image = (Image)Class348.Resources.GetObject("max_small_darkgray");
				}
			}
		}

		// Token: 0x17000435 RID: 1077
		// (get) Token: 0x0600195F RID: 6495 RVA: 0x000AAC58 File Offset: 0x000A8E58
		public bool IsMaximized
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x17000436 RID: 1078
		// (get) Token: 0x06001960 RID: 6496 RVA: 0x000AAC70 File Offset: 0x000A8E70
		// (set) Token: 0x06001961 RID: 6497 RVA: 0x0000A8A2 File Offset: 0x00008AA2
		public bool IsSwitchedBehind
		{
			get
			{
				return this.bool_1;
			}
			set
			{
				if (this.bool_1 != value)
				{
					this.bool_1 = value;
				}
			}
		}

		// Token: 0x17000437 RID: 1079
		// (get) Token: 0x06001962 RID: 6498 RVA: 0x000AAC88 File Offset: 0x000A8E88
		// (set) Token: 0x06001963 RID: 6499 RVA: 0x0000A8B6 File Offset: 0x00008AB6
		public SplitterPanel ParentSplitPanel
		{
			get
			{
				return this.splitterPanel_0;
			}
			set
			{
				this.splitterPanel_0 = value;
			}
		}

		// Token: 0x17000438 RID: 1080
		// (get) Token: 0x06001964 RID: 6500 RVA: 0x000AACA0 File Offset: 0x000A8EA0
		public SplitterPanel ContainerPanel
		{
			get
			{
				return this.splitContainer2.Panel2;
			}
		}

		// Token: 0x17000439 RID: 1081
		// (get) Token: 0x06001965 RID: 6501 RVA: 0x000AACBC File Offset: 0x000A8EBC
		// (set) Token: 0x06001966 RID: 6502 RVA: 0x0000A8C1 File Offset: 0x00008AC1
		public string PanelHeaderText
		{
			get
			{
				return this.panelEx1.Text;
			}
			set
			{
				this.panelEx1.Text = value;
			}
		}

		// Token: 0x1700043A RID: 1082
		// (get) Token: 0x06001967 RID: 6503 RVA: 0x000AACD8 File Offset: 0x000A8ED8
		// (set) Token: 0x06001968 RID: 6504 RVA: 0x0000A8D1 File Offset: 0x00008AD1
		public Font PanelHeaderFont
		{
			get
			{
				return this.panelEx1.Font;
			}
			set
			{
				this.panelEx1.Font = value;
			}
		}

		// Token: 0x1700043B RID: 1083
		// (get) Token: 0x06001969 RID: 6505 RVA: 0x000AACF4 File Offset: 0x000A8EF4
		// (set) Token: 0x0600196A RID: 6506 RVA: 0x0000A8E1 File Offset: 0x00008AE1
		public bool MaxButtonVisible
		{
			get
			{
				return this.button_Max.Visible;
			}
			set
			{
				this.button_Max.Visible = value;
			}
		}

		// Token: 0x0600196B RID: 6507 RVA: 0x0000A8F1 File Offset: 0x00008AF1
		private void splitContainer2_Resize(object sender, EventArgs e)
		{
			this.button_Max.Location = new Point(this.splitContainer2.Width - 23, 2);
		}

		// Token: 0x0600196C RID: 6508 RVA: 0x0000A914 File Offset: 0x00008B14
		protected override void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600196D RID: 6509 RVA: 0x000AAD10 File Offset: 0x000A8F10
		private void InitializeComponent()
		{
			this.splitContainer2 = new SplitContainer();
			this.panelEx1 = new PanelEx();
			this.button_Max = new Button();
			this.splitContainer2.Panel1.SuspendLayout();
			this.splitContainer2.SuspendLayout();
			this.panelEx1.SuspendLayout();
			base.SuspendLayout();
			this.panelEx1.CanvasColor = SystemColors.Control;
			this.panelEx1.ColorSchemeStyle = eDotNetBarStyle.StyleManagerControlled;
			this.panelEx1.Controls.Add(this.button_Max);
			this.panelEx1.Dock = DockStyle.Fill;
			this.panelEx1.Font = new Font("Microsoft Sans Serif", 8.4f);
			this.panelEx1.Location = new Point(0, 0);
			this.panelEx1.Margin = new System.Windows.Forms.Padding(0);
			this.panelEx1.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
			this.panelEx1.Name = "panelEx1";
			this.panelEx1.Size = new Size(606, 22);
			this.panelEx1.Style.BackColor1.Color = Color.White;
			this.panelEx1.Style.BackColor2.Color = Color.FromArgb(224, 224, 224);
			this.panelEx1.Style.BorderColor.Color = Color.Silver;
			this.panelEx1.Style.ForeColor.ColorSchemePart = eColorSchemePart.PanelText;
			this.panelEx1.Style.GradientAngle = 90;
			this.panelEx1.Style.LineAlignment = StringAlignment.Center;
			this.panelEx1.TabIndex = 1;
			this.panelEx1.Text = "";
			this.splitContainer2.FixedPanel = FixedPanel.Panel1;
			this.splitContainer2.IsSplitterFixed = true;
			this.splitContainer2.Location = new Point(0, 0);
			this.splitContainer2.MinimumSize = new Size(200, 120);
			this.splitContainer2.Name = "splitContainer2";
			this.splitContainer2.Dock = DockStyle.Fill;
			this.splitContainer2.Orientation = Orientation.Horizontal;
			this.splitContainer2.Panel1.Controls.Add(this.panelEx1);
			this.splitContainer2.Panel1MinSize = 20;
			this.splitContainer2.Size = new Size(606, 397);
			this.splitContainer2.SplitterDistance = 22;
			this.splitContainer2.SplitterWidth = 1;
			this.splitContainer2.TabIndex = 1;
			this.splitContainer2.Resize += this.splitContainer2_Resize;
			this.button_Max.BackColor = Color.Transparent;
			this.button_Max.FlatAppearance.BorderColor = Color.FromArgb(49, 106, 197);
			this.button_Max.FlatAppearance.BorderSize = 0;
			this.button_Max.FlatAppearance.MouseOverBackColor = Color.FromArgb(193, 210, 238);
			this.button_Max.FlatStyle = FlatStyle.Flat;
			this.button_Max.Image = Class372.max_small;
			this.button_Max.Location = new Point(583, 2);
			this.button_Max.Name = "button_Max";
			this.button_Max.Size = new Size(16, 16);
			this.button_Max.TabIndex = 2;
			this.button_Max.UseVisualStyleBackColor = true;
			this.button_Max.Click += this.button_Max_Click;
			base.AutoScaleDimensions = new SizeF(7f, 13f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.AutoSize = true;
			this.BackColor = Color.Transparent;
			base.Controls.Add(this.splitContainer2);
			base.Name = "TransTabCtrl";
			base.Size = new Size(609, 400);
			this.splitContainer2.Panel1.ResumeLayout(false);
			this.splitContainer2.ResumeLayout(false);
			this.panelEx1.ResumeLayout(false);
			base.ResumeLayout(false);
		}

		// Token: 0x04000CC1 RID: 3265
		private bool bool_0;

		// Token: 0x04000CC2 RID: 3266
		private SplitterPanel splitterPanel_0;

		// Token: 0x04000CC3 RID: 3267
		private bool bool_1;

		// Token: 0x04000CC4 RID: 3268
		private IContainer icontainer_0;

		// Token: 0x04000CC5 RID: 3269
		private SplitContainer splitContainer2;

		// Token: 0x04000CC6 RID: 3270
		private PanelEx panelEx1;

		// Token: 0x04000CC7 RID: 3271
		private Button button_Max;
	}
}
