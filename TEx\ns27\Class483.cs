﻿using System;
using ns4;
using TEx.ImportTrans.Captcha;

namespace ns27
{
	// Token: 0x0200037F RID: 895
	internal static class Class483
	{
		// Token: 0x060024FE RID: 9470 RVA: 0x000F68E4 File Offset: 0x000F4AE4
		public static Class482 smethod_0(string string_0)
		{
			Class482 result;
			if (string_0 == "beyes")
			{
				result = new NaiveBeyes(25);
			}
			else
			{
				if (!(string_0 == "leven"))
				{
					throw new Exception("没有这个分类器");
				}
				result = new LevenClassifer();
			}
			return result;
		}
	}
}
