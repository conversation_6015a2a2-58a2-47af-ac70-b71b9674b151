﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000077 RID: 119
	[Serializable]
	internal class DrawLineH : DrawObj, ISerializable
	{
		// Token: 0x06000442 RID: 1090 RVA: 0x000037AA File Offset: 0x000019AA
		public DrawLineH()
		{
		}

		// Token: 0x06000443 RID: 1091 RVA: 0x00003D85 File Offset: 0x00001F85
		public DrawLineH(ChartCS chart, double x1, double y1, double x2, double y2) : this(chart, x1, y1, x2, y2, false)
		{
		}

		// Token: 0x06000444 RID: 1092 RVA: 0x00003D95 File Offset: 0x00001F95
		public DrawLineH(ChartCS chart, double x1, double y1, double x2, double y2, bool disableRstPrc) : base(chart, x1, y1, x2, y2, disableRstPrc)
		{
			base.Name = "水平线";
			base.CanChgColor = true;
			base.IsOneClickLoc = true;
		}

		// Token: 0x06000445 RID: 1093 RVA: 0x000037DC File Offset: 0x000019DC
		protected DrawLineH(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x06000446 RID: 1094 RVA: 0x000037ED File Offset: 0x000019ED
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x06000447 RID: 1095 RVA: 0x00022FDC File Offset: 0x000211DC
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			LineObj item = this.vmethod_24(chartCS_1, double_1, double_3, double_2, double_4, string_5);
			list.Add(item);
			this.lineObj_0 = item;
			return list;
		}

		// Token: 0x06000448 RID: 1096 RVA: 0x00023010 File Offset: 0x00021210
		protected virtual LineObj vmethod_24(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			double min = chartCS_1.GraphPane.XAxis.Scale.Min;
			double max = chartCS_1.GraphPane.XAxis.Scale.Max;
			return base.method_23(min, double_4, max, double_4, base.Tag);
		}

		// Token: 0x06000449 RID: 1097 RVA: 0x000037F9 File Offset: 0x000019F9
		public override void vmethod_17(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4)
		{
			base.vmethod_17(chartCS_1, double_3, double_4, double_3, double_4);
		}

		// Token: 0x0600044A RID: 1098 RVA: 0x0000380C File Offset: 0x00001A0C
		public override void vmethod_18(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, bool bool_6, bool bool_7)
		{
			base.vmethod_18(chartCS_1, double_3, double_4, double_3, double_4, bool_6, bool_7);
		}

		// Token: 0x0600044B RID: 1099 RVA: 0x00023060 File Offset: 0x00021260
		protected override bool vmethod_11(ChartCS chartCS_1, int? nullable_4, int? nullable_5)
		{
			return true;
		}

		// Token: 0x170000DD RID: 221
		// (get) Token: 0x0600044C RID: 1100 RVA: 0x00023074 File Offset: 0x00021274
		public LineObj Line
		{
			get
			{
				return this.lineObj_0;
			}
		}

		// Token: 0x04000153 RID: 339
		private LineObj lineObj_0;
	}
}
