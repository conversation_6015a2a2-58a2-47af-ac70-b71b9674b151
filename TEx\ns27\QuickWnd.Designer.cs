﻿namespace ns27
{
	// Token: 0x020001F3 RID: 499
	internal sealed partial class QuickWnd : global::System.Windows.Forms.Form
	{
		// Token: 0x060013A8 RID: 5032 RVA: 0x0008408C File Offset: 0x0008228C
		private void InitializeComponent()
		{
			this.textBox = new global::System.Windows.Forms.TextBox();
			this.dataGridView = new global::System.Windows.Forms.DataGridView();
			((global::System.ComponentModel.ISupportInitialize)this.dataGridView).BeginInit();
			base.SuspendLayout();
			this.textBox.Location = new global::System.Drawing.Point(-2, 1);
			this.textBox.Name = "textBox";
			this.textBox.Size = new global::System.Drawing.Size(230, 25);
			this.textBox.TabIndex = 0;
			this.dataGridView.BackgroundColor = global::System.Drawing.SystemColors.Window;
			this.dataGridView.BorderStyle = global::System.Windows.Forms.BorderStyle.None;
			this.dataGridView.ColumnHeadersHeightSizeMode = global::System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.dataGridView.Location = new global::System.Drawing.Point(0, 28);
			this.dataGridView.Name = "dataGridView";
			this.dataGridView.RowTemplate.Height = 27;
			this.dataGridView.Size = new global::System.Drawing.Size(228, 182);
			this.dataGridView.TabIndex = 1;
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(8f, 15f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new global::System.Drawing.Size(229, 211);
			base.Controls.Add(this.dataGridView);
			base.Controls.Add(this.textBox);
			base.FormBorderStyle = global::System.Windows.Forms.FormBorderStyle.FixedToolWindow;
			base.Name = "QuickWnd";
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			this.Text = "快捷键盘";
			((global::System.ComponentModel.ISupportInitialize)this.dataGridView).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000A3A RID: 2618
		private global::System.Windows.Forms.TextBox textBox;

		// Token: 0x04000A3B RID: 2619
		private global::System.Windows.Forms.DataGridView dataGridView;
	}
}
