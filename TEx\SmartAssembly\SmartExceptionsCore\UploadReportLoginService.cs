﻿using System;
using System.Net;
using System.Web.Services;
using System.Web.Services.Protocols;
using ns32;

namespace SmartAssembly.SmartExceptionsCore
{
	// Token: 0x02000407 RID: 1031
	[WebServiceBinding(Name = "LoginServiceSoap", Namespace = "http://www.smartassembly.com/webservices/UploadReportLogin/")]
	internal sealed class UploadReportLoginService : SoapHttpClientProtocol
	{
		// Token: 0x06002803 RID: 10243 RVA: 0x0000F74F File Offset: 0x0000D94F
		public UploadReportLoginService()
		{
			base.Url = Class542.string_0 + "UploadReportLogin.asmx";
			base.Timeout = 30000;
		}

		// Token: 0x06002804 RID: 10244 RVA: 0x0000F777 File Offset: 0x0000D977
		[SoapDocumentMethod("http://www.smartassembly.com/webservices/UploadReportLogin/GetServerURL")]
		public string GetServerURL(string licenseID)
		{
			return (string)base.Invoke("GetServerURL", new object[]
			{
				licenseID
			})[0];
		}

		// Token: 0x06002805 RID: 10245 RVA: 0x00103354 File Offset: 0x00101554
		protected override WebRequest GetWebRequest(Uri uri)
		{
			WebRequest webRequest = base.GetWebRequest(uri);
			HttpWebRequest httpWebRequest = webRequest as HttpWebRequest;
			if (httpWebRequest != null)
			{
				httpWebRequest.ServicePoint.Expect100Continue = false;
			}
			return webRequest;
		}
	}
}
