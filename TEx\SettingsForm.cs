﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns11;
using ns28;
using ns5;
using TEx.Trading;

namespace TEx
{
	// Token: 0x020001A9 RID: 425
	public sealed partial class SettingsForm : Form
	{
		// Token: 0x1400007B RID: 123
		// (add) Token: 0x06001060 RID: 4192 RVA: 0x0006AE78 File Offset: 0x00069078
		// (remove) Token: 0x06001061 RID: 4193 RVA: 0x0006AEB0 File Offset: 0x000690B0
		public event EventHandler UISettingsConfirmed
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06001062 RID: 4194 RVA: 0x00006F7F File Offset: 0x0000517F
		protected void method_0()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x06001063 RID: 4195 RVA: 0x00006F9A File Offset: 0x0000519A
		public SettingsForm()
		{
			this.InitializeComponent();
		}

		// Token: 0x06001064 RID: 4196 RVA: 0x0006AEE8 File Offset: 0x000690E8
		private void SettingsForm_Load(object sender, EventArgs e)
		{
			this.checkBox_AlwaysShowTransNoteBox.Checked = Base.UI.Form.IfAlwaysShowTransNoteBox;
			this.checkBox_AlwaysShowTransNoteBox.CheckedChanged += this.checkBox_AlwaysShowTransNoteBox_CheckedChanged;
			this.method_14();
			this.checkBox_OnlyShowUserNoteBox.Checked = !Base.UI.Form.IfShowAllNotesWhenAlwaysShowTransNoteBox;
			this.checkBox_ShowTransNotesBorder.Checked = !Base.UI.Form.IfHideTransNoteBorder;
			this.checkBox_TransNotesFillTransparant.Checked = Base.UI.Form.IfTransNoteFillTransparent;
			this.checkBox_PlayOCSound.Checked = !Base.UI.Form.IfDisableOpenCloseSound;
			this.checkBox_ShowTOdrLine.Checked = !Base.UI.Form.IfDisableTsOdrLine;
			this.checkBox_AutoShowCurrTransTab.Checked = !Base.UI.Form.IfDisableAutoShowCurrTransTab;
			this.chkBox_IfFollowPrcInTradingInput.Checked = Base.UI.Form.IfFollowPrcInTradingTab;
			this.checkBox_ShowHiLowMarks.Checked = !Base.UI.Form.NoShowHighLowMark;
			this.comboBox_arrwType.SelectedIndex = 0;
			if (Base.UI.Form.TransArrowType == TransArrowType.BigOblique)
			{
				this.comboBox_arrwType.SelectedIndex = 0;
				this.picBox_ArrwType.Image = Class372.BigArrwSamples;
			}
			else if (Base.UI.Form.TransArrowType == TransArrowType.SmallUpDn)
			{
				this.comboBox_arrwType.SelectedIndex = 1;
				this.picBox_ArrwType.Image = Class372.SmallArrwSample;
			}
			this.comboBox_arrwType.SelectedIndexChanged += this.comboBox_arrwType_SelectedIndexChanged;
			if (Base.UI.Form.IfShowDayOfWeek)
			{
				this.chkBox_IfShowDayOfWeek.Checked = true;
			}
			this.chkBox_ifDispDayDivLine.Checked = Base.UI.Form.IfDispDayDivLine;
			this.cmbBx_DDlineChtMins.Items.Add("1分钟");
			this.cmbBx_DDlineChtMins.Items.Add("3分钟");
			this.cmbBx_DDlineChtMins.Items.Add("5分钟");
			this.cmbBx_DDlineChtMins.Items.Add("15分钟");
			this.cmbBx_DDlineChtMins.Items.Add("30分钟");
			this.cmbBx_DDlineChtMins.Items.Add("1小时");
			this.cmbBx_DDlineChtMins.SelectedItem = this.method_16(Base.UI.Form.PeriodOfChartDispDayDivLine);
			this.method_12();
			this.chkBox_pauseAtDayEnd.Checked = Base.UI.Form.IfPauseAtDayEnd;
			this.chkBx_savePage.Checked = Base.UI.Form.IfAutoSavePageOnExit;
			this.chkBx_saveSpeed.Checked = Base.UI.Form.IfSaveSpeedOnQuit;
			this.chkBx_saveWindow.Checked = Base.UI.Form.IfSaveWindowOnQuit;
			this.chkBx_confQuit.Checked = Base.UI.Form.IfConfirmQuit;
			this.checkBox_ShowTransArrow.Checked = !Base.UI.Form.IfShowNoTransArrow;
			this.checkBox_ShowTransArrow.CheckedChanged += this.checkBox_ShowTransArrow_CheckedChanged;
			this.method_13();
			if (!Base.UI.Form.IfShowAllTransArrow)
			{
				this.radioBtn_CurrTransArrow.Checked = true;
			}
			else
			{
				this.radioBtn_AllTransArrow.Checked = true;
			}
			this.cmbBx_PeriodForTransArrow.Items.Add("1分钟");
			this.cmbBx_PeriodForTransArrow.Items.Add("3分钟");
			this.cmbBx_PeriodForTransArrow.Items.Add("5分钟");
			this.cmbBx_PeriodForTransArrow.Items.Add("15分钟");
			this.cmbBx_PeriodForTransArrow.Items.Add("30分钟");
			this.cmbBx_PeriodForTransArrow.Items.Add("1小时");
			this.cmbBx_PeriodForTransArrow.Items.Add("2小时");
			this.cmbBx_PeriodForTransArrow.Items.Add("日线");
			this.cmbBx_PeriodForTransArrow.Items.Add("周线");
			this.cmbBx_PeriodForTransArrow.Items.Add("月线");
			TimeUnit timeUnit = Base.UI.Form.PeriodOfChartDispTransArrow;
			if (timeUnit == (TimeUnit)0)
			{
				timeUnit = TimeUnit.Day;
			}
			this.cmbBx_PeriodForTransArrow.SelectedItem = this.method_16(timeUnit);
			if (Base.UI.Form.IfSymbSwitchShowCurrDT)
			{
				this.radioBtn_SymbSwitchCurrDt.Checked = true;
			}
			else
			{
				this.radioBtn_SymbSwitchLastDt.Checked = true;
			}
			if (!Base.UI.Form.IfNotShowAcctInfoOnTransTabHeader)
			{
				this.chkBox_IfShowAcctInfoOnTransTabHeader.Checked = true;
			}
			if (Base.UI.Form.IfShowSymbCNNameInOpenTransGridView)
			{
				this.radioBtn_ShowSymbCNName.Checked = true;
			}
			else
			{
				this.radioBtn_ShowSymbCode.Checked = true;
			}
			if (Base.UI.CurrTradingSymbol != null)
			{
				this.stkSymbol_0 = Base.UI.CurrTradingSymbol;
			}
			else if (this.stkSymbol_0 == null)
			{
				this.stkSymbol_0 = Base.UI.CurrSymbol;
			}
			if (this.stkSymbol_0 != null)
			{
				this.groupBox_自动盈损.Text = "自动盈损";
			}
			if (Base.UI.Form.IsInBlindTestMode)
			{
				GroupBox groupBox = this.groupBox_自动盈损;
				groupBox.Text += "（当前品种）";
			}
			else
			{
				GroupBox groupBox2 = this.groupBox_自动盈损;
				groupBox2.Text = groupBox2.Text + "（品种：" + this.stkSymbol_0.MstSymbol.AbbrCNName + "）";
			}
			this.method_8();
			ToolTip toolTip = new ToolTip();
			string caption = "点击链接设置点数";
			toolTip.SetToolTip(this.linkLabel_AutoStopPt, caption);
			toolTip.SetToolTip(this.linkLabel_AutoPrftPt, caption);
			this.linkLabel_AutoStopPt.LinkClicked += this.linkLabel_AutoStopPt_LinkClicked;
			this.linkLabel_AutoPrftPt.LinkClicked += this.linkLabel_AutoPrftPt_LinkClicked;
			if (this.stkSymbol_0 != null)
			{
				AcctSymbol acctSymbol = Base.Acct.smethod_30(this.stkSymbol_0.ID);
				if (this.stkSymbol_0.AutoStopLossPoints != null)
				{
					this.chkBox_AutoStop.Checked = acctSymbol.IfAutoStopLoss;
				}
				else
				{
					this.chkBox_AutoStop.Checked = false;
				}
				if (this.stkSymbol_0.AutoLimitTakePoints != null)
				{
					this.chkBox_AutoPrft.Checked = acctSymbol.IfAutoLimitTake;
				}
				else
				{
					this.chkBox_AutoPrft.Checked = false;
				}
			}
			this.chkBox_AutoStop.CheckedChanged += this.chkBox_AutoStop_CheckedChanged;
			this.chkBox_AutoPrft.CheckedChanged += this.chkBox_AutoPrft_CheckedChanged;
			this.chkBox_ConfOrdr.Checked = Base.UI.Form.IsOrderConfmNeeded;
			this.chkBox_ShortForStock.Checked = Base.UI.Form.EnableShortForStock;
			this.chkBox_T0ForStock.Checked = Base.UI.Form.EnableT0ForStock;
			this.chkBox_ConfDblClickClsTrans.Checked = Base.UI.Form.IfNoConfClsTransWhenDblClick;
			this.chkBox_SyncToolbarTradingTabPriceUnits.Checked = !Base.UI.Form.IfNoSyncToolBarAndTradingTabPriceUnits;
			this.chkBox_IfAutoCancelOpenTransOrder.Checked = !Base.UI.Form.IfNotAutoCancelExistingOpenTransOrder;
			this.chkBox_IfAutoCancelCloseTransOrder.Checked = !Base.UI.Form.IfNotAutoCancelExistingCloseTransOrder;
			if (Base.UI.Form.IfCloseNewTransFirst)
			{
				this.radioBtn_ClsOrd_NewFirst.Checked = true;
			}
			else
			{
				this.radioBtn_ClsOrd_OldFirst.Checked = true;
			}
			string caption2 = "此处设定未指定仓单时的系统默认平仓次序。" + Environment.NewLine + "一般交易软件默认先平老仓。";
			ToolTip toolTip2 = new ToolTip();
			toolTip2.ToolTipIcon = ToolTipIcon.Info;
			toolTip2.ToolTipTitle = "平仓次序说明";
			toolTip2.SetToolTip(this.groupBox_ClsOdr, caption2);
			toolTip2.SetToolTip(this.radioBtn_ClsOrd_NewFirst, caption2);
			toolTip2.SetToolTip(this.radioBtn_ClsOrd_OldFirst, caption2);
			if (Base.UI.Form.StockRestorationMethod != null && Base.UI.Form.StockRestorationMethod.Value != StockRestorationMethod.Prior)
			{
				if (Base.UI.Form.StockRestorationMethod != null && Base.UI.Form.StockRestorationMethod.Value == StockRestorationMethod.Later)
				{
					this.comboBox_StkRstMethod.SelectedIndex = 1;
				}
				else
				{
					this.comboBox_StkRstMethod.SelectedIndex = 2;
				}
			}
			else
			{
				this.comboBox_StkRstMethod.SelectedIndex = 0;
			}
			if (Base.UI.Form.IfNoBonusShare)
			{
				this.comboBox_BonusShareTreatment.SelectedIndex = 1;
			}
			else
			{
				this.comboBox_BonusShareTreatment.SelectedIndex = 0;
			}
			if (Base.UI.Form.IfNoDivident)
			{
				this.comboBox_DividentTreatment.SelectedIndex = 1;
			}
			else
			{
				this.comboBox_DividentTreatment.SelectedIndex = 0;
			}
			if (Base.UI.Form.RationedShareTreatmt != null)
			{
				if (Base.UI.Form.RationedShareTreatmt.Value != RationedShareTreatmt.Prompt)
				{
					if (Base.UI.Form.RationedShareTreatmt != null && Base.UI.Form.RationedShareTreatmt.Value == RationedShareTreatmt.Auto)
					{
						this.comboBox_RationedShareTreatmt.SelectedIndex = 0;
						goto IL_85B;
					}
					this.comboBox_RationedShareTreatmt.SelectedIndex = 2;
					goto IL_85B;
				}
			}
			this.comboBox_RationedShareTreatmt.SelectedIndex = 1;
			IL_85B:
			if (Base.UI.Form.IfROpenFixedAmt)
			{
				this.radioBtn_ROpenFixAmt.Checked = true;
				this.numUpDown_ROpenRatio.Enabled = false;
			}
			else
			{
				this.radioBtn_ROpenRatio.Checked = true;
				this.txtBox_ROpenRAmt.Enabled = false;
			}
			if (Base.UI.Form.ROpenRatio != null)
			{
				this.numUpDown_ROpenRatio.Value = Base.UI.Form.ROpenRatio.Value;
			}
			else
			{
				this.numUpDown_ROpenRatio.Value = 3m;
			}
			if (Base.UI.Form.ROpenFixedAmt != null)
			{
				this.txtBox_ROpenRAmt.Text = Base.UI.Form.ROpenFixedAmt.Value.ToString();
			}
			else
			{
				this.txtBox_ROpenRAmt.Text = Base.Trading.smethod_213().ToString();
			}
			if (!Base.UI.Form.IfROpenNoShowCnfmDlg)
			{
				this.chkBox_ROpenShowCnfmDlg.Checked = true;
			}
			if (Base.UI.Form.KLineType != null && Base.UI.Form.KLineType.Value != KLineType.CandleStick_EmptyUpK)
			{
				if (Base.UI.Form.KLineType.Value == KLineType.CandleStick_SolidUpK)
				{
					this.comboBox_BarTypes.SelectedIndex = 1;
				}
				else if (Base.UI.Form.KLineType.Value == KLineType.OHLCBar)
				{
					this.comboBox_BarTypes.SelectedIndex = 2;
				}
				else
				{
					this.comboBox_BarTypes.SelectedIndex = 3;
				}
			}
			else
			{
				this.comboBox_BarTypes.SelectedIndex = 0;
			}
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.comboBox_ChartThemes.SelectedIndex = 0;
			}
			else if (Base.UI.Form.ChartTheme == ChartTheme.Yellow)
			{
				this.comboBox_ChartThemes.SelectedIndex = 1;
			}
			else
			{
				this.comboBox_ChartThemes.SelectedIndex = 2;
			}
			this.method_1();
			this.comboBox_BarTypes.SelectedIndexChanged += this.comboBox_BarTypes_SelectedIndexChanged;
			this.comboBox_ChartThemes.SelectedIndexChanged += this.comboBox_ChartThemes_SelectedIndexChanged;
			this.txtBox_ROpenRAmt.KeyPress += this.txtBox_ROpenRAmt_KeyPress;
			this.radioBtn_ROpenRatio.CheckedChanged += this.radioBtn_ROpenRatio_CheckedChanged;
			this.tabControl_Settings.Controls.Remove(this.tabPage_BackupSettings);
		}

		// Token: 0x06001065 RID: 4197 RVA: 0x00006FAA File Offset: 0x000051AA
		private void comboBox_ChartThemes_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.method_1();
		}

		// Token: 0x06001066 RID: 4198 RVA: 0x00006FAA File Offset: 0x000051AA
		private void comboBox_BarTypes_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.method_1();
		}

		// Token: 0x06001067 RID: 4199 RVA: 0x0006B98C File Offset: 0x00069B8C
		private void method_1()
		{
			if (this.comboBox_BarTypes.SelectedIndex == 0)
			{
				if (this.comboBox_ChartThemes.SelectedIndex == 0)
				{
					this.picBox_theme.Image = Class372.theme_1_1;
				}
				else if (this.comboBox_ChartThemes.SelectedIndex == 1)
				{
					this.picBox_theme.Image = Class372.theme_1_2;
				}
				else
				{
					this.picBox_theme.Image = Class372.theme_1_3;
				}
			}
			else if (this.comboBox_BarTypes.SelectedIndex == 1)
			{
				if (this.comboBox_ChartThemes.SelectedIndex == 0)
				{
					this.picBox_theme.Image = Class372.theme_2_1;
				}
				else if (this.comboBox_ChartThemes.SelectedIndex == 1)
				{
					this.picBox_theme.Image = Class372.theme_2_2;
				}
				else
				{
					this.picBox_theme.Image = Class372.theme_2_3;
				}
			}
			else if (this.comboBox_BarTypes.SelectedIndex == 2)
			{
				if (this.comboBox_ChartThemes.SelectedIndex == 0)
				{
					this.picBox_theme.Image = Class372.theme_3_1;
				}
				else if (this.comboBox_ChartThemes.SelectedIndex == 1)
				{
					this.picBox_theme.Image = Class372.theme_3_2;
				}
				else
				{
					this.picBox_theme.Image = Class372.theme_3_3;
				}
			}
			else if (this.comboBox_ChartThemes.SelectedIndex == 0)
			{
				this.picBox_theme.Image = Class372.theme_4_1;
			}
			else if (this.comboBox_ChartThemes.SelectedIndex == 1)
			{
				this.picBox_theme.Image = Class372.theme_4_2;
			}
			else
			{
				this.picBox_theme.Image = Class372.theme_4_3;
			}
		}

		// Token: 0x06001068 RID: 4200 RVA: 0x0006BB1C File Offset: 0x00069D1C
		private void comboBox_arrwType_SelectedIndexChanged(object sender, EventArgs e)
		{
			if (this.comboBox_arrwType.SelectedIndex == 0)
			{
				this.picBox_ArrwType.Image = Class372.BigArrwSamples;
			}
			else if (this.comboBox_arrwType.SelectedIndex == 1)
			{
				this.picBox_ArrwType.Image = Class372.SmallArrwSample;
			}
		}

		// Token: 0x06001069 RID: 4201 RVA: 0x0006BB68 File Offset: 0x00069D68
		private void radioBtn_ROpenRatio_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioBtn_ROpenRatio.Checked)
			{
				this.numUpDown_ROpenRatio.Enabled = true;
				this.txtBox_ROpenRAmt.Enabled = false;
			}
			else
			{
				this.numUpDown_ROpenRatio.Enabled = false;
				this.txtBox_ROpenRAmt.Enabled = true;
			}
		}

		// Token: 0x0600106A RID: 4202 RVA: 0x00006FB4 File Offset: 0x000051B4
		private void method_2(object sender, EventArgs e)
		{
			if (TApp.IsTrialUser)
			{
				this.method_4();
			}
			else if (!BkupSyncMgr.smethod_5(null, new bool?(false), false))
			{
				this.method_5();
			}
		}

		// Token: 0x0600106B RID: 4203 RVA: 0x00006FDC File Offset: 0x000051DC
		private void method_3(object sender, EventArgs e)
		{
			if (TApp.IsTrialUser)
			{
				this.method_4();
			}
			else if (!BkupSyncMgr.smethod_5(null, new bool?(true), false))
			{
				this.method_5();
			}
		}

		// Token: 0x0600106C RID: 4204 RVA: 0x00007004 File Offset: 0x00005204
		private void method_4()
		{
			MessageBox.Show("免费版不支持云备份同步。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
		}

		// Token: 0x0600106D RID: 4205 RVA: 0x0000701B File Offset: 0x0000521B
		private void method_5()
		{
			MessageBox.Show("已与云端同步，无需重复执行。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
		}

		// Token: 0x0600106E RID: 4206 RVA: 0x0006BBB8 File Offset: 0x00069DB8
		private void method_6(object sender, EventArgs e)
		{
			CheckBox checkBox = sender as CheckBox;
			if (TApp.IsTrialUser)
			{
				if (checkBox.Checked)
				{
					checkBox.Checked = false;
					MessageBox.Show("免费版不支持云备份同步。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
				}
			}
			else if (this.groupBox_SyncTime.Controls.OfType<CheckBox>().Count(new Func<CheckBox, bool>(SettingsForm.<>c.<>9.method_0)) == 0)
			{
				this.groupBox_SyncDirection.Enabled = false;
			}
			else
			{
				this.groupBox_SyncDirection.Enabled = true;
			}
		}

		// Token: 0x0600106F RID: 4207 RVA: 0x00007032 File Offset: 0x00005232
		private void method_7(object sender, EventArgs e)
		{
			if (this.chkBox_BackupTimePeriodically.Checked)
			{
				this.comboBox_BackupPeriod.Enabled = true;
			}
			else
			{
				this.comboBox_BackupPeriod.Enabled = false;
			}
		}

		// Token: 0x06001070 RID: 4208 RVA: 0x0006BC4C File Offset: 0x00069E4C
		private void method_8()
		{
			this.linkLabel_AutoStopPt.Text = "（点数：";
			string text = "未设置";
			if (this.stkSymbol_0 != null && this.stkSymbol_0.AutoStopLossPoints != null)
			{
				text = this.stkSymbol_0.AutoStopLossPoints.Value.ToString();
			}
			LinkLabel linkLabel = this.linkLabel_AutoStopPt;
			linkLabel.Text = linkLabel.Text + text + "）";
			this.linkLabel_AutoStopPt.LinkArea = new LinkArea(4, text.Length);
			this.linkLabel_AutoPrftPt.Text = "（点数：";
			string text2 = "未设置";
			if (this.stkSymbol_0 != null && this.stkSymbol_0.AutoLimitTakePoints != null)
			{
				text2 = this.stkSymbol_0.AutoLimitTakePoints.Value.ToString();
			}
			LinkLabel linkLabel2 = this.linkLabel_AutoPrftPt;
			linkLabel2.Text = linkLabel2.Text + text2 + "）";
			this.linkLabel_AutoPrftPt.LinkArea = new LinkArea(4, text2.Length);
		}

		// Token: 0x06001071 RID: 4209 RVA: 0x0000705D File Offset: 0x0000525D
		private void linkLabel_AutoStopPt_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			if (this.stkSymbol_0 != null)
			{
				this.method_9(false, false, true, false);
			}
		}

		// Token: 0x06001072 RID: 4210 RVA: 0x00007073 File Offset: 0x00005273
		private void linkLabel_AutoPrftPt_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
		{
			if (this.stkSymbol_0 != null)
			{
				this.method_9(false, false, false, true);
			}
		}

		// Token: 0x06001073 RID: 4211 RVA: 0x0006BD60 File Offset: 0x00069F60
		private void chkBox_AutoStop_CheckedChanged(object sender, EventArgs e)
		{
			if (this.chkBox_AutoStop.Checked && this.stkSymbol_0 != null && this.stkSymbol_0.AutoStopLossPoints == null)
			{
				this.chkBox_AutoStop.Checked = false;
				if (MessageBox.Show("自动止损点数尚未设置，自动止损无法生效。现在设置吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
				{
					this.method_9(true, false, true, false);
				}
			}
		}

		// Token: 0x06001074 RID: 4212 RVA: 0x0006BDC4 File Offset: 0x00069FC4
		private void chkBox_AutoPrft_CheckedChanged(object sender, EventArgs e)
		{
			if (this.chkBox_AutoPrft.Checked)
			{
				StkSymbol currSymbol = Base.UI.CurrSymbol;
				if (currSymbol != null && currSymbol.AutoLimitTakePoints == null)
				{
					this.chkBox_AutoPrft.Checked = false;
					if (MessageBox.Show("自动止盈点数尚未设置，自动止盈无法生效。现在设置吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
					{
						this.method_9(false, true, false, true);
					}
				}
			}
		}

		// Token: 0x06001075 RID: 4213 RVA: 0x0006BE24 File Offset: 0x0006A024
		private void method_9(bool bool_2, bool bool_3, bool bool_4, bool bool_5)
		{
			SetSymbParamForm setSymbParamForm = new SetSymbParamForm(new List<StkSymbol>
			{
				this.stkSymbol_0
			}, bool_2, bool_3, true);
			setSymbParamForm.SymbParamsUpdated += this.method_10;
			setSymbParamForm.Owner = this;
			if (bool_4)
			{
				setSymbParamForm.SymbParamCtrl.IfFocusOnAutoStopOnStartup = true;
			}
			else if (bool_5)
			{
				setSymbParamForm.SymbParamCtrl.IfFocusOnAutoLimitOnStartup = true;
			}
			setSymbParamForm.ShowDialog();
		}

		// Token: 0x06001076 RID: 4214 RVA: 0x0006BE90 File Offset: 0x0006A090
		private void method_10(object sender, EventArgs21 e)
		{
			if (this.stkSymbol_0 != null && this.stkSymbol_0.AutoStopLossPoints != null)
			{
				this.method_8();
				if (e.IfTurnOnAutoStop)
				{
					this.chkBox_AutoStop.Checked = true;
				}
			}
			else if (this.stkSymbol_0 != null && this.stkSymbol_0.AutoLimitTakePoints != null)
			{
				this.method_8();
				if (e.IfTurnOnAutoLimit)
				{
					this.chkBox_AutoPrft.Checked = true;
				}
			}
		}

		// Token: 0x06001077 RID: 4215 RVA: 0x0006BF10 File Offset: 0x0006A110
		private void button_OK_Click(object sender, EventArgs e)
		{
			Base.UI.Form.IfDispDayDivLine = this.chkBox_ifDispDayDivLine.Checked;
			Base.UI.Form.PeriodOfChartDispDayDivLine = this.method_15(this.cmbBx_DDlineChtMins.SelectedItem as string);
			Base.UI.Form.IfPauseAtDayEnd = this.chkBox_pauseAtDayEnd.Checked;
			Base.UI.Form.IfAutoSavePageOnExit = this.chkBx_savePage.Checked;
			Base.UI.Form.IfSaveSpeedOnQuit = this.chkBx_saveSpeed.Checked;
			Base.UI.Form.IfSaveWindowOnQuit = this.chkBx_saveWindow.Checked;
			Base.UI.Form.IfConfirmQuit = this.chkBx_confQuit.Checked;
			Base.UI.Form.IfSymbSwitchShowCurrDT = this.radioBtn_SymbSwitchCurrDt.Checked;
			if (this.checkBox_ShowTransArrow.Checked)
			{
				Base.UI.Form.IfShowNoTransArrow = false;
				if (this.radioBtn_CurrTransArrow.Checked)
				{
					Base.UI.Form.IfShowAllTransArrow = false;
				}
				else
				{
					Base.UI.Form.IfShowAllTransArrow = true;
				}
			}
			else
			{
				Base.UI.Form.IfShowNoTransArrow = true;
			}
			Base.UI.Form.PeriodOfChartDispTransArrow = this.method_15(this.cmbBx_PeriodForTransArrow.SelectedItem as string);
			Base.UI.Form.IfAlwaysShowTransNoteBox = this.checkBox_AlwaysShowTransNoteBox.Checked;
			Base.UI.Form.IfShowAllNotesWhenAlwaysShowTransNoteBox = !this.checkBox_OnlyShowUserNoteBox.Checked;
			Base.UI.Form.IfHideTransNoteBorder = !this.checkBox_ShowTransNotesBorder.Checked;
			Base.UI.Form.IfTransNoteFillTransparent = this.checkBox_TransNotesFillTransparant.Checked;
			Base.UI.Form.IfDisableOpenCloseSound = !this.checkBox_PlayOCSound.Checked;
			Base.UI.Form.IfDisableTsOdrLine = !this.checkBox_ShowTOdrLine.Checked;
			Base.UI.Form.IfDisableAutoShowCurrTransTab = !this.checkBox_AutoShowCurrTransTab.Checked;
			Base.UI.Form.IfShowSymbCNNameInOpenTransGridView = this.radioBtn_ShowSymbCNName.Checked;
			Base.UI.Form.IfShowDayOfWeek = this.chkBox_IfShowDayOfWeek.Checked;
			Base.UI.Form.IfNotShowAcctInfoOnTransTabHeader = !this.chkBox_IfShowAcctInfoOnTransTabHeader.Checked;
			Base.UI.Form.NoShowHighLowMark = !this.checkBox_ShowHiLowMarks.Checked;
			if (this.comboBox_arrwType.SelectedIndex == 0)
			{
				Base.UI.Form.TransArrowType = TransArrowType.BigOblique;
			}
			else if (this.comboBox_arrwType.SelectedIndex == 1)
			{
				Base.UI.Form.TransArrowType = TransArrowType.SmallUpDn;
			}
			if (this.comboBox_BarTypes.SelectedIndex == 0)
			{
				if (Base.UI.Form.KLineType != null && Base.UI.Form.KLineType.Value != KLineType.CandleStick_EmptyUpK)
				{
					Base.UI.Form.KLineType = new KLineType?(KLineType.CandleStick_EmptyUpK);
					this.bool_0 = true;
				}
			}
			else if (this.comboBox_BarTypes.SelectedIndex == 1)
			{
				KLineType? klineType = Base.UI.Form.KLineType;
				if (!(klineType.GetValueOrDefault() == KLineType.CandleStick_SolidUpK & klineType != null))
				{
					Base.UI.Form.KLineType = new KLineType?(KLineType.CandleStick_SolidUpK);
					this.bool_0 = true;
				}
			}
			else if (this.comboBox_BarTypes.SelectedIndex == 2)
			{
				KLineType? klineType = Base.UI.Form.KLineType;
				if (!(klineType.GetValueOrDefault() == KLineType.OHLCBar & klineType != null))
				{
					Base.UI.Form.KLineType = new KLineType?(KLineType.OHLCBar);
					this.bool_0 = true;
				}
			}
			else if (this.comboBox_BarTypes.SelectedIndex == 3)
			{
				KLineType? klineType = Base.UI.Form.KLineType;
				if (!(klineType.GetValueOrDefault() == KLineType.HLCBar & klineType != null))
				{
					Base.UI.Form.KLineType = new KLineType?(KLineType.HLCBar);
					this.bool_0 = true;
				}
			}
			if (this.comboBox_ChartThemes.SelectedIndex == 0)
			{
				if (Base.UI.Form.ChartTheme != ChartTheme.Classic)
				{
					Base.UI.Form.ChartTheme = ChartTheme.Classic;
					this.bool_1 = true;
				}
			}
			else if (this.comboBox_ChartThemes.SelectedIndex == 1)
			{
				if (Base.UI.Form.ChartTheme != ChartTheme.Yellow)
				{
					Base.UI.Form.ChartTheme = ChartTheme.Yellow;
					this.bool_1 = true;
				}
			}
			else if (Base.UI.Form.ChartTheme != ChartTheme.Modern)
			{
				Base.UI.Form.ChartTheme = ChartTheme.Modern;
				this.bool_1 = true;
			}
			Base.UI.Form.IsOrderConfmNeeded = this.chkBox_ConfOrdr.Checked;
			Base.UI.Form.EnableShortForStock = this.chkBox_ShortForStock.Checked;
			Base.UI.Form.EnableT0ForStock = this.chkBox_T0ForStock.Checked;
			Base.UI.Form.IfNoConfClsTransWhenDblClick = this.chkBox_ConfDblClickClsTrans.Checked;
			Base.UI.Form.IfCloseNewTransFirst = this.radioBtn_ClsOrd_NewFirst.Checked;
			Base.UI.Form.IfNotAutoCancelExistingOpenTransOrder = !this.chkBox_IfAutoCancelOpenTransOrder.Checked;
			Base.UI.Form.IfNotAutoCancelExistingCloseTransOrder = !this.chkBox_IfAutoCancelCloseTransOrder.Checked;
			Base.UI.Form.IfNoBonusShare = (this.comboBox_BonusShareTreatment.SelectedIndex == 1);
			Base.UI.Form.IfNoDivident = (this.comboBox_DividentTreatment.SelectedIndex == 1);
			Base.UI.Form.IfNoSyncToolBarAndTradingTabPriceUnits = !this.chkBox_SyncToolbarTradingTabPriceUnits.Checked;
			Base.UI.Form.IfFollowPrcInTradingTab = this.chkBox_IfFollowPrcInTradingInput.Checked;
			if (this.comboBox_StkRstMethod.SelectedIndex == 0)
			{
				Base.UI.Form.StockRestorationMethod = new StockRestorationMethod?(StockRestorationMethod.Prior);
			}
			else if (this.comboBox_StkRstMethod.SelectedIndex == 1)
			{
				Base.UI.Form.StockRestorationMethod = new StockRestorationMethod?(StockRestorationMethod.Later);
			}
			else
			{
				Base.UI.Form.StockRestorationMethod = new StockRestorationMethod?(StockRestorationMethod.None);
			}
			if (this.comboBox_RationedShareTreatmt.SelectedIndex == 0)
			{
				Base.UI.Form.RationedShareTreatmt = new RationedShareTreatmt?(RationedShareTreatmt.Auto);
			}
			else if (this.comboBox_RationedShareTreatmt.SelectedIndex == 1)
			{
				Base.UI.Form.RationedShareTreatmt = new RationedShareTreatmt?(RationedShareTreatmt.Prompt);
			}
			else
			{
				Base.UI.Form.RationedShareTreatmt = new RationedShareTreatmt?(RationedShareTreatmt.None);
			}
			Base.Trading.smethod_109(this.stkSymbol_0.ID, this.chkBox_AutoStop.Checked);
			Base.Trading.smethod_110(this.stkSymbol_0.ID, this.chkBox_AutoPrft.Checked);
			if (this.radioBtn_ROpenFixAmt.Checked)
			{
				Base.UI.Form.IfROpenFixedAmt = true;
				bool flag = false;
				string value = this.txtBox_ROpenRAmt.Text.Trim();
				try
				{
					if (string.IsNullOrEmpty(value))
					{
						flag = true;
					}
					else if (Convert.ToInt32(value) < 1)
					{
						flag = true;
					}
				}
				catch
				{
					flag = true;
				}
				if (flag)
				{
					MessageBox.Show("以损定量固定金额输入值无效，请重新输入！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					this.txtBox_ROpenRAmt.Focus();
					return;
				}
				Base.UI.Form.ROpenFixedAmt = new int?(Convert.ToInt32(value));
			}
			else
			{
				Base.UI.Form.IfROpenFixedAmt = false;
				Base.UI.Form.ROpenRatio = new decimal?(this.numUpDown_ROpenRatio.Value);
			}
			Base.UI.Form.IfROpenNoShowCnfmDlg = !this.chkBox_ROpenShowCnfmDlg.Checked;
			Base.UI.smethod_47();
			this.method_0();
			base.Dispose();
		}

		// Token: 0x06001078 RID: 4216 RVA: 0x00007089 File Offset: 0x00005289
		private void method_11(object sender, EventArgs e)
		{
			this.method_12();
		}

		// Token: 0x06001079 RID: 4217 RVA: 0x0006C5C8 File Offset: 0x0006A7C8
		private void method_12()
		{
			if (this.chkBox_ifDispDayDivLine.Checked)
			{
				this.cmbBx_DDlineChtMins.Enabled = true;
				this.lbl_DispDayDivLine2.Enabled = true;
			}
			else
			{
				this.cmbBx_DDlineChtMins.Enabled = false;
				this.lbl_DispDayDivLine2.Enabled = false;
			}
		}

		// Token: 0x0600107A RID: 4218 RVA: 0x000041AE File Offset: 0x000023AE
		private void button_Cancel_Click(object sender, EventArgs e)
		{
		}

		// Token: 0x0600107B RID: 4219 RVA: 0x00007093 File Offset: 0x00005293
		private void checkBox_ShowTransArrow_CheckedChanged(object sender, EventArgs e)
		{
			this.method_13();
		}

		// Token: 0x0600107C RID: 4220 RVA: 0x0006C618 File Offset: 0x0006A818
		private void method_13()
		{
			if (this.checkBox_ShowTransArrow.Checked)
			{
				this.groupBox_TransNote.Enabled = true;
				this.radioBtn_CurrTransArrow.Enabled = true;
				this.radioBtn_AllTransArrow.Enabled = true;
				this.cmbBx_PeriodForTransArrow.Enabled = true;
				this.label_dispPeriodOfTransArw.Enabled = true;
				if (Base.UI.Form.IfShowAllTransArrow)
				{
					this.radioBtn_CurrTransArrow.Checked = false;
				}
				else
				{
					this.radioBtn_AllTransArrow.Checked = false;
				}
			}
			else
			{
				this.groupBox_TransNote.Enabled = false;
				this.radioBtn_CurrTransArrow.Enabled = false;
				this.radioBtn_AllTransArrow.Enabled = false;
				this.cmbBx_PeriodForTransArrow.Enabled = false;
				this.label_dispPeriodOfTransArw.Enabled = false;
			}
		}

		// Token: 0x0600107D RID: 4221 RVA: 0x0000709D File Offset: 0x0000529D
		private void checkBox_AlwaysShowTransNoteBox_CheckedChanged(object sender, EventArgs e)
		{
			this.method_14();
		}

		// Token: 0x0600107E RID: 4222 RVA: 0x000070A7 File Offset: 0x000052A7
		private void method_14()
		{
			if (this.checkBox_AlwaysShowTransNoteBox.Checked)
			{
				this.checkBox_OnlyShowUserNoteBox.Enabled = true;
			}
			else
			{
				this.checkBox_OnlyShowUserNoteBox.Enabled = false;
			}
		}

		// Token: 0x0600107F RID: 4223 RVA: 0x0006C6D4 File Offset: 0x0006A8D4
		private TimeUnit method_15(string string_0)
		{
			uint num = Class508.smethod_0(string_0);
			if (num <= 2269934029U)
			{
				if (num <= 1239321821U)
				{
					if (num != 647060167U)
					{
						if (num == 1239321821U)
						{
							if (string_0 == "30分钟")
							{
								return TimeUnit.ThirtyMins;
							}
						}
					}
					else if (string_0 == "5分钟")
					{
						return TimeUnit.FiveMins;
					}
				}
				else if (num != 2026632856U)
				{
					if (num != 2123123768U)
					{
						if (num == 2269934029U)
						{
							if (string_0 == "1小时")
							{
								return TimeUnit.OneHour;
							}
						}
					}
					else if (string_0 == "周线")
					{
						return TimeUnit.Week;
					}
				}
				else if (string_0 == "月线")
				{
					return TimeUnit.Month;
				}
			}
			else if (num <= 3193763256U)
			{
				if (num != 3092397832U)
				{
					if (num == 3193763256U)
					{
						if (string_0 == "15分钟")
						{
							return TimeUnit.FifteenMins;
						}
					}
				}
				else if (string_0 == "2小时")
				{
					return TimeUnit.TwoHour;
				}
			}
			else if (num != 3462998755U)
			{
				if (num != 3897470057U)
				{
					if (num == 4074130189U)
					{
						if (string_0 == "日线")
						{
							return TimeUnit.Day;
						}
					}
				}
				else if (string_0 == "3分钟")
				{
					return TimeUnit.ThreeMins;
				}
			}
			else if (string_0 == "1分钟")
			{
				return TimeUnit.OneMin;
			}
			return TimeUnit.OneMin;
		}

		// Token: 0x06001080 RID: 4224 RVA: 0x0006C840 File Offset: 0x0006AA40
		private string method_16(TimeUnit timeUnit_0)
		{
			if (timeUnit_0 <= TimeUnit.OneHour)
			{
				if (timeUnit_0 <= TimeUnit.FifteenMins)
				{
					switch (timeUnit_0)
					{
					case TimeUnit.OneMin:
						return "1分钟";
					case (TimeUnit)2:
					case (TimeUnit)4:
						break;
					case TimeUnit.ThreeMins:
						return "3分钟";
					case TimeUnit.FiveMins:
						return "5分钟";
					default:
						if (timeUnit_0 == TimeUnit.FifteenMins)
						{
							return "15分钟";
						}
						break;
					}
				}
				else
				{
					if (timeUnit_0 == TimeUnit.ThirtyMins)
					{
						return "30分钟";
					}
					if (timeUnit_0 == TimeUnit.OneHour)
					{
						return "1小时";
					}
				}
			}
			else if (timeUnit_0 <= TimeUnit.Day)
			{
				if (timeUnit_0 == TimeUnit.TwoHour)
				{
					return "2小时";
				}
				if (timeUnit_0 == TimeUnit.Day)
				{
					return "日线";
				}
			}
			else
			{
				if (timeUnit_0 == TimeUnit.Week)
				{
					return "周线";
				}
				if (timeUnit_0 == TimeUnit.Month)
				{
					return "月线";
				}
			}
			return string.Empty;
		}

		// Token: 0x06001081 RID: 4225 RVA: 0x000070D2 File Offset: 0x000052D2
		private void txtBox_ROpenRAmt_KeyPress(object sender, KeyPressEventArgs e)
		{
			if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar))
			{
				e.Handled = true;
			}
		}

		// Token: 0x17000268 RID: 616
		// (get) Token: 0x06001082 RID: 4226 RVA: 0x0006C90C File Offset: 0x0006AB0C
		public bool KLineTypeChanged
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x17000269 RID: 617
		// (get) Token: 0x06001083 RID: 4227 RVA: 0x0006C924 File Offset: 0x0006AB24
		public bool ChartThemeChanged
		{
			get
			{
				return this.bool_1;
			}
		}

		// Token: 0x06001084 RID: 4228 RVA: 0x000070F7 File Offset: 0x000052F7
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000834 RID: 2100
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000835 RID: 2101
		private StkSymbol stkSymbol_0;

		// Token: 0x04000836 RID: 2102
		private bool bool_0;

		// Token: 0x04000837 RID: 2103
		private bool bool_1;

		// Token: 0x04000838 RID: 2104
		private IContainer icontainer_0;
	}
}
