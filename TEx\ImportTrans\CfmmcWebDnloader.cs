﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns10;
using ns11;
using ns12;
using ns13;
using ns17;
using ns18;
using ns21;
using ns22;
using ns28;
using ns3;
using ns30;
using ns8;
using ns9;
using SHDocVw;

namespace TEx.ImportTrans
{
	// Token: 0x02000361 RID: 865
	internal sealed class CfmmcWebDnloader
	{
		// Token: 0x140000A7 RID: 167
		// (add) Token: 0x060023F0 RID: 9200 RVA: 0x000F2134 File Offset: 0x000F0334
		// (remove) Token: 0x060023F1 RID: 9201 RVA: 0x000F216C File Offset: 0x000F036C
		public event EventHandler IdPswdCheckOK
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060023F2 RID: 9202 RVA: 0x0000E058 File Offset: 0x0000C258
		protected void vmethod_0()
		{
			this.method_0(this.eventHandler_0);
		}

		// Token: 0x140000A8 RID: 168
		// (add) Token: 0x060023F3 RID: 9203 RVA: 0x000F21A4 File Offset: 0x000F03A4
		// (remove) Token: 0x060023F4 RID: 9204 RVA: 0x000F21DC File Offset: 0x000F03DC
		public event Delegate30 IdPswdCheckFailed
		{
			[CompilerGenerated]
			add
			{
				Delegate30 @delegate = this.delegate30_0;
				Delegate30 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate30 value2 = (Delegate30)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate30>(ref this.delegate30_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate30 @delegate = this.delegate30_0;
				Delegate30 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate30 value2 = (Delegate30)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate30>(ref this.delegate30_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x060023F5 RID: 9205 RVA: 0x000F2214 File Offset: 0x000F0414
		protected void vmethod_1(string string_1, string string_2, bool bool_5)
		{
			Class467.smethod_1("在OnIdPswdCheckFailed中触发_downDayInit()");
			EventArgs26 e = new EventArgs26(string_1, string_2, bool_5);
			this.delegate30_0(this, e);
		}

		// Token: 0x140000A9 RID: 169
		// (add) Token: 0x060023F6 RID: 9206 RVA: 0x000F2244 File Offset: 0x000F0444
		// (remove) Token: 0x060023F7 RID: 9207 RVA: 0x000F227C File Offset: 0x000F047C
		public event EventHandler CaptchaFailed
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060023F8 RID: 9208 RVA: 0x0000E068 File Offset: 0x0000C268
		protected void vmethod_2()
		{
			this.method_0(this.eventHandler_1);
		}

		// Token: 0x140000AA RID: 170
		// (add) Token: 0x060023F9 RID: 9209 RVA: 0x000F22B4 File Offset: 0x000F04B4
		// (remove) Token: 0x060023FA RID: 9210 RVA: 0x000F22EC File Offset: 0x000F04EC
		public event EventHandler LoggingIn
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_2;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_2, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_2;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_2, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060023FB RID: 9211 RVA: 0x0000E078 File Offset: 0x0000C278
		protected void vmethod_3()
		{
			this.method_0(this.eventHandler_2);
		}

		// Token: 0x140000AB RID: 171
		// (add) Token: 0x060023FC RID: 9212 RVA: 0x000F2324 File Offset: 0x000F0524
		// (remove) Token: 0x060023FD RID: 9213 RVA: 0x000F235C File Offset: 0x000F055C
		public event EventHandler LoginSuccess
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_3;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_3, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_3;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_3, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060023FE RID: 9214 RVA: 0x0000E088 File Offset: 0x0000C288
		protected void vmethod_4()
		{
			this.method_0(this.eventHandler_3);
		}

		// Token: 0x140000AC RID: 172
		// (add) Token: 0x060023FF RID: 9215 RVA: 0x000F2394 File Offset: 0x000F0594
		// (remove) Token: 0x06002400 RID: 9216 RVA: 0x000F23CC File Offset: 0x000F05CC
		public event EventHandler LoginFailed
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_4;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_4, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_4;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_4, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06002401 RID: 9217 RVA: 0x0000E098 File Offset: 0x0000C298
		protected void vmethod_5()
		{
			Class467.smethod_1("在OnLoginFailed中触发_downDayInit()");
			this.method_0(this.eventHandler_4);
		}

		// Token: 0x140000AD RID: 173
		// (add) Token: 0x06002402 RID: 9218 RVA: 0x000F2404 File Offset: 0x000F0604
		// (remove) Token: 0x06002403 RID: 9219 RVA: 0x000F243C File Offset: 0x000F063C
		public event EventHandler RecDnSuccess
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_5;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_5, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_5;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_5, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06002404 RID: 9220 RVA: 0x000F2474 File Offset: 0x000F0674
		protected void vmethod_6()
		{
			this.cfmmcWebFrm_0.WebBrowserMain.DocumentCompleted -= this.method_10;
			this.cfmmcWebFrm_0.WebBrowserQryRslt.DocumentCompleted -= this.method_14;
			this.webBrowser_1.NavigateError -= new DWebBrowserEvents2_NavigateErrorEventHandler(this, (UIntPtr)ldftn(method_12));
			this.webBrowser_0.NavigateError -= new DWebBrowserEvents2_NavigateErrorEventHandler(this, (UIntPtr)ldftn(method_12));
			Class481.smethod_1();
			Class449.smethod_0();
			Class467.smethod_1("在OnRecDnSuccess中触发_downDayInit()");
			this.method_0(this.eventHandler_5);
		}

		// Token: 0x140000AE RID: 174
		// (add) Token: 0x06002405 RID: 9221 RVA: 0x000F250C File Offset: 0x000F070C
		// (remove) Token: 0x06002406 RID: 9222 RVA: 0x000F2544 File Offset: 0x000F0744
		public event Delegate29 RecDnFailed
		{
			[CompilerGenerated]
			add
			{
				Delegate29 @delegate = this.delegate29_0;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref this.delegate29_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate29 @delegate = this.delegate29_0;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref this.delegate29_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06002407 RID: 9223 RVA: 0x000F257C File Offset: 0x000F077C
		protected void vmethod_7(int int_4, bool bool_5, string string_1)
		{
			this.int_3++;
			if (this.int_3 <= this.int_2)
			{
				this.method_5();
				Class46.smethod_1(Class21.Error, Class22.ErrorOccurred, string.Format("重新登陆下载记录。发生错误次数{0}，当前下载时间{1}。", this.int_3, this.class470_0.CurrDay));
			}
			else
			{
				Class46.smethod_1(Class21.Error, Class22.ErrorOccurred, string.Format("尝试下载{0}次失败，系统退出下载。", this.int_3));
				this.int_3 = 0;
				this.cfmmcWebFrm_0.WebBrowserMain.DocumentCompleted -= this.method_10;
				this.cfmmcWebFrm_0.WebBrowserQryRslt.DocumentCompleted -= this.method_14;
				try
				{
					this.webBrowser_1.NavigateError -= new DWebBrowserEvents2_NavigateErrorEventHandler(this, (UIntPtr)ldftn(method_12));
					this.webBrowser_0.NavigateError -= new DWebBrowserEvents2_NavigateErrorEventHandler(this, (UIntPtr)ldftn(method_12));
				}
				catch (Exception exception_)
				{
					Class46.smethod_4(exception_, true, "移除事件NavigateError时引发异常。");
				}
				Class481.smethod_1();
				Class449.smethod_0();
				Class467.smethod_1("在OnRecDnFailed中触发_downDayInit");
				if (this.delegate29_0 != null)
				{
					EventArgs25 e = new EventArgs25(int_4, bool_5, string_1);
					this.delegate29_0(this, e);
				}
			}
		}

		// Token: 0x140000AF RID: 175
		// (add) Token: 0x06002408 RID: 9224 RVA: 0x000F26C8 File Offset: 0x000F08C8
		// (remove) Token: 0x06002409 RID: 9225 RVA: 0x000F2700 File Offset: 0x000F0900
		public event Delegate29 NotifyDnRecIndex
		{
			[CompilerGenerated]
			add
			{
				Delegate29 @delegate = this.delegate29_1;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref this.delegate29_1, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate29 @delegate = this.delegate29_1;
				Delegate29 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate29 value2 = (Delegate29)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate29>(ref this.delegate29_1, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x0600240A RID: 9226 RVA: 0x000F2738 File Offset: 0x000F0938
		protected void vmethod_8(int int_4, bool bool_5, string string_1)
		{
			EventArgs25 e = new EventArgs25(int_4, bool_5, string_1);
			this.delegate29_1(this, e);
		}

		// Token: 0x0600240B RID: 9227 RVA: 0x00018F9C File Offset: 0x0001719C
		private void method_0(EventHandler eventHandler_6)
		{
			EventArgs e = new EventArgs();
			if (eventHandler_6 != null)
			{
				eventHandler_6(this, e);
			}
		}

		// Token: 0x0600240C RID: 9228 RVA: 0x000F2760 File Offset: 0x000F0960
		public CfmmcWebDnloader(CfmmcAcct cfmmcAcct)
		{
			this.cfmmcAcct = cfmmcAcct;
			this.list_0 = new List<List<string>>();
			this.int_1 = 6;
			if (TApp.IsTrialUser)
			{
				this.int_1 = 1;
			}
		}

		// Token: 0x0600240D RID: 9229 RVA: 0x000F27BC File Offset: 0x000F09BC
		public void method_1()
		{
			this.cfmmcWebFrm_0 = new CfmmcWebFrm();
			this.cfmmcWebFrm_0.WebBrowserMain.DocumentCompleted += this.method_10;
			this.cfmmcWebFrm_0.WebBrowserQryRslt.DocumentCompleted += this.method_14;
			this.webBrowser_0 = (this.cfmmcWebFrm_0.WebBrowserMain.ActiveXInstance as SHDocVw.WebBrowser);
			this.webBrowser_0.NavigateError += new DWebBrowserEvents2_NavigateErrorEventHandler(this, (UIntPtr)ldftn(method_12));
			this.webBrowser_1 = (this.cfmmcWebFrm_0.WebBrowserQryRslt.ActiveXInstance as SHDocVw.WebBrowser);
			this.webBrowser_1.NavigateError += new DWebBrowserEvents2_NavigateErrorEventHandler(this, (UIntPtr)ldftn(method_12));
			Class481.OnTimeOut += this.method_11;
			Class481.smethod_2();
			Class449.ScriptError += this.method_17;
			Class449.smethod_1();
		}

		// Token: 0x0600240E RID: 9230 RVA: 0x0000E0B2 File Offset: 0x0000C2B2
		public void method_2()
		{
			this.vmethod_9(true);
			GC.SuppressFinalize(this);
		}

		// Token: 0x0600240F RID: 9231 RVA: 0x000F28A0 File Offset: 0x000F0AA0
		~CfmmcWebDnloader()
		{
			this.vmethod_9(false);
		}

		// Token: 0x06002410 RID: 9232 RVA: 0x000F28D0 File Offset: 0x000F0AD0
		protected void vmethod_9(bool bool_5)
		{
			if (!this.bool_1)
			{
				if (bool_5)
				{
					this.bool_0 = true;
					if (this.cfmmcWebFrm_0 != null)
					{
						this.cfmmcWebFrm_0.WebBrowserMain.Dispose();
						this.cfmmcWebFrm_0.WebBrowserQryRslt.Dispose();
						this.cfmmcWebFrm_0.Dispose();
					}
				}
				this.bool_1 = true;
				this.bool_0 = false;
			}
		}

		// Token: 0x06002411 RID: 9233 RVA: 0x0000E0C3 File Offset: 0x0000C2C3
		public void method_3()
		{
			this.cfmmcWebFrm_0.method_0();
		}

		// Token: 0x06002412 RID: 9234 RVA: 0x0000E0D3 File Offset: 0x0000C2D3
		public void method_4()
		{
			this.list_0.Clear();
			this.int_0 = 0;
			this.int_3 = 0;
			this.enum28_0 = Enum28.const_0;
			this.cfmmcWebFrm_0.method_3(this.string_0);
		}

		// Token: 0x06002413 RID: 9235 RVA: 0x000F2934 File Offset: 0x000F0B34
		private void method_5()
		{
			try
			{
				Class470 obj = this.class470_0;
				lock (obj)
				{
					this.method_16(this.class470_0.CurrDay);
					this.int_0 = 0;
					this.enum28_0 = Enum28.const_0;
					this.cfmmcWebFrm_0.method_3(this.string_0);
				}
			}
			catch (Exception ex)
			{
				Class467.smethod_1(string.Format("重新导航网页失败.失败信息{0}", ex.Message));
			}
		}

		// Token: 0x06002414 RID: 9236 RVA: 0x000F29C0 File Offset: 0x000F0BC0
		private bool method_6()
		{
			CfmmcAcct cfmmcAcct_ = this.CfmmcAcct;
			DateTime? dateTime = Class463.smethod_21(cfmmcAcct_);
			if (dateTime == null)
			{
				dateTime = new DateTime?(DateTime.Now.AddMonths(-this.int_1));
			}
			DateTime? nullable_ = Class463.smethod_23(cfmmcAcct_);
			TimeSpan timeSpan = this.dateTime_0 - dateTime.Value;
			bool result;
			if (!(dateTime > this.dateTime_0) && (!(dateTime.Value.Date == this.dateTime_0.Date) || dateTime.Value.Hour < 18) && (!(dateTime.Value.Date == this.dateTime_0.Date) || this.dateTime_0.Hour > 17) && (timeSpan.TotalHours >= 24.0 || dateTime.Value.Hour < 18 || this.dateTime_0.Hour >= 17) && (dateTime.Value.DayOfWeek != DayOfWeek.Friday || dateTime.Value.Hour < 18 || timeSpan.TotalHours >= 60.0) && (dateTime.Value.DayOfWeek != DayOfWeek.Saturday || timeSpan.TotalHours >= 40.0) && (dateTime.Value.DayOfWeek != DayOfWeek.Sunday || timeSpan.TotalHours >= 16.0))
			{
				result = this.method_8(dateTime.Value, nullable_);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06002415 RID: 9237 RVA: 0x000F2B78 File Offset: 0x000F0D78
		public bool method_7()
		{
			DateTime dateTime_ = DateTime.Now.AddMonths(-this.int_1);
			return this.method_8(dateTime_, null);
		}

		// Token: 0x06002416 RID: 9238 RVA: 0x000F2BB0 File Offset: 0x000F0DB0
		public bool method_8(DateTime dateTime_2, DateTime? nullable_0)
		{
			DateTime dateTime = this.method_9(dateTime_2, nullable_0);
			if (this.class470_0 == null)
			{
				this.class470_0 = new Class470(this.dateTime_0);
			}
			return this.class470_0.method_3(dateTime);
		}

		// Token: 0x06002417 RID: 9239 RVA: 0x000F2BF0 File Offset: 0x000F0DF0
		private DateTime method_9(DateTime dateTime_2, DateTime? nullable_0)
		{
			int num = 5;
			if (TApp.IsTrialUser)
			{
				num = 1;
			}
			DateTime result = this.dateTime_0.AddMonths(-num);
			if (!TApp.IsTrialUser)
			{
				while (result.Day != 1)
				{
					result = result.AddDays(-1.0);
				}
			}
			if (dateTime_2.Date >= result.Date)
			{
				result = dateTime_2;
				if ((nullable_0 != null && nullable_0.Value.Date == dateTime_2.Date) || dateTime_2.Hour >= 18)
				{
					result = dateTime_2.AddDays(1.0);
				}
			}
			return result;
		}

		// Token: 0x06002418 RID: 9240 RVA: 0x000F2C98 File Offset: 0x000F0E98
		public void method_10(object sender, WebBrowserDocumentCompletedEventArgs e)
		{
			if ((sender as System.Windows.Forms.WebBrowser).ReadyState == WebBrowserReadyState.Complete)
			{
				if (!(e.Url.ToString() != (sender as System.Windows.Forms.WebBrowser).Url.ToString()))
				{
					Class481.smethod_0(DateTime.Now);
					if (this.enum28_0 != Enum28.const_8)
					{
						if (this.enum28_0 == Enum28.const_5)
						{
							Class467.smethod_1(string.Format("触发系统退出登录.系统退出", new object[0]));
							this.vmethod_6();
						}
						else
						{
							if (this.enum28_0 != Enum28.const_2 && this.enum28_0 != Enum28.const_4)
							{
								if (this.enum28_0 != Enum28.const_6)
								{
									if (this.bool_0)
									{
										return;
									}
									if (this.enum28_0 != Enum28.const_0)
									{
										if (this.enum28_0 == Enum28.const_1)
										{
											string text = this.cfmmcWebFrm_0.method_9();
											if (!string.IsNullOrEmpty(text))
											{
												this.vmethod_1(this.UserName, this.PassWord, false);
												this.enum28_0 = Enum28.const_2;
												this.vmethod_7(0, false, text);
												return;
											}
											this.vmethod_0();
											text = this.cfmmcWebFrm_0.method_10();
											if (!string.IsNullOrEmpty(text))
											{
												this.int_0++;
												this.vmethod_2();
												string text2 = this.cfmmcWebFrm_0.method_6(this.UserName, this.PassWord);
												if (!string.IsNullOrEmpty(text2))
												{
													this.enum28_0 = Enum28.const_2;
													this.vmethod_7(0, false, text2);
													return;
												}
												return;
											}
											else
											{
												this.vmethod_4();
												this.enum28_0 = Enum28.const_3;
												if (this.class470_0.Idx > -1)
												{
													this.edownDay_0 = EDownDay.b输入提交日期;
												}
												else
												{
													this.edownDay_0 = EDownDay.f不下载;
												}
											}
										}
										if (this.enum28_0 == Enum28.const_3)
										{
											if (this.bool_3)
											{
												Class467.smethod_1(string.Format("complete中拦截到脚本错误.日期为{0}", this.dateTime_1));
												Class449.smethod_0();
												this.method_16(this.dateTime_1);
												this.edownDay_0 = EDownDay.b输入提交日期;
												this.bool_3 = false;
												Class449.smethod_1();
											}
											if (this.edownDay_0 == EDownDay.e失败)
											{
												this.enum28_0 = Enum28.const_4;
												this.vmethod_7(0, false, "交易记录下载失败");
											}
											else if (this.class470_0.OK)
											{
												Class467.smethod_1(string.Format("下载时间{0}进入DownDayOK", this.class470_0.CurrDay));
												this.enum28_0 = Enum28.const_7;
												string text3 = this.cfmmcWebFrm_0.method_0();
												if (string.IsNullOrEmpty(text3))
												{
													this.enum28_0 = Enum28.const_5;
												}
												else
												{
													this.enum28_0 = Enum28.const_6;
													Class467.smethod_1(string.Format("下载时间{0}点击退出登录错误{1}", this.class470_0.CurrDay, text3));
												}
												if (this.enum28_0 == Enum28.const_6)
												{
													this.vmethod_5();
													this.vmethod_7(0, false, "登录失败");
												}
											}
											else
											{
												Class467.smethod_1(string.Format("下载时间{0}进入{1}", this.class470_0.CurrDay, this.edownDay_0));
												if (this.edownDay_0 == EDownDay.b输入提交日期)
												{
													string text4 = this.cfmmcWebFrm_0.method_14(this.class470_0.CurrDay);
													if (string.IsNullOrEmpty(text4))
													{
														this.edownDay_0 = EDownDay.d1点击成交明细;
													}
													else
													{
														string string_ = string.Format("下载时间{0}输入提交日期失败返回值{1}", this.class470_0.CurrDay, text4);
														Class467.smethod_1(string_);
														this.vmethod_7(0, false, string_);
													}
												}
												else if (this.edownDay_0 == EDownDay.d1点击成交明细)
												{
													if (this.bool_2)
													{
														Class467.smethod_1(string.Format("取消一次{0}", this.edownDay_0));
													}
													else
													{
														if (!this.bool_2)
														{
															this.bool_2 = true;
														}
														string text5 = this.cfmmcWebFrm_0.method_16();
														if (string.IsNullOrEmpty(text5))
														{
															this.edownDay_0 = EDownDay.d2成交下载;
															this.bool_2 = false;
														}
														else
														{
															Class467.smethod_1(string.Format("失败{0}", text5));
															this.vmethod_8(this.class470_0.Idx, false, string.Empty);
															this.class470_0.method_4();
															text5 = this.cfmmcWebFrm_0.method_14(this.class470_0.CurrDay);
															if (string.IsNullOrEmpty(text5))
															{
																this.edownDay_0 = EDownDay.d1点击成交明细;
																this.bool_2 = false;
															}
															else
															{
																string string_2 = string.Format("下载时间{0}输入提交日期失败返回值{1}", this.class470_0.CurrDay, text5);
																Class467.smethod_1(string_2);
																this.vmethod_7(0, false, string_2);
																this.bool_2 = false;
															}
														}
													}
												}
											}
										}
										return;
									}
									if (string.IsNullOrEmpty(this.cfmmcWebFrm_0.method_6(this.UserName, this.PassWord)))
									{
										this.enum28_0 = Enum28.const_1;
										this.vmethod_3();
										return;
									}
									this.enum28_0 = Enum28.const_2;
									this.vmethod_5();
									this.vmethod_7(0, false, "登录失败");
									return;
								}
							}
							string string_3;
							if (this.enum28_0 == Enum28.const_2)
							{
								string_3 = "登录失败";
							}
							else if (this.enum28_0 == Enum28.const_4)
							{
								string_3 = "下载失败";
							}
							else
							{
								string_3 = "登出失败";
							}
							this.vmethod_7(0, false, string_3);
						}
					}
				}
			}
		}

		// Token: 0x06002419 RID: 9241 RVA: 0x000F3164 File Offset: 0x000F1364
		private void method_11()
		{
			this.enum28_0 = Enum28.const_8;
			string string_ = string.Format("读取网页发生错误，请检查网络。", new object[0]);
			Class467.smethod_1("在WebTimeOut中触发错误代码");
			try
			{
				this.vmethod_7(0, false, string_);
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x0600241A RID: 9242 RVA: 0x000F31B8 File Offset: 0x000F13B8
		private void method_12(object object_0, ref object object_1, ref object object_2, ref object object_3, ref bool bool_5)
		{
			this.enum28_0 = Enum28.const_8;
			string string_ = string.Format("读取网页发生错误，请检查网络。", new object[0]);
			Class467.smethod_1("在NavigateError中触发错误代码");
			this.vmethod_7(0, false, string_);
		}

		// Token: 0x0600241B RID: 9243 RVA: 0x000F31F4 File Offset: 0x000F13F4
		private void method_13(DateTime dateTime_2)
		{
			string text = this.cfmmcWebFrm_0.method_14(this.class470_0.CurrDay);
			if (string.IsNullOrEmpty(text))
			{
				this.edownDay_0 = EDownDay.d1点击成交明细;
			}
			else
			{
				string string_ = string.Format("下载时间{0}输入提交日期失败返回值{1}", this.class470_0.CurrDay, text);
				Class467.smethod_1(string_);
				this.vmethod_7(0, false, string_);
			}
		}

		// Token: 0x0600241C RID: 9244 RVA: 0x000F3258 File Offset: 0x000F1458
		public void method_14(object sender, WebBrowserDocumentCompletedEventArgs e)
		{
			if (!this.bool_0)
			{
				if (this.cfmmcWebFrm_0.method_13(sender))
				{
					if (this.enum28_0 != Enum28.const_8)
					{
						Class481.smethod_0(DateTime.Now);
						List<List<string>> list = new List<List<string>>();
						if (this.edownDay_0 == EDownDay.d2成交下载 || this.edownDay_0 == EDownDay.d4平仓下载)
						{
							list = this.method_19((sender as System.Windows.Forms.WebBrowser).Document);
						}
						Class467.smethod_1(string.Format("{0}读取数量为{1}", this.edownDay_0, list.Count));
						if (!list.Any<List<string>>())
						{
							HtmlDocument document = (sender as System.Windows.Forms.WebBrowser).Document;
							if (document.Body.InnerText == null)
							{
								Class467.smethod_1(string.Format("{0}_{1}读取网页内容为null，重新下载当天文件", this.edownDay_0, this.class470_0.CurrDay));
								this.method_13(this.class470_0.CurrDay);
								return;
							}
							if (string.IsNullOrEmpty(document.Body.InnerText.Trim()))
							{
								Class467.smethod_1(string.Format("{0}_{1}读取网页内容为空，重新下载当天文件", this.edownDay_0, this.class470_0.CurrDay));
								this.method_13(this.class470_0.CurrDay);
								return;
							}
						}
						if (this.edownDay_0 == EDownDay.d2成交下载)
						{
							if (list.Any(new Func<List<string>, bool>(CfmmcWebDnloader.<>c.<>9.method_0)))
							{
								Class467.smethod_1(string.Format("查找到平仓单,点击平仓按钮", new object[0]));
								this.list_1 = list;
								this.edownDay_0 = EDownDay.d3点击平仓明细;
								string text = this.cfmmcWebFrm_0.method_15();
								if (string.IsNullOrEmpty(text))
								{
									this.edownDay_0 = EDownDay.d4平仓下载;
									return;
								}
								Class467.smethod_1(string.Format("失败{0}", text));
								this.vmethod_7(0, false, text);
								return;
							}
							else
							{
								foreach (List<string> list2 in list)
								{
									list2.Insert(11, "");
									this.list_0.Add(list2);
								}
								Class467.smethod_1(string.Format("读取成交单,完成", new object[0]));
								this.edownDay_0 = EDownDay.e1完成;
							}
						}
						else if (this.edownDay_0 == EDownDay.d4平仓下载)
						{
							foreach (List<string> list3 in this.method_18(this.list_1, list))
							{
								int num = Enum.GetNames(typeof(CfmmcRecFieldsEnum)).Length;
								if (list3.Count != num)
								{
									throw new Exception(string.Format("读取{0}平仓明细错误,返回的列数{1}不等于{2}", this.class470_0.CurrDay, list3.Count, num));
								}
								this.list_0.Add(list3);
							}
							this.list_1.Clear();
							this.edownDay_0 = EDownDay.e1完成;
							Class467.smethod_1(string.Format("读取平仓单,完成", new object[0]));
						}
						if (this.enum28_0 == Enum28.const_3 && this.edownDay_0 == EDownDay.e1完成)
						{
							Class467.smethod_1(string.Format("在newwindow中下载{0}数据完成.", this.class470_0.CurrDay));
							this.vmethod_8(this.class470_0.Idx, true, string.Empty);
							this.class470_0.method_4();
							this.method_13(this.class470_0.CurrDay);
						}
					}
				}
			}
		}

		// Token: 0x0600241D RID: 9245 RVA: 0x000F35F8 File Offset: 0x000F17F8
		private int method_15(DateTime dateTime_2)
		{
			CfmmcWebDnloader.Class468 @class = new CfmmcWebDnloader.Class468();
			@class.dateTime_0 = dateTime_2;
			return CfmmcWebDnloader.list_2.Count(new Func<List<string>, bool>(@class.method_0));
		}

		// Token: 0x0600241E RID: 9246 RVA: 0x000F362C File Offset: 0x000F182C
		private void method_16(DateTime dateTime_2)
		{
			int index = Convert.ToInt32(CfmmcRecFieldsEnum.实际成交日期);
			int count = this.list_0.Count;
			try
			{
				for (;;)
				{
					IL_19:
					for (int i = 0; i < this.list_0.Count; i++)
					{
						if (Convert.ToDateTime(this.list_0[i][index]).Date.Date == dateTime_2.Date)
						{
							this.list_0.RemoveAt(i);
							Class467.smethod_1(string.Format("删除指定日期{0}的数据", dateTime_2.Date));
							goto IL_19;
						}
					}
					break;
				}
			}
			catch (Exception)
			{
				throw new Exception(string.Format("删除_recordList中的指定日期{0}错误", dateTime_2));
			}
			int num = count - this.list_0.Count;
			if (num != 0)
			{
				Class467.smethod_1(string.Format("在RecordeList中删除了指定日期{0}的数据,共{1}个", dateTime_2, num));
			}
		}

		// Token: 0x0600241F RID: 9247 RVA: 0x000F3724 File Offset: 0x000F1924
		private void method_17(object sender, EventArgs e)
		{
			try
			{
				Class467.smethod_1(string.Format("接收到脚本错误事件.日期为{0}", this.class470_0.CurrDay));
				this.bool_3 = true;
				this.dateTime_1 = this.class470_0.CurrDay;
				this.class470_0.method_0(false);
			}
			catch (Exception ex)
			{
				Class467.smethod_1(string.Format("在脚本错误中接收到异常{0}", ex.Message));
			}
		}

		// Token: 0x06002420 RID: 9248 RVA: 0x000F37A0 File Offset: 0x000F19A0
		private List<List<string>> method_18(List<List<string>> list_3, List<List<string>> list_4)
		{
			List<List<string>> list = new List<List<string>>();
			List<List<string>> result;
			if (!list_3.Any<List<string>>())
			{
				result = list;
			}
			else
			{
				for (int i = 0; i < list_3.Count; i++)
				{
					List<string> list2 = list_3[i];
					if (list2[8].Contains("平"))
					{
						string text = list2[1];
						for (int j = 0; j < list_4.Count; j++)
						{
							List<string> list3 = list_4[j];
							if (list3[0] == list2[0] && !string.IsNullOrEmpty(list3[1]) && text.Contains(list3[1]))
							{
								List<string> list4 = new List<string>(list2);
								list4[10] = list3[7];
								list4[6] = list3[5];
								decimal num = Convert.ToDecimal(list2[6]);
								decimal d = Convert.ToDecimal(list3[5]);
								if (num != d)
								{
									decimal d2 = Convert.ToDecimal(list2[9]);
									list4[9] = Math.Round(d2 * d / num, 2).ToString();
								}
								list4.Insert(11, list_4[j][8]);
								list.Add(list4);
							}
						}
					}
					else
					{
						list2.Insert(11, "");
						list.Add(list2);
					}
				}
				result = list;
			}
			return result;
		}

		// Token: 0x06002421 RID: 9249 RVA: 0x000F3928 File Offset: 0x000F1B28
		private List<List<string>> method_19(HtmlDocument htmlDocument_0)
		{
			bool flag = false;
			List<List<string>> list = new List<List<string>>();
			List<List<string>> result;
			try
			{
				HtmlElementCollection elementsByTagName = htmlDocument_0.GetElementsByTagName("table");
				for (int i = 1; i < elementsByTagName.Count; i++)
				{
					if (i == 3)
					{
						HtmlElement htmlElement = elementsByTagName[i];
						if (!(htmlElement == null))
						{
							foreach (object obj in htmlElement.GetElementsByTagName("tr"))
							{
								HtmlElementCollection elementsByTagName2 = ((HtmlElement)obj).GetElementsByTagName("td");
								int num = -1;
								if (!flag)
								{
									using (IEnumerator enumerator2 = elementsByTagName2.GetEnumerator())
									{
										while (enumerator2.MoveNext())
										{
											object obj2 = enumerator2.Current;
											HtmlElement htmlElement2 = (HtmlElement)obj2;
											num++;
											if (htmlElement2.InnerText.Trim() == "合约")
											{
												flag = true;
												break;
											}
										}
										continue;
									}
								}
								List<string> list2 = new List<string>();
								for (int j = 0; j < elementsByTagName2.Count; j++)
								{
									HtmlElement htmlElement3 = elementsByTagName2[j];
									if (htmlElement3 != null)
									{
										string item = htmlElement3.InnerText.Trim();
										list2.Add(item);
									}
								}
								if (list2.Any<string>() && list2[0] != "合计")
								{
									list.Add(list2);
								}
							}
							if (list != null && list.Any<List<string>>())
							{
								break;
							}
						}
					}
				}
				result = list;
			}
			catch
			{
				result = list;
			}
			return result;
		}

		// Token: 0x1700062C RID: 1580
		// (get) Token: 0x06002422 RID: 9250 RVA: 0x000F3B0C File Offset: 0x000F1D0C
		public Enum28 WebDnStatus
		{
			get
			{
				return this.enum28_0;
			}
		}

		// Token: 0x1700062D RID: 1581
		// (get) Token: 0x06002423 RID: 9251 RVA: 0x000F3B24 File Offset: 0x000F1D24
		public List<List<string>> RecordList
		{
			get
			{
				return this.list_0;
			}
		}

		// Token: 0x1700062E RID: 1582
		// (get) Token: 0x06002424 RID: 9252 RVA: 0x000F3B3C File Offset: 0x000F1D3C
		public string UserName
		{
			get
			{
				return this.CfmmcAcct.ID;
			}
		}

		// Token: 0x1700062F RID: 1583
		// (get) Token: 0x06002425 RID: 9253 RVA: 0x000F3B58 File Offset: 0x000F1D58
		public string PassWord
		{
			get
			{
				return this.CfmmcAcct.Password;
			}
		}

		// Token: 0x17000630 RID: 1584
		// (get) Token: 0x06002426 RID: 9254 RVA: 0x000F3B74 File Offset: 0x000F1D74
		// (set) Token: 0x06002427 RID: 9255 RVA: 0x0000E108 File Offset: 0x0000C308
		public CfmmcAcct CfmmcAcct
		{
			get
			{
				return this.cfmmcAcct;
			}
			set
			{
				this.cfmmcAcct = value;
			}
		}

		// Token: 0x17000631 RID: 1585
		// (get) Token: 0x06002428 RID: 9256 RVA: 0x000F3B8C File Offset: 0x000F1D8C
		public bool IsDnldNeeded
		{
			get
			{
				return this.method_6();
			}
		}

		// Token: 0x17000632 RID: 1586
		// (get) Token: 0x06002429 RID: 9257 RVA: 0x000F3BA4 File Offset: 0x000F1DA4
		public List<DateTime> DnDayList
		{
			get
			{
				return this.class470_0.DayList;
			}
		}

		// Token: 0x17000633 RID: 1587
		// (get) Token: 0x0600242A RID: 9258 RVA: 0x000F3BC0 File Offset: 0x000F1DC0
		// (set) Token: 0x0600242B RID: 9259 RVA: 0x0000E113 File Offset: 0x0000C313
		public bool IsInBackground { get; set; }

		// Token: 0x04001166 RID: 4454
		private readonly string string_0 = "https://investorservice.cfmmc.com/login.do";

		// Token: 0x04001167 RID: 4455
		private EDownDay edownDay_0;

		// Token: 0x04001168 RID: 4456
		private CfmmcWebFrm cfmmcWebFrm_0;

		// Token: 0x04001169 RID: 4457
		private Class470 class470_0;

		// Token: 0x0400116A RID: 4458
		private List<List<string>> list_0;

		// Token: 0x0400116B RID: 4459
		private int int_0;

		// Token: 0x0400116C RID: 4460
		private List<List<string>> list_1;

		// Token: 0x0400116D RID: 4461
		private int int_1;

		// Token: 0x0400116E RID: 4462
		private DateTime dateTime_0 = DateTime.Now;

		// Token: 0x0400116F RID: 4463
		private readonly int int_2 = 3;

		// Token: 0x04001170 RID: 4464
		private int int_3;

		// Token: 0x04001171 RID: 4465
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04001172 RID: 4466
		[CompilerGenerated]
		private Delegate30 delegate30_0;

		// Token: 0x04001173 RID: 4467
		[CompilerGenerated]
		private EventHandler eventHandler_1;

		// Token: 0x04001174 RID: 4468
		[CompilerGenerated]
		private EventHandler eventHandler_2;

		// Token: 0x04001175 RID: 4469
		[CompilerGenerated]
		private EventHandler eventHandler_3;

		// Token: 0x04001176 RID: 4470
		[CompilerGenerated]
		private EventHandler eventHandler_4;

		// Token: 0x04001177 RID: 4471
		[CompilerGenerated]
		private EventHandler eventHandler_5;

		// Token: 0x04001178 RID: 4472
		[CompilerGenerated]
		private Delegate29 delegate29_0;

		// Token: 0x04001179 RID: 4473
		[CompilerGenerated]
		private Delegate29 delegate29_1;

		// Token: 0x0400117A RID: 4474
		private SHDocVw.WebBrowser webBrowser_0;

		// Token: 0x0400117B RID: 4475
		private SHDocVw.WebBrowser webBrowser_1;

		// Token: 0x0400117C RID: 4476
		private bool bool_0;

		// Token: 0x0400117D RID: 4477
		private bool bool_1;

		// Token: 0x0400117E RID: 4478
		private bool bool_2;

		// Token: 0x0400117F RID: 4479
		public static List<List<string>> list_2 = new List<List<string>>();

		// Token: 0x04001180 RID: 4480
		private bool bool_3;

		// Token: 0x04001181 RID: 4481
		private DateTime dateTime_1;

		// Token: 0x04001182 RID: 4482
		private Enum28 enum28_0;

		// Token: 0x04001183 RID: 4483
		private CfmmcAcct cfmmcAcct;

		// Token: 0x04001184 RID: 4484
		[CompilerGenerated]
		private bool bool_4;

		// Token: 0x02000362 RID: 866
		public enum Enum29
		{
			// Token: 0x04001186 RID: 4486
			const_0,
			// Token: 0x04001187 RID: 4487
			const_1
		}

		// Token: 0x02000364 RID: 868
		[CompilerGenerated]
		private sealed class Class468
		{
			// Token: 0x06002431 RID: 9265 RVA: 0x000F3BFC File Offset: 0x000F1DFC
			internal bool method_0(List<string> list_0)
			{
				return DateTime.Parse(list_0[12]) == this.dateTime_0;
			}

			// Token: 0x0400118A RID: 4490
			public DateTime dateTime_0;
		}
	}
}
