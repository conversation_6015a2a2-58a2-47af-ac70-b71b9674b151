﻿using System;
using System.IO;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;

namespace ns30
{
	// Token: 0x020003D8 RID: 984
	internal static class Class518
	{
		// Token: 0x06002708 RID: 9992 RVA: 0x00023060 File Offset: 0x00021260
		private static bool smethod_0(Assembly assembly_0, Assembly assembly_1)
		{
			return true;
		}

		// Token: 0x06002709 RID: 9993 RVA: 0x000FCEC4 File Offset: 0x000FB0C4
		private static ICryptoTransform smethod_1(byte[] byte_0, byte[] byte_1, bool bool_0)
		{
			ICryptoTransform result;
			using (SymmetricAlgorithm symmetricAlgorithm = new RijndaelManaged())
			{
				result = (bool_0 ? symmetricAlgorithm.CreateDecryptor(byte_0, byte_1) : symmetricAlgorithm.CreateEncryptor(byte_0, byte_1));
			}
			return result;
		}

		// Token: 0x0600270A RID: 9994 RVA: 0x000FCF10 File Offset: 0x000FB110
		private static ICryptoTransform smethod_2(byte[] byte_0, byte[] byte_1, bool bool_0)
		{
			ICryptoTransform result;
			using (DESCryptoServiceProvider descryptoServiceProvider = new DESCryptoServiceProvider())
			{
				result = (bool_0 ? descryptoServiceProvider.CreateDecryptor(byte_0, byte_1) : descryptoServiceProvider.CreateEncryptor(byte_0, byte_1));
			}
			return result;
		}

		// Token: 0x0600270B RID: 9995 RVA: 0x000FCF5C File Offset: 0x000FB15C
		public static byte[] smethod_3(byte[] byte_0)
		{
			Assembly callingAssembly = Assembly.GetCallingAssembly();
			Assembly executingAssembly = Assembly.GetExecutingAssembly();
			byte[] result;
			if (callingAssembly != executingAssembly && !Class518.smethod_0(executingAssembly, callingAssembly))
			{
				result = null;
			}
			else
			{
				Class518.Stream0 stream = new Class518.Stream0(byte_0);
				byte[] array = new byte[0];
				int num = stream.method_3();
				if (num == 67324752)
				{
					short num2 = (short)stream.method_2();
					int num3 = stream.method_2();
					int num4 = stream.method_2();
					if (num == 67324752 && num2 == 20 && num3 == 0)
					{
						if (num4 == 8)
						{
							stream.method_3();
							stream.method_3();
							stream.method_3();
							int num5 = stream.method_3();
							int num6 = stream.method_2();
							int num7 = stream.method_2();
							if (num6 > 0)
							{
								byte[] buffer = new byte[num6];
								stream.Read(buffer, 0, num6);
							}
							if (num7 > 0)
							{
								byte[] buffer2 = new byte[num7];
								stream.Read(buffer2, 0, num7);
							}
							byte[] array2 = new byte[stream.Length - stream.Position];
							stream.Read(array2, 0, array2.Length);
							Class518.Class519 @class = new Class518.Class519(array2);
							array = new byte[num5];
							@class.method_2(array, 0, array.Length);
							goto IL_260;
						}
					}
					throw new FormatException("Wrong Header Signature");
				}
				int num8 = num >> 24;
				num -= num8 << 24;
				if (num != 8223355)
				{
					throw new FormatException("Unknown Header");
				}
				if (num8 == 1)
				{
					int num9 = stream.method_3();
					array = new byte[num9];
					int num11;
					for (int i = 0; i < num9; i += num11)
					{
						int num10 = stream.method_3();
						num11 = stream.method_3();
						byte[] array3 = new byte[num10];
						stream.Read(array3, 0, array3.Length);
						new Class518.Class519(array3).method_2(array, i, num11);
					}
				}
				if (num8 == 2)
				{
					byte[] byte_ = new byte[]
					{
						50,
						237,
						4,
						239,
						241,
						97,
						90,
						159
					};
					byte[] byte_2 = new byte[]
					{
						96,
						193,
						77,
						160,
						53,
						202,
						248,
						187
					};
					using (ICryptoTransform cryptoTransform = Class518.smethod_2(byte_, byte_2, true))
					{
						array = Class518.smethod_3(cryptoTransform.TransformFinalBlock(byte_0, 4, byte_0.Length - 4));
					}
				}
				if (num8 == 3)
				{
					byte[] byte_3 = new byte[]
					{
						1,
						1,
						1,
						1,
						1,
						1,
						1,
						1,
						1,
						1,
						1,
						1,
						1,
						1,
						1,
						1
					};
					byte[] byte_4 = new byte[]
					{
						2,
						2,
						2,
						2,
						2,
						2,
						2,
						2,
						2,
						2,
						2,
						2,
						2,
						2,
						2,
						2
					};
					using (ICryptoTransform cryptoTransform2 = Class518.smethod_1(byte_3, byte_4, true))
					{
						array = Class518.smethod_3(cryptoTransform2.TransformFinalBlock(byte_0, 4, byte_0.Length - 4));
					}
				}
				IL_260:
				stream.Close();
				stream = null;
				result = array;
			}
			return result;
		}

		// Token: 0x0600270C RID: 9996 RVA: 0x000FD200 File Offset: 0x000FB400
		public static byte[] smethod_4(byte[] byte_0)
		{
			return Class518.smethod_7(byte_0, 1, null, null);
		}

		// Token: 0x0600270D RID: 9997 RVA: 0x000FD21C File Offset: 0x000FB41C
		public static byte[] smethod_5(byte[] byte_0, byte[] byte_1, byte[] byte_2)
		{
			return Class518.smethod_7(byte_0, 2, byte_1, byte_2);
		}

		// Token: 0x0600270E RID: 9998 RVA: 0x000FD238 File Offset: 0x000FB438
		public static byte[] smethod_6(byte[] byte_0, byte[] byte_1, byte[] byte_2)
		{
			return Class518.smethod_7(byte_0, 3, byte_1, byte_2);
		}

		// Token: 0x0600270F RID: 9999 RVA: 0x000FD254 File Offset: 0x000FB454
		private static byte[] smethod_7(byte[] byte_0, int int_0, byte[] byte_1, byte[] byte_2)
		{
			byte[] result;
			try
			{
				Class518.Stream0 stream = new Class518.Stream0();
				if (int_0 == 0)
				{
					Class518.Class524 @class = new Class518.Class524();
					DateTime now = DateTime.Now;
					long num = (long)((ulong)((now.Year - 1980 & 127) << 25 | now.Month << 21 | now.Day << 16 | now.Hour << 11 | now.Minute << 5 | (int)((uint)now.Second >> 1)));
					uint[] array = new uint[]
					{
						0U,
						1996959894U,
						3993919788U,
						2567524794U,
						124634137U,
						1886057615U,
						3915621685U,
						2657392035U,
						249268274U,
						2044508324U,
						3772115230U,
						2547177864U,
						162941995U,
						2125561021U,
						3887607047U,
						2428444049U,
						498536548U,
						1789927666U,
						4089016648U,
						2227061214U,
						450548861U,
						1843258603U,
						4107580753U,
						2211677639U,
						325883990U,
						1684777152U,
						4251122042U,
						2321926636U,
						335633487U,
						1661365465U,
						4195302755U,
						2366115317U,
						997073096U,
						1281953886U,
						3579855332U,
						2724688242U,
						1006888145U,
						1258607687U,
						3524101629U,
						2768942443U,
						901097722U,
						1119000684U,
						3686517206U,
						2898065728U,
						853044451U,
						1172266101U,
						3705015759U,
						2882616665U,
						651767980U,
						1373503546U,
						3369554304U,
						3218104598U,
						565507253U,
						1454621731U,
						3485111705U,
						3099436303U,
						671266974U,
						1594198024U,
						3322730930U,
						2970347812U,
						795835527U,
						1483230225U,
						3244367275U,
						3060149565U,
						1994146192U,
						31158534U,
						2563907772U,
						4023717930U,
						1907459465U,
						112637215U,
						2680153253U,
						3904427059U,
						2013776290U,
						251722036U,
						2517215374U,
						3775830040U,
						2137656763U,
						141376813U,
						2439277719U,
						3865271297U,
						1802195444U,
						476864866U,
						2238001368U,
						4066508878U,
						1812370925U,
						453092731U,
						2181625025U,
						4111451223U,
						1706088902U,
						314042704U,
						2344532202U,
						4240017532U,
						1658658271U,
						366619977U,
						2362670323U,
						4224994405U,
						1303535960U,
						984961486U,
						2747007092U,
						3569037538U,
						1256170817U,
						1037604311U,
						2765210733U,
						3554079995U,
						1131014506U,
						879679996U,
						2909243462U,
						3663771856U,
						1141124467U,
						855842277U,
						2852801631U,
						3708648649U,
						1342533948U,
						654459306U,
						3188396048U,
						3373015174U,
						1466479909U,
						544179635U,
						3110523913U,
						3462522015U,
						1591671054U,
						702138776U,
						2966460450U,
						3352799412U,
						1504918807U,
						783551873U,
						3082640443U,
						3233442989U,
						3988292384U,
						2596254646U,
						62317068U,
						1957810842U,
						3939845945U,
						2647816111U,
						81470997U,
						1943803523U,
						3814918930U,
						2489596804U,
						225274430U,
						2053790376U,
						3826175755U,
						2466906013U,
						167816743U,
						2097651377U,
						4027552580U,
						2265490386U,
						503444072U,
						1762050814U,
						4150417245U,
						2154129355U,
						426522225U,
						1852507879U,
						4275313526U,
						2312317920U,
						282753626U,
						1742555852U,
						4189708143U,
						2394877945U,
						397917763U,
						1622183637U,
						3604390888U,
						2714866558U,
						953729732U,
						1340076626U,
						3518719985U,
						2797360999U,
						1068828381U,
						1219638859U,
						3624741850U,
						2936675148U,
						906185462U,
						1090812512U,
						3747672003U,
						2825379669U,
						829329135U,
						1181335161U,
						3412177804U,
						3160834842U,
						628085408U,
						1382605366U,
						3423369109U,
						3138078467U,
						570562233U,
						1426400815U,
						3317316542U,
						2998733608U,
						733239954U,
						1555261956U,
						3268935591U,
						3050360625U,
						752459403U,
						1541320221U,
						2607071920U,
						3965973030U,
						1969922972U,
						40735498U,
						2617837225U,
						3943577151U,
						1913087877U,
						83908371U,
						2512341634U,
						3803740692U,
						2075208622U,
						213261112U,
						2463272603U,
						3855990285U,
						2094854071U,
						198958881U,
						2262029012U,
						4057260610U,
						1759359992U,
						534414190U,
						2176718541U,
						4139329115U,
						1873836001U,
						414664567U,
						2282248934U,
						4279200368U,
						1711684554U,
						285281116U,
						2405801727U,
						4167216745U,
						1634467795U,
						376229701U,
						2685067896U,
						3608007406U,
						1308918612U,
						956543938U,
						2808555105U,
						3495958263U,
						1231636301U,
						1047427035U,
						2932959818U,
						3654703836U,
						1088359270U,
						936918000U,
						2847714899U,
						3736837829U,
						1202900863U,
						817233897U,
						3183342108U,
						3401237130U,
						1404277552U,
						615818150U,
						3134207493U,
						3453421203U,
						1423857449U,
						601450431U,
						3009837614U,
						3294710456U,
						1567103746U,
						711928724U,
						3020668471U,
						3272380065U,
						1510334235U,
						755167117U
					};
					uint maxValue = uint.MaxValue;
					uint num2 = uint.MaxValue;
					int num3 = 0;
					int num4 = byte_0.Length;
					while (--num4 >= 0)
					{
						num2 = (array[(int)((num2 ^ (uint)byte_0[num3++]) & 255U)] ^ num2 >> 8);
					}
					num2 ^= maxValue;
					stream.method_1(67324752);
					stream.method_0(20);
					stream.method_0(0);
					stream.method_0(8);
					stream.method_1((int)num);
					stream.method_1((int)num2);
					long position = stream.Position;
					stream.method_1(0);
					stream.method_1(byte_0.Length);
					byte[] bytes = Encoding.UTF8.GetBytes("{data}");
					stream.method_0(bytes.Length);
					stream.method_0(0);
					stream.Write(bytes, 0, bytes.Length);
					@class.method_1(byte_0);
					while (!@class.IsNeedingInput)
					{
						byte[] array2 = new byte[512];
						int num5 = @class.method_2(array2);
						if (num5 <= 0)
						{
							break;
						}
						stream.Write(array2, 0, num5);
					}
					@class.method_0();
					while (!@class.IsFinished)
					{
						byte[] array3 = new byte[512];
						int num6 = @class.method_2(array3);
						if (num6 <= 0)
						{
							break;
						}
						stream.Write(array3, 0, num6);
					}
					long totalOut = @class.TotalOut;
					stream.method_1(33639248);
					stream.method_0(20);
					stream.method_0(20);
					stream.method_0(0);
					stream.method_0(8);
					stream.method_1((int)num);
					stream.method_1((int)num2);
					stream.method_1((int)totalOut);
					stream.method_1(byte_0.Length);
					stream.method_0(bytes.Length);
					stream.method_0(0);
					stream.method_0(0);
					stream.method_0(0);
					stream.method_0(0);
					stream.method_1(0);
					stream.method_1(0);
					stream.Write(bytes, 0, bytes.Length);
					stream.method_1(101010256);
					stream.method_0(0);
					stream.method_0(0);
					stream.method_0(1);
					stream.method_0(1);
					stream.method_1(46 + bytes.Length);
					stream.method_1((int)((long)(30 + bytes.Length) + totalOut));
					stream.method_0(0);
					stream.Seek(position, SeekOrigin.Begin);
					stream.method_1((int)totalOut);
				}
				else if (int_0 == 1)
				{
					stream.method_1(25000571);
					stream.method_1(byte_0.Length);
					byte[] array4;
					for (int i = 0; i < byte_0.Length; i += array4.Length)
					{
						array4 = new byte[Math.Min(2097151, byte_0.Length - i)];
						Buffer.BlockCopy(byte_0, i, array4, 0, array4.Length);
						long position2 = stream.Position;
						stream.method_1(0);
						stream.method_1(array4.Length);
						Class518.Class524 class2 = new Class518.Class524();
						class2.method_1(array4);
						while (!class2.IsNeedingInput)
						{
							byte[] array5 = new byte[512];
							int num7 = class2.method_2(array5);
							if (num7 <= 0)
							{
								break;
							}
							stream.Write(array5, 0, num7);
						}
						class2.method_0();
						while (!class2.IsFinished)
						{
							byte[] array6 = new byte[512];
							int num8 = class2.method_2(array6);
							if (num8 <= 0)
							{
								break;
							}
							stream.Write(array6, 0, num8);
						}
						long position3 = stream.Position;
						stream.Position = position2;
						stream.method_1((int)class2.TotalOut);
						stream.Position = position3;
					}
				}
				else
				{
					if (int_0 == 2)
					{
						stream.method_1(41777787);
						byte[] array7 = Class518.smethod_7(byte_0, 1, null, null);
						using (ICryptoTransform cryptoTransform = Class518.smethod_2(byte_1, byte_2, false))
						{
							byte[] array8 = cryptoTransform.TransformFinalBlock(array7, 0, array7.Length);
							stream.Write(array8, 0, array8.Length);
							goto IL_44D;
						}
					}
					if (int_0 == 3)
					{
						stream.method_1(58555003);
						byte[] array9 = Class518.smethod_7(byte_0, 1, null, null);
						using (ICryptoTransform cryptoTransform2 = Class518.smethod_1(byte_1, byte_2, false))
						{
							byte[] array10 = cryptoTransform2.TransformFinalBlock(array9, 0, array9.Length);
							stream.Write(array10, 0, array10.Length);
						}
					}
				}
				IL_44D:
				stream.Flush();
				stream.Close();
				result = stream.ToArray();
			}
			catch (Exception ex)
			{
				Class518.string_0 = "ERR 2003: " + ex.Message;
				throw;
			}
			return result;
		}

		// Token: 0x040012ED RID: 4845
		public static string string_0;

		// Token: 0x020003D9 RID: 985
		internal sealed class Class519
		{
			// Token: 0x06002710 RID: 10000 RVA: 0x0000EF35 File Offset: 0x0000D135
			public Class519(byte[] byte_0)
			{
				this.class520_0 = new Class518.Class520();
				this.class521_0 = new Class518.Class521();
				this.int_17 = 2;
				this.class520_0.method_5(byte_0, 0, byte_0.Length);
			}

			// Token: 0x06002711 RID: 10001 RVA: 0x000FD734 File Offset: 0x000FB934
			private bool method_0()
			{
				int i = this.class521_0.method_5();
				while (i >= 258)
				{
					int num;
					switch (this.int_17)
					{
					case 7:
						while (((num = this.class522_0.method_1(this.class520_0)) & -256) == 0)
						{
							this.class521_0.method_0(num);
							if (--i < 258)
							{
								return true;
							}
						}
						if (num >= 257)
						{
							this.int_19 = Class518.Class519.int_0[num - 257];
							this.int_18 = Class518.Class519.int_1[num - 257];
							goto IL_9C;
						}
						if (num < 0)
						{
							return false;
						}
						this.class522_1 = null;
						this.class522_0 = null;
						this.int_17 = 2;
						return true;
					case 8:
						goto IL_9C;
					case 9:
						goto IL_EC;
					case 10:
						break;
					default:
						continue;
					}
					IL_11F:
					if (this.int_18 > 0)
					{
						this.int_17 = 10;
						int num2 = this.class520_0.method_0(this.int_18);
						if (num2 < 0)
						{
							return false;
						}
						this.class520_0.method_1(this.int_18);
						this.int_20 += num2;
					}
					this.class521_0.method_2(this.int_19, this.int_20);
					i -= this.int_19;
					this.int_17 = 7;
					continue;
					IL_EC:
					num = this.class522_1.method_1(this.class520_0);
					if (num >= 0)
					{
						this.int_20 = Class518.Class519.int_2[num];
						this.int_18 = Class518.Class519.int_3[num];
						goto IL_11F;
					}
					return false;
					IL_9C:
					if (this.int_18 > 0)
					{
						this.int_17 = 8;
						int num3 = this.class520_0.method_0(this.int_18);
						if (num3 < 0)
						{
							return false;
						}
						this.class520_0.method_1(this.int_18);
						this.int_19 += num3;
					}
					this.int_17 = 9;
					goto IL_EC;
				}
				return true;
			}

			// Token: 0x06002712 RID: 10002 RVA: 0x000FD91C File Offset: 0x000FBB1C
			private bool method_1()
			{
				switch (this.int_17)
				{
				case 2:
				{
					if (this.bool_0)
					{
						this.int_17 = 12;
						return false;
					}
					int num = this.class520_0.method_0(3);
					if (num < 0)
					{
						return false;
					}
					this.class520_0.method_1(3);
					if ((num & 1) != 0)
					{
						this.bool_0 = true;
					}
					switch (num >> 1)
					{
					case 0:
						this.class520_0.method_2();
						this.int_17 = 3;
						break;
					case 1:
						this.class522_0 = Class518.Class522.class522_0;
						this.class522_1 = Class518.Class522.class522_1;
						this.int_17 = 7;
						break;
					case 2:
						this.class523_0 = new Class518.Class523();
						this.int_17 = 6;
						break;
					}
					return true;
				}
				case 3:
					if ((this.int_21 = this.class520_0.method_0(16)) < 0)
					{
						return false;
					}
					this.class520_0.method_1(16);
					this.int_17 = 4;
					break;
				case 4:
					break;
				case 5:
					goto IL_14A;
				case 6:
					if (!this.class523_0.method_0(this.class520_0))
					{
						return false;
					}
					this.class522_0 = this.class523_0.method_1();
					this.class522_1 = this.class523_0.method_2();
					this.int_17 = 7;
					goto IL_1D4;
				case 7:
				case 8:
				case 9:
				case 10:
					goto IL_1D4;
				case 11:
					goto IL_1DD;
				case 12:
					return false;
				default:
					goto IL_1DD;
				}
				if (this.class520_0.method_0(16) < 0)
				{
					return false;
				}
				this.class520_0.method_1(16);
				this.int_17 = 5;
				IL_14A:
				int num2 = this.class521_0.method_3(this.class520_0, this.int_21);
				this.int_21 -= num2;
				if (this.int_21 == 0)
				{
					this.int_17 = 2;
					return true;
				}
				return !this.class520_0.IsNeedingInput;
				IL_1D4:
				return this.method_0();
				IL_1DD:
				return false;
			}

			// Token: 0x06002713 RID: 10003 RVA: 0x000FDB10 File Offset: 0x000FBD10
			public int method_2(byte[] byte_0, int int_22, int int_23)
			{
				int num = 0;
				for (;;)
				{
					if (this.int_17 != 11)
					{
						int num2 = this.class521_0.method_7(byte_0, int_22, int_23);
						int_22 += num2;
						num += num2;
						int_23 -= num2;
						if (int_23 == 0)
						{
							goto Block_4;
						}
					}
					if (!this.method_1())
					{
						if (this.class521_0.method_6() <= 0)
						{
							break;
						}
						if (this.int_17 == 11)
						{
							break;
						}
					}
				}
				goto IL_58;
				Block_4:
				return num;
				IL_58:
				return num;
			}

			// Token: 0x040012EE RID: 4846
			private static readonly int[] int_0 = new int[]
			{
				3,
				4,
				5,
				6,
				7,
				8,
				9,
				10,
				11,
				13,
				15,
				17,
				19,
				23,
				27,
				31,
				35,
				43,
				51,
				59,
				67,
				83,
				99,
				115,
				131,
				163,
				195,
				227,
				258
			};

			// Token: 0x040012EF RID: 4847
			private static readonly int[] int_1 = new int[]
			{
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				1,
				1,
				1,
				1,
				2,
				2,
				2,
				2,
				3,
				3,
				3,
				3,
				4,
				4,
				4,
				4,
				5,
				5,
				5,
				5,
				0
			};

			// Token: 0x040012F0 RID: 4848
			private static readonly int[] int_2 = new int[]
			{
				1,
				2,
				3,
				4,
				5,
				7,
				9,
				13,
				17,
				25,
				33,
				49,
				65,
				97,
				129,
				193,
				257,
				385,
				513,
				769,
				1025,
				1537,
				2049,
				3073,
				4097,
				6145,
				8193,
				12289,
				16385,
				24577
			};

			// Token: 0x040012F1 RID: 4849
			private static readonly int[] int_3 = new int[]
			{
				0,
				0,
				0,
				0,
				1,
				1,
				2,
				2,
				3,
				3,
				4,
				4,
				5,
				5,
				6,
				6,
				7,
				7,
				8,
				8,
				9,
				9,
				10,
				10,
				11,
				11,
				12,
				12,
				13,
				13
			};

			// Token: 0x040012F2 RID: 4850
			private const int int_4 = 0;

			// Token: 0x040012F3 RID: 4851
			private const int int_5 = 1;

			// Token: 0x040012F4 RID: 4852
			private const int int_6 = 2;

			// Token: 0x040012F5 RID: 4853
			private const int int_7 = 3;

			// Token: 0x040012F6 RID: 4854
			private const int int_8 = 4;

			// Token: 0x040012F7 RID: 4855
			private const int int_9 = 5;

			// Token: 0x040012F8 RID: 4856
			private const int int_10 = 6;

			// Token: 0x040012F9 RID: 4857
			private const int int_11 = 7;

			// Token: 0x040012FA RID: 4858
			private const int int_12 = 8;

			// Token: 0x040012FB RID: 4859
			private const int int_13 = 9;

			// Token: 0x040012FC RID: 4860
			private const int int_14 = 10;

			// Token: 0x040012FD RID: 4861
			private const int int_15 = 11;

			// Token: 0x040012FE RID: 4862
			private const int int_16 = 12;

			// Token: 0x040012FF RID: 4863
			private int int_17;

			// Token: 0x04001300 RID: 4864
			private int int_18;

			// Token: 0x04001301 RID: 4865
			private int int_19;

			// Token: 0x04001302 RID: 4866
			private int int_20;

			// Token: 0x04001303 RID: 4867
			private int int_21;

			// Token: 0x04001304 RID: 4868
			private bool bool_0;

			// Token: 0x04001305 RID: 4869
			private Class518.Class520 class520_0;

			// Token: 0x04001306 RID: 4870
			private Class518.Class521 class521_0;

			// Token: 0x04001307 RID: 4871
			private Class518.Class523 class523_0;

			// Token: 0x04001308 RID: 4872
			private Class518.Class522 class522_0;

			// Token: 0x04001309 RID: 4873
			private Class518.Class522 class522_1;
		}

		// Token: 0x020003DA RID: 986
		internal sealed class Class520
		{
			// Token: 0x06002715 RID: 10005 RVA: 0x000FDBE8 File Offset: 0x000FBDE8
			public int method_0(int int_3)
			{
				if (this.int_2 < int_3)
				{
					if (this.int_0 == this.int_1)
					{
						return -1;
					}
					uint num = this.uint_0;
					byte[] array = this.byte_0;
					int num2 = this.int_0;
					this.int_0 = num2 + 1;
					uint num3 = array[num2] & 255U;
					byte[] array2 = this.byte_0;
					num2 = this.int_0;
					this.int_0 = num2 + 1;
					this.uint_0 = (num | (num3 | (array2[num2] & 255U) << 8) << this.int_2);
					this.int_2 += 16;
				}
				return (int)((ulong)this.uint_0 & (ulong)((long)((1 << int_3) - 1)));
			}

			// Token: 0x06002716 RID: 10006 RVA: 0x0000EF6C File Offset: 0x0000D16C
			public void method_1(int int_3)
			{
				this.uint_0 >>= int_3;
				this.int_2 -= int_3;
			}

			// Token: 0x170006C1 RID: 1729
			// (get) Token: 0x06002717 RID: 10007 RVA: 0x000FDC8C File Offset: 0x000FBE8C
			public int AvailableBits
			{
				get
				{
					return this.int_2;
				}
			}

			// Token: 0x170006C2 RID: 1730
			// (get) Token: 0x06002718 RID: 10008 RVA: 0x000FDCA4 File Offset: 0x000FBEA4
			public int AvailableBytes
			{
				get
				{
					return this.int_1 - this.int_0 + (this.int_2 >> 3);
				}
			}

			// Token: 0x06002719 RID: 10009 RVA: 0x0000EF8F File Offset: 0x0000D18F
			public void method_2()
			{
				this.uint_0 >>= (this.int_2 & 7);
				this.int_2 &= -8;
			}

			// Token: 0x170006C3 RID: 1731
			// (get) Token: 0x0600271A RID: 10010 RVA: 0x000FDCCC File Offset: 0x000FBECC
			public bool IsNeedingInput
			{
				get
				{
					return this.int_0 == this.int_1;
				}
			}

			// Token: 0x0600271B RID: 10011 RVA: 0x000FDCEC File Offset: 0x000FBEEC
			public int method_3(byte[] byte_1, int int_3, int int_4)
			{
				int num = 0;
				while (this.int_2 > 0 && int_4 > 0)
				{
					byte_1[int_3++] = (byte)this.uint_0;
					this.uint_0 >>= 8;
					this.int_2 -= 8;
					int_4--;
					num++;
				}
				int result;
				if (int_4 == 0)
				{
					result = num;
				}
				else
				{
					int num2 = this.int_1 - this.int_0;
					if (int_4 > num2)
					{
						int_4 = num2;
					}
					Array.Copy(this.byte_0, this.int_0, byte_1, int_3, int_4);
					this.int_0 += int_4;
					if ((this.int_0 - this.int_1 & 1) != 0)
					{
						byte[] array = this.byte_0;
						int num3 = this.int_0;
						this.int_0 = num3 + 1;
						this.uint_0 = (array[num3] & 255U);
						this.int_2 = 8;
					}
					result = num + int_4;
				}
				return result;
			}

			// Token: 0x0600271D RID: 10013 RVA: 0x000FDDC0 File Offset: 0x000FBFC0
			public void method_4()
			{
				this.int_2 = 0;
				this.int_1 = 0;
				this.int_0 = 0;
				this.uint_0 = 0U;
			}

			// Token: 0x0600271E RID: 10014 RVA: 0x000FDDF4 File Offset: 0x000FBFF4
			public void method_5(byte[] byte_1, int int_3, int int_4)
			{
				if (this.int_0 < this.int_1)
				{
					throw new InvalidOperationException();
				}
				int num = int_3 + int_4;
				if (0 <= int_3 && int_3 <= num && num <= byte_1.Length)
				{
					if ((int_4 & 1) != 0)
					{
						this.uint_0 |= (uint)((uint)(byte_1[int_3++] & byte.MaxValue) << this.int_2);
						this.int_2 += 8;
					}
					this.byte_0 = byte_1;
					this.int_0 = int_3;
					this.int_1 = num;
					return;
				}
				throw new ArgumentOutOfRangeException();
			}

			// Token: 0x0400130A RID: 4874
			private byte[] byte_0;

			// Token: 0x0400130B RID: 4875
			private int int_0;

			// Token: 0x0400130C RID: 4876
			private int int_1;

			// Token: 0x0400130D RID: 4877
			private uint uint_0;

			// Token: 0x0400130E RID: 4878
			private int int_2;
		}

		// Token: 0x020003DB RID: 987
		internal sealed class Class521
		{
			// Token: 0x0600271F RID: 10015 RVA: 0x000FDE7C File Offset: 0x000FC07C
			public void method_0(int int_4)
			{
				int num = this.int_3;
				this.int_3 = num + 1;
				if (num == 32768)
				{
					throw new InvalidOperationException();
				}
				byte[] array = this.byte_0;
				num = this.int_2;
				this.int_2 = num + 1;
				array[num] = (byte)int_4;
				this.int_2 &= 32767;
			}

			// Token: 0x06002720 RID: 10016 RVA: 0x000FDED8 File Offset: 0x000FC0D8
			private void method_1(int int_4, int int_5, int int_6)
			{
				while (int_5-- > 0)
				{
					byte[] array = this.byte_0;
					int num = this.int_2;
					this.int_2 = num + 1;
					array[num] = this.byte_0[int_4++];
					this.int_2 &= 32767;
					int_4 &= 32767;
				}
			}

			// Token: 0x06002721 RID: 10017 RVA: 0x000FDF34 File Offset: 0x000FC134
			public void method_2(int int_4, int int_5)
			{
				if ((this.int_3 += int_4) > 32768)
				{
					throw new InvalidOperationException();
				}
				int num = this.int_2 - int_5 & 32767;
				int num2 = 32768 - int_4;
				if (num <= num2 && this.int_2 < num2)
				{
					if (int_4 <= int_5)
					{
						Array.Copy(this.byte_0, num, this.byte_0, this.int_2, int_4);
						this.int_2 += int_4;
					}
					else
					{
						while (int_4-- > 0)
						{
							byte[] array = this.byte_0;
							int num3 = this.int_2;
							this.int_2 = num3 + 1;
							array[num3] = this.byte_0[num++];
						}
					}
				}
				else
				{
					this.method_1(num, int_4, int_5);
				}
			}

			// Token: 0x06002722 RID: 10018 RVA: 0x000FDFEC File Offset: 0x000FC1EC
			public int method_3(Class518.Class520 class520_0, int int_4)
			{
				int_4 = Math.Min(Math.Min(int_4, 32768 - this.int_3), class520_0.AvailableBytes);
				int num = 32768 - this.int_2;
				int num2;
				if (int_4 > num)
				{
					num2 = class520_0.method_3(this.byte_0, this.int_2, num);
					if (num2 == num)
					{
						num2 += class520_0.method_3(this.byte_0, 0, int_4 - num);
					}
				}
				else
				{
					num2 = class520_0.method_3(this.byte_0, this.int_2, int_4);
				}
				this.int_2 = (this.int_2 + num2 & 32767);
				this.int_3 += num2;
				return num2;
			}

			// Token: 0x06002723 RID: 10019 RVA: 0x000FE094 File Offset: 0x000FC294
			public void method_4(byte[] byte_1, int int_4, int int_5)
			{
				if (this.int_3 > 0)
				{
					throw new InvalidOperationException();
				}
				if (int_5 > 32768)
				{
					int_4 += int_5 - 32768;
					int_5 = 32768;
				}
				Array.Copy(byte_1, int_4, this.byte_0, 0, int_5);
				this.int_2 = (int_5 & 32767);
			}

			// Token: 0x06002724 RID: 10020 RVA: 0x000FE0E8 File Offset: 0x000FC2E8
			public int method_5()
			{
				return 32768 - this.int_3;
			}

			// Token: 0x06002725 RID: 10021 RVA: 0x000FE108 File Offset: 0x000FC308
			public int method_6()
			{
				return this.int_3;
			}

			// Token: 0x06002726 RID: 10022 RVA: 0x000FE120 File Offset: 0x000FC320
			public int method_7(byte[] byte_1, int int_4, int int_5)
			{
				int num = this.int_2;
				if (int_5 > this.int_3)
				{
					int_5 = this.int_3;
				}
				else
				{
					num = (this.int_2 - this.int_3 + int_5 & 32767);
				}
				int num2 = int_5;
				int num3 = int_5 - num;
				if (num3 > 0)
				{
					Array.Copy(this.byte_0, 32768 - num3, byte_1, int_4, num3);
					int_4 += num3;
					int_5 = num;
				}
				Array.Copy(this.byte_0, num - int_5, byte_1, int_4, int_5);
				this.int_3 -= num2;
				if (this.int_3 < 0)
				{
					throw new InvalidOperationException();
				}
				return num2;
			}

			// Token: 0x06002727 RID: 10023 RVA: 0x000FE1B8 File Offset: 0x000FC3B8
			public void method_8()
			{
				this.int_2 = 0;
				this.int_3 = 0;
			}

			// Token: 0x0400130F RID: 4879
			private const int int_0 = 32768;

			// Token: 0x04001310 RID: 4880
			private const int int_1 = 32767;

			// Token: 0x04001311 RID: 4881
			private byte[] byte_0 = new byte[32768];

			// Token: 0x04001312 RID: 4882
			private int int_2;

			// Token: 0x04001313 RID: 4883
			private int int_3;
		}

		// Token: 0x020003DC RID: 988
		internal sealed class Class522
		{
			// Token: 0x06002729 RID: 10025 RVA: 0x000FE1D8 File Offset: 0x000FC3D8
			static Class522()
			{
				byte[] array = new byte[288];
				int i = 0;
				while (i < 144)
				{
					array[i++] = 8;
				}
				while (i < 256)
				{
					array[i++] = 9;
				}
				while (i < 280)
				{
					array[i++] = 7;
				}
				while (i < 288)
				{
					array[i++] = 8;
				}
				Class518.Class522.class522_0 = new Class518.Class522(array);
				array = new byte[32];
				i = 0;
				while (i < 32)
				{
					array[i++] = 5;
				}
				Class518.Class522.class522_1 = new Class518.Class522(array);
			}

			// Token: 0x0600272A RID: 10026 RVA: 0x0000EFD2 File Offset: 0x0000D1D2
			public Class522(byte[] byte_0)
			{
				this.method_0(byte_0);
			}

			// Token: 0x0600272B RID: 10027 RVA: 0x000FE26C File Offset: 0x000FC46C
			private void method_0(byte[] byte_0)
			{
				int[] array = new int[16];
				int[] array2 = new int[16];
				foreach (int num in byte_0)
				{
					if (num > 0)
					{
						array[num]++;
					}
				}
				int num2 = 0;
				int num3 = 512;
				for (int j = 1; j <= 15; j++)
				{
					array2[j] = num2;
					num2 += array[j] << 16 - j;
					if (j >= 10)
					{
						int num4 = array2[j] & 130944;
						int num5 = num2 & 130944;
						num3 += num5 - num4 >> 16 - j;
					}
				}
				this.short_0 = new short[num3];
				int num6 = 512;
				for (int k = 15; k >= 10; k--)
				{
					int num7 = num2 & 130944;
					num2 -= array[k] << 16 - k;
					for (int l = num2 & 130944; l < num7; l += 128)
					{
						this.short_0[(int)Class518.Class525.smethod_0(l)] = (short)(-num6 << 4 | k);
						num6 += 1 << k - 9;
					}
				}
				for (int m = 0; m < byte_0.Length; m++)
				{
					int num8 = (int)byte_0[m];
					if (num8 != 0)
					{
						num2 = array2[num8];
						int num9 = (int)Class518.Class525.smethod_0(num2);
						if (num8 <= 9)
						{
							do
							{
								this.short_0[num9] = (short)(m << 4 | num8);
								num9 += 1 << num8;
							}
							while (num9 < 512);
						}
						else
						{
							int num10 = (int)this.short_0[num9 & 511];
							int num11 = 1 << (num10 & 15);
							num10 = -(num10 >> 4);
							do
							{
								this.short_0[num10 | num9 >> 9] = (short)(m << 4 | num8);
								num9 += 1 << num8;
							}
							while (num9 < num11);
						}
						array2[num8] = num2 + (1 << 16 - num8);
					}
				}
			}

			// Token: 0x0600272C RID: 10028 RVA: 0x000FE44C File Offset: 0x000FC64C
			public int method_1(Class518.Class520 class520_0)
			{
				int num;
				int result;
				if ((num = class520_0.method_0(9)) >= 0)
				{
					int num2;
					if ((num2 = (int)this.short_0[num]) >= 0)
					{
						class520_0.method_1(num2 & 15);
						result = num2 >> 4;
					}
					else
					{
						int num3 = -(num2 >> 4);
						int int_ = num2 & 15;
						if ((num = class520_0.method_0(int_)) >= 0)
						{
							num2 = (int)this.short_0[num3 | num >> 9];
							class520_0.method_1(num2 & 15);
							result = num2 >> 4;
						}
						else
						{
							int availableBits = class520_0.AvailableBits;
							num = class520_0.method_0(availableBits);
							num2 = (int)this.short_0[num3 | num >> 9];
							if ((num2 & 15) <= availableBits)
							{
								class520_0.method_1(num2 & 15);
								result = num2 >> 4;
							}
							else
							{
								result = -1;
							}
						}
					}
				}
				else
				{
					int availableBits2 = class520_0.AvailableBits;
					num = class520_0.method_0(availableBits2);
					int num2 = (int)this.short_0[num];
					if (num2 >= 0 && (num2 & 15) <= availableBits2)
					{
						class520_0.method_1(num2 & 15);
						result = num2 >> 4;
					}
					else
					{
						result = -1;
					}
				}
				return result;
			}

			// Token: 0x04001314 RID: 4884
			private const int int_0 = 15;

			// Token: 0x04001315 RID: 4885
			private short[] short_0;

			// Token: 0x04001316 RID: 4886
			public static readonly Class518.Class522 class522_0;

			// Token: 0x04001317 RID: 4887
			public static readonly Class518.Class522 class522_1;
		}

		// Token: 0x020003DD RID: 989
		internal sealed class Class523
		{
			// Token: 0x0600272E RID: 10030 RVA: 0x000FE53C File Offset: 0x000FC73C
			public bool method_0(Class518.Class520 class520_0)
			{
				for (;;)
				{
					switch (this.int_8)
					{
					case 0:
						this.int_9 = class520_0.method_0(5);
						if (this.int_9 >= 0)
						{
							this.int_9 += 257;
							class520_0.method_1(5);
							this.int_8 = 1;
							goto IL_1DE;
						}
						goto IL_27D;
					case 1:
						goto IL_1DE;
					case 2:
						goto IL_190;
					case 3:
						goto IL_157;
					case 4:
						break;
					case 5:
						goto IL_2A;
					default:
						continue;
					}
					IL_E2:
					int num;
					while (((num = this.class522_0.method_1(class520_0)) & -16) == 0)
					{
						byte[] array = this.byte_1;
						int num2 = this.int_14;
						this.int_14 = num2 + 1;
						array[num2] = (this.byte_2 = (byte)num);
						if (this.int_14 == this.int_12)
						{
							goto Block_4;
						}
					}
					if (num >= 0)
					{
						if (num >= 17)
						{
							this.byte_2 = 0;
						}
						this.int_13 = num - 16;
						this.int_8 = 5;
						goto IL_2A;
					}
					goto IL_291;
					IL_157:
					while (this.int_14 < this.int_11)
					{
						int num3 = class520_0.method_0(3);
						if (num3 < 0)
						{
							goto IL_28C;
						}
						class520_0.method_1(3);
						this.byte_0[Class518.Class523.int_15[this.int_14]] = (byte)num3;
						this.int_14++;
					}
					this.class522_0 = new Class518.Class522(this.byte_0);
					this.byte_0 = null;
					this.int_14 = 0;
					this.int_8 = 4;
					goto IL_E2;
					IL_2A:
					int num4 = Class518.Class523.int_7[this.int_13];
					int num5 = class520_0.method_0(num4);
					if (num5 < 0)
					{
						goto IL_29B;
					}
					class520_0.method_1(num4);
					num5 += Class518.Class523.int_6[this.int_13];
					while (num5-- > 0)
					{
						byte[] array2 = this.byte_1;
						int num2 = this.int_14;
						this.int_14 = num2 + 1;
						array2[num2] = this.byte_2;
					}
					if (this.int_14 == this.int_12)
					{
						break;
					}
					this.int_8 = 4;
					continue;
					IL_190:
					this.int_11 = class520_0.method_0(4);
					if (this.int_11 >= 0)
					{
						this.int_11 += 4;
						class520_0.method_1(4);
						this.byte_0 = new byte[19];
						this.int_14 = 0;
						this.int_8 = 3;
						goto IL_157;
					}
					goto IL_287;
					IL_1DE:
					this.int_10 = class520_0.method_0(5);
					if (this.int_10 >= 0)
					{
						this.int_10++;
						class520_0.method_1(5);
						this.int_12 = this.int_9 + this.int_10;
						this.byte_1 = new byte[this.int_12];
						this.int_8 = 2;
						goto IL_190;
					}
					goto IL_282;
				}
				return true;
				Block_4:
				return true;
				IL_27D:
				return false;
				IL_282:
				return false;
				IL_287:
				return false;
				IL_28C:
				return false;
				IL_291:
				return false;
				IL_29B:
				return false;
			}

			// Token: 0x0600272F RID: 10031 RVA: 0x000FE7F0 File Offset: 0x000FC9F0
			public Class518.Class522 method_1()
			{
				byte[] destinationArray = new byte[this.int_9];
				Array.Copy(this.byte_1, 0, destinationArray, 0, this.int_9);
				return new Class518.Class522(destinationArray);
			}

			// Token: 0x06002730 RID: 10032 RVA: 0x000FE828 File Offset: 0x000FCA28
			public Class518.Class522 method_2()
			{
				byte[] destinationArray = new byte[this.int_10];
				Array.Copy(this.byte_1, this.int_9, destinationArray, 0, this.int_10);
				return new Class518.Class522(destinationArray);
			}

			// Token: 0x04001318 RID: 4888
			private const int int_0 = 0;

			// Token: 0x04001319 RID: 4889
			private const int int_1 = 1;

			// Token: 0x0400131A RID: 4890
			private const int int_2 = 2;

			// Token: 0x0400131B RID: 4891
			private const int int_3 = 3;

			// Token: 0x0400131C RID: 4892
			private const int int_4 = 4;

			// Token: 0x0400131D RID: 4893
			private const int int_5 = 5;

			// Token: 0x0400131E RID: 4894
			private static readonly int[] int_6 = new int[]
			{
				3,
				3,
				11
			};

			// Token: 0x0400131F RID: 4895
			private static readonly int[] int_7 = new int[]
			{
				2,
				3,
				7
			};

			// Token: 0x04001320 RID: 4896
			private byte[] byte_0;

			// Token: 0x04001321 RID: 4897
			private byte[] byte_1;

			// Token: 0x04001322 RID: 4898
			private Class518.Class522 class522_0;

			// Token: 0x04001323 RID: 4899
			private int int_8;

			// Token: 0x04001324 RID: 4900
			private int int_9;

			// Token: 0x04001325 RID: 4901
			private int int_10;

			// Token: 0x04001326 RID: 4902
			private int int_11;

			// Token: 0x04001327 RID: 4903
			private int int_12;

			// Token: 0x04001328 RID: 4904
			private int int_13;

			// Token: 0x04001329 RID: 4905
			private byte byte_2;

			// Token: 0x0400132A RID: 4906
			private int int_14;

			// Token: 0x0400132B RID: 4907
			private static readonly int[] int_15 = new int[]
			{
				16,
				17,
				18,
				0,
				8,
				7,
				9,
				6,
				10,
				5,
				11,
				4,
				12,
				3,
				13,
				2,
				14,
				1,
				15
			};
		}

		// Token: 0x020003DE RID: 990
		internal sealed class Class524
		{
			// Token: 0x06002732 RID: 10034 RVA: 0x0000EFE3 File Offset: 0x0000D1E3
			public Class524()
			{
				this.class528_0 = new Class518.Class528();
				this.class527_0 = new Class518.Class527(this.class528_0);
			}

			// Token: 0x170006C4 RID: 1732
			// (get) Token: 0x06002733 RID: 10035 RVA: 0x000FE8B8 File Offset: 0x000FCAB8
			public long TotalOut
			{
				get
				{
					return this.long_0;
				}
			}

			// Token: 0x06002734 RID: 10036 RVA: 0x0000F011 File Offset: 0x0000D211
			public void method_0()
			{
				this.int_6 |= 12;
			}

			// Token: 0x170006C5 RID: 1733
			// (get) Token: 0x06002735 RID: 10037 RVA: 0x000FE8D0 File Offset: 0x000FCAD0
			public bool IsFinished
			{
				get
				{
					bool result;
					if (this.int_6 == 30)
					{
						result = this.class528_0.IsFlushed;
					}
					else
					{
						result = false;
					}
					return result;
				}
			}

			// Token: 0x170006C6 RID: 1734
			// (get) Token: 0x06002736 RID: 10038 RVA: 0x000FE8FC File Offset: 0x000FCAFC
			public bool IsNeedingInput
			{
				get
				{
					return this.class527_0.method_8();
				}
			}

			// Token: 0x06002737 RID: 10039 RVA: 0x0000F024 File Offset: 0x0000D224
			public void method_1(byte[] byte_0)
			{
				this.class527_0.method_7(byte_0);
			}

			// Token: 0x06002738 RID: 10040 RVA: 0x000FE918 File Offset: 0x000FCB18
			public int method_2(byte[] byte_0)
			{
				int num = 0;
				int num2 = byte_0.Length;
				int num3 = num2;
				for (;;)
				{
					int num4 = this.class528_0.method_4(byte_0, num, num2);
					num += num4;
					this.long_0 += (long)num4;
					num2 -= num4;
					if (num2 == 0 || this.int_6 == 30)
					{
						goto IL_E0;
					}
					if (!this.class527_0.method_6((this.int_6 & 4) != 0, (this.int_6 & 8) != 0))
					{
						if (this.int_6 == 16)
						{
							break;
						}
						if (this.int_6 == 20)
						{
							for (int i = 8 + (-this.class528_0.BitCount & 7); i > 0; i -= 10)
							{
								this.class528_0.method_3(2, 10);
							}
							this.int_6 = 16;
						}
						else if (this.int_6 == 28)
						{
							this.class528_0.method_2();
							this.int_6 = 30;
						}
					}
				}
				return num3 - num2;
				IL_E0:
				return num3 - num2;
			}

			// Token: 0x0400132C RID: 4908
			private const int int_0 = 4;

			// Token: 0x0400132D RID: 4909
			private const int int_1 = 8;

			// Token: 0x0400132E RID: 4910
			private const int int_2 = 16;

			// Token: 0x0400132F RID: 4911
			private const int int_3 = 20;

			// Token: 0x04001330 RID: 4912
			private const int int_4 = 28;

			// Token: 0x04001331 RID: 4913
			private const int int_5 = 30;

			// Token: 0x04001332 RID: 4914
			private int int_6 = 16;

			// Token: 0x04001333 RID: 4915
			private long long_0;

			// Token: 0x04001334 RID: 4916
			private Class518.Class528 class528_0;

			// Token: 0x04001335 RID: 4917
			private Class518.Class527 class527_0;
		}

		// Token: 0x020003DF RID: 991
		internal sealed class Class525
		{
			// Token: 0x06002739 RID: 10041 RVA: 0x000FEA10 File Offset: 0x000FCC10
			public static short smethod_0(int int_11)
			{
				return (short)((int)Class518.Class525.byte_0[int_11 & 15] << 12 | (int)Class518.Class525.byte_0[int_11 >> 4 & 15] << 8 | (int)Class518.Class525.byte_0[int_11 >> 8 & 15] << 4 | (int)Class518.Class525.byte_0[int_11 >> 12]);
			}

			// Token: 0x0600273A RID: 10042 RVA: 0x000FEA58 File Offset: 0x000FCC58
			static Class525()
			{
				int i = 0;
				while (i < 144)
				{
					Class518.Class525.short_1[i] = Class518.Class525.smethod_0(48 + i << 8);
					Class518.Class525.byte_2[i++] = 8;
				}
				while (i < 256)
				{
					Class518.Class525.short_1[i] = Class518.Class525.smethod_0(256 + i << 7);
					Class518.Class525.byte_2[i++] = 9;
				}
				while (i < 280)
				{
					Class518.Class525.short_1[i] = Class518.Class525.smethod_0(-256 + i << 9);
					Class518.Class525.byte_2[i++] = 7;
				}
				while (i < 286)
				{
					Class518.Class525.short_1[i] = Class518.Class525.smethod_0(-88 + i << 8);
					Class518.Class525.byte_2[i++] = 8;
				}
				Class518.Class525.short_2 = new short[30];
				Class518.Class525.byte_3 = new byte[30];
				for (i = 0; i < 30; i++)
				{
					Class518.Class525.short_2[i] = Class518.Class525.smethod_0(i << 11);
					Class518.Class525.byte_3[i] = 5;
				}
			}

			// Token: 0x0600273B RID: 10043 RVA: 0x000FEB9C File Offset: 0x000FCD9C
			public Class525(Class518.Class528 class528_1)
			{
				this.class528_0 = class528_1;
				this.class526_0 = new Class518.Class525.Class526(this, 286, 257, 15);
				this.class526_1 = new Class518.Class525.Class526(this, 30, 1, 15);
				this.class526_2 = new Class518.Class525.Class526(this, 19, 4, 7);
				this.short_0 = new short[16384];
				this.byte_1 = new byte[16384];
			}

			// Token: 0x0600273C RID: 10044 RVA: 0x0000F034 File Offset: 0x0000D234
			public void method_0()
			{
				this.int_9 = 0;
				this.int_10 = 0;
			}

			// Token: 0x0600273D RID: 10045 RVA: 0x000FEC14 File Offset: 0x000FCE14
			private int method_1(int int_11)
			{
				int result;
				if (int_11 == 255)
				{
					result = 285;
				}
				else
				{
					int num = 257;
					while (int_11 >= 8)
					{
						num += 4;
						int_11 >>= 1;
					}
					result = num + int_11;
				}
				return result;
			}

			// Token: 0x0600273E RID: 10046 RVA: 0x000FEC50 File Offset: 0x000FCE50
			private int method_2(int int_11)
			{
				int num = 0;
				while (int_11 >= 4)
				{
					num += 2;
					int_11 >>= 1;
				}
				return num + int_11;
			}

			// Token: 0x0600273F RID: 10047 RVA: 0x000FEC78 File Offset: 0x000FCE78
			public void method_3(int int_11)
			{
				this.class526_2.method_2();
				this.class526_0.method_2();
				this.class526_1.method_2();
				this.class528_0.method_3(this.class526_0.int_1 - 257, 5);
				this.class528_0.method_3(this.class526_1.int_1 - 1, 5);
				this.class528_0.method_3(int_11 - 4, 4);
				for (int i = 0; i < int_11; i++)
				{
					this.class528_0.method_3((int)this.class526_2.byte_0[Class518.Class525.int_8[i]], 3);
				}
				this.class526_0.method_7(this.class526_2);
				this.class526_1.method_7(this.class526_2);
			}

			// Token: 0x06002740 RID: 10048 RVA: 0x000FED3C File Offset: 0x000FCF3C
			public void method_4()
			{
				for (int i = 0; i < this.int_9; i++)
				{
					int num = (int)(this.byte_1[i] & byte.MaxValue);
					int num2 = (int)this.short_0[i];
					if (num2-- != 0)
					{
						int num3 = this.method_1(num);
						this.class526_0.method_0(num3);
						int num4 = (num3 - 261) / 4;
						if (num4 > 0 && num4 <= 5)
						{
							this.class528_0.method_3(num & (1 << num4) - 1, num4);
						}
						int num5 = this.method_2(num2);
						this.class526_1.method_0(num5);
						num4 = num5 / 2 - 1;
						if (num4 > 0)
						{
							this.class528_0.method_3(num2 & (1 << num4) - 1, num4);
						}
					}
					else
					{
						this.class526_0.method_0(num);
					}
				}
				this.class526_0.method_0(256);
			}

			// Token: 0x06002741 RID: 10049 RVA: 0x000FEE1C File Offset: 0x000FD01C
			public void method_5(byte[] byte_4, int int_11, int int_12, bool bool_0)
			{
				this.class528_0.method_3(bool_0 ? 1 : 0, 3);
				this.class528_0.method_2();
				this.class528_0.method_0(int_12);
				this.class528_0.method_0(~int_12);
				this.class528_0.method_1(byte_4, int_11, int_12);
				this.method_0();
			}

			// Token: 0x06002742 RID: 10050 RVA: 0x000FEE78 File Offset: 0x000FD078
			public void method_6(byte[] byte_4, int int_11, int int_12, bool bool_0)
			{
				short[] array = this.class526_0.short_0;
				int num = 256;
				array[num] += 1;
				this.class526_0.method_4();
				this.class526_1.method_4();
				this.class526_0.method_6(this.class526_2);
				this.class526_1.method_6(this.class526_2);
				this.class526_2.method_4();
				int num2 = 4;
				for (int i = 18; i > num2; i--)
				{
					if (this.class526_2.byte_0[Class518.Class525.int_8[i]] > 0)
					{
						num2 = i + 1;
					}
				}
				int num3 = 14 + num2 * 3 + this.class526_2.method_5() + this.class526_0.method_5() + this.class526_1.method_5() + this.int_10;
				int num4 = this.int_10;
				for (int j = 0; j < 286; j++)
				{
					num4 += (int)(this.class526_0.short_0[j] * (short)Class518.Class525.byte_2[j]);
				}
				for (int k = 0; k < 30; k++)
				{
					num4 += (int)(this.class526_1.short_0[k] * (short)Class518.Class525.byte_3[k]);
				}
				if (num3 >= num4)
				{
					num3 = num4;
				}
				if (int_11 >= 0 && int_12 + 4 < num3 >> 3)
				{
					this.method_5(byte_4, int_11, int_12, bool_0);
				}
				else if (num3 == num4)
				{
					this.class528_0.method_3(2 + (bool_0 ? 1 : 0), 3);
					this.class526_0.method_1(Class518.Class525.short_1, Class518.Class525.byte_2);
					this.class526_1.method_1(Class518.Class525.short_2, Class518.Class525.byte_3);
					this.method_4();
					this.method_0();
				}
				else
				{
					this.class528_0.method_3(4 + (bool_0 ? 1 : 0), 3);
					this.method_3(num2);
					this.method_4();
					this.method_0();
				}
			}

			// Token: 0x06002743 RID: 10051 RVA: 0x000FF03C File Offset: 0x000FD23C
			public bool method_7()
			{
				return this.int_9 >= 16384;
			}

			// Token: 0x06002744 RID: 10052 RVA: 0x000FF060 File Offset: 0x000FD260
			public bool method_8(int int_11)
			{
				this.short_0[this.int_9] = 0;
				byte[] array = this.byte_1;
				int num = this.int_9;
				this.int_9 = num + 1;
				array[num] = (byte)int_11;
				short[] array2 = this.class526_0.short_0;
				array2[int_11] += 1;
				return this.method_7();
			}

			// Token: 0x06002745 RID: 10053 RVA: 0x000FF0B8 File Offset: 0x000FD2B8
			public bool method_9(int int_11, int int_12)
			{
				this.short_0[this.int_9] = (short)int_11;
				byte[] array = this.byte_1;
				int num = this.int_9;
				this.int_9 = num + 1;
				array[num] = (byte)(int_12 - 3);
				int num2 = this.method_1(int_12 - 3);
				short[] array2 = this.class526_0.short_0;
				int num3 = num2;
				array2[num3] += 1;
				if (num2 >= 265 && num2 < 285)
				{
					this.int_10 += (num2 - 261) / 4;
				}
				int num4 = this.method_2(int_11 - 1);
				short[] array3 = this.class526_1.short_0;
				int num5 = num4;
				array3[num5] += 1;
				if (num4 >= 4)
				{
					this.int_10 += num4 / 2 - 1;
				}
				return this.method_7();
			}

			// Token: 0x04001336 RID: 4918
			private const int int_0 = 16384;

			// Token: 0x04001337 RID: 4919
			private const int int_1 = 286;

			// Token: 0x04001338 RID: 4920
			private const int int_2 = 30;

			// Token: 0x04001339 RID: 4921
			private const int int_3 = 19;

			// Token: 0x0400133A RID: 4922
			private const int int_4 = 16;

			// Token: 0x0400133B RID: 4923
			private const int int_5 = 17;

			// Token: 0x0400133C RID: 4924
			private const int int_6 = 18;

			// Token: 0x0400133D RID: 4925
			private const int int_7 = 256;

			// Token: 0x0400133E RID: 4926
			private static readonly int[] int_8 = new int[]
			{
				16,
				17,
				18,
				0,
				8,
				7,
				9,
				6,
				10,
				5,
				11,
				4,
				12,
				3,
				13,
				2,
				14,
				1,
				15
			};

			// Token: 0x0400133F RID: 4927
			private static readonly byte[] byte_0 = new byte[]
			{
				0,
				8,
				4,
				12,
				2,
				10,
				6,
				14,
				1,
				9,
				5,
				13,
				3,
				11,
				7,
				15
			};

			// Token: 0x04001340 RID: 4928
			private Class518.Class528 class528_0;

			// Token: 0x04001341 RID: 4929
			private Class518.Class525.Class526 class526_0;

			// Token: 0x04001342 RID: 4930
			private Class518.Class525.Class526 class526_1;

			// Token: 0x04001343 RID: 4931
			private Class518.Class525.Class526 class526_2;

			// Token: 0x04001344 RID: 4932
			private short[] short_0;

			// Token: 0x04001345 RID: 4933
			private byte[] byte_1;

			// Token: 0x04001346 RID: 4934
			private int int_9;

			// Token: 0x04001347 RID: 4935
			private int int_10;

			// Token: 0x04001348 RID: 4936
			private static readonly short[] short_1 = new short[286];

			// Token: 0x04001349 RID: 4937
			private static readonly byte[] byte_2 = new byte[286];

			// Token: 0x0400134A RID: 4938
			private static readonly short[] short_2;

			// Token: 0x0400134B RID: 4939
			private static readonly byte[] byte_3;

			// Token: 0x020003E0 RID: 992
			public sealed class Class526
			{
				// Token: 0x06002746 RID: 10054 RVA: 0x0000F046 File Offset: 0x0000D246
				public Class526(Class518.Class525 class525_1, int int_4, int int_5, int int_6)
				{
					this.class525_0 = class525_1;
					this.int_0 = int_5;
					this.int_3 = int_6;
					this.short_0 = new short[int_4];
					this.int_2 = new int[int_6];
				}

				// Token: 0x06002747 RID: 10055 RVA: 0x0000F07F File Offset: 0x0000D27F
				public void method_0(int int_4)
				{
					this.class525_0.class528_0.method_3((int)this.short_1[int_4] & 65535, (int)this.byte_0[int_4]);
				}

				// Token: 0x06002748 RID: 10056 RVA: 0x0000F0A9 File Offset: 0x0000D2A9
				public void method_1(short[] short_2, byte[] byte_1)
				{
					this.short_1 = short_2;
					this.byte_0 = byte_1;
				}

				// Token: 0x06002749 RID: 10057 RVA: 0x000FF178 File Offset: 0x000FD378
				public void method_2()
				{
					int[] array = new int[this.int_3];
					int num = 0;
					this.short_1 = new short[this.short_0.Length];
					for (int i = 0; i < this.int_3; i++)
					{
						array[i] = num;
						num += this.int_2[i] << 15 - i;
					}
					for (int j = 0; j < this.int_1; j++)
					{
						int num2 = (int)this.byte_0[j];
						if (num2 > 0)
						{
							this.short_1[j] = Class518.Class525.smethod_0(array[num2 - 1]);
							array[num2 - 1] += 1 << 16 - num2;
						}
					}
				}

				// Token: 0x0600274A RID: 10058 RVA: 0x000FF21C File Offset: 0x000FD41C
				private void method_3(int[] int_4)
				{
					this.byte_0 = new byte[this.short_0.Length];
					int num = int_4.Length / 2;
					int num2 = (num + 1) / 2;
					int num3 = 0;
					for (int i = 0; i < this.int_3; i++)
					{
						this.int_2[i] = 0;
					}
					int[] array = new int[num];
					array[num - 1] = 0;
					for (int j = num - 1; j >= 0; j--)
					{
						if (int_4[2 * j + 1] != -1)
						{
							int num4 = array[j] + 1;
							if (num4 > this.int_3)
							{
								num4 = this.int_3;
								num3++;
							}
							array[int_4[2 * j]] = (array[int_4[2 * j + 1]] = num4);
						}
						else
						{
							int num5 = array[j];
							this.int_2[num5 - 1]++;
							this.byte_0[int_4[2 * j]] = (byte)array[j];
						}
					}
					if (num3 != 0)
					{
						int num6 = this.int_3 - 1;
						for (;;)
						{
							if (this.int_2[--num6] != 0)
							{
								do
								{
									this.int_2[num6]--;
									this.int_2[++num6]++;
									num3 -= 1 << this.int_3 - 1 - num6;
								}
								while (num3 > 0 && num6 < this.int_3 - 1);
								if (num3 <= 0)
								{
									break;
								}
							}
						}
						this.int_2[this.int_3 - 1] += num3;
						this.int_2[this.int_3 - 2] -= num3;
						int num7 = 2 * num2;
						for (int num8 = this.int_3; num8 != 0; num8--)
						{
							int k = this.int_2[num8 - 1];
							while (k > 0)
							{
								int num9 = 2 * int_4[num7++];
								if (int_4[num9 + 1] == -1)
								{
									this.byte_0[int_4[num9]] = (byte)num8;
									k--;
								}
							}
						}
					}
				}

				// Token: 0x0600274B RID: 10059 RVA: 0x000FF3F8 File Offset: 0x000FD5F8
				public void method_4()
				{
					int num = this.short_0.Length;
					int[] array = new int[num];
					int i = 0;
					int num2 = 0;
					for (int j = 0; j < num; j++)
					{
						int num3 = (int)this.short_0[j];
						if (num3 != 0)
						{
							int num4 = i++;
							int num5;
							while (num4 > 0 && (int)this.short_0[array[num5 = (num4 - 1) / 2]] > num3)
							{
								array[num4] = array[num5];
								num4 = num5;
							}
							array[num4] = j;
							num2 = j;
						}
					}
					while (i < 2)
					{
						int num6 = (num2 < 2) ? (++num2) : 0;
						array[i++] = num6;
					}
					this.int_1 = Math.Max(num2 + 1, this.int_0);
					int num7 = i;
					int[] array2 = new int[4 * i - 2];
					int[] array3 = new int[2 * i - 1];
					int num8 = num7;
					for (int k = 0; k < i; k++)
					{
						int num9 = array[k];
						array2[2 * k] = num9;
						array2[2 * k + 1] = -1;
						array3[k] = (int)this.short_0[num9] << 8;
						array[k] = k;
					}
					do
					{
						int num10 = array[0];
						int num11 = array[--i];
						int num12 = 0;
						int l;
						for (l = 1; l < i; l = l * 2 + 1)
						{
							if (l + 1 < i && array3[array[l]] > array3[array[l + 1]])
							{
								l++;
							}
							array[num12] = array[l];
							num12 = l;
						}
						int num13 = array3[num11];
						while ((l = num12) > 0 && array3[array[num12 = (l - 1) / 2]] > num13)
						{
							array[l] = array[num12];
						}
						array[l] = num11;
						int num14 = array[0];
						num11 = num8++;
						array2[2 * num11] = num10;
						array2[2 * num11 + 1] = num14;
						int num15 = Math.Min(array3[num10] & 255, array3[num14] & 255);
						num13 = (array3[num11] = array3[num10] + array3[num14] - num15 + 1);
						num12 = 0;
						for (l = 1; l < i; l = num12 * 2 + 1)
						{
							if (l + 1 < i && array3[array[l]] > array3[array[l + 1]])
							{
								l++;
							}
							array[num12] = array[l];
							num12 = l;
						}
						while ((l = num12) > 0 && array3[array[num12 = (l - 1) / 2]] > num13)
						{
							array[l] = array[num12];
						}
						array[l] = num11;
					}
					while (i > 1);
					this.method_3(array2);
				}

				// Token: 0x0600274C RID: 10060 RVA: 0x000FF654 File Offset: 0x000FD854
				public int method_5()
				{
					int num = 0;
					for (int i = 0; i < this.short_0.Length; i++)
					{
						num += (int)(this.short_0[i] * (short)this.byte_0[i]);
					}
					return num;
				}

				// Token: 0x0600274D RID: 10061 RVA: 0x000FF690 File Offset: 0x000FD890
				public void method_6(Class518.Class525.Class526 class526_0)
				{
					int num = -1;
					int i = 0;
					while (i < this.int_1)
					{
						int num2 = 1;
						int num3 = (int)this.byte_0[i];
						int num4;
						int num5;
						if (num3 == 0)
						{
							num4 = 138;
							num5 = 3;
						}
						else
						{
							num4 = 6;
							num5 = 3;
							if (num != num3)
							{
								short[] array = class526_0.short_0;
								int num6 = num3;
								array[num6] += 1;
								num2 = 0;
							}
						}
						num = num3;
						i++;
						while (i < this.int_1)
						{
							if (num != (int)this.byte_0[i])
							{
								break;
							}
							i++;
							if (++num2 >= num4)
							{
								break;
							}
						}
						if (num2 < num5)
						{
							short[] array2 = class526_0.short_0;
							int num7 = num;
							array2[num7] += (short)num2;
						}
						else if (num != 0)
						{
							short[] array3 = class526_0.short_0;
							int num8 = 16;
							array3[num8] += 1;
						}
						else if (num2 <= 10)
						{
							short[] array4 = class526_0.short_0;
							int num9 = 17;
							array4[num9] += 1;
						}
						else
						{
							short[] array5 = class526_0.short_0;
							int num10 = 18;
							array5[num10] += 1;
						}
					}
				}

				// Token: 0x0600274E RID: 10062 RVA: 0x000FF780 File Offset: 0x000FD980
				public void method_7(Class518.Class525.Class526 class526_0)
				{
					int num = -1;
					int i = 0;
					while (i < this.int_1)
					{
						int num2 = 1;
						int num3 = (int)this.byte_0[i];
						int num4;
						int num5;
						if (num3 == 0)
						{
							num4 = 138;
							num5 = 3;
						}
						else
						{
							num4 = 6;
							num5 = 3;
							if (num != num3)
							{
								class526_0.method_0(num3);
								num2 = 0;
							}
						}
						num = num3;
						i++;
						while (i < this.int_1)
						{
							if (num != (int)this.byte_0[i])
							{
								break;
							}
							i++;
							if (++num2 >= num4)
							{
								break;
							}
						}
						if (num2 < num5)
						{
							while (num2-- > 0)
							{
								class526_0.method_0(num);
							}
						}
						else if (num != 0)
						{
							class526_0.method_0(16);
							this.class525_0.class528_0.method_3(num2 - 3, 2);
						}
						else if (num2 <= 10)
						{
							class526_0.method_0(17);
							this.class525_0.class528_0.method_3(num2 - 3, 3);
						}
						else
						{
							class526_0.method_0(18);
							this.class525_0.class528_0.method_3(num2 - 11, 7);
						}
					}
				}

				// Token: 0x0400134C RID: 4940
				public short[] short_0;

				// Token: 0x0400134D RID: 4941
				public byte[] byte_0;

				// Token: 0x0400134E RID: 4942
				public int int_0;

				// Token: 0x0400134F RID: 4943
				public int int_1;

				// Token: 0x04001350 RID: 4944
				private short[] short_1;

				// Token: 0x04001351 RID: 4945
				private int[] int_2;

				// Token: 0x04001352 RID: 4946
				private int int_3;

				// Token: 0x04001353 RID: 4947
				private Class518.Class525 class525_0;
			}
		}

		// Token: 0x020003E1 RID: 993
		internal sealed class Class527
		{
			// Token: 0x0600274F RID: 10063 RVA: 0x000FF880 File Offset: 0x000FDA80
			public Class527(Class518.Class528 class528_1)
			{
				this.class528_0 = class528_1;
				this.class525_0 = new Class518.Class525(class528_1);
				this.byte_0 = new byte[65536];
				this.short_0 = new short[32768];
				this.short_1 = new short[32768];
				this.int_14 = 1;
				this.int_13 = 1;
			}

			// Token: 0x06002750 RID: 10064 RVA: 0x0000F0BB File Offset: 0x0000D2BB
			private void method_0()
			{
				this.int_10 = ((int)this.byte_0[this.int_14] << 5 ^ (int)this.byte_0[this.int_14 + 1]);
			}

			// Token: 0x06002751 RID: 10065 RVA: 0x000FF8E8 File Offset: 0x000FDAE8
			private int method_1()
			{
				int num = (this.int_10 << 5 ^ (int)this.byte_0[this.int_14 + 2]) & 32767;
				short num2 = this.short_1[this.int_14 & 32767] = this.short_0[num];
				this.short_0[num] = (short)this.int_14;
				this.int_10 = num;
				return (int)num2 & 65535;
			}

			// Token: 0x06002752 RID: 10066 RVA: 0x000FF954 File Offset: 0x000FDB54
			private void method_2()
			{
				Array.Copy(this.byte_0, 32768, this.byte_0, 0, 32768);
				this.int_11 -= 32768;
				this.int_14 -= 32768;
				this.int_13 -= 32768;
				for (int i = 0; i < 32768; i++)
				{
					int num = (int)this.short_0[i] & 65535;
					this.short_0[i] = (short)((num >= 32768) ? (num - 32768) : 0);
				}
				for (int j = 0; j < 32768; j++)
				{
					int num2 = (int)this.short_1[j] & 65535;
					this.short_1[j] = (short)((num2 >= 32768) ? (num2 - 32768) : 0);
				}
			}

			// Token: 0x06002753 RID: 10067 RVA: 0x000FFA2C File Offset: 0x000FDC2C
			public void method_3()
			{
				if (this.int_14 >= 65274)
				{
					this.method_2();
				}
				while (this.int_15 < 262 && this.int_17 < this.int_18)
				{
					int num = 65536 - this.int_15 - this.int_14;
					if (num > this.int_18 - this.int_17)
					{
						num = this.int_18 - this.int_17;
					}
					Array.Copy(this.byte_1, this.int_17, this.byte_0, this.int_14 + this.int_15, num);
					this.int_17 += num;
					this.int_16 += num;
					this.int_15 += num;
				}
				if (this.int_15 >= 3)
				{
					this.method_0();
				}
			}

			// Token: 0x06002754 RID: 10068 RVA: 0x000FFB08 File Offset: 0x000FDD08
			private bool method_4(int int_19)
			{
				int num = 128;
				int num2 = 128;
				short[] array = this.short_1;
				int num3 = this.int_14;
				int num4 = this.int_14 + this.int_12;
				int num5 = Math.Max(this.int_12, 2);
				int num6 = Math.Max(this.int_14 - 32506, 0);
				int num7 = this.int_14 + 258 - 1;
				byte b = this.byte_0[num4 - 1];
				byte b2 = this.byte_0[num4];
				if (num5 >= 8)
				{
					num >>= 2;
				}
				if (num2 > this.int_15)
				{
					num2 = this.int_15;
				}
				do
				{
					if (this.byte_0[int_19 + num5] == b2 && this.byte_0[int_19 + num5 - 1] == b && this.byte_0[int_19] == this.byte_0[num3] && this.byte_0[int_19 + 1] == this.byte_0[num3 + 1])
					{
						int num8 = int_19 + 2;
						num3 += 2;
						while (this.byte_0[++num3] == this.byte_0[++num8] && this.byte_0[++num3] == this.byte_0[++num8] && this.byte_0[++num3] == this.byte_0[++num8] && this.byte_0[++num3] == this.byte_0[++num8] && this.byte_0[++num3] == this.byte_0[++num8] && this.byte_0[++num3] == this.byte_0[++num8] && this.byte_0[++num3] == this.byte_0[++num8] && this.byte_0[++num3] == this.byte_0[++num8] && num3 < num7)
						{
						}
						if (num3 > num4)
						{
							this.int_11 = int_19;
							num4 = num3;
							num5 = num3 - this.int_14;
							if (num5 >= num2)
							{
								break;
							}
							b = this.byte_0[num4 - 1];
							b2 = this.byte_0[num4];
						}
						num3 = this.int_14;
					}
					if ((int_19 = ((int)array[int_19 & 32767] & 65535)) <= num6)
					{
						break;
					}
				}
				while (--num != 0);
				this.int_12 = Math.Min(num5, this.int_15);
				return this.int_12 >= 3;
			}

			// Token: 0x06002755 RID: 10069 RVA: 0x000FFD8C File Offset: 0x000FDF8C
			private bool method_5(bool bool_1, bool bool_2)
			{
				bool result;
				if (this.int_15 < 262 && !bool_1)
				{
					result = false;
				}
				else
				{
					while (this.int_15 >= 262 || bool_1)
					{
						if (this.int_15 == 0)
						{
							if (this.bool_0)
							{
								this.class525_0.method_8((int)(this.byte_0[this.int_14 - 1] & byte.MaxValue));
							}
							this.bool_0 = false;
							this.class525_0.method_6(this.byte_0, this.int_13, this.int_14 - this.int_13, bool_2);
							this.int_13 = this.int_14;
							return false;
						}
						if (this.int_14 >= 65274)
						{
							this.method_2();
						}
						int num = this.int_11;
						int num2 = this.int_12;
						if (this.int_15 >= 3)
						{
							int num3 = this.method_1();
							if (num3 != 0 && this.int_14 - num3 <= 32506 && this.method_4(num3) && this.int_12 <= 5 && this.int_12 == 3 && this.int_14 - this.int_11 > 4096)
							{
								this.int_12 = 2;
							}
						}
						if (num2 >= 3 && this.int_12 <= num2)
						{
							this.class525_0.method_9(this.int_14 - 1 - num, num2);
							num2 -= 2;
							do
							{
								this.int_14++;
								this.int_15--;
								if (this.int_15 >= 3)
								{
									this.method_1();
								}
							}
							while (--num2 > 0);
							this.int_14++;
							this.int_15--;
							this.bool_0 = false;
							this.int_12 = 2;
						}
						else
						{
							if (this.bool_0)
							{
								this.class525_0.method_8((int)(this.byte_0[this.int_14 - 1] & byte.MaxValue));
							}
							this.bool_0 = true;
							this.int_14++;
							this.int_15--;
						}
						if (this.class525_0.method_7())
						{
							int num4 = this.int_14 - this.int_13;
							if (this.bool_0)
							{
								num4--;
							}
							bool flag = bool_2 && this.int_15 == 0 && !this.bool_0;
							this.class525_0.method_6(this.byte_0, this.int_13, num4, flag);
							this.int_13 += num4;
							return !flag;
						}
					}
					result = true;
				}
				return result;
			}

			// Token: 0x06002756 RID: 10070 RVA: 0x00100018 File Offset: 0x000FE218
			public bool method_6(bool bool_1, bool bool_2)
			{
				bool flag;
				do
				{
					this.method_3();
					bool bool_3 = bool_1 && this.int_17 == this.int_18;
					flag = this.method_5(bool_3, bool_2);
				}
				while (this.class528_0.IsFlushed && flag);
				return flag;
			}

			// Token: 0x06002757 RID: 10071 RVA: 0x0000F0E4 File Offset: 0x0000D2E4
			public void method_7(byte[] byte_2)
			{
				this.byte_1 = byte_2;
				this.int_17 = 0;
				this.int_18 = byte_2.Length;
			}

			// Token: 0x06002758 RID: 10072 RVA: 0x00100060 File Offset: 0x000FE260
			public bool method_8()
			{
				return this.int_18 == this.int_17;
			}

			// Token: 0x04001354 RID: 4948
			private const int int_0 = 258;

			// Token: 0x04001355 RID: 4949
			private const int int_1 = 3;

			// Token: 0x04001356 RID: 4950
			private const int int_2 = 32768;

			// Token: 0x04001357 RID: 4951
			private const int int_3 = 32767;

			// Token: 0x04001358 RID: 4952
			private const int int_4 = 32768;

			// Token: 0x04001359 RID: 4953
			private const int int_5 = 32767;

			// Token: 0x0400135A RID: 4954
			private const int int_6 = 5;

			// Token: 0x0400135B RID: 4955
			private const int int_7 = 262;

			// Token: 0x0400135C RID: 4956
			private const int int_8 = 32506;

			// Token: 0x0400135D RID: 4957
			private const int int_9 = 4096;

			// Token: 0x0400135E RID: 4958
			private int int_10;

			// Token: 0x0400135F RID: 4959
			private short[] short_0;

			// Token: 0x04001360 RID: 4960
			private short[] short_1;

			// Token: 0x04001361 RID: 4961
			private int int_11;

			// Token: 0x04001362 RID: 4962
			private int int_12;

			// Token: 0x04001363 RID: 4963
			private bool bool_0;

			// Token: 0x04001364 RID: 4964
			private int int_13;

			// Token: 0x04001365 RID: 4965
			private int int_14;

			// Token: 0x04001366 RID: 4966
			private int int_15;

			// Token: 0x04001367 RID: 4967
			private byte[] byte_0;

			// Token: 0x04001368 RID: 4968
			private byte[] byte_1;

			// Token: 0x04001369 RID: 4969
			private int int_16;

			// Token: 0x0400136A RID: 4970
			private int int_17;

			// Token: 0x0400136B RID: 4971
			private int int_18;

			// Token: 0x0400136C RID: 4972
			private Class518.Class528 class528_0;

			// Token: 0x0400136D RID: 4973
			private Class518.Class525 class525_0;
		}

		// Token: 0x020003E2 RID: 994
		internal sealed class Class528
		{
			// Token: 0x06002759 RID: 10073 RVA: 0x00100080 File Offset: 0x000FE280
			public void method_0(int int_3)
			{
				byte[] array = this.byte_0;
				int num = this.int_1;
				this.int_1 = num + 1;
				array[num] = (byte)int_3;
				byte[] array2 = this.byte_0;
				num = this.int_1;
				this.int_1 = num + 1;
				array2[num] = (byte)(int_3 >> 8);
			}

			// Token: 0x0600275A RID: 10074 RVA: 0x0000F0FF File Offset: 0x0000D2FF
			public void method_1(byte[] byte_1, int int_3, int int_4)
			{
				Array.Copy(byte_1, int_3, this.byte_0, this.int_1, int_4);
				this.int_1 += int_4;
			}

			// Token: 0x170006C7 RID: 1735
			// (get) Token: 0x0600275B RID: 10075 RVA: 0x001000C8 File Offset: 0x000FE2C8
			public int BitCount
			{
				get
				{
					return this.int_2;
				}
			}

			// Token: 0x0600275C RID: 10076 RVA: 0x001000E0 File Offset: 0x000FE2E0
			public void method_2()
			{
				if (this.int_2 > 0)
				{
					byte[] array = this.byte_0;
					int num = this.int_1;
					this.int_1 = num + 1;
					array[num] = (byte)this.uint_0;
					if (this.int_2 > 8)
					{
						byte[] array2 = this.byte_0;
						num = this.int_1;
						this.int_1 = num + 1;
						array2[num] = (byte)(this.uint_0 >> 8);
					}
				}
				this.uint_0 = 0U;
				this.int_2 = 0;
			}

			// Token: 0x0600275D RID: 10077 RVA: 0x00100150 File Offset: 0x000FE350
			public void method_3(int int_3, int int_4)
			{
				this.uint_0 |= (uint)((uint)int_3 << this.int_2);
				this.int_2 += int_4;
				if (this.int_2 >= 16)
				{
					byte[] array = this.byte_0;
					int num = this.int_1;
					this.int_1 = num + 1;
					array[num] = (byte)this.uint_0;
					byte[] array2 = this.byte_0;
					num = this.int_1;
					this.int_1 = num + 1;
					array2[num] = (byte)(this.uint_0 >> 8);
					this.uint_0 >>= 16;
					this.int_2 -= 16;
				}
			}

			// Token: 0x170006C8 RID: 1736
			// (get) Token: 0x0600275E RID: 10078 RVA: 0x001001F0 File Offset: 0x000FE3F0
			public bool IsFlushed
			{
				get
				{
					return this.int_1 == 0;
				}
			}

			// Token: 0x0600275F RID: 10079 RVA: 0x0010020C File Offset: 0x000FE40C
			public int method_4(byte[] byte_1, int int_3, int int_4)
			{
				if (this.int_2 >= 8)
				{
					byte[] array = this.byte_0;
					int num = this.int_1;
					this.int_1 = num + 1;
					array[num] = (byte)this.uint_0;
					this.uint_0 >>= 8;
					this.int_2 -= 8;
				}
				if (int_4 > this.int_1 - this.int_0)
				{
					int_4 = this.int_1 - this.int_0;
					Array.Copy(this.byte_0, this.int_0, byte_1, int_3, int_4);
					this.int_0 = 0;
					this.int_1 = 0;
				}
				else
				{
					Array.Copy(this.byte_0, this.int_0, byte_1, int_3, int_4);
					this.int_0 += int_4;
				}
				return int_4;
			}

			// Token: 0x0400136E RID: 4974
			protected byte[] byte_0 = new byte[65536];

			// Token: 0x0400136F RID: 4975
			private int int_0;

			// Token: 0x04001370 RID: 4976
			private int int_1;

			// Token: 0x04001371 RID: 4977
			private uint uint_0;

			// Token: 0x04001372 RID: 4978
			private int int_2;
		}

		// Token: 0x020003E3 RID: 995
		internal sealed class Stream0 : MemoryStream
		{
			// Token: 0x06002761 RID: 10081 RVA: 0x0000F13D File Offset: 0x0000D33D
			public void method_0(int int_0)
			{
				this.WriteByte((byte)(int_0 & 255));
				this.WriteByte((byte)(int_0 >> 8 & 255));
			}

			// Token: 0x06002762 RID: 10082 RVA: 0x0000F15F File Offset: 0x0000D35F
			public void method_1(int int_0)
			{
				this.method_0(int_0);
				this.method_0(int_0 >> 16);
			}

			// Token: 0x06002763 RID: 10083 RVA: 0x001002C8 File Offset: 0x000FE4C8
			public int method_2()
			{
				return this.ReadByte() | this.ReadByte() << 8;
			}

			// Token: 0x06002764 RID: 10084 RVA: 0x001002E8 File Offset: 0x000FE4E8
			public int method_3()
			{
				return this.method_2() | this.method_2() << 16;
			}

			// Token: 0x06002765 RID: 10085 RVA: 0x0000F174 File Offset: 0x0000D374
			public Stream0()
			{
			}

			// Token: 0x06002766 RID: 10086 RVA: 0x0000F17C File Offset: 0x0000D37C
			public Stream0(byte[] byte_0) : base(byte_0, false)
			{
			}
		}
	}
}
