﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns24;
using ns25;
using ns28;

namespace ns23
{
	// Token: 0x02000411 RID: 1041
	[DesignerCategory("Code")]
	internal sealed class Control4 : Control
	{
		// Token: 0x170006E6 RID: 1766
		// (get) Token: 0x06002834 RID: 10292 RVA: 0x0000F9CA File Offset: 0x0000DBCA
		// (set) Token: 0x06002835 RID: 10293 RVA: 0x001039BC File Offset: 0x00101BBC
		public Enum36 IconState
		{
			get
			{
				return this.enum36_0;
			}
			set
			{
				if (this.enum36_0 != value)
				{
					this.enum36_0 = value;
					Enum36 @enum = this.enum36_0;
					if (@enum != Enum36.const_1)
					{
						if (@enum != Enum36.const_2)
						{
							this.bitmap_0 = null;
						}
						else
						{
							this.bitmap_0 = Class537.smethod_0("warning16");
						}
					}
					else
					{
						this.bitmap_0 = Class537.smethod_0("error16");
					}
					this.Refresh();
				}
			}
		}

		// Token: 0x170006E7 RID: 1767
		// (get) Token: 0x06002836 RID: 10294 RVA: 0x0000F9D2 File Offset: 0x0000DBD2
		// (set) Token: 0x06002837 RID: 10295 RVA: 0x0000F9DF File Offset: 0x0000DBDF
		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		public string Text
		{
			get
			{
				return this.label_0.Text;
			}
			set
			{
				this.label_0.Text = value;
			}
		}

		// Token: 0x170006E8 RID: 1768
		// (get) Token: 0x06002838 RID: 10296 RVA: 0x0000F9ED File Offset: 0x0000DBED
		// (set) Token: 0x06002839 RID: 10297 RVA: 0x0000F9F5 File Offset: 0x0000DBF5
		public Image Image
		{
			get
			{
				return this.image_0;
			}
			set
			{
				this.image_0 = value;
				this.Refresh();
			}
		}

		// Token: 0x0600283A RID: 10298 RVA: 0x00103A1C File Offset: 0x00101C1C
		protected void Dispose(bool disposing)
		{
			if (disposing)
			{
				if (this.icon_0 != null)
				{
					this.icon_0.Dispose();
					this.icon_0 = null;
				}
				if (this.image_0 != null)
				{
					this.image_0.Dispose();
					this.image_0 = null;
				}
				if (this.bitmap_0 != null)
				{
					this.bitmap_0.Dispose();
					this.bitmap_0 = null;
				}
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600283B RID: 10299 RVA: 0x00103A84 File Offset: 0x00101C84
		protected void OnResize(EventArgs e)
		{
			this.label_0.SetBounds(Convert.ToInt32(13f * this.float_0), Convert.ToInt32(15f * this.float_1), base.Width - Convert.ToInt32(69f * this.float_0), base.Height - Convert.ToInt32(18f * this.float_1));
			base.OnResize(e);
		}

		// Token: 0x0600283C RID: 10300 RVA: 0x0000FA04 File Offset: 0x0000DC04
		protected void ScaleCore(float dx, float dy)
		{
			this.float_0 = dx;
			this.float_1 = dy;
			base.ScaleCore(dx, dy);
			this.OnResize(EventArgs.Empty);
		}

		// Token: 0x0600283D RID: 10301 RVA: 0x00103AF8 File Offset: 0x00101CF8
		protected void OnPaint(PaintEventArgs e)
		{
			base.OnPaint(e);
			e.Graphics.DrawLine(SystemPens.ControlDark, 0, base.ClientSize.Height - 2, base.ClientSize.Width, base.ClientSize.Height - 2);
			e.Graphics.DrawLine(SystemPens.ControlLightLight, 0, base.ClientSize.Height - 1, base.ClientSize.Width, base.ClientSize.Height - 1);
			Rectangle rectangle = new Rectangle(base.ClientSize.Width - Convert.ToInt32(48f * this.float_0), Convert.ToInt32(11f * this.float_1), Convert.ToInt32(32f * this.float_0), Convert.ToInt32(32f * this.float_1));
			if (this.image_0 != null)
			{
				e.Graphics.DrawImage(this.image_0, rectangle, new Rectangle(0, 0, 32, 32), GraphicsUnit.Pixel);
				return;
			}
			if (this.icon_0 != null)
			{
				e.Graphics.DrawIcon(this.icon_0, rectangle);
				if (this.bitmap_0 != null)
				{
					e.Graphics.DrawImage(this.bitmap_0, new Rectangle(rectangle.Right - Convert.ToInt32(12f * this.float_0), rectangle.Bottom - Convert.ToInt32(12f * this.float_1), Convert.ToInt32(16f * this.float_0), Convert.ToInt32(16f * this.float_1)), new Rectangle(0, 0, 16, 16), GraphicsUnit.Pixel);
				}
			}
		}

		// Token: 0x0600283E RID: 10302 RVA: 0x00103CA8 File Offset: 0x00101EA8
		protected void OnFontChanged(EventArgs e)
		{
			try
			{
				this.label_0.Font = new Font(this.Font, FontStyle.Bold);
				base.OnFontChanged(e);
			}
			catch
			{
			}
		}

		// Token: 0x0600283F RID: 10303 RVA: 0x00103CE8 File Offset: 0x00101EE8
		public Control4()
		{
			try
			{
				this.label_0.FlatStyle = FlatStyle.System;
				this.label_0.Font = new Font(this.Font, FontStyle.Bold);
			}
			catch
			{
			}
			base.Controls.Add(this.label_0);
			this.BackColor = SystemColors.Window;
			base.TabStop = false;
			this.Dock = DockStyle.Top;
			base.Height = 58;
			base.SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.SupportsTransparentBackColor | ControlStyles.AllPaintingInWmPaint | ControlStyles.DoubleBuffer, true);
			this.icon_0 = Class543.smethod_0();
			this.OnResize(EventArgs.Empty);
		}

		// Token: 0x06002840 RID: 10304 RVA: 0x0000FA27 File Offset: 0x0000DC27
		public Control4(string string_0) : this()
		{
			this.label_0.Text = string_0;
		}

		// Token: 0x04001406 RID: 5126
		private Label label_0 = new Label();

		// Token: 0x04001407 RID: 5127
		private Image image_0;

		// Token: 0x04001408 RID: 5128
		private Icon icon_0;

		// Token: 0x04001409 RID: 5129
		private Bitmap bitmap_0;

		// Token: 0x0400140A RID: 5130
		private Enum36 enum36_0;

		// Token: 0x0400140B RID: 5131
		private float float_0 = 1f;

		// Token: 0x0400140C RID: 5132
		private float float_1 = 1f;
	}
}
