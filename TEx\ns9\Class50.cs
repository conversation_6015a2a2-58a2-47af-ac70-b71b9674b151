﻿using System;
using System.Drawing;
using System.Windows.Forms;
using ns11;
using ns28;

namespace ns9
{
	// Token: 0x02000205 RID: 517
	internal sealed class Class50 : Class48
	{
		// Token: 0x06001517 RID: 5399 RVA: 0x0008A544 File Offset: 0x00088744
		public Class50()
		{
			base.NormalImage = Class372.cross_gray;
			base.HoverImage = Class372.cross_lightgray;
			base.Image = base.NormalImage;
			base.Size = new Size(18, 18);
			this.Cursor = Cursors.Hand;
		}

		// Token: 0x06001518 RID: 5400 RVA: 0x000041AE File Offset: 0x000023AE
		protected override void Class48_Click(object sender, EventArgs e)
		{
		}
	}
}
