﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;
using ns10;
using ns15;
using ns23;
using ns24;
using ns28;
using ns30;
using ns31;
using ns6;
using ns7;

namespace ns16
{
	// Token: 0x02000418 RID: 1048
	internal sealed partial class ExceptionReportingForm : Form
	{
		// Token: 0x0600284E RID: 10318 RVA: 0x0010414C File Offset: 0x0010234C
		public ExceptionReportingForm(Class540 class540_1, EventArgs35 eventArgs35_1) : this()
		{
			int num = base.Height;
			this.eventArgs35_0 = eventArgs35_1;
			this.class540_0 = class540_1;
			this.errorMessage.Text = eventArgs35_1.Exception.Message;
			num += this.errorMessage.Height - base.FontHeight;
			if (!eventArgs35_1.CanContinue)
			{
				this.continueCheckBox.Visible = false;
				num -= this.continueCheckBox.Height;
			}
			if (num > base.Height)
			{
				base.Height = num;
			}
			if (eventArgs35_1.CanDebug)
			{
				class540_1.DebuggerLaunched += this.method_4;
			}
			if (!eventArgs35_1.CanSendReport)
			{
				this.sendReport.Enabled = false;
				if (this.dontSendReport.CanFocus)
				{
					this.dontSendReport.Focus();
				}
			}
			this.email.Text = Class547.smethod_0("Email");
			class540_1.SendingReportFeedback += this.method_3;
		}

		// Token: 0x0600284F RID: 10319 RVA: 0x00104240 File Offset: 0x00102440
		public ExceptionReportingForm()
		{
			this.InitializeComponent();
			base.Size = new Size(419, 264);
			base.MinimizeBox = false;
			base.MaximizeBox = false;
			this.panelInformation.Location = Point.Empty;
			this.panelInformation.Dock = DockStyle.Fill;
			this.retrySending.Location = this.ok.Location;
			this.retrySending.Size = this.ok.Size;
			this.retrySending.BringToFront();
			this.panelSending.Location = Point.Empty;
			this.panelSending.Dock = DockStyle.Fill;
			this.Text = this.method_0(this.Text);
			this.panelEmail.Location = Point.Empty;
			this.panelEmail.Dock = DockStyle.Fill;
			foreach (object obj in base.Controls)
			{
				Control control = (Control)obj;
				control.Text = this.method_0(control.Text);
				foreach (object obj2 in control.Controls)
				{
					Control control2 = (Control)obj2;
					control2.Text = this.method_0(control2.Text);
				}
			}
		}

		// Token: 0x06002850 RID: 10320 RVA: 0x0000FBAC File Offset: 0x0000DDAC
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06002852 RID: 10322 RVA: 0x0000FBCB File Offset: 0x0000DDCB
		private string method_0(string string_0)
		{
			string_0 = string_0.Replace("%AppName%", "『交易练习者』");
			string_0 = string_0.Replace("%CompanyName%", "TEx Studio");
			return string_0;
		}

		// Token: 0x06002853 RID: 10323 RVA: 0x00105388 File Offset: 0x00103588
		public void method_1()
		{
			try
			{
				this.panelEmail.Visible = false;
				this.panelSending.Visible = true;
				if (this.eventArgs35_0 != null)
				{
					this.method_2(new ThreadStart(this.method_7));
				}
			}
			catch
			{
			}
		}

		// Token: 0x06002854 RID: 10324 RVA: 0x0000FBF2 File Offset: 0x0000DDF2
		private void sendReport_Click(object sender, EventArgs e)
		{
			this.panelInformation.Visible = false;
			this.panelEmail.Visible = true;
		}

		// Token: 0x06002855 RID: 10325 RVA: 0x0000FC0C File Offset: 0x0000DE0C
		private void method_2(ThreadStart threadStart_0)
		{
			this.thread_0 = new Thread(threadStart_0);
			this.thread_0.Start();
		}

		// Token: 0x06002856 RID: 10326 RVA: 0x0000FC25 File Offset: 0x0000DE25
		private void dontSendReport_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x06002857 RID: 10327 RVA: 0x001053DC File Offset: 0x001035DC
		private void cancelSending_Click(object sender, EventArgs e)
		{
			try
			{
				if (this.thread_0 != null)
				{
					this.thread_0.Abort();
				}
			}
			catch
			{
			}
			base.Close();
		}

		// Token: 0x06002858 RID: 10328 RVA: 0x0000FC25 File Offset: 0x0000DE25
		private void ok_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x06002859 RID: 10329 RVA: 0x0000FC2D File Offset: 0x0000DE2D
		private void continueCheckBox_CheckedChanged(object sender, EventArgs e)
		{
			this.eventArgs35_0.TryToContinue = this.continueCheckBox.Checked;
		}

		// Token: 0x0600285A RID: 10330 RVA: 0x00105418 File Offset: 0x00103618
		private void method_3(object sender, EventArgs37 e)
		{
			try
			{
				base.Invoke(new Delegate38(this.method_5), new object[]
				{
					sender,
					e
				});
			}
			catch (InvalidOperationException)
			{
			}
		}

		// Token: 0x0600285B RID: 10331 RVA: 0x0010545C File Offset: 0x0010365C
		private void method_4(object sender, EventArgs e)
		{
			try
			{
				base.Invoke(new EventHandler(this.method_6), new object[]
				{
					sender,
					e
				});
			}
			catch (InvalidOperationException)
			{
			}
		}

		// Token: 0x0600285C RID: 10332 RVA: 0x0000FC45 File Offset: 0x0000DE45
		protected void OnClosing(CancelEventArgs e)
		{
			if (this.thread_0 != null && this.thread_0.IsAlive)
			{
				this.thread_0.Abort();
			}
			base.OnClosing(e);
		}

		// Token: 0x0600285D RID: 10333 RVA: 0x001054A0 File Offset: 0x001036A0
		private void method_5(object sender, EventArgs37 e)
		{
			switch (e.Step)
			{
			case Enum35.const_0:
				if (e.Failed)
				{
					this.preparingFeedback.method_3(e.ErrorMessage);
					this.retrySending.Visible = true;
					this.retrySending.Focus();
					return;
				}
				this.preparingFeedback.method_1();
				return;
			case Enum35.const_1:
				if (e.Failed)
				{
					this.connectingFeedback.method_3(e.ErrorMessage);
					this.retrySending.Visible = true;
					this.retrySending.Focus();
					return;
				}
				this.preparingFeedback.method_2();
				this.connectingFeedback.method_1();
				return;
			case Enum35.const_2:
				if (e.Failed)
				{
					this.waitSendingReport.Visible = false;
					this.transferingFeedback.method_3(e.ErrorMessage);
					this.retrySending.Visible = true;
					this.retrySending.Focus();
					return;
				}
				this.connectingFeedback.method_2();
				this.transferingFeedback.method_1();
				this.waitSendingReport.Visible = true;
				return;
			case Enum35.const_3:
				this.waitSendingReport.Visible = false;
				this.transferingFeedback.method_2();
				this.completedFeedback.method_2();
				this.ok.Enabled = true;
				this.ok.Focus();
				this.cancelSending.Enabled = false;
				return;
			default:
				return;
			}
		}

		// Token: 0x0600285E RID: 10334 RVA: 0x0000FC25 File Offset: 0x0000DE25
		private void method_6(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x0600285F RID: 10335 RVA: 0x001055FC File Offset: 0x001037FC
		private void retrySending_Click(object sender, EventArgs e)
		{
			this.retrySending.Visible = false;
			this.preparingFeedback.method_0();
			this.connectingFeedback.method_0();
			this.transferingFeedback.method_0();
			if (this.eventArgs35_0 != null)
			{
				this.method_2(new ThreadStart(this.method_7));
			}
		}

		// Token: 0x06002860 RID: 10336 RVA: 0x0000FC6E File Offset: 0x0000DE6E
		private void method_7()
		{
			this.eventArgs35_0.method_6();
		}

		// Token: 0x06002861 RID: 10337 RVA: 0x0000FC7C File Offset: 0x0000DE7C
		private void method_8(object sender, EventArgs e)
		{
			if (this.eventArgs35_0 != null)
			{
				this.method_2(new ThreadStart(this.eventArgs35_0.method_3));
			}
		}

		// Token: 0x06002862 RID: 10338 RVA: 0x00105650 File Offset: 0x00103850
		private void continueSendReport_Click(object sender, EventArgs e)
		{
			if (!this.sendAnonymously.Checked && this.eventArgs35_0 != null)
			{
				this.eventArgs35_0.method_7("Email", this.email.Text);
				Class547.smethod_1("Email", this.email.Text);
			}
			this.method_1();
		}

		// Token: 0x06002863 RID: 10339 RVA: 0x0000FC9D File Offset: 0x0000DE9D
		private void email_TextChanged(object sender, EventArgs e)
		{
			this.continueSendReport.Enabled = (this.email.Text.Length > 0 || this.sendAnonymously.Checked);
		}

		// Token: 0x06002864 RID: 10340 RVA: 0x001056A8 File Offset: 0x001038A8
		private void sendAnonymously_CheckedChanged(object sender, EventArgs e)
		{
			this.email.Enabled = !this.sendAnonymously.Checked;
			this.continueSendReport.Enabled = (this.email.Text.Length > 0 || this.sendAnonymously.Checked);
		}

		// Token: 0x06002865 RID: 10341 RVA: 0x001056FC File Offset: 0x001038FC
		private void method_9(object sender, EventArgs e)
		{
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.DefaultExt = "saencryptedreport";
			saveFileDialog.Filter = "SmartAssembly Exception Report|*.saencryptedreport|All files|*.*";
			saveFileDialog.Title = "Save an Exception Report";
			if (saveFileDialog.ShowDialog(this) != DialogResult.Cancel)
			{
				if (this.eventArgs35_0.method_4(saveFileDialog.FileName))
				{
					MessageBox.Show(string.Format("Please send the Exception Report to {0} Support Team.", "TEx Studio"), "『交易练习者』", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					base.Close();
					return;
				}
				MessageBox.Show("Failed to save the report.", "『交易练习者』", MessageBoxButtons.OK, MessageBoxIcon.Hand);
			}
		}

		// Token: 0x04001420 RID: 5152
		private Class540 class540_0;

		// Token: 0x04001421 RID: 5153
		private EventArgs35 eventArgs35_0;

		// Token: 0x04001422 RID: 5154
		private Thread thread_0;

		// Token: 0x04001433 RID: 5171
		private IContainer icontainer_0;
	}
}
