﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using TEx.Comn;

namespace ns15
{
	// Token: 0x0200003D RID: 61
	internal sealed class Class19
	{
		// Token: 0x17000075 RID: 117
		// (get) Token: 0x060001BA RID: 442 RVA: 0x00017D8C File Offset: 0x00015F8C
		// (set) Token: 0x060001BB RID: 443 RVA: 0x000031A5 File Offset: 0x000013A5
		public string[] tscodes { get; set; }

		// Token: 0x17000076 RID: 118
		// (get) Token: 0x060001BC RID: 444 RVA: 0x00017DA4 File Offset: 0x00015FA4
		// (set) Token: 0x060001BD RID: 445 RVA: 0x000031B0 File Offset: 0x000013B0
		public List<Forecast> forecasts { get; set; }

		// Token: 0x17000077 RID: 119
		// (get) Token: 0x060001BE RID: 446 RVA: 0x00017DBC File Offset: 0x00015FBC
		// (set) Token: 0x060001BF RID: 447 RVA: 0x000031BB File Offset: 0x000013BB
		public List<Express> expresses { get; set; }

		// Token: 0x060001C0 RID: 448 RVA: 0x00017DD4 File Offset: 0x00015FD4
		public bool method_0()
		{
			bool result;
			if (this.forecasts != null && this.forecasts.Count != 0)
			{
				result = false;
			}
			else if (this.expresses != null)
			{
				result = (this.expresses.Count == 0);
			}
			else
			{
				result = true;
			}
			return result;
		}

		// Token: 0x040000B1 RID: 177
		[CompilerGenerated]
		private string[] string_0;

		// Token: 0x040000B2 RID: 178
		[CompilerGenerated]
		private List<Forecast> list_0;

		// Token: 0x040000B3 RID: 179
		[CompilerGenerated]
		private List<Express> list_1;
	}
}
