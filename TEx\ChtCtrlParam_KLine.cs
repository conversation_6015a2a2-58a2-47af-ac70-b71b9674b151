﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.Serialization;
using System.Xml.Linq;
using TEx.Inds;
using TEx.SIndicator;

namespace TEx
{
	// Token: 0x020001CF RID: 463
	[Serializable]
	internal sealed class ChtCtrlParam_KLine : ChtCtrlParam
	{
		// Token: 0x06001239 RID: 4665 RVA: 0x0007E490 File Offset: 0x0007C690
		public override XElement vmethod_0()
		{
			XElement xelement = base.vmethod_0();
			xelement.SetAttributeValue("IsChtCtrlParam_KLine", "1");
			if (this.SpCParam_VolMACD != null)
			{
				XElement xelement2 = this.SpCParam_VolMACD.GetXElement("SpCParam_VolMACD");
				xelement.Add(xelement2);
			}
			if (this.SpCParam_VolMACD_Sub != null)
			{
				XElement xelement3 = this.SpCParam_VolMACD_Sub.GetXElement("SpCParam_VolMACD_Sub");
				xelement.Add(xelement3);
			}
			if (this.ChartParamList != null && this.ChartParamList.Any<ChartParam>())
			{
				XElement xelement4 = new XElement("ChartParamList");
				foreach (ChartParam chartParam in this.ChartParamList)
				{
					XElement xelement5 = new XElement("ChartParam");
					xelement5.SetAttributeValue("ChartType", Convert.ToInt32(chartParam.ChartType));
					if (chartParam.ChartType == ChartType.CandleStick)
					{
						xelement5.SetAttributeValue("IsYAxisLogType", chartParam.IsYAxisLogType);
					}
					if (chartParam.UDSList != null && chartParam.UDSList.Any<UserDefineIndScript>())
					{
						XElement xelement6 = new XElement("UDInds");
						foreach (UserDefineIndScript userDefineIndScript in chartParam.UDSList)
						{
							XElement content = UserDefineFileMgr.smethod_26(new UserDefineIndParams(userDefineIndScript.Name, userDefineIndScript.UserDefineParams));
							xelement6.Add(content);
						}
						xelement5.Add(xelement6);
					}
					if (chartParam.IndList != null && chartParam.IndList.Any<Indicator>())
					{
						XElement xelement7 = new XElement("IndList");
						foreach (Indicator indicator in chartParam.IndList)
						{
							XElement xelement8 = new XElement("Indicator");
							Type type = indicator.GetType();
							xelement8.SetAttributeValue("Type", type.ToString());
							XElement xelement9 = new XElement("Properties");
							SerializationInfo serializationInfo = new SerializationInfo(type, new FormatterConverter());
							indicator.GetObjectData(serializationInfo, new StreamingContext(StreamingContextStates.All));
							SerializationInfoEnumerator enumerator4 = serializationInfo.GetEnumerator();
							for (int i = 0; i < serializationInfo.MemberCount; i++)
							{
								enumerator4.MoveNext();
								XElement xelement10 = new XElement("Property");
								xelement10.SetAttributeValue("Name", enumerator4.Name);
								if (enumerator4.ObjectType == typeof(int[]))
								{
									int[] array = enumerator4.Value as int[];
									string text = string.Empty;
									foreach (int num in array)
									{
										text = text + num.ToString() + " ";
									}
									text = text.Trim();
									xelement10.SetAttributeValue("Value", text);
								}
								else if (enumerator4.ObjectType == typeof(Color))
								{
									xelement10.SetAttributeValue("Value", ((Color)enumerator4.Value).ToArgb());
								}
								else if (enumerator4.ObjectType.IsEnum)
								{
									xelement10.SetAttributeValue("Value", Convert.ToInt32(enumerator4.Value));
								}
								else
								{
									xelement10.SetAttributeValue("Value", enumerator4.Value);
								}
								xelement10.SetAttributeValue("Type", enumerator4.ObjectType.FullName);
								xelement10.SetAttributeValue("AssemblyType", enumerator4.ObjectType.AssemblyQualifiedName);
								xelement9.Add(xelement10);
							}
							if (xelement9.HasElements)
							{
								xelement8.Add(xelement9);
							}
							xelement7.Add(xelement8);
						}
						xelement5.Add(xelement7);
					}
					xelement4.Add(xelement5);
				}
				xelement.Add(xelement4);
			}
			return xelement;
		}

		// Token: 0x170002B0 RID: 688
		// (get) Token: 0x0600123A RID: 4666 RVA: 0x0007E928 File Offset: 0x0007CB28
		// (set) Token: 0x0600123B RID: 4667 RVA: 0x0000795B File Offset: 0x00005B5B
		public SplitContainerParam SpCParam_VolMACD
		{
			get
			{
				return this._SpCParam_VolMACD;
			}
			set
			{
				this._SpCParam_VolMACD = value;
			}
		}

		// Token: 0x170002B1 RID: 689
		// (get) Token: 0x0600123C RID: 4668 RVA: 0x0007E940 File Offset: 0x0007CB40
		// (set) Token: 0x0600123D RID: 4669 RVA: 0x00007966 File Offset: 0x00005B66
		public SplitContainerParam SpCParam_VolMACD_Sub
		{
			get
			{
				return this._SpCParam_VolMACD_Sub;
			}
			set
			{
				this._SpCParam_VolMACD_Sub = value;
			}
		}

		// Token: 0x170002B2 RID: 690
		// (get) Token: 0x0600123E RID: 4670 RVA: 0x0007E958 File Offset: 0x0007CB58
		// (set) Token: 0x0600123F RID: 4671 RVA: 0x00007971 File Offset: 0x00005B71
		public List<ChartParam> ChartParamList
		{
			get
			{
				return this._ChartParamList;
			}
			set
			{
				this._ChartParamList = value;
			}
		}

		// Token: 0x04000989 RID: 2441
		private SplitContainerParam _SpCParam_VolMACD;

		// Token: 0x0400098A RID: 2442
		private SplitContainerParam _SpCParam_VolMACD_Sub;

		// Token: 0x0400098B RID: 2443
		private List<ChartParam> _ChartParamList;
	}
}
