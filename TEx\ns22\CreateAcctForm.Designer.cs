﻿namespace ns22
{
	// Token: 0x02000246 RID: 582
	internal sealed partial class CreateAcctForm : global::System.Windows.Forms.Form
	{
		// Token: 0x060018CD RID: 6349 RVA: 0x000A503C File Offset: 0x000A323C
		private void InitializeComponent()
		{
			this.label1 = new global::System.Windows.Forms.Label();
			this.label2 = new global::System.Windows.Forms.Label();
			this.textBox_AcctName = new global::System.Windows.Forms.TextBox();
			this.numericUpDown_IniBal = new global::System.Windows.Forms.NumericUpDown();
			this.textBox_Notes = new global::System.Windows.Forms.TextBox();
			this.label3 = new global::System.Windows.Forms.Label();
			this.button_OK = new global::System.Windows.Forms.Button();
			this.button_Cancel = new global::System.Windows.Forms.Button();
			this.label4 = new global::System.Windows.Forms.Label();
			this.chkBox_IsReadOnly = new global::System.Windows.Forms.CheckBox();
			((global::System.ComponentModel.ISupportInitialize)this.numericUpDown_IniBal).BeginInit();
			base.SuspendLayout();
			this.label1.AutoSize = true;
			this.label1.Location = new global::System.Drawing.Point(54, 29);
			this.label1.Name = "label1";
			this.label1.Size = new global::System.Drawing.Size(82, 15);
			this.label1.TabIndex = 0;
			this.label1.Text = "账户名称：";
			this.label2.AutoSize = true;
			this.label2.Location = new global::System.Drawing.Point(54, 70);
			this.label2.Name = "label2";
			this.label2.Size = new global::System.Drawing.Size(82, 15);
			this.label2.TabIndex = 0;
			this.label2.Text = "初始资金：";
			this.textBox_AcctName.BorderStyle = global::System.Windows.Forms.BorderStyle.FixedSingle;
			this.textBox_AcctName.Location = new global::System.Drawing.Point(147, 25);
			this.textBox_AcctName.MaxLength = 16;
			this.textBox_AcctName.Name = "textBox_AcctName";
			this.textBox_AcctName.Size = new global::System.Drawing.Size(186, 25);
			this.textBox_AcctName.TabIndex = 1;
			this.numericUpDown_IniBal.BorderStyle = global::System.Windows.Forms.BorderStyle.FixedSingle;
			global::System.Windows.Forms.NumericUpDown numericUpDown = this.numericUpDown_IniBal;
			int[] array = new int[4];
			array[0] = 100;
			numericUpDown.Increment = new decimal(array);
			this.numericUpDown_IniBal.Location = new global::System.Drawing.Point(147, 65);
			global::System.Windows.Forms.NumericUpDown numericUpDown2 = this.numericUpDown_IniBal;
			int[] array2 = new int[4];
			array2[0] = 100000000;
			numericUpDown2.Maximum = new decimal(array2);
			global::System.Windows.Forms.NumericUpDown numericUpDown3 = this.numericUpDown_IniBal;
			int[] array3 = new int[4];
			array3[0] = 20000;
			numericUpDown3.Minimum = new decimal(array3);
			this.numericUpDown_IniBal.Name = "numericUpDown_IniBal";
			this.numericUpDown_IniBal.Size = new global::System.Drawing.Size(186, 25);
			this.numericUpDown_IniBal.TabIndex = 2;
			this.numericUpDown_IniBal.ThousandsSeparator = true;
			global::System.Windows.Forms.NumericUpDown numericUpDown4 = this.numericUpDown_IniBal;
			int[] array4 = new int[4];
			array4[0] = 500000;
			numericUpDown4.Value = new decimal(array4);
			this.textBox_Notes.BorderStyle = global::System.Windows.Forms.BorderStyle.FixedSingle;
			this.textBox_Notes.Location = new global::System.Drawing.Point(147, 107);
			this.textBox_Notes.MaxLength = 255;
			this.textBox_Notes.Multiline = true;
			this.textBox_Notes.Name = "textBox_Notes";
			this.textBox_Notes.Size = new global::System.Drawing.Size(287, 90);
			this.textBox_Notes.TabIndex = 3;
			this.label3.AutoSize = true;
			this.label3.Location = new global::System.Drawing.Point(84, 110);
			this.label3.Name = "label3";
			this.label3.Size = new global::System.Drawing.Size(52, 15);
			this.label3.TabIndex = 0;
			this.label3.Text = "注释：";
			this.button_OK.Location = new global::System.Drawing.Point(243, 257);
			this.button_OK.Name = "button_OK";
			this.button_OK.Size = new global::System.Drawing.Size(111, 30);
			this.button_OK.TabIndex = 4;
			this.button_OK.Text = "确定";
			this.button_OK.UseVisualStyleBackColor = true;
			this.button_OK.Click += new global::System.EventHandler(this.button_OK_Click);
			this.button_Cancel.DialogResult = global::System.Windows.Forms.DialogResult.Cancel;
			this.button_Cancel.Location = new global::System.Drawing.Point(368, 257);
			this.button_Cancel.Name = "button_Cancel";
			this.button_Cancel.Size = new global::System.Drawing.Size(111, 30);
			this.button_Cancel.TabIndex = 5;
			this.button_Cancel.Text = "取消";
			this.button_Cancel.UseVisualStyleBackColor = true;
			this.label4.AutoSize = true;
			this.label4.Location = new global::System.Drawing.Point(54, 213);
			this.label4.Name = "label4";
			this.label4.Size = new global::System.Drawing.Size(82, 15);
			this.label4.TabIndex = 6;
			this.label4.Text = "是否只读：";
			this.chkBox_IsReadOnly.AutoSize = true;
			this.chkBox_IsReadOnly.Location = new global::System.Drawing.Point(147, 212);
			this.chkBox_IsReadOnly.Name = "chkBox_IsReadOnly";
			this.chkBox_IsReadOnly.Size = new global::System.Drawing.Size(345, 19);
			this.chkBox_IsReadOnly.TabIndex = 7;
			this.chkBox_IsReadOnly.Text = "(不允许开平仓，适用于仅导入交易记录的账户)";
			this.chkBox_IsReadOnly.UseVisualStyleBackColor = true;
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(8f, 15f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			this.BackColor = global::System.Drawing.SystemColors.Control;
			base.CancelButton = this.button_Cancel;
			base.ClientSize = new global::System.Drawing.Size(512, 297);
			base.Controls.Add(this.chkBox_IsReadOnly);
			base.Controls.Add(this.label4);
			base.Controls.Add(this.button_Cancel);
			base.Controls.Add(this.button_OK);
			base.Controls.Add(this.label3);
			base.Controls.Add(this.textBox_Notes);
			base.Controls.Add(this.numericUpDown_IniBal);
			base.Controls.Add(this.textBox_AcctName);
			base.Controls.Add(this.label2);
			base.Controls.Add(this.label1);
			this.DoubleBuffered = true;
			base.FormBorderStyle = global::System.Windows.Forms.FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = "CreateAcctForm";
			base.ShowInTaskbar = false;
			base.SizeGripStyle = global::System.Windows.Forms.SizeGripStyle.Hide;
			base.StartPosition = global::System.Windows.Forms.FormStartPosition.CenterParent;
			this.Text = "新建账户";
			base.Load += new global::System.EventHandler(this.CreateAcctForm_Load);
			((global::System.ComponentModel.ISupportInitialize)this.numericUpDown_IniBal).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000C5C RID: 3164
		private global::System.Windows.Forms.Label label1;

		// Token: 0x04000C5D RID: 3165
		private global::System.Windows.Forms.Label label2;

		// Token: 0x04000C5E RID: 3166
		private global::System.Windows.Forms.TextBox textBox_AcctName;

		// Token: 0x04000C5F RID: 3167
		private global::System.Windows.Forms.NumericUpDown numericUpDown_IniBal;

		// Token: 0x04000C60 RID: 3168
		private global::System.Windows.Forms.TextBox textBox_Notes;

		// Token: 0x04000C61 RID: 3169
		private global::System.Windows.Forms.Label label3;

		// Token: 0x04000C62 RID: 3170
		private global::System.Windows.Forms.Button button_OK;

		// Token: 0x04000C63 RID: 3171
		private global::System.Windows.Forms.Button button_Cancel;

		// Token: 0x04000C64 RID: 3172
		private global::System.Windows.Forms.Label label4;

		// Token: 0x04000C65 RID: 3173
		private global::System.Windows.Forms.CheckBox chkBox_IsReadOnly;
	}
}
