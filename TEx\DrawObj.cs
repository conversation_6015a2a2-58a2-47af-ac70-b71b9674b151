﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Runtime.Serialization;
using System.Threading;
using ns28;
using TEx.Chart;
using TEx.Comn;

namespace TEx
{
	// Token: 0x02000067 RID: 103
	[Serializable]
	internal abstract class DrawObj : ISerializable
	{
		// Token: 0x06000366 RID: 870
		protected abstract List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5);

		// Token: 0x1400001A RID: 26
		// (add) Token: 0x06000367 RID: 871 RVA: 0x0001FB88 File Offset: 0x0001DD88
		// (remove) Token: 0x06000368 RID: 872 RVA: 0x0001FBC0 File Offset: 0x0001DDC0
		public event EventHandler MouseEnter
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000369 RID: 873 RVA: 0x00003823 File Offset: 0x00001A23
		protected virtual void vmethod_1()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x1400001B RID: 27
		// (add) Token: 0x0600036A RID: 874 RVA: 0x0001FBF8 File Offset: 0x0001DDF8
		// (remove) Token: 0x0600036B RID: 875 RVA: 0x0001FC30 File Offset: 0x0001DE30
		public event EventHandler MouseLeave
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600036C RID: 876 RVA: 0x0000383E File Offset: 0x00001A3E
		protected virtual void vmethod_2()
		{
			EventHandler eventHandler = this.eventHandler_1;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x0600036D RID: 877 RVA: 0x00002D25 File Offset: 0x00000F25
		public DrawObj()
		{
		}

		// Token: 0x0600036E RID: 878 RVA: 0x00003859 File Offset: 0x00001A59
		public DrawObj(ChartCS chart, double x1, double y1, double x2, double y2) : this(chart, x1, y1, x2, y2, false)
		{
		}

		// Token: 0x0600036F RID: 879 RVA: 0x0001FC68 File Offset: 0x0001DE68
		public DrawObj(ChartCS chart, double x1, double y1, double x2, double y2, bool disableRstPrc)
		{
			this.Chart = chart;
			this.PeriodType = chart.HisDataPeriodSet.PeriodType;
			this.PeriodUnits = chart.HisDataPeriodSet.PeriodUnits;
			this.AcctID = Base.Acct.CurrAccount.ID;
			this.SymbolCode = this.Chart.Symbol.Code;
			this.DisableRstPrice = disableRstPrc;
			if (this.LineColor == null)
			{
				this.LineColor = Base.UI.Form.LastSelectedLineColor;
			}
			this.method_3(chart, x1, y1, x2, y2);
			this.method_16(chart, x1, y1, x2, y2);
			this.XPtsGap = x2 - x1;
			this.nullable_0 = new double?(y1);
			this.nullable_1 = new double?(y2);
		}

		// Token: 0x06000370 RID: 880 RVA: 0x0001FD34 File Offset: 0x0001DF34
		public virtual void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			info.AddValue("Location", this.location_0);
			info.AddValue("AcctID", this.int_0);
			info.AddValue("SymbolCode", this.string_2);
			info.AddValue("PeriodType", this.periodType_0);
			info.AddValue("PeriodUnits", this.nullable_2);
			info.AddValue("DTValLocation", this.dtvalLocation_0);
			info.AddValue("EndPtDTValLoc", this.dtvalLocation_1);
			info.AddValue("XPtsGap", this.double_0);
			info.AddValue("LineColor", this.nullable_3);
			info.AddValue("CanChgColor", this.bool_1);
			info.AddValue("IsOneClickLoc", this.bool_3);
			info.AddValue("Tag", this.string_3);
			info.AddValue("Name", this.string_4);
			info.AddValue("SublineParamList", this.list_1);
			info.AddValue("CanChgLineType", this.bool_2);
			info.AddValue("LineStyle", this.drawLineStyle_0);
		}

		// Token: 0x06000371 RID: 881 RVA: 0x0001FE64 File Offset: 0x0001E064
		protected void method_0(SerializationInfo serializationInfo_0)
		{
			this.location_0 = (Location)serializationInfo_0.GetValue("Location", typeof(Location));
			this.int_0 = serializationInfo_0.GetInt32("AcctID");
			this.string_2 = serializationInfo_0.GetString("SymbolCode");
			this.periodType_0 = (PeriodType)serializationInfo_0.GetValue("PeriodType", typeof(PeriodType));
			this.nullable_2 = (int?)serializationInfo_0.GetValue("PeriodUnits", typeof(int?));
			this.dtvalLocation_0 = (DTValLocation)serializationInfo_0.GetValue("DTValLocation", typeof(DTValLocation));
			this.dtvalLocation_1 = (DTValLocation)serializationInfo_0.GetValue("EndPtDTValLoc", typeof(DTValLocation));
			this.double_0 = serializationInfo_0.GetDouble("XPtsGap");
			this.nullable_3 = (Color?)serializationInfo_0.GetValue("LineColor", typeof(Color?));
			this.bool_1 = serializationInfo_0.GetBoolean("CanChgColor");
			this.bool_3 = serializationInfo_0.GetBoolean("IsOneClickLoc");
			this.string_3 = serializationInfo_0.GetString("Tag");
			try
			{
				this.string_4 = serializationInfo_0.GetString("Name");
			}
			catch
			{
			}
			try
			{
				this.list_1 = (List<DrawSublineParam>)serializationInfo_0.GetValue("SublineParamList", typeof(List<DrawSublineParam>));
			}
			catch
			{
			}
			try
			{
				this.bool_2 = serializationInfo_0.GetBoolean("CanChgLineType");
			}
			catch
			{
			}
			try
			{
				this.drawLineStyle_0 = (DrawLineStyle)serializationInfo_0.GetValue("LineStyle", typeof(DrawLineStyle));
			}
			catch
			{
			}
			this.nullable_0 = new double?(this.location_0.Y1);
			this.nullable_1 = new double?(this.location_0.Y2);
		}

		// Token: 0x170000C6 RID: 198
		// (get) Token: 0x06000372 RID: 882 RVA: 0x00020074 File Offset: 0x0001E274
		// (set) Token: 0x06000373 RID: 883 RVA: 0x00003869 File Offset: 0x00001A69
		public ChartCS Chart
		{
			get
			{
				return this.chartCS_0;
			}
			set
			{
				this.chartCS_0 = value;
			}
		}

		// Token: 0x170000C7 RID: 199
		// (get) Token: 0x06000374 RID: 884 RVA: 0x0002008C File Offset: 0x0001E28C
		// (set) Token: 0x06000375 RID: 885 RVA: 0x00003874 File Offset: 0x00001A74
		public Location Location
		{
			get
			{
				return this.location_0;
			}
			set
			{
				this.location_0 = value;
			}
		}

		// Token: 0x170000C8 RID: 200
		// (get) Token: 0x06000376 RID: 886 RVA: 0x000200A4 File Offset: 0x0001E2A4
		// (set) Token: 0x06000377 RID: 887 RVA: 0x0000387F File Offset: 0x00001A7F
		public int AcctID
		{
			get
			{
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
			}
		}

		// Token: 0x170000C9 RID: 201
		// (get) Token: 0x06000378 RID: 888 RVA: 0x000200BC File Offset: 0x0001E2BC
		// (set) Token: 0x06000379 RID: 889 RVA: 0x0000388A File Offset: 0x00001A8A
		public string SymbolCode
		{
			get
			{
				return this.string_2;
			}
			set
			{
				this.string_2 = value;
			}
		}

		// Token: 0x170000CA RID: 202
		// (get) Token: 0x0600037A RID: 890 RVA: 0x000200D4 File Offset: 0x0001E2D4
		// (set) Token: 0x0600037B RID: 891 RVA: 0x00003895 File Offset: 0x00001A95
		public PeriodType PeriodType
		{
			get
			{
				return this.periodType_0;
			}
			set
			{
				this.periodType_0 = value;
			}
		}

		// Token: 0x170000CB RID: 203
		// (get) Token: 0x0600037C RID: 892 RVA: 0x000200EC File Offset: 0x0001E2EC
		// (set) Token: 0x0600037D RID: 893 RVA: 0x000038A0 File Offset: 0x00001AA0
		public int? PeriodUnits
		{
			get
			{
				return this.nullable_2;
			}
			set
			{
				this.nullable_2 = value;
			}
		}

		// Token: 0x170000CC RID: 204
		// (get) Token: 0x0600037E RID: 894 RVA: 0x00020104 File Offset: 0x0001E304
		// (set) Token: 0x0600037F RID: 895 RVA: 0x000038AB File Offset: 0x00001AAB
		public DTValLocation DTValLocation
		{
			get
			{
				return this.dtvalLocation_0;
			}
			set
			{
				this.dtvalLocation_0 = value;
			}
		}

		// Token: 0x170000CD RID: 205
		// (get) Token: 0x06000380 RID: 896 RVA: 0x0002011C File Offset: 0x0001E31C
		// (set) Token: 0x06000381 RID: 897 RVA: 0x000038B6 File Offset: 0x00001AB6
		public DTValLocation EndPtDTValLoc
		{
			get
			{
				return this.dtvalLocation_1;
			}
			set
			{
				this.dtvalLocation_1 = value;
			}
		}

		// Token: 0x170000CE RID: 206
		// (get) Token: 0x06000382 RID: 898 RVA: 0x00020134 File Offset: 0x0001E334
		// (set) Token: 0x06000383 RID: 899 RVA: 0x000038C1 File Offset: 0x00001AC1
		public Color? LineColor
		{
			get
			{
				return this.nullable_3;
			}
			set
			{
				this.nullable_3 = value;
			}
		}

		// Token: 0x170000CF RID: 207
		// (get) Token: 0x06000384 RID: 900 RVA: 0x0002014C File Offset: 0x0001E34C
		// (set) Token: 0x06000385 RID: 901 RVA: 0x000038CC File Offset: 0x00001ACC
		public string Tag
		{
			get
			{
				return this.string_3;
			}
			set
			{
				this.string_3 = value;
			}
		}

		// Token: 0x170000D0 RID: 208
		// (get) Token: 0x06000386 RID: 902 RVA: 0x00020164 File Offset: 0x0001E364
		public bool IsSelected
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x170000D1 RID: 209
		// (get) Token: 0x06000387 RID: 903 RVA: 0x0002017C File Offset: 0x0001E37C
		// (set) Token: 0x06000388 RID: 904 RVA: 0x000038D7 File Offset: 0x00001AD7
		public bool CanChgColor
		{
			get
			{
				return this.bool_1;
			}
			set
			{
				this.bool_1 = value;
			}
		}

		// Token: 0x170000D2 RID: 210
		// (get) Token: 0x06000389 RID: 905 RVA: 0x00020194 File Offset: 0x0001E394
		// (set) Token: 0x0600038A RID: 906 RVA: 0x000038E2 File Offset: 0x00001AE2
		public bool CanChgLineType
		{
			get
			{
				return this.bool_2;
			}
			set
			{
				this.bool_2 = value;
			}
		}

		// Token: 0x170000D3 RID: 211
		// (get) Token: 0x0600038B RID: 907 RVA: 0x000201AC File Offset: 0x0001E3AC
		// (set) Token: 0x0600038C RID: 908 RVA: 0x000038ED File Offset: 0x00001AED
		public bool IsOneClickLoc
		{
			get
			{
				return this.bool_3;
			}
			set
			{
				this.bool_3 = value;
			}
		}

		// Token: 0x170000D4 RID: 212
		// (get) Token: 0x0600038D RID: 909 RVA: 0x000201C4 File Offset: 0x0001E3C4
		// (set) Token: 0x0600038E RID: 910 RVA: 0x000038F8 File Offset: 0x00001AF8
		public double XPtsGap
		{
			get
			{
				return this.double_0;
			}
			set
			{
				this.double_0 = value;
			}
		}

		// Token: 0x170000D5 RID: 213
		// (get) Token: 0x0600038F RID: 911 RVA: 0x000201DC File Offset: 0x0001E3DC
		public bool SelectBoxVisible
		{
			get
			{
				bool result;
				if (this.Chart != null && this.Chart.GraphPane.GraphObjList.Where(new Func<GraphObj, bool>(this.method_31)).Any<GraphObj>())
				{
					result = true;
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x170000D6 RID: 214
		// (get) Token: 0x06000390 RID: 912 RVA: 0x00020224 File Offset: 0x0001E424
		// (set) Token: 0x06000391 RID: 913 RVA: 0x00003903 File Offset: 0x00001B03
		public bool MouseHovering
		{
			get
			{
				return this.bool_4;
			}
			set
			{
				if (this.bool_4 != value)
				{
					this.bool_4 = value;
					if (value)
					{
						this.vmethod_1();
					}
					else
					{
						this.vmethod_2();
					}
				}
			}
		}

		// Token: 0x170000D7 RID: 215
		// (get) Token: 0x06000392 RID: 914 RVA: 0x0002023C File Offset: 0x0001E43C
		// (set) Token: 0x06000393 RID: 915 RVA: 0x00003928 File Offset: 0x00001B28
		public bool DisableRstPrice
		{
			get
			{
				return this.bool_5;
			}
			set
			{
				if (this.bool_5 != value)
				{
					this.bool_5 = value;
				}
			}
		}

		// Token: 0x170000D8 RID: 216
		// (get) Token: 0x06000394 RID: 916 RVA: 0x00020254 File Offset: 0x0001E454
		// (set) Token: 0x06000395 RID: 917 RVA: 0x0000393C File Offset: 0x00001B3C
		public List<GraphObj> GraphObjLst
		{
			get
			{
				return this.list_0;
			}
			set
			{
				this.list_0 = value;
			}
		}

		// Token: 0x170000D9 RID: 217
		// (get) Token: 0x06000396 RID: 918 RVA: 0x0002026C File Offset: 0x0001E46C
		// (set) Token: 0x06000397 RID: 919 RVA: 0x00003947 File Offset: 0x00001B47
		public List<DrawSublineParam> SublineParamList
		{
			get
			{
				List<DrawSublineParam> result;
				if (this.list_1 == null)
				{
					object obj = this.method_29(DrawObjParamType.SublineParam);
					List<DrawSublineParam> list;
					if (obj != null)
					{
						list = (obj as List<DrawSublineParam>);
					}
					else
					{
						list = this.vmethod_22();
					}
					result = list;
				}
				else
				{
					result = this.list_1;
				}
				return result;
			}
			set
			{
				this.list_1 = value;
			}
		}

		// Token: 0x170000DA RID: 218
		// (get) Token: 0x06000398 RID: 920 RVA: 0x000202AC File Offset: 0x0001E4AC
		// (set) Token: 0x06000399 RID: 921 RVA: 0x00003952 File Offset: 0x00001B52
		public string Name
		{
			get
			{
				return this.string_4;
			}
			set
			{
				this.string_4 = value;
			}
		}

		// Token: 0x170000DB RID: 219
		// (get) Token: 0x0600039A RID: 922 RVA: 0x000202C4 File Offset: 0x0001E4C4
		// (set) Token: 0x0600039B RID: 923 RVA: 0x0000395D File Offset: 0x00001B5D
		public DrawLineStyle LineStyle
		{
			get
			{
				DrawLineStyle result;
				if (this.drawLineStyle_0 == null)
				{
					result = this.vmethod_23();
				}
				else
				{
					result = this.drawLineStyle_0;
				}
				return result;
			}
			set
			{
				this.drawLineStyle_0 = value;
			}
		}

		// Token: 0x0600039C RID: 924 RVA: 0x00003968 File Offset: 0x00001B68
		protected void method_1(ChartCS chartCS_1, List<GraphObj> list_2)
		{
			this.method_2(chartCS_1, list_2[0].Location);
		}

		// Token: 0x0600039D RID: 925 RVA: 0x000202EC File Offset: 0x0001E4EC
		protected void method_2(ChartCS chartCS_1, Location location_1)
		{
			double x = location_1.X1;
			double y = location_1.Y1;
			double x2 = location_1.X2;
			double y2 = location_1.Y2;
			this.method_3(chartCS_1, x, y, x2, y2);
		}

		// Token: 0x0600039E RID: 926 RVA: 0x00020324 File Offset: 0x0001E524
		protected void method_3(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4)
		{
			this.Location = this.vmethod_3(double_1, double_2, double_3, double_4);
			try
			{
				this.method_4(chartCS_1);
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
			DTValLocation dtvalLocation = this.vmethod_8(chartCS_1);
			if (dtvalLocation == null && this.DTValLocation != null)
			{
				dtvalLocation = this.DTValLocation.method_0();
			}
			if (dtvalLocation != null)
			{
				this.EndPtDTValLoc = dtvalLocation;
			}
			this.XPtsGap = double_3 - double_1;
		}

		// Token: 0x0600039F RID: 927 RVA: 0x00020398 File Offset: 0x0001E598
		protected virtual Location vmethod_3(double double_1, double double_2, double double_3, double double_4)
		{
			return new Location(double_1, double_2, double_3 - double_1, double_4 - double_2, CoordType.AxisXYScale, AlignH.Left, AlignV.Top);
		}

		// Token: 0x060003A0 RID: 928 RVA: 0x000203BC File Offset: 0x0001E5BC
		public virtual Location vmethod_4(ChartCS chartCS_1)
		{
			Location result;
			try
			{
				result = chartCS_1.GraphPane.GraphObjList.First(new Func<GraphObj, bool>(this.method_32)).Location;
			}
			catch
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060003A1 RID: 929 RVA: 0x00020408 File Offset: 0x0001E608
		protected virtual void vmethod_5(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4)
		{
			double double_5 = chartCS_1.method_246(double_1);
			double double_6 = chartCS_1.method_246(double_3);
			this.Location = this.vmethod_3(double_5, double_2, double_6, double_4);
		}

		// Token: 0x060003A2 RID: 930 RVA: 0x0000397F File Offset: 0x00001B7F
		private void method_4(ChartCS chartCS_1)
		{
			this.method_5(chartCS_1, this.Location);
		}

		// Token: 0x060003A3 RID: 931 RVA: 0x0002043C File Offset: 0x0001E63C
		public void method_5(ChartCS chartCS_1, Location location_1)
		{
			DTValLocation dtvalLocation = this.method_7(chartCS_1, location_1);
			if (dtvalLocation != null)
			{
				StkSymbol symbol = this.Chart.Symbol;
				if (!this.DisableRstPrice && symbol.IsStock)
				{
					DateTime dateTime_ = dtvalLocation.X2DateTime;
					if (dtvalLocation.X1DateTime > dtvalLocation.X2DateTime)
					{
						dateTime_ = dtvalLocation.X1DateTime;
					}
					StockRestorationMethod stockRestorationMethod = Base.UI.smethod_171(symbol);
					if (stockRestorationMethod != StockRestorationMethod.None)
					{
						SymbDataSet symbDataSet = this.Chart.ChtCtrl.SymbDataSet;
						dtvalLocation.Y1Value = symbDataSet.method_95(dtvalLocation.Y1Value, dateTime_, stockRestorationMethod);
						dtvalLocation.Y2Value = symbDataSet.method_95(dtvalLocation.Y2Value, dateTime_, stockRestorationMethod);
					}
				}
				this.DTValLocation = dtvalLocation;
			}
			this.XPtsGap = location_1.Width;
		}

		// Token: 0x060003A4 RID: 932 RVA: 0x000204F4 File Offset: 0x0001E6F4
		public void method_6(ChartCS chartCS_1, PointD pointD_0, PointD pointD_1)
		{
			Location location_ = this.vmethod_3(pointD_0.X, pointD_0.Y, pointD_1.X, pointD_1.Y);
			this.method_5(chartCS_1, location_);
		}

		// Token: 0x060003A5 RID: 933 RVA: 0x0002052C File Offset: 0x0001E72C
		public virtual Location vmethod_6(ChartCS chartCS_1)
		{
			Location result;
			if (this.DTValLocation == null)
			{
				result = null;
			}
			else
			{
				int? num = chartCS_1.method_184(this.DTValLocation.X1DateTime) + 1;
				int? num2 = chartCS_1.method_184(this.DTValLocation.X2DateTime) + 1;
				if (num == null && num2 == null)
				{
					result = null;
				}
				else
				{
					double double_;
					if (num == null)
					{
						double_ = (double)num2.Value - this.XPtsGap;
					}
					else
					{
						double_ = (double)num.Value;
					}
					double double_2;
					if (num2 == null)
					{
						double_2 = (double)num.Value + this.XPtsGap;
					}
					else
					{
						double_2 = (double)num2.Value;
					}
					double double_3 = this.method_17(this.DTValLocation.Y1Value);
					double double_4 = this.method_17(this.DTValLocation.Y2Value);
					result = this.vmethod_3(double_, double_3, double_2, double_4);
				}
			}
			return result;
		}

		// Token: 0x060003A6 RID: 934 RVA: 0x00020654 File Offset: 0x0001E854
		public DTValLocation method_7(ChartCS chartCS_1, Location location_1)
		{
			DTValLocation result;
			if (location_1 == null)
			{
				result = null;
			}
			else
			{
				int num = Convert.ToInt32(Math.Round(location_1.X1)) - 1;
				double y = location_1.Y1;
				int num2 = Convert.ToInt32(Math.Round(location_1.X2)) - 1;
				double y2 = location_1.Y2;
				double y1Val = y;
				double y2Val = y2;
				int num3 = chartCS_1.FirstItemIndex - 1;
				int num4 = num3 + num;
				int num5 = num3 + num2;
				if (num4 < 0 && num5 < 0)
				{
					Class182.smethod_0(new Exception("Can't get DTValLocation from Location! All xIdx < 0."));
					result = null;
				}
				else
				{
					DTValLocation dtvalLocation;
					try
					{
						if (num4 < 0)
						{
							y1Val = (y2 - y) * (double)(-(double)num4) / (double)(num5 - num4) + y;
							num4 = 0;
						}
						else if (num5 < 0)
						{
							y2Val = (y - y2) * (double)(-(double)num5) / (double)(num4 - num5) + y2;
							num5 = 0;
						}
						int count = chartCS_1.HisDataPeriodSet.PeriodHisDataList.Count;
						if (num4 >= count && num5 >= count)
						{
							Class182.smethod_0(new Exception("Can't get DTValLocation from Location! All xIdx > pHDCount."));
							dtvalLocation = null;
						}
						else
						{
							if (num4 >= count - 1)
							{
								y1Val = y - (double)((num4 - count - 1) / (num4 - num5)) * (y - y2);
								int num6 = num4 - count + 2;
								num4 = count - 2;
								this.Location = this.vmethod_3(location_1.X1 - (double)num6, location_1.Y1, location_1.X2, location_1.Y2);
							}
							else if (num5 >= count - 1)
							{
								y2Val = (y2 - y) * (double)(-(double)num4) / (double)(num5 - num4) + y;
								int num7 = num5 - count + 2;
								num5 = count - 2;
								this.Location = this.vmethod_3(location_1.X1, location_1.Y1, location_1.X2 - (double)num7, location_1.Y2);
							}
							int index = this.method_8(num4 + 1, chartCS_1.HisDataPeriodSet.PeriodHisDataList.Count);
							int index2 = this.method_8(num5 + 1, chartCS_1.HisDataPeriodSet.PeriodHisDataList.Count);
							DateTime x1DT = chartCS_1.HisDataPeriodSet.PeriodHisDataList.Keys[index];
							DateTime x2DT = chartCS_1.HisDataPeriodSet.PeriodHisDataList.Keys[index2];
							dtvalLocation = new DTValLocation(x1DT, y1Val, x2DT, y2Val);
						}
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
						dtvalLocation = null;
					}
					result = dtvalLocation;
				}
			}
			return result;
		}

		// Token: 0x060003A7 RID: 935 RVA: 0x00020894 File Offset: 0x0001EA94
		private int method_8(int int_1, int int_2)
		{
			if (int_1 < 0)
			{
				Class182.smethod_0(new Exception("idx < 0!"));
				int_1 = 0;
			}
			if (int_1 >= int_2)
			{
				Class182.smethod_0(new Exception("idx > count!"));
				int_1 = int_2 - 1;
			}
			return int_1;
		}

		// Token: 0x060003A8 RID: 936 RVA: 0x000208D4 File Offset: 0x0001EAD4
		public virtual void vmethod_7(ChartCS chartCS_1)
		{
			DTValLocation dtvalLocation = this.vmethod_8(chartCS_1);
			if (dtvalLocation != null)
			{
				this.EndPtDTValLoc = dtvalLocation;
			}
		}

		// Token: 0x060003A9 RID: 937 RVA: 0x000208F8 File Offset: 0x0001EAF8
		protected virtual DTValLocation vmethod_8(ChartCS chartCS_1)
		{
			Location location = this.Location;
			DTValLocation result;
			if (location != null)
			{
				result = this.method_7(chartCS_1, location);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060003AA RID: 938 RVA: 0x00020920 File Offset: 0x0001EB20
		public virtual void vmethod_9(ChartCS chartCS_1)
		{
			try
			{
				GraphObj graphObj = chartCS_1.GraphPane.GraphObjList.First(new Func<GraphObj, bool>(this.method_33));
				this.XPtsGap = graphObj.Location.X2 - graphObj.Location.X1;
			}
			catch
			{
			}
		}

		// Token: 0x060003AB RID: 939 RVA: 0x00020980 File Offset: 0x0001EB80
		public virtual bool vmethod_10(ChartCS chartCS_1, bool bool_6 = false, bool bool_7 = false)
		{
			bool result;
			if (!this.method_21(chartCS_1))
			{
				this.vmethod_20(chartCS_1);
				result = false;
			}
			else if (this.DTValLocation == null)
			{
				result = false;
			}
			else
			{
				int? num = chartCS_1.method_184(this.DTValLocation.X1DateTime);
				int? num2 = chartCS_1.method_184(this.DTValLocation.X2DateTime);
				bool? flag = this.method_9(chartCS_1, bool_6, bool_7, num, num2);
				if (flag != null)
				{
					if (flag.Value)
					{
						chartCS_1.GraphPane.GraphObjList.RemoveAll(new Predicate<GraphObj>(this.method_34));
						if (num == null)
						{
							num = new int?(num2.Value - Convert.ToInt32(Math.Round(this.XPtsGap)));
						}
						if (num2 == null)
						{
							num2 = new int?(num.Value + Convert.ToInt32(Math.Round(this.XPtsGap)));
						}
						double y = this.Location.Y1;
						double y2 = this.Location.Y2;
						this.vmethod_18(chartCS_1, Convert.ToDouble(num + 1), y, Convert.ToDouble(num2 + 1), y2, false, bool_7);
						return true;
					}
				}
				else if (this.method_20(chartCS_1))
				{
					this.vmethod_20(chartCS_1);
				}
				result = false;
			}
			return result;
		}

		// Token: 0x060003AC RID: 940 RVA: 0x00020B0C File Offset: 0x0001ED0C
		private bool? method_9(ChartCS chartCS_1, bool bool_6, bool bool_7, int? nullable_4, int? nullable_5)
		{
			bool? result;
			if (nullable_4 != null && nullable_5 != null)
			{
				double num;
				double num2;
				if (this.EndPtDTValLoc != null)
				{
					num = this.EndPtDTValLoc.Y1Value;
					num2 = this.EndPtDTValLoc.Y2Value;
				}
				else
				{
					num = this.method_17(this.DTValLocation.Y1Value);
					num2 = this.method_17(this.DTValLocation.Y2Value);
				}
				double num3 = (num2 >= num) ? num2 : num;
				double num4 = (num2 >= num) ? num : num2;
				if (!chartCS_1.IsInitiating && (num4 >= chartCS_1.GraphPane.YAxis.Scale.Max || num3 <= chartCS_1.GraphPane.YAxis.Scale.Min))
				{
					result = null;
				}
				else if (this.vmethod_11(chartCS_1, nullable_4, nullable_5))
				{
					result = new bool?(true);
				}
				else
				{
					result = null;
				}
			}
			else
			{
				result = new bool?(false);
			}
			return result;
		}

		// Token: 0x060003AD RID: 941 RVA: 0x00020C00 File Offset: 0x0001EE00
		protected virtual bool vmethod_11(ChartCS chartCS_1, int? nullable_4, int? nullable_5)
		{
			int? num = chartCS_1.method_184(this.EndPtDTValLoc.X1DateTime);
			int? num2 = chartCS_1.method_184(this.EndPtDTValLoc.X2DateTime);
			int? num3;
			if (num != null)
			{
				num3 = num;
				if (num3.GetValueOrDefault() > 0 & num3 != null)
				{
					goto IL_66;
				}
			}
			if (num2 == null)
			{
				goto IL_7D;
			}
			num3 = num2;
			if (!(num3.GetValueOrDefault() > 0 & num3 != null))
			{
				goto IL_7D;
			}
			IL_66:
			if (nullable_4 != null || nullable_5 != null)
			{
				return true;
			}
			IL_7D:
			return false;
		}

		// Token: 0x060003AE RID: 942 RVA: 0x00003990 File Offset: 0x00001B90
		public void method_10(ChartCS chartCS_1)
		{
			if (!this.bool_0)
			{
				this.bool_0 = true;
				this.vmethod_12(chartCS_1);
				chartCS_1.SelectedDrawObj = this;
			}
		}

		// Token: 0x060003AF RID: 943 RVA: 0x000039B1 File Offset: 0x00001BB1
		public void method_11(ChartCS chartCS_1)
		{
			this.vmethod_12(chartCS_1);
		}

		// Token: 0x060003B0 RID: 944 RVA: 0x00020C94 File Offset: 0x0001EE94
		protected virtual void vmethod_12(ChartCS chartCS_1)
		{
			GraphPane graphPane = chartCS_1.GraphPane;
			if (Base.UI.Form.ChartTheme != ChartTheme.Classic)
			{
				Color black = Color.Black;
			}
			else
			{
				Color white = Color.White;
			}
			double num = (double)chartCS_1.ChtCtrl.MaxSticksPerChart / 50.0;
			double double_ = (graphPane.YAxis.Scale.Max - graphPane.YAxis.Scale.Min) * 0.6800000071525574 * num / (double)(graphPane.Rect.Height / graphPane.Rect.Width * (float)chartCS_1.ChtCtrl.MaxSticksPerChart);
			List<Location> list_ = this.vmethod_13(chartCS_1);
			this.method_12(chartCS_1, list_, num, double_);
		}

		// Token: 0x060003B1 RID: 945 RVA: 0x00020D4C File Offset: 0x0001EF4C
		protected virtual List<Location> vmethod_13(ChartCS chartCS_1)
		{
			return new List<Location>
			{
				this.Location
			};
		}

		// Token: 0x060003B2 RID: 946 RVA: 0x00020D70 File Offset: 0x0001EF70
		private void method_12(ChartCS chartCS_1, List<Location> list_2, double double_1, double double_2)
		{
			Color color_ = (Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.White : Color.Black;
			foreach (Location location in list_2)
			{
				this.method_13(chartCS_1, location.X1 - 0.4 * double_1, location.Y1 + double_2 / 2.0, 0.8 * double_1, double_2, color_);
				if (this.Location.X2 != this.Location.X1 || this.Location.Y2 != this.Location.Y1)
				{
					this.method_13(chartCS_1, location.X2 - 0.4 * double_1, location.Y2 + double_2 / 2.0, 0.8 * double_1, double_2, color_);
				}
			}
		}

		// Token: 0x060003B3 RID: 947 RVA: 0x00020E80 File Offset: 0x0001F080
		protected void method_13(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, Color color_0)
		{
			BoxObj boxObj = new BoxObj(double_1, double_2, double_3, double_4, color_0, Color.Transparent);
			boxObj.Tag = this.Tag + DrawObj.string_0;
			boxObj.ZOrder = ZOrder.A_InFront;
			boxObj.IsClippedToChartRect = true;
			boxObj.Location.CoordinateFrame = CoordType.AxisXYScale;
			chartCS_1.GraphPane.GraphObjList.Add(boxObj);
		}

		// Token: 0x060003B4 RID: 948 RVA: 0x000039BC File Offset: 0x00001BBC
		public void method_14(ChartCS chartCS_1)
		{
			if (this.bool_0)
			{
				this.bool_0 = false;
				this.vmethod_14(chartCS_1);
				chartCS_1.SelectedDrawObj = null;
			}
		}

		// Token: 0x060003B5 RID: 949 RVA: 0x000039DD File Offset: 0x00001BDD
		public void method_15(ChartCS chartCS_1)
		{
			this.vmethod_14(chartCS_1);
		}

		// Token: 0x060003B6 RID: 950 RVA: 0x00020EE4 File Offset: 0x0001F0E4
		protected virtual void vmethod_14(ChartCS chartCS_1)
		{
			if (chartCS_1 != null)
			{
				GraphPane graphPane = chartCS_1.GraphPane;
				try
				{
					if (graphPane.GraphObjList.Where(new Func<GraphObj, bool>(this.method_35)).Any<GraphObj>())
					{
						graphPane.GraphObjList.RemoveAll(new Predicate<GraphObj>(this.method_36));
						chartCS_1.ZedGraphControl.Refresh();
					}
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
		}

		// Token: 0x060003B7 RID: 951 RVA: 0x00020F58 File Offset: 0x0001F158
		protected void method_16(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4)
		{
			string text = DrawObj.string_1 + Guid.NewGuid().ToString().Substring(0, 23);
			this.Tag = text;
			this.vmethod_15(chartCS_1, double_1, double_2, double_3, double_4, text);
		}

		// Token: 0x060003B8 RID: 952 RVA: 0x00020FA4 File Offset: 0x0001F1A4
		protected virtual void vmethod_15(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list_ = this.vmethod_0(chartCS_1, double_1, double_2, double_3, double_4, string_5);
			this.vmethod_21(chartCS_1, list_);
			this.list_0 = list_;
		}

		// Token: 0x060003B9 RID: 953 RVA: 0x00020FD4 File Offset: 0x0001F1D4
		private double method_17(double double_1)
		{
			double result;
			if (this.Chart.Symbol.IsStock && this.Chart.SymbDataSet.CurrStSplitList != null && this.Chart.SymbDataSet.CurrStSplitList.Any<StSplit>())
			{
				DateTime date = this.Chart.SymbDataSet.CurrHisData.Date;
				DateTime dateTime_ = this.DTValLocation.X2DateTime;
				if (this.DTValLocation.X1DateTime > this.DTValLocation.X2DateTime)
				{
					dateTime_ = this.DTValLocation.X1DateTime;
				}
				result = this.Chart.SymbDataSet.method_98(double_1, dateTime_);
			}
			else
			{
				result = double_1;
			}
			return result;
		}

		// Token: 0x060003BA RID: 954 RVA: 0x00021084 File Offset: 0x0001F284
		public virtual void vmethod_16()
		{
			if (this.Chart != null)
			{
				this.vmethod_17(this.Chart, this.Location.X1, this.Location.Y1, this.Location.X2, this.Location.Y2);
			}
		}

		// Token: 0x060003BB RID: 955 RVA: 0x000039E8 File Offset: 0x00001BE8
		public virtual void vmethod_17(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4)
		{
			this.vmethod_18(chartCS_1, double_1, double_2, double_3, double_4, false, false);
		}

		// Token: 0x060003BC RID: 956 RVA: 0x000210D4 File Offset: 0x0001F2D4
		public virtual void vmethod_18(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, bool bool_6, bool bool_7)
		{
			this.vmethod_20(chartCS_1);
			double double_5 = double_2;
			double double_6 = double_4;
			if (this.DTValLocation != null)
			{
				if (chartCS_1.Symbol.IsStock)
				{
					if (!bool_6 && !this.DisableRstPrice && bool_7)
					{
						this.nullable_0 = new double?(this.method_17(this.DTValLocation.Y1Value));
						this.nullable_1 = new double?(this.method_17(this.DTValLocation.Y2Value));
					}
					else
					{
						this.nullable_0 = new double?(this.EndPtDTValLoc.Y1Value);
						this.nullable_1 = new double?(this.EndPtDTValLoc.Y2Value);
					}
				}
				else
				{
					this.nullable_0 = new double?(this.DTValLocation.Y1Value);
					this.nullable_1 = new double?(this.DTValLocation.Y2Value);
				}
			}
			if (bool_6)
			{
				this.nullable_0 = new double?(double_2);
				this.nullable_1 = new double?(double_4);
			}
			else
			{
				if (this.nullable_0 != null)
				{
					double_5 = this.nullable_0.Value;
				}
				if (this.nullable_1 != null)
				{
					double_6 = this.nullable_1.Value;
				}
			}
			this.vmethod_5(chartCS_1, double_1, double_5, double_3, double_6);
			List<GraphObj> list = this.vmethod_0(chartCS_1, this.Location.X1, double_5, this.Location.X2, double_6, this.Tag);
			this.vmethod_21(chartCS_1, list);
			this.GraphObjLst = list;
			this.vmethod_7(chartCS_1);
			if (this.IsSelected)
			{
				this.vmethod_12(chartCS_1);
			}
			this.Chart = chartCS_1;
		}

		// Token: 0x060003BD RID: 957 RVA: 0x00021260 File Offset: 0x0001F460
		public virtual PointD vmethod_19(ChartCS chartCS_1, PointD pointD_0)
		{
			PointD result;
			if (this.method_18(pointD_0, this.Location))
			{
				result = new PointD(this.Location.X2, this.Location.Y2);
			}
			else
			{
				result = new PointD(this.Location.X1, this.Location.Y1);
			}
			return result;
		}

		// Token: 0x060003BE RID: 958 RVA: 0x000212BC File Offset: 0x0001F4BC
		protected bool method_18(PointD pointD_0, Location location_1)
		{
			return this.method_19(pointD_0, new PointD(location_1.X1, location_1.Y1), new PointD(location_1.X2, location_1.Y2));
		}

		// Token: 0x060003BF RID: 959 RVA: 0x000212F8 File Offset: 0x0001F4F8
		protected bool method_19(PointD pointD_0, PointD pointD_1, PointD pointD_2)
		{
			bool result = true;
			if (Math.Abs(pointD_1.X - pointD_0.X) > Math.Abs(pointD_2.X - pointD_0.X))
			{
				result = false;
			}
			else if (Math.Abs(pointD_1.X - pointD_0.X) == Math.Abs(pointD_2.X - pointD_0.X) && Math.Abs(pointD_1.Y - pointD_0.Y) > Math.Abs(pointD_2.Y - pointD_0.Y))
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060003C0 RID: 960 RVA: 0x00021384 File Offset: 0x0001F584
		public virtual void vmethod_20(ChartCS chartCS_1)
		{
			if (chartCS_1 != null && chartCS_1.GraphPane != null)
			{
				this.vmethod_14(chartCS_1);
				try
				{
					chartCS_1.GraphPane.GraphObjList.RemoveAll(new Predicate<GraphObj>(this.method_37));
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
		}

		// Token: 0x060003C1 RID: 961 RVA: 0x000213DC File Offset: 0x0001F5DC
		public bool method_20(ChartCS chartCS_1)
		{
			return chartCS_1.GraphPane.GraphObjList.Where(new Func<GraphObj, bool>(this.method_38)).Any<GraphObj>();
		}

		// Token: 0x060003C2 RID: 962 RVA: 0x00021410 File Offset: 0x0001F610
		public virtual void vmethod_21(ChartCS chartCS_1, List<GraphObj> list_2)
		{
			foreach (GraphObj item in list_2)
			{
				chartCS_1.GraphPane.GraphObjList.Add(item);
			}
		}

		// Token: 0x060003C3 RID: 963 RVA: 0x0002146C File Offset: 0x0001F66C
		public bool method_21(ChartCS chartCS_1)
		{
			if (chartCS_1 != null)
			{
				bool flag;
				if (this.PeriodType == chartCS_1.HisDataPeriodSet.PeriodType)
				{
					int? periodUnits = this.PeriodUnits;
					int? periodUnits2 = chartCS_1.HisDataPeriodSet.PeriodUnits;
					flag = (periodUnits.GetValueOrDefault() == periodUnits2.GetValueOrDefault() & periodUnits != null == (periodUnits2 != null));
				}
				else
				{
					flag = false;
				}
				bool flag2 = flag;
				bool flag3 = this.Chart != null && this.Chart == chartCS_1;
				if ((this.AcctID == Base.Acct.CurrAccount.ID & this.SymbolCode == chartCS_1.Symbol.Code) && flag2 && (chartCS_1.IsInitiating || flag3 || this.Chart == null))
				{
					return true;
				}
			}
			return false;
		}

		// Token: 0x060003C4 RID: 964 RVA: 0x00021530 File Offset: 0x0001F730
		protected LineObj method_22(ChartCS chartCS_1, double double_1, string string_5)
		{
			double min = chartCS_1.GraphPane.YAxis.Scale.Min;
			double max = chartCS_1.GraphPane.YAxis.Scale.Max;
			return this.method_23(double_1, min, double_1, max, this.Tag);
		}

		// Token: 0x060003C5 RID: 965 RVA: 0x00021580 File Offset: 0x0001F780
		protected LineObj method_23(double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			DashStyle dashStyle_ = DashStyle.Solid;
			DrawLineStyle lineStyle = this.LineStyle;
			LineObj result;
			if (lineStyle != null)
			{
				if (lineStyle.LineType != DrawLineType.Solid)
				{
					dashStyle_ = DashStyle.Custom;
				}
				result = this.method_24(double_1, double_2, double_3, double_4, string_5, lineStyle.PenWidth, dashStyle_, lineStyle.DashPattern);
			}
			else
			{
				float float_ = 1f;
				float float_2 = 1f;
				float float_3 = 1f;
				result = this.method_25(double_1, double_2, double_3, double_4, string_5, float_3, dashStyle_, float_, float_2);
			}
			return result;
		}

		// Token: 0x060003C6 RID: 966 RVA: 0x000215EC File Offset: 0x0001F7EC
		protected LineObj method_24(double double_1, double double_2, double double_3, double double_4, string string_5, float float_0, DashStyle dashStyle_0, float[] float_1)
		{
			return new LineObj(this.method_26(), double_1, double_2, double_3, double_4)
			{
				IsClippedToChartRect = true,
				Line = 
				{
					Style = dashStyle_0,
					DashPattern = float_1,
					IsAntiAlias = true,
					Width = float_0
				},
				Tag = string_5,
				ZOrder = ZOrder.B_BehindLegend
			};
		}

		// Token: 0x060003C7 RID: 967 RVA: 0x00021658 File Offset: 0x0001F858
		protected LineObj method_25(double double_1, double double_2, double double_3, double double_4, string string_5, float float_0, DashStyle dashStyle_0, float float_1, float float_2)
		{
			return new LineObj(this.method_26(), double_1, double_2, double_3, double_4)
			{
				IsClippedToChartRect = true,
				Line = 
				{
					Style = dashStyle_0,
					DashOn = float_1,
					DashOff = float_2,
					IsAntiAlias = true,
					Width = float_0
				},
				Tag = string_5,
				ZOrder = ZOrder.B_BehindLegend
			};
		}

		// Token: 0x060003C8 RID: 968 RVA: 0x000216D0 File Offset: 0x0001F8D0
		protected Color method_26()
		{
			Color result;
			if (this.LineColor != null)
			{
				result = this.LineColor.Value;
			}
			else
			{
				result = ((Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.White : Color.Black);
			}
			return result;
		}

		// Token: 0x060003C9 RID: 969 RVA: 0x0002171C File Offset: 0x0001F91C
		protected TextObj method_27(ChartCS chartCS_1, double double_1, double double_2, string string_5, FontSpec fontSpec_0, string string_6)
		{
			Color fontColor = this.method_26();
			TextObj textObj = new TextObj(string_5, double_1, double_2, CoordType.AxisXYScale);
			if (fontSpec_0 != null)
			{
				textObj.FontSpec.Size = fontSpec_0.Size;
				textObj.FontSpec.Family = fontSpec_0.Family;
				textObj.FontSpec.IsBold = fontSpec_0.IsBold;
				textObj.FontSpec.IsItalic = fontSpec_0.IsItalic;
				textObj.FontSpec.IsUnderline = fontSpec_0.IsUnderline;
			}
			else
			{
				chartCS_1.method_60(textObj);
			}
			textObj.FontSpec.FontColor = fontColor;
			textObj.FontSpec.Border.IsVisible = false;
			textObj.FontSpec.Fill.IsVisible = false;
			textObj.FontSpec.StringAlignment = StringAlignment.Near;
			textObj.IsClippedToChartRect = true;
			textObj.ZOrder = ZOrder.A_InFront;
			textObj.Tag = string_6;
			return textObj;
		}

		// Token: 0x060003CA RID: 970 RVA: 0x000217F8 File Offset: 0x0001F9F8
		protected virtual List<DrawSublineParam> vmethod_22()
		{
			return null;
		}

		// Token: 0x060003CB RID: 971 RVA: 0x0002180C File Offset: 0x0001FA0C
		protected List<DrawSublineParam> method_28(List<double> list_2, double double_1, double double_2, int int_1 = 0)
		{
			List<DrawSublineParam> list = new List<DrawSublineParam>();
			for (int i = 0; i < list_2.Count; i++)
			{
				double value = list_2[i];
				list.Add(new DrawSublineParam
				{
					Name = (i + 1).ToString(),
					Enabled = true,
					Value = value,
					MinValue = double_1,
					MaxValue = new double?(double_2),
					DigitNb = int_1
				});
			}
			return list;
		}

		// Token: 0x060003CC RID: 972 RVA: 0x00021888 File Offset: 0x0001FA88
		protected object method_29(DrawObjParamType drawObjParamType_0)
		{
			object result = null;
			string key = base.GetType().ToString();
			Dictionary<string, Dictionary<string, object>> userDrawObjParamsDict = Base.UI.Form.UserDrawObjParamsDict;
			if (userDrawObjParamsDict != null && userDrawObjParamsDict.ContainsKey(key))
			{
				Dictionary<string, object> dictionary = userDrawObjParamsDict[key];
				if (dictionary != null)
				{
					dictionary.TryGetValue(drawObjParamType_0.ToString(), out result);
				}
			}
			return result;
		}

		// Token: 0x060003CD RID: 973 RVA: 0x000218E4 File Offset: 0x0001FAE4
		protected virtual DrawLineStyle vmethod_23()
		{
			return new DrawLineStyle(DrawLineType.Solid, DrawLineWidth.Normal, null);
		}

		// Token: 0x060003CE RID: 974 RVA: 0x00021900 File Offset: 0x0001FB00
		public void method_30()
		{
			foreach (ChtCtrl_KLine chtCtrl_KLine in Base.UI.ChtCtrl_KLineList)
			{
				ChartCS chart_CS = chtCtrl_KLine.Chart_CS;
				if (this.method_21(chart_CS) && this.method_20(chart_CS))
				{
					this.method_14(chart_CS);
					this.vmethod_20(chart_CS);
					chart_CS.ZedGraphControl.Refresh();
				}
			}
			Base.UI.DrawObjList.Remove(this);
			Base.UI.smethod_136();
		}

		// Token: 0x060003D0 RID: 976 RVA: 0x00021990 File Offset: 0x0001FB90
		[CompilerGenerated]
		private bool method_31(GraphObj graphObj_0)
		{
			bool result;
			if (graphObj_0 is BoxObj && graphObj_0.Tag != null)
			{
				result = (graphObj_0.Tag.ToString() == this.Tag + DrawObj.string_0);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060003D1 RID: 977 RVA: 0x000219D8 File Offset: 0x0001FBD8
		[CompilerGenerated]
		private bool method_32(GraphObj graphObj_0)
		{
			bool result;
			if (graphObj_0.Tag != null)
			{
				result = (graphObj_0.Tag.ToString() == this.Tag);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060003D2 RID: 978 RVA: 0x00021A0C File Offset: 0x0001FC0C
		[CompilerGenerated]
		private bool method_33(GraphObj graphObj_0)
		{
			bool result;
			if (graphObj_0.Tag != null)
			{
				result = graphObj_0.Tag.ToString().Equals(this.Tag);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060003D3 RID: 979 RVA: 0x000219D8 File Offset: 0x0001FBD8
		[CompilerGenerated]
		private bool method_34(GraphObj graphObj_0)
		{
			bool result;
			if (graphObj_0.Tag != null)
			{
				result = (graphObj_0.Tag.ToString() == this.Tag);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060003D4 RID: 980 RVA: 0x00021990 File Offset: 0x0001FB90
		[CompilerGenerated]
		private bool method_35(GraphObj graphObj_0)
		{
			bool result;
			if (graphObj_0 is BoxObj && graphObj_0.Tag != null)
			{
				result = (graphObj_0.Tag.ToString() == this.Tag + DrawObj.string_0);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060003D5 RID: 981 RVA: 0x00021990 File Offset: 0x0001FB90
		[CompilerGenerated]
		private bool method_36(GraphObj graphObj_0)
		{
			bool result;
			if (graphObj_0 is BoxObj && graphObj_0.Tag != null)
			{
				result = (graphObj_0.Tag.ToString() == this.Tag + DrawObj.string_0);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060003D6 RID: 982 RVA: 0x00021A40 File Offset: 0x0001FC40
		[CompilerGenerated]
		private bool method_37(GraphObj graphObj_0)
		{
			bool result;
			if (graphObj_0 != null && graphObj_0.Tag != null)
			{
				result = (graphObj_0.Tag.ToString() == this.Tag);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060003D7 RID: 983 RVA: 0x00021A40 File Offset: 0x0001FC40
		[CompilerGenerated]
		private bool method_38(GraphObj graphObj_0)
		{
			bool result;
			if (graphObj_0 != null && graphObj_0.Tag != null)
			{
				result = (graphObj_0.Tag.ToString() == this.Tag);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x04000133 RID: 307
		private double? nullable_0;

		// Token: 0x04000134 RID: 308
		private double? nullable_1;

		// Token: 0x04000135 RID: 309
		public static readonly string string_0 = "_SBox";

		// Token: 0x04000136 RID: 310
		public static readonly string string_1 = "DWOBJ_";

		// Token: 0x04000137 RID: 311
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000138 RID: 312
		[CompilerGenerated]
		private EventHandler eventHandler_1;

		// Token: 0x04000139 RID: 313
		private ChartCS chartCS_0;

		// Token: 0x0400013A RID: 314
		private Location location_0;

		// Token: 0x0400013B RID: 315
		private int int_0;

		// Token: 0x0400013C RID: 316
		private string string_2;

		// Token: 0x0400013D RID: 317
		private PeriodType periodType_0;

		// Token: 0x0400013E RID: 318
		private int? nullable_2;

		// Token: 0x0400013F RID: 319
		private DTValLocation dtvalLocation_0;

		// Token: 0x04000140 RID: 320
		private DTValLocation dtvalLocation_1;

		// Token: 0x04000141 RID: 321
		private Color? nullable_3;

		// Token: 0x04000142 RID: 322
		private string string_3;

		// Token: 0x04000143 RID: 323
		private bool bool_0;

		// Token: 0x04000144 RID: 324
		private bool bool_1;

		// Token: 0x04000145 RID: 325
		private bool bool_2;

		// Token: 0x04000146 RID: 326
		private bool bool_3;

		// Token: 0x04000147 RID: 327
		private double double_0;

		// Token: 0x04000148 RID: 328
		private bool bool_4;

		// Token: 0x04000149 RID: 329
		private bool bool_5;

		// Token: 0x0400014A RID: 330
		private List<GraphObj> list_0;

		// Token: 0x0400014B RID: 331
		private List<DrawSublineParam> list_1;

		// Token: 0x0400014C RID: 332
		private string string_4;

		// Token: 0x0400014D RID: 333
		private DrawLineStyle drawLineStyle_0;
	}
}
