﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000075 RID: 117
	[Serializable]
	internal sealed class DrawLineDH : DrawObj, ISerializable
	{
		// Token: 0x06000435 RID: 1077 RVA: 0x000037AA File Offset: 0x000019AA
		public DrawLineDH()
		{
		}

		// Token: 0x06000436 RID: 1078 RVA: 0x00003CFA File Offset: 0x00001EFA
		public DrawLineDH(ChartCS chart, double x1, double y1, double x2, double y2) : this(chart, x1, y1, x2, y1, false)
		{
		}

		// Token: 0x06000437 RID: 1079 RVA: 0x00003D09 File Offset: 0x00001F09
		public DrawLineDH(ChartCS chart, double x1, double y1, double x2, double y2, bool disableRstPrc) : base(chart, x1, y1, x2, y1, disableRstPrc)
		{
			base.Name = "水平线段";
			base.CanChgColor = true;
			base.IsOneClickLoc = false;
		}

		// Token: 0x06000438 RID: 1080 RVA: 0x000037DC File Offset: 0x000019DC
		protected DrawLineDH(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x06000439 RID: 1081 RVA: 0x000037ED File Offset: 0x000019ED
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x0600043A RID: 1082 RVA: 0x00022F4C File Offset: 0x0002114C
		protected override Location vmethod_3(double double_1, double double_2, double double_3, double double_4)
		{
			return new Location(double_1, double_2, double_3 - double_1, 0.0, CoordType.AxisXYScale, AlignH.Left, AlignV.Top);
		}

		// Token: 0x0600043B RID: 1083 RVA: 0x00022F74 File Offset: 0x00021174
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			LineObj item = base.method_23(double_1, double_2, double_3, double_2, string_5);
			list.Add(item);
			return list;
		}
	}
}
