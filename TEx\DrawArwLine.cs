﻿using System;
using System.Collections.Generic;
using System.Drawing.Drawing2D;
using System.Runtime.Serialization;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000193 RID: 403
	[Serializable]
	internal sealed class DrawArwLine : DrawObj, ISerializable
	{
		// Token: 0x06000F9B RID: 3995 RVA: 0x000037AA File Offset: 0x000019AA
		public DrawArwLine()
		{
		}

		// Token: 0x06000F9C RID: 3996 RVA: 0x00006B0A File Offset: 0x00004D0A
		public DrawArwLine(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = "箭头线";
			base.CanChgColor = true;
			base.IsOneClickLoc = false;
		}

		// Token: 0x06000F9D RID: 3997 RVA: 0x000037DC File Offset: 0x000019DC
		protected DrawArwLine(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x06000F9E RID: 3998 RVA: 0x000037ED File Offset: 0x000019ED
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x06000F9F RID: 3999 RVA: 0x0006219C File Offset: 0x0006039C
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			ArrowObj item = this.method_39(double_1, double_2, double_3, double_4, string_5);
			list.Add(item);
			return list;
		}

		// Token: 0x06000FA0 RID: 4000 RVA: 0x000621C8 File Offset: 0x000603C8
		private ArrowObj method_39(double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			ArrowObj arrowObj = new ArrowObj(base.method_26(), 12f, double_1, double_2, double_3, double_4);
			arrowObj.IsClippedToChartRect = true;
			DrawLineStyle lineStyle = base.LineStyle;
			if (lineStyle != null)
			{
				if (lineStyle.LineType != DrawLineType.Solid)
				{
					arrowObj.Line.Style = DashStyle.Custom;
				}
				if (lineStyle.DashPattern != null)
				{
					arrowObj.Line.DashOn = lineStyle.DashPattern[0];
					arrowObj.Line.DashOff = lineStyle.DashPattern[1];
				}
				arrowObj.Line.Width = lineStyle.PenWidth;
			}
			else
			{
				arrowObj.Line.Style = DashStyle.Solid;
			}
			arrowObj.Line.IsAntiAlias = true;
			arrowObj.Tag = string_5;
			arrowObj.ZOrder = ZOrder.A_InFront;
			return arrowObj;
		}
	}
}
