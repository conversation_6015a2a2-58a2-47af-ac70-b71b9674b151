﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns25;

namespace ns30
{
	// Token: 0x0200040F RID: 1039
	[DesignerCategory("Code")]
	internal sealed class Class545 : Label
	{
		// Token: 0x06002822 RID: 10274 RVA: 0x001036FC File Offset: 0x001018FC
		private void method_0()
		{
			try
			{
				using (Graphics graphics = base.CreateGraphics())
				{
					int num = Class543.smethod_2(graphics, this.Text, this.Font, base.Width);
					if (num > 0)
					{
						base.Height = num;
					}
				}
			}
			catch
			{
			}
		}

		// Token: 0x06002823 RID: 10275 RVA: 0x0000F886 File Offset: 0x0000DA86
		protected void OnFontChanged(EventArgs e)
		{
			base.OnFontChanged(e);
			this.method_0();
		}

		// Token: 0x06002824 RID: 10276 RVA: 0x0000F895 File Offset: 0x0000DA95
		protected void OnResize(EventArgs e)
		{
			base.OnResize(e);
			this.method_0();
		}

		// Token: 0x06002825 RID: 10277 RVA: 0x0000F8A4 File Offset: 0x0000DAA4
		protected void OnTextChanged(EventArgs e)
		{
			base.OnTextChanged(e);
			this.method_0();
		}

		// Token: 0x06002826 RID: 10278 RVA: 0x0000F8B3 File Offset: 0x0000DAB3
		public Class545()
		{
			base.FlatStyle = FlatStyle.System;
			base.UseMnemonic = false;
		}
	}
}
