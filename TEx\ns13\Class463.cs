﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Xml.Linq;
using ns2;
using ns28;
using TEx;
using TEx.ImportTrans;
using TEx.Trading;
using TEx.Util;

namespace ns13
{
	// Token: 0x02000359 RID: 857
	internal static class Class463
	{
		// Token: 0x060023CC RID: 9164 RVA: 0x000F0BC8 File Offset: 0x000EEDC8
		public static bool smethod_0(CfmmcAcct cfmmcAcct_0)
		{
			return Class463.smethod_1(cfmmcAcct_0, Class463.string_0);
		}

		// Token: 0x060023CD RID: 9165 RVA: 0x000F0BE4 File Offset: 0x000EEDE4
		public static bool smethod_1(CfmmcAcct cfmmcAcct_0, string string_1)
		{
			bool result;
			try
			{
				if (File.Exists(string_1))
				{
					XDocument xdocument = XDocument.Load(string_1);
					XElement root = xdocument.Root;
					IEnumerable<XElement> enumerable = root.Element("Accts").Elements("Acct");
					bool flag = false;
					foreach (XElement xelement in enumerable)
					{
						if (xelement.Attribute("Id").Value == cfmmcAcct_0.ID)
						{
							Class463.smethod_4(xelement, cfmmcAcct_0);
							XElement xelement2 = xelement.Element("BindingAccts");
							if (xelement2 != null)
							{
								xelement2.Elements().Remove<XElement>();
								xelement2.Remove();
							}
							Class463.smethod_6(xelement, cfmmcAcct_0.BindingAccts);
							flag = true;
							break;
						}
					}
					if (!flag)
					{
						XElement content = Class463.smethod_3(cfmmcAcct_0);
						root.Element("Accts").Add(content);
					}
					xdocument.Save(string_1);
				}
				else
				{
					Class463.smethod_2(cfmmcAcct_0).Save(string_1);
				}
				goto IL_10E;
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
				result = false;
			}
			return result;
			IL_10E:
			return true;
		}

		// Token: 0x060023CE RID: 9166 RVA: 0x000F0D24 File Offset: 0x000EEF24
		private static XDocument smethod_2(CfmmcAcct cfmmcAcct_0)
		{
			XDocument xdocument = new XDocument();
			XElement xelement = new XElement("Root");
			xdocument.Add(xelement);
			XElement xelement2 = new XElement("Accts");
			xelement.Add(xelement2);
			XElement content = Class463.smethod_3(cfmmcAcct_0);
			xelement2.Add(content);
			return xdocument;
		}

		// Token: 0x060023CF RID: 9167 RVA: 0x000F0D78 File Offset: 0x000EEF78
		private static XElement smethod_3(CfmmcAcct cfmmcAcct_0)
		{
			XElement xelement = new XElement("Acct");
			Class463.smethod_4(xelement, cfmmcAcct_0);
			Class463.smethod_6(xelement, cfmmcAcct_0.BindingAccts);
			return xelement;
		}

		// Token: 0x060023D0 RID: 9168 RVA: 0x000F0DAC File Offset: 0x000EEFAC
		private static void smethod_4(XElement xelement_0, CfmmcAcct cfmmcAcct_0)
		{
			xelement_0.SetAttributeValue("Id", cfmmcAcct_0.ID);
			xelement_0.SetAttributeValue("Password", Utility.Encrypt(cfmmcAcct_0.Password, TApp.string_12));
			if (cfmmcAcct_0.BeginDate != null)
			{
				xelement_0.SetAttributeValue("BeginDate", cfmmcAcct_0.BeginDate.Value.ToString("yyyy-MM-dd"));
			}
			if (cfmmcAcct_0.EndDate != null)
			{
				xelement_0.SetAttributeValue("EndDate", cfmmcAcct_0.EndDate.Value.ToString("yyyy-MM-dd"));
			}
			if (cfmmcAcct_0.LastDownloadTime != null)
			{
				xelement_0.SetAttributeValue("LastDnldTime", cfmmcAcct_0.LastDownloadTime.Value.ToString("yyyy-MM-dd HH:MM:ss"));
			}
			if (!string.IsNullOrEmpty(cfmmcAcct_0.Note))
			{
				xelement_0.SetAttributeValue("Note", cfmmcAcct_0.Note);
			}
			else
			{
				Class463.smethod_5(xelement_0, "Note");
			}
		}

		// Token: 0x060023D1 RID: 9169 RVA: 0x000F0ED4 File Offset: 0x000EF0D4
		private static void smethod_5(XElement xelement_0, string string_1)
		{
			XAttribute xattribute = xelement_0.Attribute(string_1);
			if (xattribute != null)
			{
				xattribute.Remove();
			}
		}

		// Token: 0x060023D2 RID: 9170 RVA: 0x000F0EFC File Offset: 0x000EF0FC
		private static void smethod_6(XElement xelement_0, List<BindingAcct> list_0)
		{
			if (list_0 != null && list_0.Any<BindingAcct>())
			{
				XElement xelement = new XElement("BindingAccts");
				xelement_0.Add(xelement);
				for (int i = 0; i < list_0.Count; i++)
				{
					XElement xelement2 = new XElement("BindingAcct");
					xelement2.SetAttributeValue("Id", list_0[i].Id);
					xelement2.SetAttributeValue("UsrName", list_0[i].UsrName);
					if (list_0[i].BeginDate != null)
					{
						xelement2.SetAttributeValue("BeginDate", list_0[i].BeginDate.Value.ToString("yyyy-MM-dd"));
					}
					if (list_0[i].EndDate != null)
					{
						xelement2.SetAttributeValue("EndDate", list_0[i].EndDate.Value.ToString("yyyy-MM-dd"));
					}
					xelement.Add(xelement2);
				}
			}
		}

		// Token: 0x060023D3 RID: 9171 RVA: 0x000F1030 File Offset: 0x000EF230
		public static List<CfmmcAcct> smethod_7(CfmmcAcct cfmmcAcct_0)
		{
			return Class463.smethod_8(cfmmcAcct_0, Class463.string_0);
		}

		// Token: 0x060023D4 RID: 9172 RVA: 0x000F104C File Offset: 0x000EF24C
		public static List<CfmmcAcct> smethod_8(CfmmcAcct cfmmcAcct_0, string string_1)
		{
			List<CfmmcAcct> result;
			if (!File.Exists(string_1))
			{
				result = null;
			}
			else
			{
				try
				{
					XDocument xdocument = XDocument.Load(string_1);
					foreach (XElement xelement in xdocument.Root.Element("Accts").Elements("Acct"))
					{
						if (xelement.Attribute("Id").Value == cfmmcAcct_0.ID)
						{
							xelement.Remove();
							break;
						}
					}
					xdocument.Save(string_1);
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
				result = Class463.smethod_10(string_1);
			}
			return result;
		}

		// Token: 0x060023D5 RID: 9173 RVA: 0x000F1118 File Offset: 0x000EF318
		public static List<CfmmcAcct> smethod_9()
		{
			return Class463.smethod_10(Class463.string_0);
		}

		// Token: 0x060023D6 RID: 9174 RVA: 0x000F1134 File Offset: 0x000EF334
		public static List<CfmmcAcct> smethod_10(string string_1)
		{
			List<CfmmcAcct> list = new List<CfmmcAcct>();
			List<CfmmcAcct> result;
			if (!File.Exists(string_1))
			{
				result = list;
			}
			else
			{
				List<CfmmcAcct> result2;
				try
				{
					foreach (XElement xelement in XDocument.Load(string_1).Root.Element("Accts").Elements("Acct"))
					{
						string value = xelement.Attribute("Id").Value;
						string password = Utility.Decrypt(xelement.Attribute("Password").Value, TApp.string_12);
						DateTime? begDT = null;
						DateTime? endDT = null;
						XAttribute xattribute = xelement.Attribute("BeginDate");
						if (xattribute != null)
						{
							begDT = new DateTime?(Convert.ToDateTime(xattribute.Value));
						}
						XAttribute xattribute2 = xelement.Attribute("EndDate");
						if (xattribute2 != null)
						{
							endDT = new DateTime?(Convert.ToDateTime(xattribute2.Value));
						}
						DateTime? lastDnldTime = null;
						XAttribute xattribute3 = xelement.Attribute("LastDnldTime");
						if (xattribute3 != null)
						{
							try
							{
								lastDnldTime = new DateTime?(Convert.ToDateTime(xattribute3.Value));
							}
							catch (Exception exception_)
							{
								Class182.smethod_0(exception_);
							}
						}
						string note = string.Empty;
						XAttribute xattribute4 = xelement.Attribute("Note");
						if (xattribute4 != null)
						{
							note = xattribute4.Value;
						}
						CfmmcAcct cfmmcAcct = new CfmmcAcct(value, password, begDT, endDT, lastDnldTime, note);
						list.Add(cfmmcAcct);
						List<BindingAcct> list2 = new List<BindingAcct>();
						cfmmcAcct.BindingAccts = list2;
						XElement xelement2 = xelement.Element("BindingAccts");
						if (xelement2 != null)
						{
							foreach (XElement xelement3 in xelement2.Elements())
							{
								BindingAcct bindingAcct = new BindingAcct();
								bindingAcct.UsrName = xelement3.Attribute("UsrName").Value;
								bindingAcct.Id = Convert.ToInt32(xelement3.Attribute("Id").Value);
								XAttribute xattribute5 = xelement3.Attribute("BeginDate");
								if (xattribute5 != null)
								{
									try
									{
										bindingAcct.BeginDate = new DateTime?(Convert.ToDateTime(xattribute5.Value));
										goto IL_260;
									}
									catch (Exception exception_2)
									{
										Class182.smethod_0(exception_2);
										goto IL_260;
									}
									goto Block_17;
								}
								goto IL_260;
								IL_252:
								list2.Add(bindingAcct);
								continue;
								Block_17:
								XAttribute xattribute6;
								try
								{
									IL_231:
									bindingAcct.EndDate = new DateTime?(Convert.ToDateTime(xattribute6.Value));
								}
								catch (Exception exception_3)
								{
									Class182.smethod_0(exception_3);
								}
								goto IL_252;
								IL_260:
								xattribute6 = xelement3.Attribute("EndDate");
								if (xattribute6 != null)
								{
									goto IL_231;
								}
								goto IL_252;
							}
						}
					}
					goto IL_2B0;
				}
				catch (Exception exception_4)
				{
					Class182.smethod_0(exception_4);
					result2 = list;
				}
				return result2;
				IL_2B0:
				result = list;
			}
			return result;
		}

		// Token: 0x060023D7 RID: 9175 RVA: 0x000F148C File Offset: 0x000EF68C
		public static CfmmcAcct smethod_11(string string_1)
		{
			Class463.Class464 @class = new Class463.Class464();
			@class.string_0 = string_1;
			CfmmcAcct result;
			if (!File.Exists(Class463.string_0))
			{
				result = null;
			}
			else
			{
				CfmmcAcct cfmmcAcct = null;
				List<CfmmcAcct> list = Class463.smethod_9();
				if (list != null && list.Any<CfmmcAcct>())
				{
					IEnumerable<CfmmcAcct> source = list.Where(new Func<CfmmcAcct, bool>(@class.method_0));
					if (source.Any<CfmmcAcct>())
					{
						cfmmcAcct = source.First<CfmmcAcct>();
					}
				}
				result = cfmmcAcct;
			}
			return result;
		}

		// Token: 0x060023D8 RID: 9176 RVA: 0x000F14F4 File Offset: 0x000EF6F4
		public static List<string> smethod_12()
		{
			return Class463.smethod_13(Class463.string_0);
		}

		// Token: 0x060023D9 RID: 9177 RVA: 0x000F1510 File Offset: 0x000EF710
		public static List<string> smethod_13(string string_1)
		{
			List<string> list = new List<string>();
			if (File.Exists(string_1))
			{
				try
				{
					foreach (XElement xelement in XDocument.Load(string_1).Root.Element("Accts").Elements("Acct"))
					{
						string value = xelement.Attribute("Id").Value;
						string value2 = xelement.Attribute("Password").Value;
						string item = value + " " + value2;
						list.Add(item);
					}
				}
				catch
				{
				}
			}
			return list;
		}

		// Token: 0x060023DA RID: 9178 RVA: 0x000F15E4 File Offset: 0x000EF7E4
		public static void smethod_14(List<List<string>> list_0, string string_1, string string_2)
		{
			FileInfo fileInfo = new FileInfo(string_1);
			if (fileInfo.Exists)
			{
				XDocument xdocument = XDocument.Load(string_1);
				foreach (XElement xelement in xdocument.Element("Root").Elements("records"))
				{
					if (xelement.Attribute("id").Value == string_2)
					{
						bool flag = false;
						XElement xelement2 = xelement.Elements().Last<XElement>();
						string text = xelement2.Attribute(CfmmcRecFieldsEnum.实际成交日期.ToString()).Value + " " + xelement2.Attribute(CfmmcRecFieldsEnum.成交时间.ToString()).Value;
						int i = 0;
						while (i < list_0.Count)
						{
							if (list_0[i].Count != Convert.ToInt32(CfmmcRecFieldsEnum.实际成交日期) + 1)
							{
								throw new Exception(string.Format("recordList列数错误,位置为{0}", i));
							}
							string text2 = list_0[i][12] + " " + list_0[i][2];
							try
							{
								if (Convert.ToDateTime(text2) <= Convert.ToDateTime(text))
								{
									goto IL_1AF;
								}
							}
							catch (Exception)
							{
								throw new Exception(string.Format("时间转换错误,{0},{1}", text2, text));
							}
							goto IL_155;
							IL_1AF:
							i++;
							continue;
							IL_155:
							flag = true;
							List<string> list = list_0[i];
							XElement xelement3 = new XElement("record");
							for (CfmmcRecFieldsEnum cfmmcRecFieldsEnum = CfmmcRecFieldsEnum.合约; cfmmcRecFieldsEnum <= CfmmcRecFieldsEnum.实际成交日期; cfmmcRecFieldsEnum++)
							{
								xelement3.SetAttributeValue(cfmmcRecFieldsEnum.ToString(), list[(int)cfmmcRecFieldsEnum]);
							}
							xelement.Add(xelement3);
							goto IL_1AF;
						}
						if (flag)
						{
							xdocument.Save(string_1);
						}
						return;
					}
				}
				XElement xelement4 = new XElement("records");
				xelement4.SetAttributeValue("id", string_2);
				for (int j = 0; j < list_0.Count; j++)
				{
					List<string> list2 = list_0[j];
					XElement xelement5 = new XElement("record");
					for (CfmmcRecFieldsEnum cfmmcRecFieldsEnum2 = CfmmcRecFieldsEnum.合约; cfmmcRecFieldsEnum2 <= CfmmcRecFieldsEnum.实际成交日期; cfmmcRecFieldsEnum2++)
					{
						xelement5.SetAttributeValue(cfmmcRecFieldsEnum2.ToString(), list2[(int)cfmmcRecFieldsEnum2]);
					}
					xelement4.Add(xelement5);
				}
				xdocument.Element("Root").Add(xelement4);
				xdocument.Save(string_1);
			}
			else
			{
				XDocument xdocument2 = new XDocument();
				XElement xelement6 = new XElement("Root");
				xdocument2.Add(xelement6);
				XElement xelement7 = new XElement("records");
				xelement7.SetAttributeValue("id", string_2);
				xelement6.Add(xelement7);
				for (int k = 0; k < list_0.Count; k++)
				{
					XElement xelement8 = new XElement("record");
					xelement7.Add(xelement8);
					List<string> list3 = list_0[k];
					for (CfmmcRecFieldsEnum cfmmcRecFieldsEnum3 = CfmmcRecFieldsEnum.合约; cfmmcRecFieldsEnum3 <= CfmmcRecFieldsEnum.实际成交日期; cfmmcRecFieldsEnum3++)
					{
						xelement8.SetAttributeValue(cfmmcRecFieldsEnum3.ToString(), list3[(int)cfmmcRecFieldsEnum3]);
					}
				}
				if (!fileInfo.Directory.Exists)
				{
					fileInfo.Directory.Create();
				}
				xdocument2.Save(string_1);
			}
		}

		// Token: 0x060023DB RID: 9179 RVA: 0x000F19A4 File Offset: 0x000EFBA4
		public static List<List<string>> smethod_15(string string_1)
		{
			return Class463.smethod_16(string_1, null, null);
		}

		// Token: 0x060023DC RID: 9180 RVA: 0x000F19D0 File Offset: 0x000EFBD0
		public static List<List<string>> smethod_16(string string_1, DateTime? nullable_0, DateTime? nullable_1)
		{
			Class463.Class465 @class = new Class463.Class465();
			@class.string_0 = string_1;
			List<List<string>> result;
			try
			{
				string text = Path.Combine(TApp.UserAcctFolder, CfmmcRecImporter.string_0);
				if (File.Exists(text))
				{
					IEnumerable<XElement> source = XDocument.Load(text).Element("Root").Elements("records").Where(new Func<XElement, bool>(@class.method_0));
					if (!source.Any<XElement>())
					{
						result = null;
						goto IL_1BD;
					}
					List<List<string>> list = new List<List<string>>();
					foreach (XElement xelement in source.Elements("record"))
					{
						if (nullable_0 != null)
						{
							bool flag = false;
							XAttribute xattribute = xelement.Attribute(CfmmcRecFieldsEnum.实际成交日期.ToString());
							if (nullable_0 != null)
							{
								if (Convert.ToDateTime(xattribute.Value) > nullable_0)
								{
									flag = true;
								}
							}
							else
							{
								flag = true;
							}
							if (flag && nullable_1 != null && Convert.ToDateTime(xattribute.Value) > nullable_1)
							{
								flag = false;
							}
							if (!flag)
							{
								continue;
							}
						}
						List<string> list2 = new List<string>();
						for (CfmmcRecFieldsEnum cfmmcRecFieldsEnum = CfmmcRecFieldsEnum.合约; cfmmcRecFieldsEnum <= CfmmcRecFieldsEnum.实际成交日期; cfmmcRecFieldsEnum++)
						{
							XAttribute xattribute2 = xelement.Attribute(cfmmcRecFieldsEnum.ToString());
							list2.Add(xattribute2.Value);
						}
						list.Add(list2);
					}
					result = list;
					goto IL_1BD;
				}
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
			return null;
			IL_1BD:
			return result;
		}

		// Token: 0x060023DD RID: 9181 RVA: 0x000F1BD8 File Offset: 0x000EFDD8
		[Obsolete]
		public static List<List<string>> smethod_17(string string_1, string string_2, DateTime? nullable_0, DateTime? nullable_1)
		{
			List<List<string>> list = new List<List<string>>();
			List<List<string>> result;
			try
			{
				if (File.Exists(string_2))
				{
					foreach (XElement xelement in XDocument.Load(string_2).Element("Root").Elements("records"))
					{
						if (xelement.Attribute("id").Value == string_1)
						{
							foreach (XElement xelement2 in xelement.Elements("record"))
							{
								bool flag = false;
								XAttribute xattribute = xelement2.Attribute(CfmmcRecFieldsEnum.实际成交日期.ToString());
								if (nullable_0 != null)
								{
									if (Convert.ToDateTime(xattribute.Value) > nullable_0)
									{
										flag = true;
									}
								}
								else
								{
									flag = true;
								}
								if (flag && nullable_1 != null && Convert.ToDateTime(xattribute.Value) > nullable_1)
								{
									flag = false;
								}
								if (flag)
								{
									List<string> list2 = new List<string>();
									for (CfmmcRecFieldsEnum cfmmcRecFieldsEnum = CfmmcRecFieldsEnum.合约; cfmmcRecFieldsEnum <= CfmmcRecFieldsEnum.实际成交日期; cfmmcRecFieldsEnum++)
									{
										XAttribute xattribute2 = xelement2.Attribute(cfmmcRecFieldsEnum.ToString());
										list2.Add(xattribute2.Value);
									}
									list.Add(list2);
								}
							}
							break;
						}
					}
				}
				result = list;
			}
			catch
			{
				result = list;
			}
			return result;
		}

		// Token: 0x060023DE RID: 9182 RVA: 0x000F1DE4 File Offset: 0x000EFFE4
		public static string smethod_18(string string_1)
		{
			return XDocument.Parse(string_1).Element("TransNote").Element(TransData.string_0).Attribute("Id").Value;
		}

		// Token: 0x060023DF RID: 9183 RVA: 0x000F1E30 File Offset: 0x000F0030
		public static string smethod_19(string string_1)
		{
			return XDocument.Parse(string_1).Element("TransNote").Element(TransData.string_0).Attribute("SN").Value;
		}

		// Token: 0x060023E0 RID: 9184 RVA: 0x000F1E7C File Offset: 0x000F007C
		public static DateTime? smethod_20(string string_1)
		{
			return Class463.smethod_21(Class463.smethod_11(string_1));
		}

		// Token: 0x060023E1 RID: 9185 RVA: 0x000F1E98 File Offset: 0x000F0098
		public static DateTime? smethod_21(CfmmcAcct cfmmcAcct_0)
		{
			DateTime? result;
			if (cfmmcAcct_0 == null)
			{
				result = null;
			}
			else if (cfmmcAcct_0.LastDownloadTime == null)
			{
				result = null;
			}
			else
			{
				result = cfmmcAcct_0.LastDownloadTime;
			}
			return result;
		}

		// Token: 0x060023E2 RID: 9186 RVA: 0x000F1EDC File Offset: 0x000F00DC
		public static DateTime? smethod_22(string string_1)
		{
			return Class463.smethod_23(Class463.smethod_11(string_1));
		}

		// Token: 0x060023E3 RID: 9187 RVA: 0x000F1EF8 File Offset: 0x000F00F8
		public static DateTime? smethod_23(CfmmcAcct cfmmcAcct_0)
		{
			DateTime? result;
			if (cfmmcAcct_0 == null)
			{
				result = null;
			}
			else if (cfmmcAcct_0.EndDate == null)
			{
				result = null;
			}
			else
			{
				result = cfmmcAcct_0.EndDate;
			}
			return result;
		}

		// Token: 0x060023E4 RID: 9188 RVA: 0x000F1F3C File Offset: 0x000F013C
		public static void smethod_24(CfmmcAcct cfmmcAcct_0)
		{
			if (cfmmcAcct_0 != null)
			{
				List<List<string>> list = Class463.smethod_15(cfmmcAcct_0.ID);
				if (list != null && list.Any<List<string>>())
				{
					Class448 @class = new Class448(TransData.string_0, cfmmcAcct_0.ID);
					for (int i = 0; i < list.Count; i++)
					{
						TransData transData = new TransData();
						transData.method_3(list[i], cfmmcAcct_0.ID);
						@class.Data.Add(transData);
					}
					for (int j = 0; j < cfmmcAcct_0.BindingAccts.Count; j++)
					{
						Class463.smethod_26(@class, cfmmcAcct_0.BindingAccts[j].Id, cfmmcAcct_0.ID);
					}
				}
				else if (cfmmcAcct_0.EndDate != null || cfmmcAcct_0.LastDownloadTime != null)
				{
					cfmmcAcct_0.EndDate = null;
					cfmmcAcct_0.LastDownloadTime = null;
					Class463.smethod_0(cfmmcAcct_0);
				}
			}
		}

		// Token: 0x060023E5 RID: 9189 RVA: 0x000F203C File Offset: 0x000F023C
		public static void smethod_25(CfmmcAcct cfmmcAcct_0, int int_0)
		{
			if (cfmmcAcct_0 != null)
			{
				List<List<string>> list = Class463.smethod_15(cfmmcAcct_0.ID);
				Class448 @class = new Class448(TransData.string_0, cfmmcAcct_0.ID);
				for (int i = 0; i < list.Count; i++)
				{
					TransData transData = new TransData();
					transData.method_3(list[i], cfmmcAcct_0.ID);
					@class.Data.Add(transData);
				}
				Class463.smethod_26(@class, int_0, cfmmcAcct_0.ID);
			}
		}

		// Token: 0x060023E6 RID: 9190 RVA: 0x000F20B0 File Offset: 0x000F02B0
		public static void smethod_26(Class448 class448_0, int int_0, string string_1)
		{
			List<Transaction> list = Base.Trading.smethod_124(int_0);
			if (TransFileImporter.smethod_3(list, class448_0, int_0))
			{
				Base.Trading.smethod_128(int_0, list);
				Base.Trading.smethod_17();
			}
		}

		// Token: 0x04001145 RID: 4421
		private static readonly string string_0 = TApp.UserAcctFolder + "\\cfmmcacct.dat";

		// Token: 0x0200035A RID: 858
		[CompilerGenerated]
		private sealed class Class464
		{
			// Token: 0x060023E9 RID: 9193 RVA: 0x000F20DC File Offset: 0x000F02DC
			internal bool method_0(CfmmcAcct cfmmcAcct_0)
			{
				return cfmmcAcct_0.ID.Equals(this.string_0);
			}

			// Token: 0x04001146 RID: 4422
			public string string_0;
		}

		// Token: 0x0200035B RID: 859
		[CompilerGenerated]
		private sealed class Class465
		{
			// Token: 0x060023EB RID: 9195 RVA: 0x000F2100 File Offset: 0x000F0300
			internal bool method_0(XElement xelement_0)
			{
				return xelement_0.Attribute("id").Value == this.string_0;
			}

			// Token: 0x04001147 RID: 4423
			public string string_0;
		}
	}
}
