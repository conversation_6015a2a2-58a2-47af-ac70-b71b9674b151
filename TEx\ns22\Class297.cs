﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ns22
{
	// Token: 0x0200021C RID: 540
	[DesignerCategory("Code")]
	internal sealed class Class297 : ProgressBar
	{
		// Token: 0x06001649 RID: 5705 RVA: 0x00008F95 File Offset: 0x00007195
		public Class297(Color color_1 = default(Color))
		{
			this.BarColor = color_1;
			base.SetStyle(ControlStyles.UserPaint, true);
		}

		// Token: 0x0600164A RID: 5706 RVA: 0x00095BE8 File Offset: 0x00093DE8
		protected void OnPaint(PaintEventArgs e)
		{
			base.OnPaint(e);
			Rectangle clipRectangle = e.ClipRectangle;
			int num = 0;
			clipRectangle.Width = (int)((double)clipRectangle.Width * ((double)base.Value / (double)base.Maximum)) - 0;
			if (ProgressBarRenderer.IsSupported)
			{
				ProgressBarRenderer.DrawHorizontalBar(e.Graphics, e.ClipRectangle);
			}
			clipRectangle.Height -= num * 2;
			SolidBrush brush = new SolidBrush(this.BarColor);
			e.Graphics.FillRectangle(brush, num, num, clipRectangle.Width, clipRectangle.Height);
		}

		// Token: 0x17000395 RID: 917
		// (get) Token: 0x0600164B RID: 5707 RVA: 0x00095C7C File Offset: 0x00093E7C
		// (set) Token: 0x0600164C RID: 5708 RVA: 0x00008FAE File Offset: 0x000071AE
		public Color BarColor { get; set; }

		// Token: 0x04000B44 RID: 2884
		[CompilerGenerated]
		private Color color_0;
	}
}
