﻿namespace ns16
{
	// Token: 0x02000418 RID: 1048
	internal sealed partial class ExceptionReportingForm : global::System.Windows.Forms.Form
	{
		// Token: 0x06002851 RID: 10321 RVA: 0x001043CC File Offset: 0x001025CC
		private void InitializeComponent()
		{
			this.panelInformation = new global::System.Windows.Forms.Panel();
			this.continueCheckBox = new global::System.Windows.Forms.CheckBox();
			this.dontSendReport = new global::System.Windows.Forms.Button();
			this.sendReport = new global::System.Windows.Forms.Button();
			this.pleaseTellMessage = new global::System.Windows.Forms.Label();
			this.headerControl1 = new global::ns23.Control4();
			this.errorMessage = new global::ns30.Class545();
			this.panelSending = new global::System.Windows.Forms.Panel();
			this.cancelSending = new global::System.Windows.Forms.Button();
			this.ok = new global::System.Windows.Forms.Button();
			this.retrySending = new global::System.Windows.Forms.Button();
			this.waitSendingReport = new global::ns31.Control6();
			this.headerControl2 = new global::ns23.Control4();
			this.preparingFeedback = new global::ns10.Control3();
			this.connectingFeedback = new global::ns10.Control3();
			this.transferingFeedback = new global::ns10.Control3();
			this.completedFeedback = new global::ns10.Control3();
			this.panelEmail = new global::System.Windows.Forms.Panel();
			this.labelEmail = new global::System.Windows.Forms.Label();
			this.sendAnonymously = new global::System.Windows.Forms.CheckBox();
			this.email = new global::System.Windows.Forms.TextBox();
			this.headerControl3 = new global::ns23.Control4();
			this.label3 = new global::System.Windows.Forms.Label();
			this.continueSendReport = new global::System.Windows.Forms.Button();
			this.panelInformation.SuspendLayout();
			this.panelSending.SuspendLayout();
			this.panelEmail.SuspendLayout();
			base.SuspendLayout();
			this.panelInformation.Controls.Add(this.continueCheckBox);
			this.panelInformation.Controls.Add(this.dontSendReport);
			this.panelInformation.Controls.Add(this.sendReport);
			this.panelInformation.Controls.Add(this.pleaseTellMessage);
			this.panelInformation.Controls.Add(this.headerControl1);
			this.panelInformation.Controls.Add(this.errorMessage);
			this.panelInformation.Location = new global::System.Drawing.Point(11, 9);
			this.panelInformation.Name = "panelInformation";
			this.panelInformation.Size = new global::System.Drawing.Size(578, 267);
			this.panelInformation.TabIndex = 0;
			this.continueCheckBox.Anchor = (global::System.Windows.Forms.AnchorStyles.Bottom | global::System.Windows.Forms.AnchorStyles.Left);
			this.continueCheckBox.FlatStyle = global::System.Windows.Forms.FlatStyle.System;
			this.continueCheckBox.Location = new global::System.Drawing.Point(31, 134);
			this.continueCheckBox.Name = "continueCheckBox";
			this.continueCheckBox.Size = new global::System.Drawing.Size(316, 30);
			this.continueCheckBox.TabIndex = 14;
			this.continueCheckBox.Text = "忽略错误并尝试继续";
			this.continueCheckBox.UseMnemonic = false;
			this.continueCheckBox.CheckedChanged += new global::System.EventHandler(this.continueCheckBox_CheckedChanged);
			this.dontSendReport.Anchor = (global::System.Windows.Forms.AnchorStyles.Bottom | global::System.Windows.Forms.AnchorStyles.Right);
			this.dontSendReport.FlatStyle = global::System.Windows.Forms.FlatStyle.System;
			this.dontSendReport.Location = new global::System.Drawing.Point(455, 227);
			this.dontSendReport.Name = "dontSendReport";
			this.dontSendReport.Size = new global::System.Drawing.Size(105, 27);
			this.dontSendReport.TabIndex = 6;
			this.dontSendReport.Text = "不发送";
			this.dontSendReport.Click += new global::System.EventHandler(this.dontSendReport_Click);
			this.sendReport.Anchor = (global::System.Windows.Forms.AnchorStyles.Bottom | global::System.Windows.Forms.AnchorStyles.Right);
			this.sendReport.FlatStyle = global::System.Windows.Forms.FlatStyle.System;
			this.sendReport.Location = new global::System.Drawing.Point(300, 227);
			this.sendReport.Name = "sendReport";
			this.sendReport.Size = new global::System.Drawing.Size(147, 27);
			this.sendReport.TabIndex = 9;
			this.sendReport.Text = "发送错误报告";
			this.sendReport.Click += new global::System.EventHandler(this.sendReport_Click);
			this.pleaseTellMessage.Anchor = (global::System.Windows.Forms.AnchorStyles.Bottom | global::System.Windows.Forms.AnchorStyles.Left | global::System.Windows.Forms.AnchorStyles.Right);
			this.pleaseTellMessage.FlatStyle = global::System.Windows.Forms.FlatStyle.System;
			this.pleaseTellMessage.Location = new global::System.Drawing.Point(28, 169);
			this.pleaseTellMessage.Name = "pleaseTellMessage";
			this.pleaseTellMessage.Size = new global::System.Drawing.Size(533, 51);
			this.pleaseTellMessage.TabIndex = 12;
			this.pleaseTellMessage.Text = "为了继续改进软件，我们希望知道此次发生的错误以便及时更正。一份错误报告已经准备好，您可以选择是否发送给我们。";
			this.headerControl1.BackColor = global::System.Drawing.Color.FromArgb(36, 96, 179);
			this.headerControl1.Dock = global::System.Windows.Forms.DockStyle.Top;
			this.headerControl1.ForeColor = global::System.Drawing.Color.White;
			this.headerControl1.IconState = global::ns24.Enum36.const_1;
			this.headerControl1.Image = global::ns7.Class548.TExLogo;
			this.headerControl1.IconState = global::ns24.Enum36.const_1;
			this.headerControl1.Location = new global::System.Drawing.Point(0, 0);
			this.headerControl1.Name = "headerControl1";
			this.headerControl1.Size = new global::System.Drawing.Size(578, 67);
			this.headerControl1.TabIndex = 3;
			this.headerControl1.TabStop = false;
			this.headerControl1.Text = "抱歉，%AppName%运行中发生错误";
			this.errorMessage.Anchor = (global::System.Windows.Forms.AnchorStyles.Top | global::System.Windows.Forms.AnchorStyles.Left | global::System.Windows.Forms.AnchorStyles.Right);
			this.errorMessage.FlatStyle = global::System.Windows.Forms.FlatStyle.System;
			this.errorMessage.Location = new global::System.Drawing.Point(28, 80);
			this.errorMessage.Name = "errorMessage";
			this.errorMessage.Size = new global::System.Drawing.Size(533, 13);
			this.errorMessage.TabIndex = 10;
			this.errorMessage.Text = "errorMessage";
			this.errorMessage.UseMnemonic = false;
			this.panelSending.Controls.Add(this.cancelSending);
			this.panelSending.Controls.Add(this.ok);
			this.panelSending.Controls.Add(this.retrySending);
			this.panelSending.Controls.Add(this.waitSendingReport);
			this.panelSending.Controls.Add(this.headerControl2);
			this.panelSending.Controls.Add(this.preparingFeedback);
			this.panelSending.Controls.Add(this.connectingFeedback);
			this.panelSending.Controls.Add(this.transferingFeedback);
			this.panelSending.Controls.Add(this.completedFeedback);
			this.panelSending.Location = new global::System.Drawing.Point(11, 305);
			this.panelSending.Name = "panelSending";
			this.panelSending.Size = new global::System.Drawing.Size(578, 267);
			this.panelSending.TabIndex = 2;
			this.panelSending.Visible = false;
			this.cancelSending.Anchor = (global::System.Windows.Forms.AnchorStyles.Bottom | global::System.Windows.Forms.AnchorStyles.Right);
			this.cancelSending.FlatStyle = global::System.Windows.Forms.FlatStyle.System;
			this.cancelSending.Location = new global::System.Drawing.Point(448, 227);
			this.cancelSending.Name = "cancelSending";
			this.cancelSending.Size = new global::System.Drawing.Size(112, 28);
			this.cancelSending.TabIndex = 10;
			this.cancelSending.Text = "取消";
			this.cancelSending.Click += new global::System.EventHandler(this.cancelSending_Click);
			this.ok.Anchor = (global::System.Windows.Forms.AnchorStyles.Bottom | global::System.Windows.Forms.AnchorStyles.Right);
			this.ok.Enabled = false;
			this.ok.FlatStyle = global::System.Windows.Forms.FlatStyle.System;
			this.ok.Location = new global::System.Drawing.Point(325, 227);
			this.ok.Name = "ok";
			this.ok.Size = new global::System.Drawing.Size(112, 28);
			this.ok.TabIndex = 22;
			this.ok.Text = "确定";
			this.ok.Click += new global::System.EventHandler(this.ok_Click);
			this.retrySending.Anchor = (global::System.Windows.Forms.AnchorStyles.Bottom | global::System.Windows.Forms.AnchorStyles.Right);
			this.retrySending.FlatStyle = global::System.Windows.Forms.FlatStyle.System;
			this.retrySending.Location = new global::System.Drawing.Point(202, 227);
			this.retrySending.Name = "retrySending";
			this.retrySending.Size = new global::System.Drawing.Size(112, 28);
			this.retrySending.TabIndex = 23;
			this.retrySending.Text = "重试";
			this.retrySending.Visible = false;
			this.retrySending.Click += new global::System.EventHandler(this.retrySending_Click);
			this.waitSendingReport.Location = new global::System.Drawing.Point(147, 138);
			this.waitSendingReport.Name = "waitSendingReport";
			this.waitSendingReport.Size = new global::System.Drawing.Size(250, 42);
			this.waitSendingReport.TabIndex = 11;
			this.waitSendingReport.TabStop = false;
			this.waitSendingReport.Visible = false;
			this.headerControl2.BackColor = global::System.Drawing.Color.FromArgb(36, 96, 179);
			this.headerControl2.Dock = global::System.Windows.Forms.DockStyle.Top;
			this.headerControl2.ForeColor = global::System.Drawing.Color.White;
			this.headerControl2.IconState = global::ns24.Enum36.const_1;
			this.headerControl2.Image = global::ns7.Class548.TExLogo;
			this.headerControl2.IconState = global::ns24.Enum36.const_1;
			this.headerControl2.Location = new global::System.Drawing.Point(0, 0);
			this.headerControl2.Name = "headerControl2";
			this.headerControl2.Size = new global::System.Drawing.Size(578, 67);
			this.headerControl2.TabIndex = 24;
			this.headerControl2.TabStop = false;
			this.headerControl2.Text = "请等待，%AppName%正在发送错误报告";
			this.preparingFeedback.Location = new global::System.Drawing.Point(34, 83);
			this.preparingFeedback.Name = "preparingFeedback";
			this.preparingFeedback.Size = new global::System.Drawing.Size(515, 19);
			this.preparingFeedback.TabIndex = 18;
			this.preparingFeedback.TabStop = false;
			this.preparingFeedback.Text = "准备错误报告";
			this.connectingFeedback.Location = new global::System.Drawing.Point(34, 111);
			this.connectingFeedback.Name = "connectingFeedback";
			this.connectingFeedback.Size = new global::System.Drawing.Size(515, 18);
			this.connectingFeedback.TabIndex = 19;
			this.connectingFeedback.TabStop = false;
			this.connectingFeedback.Text = "与服务器建立连接";
			this.transferingFeedback.Location = new global::System.Drawing.Point(34, 138);
			this.transferingFeedback.Name = "transferingFeedback";
			this.transferingFeedback.Size = new global::System.Drawing.Size(515, 19);
			this.transferingFeedback.TabIndex = 20;
			this.transferingFeedback.TabStop = false;
			this.transferingFeedback.Text = "传输报告";
			this.completedFeedback.Location = new global::System.Drawing.Point(34, 166);
			this.completedFeedback.Name = "completedFeedback";
			this.completedFeedback.Size = new global::System.Drawing.Size(515, 19);
			this.completedFeedback.TabIndex = 21;
			this.completedFeedback.TabStop = false;
			this.completedFeedback.Text = "完成";
			this.panelEmail.Controls.Add(this.labelEmail);
			this.panelEmail.Controls.Add(this.sendAnonymously);
			this.panelEmail.Controls.Add(this.email);
			this.panelEmail.Controls.Add(this.headerControl3);
			this.panelEmail.Controls.Add(this.label3);
			this.panelEmail.Controls.Add(this.continueSendReport);
			this.panelEmail.Location = new global::System.Drawing.Point(15, 591);
			this.panelEmail.Name = "panelEmail";
			this.panelEmail.Size = new global::System.Drawing.Size(579, 267);
			this.panelEmail.TabIndex = 4;
			this.panelEmail.Visible = false;
			this.labelEmail.FlatStyle = global::System.Windows.Forms.FlatStyle.System;
			this.labelEmail.Location = new global::System.Drawing.Point(28, 130);
			this.labelEmail.Name = "labelEmail";
			this.labelEmail.Size = new global::System.Drawing.Size(140, 19);
			this.labelEmail.TabIndex = 9;
			this.labelEmail.Text = "&Email地址：";
			this.sendAnonymously.FlatStyle = global::System.Windows.Forms.FlatStyle.System;
			this.sendAnonymously.Location = new global::System.Drawing.Point(168, 164);
			this.sendAnonymously.Name = "sendAnonymously";
			this.sendAnonymously.Size = new global::System.Drawing.Size(325, 18);
			this.sendAnonymously.TabIndex = 11;
			this.sendAnonymously.Text = "我希望匿名发送";
			this.sendAnonymously.CheckedChanged += new global::System.EventHandler(this.sendAnonymously_CheckedChanged);
			this.email.Location = new global::System.Drawing.Point(168, 127);
			this.email.Name = "email";
			this.email.Size = new global::System.Drawing.Size(204, 22);
			this.email.TabIndex = 10;
			this.email.TextChanged += new global::System.EventHandler(this.email_TextChanged);
			this.headerControl3.BackColor = global::System.Drawing.Color.FromArgb(36, 96, 179);
			this.headerControl3.Dock = global::System.Windows.Forms.DockStyle.Top;
			this.headerControl3.ForeColor = global::System.Drawing.Color.White;
			this.headerControl3.IconState = global::ns24.Enum36.const_1;
			this.headerControl3.Image = global::ns7.Class548.TExLogo;
			this.headerControl3.IconState = global::ns24.Enum36.const_1;
			this.headerControl3.Location = new global::System.Drawing.Point(0, 0);
			this.headerControl3.Name = "headerControl3";
			this.headerControl3.Size = new global::System.Drawing.Size(579, 67);
			this.headerControl3.TabIndex = 3;
			this.headerControl3.TabStop = false;
			this.headerControl3.Text = "您愿意留下Email地址以便我们就此问题与您联系吗？";
			this.label3.Anchor = (global::System.Windows.Forms.AnchorStyles.Top | global::System.Windows.Forms.AnchorStyles.Left | global::System.Windows.Forms.AnchorStyles.Right);
			this.label3.FlatStyle = global::System.Windows.Forms.FlatStyle.System;
			this.label3.Location = new global::System.Drawing.Point(28, 80);
			this.label3.Name = "label3";
			this.label3.Size = new global::System.Drawing.Size(533, 49);
			this.label3.TabIndex = 10;
			this.label3.Text = "如果您愿意留下Email地址以便我们跟您联系，请输入到以下文本框。此地址将不会被公开。";
			this.continueSendReport.Anchor = (global::System.Windows.Forms.AnchorStyles.Bottom | global::System.Windows.Forms.AnchorStyles.Right);
			this.continueSendReport.Enabled = false;
			this.continueSendReport.FlatStyle = global::System.Windows.Forms.FlatStyle.System;
			this.continueSendReport.Location = new global::System.Drawing.Point(413, 227);
			this.continueSendReport.Name = "continueSendReport";
			this.continueSendReport.Size = new global::System.Drawing.Size(147, 28);
			this.continueSendReport.TabIndex = 12;
			this.continueSendReport.Text = "发送错误报告";
			this.continueSendReport.Click += new global::System.EventHandler(this.continueSendReport_Click);
			this.AutoScaleBaseSize = new global::System.Drawing.Size(7, 15);
			this.BackColor = global::System.Drawing.SystemColors.Window;
			base.ClientSize = new global::System.Drawing.Size(602, 867);
			base.ControlBox = false;
			base.Controls.Add(this.panelEmail);
			base.Controls.Add(this.panelInformation);
			base.Controls.Add(this.panelSending);
			base.FormBorderStyle = global::System.Windows.Forms.FormBorderStyle.FixedSingle;
			base.Name = "ExceptionReportingForm";
			base.ShowInTaskbar = false;
			base.StartPosition = global::System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "%AppName%";
			base.TopMost = true;
			this.panelInformation.ResumeLayout(false);
			this.panelSending.ResumeLayout(false);
			this.panelEmail.ResumeLayout(false);
			this.panelEmail.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x04001423 RID: 5155
		private global::System.Windows.Forms.CheckBox continueCheckBox;

		// Token: 0x04001424 RID: 5156
		private global::System.Windows.Forms.Button dontSendReport;

		// Token: 0x04001425 RID: 5157
		private global::System.Windows.Forms.Button sendReport;

		// Token: 0x04001426 RID: 5158
		private global::System.Windows.Forms.Label pleaseTellMessage;

		// Token: 0x04001427 RID: 5159
		private global::System.Windows.Forms.Panel panelInformation;

		// Token: 0x04001428 RID: 5160
		private global::System.Windows.Forms.Panel panelSending;

		// Token: 0x04001429 RID: 5161
		private global::System.Windows.Forms.Button cancelSending;

		// Token: 0x0400142A RID: 5162
		private global::ns31.Control6 waitSendingReport;

		// Token: 0x0400142B RID: 5163
		private global::ns10.Control3 preparingFeedback;

		// Token: 0x0400142C RID: 5164
		private global::ns10.Control3 connectingFeedback;

		// Token: 0x0400142D RID: 5165
		private global::ns10.Control3 transferingFeedback;

		// Token: 0x0400142E RID: 5166
		private global::ns10.Control3 completedFeedback;

		// Token: 0x0400142F RID: 5167
		private global::System.Windows.Forms.Button ok;

		// Token: 0x04001430 RID: 5168
		private global::System.Windows.Forms.Button retrySending;

		// Token: 0x04001431 RID: 5169
		private global::ns23.Control4 headerControl1;

		// Token: 0x04001432 RID: 5170
		private global::ns23.Control4 headerControl2;

		// Token: 0x04001434 RID: 5172
		private global::System.Windows.Forms.Panel panelEmail;

		// Token: 0x04001435 RID: 5173
		private global::System.Windows.Forms.Label label3;

		// Token: 0x04001436 RID: 5174
		private global::ns23.Control4 headerControl3;

		// Token: 0x04001437 RID: 5175
		private global::System.Windows.Forms.Button continueSendReport;

		// Token: 0x04001438 RID: 5176
		private global::System.Windows.Forms.TextBox email;

		// Token: 0x04001439 RID: 5177
		private global::System.Windows.Forms.Label labelEmail;

		// Token: 0x0400143A RID: 5178
		private global::System.Windows.Forms.CheckBox sendAnonymously;

		// Token: 0x0400143B RID: 5179
		private global::ns30.Class545 errorMessage;
	}
}
