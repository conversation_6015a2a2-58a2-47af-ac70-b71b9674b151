﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using DevComponents.DotNetBar;

namespace ns20
{
	// Token: 0x0200005B RID: 91
	internal sealed class Class45 : Component, ISuperTooltipInfoProvider
	{
		// Token: 0x14000017 RID: 23
		// (add) Token: 0x06000323 RID: 803 RVA: 0x0001E3E4 File Offset: 0x0001C5E4
		// (remove) Token: 0x06000324 RID: 804 RVA: 0x0001E41C File Offset: 0x0001C61C
		public event EventHandler DisplayTooltip
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x14000018 RID: 24
		// (add) Token: 0x06000325 RID: 805 RVA: 0x0001E454 File Offset: 0x0001C654
		// (remove) Token: 0x06000326 RID: 806 RVA: 0x0001E48C File Offset: 0x0001C68C
		public event EventHandler HideTooltip
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x170000C2 RID: 194
		// (get) Token: 0x06000327 RID: 807 RVA: 0x0001E4C4 File Offset: 0x0001C6C4
		public Rectangle ComponentRectangle
		{
			get
			{
				Rectangle result = default(Rectangle);
				if (this.icomponent_0 is Control)
				{
					Control control = this.icomponent_0 as Control;
					if (control != null && !control.IsDisposed)
					{
						result = control.Bounds;
						result.Location = control.PointToScreen(result.Location);
					}
				}
				else if (this.icomponent_0 is BaseItem)
				{
					result = (this.icomponent_0 as BaseItem).ScreenRectangle;
				}
				return result;
			}
		}

		// Token: 0x06000328 RID: 808 RVA: 0x000035B9 File Offset: 0x000017B9
		public Class45(IComponent icomponent_1)
		{
			this.icomponent_0 = icomponent_1;
		}

		// Token: 0x06000329 RID: 809 RVA: 0x000035CA File Offset: 0x000017CA
		public void method_0()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x0600032A RID: 810 RVA: 0x000035E5 File Offset: 0x000017E5
		public void method_1()
		{
			EventHandler eventHandler = this.eventHandler_1;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x0400011F RID: 287
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000120 RID: 288
		[CompilerGenerated]
		private EventHandler eventHandler_1;

		// Token: 0x04000121 RID: 289
		private IComponent icomponent_0;
	}
}
