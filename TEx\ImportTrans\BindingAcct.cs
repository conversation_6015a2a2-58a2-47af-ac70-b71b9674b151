﻿using System;

namespace TEx.ImportTrans
{
	// Token: 0x02000356 RID: 854
	public sealed class BindingAcct
	{
		// Token: 0x060023AE RID: 9134 RVA: 0x00002D25 File Offset: 0x00000F25
		public BindingAcct()
		{
		}

		// Token: 0x060023AF RID: 9135 RVA: 0x0000DEF8 File Offset: 0x0000C0F8
		public BindingAcct(string usrName, int id) : this()
		{
			this.usrName = usrName;
			this.id = id;
		}

		// Token: 0x060023B0 RID: 9136 RVA: 0x0000DF10 File Offset: 0x0000C110
		public BindingAcct(string usrName, int id, DateTime? beginDate, DateTime? endDate) : this(usrName, id)
		{
			this.beginDate = beginDate;
			this.endDate = endDate;
		}

		// Token: 0x17000620 RID: 1568
		// (get) Token: 0x060023B1 RID: 9137 RVA: 0x000F0AC0 File Offset: 0x000EECC0
		// (set) Token: 0x060023B2 RID: 9138 RVA: 0x0000DF2B File Offset: 0x0000C12B
		public string UsrName
		{
			get
			{
				return this.usrName;
			}
			set
			{
				this.usrName = value;
			}
		}

		// Token: 0x17000621 RID: 1569
		// (get) Token: 0x060023B3 RID: 9139 RVA: 0x000F0AD8 File Offset: 0x000EECD8
		// (set) Token: 0x060023B4 RID: 9140 RVA: 0x0000DF36 File Offset: 0x0000C136
		public int Id
		{
			get
			{
				return this.id;
			}
			set
			{
				this.id = value;
			}
		}

		// Token: 0x17000622 RID: 1570
		// (get) Token: 0x060023B5 RID: 9141 RVA: 0x000F0AF0 File Offset: 0x000EECF0
		// (set) Token: 0x060023B6 RID: 9142 RVA: 0x0000DF41 File Offset: 0x0000C141
		public DateTime? BeginDate
		{
			get
			{
				return this.beginDate;
			}
			set
			{
				this.beginDate = value;
			}
		}

		// Token: 0x17000623 RID: 1571
		// (get) Token: 0x060023B7 RID: 9143 RVA: 0x000F0B08 File Offset: 0x000EED08
		// (set) Token: 0x060023B8 RID: 9144 RVA: 0x0000DF4C File Offset: 0x0000C14C
		public DateTime? EndDate
		{
			get
			{
				return this.endDate;
			}
			set
			{
				this.endDate = value;
			}
		}

		// Token: 0x0400113A RID: 4410
		private string usrName;

		// Token: 0x0400113B RID: 4411
		private int id;

		// Token: 0x0400113C RID: 4412
		private DateTime? beginDate;

		// Token: 0x0400113D RID: 4413
		private DateTime? endDate;
	}
}
