﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using Newtonsoft.Json;
using ns10;
using ns15;
using ns2;
using ns28;
using ns3;
using ns4;
using TEx.Comn;

namespace TEx
{
	// Token: 0x0200001C RID: 28
	internal sealed class InfoMineApiWorker : Class556
	{
		// Token: 0x060000A6 RID: 166 RVA: 0x00002D06 File Offset: 0x00000F06
		public InfoMineApiWorker()
		{
			base.ResultCompressed = true;
		}

		// Token: 0x060000A7 RID: 167 RVA: 0x000125CC File Offset: 0x000107CC
		public void method_3(StkSymbol stkSymbol_0)
		{
			if (stkSymbol_0 != null)
			{
				List<StkSymbol> list_ = new List<StkSymbol>
				{
					stkSymbol_0
				};
				this.method_4(list_);
			}
		}

		// Token: 0x060000A8 RID: 168 RVA: 0x000125F4 File Offset: 0x000107F4
		public void method_4(List<StkSymbol> list_0)
		{
			if (list_0 != null && list_0.Count > 0)
			{
				Dictionary<string, object> dictionary = new Dictionary<string, object>();
				string[] array = list_0.Select(new Func<StkSymbol, string>(InfoMineApiWorker.<>c.<>9.method_0)).Where(new Func<string, bool>(InfoMineApiWorker.<>c.<>9.method_1)).ToArray<string>();
				if (array.Length != 0)
				{
					Class1<string, string, int, Class2<string[]>> value = new Class1<string, string, int, Class2<string[]>>("GetInfoMines", TApp.LoginCode, base.ResultCompressed ? 1 : 0, new Class2<string[]>(array));
					dictionary["postData"] = value;
					base.GetHttpResponseInBackground(dictionary);
				}
			}
		}

		// Token: 0x060000A9 RID: 169 RVA: 0x000126A4 File Offset: 0x000108A4
		protected void ProcessRsltData(ApiResult rslt, Dictionary<string, object> reqDict)
		{
			if (rslt != null && rslt.data != null)
			{
				try
				{
					Class19 @class = JsonConvert.DeserializeObject<Class19>(rslt.data as string);
					List<Class18> list = new List<Class18>();
					if (@class != null)
					{
						string[] tscodes = @class.tscodes;
						for (int i = 0; i < tscodes.Length; i++)
						{
							InfoMineApiWorker.Class10 class2 = new InfoMineApiWorker.Class10();
							class2.string_0 = tscodes[i];
							Class18 class3 = new Class18();
							class3.tscode = class2.string_0;
							if (@class.forecasts != null)
							{
								class3.forecasts = @class.forecasts.Where(new Func<Forecast, bool>(class2.method_0)).ToList<Forecast>();
							}
							if (@class.expresses != null)
							{
								class3.expresses = @class.expresses.Where(new Func<Express, bool>(class2.method_1)).ToList<Express>();
							}
							class3.FetchDate = DateTime.Now;
							class3.method_2();
							list.Add(class3);
						}
					}
					reqDict["infoMines"] = list;
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
		}

		// Token: 0x0200001E RID: 30
		[CompilerGenerated]
		private sealed class Class10
		{
			// Token: 0x060000AF RID: 175 RVA: 0x000127F0 File Offset: 0x000109F0
			internal bool method_0(Forecast forecast_0)
			{
				return forecast_0.ts_code == this.string_0;
			}

			// Token: 0x060000B0 RID: 176 RVA: 0x00012814 File Offset: 0x00010A14
			internal bool method_1(Express express_0)
			{
				return express_0.ts_code == this.string_0;
			}

			// Token: 0x04000041 RID: 65
			public string string_0;
		}
	}
}
