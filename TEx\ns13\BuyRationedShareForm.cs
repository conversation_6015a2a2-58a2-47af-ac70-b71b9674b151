﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns28;
using ns29;
using TEx;

namespace ns13
{
	// Token: 0x02000155 RID: 341
	internal sealed partial class BuyRationedShareForm : Form
	{
		// Token: 0x06000CF2 RID: 3314 RVA: 0x0004B51C File Offset: 0x0004971C
		public BuyRationedShareForm(EventArgs22 eventArgs22_1)
		{
			this.InitializeComponent();
			StkSymbol stkSymbol = SymbMgr.smethod_3(eventArgs22_1.TranStock.SymbolID);
			this.eventArgs22_0 = eventArgs22_1;
			StSplit stSplit = eventArgs22_1.StSplit;
			long num = Convert.ToInt64(Math.Floor(eventArgs22_1.TranStock.Units / 10m * stSplit.RationedShares.Value * 10m));
			this.label_StockName.Text = stkSymbol.CNName;
			this.label_StockCode.Text = stkSymbol.Code;
			this.label_Date.Text = stSplit.Date.ToShortDateString();
			this.label_Price.Text = stSplit.RationedSharePrice.Value.ToString();
			if (Base.Acct.CurrAccount.EndingBal != null)
			{
				long num2 = Convert.ToInt64(Math.Floor(Base.Acct.CurrAccount.EndingBal.Value / stSplit.RationedSharePrice.Value));
				if (num > num2)
				{
					num = num2;
				}
			}
			this.numericUpDown_Units.Minimum = 1m;
			this.numericUpDown_Units.Maximum = num;
			this.numericUpDown_Units.Value = num;
			this.labelX_info.ForeColor = Color.Black;
			this.labelX_info.Text = "此次配股最大可买入数量为" + num + "股。";
			base.FormClosing += this.BuyRationedShareForm_FormClosing;
			this.btnOK.Click += this.btnOK_Click;
			this.btnCancel.Click += this.btnCancel_Click;
		}

		// Token: 0x06000CF3 RID: 3315 RVA: 0x0004B6EC File Offset: 0x000498EC
		private void BuyRationedShareForm_FormClosing(object sender, FormClosingEventArgs e)
		{
			if (this.eventArgs22_0.RationedShareBuyUnits == null)
			{
				this.eventArgs22_0.Cancel = true;
			}
		}

		// Token: 0x06000CF4 RID: 3316 RVA: 0x00005C57 File Offset: 0x00003E57
		private void btnOK_Click(object sender, EventArgs e)
		{
			this.eventArgs22_0.RationedShareBuyUnits = new long?(Convert.ToInt64(this.numericUpDown_Units.Value));
			base.Close();
		}

		// Token: 0x06000CF5 RID: 3317 RVA: 0x00005C81 File Offset: 0x00003E81
		private void btnCancel_Click(object sender, EventArgs e)
		{
			this.eventArgs22_0.Cancel = true;
			base.Close();
		}

		// Token: 0x06000CF6 RID: 3318 RVA: 0x00005C97 File Offset: 0x00003E97
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000645 RID: 1605
		private EventArgs22 eventArgs22_0;

		// Token: 0x04000646 RID: 1606
		private IContainer icontainer_0;
	}
}
