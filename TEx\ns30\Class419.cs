﻿using System;
using System.Collections.Generic;
using ns13;
using ns14;
using ns28;
using ns9;
using TEx.Inds;
using TEx.SIndicator;

namespace ns30
{
	// Token: 0x0200032E RID: 814
	internal sealed class Class419 : Class412
	{
		// Token: 0x06002272 RID: 8818 RVA: 0x0000D8A3 File Offset: 0x0000BAA3
		public Class419(HToken htoken_1, Class408 class408_2, Class408 class408_3) : base(htoken_1, class408_2, class408_3)
		{
		}

		// Token: 0x06002273 RID: 8819 RVA: 0x000E8564 File Offset: 0x000E6764
		public override string vmethod_0()
		{
			return base.vmethod_0();
		}

		// Token: 0x06002274 RID: 8820 RVA: 0x000EAB2C File Offset: 0x000E8D2C
		public static Class408 smethod_0(Tokenes tokenes_0, ParserEnvironment parserEnvironment_0)
		{
			Class408 class408_ = TreeSentence.smethod_0(tokenes_0, parserEnvironment_0);
			tokenes_0.method_1();
			if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_30 && !(tokenes_0.Current.Symbol.Name != ";"))
			{
				HToken htoken_ = tokenes_0.Current;
				tokenes_0.method_1();
				Class408 result;
				if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
				{
					result = new Class419(htoken_, class408_, new Class409(new HToken(0, 0, new Class439(Enum26.const_30, "结束"))));
				}
				else
				{
					Class408 class408_2 = Class419.smethod_1(tokenes_0, parserEnvironment_0);
					result = new Class419(htoken_, class408_, class408_2);
				}
				return result;
			}
			throw new Exception(tokenes_0.Current.method_0("不是分号"));
		}

		// Token: 0x06002275 RID: 8821 RVA: 0x000EABE8 File Offset: 0x000E8DE8
		private static Class408 smethod_1(Tokenes tokenes_0, ParserEnvironment parserEnvironment_0)
		{
			Class408 left = TreeSentence.smethod_0(tokenes_0, parserEnvironment_0);
			tokenes_0.method_1();
			if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_30 && !(tokenes_0.Current.Symbol.Name != ";"))
			{
				HToken token = tokenes_0.Current;
				tokenes_0.method_1();
				Class408 result;
				if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
				{
					result = new TreeSentence(token, left, new Class409(new HToken(0, 0, new Class439(Enum26.const_30, "结束"))));
				}
				else
				{
					Class408 right = Class419.smethod_1(tokenes_0, parserEnvironment_0);
					result = new TreeSentence(token, left, right);
				}
				return result;
			}
			throw new Exception(tokenes_0.Current.method_0("不是分号"));
		}

		// Token: 0x06002276 RID: 8822 RVA: 0x000EACA4 File Offset: 0x000E8EA4
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			if (this.Token.Symbol.HSymbolType != Enum26.const_22)
			{
				throw new Exception(this.Token.method_0(";"));
			}
			List<DataArray> list = new List<DataArray>();
			parserEnvironment_0.DataArrays = list;
			parserEnvironment_0.list_0 = new List<NameDoubleValue>();
			Class408 left = this.Left;
			Class408 right = this.Right;
			for (;;)
			{
				if (left.Token.Symbol.HSymbolType != Enum26.const_30)
				{
					object obj = left.vmethod_1(parserEnvironment_0);
					if (obj.GetType() == typeof(DataArray))
					{
						list.Add(obj as DataArray);
					}
					else if (obj.GetType() == typeof(NameDoubleValue))
					{
						parserEnvironment_0.list_0.Add(obj as NameDoubleValue);
					}
				}
				if (right.Token.Symbol.HSymbolType == Enum26.const_30)
				{
					break;
				}
				left = right.Left;
				right = right.Right;
			}
			return list;
		}
	}
}
