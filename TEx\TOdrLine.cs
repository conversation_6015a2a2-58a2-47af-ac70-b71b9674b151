﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Runtime.CompilerServices;
using ns28;
using TEx.Chart;
using TEx.Comn;

namespace TEx
{
	// Token: 0x020000AC RID: 172
	internal abstract class TOdrLine : IDisposable
	{
		// Token: 0x060005EC RID: 1516 RVA: 0x000047CE File Offset: 0x000029CE
		public TOdrLine(ChartCS chart, decimal price)
		{
			this.chart = chart;
			this.price = price;
		}

		// Token: 0x060005ED RID: 1517 RVA: 0x000047E6 File Offset: 0x000029E6
		public void Dispose()
		{
			this.vmethod_0(true);
			GC.SuppressFinalize(this);
		}

		// Token: 0x060005EE RID: 1518 RVA: 0x0002C484 File Offset: 0x0002A684
		~TOdrLine()
		{
			this.vmethod_0(false);
		}

		// Token: 0x060005EF RID: 1519 RVA: 0x000047F7 File Offset: 0x000029F7
		protected virtual void vmethod_0(bool bool_1)
		{
			if (!this.bool_0)
			{
				this.bool_0 = true;
			}
		}

		// Token: 0x060005F0 RID: 1520 RVA: 0x0002C4B4 File Offset: 0x0002A6B4
		protected void method_0()
		{
			LineObj lineObj = this.vmethod_5(this.Price);
			if (lineObj != null)
			{
				this.lineObj_0 = lineObj;
				this.method_1(lineObj);
			}
		}

		// Token: 0x060005F1 RID: 1521 RVA: 0x0002C4E4 File Offset: 0x0002A6E4
		private void method_1(LineObj lineObj_1)
		{
			this.Chart.GraphPane.GraphObjList.Add(lineObj_1);
			if (this.Chart.TOdrLineList == null)
			{
				this.Chart.TOdrLineList = new List<TOdrLine>();
			}
			this.Chart.TOdrLineList.Add(this);
			this.method_2();
			this.Chart.ZedGraphControl.Refresh();
			if (Base.UI.TOdrLineList == null)
			{
				Base.UI.TOdrLineList = new List<TOdrLine>();
			}
			Base.UI.TOdrLineList.Add(this);
		}

		// Token: 0x060005F2 RID: 1522 RVA: 0x0002C56C File Offset: 0x0002A76C
		private void method_2()
		{
			TextObj textObj = this.vmethod_6();
			this.TextBox = textObj;
			this.Chart.method_60(this.TextBox);
			this.Chart.GraphPane.GraphObjList.Add(textObj);
		}

		// Token: 0x060005F3 RID: 1523
		public abstract void vmethod_1();

		// Token: 0x060005F4 RID: 1524 RVA: 0x0000480C File Offset: 0x00002A0C
		public virtual void vmethod_2()
		{
			this.method_3(true);
		}

		// Token: 0x060005F5 RID: 1525 RVA: 0x0002C5B0 File Offset: 0x0002A7B0
		private void method_3(bool bool_1)
		{
			if (bool_1 && !this.IsCurrent)
			{
				this.method_4();
			}
			else if (this.Chart.GraphPane != null)
			{
				double min = this.Chart.GraphPane.XAxis.Scale.Min;
				double max = this.Chart.GraphPane.XAxis.Scale.Max;
				double num = this.vmethod_3();
				if (this.Line.Location.X1 != min || this.Line.Location.X2 != max || this.Line.Location.Y != num)
				{
					this.vmethod_4(num);
					this.Chart.ZedGraphControl.Refresh();
				}
			}
		}

		// Token: 0x060005F6 RID: 1526
		protected abstract double vmethod_3();

		// Token: 0x060005F7 RID: 1527 RVA: 0x0002C674 File Offset: 0x0002A874
		public virtual void vmethod_4(double double_0)
		{
			double min = this.Chart.GraphPane.XAxis.Scale.Min;
			double width = this.Chart.GraphPane.XAxis.Scale.Max - min;
			this.Line.Location = new Location(min, double_0, width, 0.0, CoordType.AxisXYScale, AlignH.Center, AlignV.Center);
			this.vmethod_1();
			this.TextBox.Location = new Location(0.0, double_0, this.TextBox.Location.CoordinateFrame);
			this.TextBox.Location.AlignH = AlignH.Left;
			this.TextBox.Location.AlignV = AlignV.Bottom;
		}

		// Token: 0x060005F8 RID: 1528 RVA: 0x0002C72C File Offset: 0x0002A92C
		public void method_4()
		{
			try
			{
				this.Chart.GraphPane.GraphObjList.RemoveAll(new Predicate<GraphObj>(this.method_6));
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
			try
			{
				this.Chart.GraphPane.GraphObjList.RemoveAll(new Predicate<GraphObj>(this.method_7));
			}
			catch (Exception exception_2)
			{
				Class182.smethod_0(exception_2);
			}
			this.Chart.ZedGraphControl.Refresh();
			if (Base.UI.TOdrLineList != null)
			{
				Base.UI.TOdrLineList.Remove(this);
			}
			this.System.IDisposable.Dispose();
		}

		// Token: 0x060005F9 RID: 1529 RVA: 0x0002C7D8 File Offset: 0x0002A9D8
		protected virtual LineObj vmethod_5(decimal decimal_0)
		{
			LineObj result;
			try
			{
				Color color = this.vmethod_7();
				double num = Convert.ToDouble(decimal_0);
				double min = this.Chart.GraphPane.XAxis.Scale.Min;
				double max = this.Chart.GraphPane.XAxis.Scale.Max;
				result = new LineObj(color, min, num, max, num)
				{
					IsClippedToChartRect = true,
					Line = 
					{
						Style = this.vmethod_8()
					},
					Tag = TOdrLine.string_1 + "_" + Guid.NewGuid().ToString().Substring(0, 19),
					ZOrder = ZOrder.B_BehindLegend
				};
				goto IL_AB;
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
			return null;
			IL_AB:
			return result;
		}

		// Token: 0x060005FA RID: 1530 RVA: 0x0002C8A8 File Offset: 0x0002AAA8
		protected virtual TextObj vmethod_6()
		{
			return new TextObj(this.Text, 0.0, Convert.ToDouble(this.Price))
			{
				FontSpec = 
				{
					Fill = 
					{
						IsVisible = false
					},
					FontColor = this.Line.Line.Color,
					Border = 
					{
						IsVisible = false
					},
					StringAlignment = StringAlignment.Near,
					Size = 10f
				},
				Location = 
				{
					AlignH = AlignH.Left,
					AlignV = AlignV.Bottom
				},
				ZOrder = ZOrder.B_BehindLegend,
				Tag = this.Line.Tag
			};
		}

		// Token: 0x060005FB RID: 1531 RVA: 0x0002C964 File Offset: 0x0002AB64
		protected string method_5(decimal decimal_0)
		{
			int decimals = 2;
			TradingSymbol tradingSymbol = null;
			if (this.Chart != null && this.Chart.Symbol != null)
			{
				tradingSymbol = this.Chart.Symbol.MstSymbol;
			}
			if (tradingSymbol != null)
			{
				decimals = tradingSymbol.DigitNb;
			}
			string result;
			if (decimal_0 == 0m)
			{
				result = "市价";
			}
			else
			{
				result = (Math.Round(decimal_0, decimals) / 1.000000000000000000m).ToString();
			}
			return result;
		}

		// Token: 0x060005FC RID: 1532
		protected abstract Color vmethod_7();

		// Token: 0x060005FD RID: 1533
		protected abstract DashStyle vmethod_8();

		// Token: 0x17000129 RID: 297
		// (get) Token: 0x060005FE RID: 1534 RVA: 0x0002C9E8 File Offset: 0x0002ABE8
		// (set) Token: 0x060005FF RID: 1535 RVA: 0x00004817 File Offset: 0x00002A17
		public LineObj Line
		{
			get
			{
				return this.lineObj_0;
			}
			set
			{
				this.lineObj_0 = value;
			}
		}

		// Token: 0x1700012A RID: 298
		// (get) Token: 0x06000600 RID: 1536 RVA: 0x0002CA00 File Offset: 0x0002AC00
		// (set) Token: 0x06000601 RID: 1537 RVA: 0x00004822 File Offset: 0x00002A22
		public ChartCS Chart
		{
			get
			{
				return this.chart;
			}
			set
			{
				this.chart = value;
			}
		}

		// Token: 0x1700012B RID: 299
		// (get) Token: 0x06000602 RID: 1538 RVA: 0x0002CA18 File Offset: 0x0002AC18
		// (set) Token: 0x06000603 RID: 1539 RVA: 0x0000482D File Offset: 0x00002A2D
		public TextObj TextBox
		{
			get
			{
				return this.textObj_0;
			}
			set
			{
				this.textObj_0 = value;
			}
		}

		// Token: 0x1700012C RID: 300
		// (get) Token: 0x06000604 RID: 1540 RVA: 0x0002CA30 File Offset: 0x0002AC30
		// (set) Token: 0x06000605 RID: 1541 RVA: 0x00004838 File Offset: 0x00002A38
		public decimal Price
		{
			get
			{
				return this.price;
			}
			set
			{
				this.price = value;
			}
		}

		// Token: 0x1700012D RID: 301
		// (get) Token: 0x06000606 RID: 1542 RVA: 0x0002CA48 File Offset: 0x0002AC48
		// (set) Token: 0x06000607 RID: 1543 RVA: 0x00004843 File Offset: 0x00002A43
		public string Text
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x1700012E RID: 302
		// (get) Token: 0x06000608 RID: 1544
		public abstract bool IsCurrent { get; }

		// Token: 0x1700012F RID: 303
		// (get) Token: 0x06000609 RID: 1545
		// (set) Token: 0x0600060A RID: 1546
		public abstract bool HighLighted { get; set; }

		// Token: 0x0600060C RID: 1548 RVA: 0x0002CA60 File Offset: 0x0002AC60
		[CompilerGenerated]
		private bool method_6(GraphObj graphObj_0)
		{
			bool result;
			if (graphObj_0 is LineObj)
			{
				result = (graphObj_0.Tag == this.Line.Tag);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600060D RID: 1549 RVA: 0x0002CA90 File Offset: 0x0002AC90
		[CompilerGenerated]
		private bool method_7(GraphObj graphObj_0)
		{
			bool result;
			if (graphObj_0 is TextObj)
			{
				result = (graphObj_0.Tag == this.TextBox.Tag);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0400029C RID: 668
		private LineObj lineObj_0;

		// Token: 0x0400029D RID: 669
		private ChartCS chart;

		// Token: 0x0400029E RID: 670
		private TextObj textObj_0;

		// Token: 0x0400029F RID: 671
		private string string_0;

		// Token: 0x040002A0 RID: 672
		private decimal price;

		// Token: 0x040002A1 RID: 673
		public static readonly string string_1 = "TOLINE_";

		// Token: 0x040002A2 RID: 674
		private bool bool_0;
	}
}
