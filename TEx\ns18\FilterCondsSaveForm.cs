﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns23;
using ns3;
using TEx;
using TEx.Comn;

namespace ns18
{
	// Token: 0x02000094 RID: 148
	internal sealed partial class FilterCondsSaveForm : Form
	{
		// Token: 0x1400001F RID: 31
		// (add) Token: 0x060004E3 RID: 1251 RVA: 0x000265AC File Offset: 0x000247AC
		// (remove) Token: 0x060004E4 RID: 1252 RVA: 0x000265E4 File Offset: 0x000247E4
		public event MsgEventHandler CondGroupSaved
		{
			[CompilerGenerated]
			add
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Combine(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Remove(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
		}

		// Token: 0x060004E5 RID: 1253 RVA: 0x0000429E File Offset: 0x0000249E
		protected void method_0(string string_1)
		{
			MsgEventHandler msgEventHandler = this.msgEventHandler_0;
			if (msgEventHandler != null)
			{
				msgEventHandler(this, new MsgEventArgs(string_1, null));
			}
		}

		// Token: 0x060004E6 RID: 1254 RVA: 0x0002661C File Offset: 0x0002481C
		public FilterCondsSaveForm()
		{
			this.InitializeComponent();
			this.FilterCondsUserData = Class300.smethod_1();
			base.StartPosition = FormStartPosition.CenterScreen;
			this.button_OK.Click += this.button_OK_Click;
			this.button_Cancel.Click += this.button_Cancel_Click;
		}

		// Token: 0x060004E7 RID: 1255 RVA: 0x00026678 File Offset: 0x00024878
		public FilterCondsSaveForm(List<FilterCond> list_1 = null, string string_1 = null) : this()
		{
			this.FilterCondsToBeSaved = list_1;
			this.CurrUsrCfgCondGrpName = string_1;
			string text = "条件组1";
			if (!string.IsNullOrEmpty(string_1))
			{
				text = string_1;
			}
			this.textBox.Text = text;
		}

		// Token: 0x060004E8 RID: 1256 RVA: 0x000266B8 File Offset: 0x000248B8
		private void button_OK_Click(object sender, EventArgs e)
		{
			if (this.FilterCondsToBeSaved != null)
			{
				FilterCondsSaveForm.Class57 @class = new FilterCondsSaveForm.Class57();
				@class.string_0 = this.textBox.Text.Trim();
				if (!string.IsNullOrEmpty(@class.string_0))
				{
					bool flag = false;
					List<Class301> list;
					if (this.FilterCondsUserData != null)
					{
						flag = this.FilterCondsUserData.CondGroups.Exists(new Predicate<Class301>(@class.method_0));
						list = this.FilterCondsUserData.CondGroups;
					}
					else
					{
						list = new List<Class301>();
						this.FilterCondsUserData = new Class300();
					}
					if (flag)
					{
						if (MessageBox.Show("该条件组已存在，覆盖保存吗？", "提示", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question) != DialogResult.Yes)
						{
							return;
						}
						list.RemoveAll(new Predicate<Class301>(@class.method_1));
					}
					list.Add(new Class301
					{
						Name = @class.string_0,
						FilterConds = this.FilterCondsToBeSaved
					});
					Class300 filterCondsUserData = this.FilterCondsUserData;
					filterCondsUserData.CurrGrpName = @class.string_0;
					filterCondsUserData.CondGroups = list;
					if (filterCondsUserData.method_1())
					{
						this.method_0(@class.string_0);
					}
					base.Close();
				}
				else
				{
					this.textBox.Focus();
				}
			}
		}

		// Token: 0x060004E9 RID: 1257 RVA: 0x00004268 File Offset: 0x00002468
		private void button_Cancel_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x17000101 RID: 257
		// (get) Token: 0x060004EA RID: 1258 RVA: 0x000267D8 File Offset: 0x000249D8
		// (set) Token: 0x060004EB RID: 1259 RVA: 0x000042BB File Offset: 0x000024BB
		public List<FilterCond> FilterCondsToBeSaved { get; set; }

		// Token: 0x17000102 RID: 258
		// (get) Token: 0x060004EC RID: 1260 RVA: 0x000267F0 File Offset: 0x000249F0
		// (set) Token: 0x060004ED RID: 1261 RVA: 0x000042C6 File Offset: 0x000024C6
		public Class300 FilterCondsUserData { get; set; }

		// Token: 0x17000103 RID: 259
		// (get) Token: 0x060004EE RID: 1262 RVA: 0x00026808 File Offset: 0x00024A08
		// (set) Token: 0x060004EF RID: 1263 RVA: 0x000042D1 File Offset: 0x000024D1
		public string CurrUsrCfgCondGrpName { get; set; }

		// Token: 0x060004F0 RID: 1264 RVA: 0x000042DC File Offset: 0x000024DC
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x040001FC RID: 508
		[CompilerGenerated]
		private MsgEventHandler msgEventHandler_0;

		// Token: 0x040001FD RID: 509
		[CompilerGenerated]
		private List<FilterCond> list_0;

		// Token: 0x040001FE RID: 510
		[CompilerGenerated]
		private Class300 class300_0;

		// Token: 0x040001FF RID: 511
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04000200 RID: 512
		private IContainer icontainer_0;

		// Token: 0x02000095 RID: 149
		[CompilerGenerated]
		private sealed class Class57
		{
			// Token: 0x060004F3 RID: 1267 RVA: 0x00026AB4 File Offset: 0x00024CB4
			internal bool method_0(Class301 class301_0)
			{
				return class301_0.Name.Equals(this.string_0, StringComparison.InvariantCultureIgnoreCase);
			}

			// Token: 0x060004F4 RID: 1268 RVA: 0x00026AB4 File Offset: 0x00024CB4
			internal bool method_1(Class301 class301_0)
			{
				return class301_0.Name.Equals(this.string_0, StringComparison.InvariantCultureIgnoreCase);
			}

			// Token: 0x04000205 RID: 517
			public string string_0;
		}
	}
}
