﻿using System;
using System.ComponentModel;

namespace TEx
{
	// Token: 0x020001D7 RID: 471
	[Serializable]
	public sealed class DrawSublineParam
	{
		// Token: 0x170002BD RID: 701
		// (get) Token: 0x0600126E RID: 4718 RVA: 0x0007F0EC File Offset: 0x0007D2EC
		// (set) Token: 0x0600126F RID: 4719 RVA: 0x00007A7A File Offset: 0x00005C7A
		[DisplayName("#")]
		public string Name { get; set; }

		// Token: 0x170002BE RID: 702
		// (get) Token: 0x06001270 RID: 4720 RVA: 0x0007F104 File Offset: 0x0007D304
		// (set) Token: 0x06001271 RID: 4721 RVA: 0x00007A85 File Offset: 0x00005C85
		[DisplayName("显示")]
		public bool Enabled { get; set; }

		// Token: 0x170002BF RID: 703
		// (get) Token: 0x06001272 RID: 4722 RVA: 0x0007F11C File Offset: 0x0007D31C
		// (set) Token: 0x06001273 RID: 4723 RVA: 0x00007A90 File Offset: 0x00005C90
		[DisplayName("分线参数值")]
		public double Value { get; set; }

		// Token: 0x170002C0 RID: 704
		// (get) Token: 0x06001274 RID: 4724 RVA: 0x0007F134 File Offset: 0x0007D334
		// (set) Token: 0x06001275 RID: 4725 RVA: 0x00007A9B File Offset: 0x00005C9B
		public int DigitNb { get; set; }

		// Token: 0x170002C1 RID: 705
		// (get) Token: 0x06001276 RID: 4726 RVA: 0x0007F14C File Offset: 0x0007D34C
		// (set) Token: 0x06001277 RID: 4727 RVA: 0x00007AA6 File Offset: 0x00005CA6
		public double MinValue { get; set; }

		// Token: 0x170002C2 RID: 706
		// (get) Token: 0x06001278 RID: 4728 RVA: 0x0007F164 File Offset: 0x0007D364
		// (set) Token: 0x06001279 RID: 4729 RVA: 0x00007AB1 File Offset: 0x00005CB1
		public double? MaxValue { get; set; }
	}
}
