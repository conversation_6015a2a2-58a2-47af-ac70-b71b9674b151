﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using TEx.Inds;

namespace TEx.SIndicator
{
	// Token: 0x020002D1 RID: 721
	public sealed partial class FormIndFunction : Form
	{
		// Token: 0x06002043 RID: 8259 RVA: 0x000DD614 File Offset: 0x000DB814
		public FormIndFunction()
		{
			this.InitializeComponent();
			base.Load += this.FormIndFunction_Load;
			base.AcceptButton = this.buttonOK;
			this.FunctionName = "";
			this.dataGridViewCategroy.Click += this.dataGridViewCategroy_Click;
			this.dataGridViewFuncName.Click += this.dataGridViewFuncName_Click;
			this.dataGridViewFuncName.DoubleClick += this.dataGridViewFuncName_DoubleClick;
			Base.UI.smethod_55(this.dataGridViewCategroy);
			Base.UI.smethod_55(this.dataGridViewFuncName);
			base.AcceptButton = this.buttonOK;
			base.CancelButton = this.buttonClose;
			this.textBoxFindInfo.Focus();
			Base.UI.smethod_54(this);
		}

		// Token: 0x06002044 RID: 8260 RVA: 0x0000D169 File Offset: 0x0000B369
		private void dataGridViewFuncName_DoubleClick(object sender, EventArgs e)
		{
			this.method_4();
		}

		// Token: 0x06002045 RID: 8261 RVA: 0x0000D173 File Offset: 0x0000B373
		private void dataGridViewFuncName_Click(object sender, EventArgs e)
		{
			this.dataGridViewFuncName.CurrentRow.Selected = true;
		}

		// Token: 0x06002046 RID: 8262 RVA: 0x000DD6E8 File Offset: 0x000DB8E8
		private List<IndFuncAttri> method_0(string string_1)
		{
			FormIndFunction.Class373 @class = new FormIndFunction.Class373();
			@class.string_0 = string_1;
			List<IndFuncAttri> result;
			if (@class.string_0 == "全部函数")
			{
				result = this.list_0;
			}
			else
			{
				IGrouping<string, IndFuncAttri> grouping = this.list_0.GroupBy(new Func<IndFuncAttri, string>(FormIndFunction.<>c.<>9.method_0)).SingleOrDefault(new Func<IGrouping<string, IndFuncAttri>, bool>(@class.method_0));
				if (grouping != null)
				{
					result = new List<IndFuncAttri>(grouping.ToArray<IndFuncAttri>());
				}
				else
				{
					result = new List<IndFuncAttri>();
				}
			}
			return result;
		}

		// Token: 0x06002047 RID: 8263 RVA: 0x000DD774 File Offset: 0x000DB974
		private void method_1(List<IndFuncAttri> list_1)
		{
			if (list_1.Any<IndFuncAttri>())
			{
				List<NameScript> dataSource = this.method_3(list_1);
				this.dataGridViewFuncName.DataSource = dataSource;
				this.dataGridViewFuncName.Columns["Name"].AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
				this.dataGridViewFuncName.Columns["Script"].AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
			}
		}

		// Token: 0x06002048 RID: 8264 RVA: 0x000DD7D8 File Offset: 0x000DB9D8
		private void method_2(string string_1)
		{
			try
			{
				List<IndFuncAttri> list_ = this.method_0(string_1);
				this.method_1(list_);
			}
			catch (Exception)
			{
			}
		}

		// Token: 0x06002049 RID: 8265 RVA: 0x000DD80C File Offset: 0x000DBA0C
		private void dataGridViewCategroy_Click(object sender, EventArgs e)
		{
			try
			{
				string string_ = this.dataGridViewCategroy.CurrentRow.Cells["cate"].Value.ToString();
				this.method_2(string_);
			}
			catch (Exception)
			{
			}
		}

		// Token: 0x0600204A RID: 8266 RVA: 0x000DD85C File Offset: 0x000DBA5C
		private List<NameScript> method_3(List<IndFuncAttri> list_1)
		{
			List<NameScript> list = new List<NameScript>();
			foreach (IndFuncAttri indFuncAttri in list_1)
			{
				NameScript item = new NameScript(indFuncAttri.Name, indFuncAttri.ParamStr + indFuncAttri.Script);
				list.Add(item);
			}
			return list;
		}

		// Token: 0x0600204B RID: 8267 RVA: 0x000DD8D8 File Offset: 0x000DBAD8
		private void FormIndFunction_Load(object sender, EventArgs e)
		{
			this.dataGridViewFuncName.ColumnHeadersVisible = false;
			this.dataGridViewFuncName.RowHeadersVisible = false;
			this.dataGridViewFuncName.ReadOnly = true;
			this.dataGridViewFuncName.CellBorderStyle = DataGridViewCellBorderStyle.None;
			DataGridViewTextBoxColumn dataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
			dataGridViewTextBoxColumn.Name = "cate";
			dataGridViewTextBoxColumn.HeaderText = "分类";
			this.dataGridViewCategroy.Columns.Add(dataGridViewTextBoxColumn);
			this.dataGridViewCategroy.Columns[0].Width = this.dataGridViewCategroy.Width;
			this.dataGridViewCategroy.ColumnHeadersVisible = false;
			this.dataGridViewCategroy.RowHeadersVisible = false;
			this.dataGridViewCategroy.EditMode = DataGridViewEditMode.EditProgrammatically;
			this.dataGridViewCategroy.CellBorderStyle = DataGridViewCellBorderStyle.None;
			this.dataGridViewCategroy.ReadOnly = true;
			this.list_0 = ParserEnvironment.IndFuncAttriList;
			List<string> list = this.list_0.GroupBy(new Func<IndFuncAttri, string>(FormIndFunction.<>c.<>9.method_1)).Select(new Func<IGrouping<string, IndFuncAttri>, string>(FormIndFunction.<>c.<>9.method_2)).ToList<string>();
			int index = this.dataGridViewCategroy.Rows.Add();
			this.dataGridViewCategroy.Rows[index].Cells["cate"].Value = "全部函数";
			foreach (string text in list)
			{
				string[] array = text.Split(new char[]
				{
					'.'
				});
				if (array.Count<string>() == 2)
				{
					int index2 = this.dataGridViewCategroy.Rows.Add();
					this.dataGridViewCategroy.Rows[index2].Cells["cate"].Value = array[1];
				}
			}
			this.textBoxFindInfo.Select();
			this.method_2("全部函数");
		}

		// Token: 0x0600204C RID: 8268 RVA: 0x000DDAE0 File Offset: 0x000DBCE0
		private void method_4()
		{
			if (this.dataGridViewFuncName.Rows.Count > 0 && this.dataGridViewFuncName.CurrentRow != null)
			{
				this.FunctionName = this.dataGridViewFuncName.CurrentRow.Cells["Name"].Value.ToString();
				base.DialogResult = DialogResult.OK;
			}
			else
			{
				base.DialogResult = DialogResult.Cancel;
			}
			base.Close();
		}

		// Token: 0x170005B1 RID: 1457
		// (get) Token: 0x0600204D RID: 8269 RVA: 0x000DDB50 File Offset: 0x000DBD50
		// (set) Token: 0x0600204E RID: 8270 RVA: 0x0000D188 File Offset: 0x0000B388
		public string FunctionName { get; set; }

		// Token: 0x0600204F RID: 8271 RVA: 0x000DDB68 File Offset: 0x000DBD68
		private void buttonOK_Click(object sender, EventArgs e)
		{
			this.method_4();
			if (this.dataGridViewFuncName.Rows.Count > 0 && this.dataGridViewFuncName.CurrentRow != null)
			{
				this.FunctionName = this.dataGridViewFuncName.CurrentRow.Cells["Name"].Value.ToString();
			}
		}

		// Token: 0x06002050 RID: 8272 RVA: 0x000045E3 File Offset: 0x000027E3
		private void buttonClose_Click(object sender, EventArgs e)
		{
			base.DialogResult = DialogResult.Cancel;
			base.Close();
		}

		// Token: 0x06002051 RID: 8273 RVA: 0x000041AE File Offset: 0x000023AE
		private void method_5()
		{
		}

		// Token: 0x06002052 RID: 8274 RVA: 0x000DDBC8 File Offset: 0x000DBDC8
		private void buttonFind_Click(object sender, EventArgs e)
		{
			FormIndFunction.Class374 @class = new FormIndFunction.Class374();
			@class.string_0 = this.textBoxFindInfo.Text.Trim();
			List<IndFuncAttri> list = this.list_0.Where(new Func<IndFuncAttri, bool>(@class.method_0)).ToList<IndFuncAttri>();
			list.Sort();
			this.method_1(list);
		}

		// Token: 0x06002053 RID: 8275 RVA: 0x000DDC20 File Offset: 0x000DBE20
		private void textBoxFindInfo_TextChanged(object sender, EventArgs e)
		{
			FormIndFunction.Class375 @class = new FormIndFunction.Class375();
			@class.string_0 = this.textBoxFindInfo.Text.Trim();
			List<IndFuncAttri> list = this.list_0.Where(new Func<IndFuncAttri, bool>(@class.method_0)).ToList<IndFuncAttri>();
			list.Sort();
			this.method_1(list);
		}

		// Token: 0x06002054 RID: 8276 RVA: 0x0000D193 File Offset: 0x0000B393
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000FD5 RID: 4053
		private List<IndFuncAttri> list_0 = new List<IndFuncAttri>();

		// Token: 0x04000FD6 RID: 4054
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04000FD7 RID: 4055
		private IContainer icontainer_0;

		// Token: 0x020002D2 RID: 722
		[CompilerGenerated]
		private sealed class Class373
		{
			// Token: 0x06002057 RID: 8279 RVA: 0x000DE23C File Offset: 0x000DC43C
			internal bool method_0(IGrouping<string, IndFuncAttri> igrouping_0)
			{
				return igrouping_0.Key.Contains(this.string_0);
			}

			// Token: 0x04000FE0 RID: 4064
			public string string_0;
		}

		// Token: 0x020002D4 RID: 724
		[CompilerGenerated]
		private sealed class Class374
		{
			// Token: 0x0600205E RID: 8286 RVA: 0x000DE290 File Offset: 0x000DC490
			internal bool method_0(IndFuncAttri indFuncAttri_0)
			{
				return indFuncAttri_0.Name.IndexOf(this.string_0) > 0;
			}

			// Token: 0x04000FE5 RID: 4069
			public string string_0;
		}

		// Token: 0x020002D5 RID: 725
		[CompilerGenerated]
		private sealed class Class375
		{
			// Token: 0x06002060 RID: 8288 RVA: 0x000DE2B8 File Offset: 0x000DC4B8
			internal bool method_0(IndFuncAttri indFuncAttri_0)
			{
				return indFuncAttri_0.Name.IndexOf(this.string_0) >= 0;
			}

			// Token: 0x04000FE6 RID: 4070
			public string string_0;
		}
	}
}
