﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace ns9
{
	// Token: 0x02000011 RID: 17
	[CompilerGenerated]
	internal sealed class Class8<T, U, V>
	{
		// Token: 0x17000024 RID: 36
		// (get) Token: 0x06000060 RID: 96 RVA: 0x000114B8 File Offset: 0x0000F6B8
		public T api
		{
			get
			{
				return this.gparam_0;
			}
		}

		// Token: 0x17000025 RID: 37
		// (get) Token: 0x06000061 RID: 97 RVA: 0x000114D0 File Offset: 0x0000F6D0
		public U lc
		{
			get
			{
				return this.gparam_1;
			}
		}

		// Token: 0x17000026 RID: 38
		// (get) Token: 0x06000062 RID: 98 RVA: 0x000114E8 File Offset: 0x0000F6E8
		public V param
		{
			get
			{
				return this.gparam_2;
			}
		}

		// Token: 0x06000063 RID: 99 RVA: 0x00002BE2 File Offset: 0x00000DE2
		[DebuggerHidden]
		public Class8(T gparam_3, U gparam_4, V gparam_5)
		{
			this.gparam_0 = gparam_3;
			this.gparam_1 = gparam_4;
			this.gparam_2 = gparam_5;
		}

		// Token: 0x06000064 RID: 100 RVA: 0x00011500 File Offset: 0x0000F700
		[DebuggerHidden]
		public bool Equals(object obj)
		{
			Class8<T, U, V> @class = obj as Class8<T, U, V>;
			bool result;
			if (@class != null && EqualityComparer<T>.Default.Equals(this.gparam_0, @class.gparam_0) && EqualityComparer<U>.Default.Equals(this.gparam_1, @class.gparam_1))
			{
				result = EqualityComparer<V>.Default.Equals(this.gparam_2, @class.gparam_2);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06000065 RID: 101 RVA: 0x00011568 File Offset: 0x0000F768
		[DebuggerHidden]
		public int GetHashCode()
		{
			return ((-394522690 + EqualityComparer<T>.Default.GetHashCode(this.gparam_0)) * -********** + EqualityComparer<U>.Default.GetHashCode(this.gparam_1)) * -********** + EqualityComparer<V>.Default.GetHashCode(this.gparam_2);
		}

		// Token: 0x06000066 RID: 102 RVA: 0x000115C0 File Offset: 0x0000F7C0
		[DebuggerHidden]
		public string ToString()
		{
			IFormatProvider provider = null;
			string format = "{{ api = {0}, lc = {1}, param = {2} }}";
			object[] array = new object[3];
			int num = 0;
			T t = this.gparam_0;
			ref T ptr = ref t;
			T t2 = default(T);
			object obj;
			if (t2 == null)
			{
				t2 = t;
				ptr = ref t2;
				if (t2 == null)
				{
					obj = null;
					goto IL_46;
				}
			}
			obj = ptr.ToString();
			IL_46:
			array[num] = obj;
			int num2 = 1;
			U u = this.gparam_1;
			ref U ptr2 = ref u;
			U u2 = default(U);
			object obj2;
			if (u2 == null)
			{
				u2 = u;
				ptr2 = ref u2;
				if (u2 == null)
				{
					obj2 = null;
					goto IL_81;
				}
			}
			obj2 = ptr2.ToString();
			IL_81:
			array[num2] = obj2;
			int num3 = 2;
			V v = this.gparam_2;
			ref V ptr3 = ref v;
			V v2 = default(V);
			object obj3;
			if (v2 == null)
			{
				v2 = v;
				ptr3 = ref v2;
				if (v2 == null)
				{
					obj3 = null;
					goto IL_C0;
				}
			}
			obj3 = ptr3.ToString();
			IL_C0:
			array[num3] = obj3;
			return string.Format(provider, format, array);
		}

		// Token: 0x04000024 RID: 36
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly T gparam_0;

		// Token: 0x04000025 RID: 37
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly U gparam_1;

		// Token: 0x04000026 RID: 38
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly V gparam_2;
	}
}
