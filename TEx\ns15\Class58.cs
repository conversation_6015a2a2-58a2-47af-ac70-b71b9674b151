﻿using System;

namespace ns15
{
	// Token: 0x02000098 RID: 152
	internal sealed class Class58
	{
		// Token: 0x0600050C RID: 1292 RVA: 0x00004426 File Offset: 0x00002626
		public Class58(double double_4, double double_5, double double_6, double double_7)
		{
			this.double_0 = double_4;
			this.double_1 = double_5;
			this.double_2 = double_6;
			this.double_3 = double_7;
		}

		// Token: 0x17000104 RID: 260
		// (get) Token: 0x0600050D RID: 1293 RVA: 0x000278E8 File Offset: 0x00025AE8
		// (set) Token: 0x0600050E RID: 1294 RVA: 0x0000444D File Offset: 0x0000264D
		public double X1
		{
			get
			{
				return this.double_0;
			}
			set
			{
				this.double_0 = value;
			}
		}

		// Token: 0x17000105 RID: 261
		// (get) Token: 0x0600050F RID: 1295 RVA: 0x00027900 File Offset: 0x00025B00
		// (set) Token: 0x06000510 RID: 1296 RVA: 0x00004458 File Offset: 0x00002658
		public double Y1
		{
			get
			{
				return this.double_1;
			}
			set
			{
				this.double_1 = value;
			}
		}

		// Token: 0x17000106 RID: 262
		// (get) Token: 0x06000511 RID: 1297 RVA: 0x00027918 File Offset: 0x00025B18
		// (set) Token: 0x06000512 RID: 1298 RVA: 0x00004463 File Offset: 0x00002663
		public double X2
		{
			get
			{
				return this.double_2;
			}
			set
			{
				this.double_2 = value;
			}
		}

		// Token: 0x17000107 RID: 263
		// (get) Token: 0x06000513 RID: 1299 RVA: 0x00027930 File Offset: 0x00025B30
		// (set) Token: 0x06000514 RID: 1300 RVA: 0x0000446E File Offset: 0x0000266E
		public double Y2
		{
			get
			{
				return this.double_3;
			}
			set
			{
				this.double_3 = value;
			}
		}

		// Token: 0x04000219 RID: 537
		private double double_0;

		// Token: 0x0400021A RID: 538
		private double double_1;

		// Token: 0x0400021B RID: 539
		private double double_2;

		// Token: 0x0400021C RID: 540
		private double double_3;
	}
}
