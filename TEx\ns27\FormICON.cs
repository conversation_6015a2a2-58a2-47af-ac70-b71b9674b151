﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Reflection;
using System.Windows.Forms;
using TEx;

namespace ns27
{
	// Token: 0x020002D0 RID: 720
	internal sealed partial class FormICON : Form
	{
		// Token: 0x06002038 RID: 8248 RVA: 0x000DCFC0 File Offset: 0x000DB1C0
		public FormICON()
		{
			this.InitializeComponent();
			base.Load += this.FormICON_Load;
			this.buttonOK.Click += this.buttonOK_Click;
			this.buttonCancel.Click += this.buttonCancel_Click;
			Base.UI.smethod_54(this);
			base.StartPosition = FormStartPosition.CenterParent;
		}

		// Token: 0x06002039 RID: 8249 RVA: 0x000045E3 File Offset: 0x000027E3
		private void buttonCancel_Click(object sender, EventArgs e)
		{
			base.DialogResult = DialogResult.Cancel;
			base.Close();
		}

		// Token: 0x0600203A RID: 8250 RVA: 0x0000D126 File Offset: 0x0000B326
		private void buttonOK_Click(object sender, EventArgs e)
		{
			if (this.string_0 != null)
			{
				base.DialogResult = DialogResult.OK;
			}
			else
			{
				base.DialogResult = DialogResult.Abort;
			}
			base.Close();
		}

		// Token: 0x0600203B RID: 8251 RVA: 0x000DD04C File Offset: 0x000DB24C
		private void FormICON_Load(object sender, EventArgs e)
		{
			try
			{
				this.method_0();
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Hand);
			}
		}

		// Token: 0x0600203C RID: 8252 RVA: 0x000DD088 File Offset: 0x000DB288
		private void method_0()
		{
			Assembly executingAssembly = Assembly.GetExecutingAssembly();
			string[] manifestResourceNames = executingAssembly.GetManifestResourceNames();
			new List<string>();
			foreach (string text in manifestResourceNames)
			{
				if (text.Contains("TEx.Resources.ICON."))
				{
					string[] array2 = text.Split(new char[]
					{
						'.'
					});
					if (array2.Length == 5)
					{
						Image backgroundImage = Image.FromStream(executingAssembly.GetManifestResourceStream(text));
						PictureBox pictureBox = new PictureBox();
						pictureBox.Width = 25;
						pictureBox.Height = 25;
						pictureBox.BorderStyle = BorderStyle.FixedSingle;
						pictureBox.SizeMode = PictureBoxSizeMode.StretchImage;
						pictureBox.BackgroundImageLayout = ImageLayout.Stretch;
						pictureBox.BackgroundImage = backgroundImage;
						pictureBox.Tag = array2[3];
						int num = int.Parse(array2[3]);
						this.toolTip_0.SetToolTip(pictureBox, "图标" + num);
						pictureBox.Click += this.method_4;
						pictureBox.DoubleClick += this.method_3;
						pictureBox.MouseEnter += this.method_2;
						pictureBox.MouseLeave += this.method_1;
						this.sortedList_0.Add(num, pictureBox);
					}
				}
			}
			for (int j = 0; j < this.sortedList_0.Count; j++)
			{
				int num2 = j / 16;
				int left = j % 16 * 25 + 5;
				int top = num2 * 25 + 30;
				this.groupBox1.Controls.Add(this.sortedList_0.Values[j]);
				this.sortedList_0.Values[j].Left = left;
				this.sortedList_0.Values[j].Top = top;
			}
		}

		// Token: 0x0600203D RID: 8253 RVA: 0x000DD250 File Offset: 0x000DB450
		private void method_1(object sender, EventArgs e)
		{
			PictureBox pictureBox = sender as PictureBox;
			if (pictureBox != null)
			{
				pictureBox.BackColor = Color.White;
			}
		}

		// Token: 0x0600203E RID: 8254 RVA: 0x000DD278 File Offset: 0x000DB478
		private void method_2(object sender, EventArgs e)
		{
			PictureBox pictureBox = sender as PictureBox;
			if (pictureBox != null)
			{
				pictureBox.BackColor = Color.LightGreen;
			}
		}

		// Token: 0x0600203F RID: 8255 RVA: 0x000DD2A0 File Offset: 0x000DB4A0
		private void method_3(object sender, EventArgs e)
		{
			PictureBox pictureBox = sender as PictureBox;
			if (pictureBox != null)
			{
				this.string_0 = (pictureBox.Tag as string);
			}
			if (this.string_0 != null)
			{
				base.DialogResult = DialogResult.OK;
			}
			else
			{
				base.DialogResult = DialogResult.Abort;
			}
			base.Close();
		}

		// Token: 0x06002040 RID: 8256 RVA: 0x000DD2E8 File Offset: 0x000DB4E8
		private void method_4(object sender, EventArgs e)
		{
			PictureBox pictureBox = sender as PictureBox;
			for (int i = 0; i < this.groupBox1.Controls.Count; i++)
			{
				PictureBox pictureBox2 = this.groupBox1.Controls[i] as PictureBox;
				if (pictureBox2 != null && pictureBox2.BorderStyle == BorderStyle.Fixed3D)
				{
					pictureBox2.BorderStyle = BorderStyle.FixedSingle;
				}
			}
			if (pictureBox != null)
			{
				this.string_0 = (pictureBox.Tag as string);
				pictureBox.Select();
				pictureBox.BorderStyle = BorderStyle.Fixed3D;
			}
		}

		// Token: 0x06002041 RID: 8257 RVA: 0x0000D148 File Offset: 0x0000B348
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000FCD RID: 4045
		private SortedList<int, PictureBox> sortedList_0 = new SortedList<int, PictureBox>();

		// Token: 0x04000FCE RID: 4046
		private ToolTip toolTip_0 = new ToolTip();

		// Token: 0x04000FCF RID: 4047
		public string string_0 = "";

		// Token: 0x04000FD0 RID: 4048
		private IContainer icontainer_0;
	}
}
