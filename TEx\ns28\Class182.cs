﻿using System;
using System.Windows.Forms;
using ns22;
using TEx;

namespace ns28
{
	// Token: 0x0200012F RID: 303
	internal static class Class182
	{
		// Token: 0x06000C8B RID: 3211 RVA: 0x0004928C File Offset: 0x0004748C
		public static void smethod_0(Exception exception_0)
		{
			Class182.smethod_1(exception_0, null);
		}

		// Token: 0x06000C8C RID: 3212 RVA: 0x000492AC File Offset: 0x000474AC
		public static void smethod_1(Exception exception_0, bool? nullable_0 = null)
		{
			string text = Environment.NewLine + "   Catched Exception.";
			try
			{
				if (TApp.EnteredMainForm)
				{
					text = string.Concat(new string[]
					{
						text,
						" CurrDate:",
						Base.Data.CurrDate.ToString(),
						"; ",
						Base.Data.smethod_127()
					});
				}
			}
			catch
			{
			}
			Class46.smethod_4(exception_0, true, text);
			bool flag = false;
			if (nullable_0 != null)
			{
				flag = nullable_0.Value;
			}
			if (flag)
			{
				throw exception_0;
			}
		}

		// Token: 0x06000C8D RID: 3213 RVA: 0x00049340 File Offset: 0x00047540
		public static void smethod_2(Exception exception_0, bool bool_0 = false)
		{
			string text = string.Concat(new string[]
			{
				"程序发生错误！错误信息: ",
				Environment.NewLine,
				exception_0.Message,
				Environment.NewLine,
				exception_0.StackTrace
			});
			if (exception_0.InnerException != null)
			{
				text = text + Environment.NewLine + exception_0.InnerException.Message;
			}
			text = text + Environment.NewLine + Environment.NewLine;
			if (bool_0)
			{
				text += "退出程序吗？";
			}
			DialogResult dialogResult = MessageBox.Show(text, "程序错误", MessageBoxButtons.OKCancel, MessageBoxIcon.Hand);
			if (bool_0 && dialogResult == DialogResult.OK)
			{
				Application.Exit();
			}
		}
	}
}
