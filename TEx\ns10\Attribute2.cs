﻿using System;

namespace ns10
{
	// Token: 0x020002A7 RID: 679
	[AttributeUsage(AttributeTargets.All)]
	internal sealed class Attribute2 : Attribute
	{
		// Token: 0x06001E1E RID: 7710 RVA: 0x0000C95D File Offset: 0x0000AB5D
		public Attribute2(string string_1)
		{
			this.string_0 = string_1;
		}

		// Token: 0x170004C5 RID: 1221
		// (get) Token: 0x06001E1F RID: 7711 RVA: 0x000CCF04 File Offset: 0x000CB104
		public string Name
		{
			get
			{
				return this.string_0;
			}
		}

		// Token: 0x04000ED5 RID: 3797
		private string string_0;
	}
}
