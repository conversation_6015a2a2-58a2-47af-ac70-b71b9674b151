﻿using System;

namespace ns19
{
	// Token: 0x0200012D RID: 301
	internal sealed class EventArgs6 : EventArgs
	{
		// Token: 0x06000C84 RID: 3204 RVA: 0x00005AD5 File Offset: 0x00003CD5
		public EventArgs6(bool bool_1)
		{
			this.bool_0 = bool_1;
		}

		// Token: 0x17000200 RID: 512
		// (get) Token: 0x06000C85 RID: 3205 RVA: 0x00049274 File Offset: 0x00047474
		// (set) Token: 0x06000C86 RID: 3206 RVA: 0x00005AE6 File Offset: 0x00003CE6
		public bool IsSucessful
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x04000528 RID: 1320
		private bool bool_0;
	}
}
