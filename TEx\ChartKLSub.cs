﻿using System;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using TEx.Chart;

namespace TEx
{
	// Token: 0x0200017B RID: 379
	internal class ChartKLSub : ChartKLine
	{
		// Token: 0x14000077 RID: 119
		// (add) Token: 0x06000E47 RID: 3655 RVA: 0x0005AF84 File Offset: 0x00059184
		// (remove) Token: 0x06000E48 RID: 3656 RVA: 0x0005AFBC File Offset: 0x000591BC
		public event EventHandler MouseClicked
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000E49 RID: 3657 RVA: 0x0005AFF4 File Offset: 0x000591F4
		protected void method_148()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x06000E4A RID: 3658 RVA: 0x00006583 File Offset: 0x00004783
		public ChartKLSub(ChtCtrl_KLine dChtCtrl_KLine, SplitterPanel panel) : base(dChtCtrl_KLine, panel)
		{
			base.ZedGraphControl.MouseDownEvent += this.method_149;
		}

		// Token: 0x06000E4B RID: 3659 RVA: 0x000065A6 File Offset: 0x000047A6
		protected override void vmethod_31(object sender, MouseEventArgs e)
		{
			this.method_148();
			this.IsEntered = true;
			base.vmethod_31(sender, e);
		}

		// Token: 0x06000E4C RID: 3660 RVA: 0x0005B01C File Offset: 0x0005921C
		private bool method_149(ZedGraphControl zedGraphControl_1, MouseEventArgs mouseEventArgs_0)
		{
			if (mouseEventArgs_0.Button == MouseButtons.Right && !this.IsEntered)
			{
				this.method_148();
				this.IsEntered = true;
			}
			return false;
		}

		// Token: 0x06000E4D RID: 3661 RVA: 0x0005B050 File Offset: 0x00059250
		private void method_150(bool bool_5)
		{
			bool flag = false;
			if (bool_5)
			{
				flag = true;
				base.GraphPane.Chart.Border.Width = 2f;
			}
			else if (base.GraphPane.Chart.Border.Width != 1f)
			{
				flag = true;
				base.GraphPane.Chart.Border.Width = 1f;
			}
			if (flag)
			{
				base.ZedGraphControl.Refresh();
			}
		}

		// Token: 0x06000E4E RID: 3662 RVA: 0x000041AE File Offset: 0x000023AE
		public override void vmethod_13(string string_10)
		{
		}

		// Token: 0x1700023D RID: 573
		// (get) Token: 0x06000E4F RID: 3663 RVA: 0x0005B0C8 File Offset: 0x000592C8
		// (set) Token: 0x06000E50 RID: 3664 RVA: 0x000065BF File Offset: 0x000047BF
		public bool IsEntered
		{
			get
			{
				return this.bool_4;
			}
			set
			{
				if (this.bool_4 != value)
				{
					this.method_150(value);
					this.bool_4 = value;
				}
			}
		}

		// Token: 0x04000771 RID: 1905
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000772 RID: 1906
		private bool bool_4;
	}
}
