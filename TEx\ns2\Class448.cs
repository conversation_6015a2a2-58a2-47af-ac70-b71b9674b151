﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using TEx.ImportTrans;

namespace ns2
{
	// Token: 0x02000342 RID: 834
	internal sealed class Class448
	{
		// Token: 0x17000604 RID: 1540
		// (get) Token: 0x06002317 RID: 8983 RVA: 0x000EE0DC File Offset: 0x000EC2DC
		// (set) Token: 0x06002318 RID: 8984 RVA: 0x0000DCCD File Offset: 0x0000BECD
		public string From { get; set; }

		// Token: 0x17000605 RID: 1541
		// (get) Token: 0x06002319 RID: 8985 RVA: 0x000EE0F4 File Offset: 0x000EC2F4
		// (set) Token: 0x0600231A RID: 8986 RVA: 0x0000DCD8 File Offset: 0x0000BED8
		public string CfmmcAcctID { get; set; }

		// Token: 0x17000606 RID: 1542
		// (get) Token: 0x0600231B RID: 8987 RVA: 0x000EE10C File Offset: 0x000EC30C
		public List<TransData> Data
		{
			get
			{
				return this.list_0;
			}
		}

		// Token: 0x0600231C RID: 8988 RVA: 0x0000DCE3 File Offset: 0x0000BEE3
		public Class448(string string_3, string string_4)
		{
			this.From = string_3;
			this.CfmmcAcctID = string_4;
			this.list_0 = new List<TransData>();
		}

		// Token: 0x0600231D RID: 8989 RVA: 0x0000DD06 File Offset: 0x0000BF06
		public Class448(string string_3)
		{
			this.From = string_3;
			this.list_0 = new List<TransData>();
		}

		// Token: 0x17000607 RID: 1543
		// (get) Token: 0x0600231E RID: 8990 RVA: 0x000EE124 File Offset: 0x000EC324
		public bool HasData
		{
			get
			{
				bool result;
				if (this.Data != null)
				{
					result = this.Data.Any<TransData>();
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x040010F3 RID: 4339
		[CompilerGenerated]
		private string string_0;

		// Token: 0x040010F4 RID: 4340
		[CompilerGenerated]
		private string string_1;

		// Token: 0x040010F5 RID: 4341
		private List<TransData> list_0;

		// Token: 0x040010F6 RID: 4342
		public static readonly string string_2 = "otherFile";
	}
}
