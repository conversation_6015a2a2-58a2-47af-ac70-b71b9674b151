﻿using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Text;
using ns15;
using ns17;
using ns22;
using ns24;

namespace ns34
{
	// Token: 0x020003D0 RID: 976
	internal static class Class516
	{
		// Token: 0x060026FE RID: 9982
		[DllImport("advapi32.dll", CharSet = CharSet.Unicode)]
		private static extern int RegOpenKeyEx(UIntPtr uintptr_0, string string_0, int int_0, int int_1, out UIntPtr uintptr_1);

		// Token: 0x060026FF RID: 9983
		[DllImport("advapi32.dll", CharSet = CharSet.Unicode, SetLastError = true)]
		private static extern int RegQueryValueEx(UIntPtr uintptr_0, string string_0, int[] int_0, ref int int_1, [Out] byte[] byte_0, ref int int_2);

		// Token: 0x06002700 RID: 9984
		[DllImport("advapi32.dll", SetLastError = true)]
		private static extern int RegCloseKey(UIntPtr uintptr_0);

		// Token: 0x06002701 RID: 9985 RVA: 0x000FCC2C File Offset: 0x000FAE2C
		internal static Class513 OpenKey(UIntPtr uintptr_0, Enum34 enum34_0, Enum32 enum32_0, string string_0, out Enum33 enum33_0)
		{
			try
			{
				UIntPtr uintptr_;
				switch (Class516.RegOpenKeyEx(uintptr_0, string_0, 0, (int)(enum32_0 | (Enum32)enum34_0), out uintptr_))
				{
				case 0:
					enum33_0 = Enum33.const_2;
					return new Class514(uintptr_);
				case 1:
					enum33_0 = Enum33.const_3;
					break;
				case 2:
					enum33_0 = Enum33.const_4;
					break;
				default:
					enum33_0 = Enum33.const_0;
					break;
				}
			}
			catch
			{
				enum33_0 = Enum33.const_0;
			}
			return null;
		}

		// Token: 0x06002702 RID: 9986 RVA: 0x000FCC94 File Offset: 0x000FAE94
		internal static object GetValue(UIntPtr uintptr_0, string string_0)
		{
			try
			{
				int num = -1;
				int num2 = -1;
				if (Class516.RegQueryValueEx(uintptr_0, string_0, null, ref num, null, ref num2) == 0)
				{
					switch (num)
					{
					case 0:
					case 3:
					case 5:
						goto IL_1AB;
					case 1:
					{
						byte[] array = new byte[num2];
						if (Class516.RegQueryValueEx(uintptr_0, string_0, null, ref num, array, ref num2) != 0)
						{
							return null;
						}
						return Encoding.Unicode.GetString(array).TrimEnd(new char[1]);
					}
					case 2:
					{
						byte[] array2 = new byte[num2];
						if (Class516.RegQueryValueEx(uintptr_0, string_0, null, ref num, array2, ref num2) != 0)
						{
							return null;
						}
						return Environment.ExpandEnvironmentVariables(Encoding.Unicode.GetString(array2).TrimEnd(new char[1]));
					}
					case 4:
					{
						if (num2 > 4)
						{
							goto IL_17B;
						}
						byte[] array3 = new byte[num2];
						if (Class516.RegQueryValueEx(uintptr_0, string_0, null, ref num, array3, ref num2) != 0)
						{
							return null;
						}
						return BitConverter.ToInt32(array3, 0);
					}
					case 7:
					{
						byte[] array4 = new byte[num2];
						if (Class516.RegQueryValueEx(uintptr_0, string_0, null, ref num, array4, ref num2) != 0)
						{
							return null;
						}
						int num3 = 0;
						List<string> list = new List<string>();
						for (int i = 0; i < num2; i++)
						{
							if (array4[i] == 0)
							{
								list.Add(Encoding.Unicode.GetString(array4, num3, i - num3).TrimEnd(new char[1]));
								num3 = i + 1;
							}
						}
						return list;
					}
					case 11:
						goto IL_17B;
					}
					return null;
					IL_17B:
					if (num2 <= 8)
					{
						byte[] array5 = new byte[num2];
						if (Class516.RegQueryValueEx(uintptr_0, string_0, null, ref num, array5, ref num2) != 0)
						{
							return null;
						}
						return BitConverter.ToInt64(array5, 0);
					}
					IL_1AB:
					byte[] array6 = new byte[num2];
					if (Class516.RegQueryValueEx(uintptr_0, string_0, null, ref num, array6, ref num2) != 0)
					{
						return null;
					}
					return array6;
				}
			}
			catch
			{
			}
			return null;
		}

		// Token: 0x06002703 RID: 9987 RVA: 0x000FCE90 File Offset: 0x000FB090
		internal static Enum33 smethod_0(UIntPtr uintptr_0)
		{
			try
			{
				if (Class516.RegCloseKey(uintptr_0) == 0)
				{
					return Enum33.const_2;
				}
			}
			catch
			{
			}
			return Enum33.const_0;
		}
	}
}
