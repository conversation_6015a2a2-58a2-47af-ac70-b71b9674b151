﻿using System;
using System.Drawing;
using System.Runtime.Serialization;
using ns28;

namespace TEx
{
	// Token: 0x020001DC RID: 476
	[Serializable]
	internal sealed class DrawRedArwRUp : DrawRedArwUp, ISerializable
	{
		// Token: 0x0600128B RID: 4747 RVA: 0x00006B34 File Offset: 0x00004D34
		public DrawRedArwRUp()
		{
		}

		// Token: 0x0600128C RID: 4748 RVA: 0x00007B18 File Offset: 0x00005D18
		public DrawRedArwRUp(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = "右上箭头";
		}

		// Token: 0x0600128D RID: 4749 RVA: 0x00006B58 File Offset: 0x00004D58
		protected DrawRedArwRUp(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x0600128E RID: 4750 RVA: 0x00006B69 File Offset: 0x00004D69
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x0600128F RID: 4751 RVA: 0x0007F2E0 File Offset: 0x0007D4E0
		protected override Image vmethod_24()
		{
			return Class372.RedArrow_RUp;
		}
	}
}
