﻿using System;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns24
{
	// Token: 0x020002EC RID: 748
	internal sealed class Class386 : ShapeCurve
	{
		// Token: 0x0600210F RID: 8463 RVA: 0x000E2A2C File Offset: 0x000E0C2C
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			Ind_HorLine lineItem_ = zedGraphControl_0.GraphPane.AddHorLine(base.IndData.Name, base.DataView, color_0, SymbolType.None);
			base.method_3(string_0, lineItem_);
		}

		// Token: 0x06002110 RID: 8464 RVA: 0x000E2A84 File Offset: 0x000E0C84
		protected override PointPair vmethod_0(int int_0, DataArray dataArray_1)
		{
			Class386.Class404 @class = new Class386.Class404();
			@class.dataArray_0 = dataArray_1;
			DateTime dateTime = base.method_0(int_0);
			if (@class.dataArray_0.Data.Length < int_0 + 1)
			{
				throw new Exception("数据长度溢出。");
			}
			double num = @class.dataArray_0.Data[int_0];
			if (@class.dataArray_0.OtherDataArrayList.Count != 1)
			{
				throw new Exception("ShapeHorLine数据不足，请检查。");
			}
			if (@class.dataArray_0.OtherDataArrayList.Any(new Func<DataArray, bool>(@class.method_0)))
			{
				throw new Exception("ShapeHorLine长度不相等。请检查。");
			}
			double y = @class.dataArray_0.Data[int_0];
			PointPair result;
			if ((int)@class.dataArray_0.OtherDataArrayList[0].Data[int_0] == 1)
			{
				result = new PointPair(new XDate(dateTime), y);
			}
			else
			{
				result = new PointPair(new XDate(dateTime), double.NaN);
			}
			return result;
		}

		// Token: 0x06002111 RID: 8465 RVA: 0x0000D56B File Offset: 0x0000B76B
		public Class386(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}

		// Token: 0x020002ED RID: 749
		[CompilerGenerated]
		private sealed class Class404
		{
			// Token: 0x06002113 RID: 8467 RVA: 0x000E2B74 File Offset: 0x000E0D74
			internal bool method_0(DataArray dataArray_1)
			{
				return dataArray_1.Data.Length != this.dataArray_0.Data.Length;
			}

			// Token: 0x0400102A RID: 4138
			public DataArray dataArray_0;
		}
	}
}
