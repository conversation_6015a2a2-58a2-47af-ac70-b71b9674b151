﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns12;
using TEx;

namespace ns30
{
	// Token: 0x02000203 RID: 515
	[DesignerCategory("Code")]
	internal class Class282 : DataGridView
	{
		// Token: 0x06001507 RID: 5383 RVA: 0x00008644 File Offset: 0x00006844
		public Class282(bool bool_2 = false)
		{
			this.ShowRowNumber = bool_2;
			this.vmethod_0();
			if (bool_2)
			{
				base.RowPostPaint += this.Class282_RowPostPaint;
			}
			Base.UI.ChartThemeChanged += this.method_0;
		}

		// Token: 0x06001508 RID: 5384 RVA: 0x0008A140 File Offset: 0x00088340
		protected virtual void vmethod_0()
		{
			base.SetStyle(ControlStyles.OptimizedDoubleBuffer, true);
			base.ReadOnly = true;
			base.MultiSelect = false;
			this.Dock = DockStyle.Fill;
			base.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
			base.DefaultCellStyle.WrapMode = DataGridViewTriState.False;
			base.EnableHeadersVisualStyles = false;
			base.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
			base.ColumnHeadersDefaultCellStyle.WrapMode = DataGridViewTriState.False;
			base.ColumnHeadersDefaultCellStyle.Padding = new Padding(0);
			base.AllowUserToOrderColumns = false;
			base.RowHeadersVisible = this.ShowRowNumber;
			base.AllowUserToAddRows = false;
			base.AllowUserToDeleteRows = false;
			base.AllowUserToResizeRows = false;
			this.vmethod_1();
			this.vmethod_2();
		}

		// Token: 0x06001509 RID: 5385 RVA: 0x0008A1E8 File Offset: 0x000883E8
		protected virtual void vmethod_1()
		{
			float emSize = TApp.smethod_4(8.5f, false);
			Font font = new Font("System", emSize, FontStyle.Regular);
			base.ColumnHeadersDefaultCellStyle.Font = font;
			base.DefaultCellStyle.Font = font;
		}

		// Token: 0x0600150A RID: 5386 RVA: 0x00008681 File Offset: 0x00006881
		private void method_0(object sender, EventArgs e)
		{
			base.SuspendLayout();
			this.smethod_0();
			this.vmethod_2();
			this.smethod_1();
			base.ResumeLayout();
		}

		// Token: 0x0600150B RID: 5387 RVA: 0x000041AE File Offset: 0x000023AE
		protected virtual void vmethod_2()
		{
		}

		// Token: 0x0600150C RID: 5388 RVA: 0x0008A228 File Offset: 0x00088428
		public void method_1(object object_0)
		{
			this.IsSettingDataSource = true;
			int num = -1;
			if (base.SelectedRows != null && base.SelectedRows.Count > 0)
			{
				num = base.SelectedRows[0].Index;
			}
			base.DataSource = null;
			base.DataSource = new BindingSource
			{
				DataSource = object_0
			};
			if (base.Columns.Count > 0)
			{
				this.vmethod_3();
			}
			if (num > 0 && base.Rows.Count > num)
			{
				base.Rows[num].Selected = true;
			}
			this.IsSettingDataSource = false;
		}

		// Token: 0x0600150D RID: 5389 RVA: 0x000041AE File Offset: 0x000023AE
		protected virtual void vmethod_3()
		{
		}

		// Token: 0x0600150E RID: 5390 RVA: 0x0008A2C4 File Offset: 0x000884C4
		protected void method_2(DataGridViewCell dataGridViewCell_0)
		{
			lock (dataGridViewCell_0)
			{
				if (dataGridViewCell_0.Value != null && dataGridViewCell_0.Value.ToString() != "-")
				{
					try
					{
						double num = Convert.ToDouble(dataGridViewCell_0.Value);
						if (double.IsNaN(num))
						{
							dataGridViewCell_0.Value = "";
						}
						else if (num > 0.0 & dataGridViewCell_0.Style.ForeColor != Color.Red)
						{
							dataGridViewCell_0.Style.ForeColor = Color.Red;
						}
						else if (num < 0.0 & dataGridViewCell_0.Style.ForeColor != Color.Green)
						{
							dataGridViewCell_0.Style.ForeColor = Color.Green;
						}
						else if (num == 0.0 & dataGridViewCell_0.Style.ForeColor != base.DefaultCellStyle.ForeColor)
						{
							dataGridViewCell_0.Style.ForeColor = base.DefaultCellStyle.ForeColor;
						}
					}
					catch
					{
					}
				}
			}
		}

		// Token: 0x0600150F RID: 5391 RVA: 0x0008A3FC File Offset: 0x000885FC
		public DataGridViewRow method_3()
		{
			DataGridViewRow result = null;
			if (base.SelectedRows.Count > 0)
			{
				result = base.SelectedRows[0];
			}
			else if (base.Rows.Count > 0)
			{
				result = base.Rows[0];
			}
			return result;
		}

		// Token: 0x06001510 RID: 5392 RVA: 0x0008A448 File Offset: 0x00088648
		public object method_4()
		{
			object result = null;
			DataGridViewSelectedRowCollection selectedRows = base.SelectedRows;
			if (selectedRows != null && selectedRows.Count > 0)
			{
				result = selectedRows[0].DataBoundItem;
			}
			return result;
		}

		// Token: 0x1700037F RID: 895
		// (get) Token: 0x06001511 RID: 5393 RVA: 0x0008A47C File Offset: 0x0008867C
		// (set) Token: 0x06001512 RID: 5394 RVA: 0x000086A3 File Offset: 0x000068A3
		public bool IsSettingDataSource { get; set; }

		// Token: 0x17000380 RID: 896
		// (get) Token: 0x06001513 RID: 5395 RVA: 0x0008A494 File Offset: 0x00088694
		// (set) Token: 0x06001514 RID: 5396 RVA: 0x000086AE File Offset: 0x000068AE
		public bool ShowRowNumber { get; set; }

		// Token: 0x06001515 RID: 5397 RVA: 0x0008A4AC File Offset: 0x000886AC
		[CompilerGenerated]
		private void Class282_RowPostPaint(object sender, DataGridViewRowPostPaintEventArgs e)
		{
			using (SolidBrush solidBrush = new SolidBrush(base.ColumnHeadersDefaultCellStyle.ForeColor))
			{
				e.Graphics.DrawString((e.RowIndex + 1).ToString(), e.InheritedRowStyle.Font, solidBrush, (float)(e.RowBounds.Location.X + 12), (float)(e.RowBounds.Location.Y + 4));
			}
		}

		// Token: 0x04000AE5 RID: 2789
		[CompilerGenerated]
		private bool bool_0;

		// Token: 0x04000AE6 RID: 2790
		[CompilerGenerated]
		private bool bool_1;
	}
}
