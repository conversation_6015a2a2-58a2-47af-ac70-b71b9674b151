﻿using System;
using ns14;
using ns30;
using ns31;
using ns9;
using TEx.Inds;
using TEx.SIndicator;

namespace ns22
{
	// Token: 0x02000317 RID: 791
	internal sealed class Class417 : Class412
	{
		// Token: 0x060021FE RID: 8702 RVA: 0x0000D8A3 File Offset: 0x0000BAA3
		public Class417(HToken htoken_1, Class408 class408_2, Class408 class408_3) : base(htoken_1, class408_2, class408_3)
		{
		}

		// Token: 0x060021FF RID: 8703 RVA: 0x000E8BB0 File Offset: 0x000E6DB0
		public static Class408 smethod_0(Tokenes tokenes_0)
		{
			if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_39)
			{
				throw new Exception(tokenes_0.Current.method_0("不是负号"));
			}
			tokenes_0.method_1();
			Class408 class408_ = Class410.smethod_0(tokenes_0);
			return new Class417(tokenes_0.Current, class408_, null);
		}

		// Token: 0x06002200 RID: 8704 RVA: 0x000E8C08 File Offset: 0x000E6E08
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			object obj = this.Left.vmethod_1(parserEnvironment_0);
			object result;
			if (obj.GetType() == typeof(double))
			{
				result = 0.0 - (double)obj;
			}
			else
			{
				if (obj.GetType() != typeof(DataArray))
				{
					throw new Exception(this.Token.method_0("负号没有找到要操作的对象。"));
				}
				result = 0.0 - (DataArray)obj;
			}
			return result;
		}
	}
}
