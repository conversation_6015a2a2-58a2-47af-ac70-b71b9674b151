﻿using System;
using System.Drawing;
using System.Runtime.CompilerServices;
using TEx;
using TEx.Chart;
using TEx.Comn;

namespace ns16
{
	// Token: 0x0200003E RID: 62
	internal sealed class Class20
	{
		// Token: 0x060001C2 RID: 450 RVA: 0x00017E18 File Offset: 0x00016018
		public Class20(ChartCS chartCS_1, DateTime dateTime_1, string string_3)
		{
			this.InfoDate = dateTime_1;
			this.InfoText = string_3;
			this.Chart = chartCS_1;
			DateTime? dateTime = this.Chart.HisDataPeriodSet.method_38(dateTime_1);
			if (dateTime != null)
			{
				this.ItemEndDT = new DateTime?(dateTime.Value);
				this.ItemPeriodType = chartCS_1.HisDataPeriodSet.PeriodType;
				this.ItemPeriodUnits = chartCS_1.HisDataPeriodSet.PeriodUnits;
				this.method_0(chartCS_1);
			}
		}

		// Token: 0x060001C3 RID: 451 RVA: 0x00017E98 File Offset: 0x00016098
		private void method_0(ChartCS chartCS_1 = null)
		{
			if (this.Chart != null && this.Chart.GraphPane != null)
			{
				if (chartCS_1 == null)
				{
					chartCS_1 = this.Chart;
				}
				TextObj textObj = null;
				Location location = this.method_1();
				if (location != null)
				{
					textObj = new TextObj();
					textObj.Text = "●";
					textObj.FontSpec.FontColor = Color.Magenta;
					textObj.FontSpec.Fill = new Fill(Color.Transparent);
					textObj.FontSpec.Size = 15f;
					textObj.FontSpec.Border.IsVisible = false;
					textObj.Location = location;
					textObj.IsClippedToChartRect = true;
					textObj.ZOrder = ZOrder.A_InFront;
					textObj.Tag = Class20.string_0 + "_" + this.InfoText;
				}
				this.Location = location;
				if (textObj != null)
				{
					this.InfoTextObj = textObj;
					this.Chart.GraphPane.GraphObjList.Add(this.InfoTextObj);
				}
			}
		}

		// Token: 0x060001C4 RID: 452 RVA: 0x00017F94 File Offset: 0x00016194
		private Location method_1()
		{
			Location result = null;
			double? num = this.method_2();
			if (num != null)
			{
				result = new Location(num.Value, 0.08, CoordType.XScaleYChartFraction, AlignH.Left, AlignV.Top);
			}
			return result;
		}

		// Token: 0x060001C5 RID: 453 RVA: 0x00017FD4 File Offset: 0x000161D4
		private double? method_2()
		{
			double? result = null;
			DateTime? itemEndDT = new DateTime?(this.InfoDate);
			if (itemEndDT != null && this.ItemPeriodType == this.Chart.HisDataPeriodSet.PeriodType)
			{
				int? itemPeriodUnits = this.ItemPeriodUnits;
				int? periodUnits = this.Chart.HisDataPeriodSet.PeriodUnits;
				if (itemPeriodUnits.GetValueOrDefault() == periodUnits.GetValueOrDefault() & itemPeriodUnits != null == (periodUnits != null))
				{
					goto IL_BB;
				}
			}
			itemEndDT = this.Chart.HisDataPeriodSet.method_38(this.InfoDate);
			this.ItemEndDT = itemEndDT;
			this.ItemPeriodType = this.Chart.HisDataPeriodSet.PeriodType;
			this.ItemPeriodUnits = this.Chart.HisDataPeriodSet.PeriodUnits;
			IL_BB:
			if (itemEndDT != null)
			{
				int? num = this.Chart.method_184(itemEndDT.Value);
				if (num != null && num.Value > 0)
				{
					result = new double?((double)(num.Value - 1) + 0.3);
				}
			}
			return result;
		}

		// Token: 0x060001C6 RID: 454 RVA: 0x000180F0 File Offset: 0x000162F0
		public void method_3()
		{
			if (this.InfoTextObj != null)
			{
				Location location = this.method_1();
				this.Location = location;
				if (location != null && this.InfoTextObj.Location != location)
				{
					this.InfoTextObj.Location = location;
					int num = this.Chart.ChtCtrl.IndexOfLastItemShown + 1;
					if (num > this.Chart.ChtCtrl.MaxSticksPerChart)
					{
						num = this.Chart.ChtCtrl.MaxSticksPerChart;
					}
					double num2 = Math.Round(location.X);
					if (num2 >= 0.0 && num2 <= (double)num)
					{
						this.InfoTextObj.IsVisible = true;
					}
					else
					{
						this.InfoTextObj.IsVisible = false;
					}
				}
			}
		}

		// Token: 0x060001C7 RID: 455 RVA: 0x000181A8 File Offset: 0x000163A8
		public static string smethod_0(string string_3)
		{
			if (!string.IsNullOrEmpty(string_3))
			{
				int num = string_3.IndexOf("_");
				if (num >= 0)
				{
					return string_3.Substring(num + 1);
				}
			}
			return null;
		}

		// Token: 0x060001C8 RID: 456 RVA: 0x000181E0 File Offset: 0x000163E0
		public void method_4()
		{
			if (this.Location != null)
			{
				if (this.NoteBox == null)
				{
					this.NoteBox = this.method_6();
					this.Chart.GraphPane.GraphObjList.Add(this.NoteBox);
				}
				else
				{
					this.NoteBox.Location = this.method_7();
					if (!this.Chart.GraphPane.GraphObjList.Contains(this.NoteBox))
					{
						this.Chart.GraphPane.GraphObjList.Add(this.NoteBox);
					}
				}
				this.bool_0 = true;
			}
		}

		// Token: 0x060001C9 RID: 457 RVA: 0x000031C6 File Offset: 0x000013C6
		public void method_5()
		{
			if (this.NoteBox != null)
			{
				this.Chart.GraphPane.GraphObjList.RemoveAll(new Predicate<GraphObj>(this.method_8));
			}
			this.bool_0 = false;
		}

		// Token: 0x060001CA RID: 458 RVA: 0x00018280 File Offset: 0x00016480
		private TextObj method_6()
		{
			TextObj textObj = new TextObj();
			textObj.Text = this.InfoText;
			if (!textObj.Text.EndsWith(Environment.NewLine + " "))
			{
				TextObj textObj2 = textObj;
				textObj2.Text = textObj2.Text + Environment.NewLine + " ";
			}
			textObj.FontSpec.Fill = new Fill(Color.LightYellow);
			textObj.ZOrder = ZOrder.A_InFront;
			textObj.FontSpec.StringAlignment = StringAlignment.Near;
			textObj.Tag = this.InfoTextObj.Tag + "_NBOX";
			textObj.Location = this.method_7();
			return textObj;
		}

		// Token: 0x060001CB RID: 459 RVA: 0x0001832C File Offset: 0x0001652C
		private Location method_7()
		{
			bool flag = true;
			if (this.Location != null)
			{
				flag = (this.Location.X < (double)this.Chart.MaxSticksPerChart / 1.5);
			}
			double num = flag ? (this.Location.X + 1.0) : this.Location.X;
			AlignH alignH = flag ? AlignH.Left : AlignH.Right;
			if (num > this.Chart.GraphPane.XAxis.Scale.Max)
			{
				num = this.Chart.GraphPane.XAxis.Scale.Max;
			}
			else if (num < this.Chart.GraphPane.XAxis.Scale.Min)
			{
				num = this.Chart.GraphPane.XAxis.Scale.Min;
			}
			return new Location(num, 0.2, CoordType.XScaleYChartFraction, alignH, AlignV.Center);
		}

		// Token: 0x17000078 RID: 120
		// (get) Token: 0x060001CC RID: 460 RVA: 0x00018420 File Offset: 0x00016620
		// (set) Token: 0x060001CD RID: 461 RVA: 0x000031FB File Offset: 0x000013FB
		public TextObj InfoTextObj
		{
			get
			{
				return this.textObj_0;
			}
			set
			{
				this.textObj_0 = value;
			}
		}

		// Token: 0x17000079 RID: 121
		// (get) Token: 0x060001CE RID: 462 RVA: 0x00018438 File Offset: 0x00016638
		// (set) Token: 0x060001CF RID: 463 RVA: 0x00003206 File Offset: 0x00001406
		public ChartCS Chart
		{
			get
			{
				return this.chartCS_0;
			}
			set
			{
				this.chartCS_0 = value;
			}
		}

		// Token: 0x1700007A RID: 122
		// (get) Token: 0x060001D0 RID: 464 RVA: 0x00018450 File Offset: 0x00016650
		// (set) Token: 0x060001D1 RID: 465 RVA: 0x00003211 File Offset: 0x00001411
		public DateTime InfoDate { get; set; }

		// Token: 0x1700007B RID: 123
		// (get) Token: 0x060001D2 RID: 466 RVA: 0x00018468 File Offset: 0x00016668
		// (set) Token: 0x060001D3 RID: 467 RVA: 0x0000321C File Offset: 0x0000141C
		public string InfoTitle { get; set; }

		// Token: 0x1700007C RID: 124
		// (get) Token: 0x060001D4 RID: 468 RVA: 0x00018480 File Offset: 0x00016680
		// (set) Token: 0x060001D5 RID: 469 RVA: 0x00003227 File Offset: 0x00001427
		public string InfoText { get; set; }

		// Token: 0x1700007D RID: 125
		// (get) Token: 0x060001D6 RID: 470 RVA: 0x00018498 File Offset: 0x00016698
		// (set) Token: 0x060001D7 RID: 471 RVA: 0x00003232 File Offset: 0x00001432
		public DateTime? ItemEndDT { get; private set; }

		// Token: 0x1700007E RID: 126
		// (get) Token: 0x060001D8 RID: 472 RVA: 0x000184B0 File Offset: 0x000166B0
		// (set) Token: 0x060001D9 RID: 473 RVA: 0x0000323D File Offset: 0x0000143D
		public PeriodType ItemPeriodType { get; private set; }

		// Token: 0x1700007F RID: 127
		// (get) Token: 0x060001DA RID: 474 RVA: 0x000184C8 File Offset: 0x000166C8
		// (set) Token: 0x060001DB RID: 475 RVA: 0x00003248 File Offset: 0x00001448
		public int? ItemPeriodUnits { get; private set; }

		// Token: 0x17000080 RID: 128
		// (get) Token: 0x060001DC RID: 476 RVA: 0x000184E0 File Offset: 0x000166E0
		// (set) Token: 0x060001DD RID: 477 RVA: 0x00003253 File Offset: 0x00001453
		public Location Location { get; set; }

		// Token: 0x17000081 RID: 129
		// (get) Token: 0x060001DE RID: 478 RVA: 0x000184F8 File Offset: 0x000166F8
		// (set) Token: 0x060001DF RID: 479 RVA: 0x0000325E File Offset: 0x0000145E
		public TextObj NoteBox { get; private set; }

		// Token: 0x17000082 RID: 130
		// (get) Token: 0x060001E0 RID: 480 RVA: 0x00018510 File Offset: 0x00016710
		// (set) Token: 0x060001E1 RID: 481 RVA: 0x00003269 File Offset: 0x00001469
		public bool ShowNoteBox
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				if (this.bool_0 != value)
				{
					if (value)
					{
						this.method_4();
					}
					else
					{
						this.method_5();
					}
					this.bool_0 = value;
				}
			}
		}

		// Token: 0x060001E3 RID: 483 RVA: 0x00018528 File Offset: 0x00016728
		[CompilerGenerated]
		private bool method_8(GraphObj graphObj_0)
		{
			bool result;
			if (graphObj_0 is TextObj)
			{
				result = (graphObj_0.Tag == this.NoteBox.Tag);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x040000B4 RID: 180
		public static readonly string string_0 = "INFOMINE";

		// Token: 0x040000B5 RID: 181
		private const double double_0 = 0.08;

		// Token: 0x040000B6 RID: 182
		private TextObj textObj_0;

		// Token: 0x040000B7 RID: 183
		private ChartCS chartCS_0;

		// Token: 0x040000B8 RID: 184
		[CompilerGenerated]
		private DateTime dateTime_0;

		// Token: 0x040000B9 RID: 185
		[CompilerGenerated]
		private string string_1;

		// Token: 0x040000BA RID: 186
		[CompilerGenerated]
		private string string_2;

		// Token: 0x040000BB RID: 187
		[CompilerGenerated]
		private DateTime? nullable_0;

		// Token: 0x040000BC RID: 188
		[CompilerGenerated]
		private PeriodType periodType_0;

		// Token: 0x040000BD RID: 189
		[CompilerGenerated]
		private int? nullable_1;

		// Token: 0x040000BE RID: 190
		[CompilerGenerated]
		private Location location_0;

		// Token: 0x040000BF RID: 191
		[CompilerGenerated]
		private TextObj textObj_1;

		// Token: 0x040000C0 RID: 192
		private bool bool_0;
	}
}
