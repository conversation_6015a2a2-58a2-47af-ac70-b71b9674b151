﻿using System;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

namespace ns22
{
	// Token: 0x02000390 RID: 912
	[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
	[DebuggerNonUserCode]
	[CompilerGenerated]
	internal sealed class Class488
	{
		// Token: 0x06002541 RID: 9537 RVA: 0x00002D25 File Offset: 0x00000F25
		internal Class488()
		{
		}

		// Token: 0x17000646 RID: 1606
		// (get) Token: 0x06002542 RID: 9538 RVA: 0x000F7ABC File Offset: 0x000F5CBC
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static ResourceManager ResourceManager
		{
			get
			{
				if (Class488.resourceManager_0 == null)
				{
					Class488.resourceManager_0 = new ResourceManager("ns22.Class488", typeof(Class488).Assembly);
				}
				return Class488.resourceManager_0;
			}
		}

		// Token: 0x17000647 RID: 1607
		// (get) Token: 0x06002543 RID: 9539 RVA: 0x000F7AF8 File Offset: 0x000F5CF8
		// (set) Token: 0x06002544 RID: 9540 RVA: 0x0000E5EB File Offset: 0x0000C7EB
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static CultureInfo Culture
		{
			get
			{
				return Class488.cultureInfo_0;
			}
			set
			{
				Class488.cultureInfo_0 = value;
			}
		}

		// Token: 0x17000648 RID: 1608
		// (get) Token: 0x06002545 RID: 9541 RVA: 0x000F7B10 File Offset: 0x000F5D10
		internal static string levenFeature
		{
			get
			{
				return Class488.ResourceManager.GetString("levenFeature", Class488.cultureInfo_0);
			}
		}

		// Token: 0x040011F4 RID: 4596
		private static ResourceManager resourceManager_0;

		// Token: 0x040011F5 RID: 4597
		private static CultureInfo cultureInfo_0;
	}
}
