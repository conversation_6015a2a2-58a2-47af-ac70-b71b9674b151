﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns11;
using ns12;
using ns14;
using ns17;
using ns18;
using ns23;
using ns28;
using ns29;
using ns3;
using ns33;
using ns6;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000223 RID: 547
	[Docking(DockingBehavior.AutoDock)]
	public sealed class SymbFilterPanel : UserControl
	{
		// Token: 0x14000082 RID: 130
		// (add) Token: 0x060016A2 RID: 5794 RVA: 0x00097AB8 File Offset: 0x00095CB8
		// (remove) Token: 0x060016A3 RID: 5795 RVA: 0x00097AF0 File Offset: 0x00095CF0
		public event MsgEventHandler MsgNotifyNeeded
		{
			[CompilerGenerated]
			add
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Combine(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Remove(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
		}

		// Token: 0x060016A4 RID: 5796 RVA: 0x000093B0 File Offset: 0x000075B0
		protected void method_0(string string_1)
		{
			MsgEventHandler msgEventHandler = this.msgEventHandler_0;
			if (msgEventHandler != null)
			{
				msgEventHandler(this, new MsgEventArgs(string_1, null));
			}
		}

		// Token: 0x060016A5 RID: 5797 RVA: 0x000093CD File Offset: 0x000075CD
		public SymbFilterPanel()
		{
			this.InitializeComponent();
		}

		// Token: 0x060016A6 RID: 5798 RVA: 0x00097B28 File Offset: 0x00095D28
		public void method_1()
		{
			this.method_5();
			if (!TApp.IsHighDpiScreen)
			{
				float emSize = TApp.smethod_4(9.3f, true);
				Font font = new Font("SimSun", emSize, FontStyle.Bold);
				this.superTabCtrl_AvblConds.SelectedTabFont = font;
				this.superTabCtrl_AvblConds.TabFont = font;
				emSize = TApp.smethod_4(9f, true);
				font = new Font("SimSun", emSize, FontStyle.Bold, GraphicsUnit.Point, 134);
				this.label_可选条件.Font = font;
				this.label_当前条件.Font = font;
				this.label_筛选结果.Font = font;
				font = new Font("SimSun", emSize);
				this.btn_ChangeDate.Font = font;
				this.btn_AddCond.Font = font;
				this.btn_Filter.Font = font;
				this.btn_SaveConds.Font = font;
				this.btn_LoadConds.Font = font;
			}
			this.method_8();
			Base.Data.CurrSymblChanging += this.method_6;
			Base.Data.CurrSymblChanged += this.method_7;
			Base.Data.DateSelectionChanged += this.method_11;
			Base.UI.ChartThemeChanged += this.method_3;
			this.btn_ChangeDate.Click += this.btn_ChangeDate_Click;
			this.cmbBox_Exchg.Items.AddRange(new object[]
			{
				"沪深A股",
				"沪深主板",
				"中小板",
				"创业板",
				"科创版"
			});
			this.cmbBox_Exchg.SelectedIndex = 0;
			List<string> list = TApp.SrvParams.UsrStkSymbols.Values.Where(new Func<StkSymbol, bool>(SymbFilterPanel.<>c.<>9.method_0)).Select(new Func<StkSymbol, string>(SymbFilterPanel.<>c.<>9.method_1)).Distinct<string>().ToList<string>();
			list.Insert(0, "全部行业");
			this.cmbBox_Index.DataSource = list;
			this.cmbBox_Index.SelectedIndex = 0;
			this.method_2();
			this.btn_AddCond.Click += this.btn_AddCond_Click;
			this.btn_Filter.Click += this.btn_Filter_Click;
			this.btn_SaveConds.Click += this.btn_SaveConds_Click;
			this.btn_LoadConds.Click += this.btn_LoadConds_Click;
			this.method_4();
			this.symbFilterApiWorker_0 = new SymbFilterApiWorker();
			this.symbFilterApiWorker_0.SendingRequest += this.method_16;
			this.symbFilterApiWorker_0.ResultReceived += this.symbFilterApiWorker_0_ResultReceived;
			this.symbFilterApiWorker_0.RequestError += this.symbFilterApiWorker_0_RequestError;
		}

		// Token: 0x060016A7 RID: 5799 RVA: 0x00097DEC File Offset: 0x00095FEC
		private void method_2()
		{
			SymbFilterPanel.dictionary_0 = new Dictionary<string, List<FilterCond>>();
			SymbFilterPanel.dictionary_0.Add("常用指标", new List<FilterCond>
			{
				new FilterCond("DailyBasic.pe", "市盈率", Enum20.const_0, ComparisonOpt.Less, null, null),
				new FilterCond("Income.n_income", "净利润", Enum20.const_2, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.grossprofit_margin", "销售毛利率", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.ar_turn", "应收账款周转率", Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.invturn_days", "存货周转天数", Enum20.const_0, ComparisonOpt.Less, null, null),
				new FilterCond("FnIndicator.roe", "净资产收益率", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("DailyBasic.turnover_rate_f", "换手率(流通股)", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("DailyBasic.circ_mv", "流通市值", Enum20.const_1, ComparisonOpt.Less, null, null)
			});
			SymbFilterPanel.dictionary_0.Add("盈利指标", new List<FilterCond>
			{
				new FilterCond("FnIndicator.grossprofit_margin", "销售毛利率", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.netprofit_margin", "销售净利率", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("Income.total_profit", "利润总额", Enum20.const_2, ComparisonOpt.Bigger, null, null),
				new FilterCond("Income.n_income", "净利润", Enum20.const_2, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.profit_dedt", "扣非后净利润", Enum20.const_2, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.profit_to_gr", "净利润/营业总收入", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.op_of_gr", "营业利润/营业总收入", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.op_to_ebt", "营业利润/利润总额", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.roe", "净资产收益率", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.assets_turn", "总资产周转率", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.roa", "总资产报酬率", Enum20.const_3, ComparisonOpt.Bigger, null, null)
			});
			SymbFilterPanel.dictionary_0.Add("成长指标", new List<FilterCond>
			{
				new FilterCond("FnIndicator.op_yoy", "营业利润同比增长率", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.basic_eps_yoy", "每股收益同比增长率", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.ebt_yoy", "利润总额同比增长率", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.q_profit_yoy", "净利润同比增长率(季度)", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.op_yoy", "营业利润同比增长率", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.ocf_yoy", "经营现金净流量同比增长率", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.bps_yoy", "每股净资产相对年初增长率", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.assets_yoy", "资产总计相对年初增长率", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.equity_yoy", "净资产同比增长率", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.rd_exp", "研发费用", Enum20.const_2, ComparisonOpt.Bigger, null, null)
			});
			SymbFilterPanel.dictionary_0.Add("经营指标", new List<FilterCond>
			{
				new FilterCond("BalanceSheet.accounts_receiv", "应收账款", Enum20.const_2, ComparisonOpt.Bigger, null, null),
				new FilterCond("BalanceSheet.oth_receiv", "其他应收款", Enum20.const_2, ComparisonOpt.Bigger, null, null),
				new FilterCond("BalanceSheet.adv_receipts", "预收款项", Enum20.const_2, ComparisonOpt.Bigger, null, null),
				new FilterCond("BalanceSheet.prepayment", "预付款项", Enum20.const_2, ComparisonOpt.Bigger, null, null),
				new FilterCond("Income.revenue", "营业收入", Enum20.const_2, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.current_ratio", "流动比率", Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.quick_ratio", "速动比率", Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.cash_ratio", "保守速动比率", Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.invturn_days", "存货周转天数", Enum20.const_0, ComparisonOpt.Less, null, null),
				new FilterCond("FnIndicator.inv_turn", "存货周转率", Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.arturn_days", "应收账款周转天数", Enum20.const_0, ComparisonOpt.Less, null, null),
				new FilterCond("FnIndicator.ar_turn", "应收账款周转率", Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.ca_turn", "流动资产周转率", Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.fa_turn", "固定资产周转率", Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.assets_turn", "总资产周转率", Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.saleexp_to_gr", "销售费用/营业总收入", Enum20.const_3, ComparisonOpt.Less, null, null),
				new FilterCond("FnIndicator.adminexp_of_gr", "管理费用/营业总收入", Enum20.const_3, ComparisonOpt.Less, null, null),
				new FilterCond("FnIndicator.finaexp_of_gr", "财务费用/营业总收入", Enum20.const_3, ComparisonOpt.Less, null, null),
				new FilterCond("FnIndicator.debt_to_assets", "资产负债率", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.tax_to_ebt", "所得税/利润总额", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.ca_to_assets", "流动资产/总资产", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("FnIndicator.tbassets_to_totalassets", "有形资产/总资产", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("Income.invest_income", "投资净收益", Enum20.const_2, ComparisonOpt.Bigger, null, null)
			});
			SymbFilterPanel.dictionary_0.Add("估价指标", new List<FilterCond>
			{
				new FilterCond("DailyBasic.pb", "市净率", Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond("DailyBasic.pe", "市盈率", Enum20.const_0, ComparisonOpt.Less, null, null),
				new FilterCond("DailyBasic.pe_ttm", "动态市盈率", Enum20.const_0, ComparisonOpt.Less, null, null),
				new FilterCond("DailyBasic.ps", "市销率", Enum20.const_0, ComparisonOpt.Less, null, null),
				new FilterCond("DailyBasic.ps_ttm", "动态市销率", Enum20.const_0, ComparisonOpt.Less, null, null),
				new FilterCond("DailyBasic.dv_ratio", "股息率", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("DailyBasic.dv_ttm", "动态股息率", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("DailyBasic.total_share", "总股本", Enum20.const_1, ComparisonOpt.Less, null, null),
				new FilterCond("DailyBasic.float_share", "流通股本", Enum20.const_1, ComparisonOpt.Less, null, null),
				new FilterCond("DailyBasic.total_mv", "总市值", Enum20.const_1, ComparisonOpt.Less, null, null),
				new FilterCond("DailyBasic.circ_mv", "流通市值", Enum20.const_1, ComparisonOpt.Less, null, null)
			});
			SymbFilterPanel.dictionary_0.Add("价量动能", new List<FilterCond>
			{
				new FilterCond("DailyBasic.close", "收盘价格", Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond("DailyBasic.turnover_rate", "换手率", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("DailyBasic.turnover_rate_f", "换手率(流通股)", Enum20.const_3, ComparisonOpt.Bigger, null, null),
				new FilterCond("DailyBasic.volume_ratio", "量比", Enum20.const_0, ComparisonOpt.Bigger, null, null),
				new FilterCond("DailyBasic.pe", "市盈率", Enum20.const_0, ComparisonOpt.Less, null, null)
			});
			this.superTabCtrl_AvblConds.SuspendLayout();
			List<string> list = SymbFilterPanel.dictionary_0.Keys.ToList<string>();
			for (int i = 0; i < list.Count; i++)
			{
				string text = list[i];
				SuperTabItem superTabItem = new SuperTabItem();
				SuperTabControlPanel superTabControlPanel = new SuperTabControlPanel();
				this.superTabCtrl_AvblConds.Controls.Add(superTabControlPanel);
				this.superTabCtrl_AvblConds.Tabs.Add(superTabItem);
				superTabControlPanel.Dock = DockStyle.Fill;
				superTabControlPanel.TabItem = superTabItem;
				Panel panel = new Panel();
				panel.Dock = DockStyle.Fill;
				panel.Margin = new System.Windows.Forms.Padding(0);
				panel.Padding = new System.Windows.Forms.Padding(3, 6, 3, 0);
				superTabControlPanel.Controls.Add(panel);
				superTabItem.Text = text;
				superTabItem.GlobalItem = false;
				superTabItem.Margin = new DevComponents.DotNetBar.Padding(0, 0, 6, 0);
				superTabItem.AttachedControl = superTabControlPanel;
			}
			float emSize = 9f;
			if (!TApp.IsHighDpiScreen)
			{
				emSize = (float)(11.25 / TApp.DpiScale);
			}
			this.class289_0 = new Class289();
			this.class289_0.BorderStyle = BorderStyle.None;
			this.class289_0.DrawMode = DrawMode.OwnerDrawFixed;
			this.class289_0.Font = new Font("SimSun", emSize, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.class289_0.FormattingEnabled = true;
			this.class289_0.ItemHeight = 28;
			this.class289_0.SelectionAddtionalString = null;
			this.class289_0.SelectionBackColor = null;
			this.class289_0.Dock = DockStyle.Fill;
			this.class289_0.MouseDoubleClick += this.class289_0_MouseDoubleClick;
			this.method_21();
			this.superTabCtrl_AvblConds.SelectedTabChanged += this.method_20;
			this.superTabCtrl_AvblConds.ResumeLayout();
			List<FilterCond> list2 = null;
			Class300 @class = Class300.smethod_1();
			if (@class != null)
			{
				if (@class.LastConds != null && @class.LastConds.Any<FilterCond>())
				{
					list2 = @class.LastConds;
				}
				else
				{
					list2 = @class.method_0();
				}
				if (!string.IsNullOrEmpty(@class.CurrGrpName))
				{
					this.CurrUserCfgCondGrpName = @class.CurrGrpName;
				}
			}
			if (list2 == null)
			{
				list2 = SymbFilterPanel.dictionary_0.Values.First<List<FilterCond>>();
			}
			this.filterCondsPanel.method_1(list2, default(System.Windows.Forms.Padding));
			string csv;
			if (@class != null && !string.IsNullOrEmpty(@class.LastRsltCsv))
			{
				csv = @class.LastRsltCsv;
			}
			else
			{
				string[] value = list2.Select(new Func<FilterCond, string>(SymbFilterPanel.<>c.<>9.method_2)).ToArray<string>();
				csv = "代码,名称," + string.Join(",", value);
			}
			try
			{
				DataTable dataTableFromCsv = Utility.GetDataTableFromCsv(csv, new int[]
				{
					0,
					1
				});
				this.method_17(dataTableFromCsv);
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x060016A8 RID: 5800 RVA: 0x000093DD File Offset: 0x000075DD
		private void method_3(object sender, EventArgs e)
		{
			this.method_4();
		}

		// Token: 0x060016A9 RID: 5801 RVA: 0x00098A4C File Offset: 0x00096C4C
		private void method_4()
		{
			this.smethod_0();
			this.btn_AddCond.ForeColor = Color.Black;
			this.btn_SaveConds.ForeColor = Color.Black;
			this.btn_LoadConds.ForeColor = Color.Black;
			this.btn_Filter.ForeColor = Color.Black;
			this.btn_ChangeDate.ForeColor = Color.Black;
			Color backColor;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				backColor = Class179.color_3;
			}
			else
			{
				backColor = Class179.color_9;
			}
			Color foreColor = Base.UI.smethod_35();
			this.tablePanel_body.BackColor = backColor;
			this.tablePanel_header.BackColor = Base.UI.smethod_34();
			this.tablePanel_bg.BackColor = backColor;
			this.tablePanel_AvlbCondsHead.BackColor = backColor;
			this.tablePanel_CondClassHead.BackColor = backColor;
			this.label_日期.ForeColor = foreColor;
			this.label_CurrDate.ForeColor = foreColor;
			this.label_交易所.ForeColor = foreColor;
			this.label_板块.ForeColor = foreColor;
			this.label_可选条件.ForeColor = foreColor;
			this.label_当前条件.ForeColor = foreColor;
			this.label_筛选结果.ForeColor = foreColor;
			this.panel_AvblConds.BackColor = backColor;
			this.panel_RsltDgv.BackColor = backColor;
			eSuperTabStyle tabStyle = Base.UI.smethod_71();
			this.superTabCtrl_AvblConds.TabStyle = tabStyle;
			foreach (object obj in this.superTabCtrl_AvblConds.Controls)
			{
				if (obj is SuperTabControlPanel)
				{
					((obj as SuperTabControlPanel).Controls[0] as Panel).BackColor = backColor;
				}
			}
			this.class289_0.method_0();
			this.filterCondsPanel.method_0();
			this.smethod_1();
		}

		// Token: 0x060016AA RID: 5802 RVA: 0x00098C1C File Offset: 0x00096E1C
		public void method_5()
		{
			string text = string.Empty;
			DateTime? dateTime = Base.Data.smethod_54();
			if (dateTime != null)
			{
				text = dateTime.Value.ToString("yyyy-MM-dd");
			}
			if (!string.IsNullOrEmpty(text))
			{
				this.label_CurrDate.Text = text;
			}
			else
			{
				this.label_CurrDate.Text = "";
			}
		}

		// Token: 0x060016AB RID: 5803 RVA: 0x000093E7 File Offset: 0x000075E7
		private void method_6(EventArgs1 eventArgs1_0)
		{
			this.method_9();
		}

		// Token: 0x060016AC RID: 5804 RVA: 0x000093F1 File Offset: 0x000075F1
		private void method_7(EventArgs1 eventArgs1_0)
		{
			this.method_8();
		}

		// Token: 0x060016AD RID: 5805 RVA: 0x000093FB File Offset: 0x000075FB
		private void method_8()
		{
			if (Base.Data.CurrSymbDataSet != null)
			{
				Base.Data.CurrSymbDataSet.CurrDateChanged += this.method_10;
			}
		}

		// Token: 0x060016AE RID: 5806 RVA: 0x0000941C File Offset: 0x0000761C
		private void method_9()
		{
			if (Base.Data.CurrSymbDataSet != null)
			{
				Base.Data.CurrSymbDataSet.CurrDateChanged -= this.method_10;
			}
		}

		// Token: 0x060016AF RID: 5807 RVA: 0x0000943D File Offset: 0x0000763D
		private void method_10(object sender, EventArgs e)
		{
			this.method_5();
		}

		// Token: 0x060016B0 RID: 5808 RVA: 0x0000943D File Offset: 0x0000763D
		private void method_11(object sender, EventArgs e)
		{
			this.method_5();
		}

		// Token: 0x060016B1 RID: 5809 RVA: 0x00008F3A File Offset: 0x0000713A
		private void btn_ChangeDate_Click(object sender, EventArgs e)
		{
			Base.UI.MainForm.method_81();
		}

		// Token: 0x060016B2 RID: 5810 RVA: 0x00098C7C File Offset: 0x00096E7C
		private void method_12(List<FilterCond> list_0)
		{
			foreach (FilterCond filterCond in list_0)
			{
				this.class289_0.Items.Add(new TEx.Util.ComboBoxItem(filterCond.Name, filterCond));
			}
			this.class289_0.SelectedIndex = 0;
		}

		// Token: 0x060016B3 RID: 5811 RVA: 0x00098CF0 File Offset: 0x00096EF0
		private void btn_AddCond_Click(object sender, EventArgs e)
		{
			SymbFilterPanel.Class298 @class = new SymbFilterPanel.Class298();
			@class.filterCond_0 = this.method_19();
			if (@class.filterCond_0 != null)
			{
				if (this.CondItemList.Exists(new Predicate<FilterCondItem>(@class.method_0)))
				{
					MessageBox.Show("该条件已添加！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
				}
				else
				{
					this.filterCondsPanel.method_2(@class.filterCond_0);
				}
			}
		}

		// Token: 0x060016B4 RID: 5812 RVA: 0x00098D58 File Offset: 0x00096F58
		private void btn_Filter_Click(object sender, EventArgs e)
		{
			List<FilterCond> list = this.filterCondsPanel.method_3();
			if (list.Count > 0)
			{
				List<FilterCond> list2 = list.Where(new Func<FilterCond, bool>(SymbFilterPanel.<>c.<>9.method_3)).ToList<FilterCond>();
				if (list2.Count > 0)
				{
					this.method_15(list2);
				}
				else
				{
					MessageBox.Show("无有效条件，请输入相应条件值。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
				}
			}
		}

		// Token: 0x060016B5 RID: 5813 RVA: 0x00098DCC File Offset: 0x00096FCC
		private string method_13()
		{
			string result = string.Empty;
			int selectedIndex = this.cmbBox_Exchg.SelectedIndex;
			if (selectedIndex > 0)
			{
				if (selectedIndex == 1)
				{
					result = "ZB";
				}
				else if (selectedIndex == 2)
				{
					result = "ZXB";
				}
				else if (selectedIndex == 3)
				{
					result = "CYB";
				}
				else if (selectedIndex == 4)
				{
					result = "KCB";
				}
			}
			return result;
		}

		// Token: 0x060016B6 RID: 5814 RVA: 0x00098E24 File Offset: 0x00097024
		private string method_14()
		{
			string result = string.Empty;
			List<string> list = this.cmbBox_Index.DataSource as List<string>;
			int selectedIndex = this.cmbBox_Index.SelectedIndex;
			if (selectedIndex > 0)
			{
				result = list[selectedIndex];
			}
			return result;
		}

		// Token: 0x060016B7 RID: 5815 RVA: 0x00098E68 File Offset: 0x00097068
		private void method_15(List<FilterCond> list_0)
		{
			new Dictionary<string, object>();
			DateTime? dateTime = Base.Data.smethod_54();
			if (dateTime != null)
			{
				string string_ = this.method_13();
				string string_2 = this.method_14();
				this.symbFilterApiWorker_0.method_5(list_0, dateTime.Value, string_, string_2);
			}
		}

		// Token: 0x060016B8 RID: 5816 RVA: 0x00009447 File Offset: 0x00007647
		private void method_16(object sender, EventArgs e)
		{
			this.label_筛选结果.Text = "筛选结果(查询中...)";
		}

		// Token: 0x060016B9 RID: 5817 RVA: 0x00098EB0 File Offset: 0x000970B0
		private void symbFilterApiWorker_0_ResultReceived(object sender, WebApiEventArgs e)
		{
			if (e.ApiResult != null)
			{
				Dictionary<string, object> requestDict = e.RequestDict;
				if (requestDict.ContainsKey("condCsv") && requestDict.ContainsKey("dataTbl"))
				{
					DataTable dataTable = requestDict["dataTbl"] as DataTable;
					this.method_17(dataTable);
					string str = dataTable.Rows.Count.ToString();
					if (dataTable.Rows.Count == 300)
					{
						str = "前300";
					}
					this.label_筛选结果.Text = "筛选结果(" + str + ")";
				}
			}
		}

		// Token: 0x060016BA RID: 5818 RVA: 0x00098F4C File Offset: 0x0009714C
		private void method_17(DataTable dataTable_0)
		{
			this.panel_RsltDgv.SuspendLayout();
			Class284 @class = this.method_18();
			if (@class == null)
			{
				@class = new Class284();
				this.panel_RsltDgv.Controls.Clear();
				this.panel_RsltDgv.Controls.Add(@class);
			}
			@class.method_1(dataTable_0);
			this.panel_RsltDgv.ResumeLayout();
		}

		// Token: 0x060016BB RID: 5819 RVA: 0x00098FAC File Offset: 0x000971AC
		private Class284 method_18()
		{
			Class284 result = null;
			if (this.panel_RsltDgv.Controls.Count > 0 && this.panel_RsltDgv.Controls[0] is Class284)
			{
				result = (this.panel_RsltDgv.Controls[0] as Class284);
			}
			return result;
		}

		// Token: 0x060016BC RID: 5820 RVA: 0x0000945B File Offset: 0x0000765B
		private void symbFilterApiWorker_0_RequestError(object sender, ErrorEventArgs e)
		{
			Class182.smethod_0(e.Exception);
			this.label_筛选结果.Text = "筛选结果";
		}

		// Token: 0x060016BD RID: 5821 RVA: 0x00099004 File Offset: 0x00097204
		private FilterCond method_19()
		{
			return (this.class289_0.SelectedItem as TEx.Util.ComboBoxItem).Value as FilterCond;
		}

		// Token: 0x060016BE RID: 5822 RVA: 0x0000947A File Offset: 0x0000767A
		private void method_20(object sender, SuperTabStripSelectedTabChangedEventArgs e)
		{
			this.method_21();
		}

		// Token: 0x060016BF RID: 5823 RVA: 0x00099030 File Offset: 0x00097230
		private void method_21()
		{
			SuperTabItem selectedTab = this.superTabCtrl_AvblConds.SelectedTab;
			Panel panel = (selectedTab.AttachedControl as SuperTabControlPanel).Controls[0] as Panel;
			panel.SuspendLayout();
			this.class289_0.Items.Clear();
			if (this.class289_0.Parent != null)
			{
				this.class289_0.Parent.Controls.Remove(this.class289_0);
			}
			List<FilterCond> list_ = SymbFilterPanel.dictionary_0[selectedTab.Text];
			this.method_12(list_);
			panel.Controls.Add(this.class289_0);
			panel.ResumeLayout();
		}

		// Token: 0x060016C0 RID: 5824 RVA: 0x000990D4 File Offset: 0x000972D4
		private void class289_0_MouseDoubleClick(object sender, MouseEventArgs e)
		{
			int num = this.class289_0.IndexFromPoint(e.Location);
			if (num != -1)
			{
				SymbFilterPanel.Class299 @class = new SymbFilterPanel.Class299();
				TEx.Util.ComboBoxItem comboBoxItem = this.class289_0.Items[num] as TEx.Util.ComboBoxItem;
				@class.filterCond_0 = (comboBoxItem.Value as FilterCond);
				if (@class.filterCond_0 != null && !this.CondItemList.Exists(new Predicate<FilterCondItem>(@class.method_0)))
				{
					this.filterCondsPanel.method_2(@class.filterCond_0);
				}
			}
		}

		// Token: 0x060016C1 RID: 5825 RVA: 0x0009915C File Offset: 0x0009735C
		private void btn_SaveConds_Click(object sender, EventArgs e)
		{
			FilterCondsSaveForm filterCondsSaveForm = new FilterCondsSaveForm(this.filterCondsPanel.method_3(), this.CurrUserCfgCondGrpName);
			filterCondsSaveForm.CondGroupSaved += this.method_22;
			filterCondsSaveForm.FormClosed += this.method_23;
			filterCondsSaveForm.ShowDialog();
		}

		// Token: 0x060016C2 RID: 5826 RVA: 0x00009484 File Offset: 0x00007684
		private void method_22(object sender, MsgEventArgs e)
		{
			this.CurrUserCfgCondGrpName = e.Msg;
			this.method_0("用户条件组已保存。");
		}

		// Token: 0x060016C3 RID: 5827 RVA: 0x0000949F File Offset: 0x0000769F
		private void method_23(object sender, FormClosedEventArgs e)
		{
			FilterCondsSaveForm filterCondsSaveForm = sender as FilterCondsSaveForm;
			filterCondsSaveForm.CondGroupSaved -= this.method_22;
			filterCondsSaveForm.FormClosed -= this.method_23;
		}

		// Token: 0x060016C4 RID: 5828 RVA: 0x000991AC File Offset: 0x000973AC
		private void btn_LoadConds_Click(object sender, EventArgs e)
		{
			Class300 @class = Class300.smethod_1();
			if (@class != null)
			{
				FilterCondsLoadForm filterCondsLoadForm = new FilterCondsLoadForm(@class);
				filterCondsLoadForm.CondGroupSelected += this.method_24;
				filterCondsLoadForm.FormClosed += this.method_25;
				filterCondsLoadForm.ShowDialog();
			}
			else
			{
				MessageBox.Show("无本地已保存用户条件组。", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Asterisk);
			}
		}

		// Token: 0x060016C5 RID: 5829 RVA: 0x0009920C File Offset: 0x0009740C
		private void method_24(object sender, MsgEventArgs e)
		{
			string msg = e.Msg;
			List<FilterCond> list_ = e.Data as List<FilterCond>;
			this.CurrUserCfgCondGrpName = msg;
			this.filterCondsPanel.method_1(list_, default(System.Windows.Forms.Padding));
			this.method_0("用户条件组已加载。");
		}

		// Token: 0x060016C6 RID: 5830 RVA: 0x000094CC File Offset: 0x000076CC
		private void method_25(object sender, FormClosedEventArgs e)
		{
			FilterCondsLoadForm filterCondsLoadForm = sender as FilterCondsLoadForm;
			filterCondsLoadForm.CondGroupSelected -= this.method_24;
			filterCondsLoadForm.FormClosed -= this.method_25;
		}

		// Token: 0x060016C7 RID: 5831 RVA: 0x00099258 File Offset: 0x00097458
		public void method_26()
		{
			Class300 @class = Class300.smethod_1();
			if (@class == null)
			{
				@class = new Class300();
			}
			@class.LastConds = this.filterCondsPanel.method_3();
			Class284 class2 = this.method_18();
			if (class2 != null)
			{
				DataTable dataTable = class2.DataSource as DataTable;
				if (dataTable != null)
				{
					string csvFromDataTable = Utility.GetCsvFromDataTable(dataTable);
					@class.LastRsltCsv = csvFromDataTable;
				}
			}
			if (@class.LastConds.Count > 0 && !string.IsNullOrEmpty(@class.LastRsltCsv))
			{
				@class.method_1();
			}
		}

		// Token: 0x170003AE RID: 942
		// (get) Token: 0x060016C8 RID: 5832 RVA: 0x000992D0 File Offset: 0x000974D0
		public List<FilterCondItem> CondItemList
		{
			get
			{
				List<FilterCondItem> list = new List<FilterCondItem>();
				foreach (object obj in this.filterCondsPanel.Controls)
				{
					FilterCondItem item = obj as FilterCondItem;
					list.Add(item);
				}
				return list;
			}
		}

		// Token: 0x170003AF RID: 943
		// (get) Token: 0x060016C9 RID: 5833 RVA: 0x0009933C File Offset: 0x0009753C
		public bool IsInInputState
		{
			get
			{
				return this.CondItemList.Exists(new Predicate<FilterCondItem>(SymbFilterPanel.<>c.<>9.method_4));
			}
		}

		// Token: 0x170003B0 RID: 944
		// (get) Token: 0x060016CA RID: 5834 RVA: 0x00099378 File Offset: 0x00097578
		public Button AcceptButton
		{
			get
			{
				return this.btn_Filter;
			}
		}

		// Token: 0x170003B1 RID: 945
		// (get) Token: 0x060016CB RID: 5835 RVA: 0x00099390 File Offset: 0x00097590
		// (set) Token: 0x060016CC RID: 5836 RVA: 0x000094F9 File Offset: 0x000076F9
		public string CurrUserCfgCondGrpName { get; set; }

		// Token: 0x170003B2 RID: 946
		// (get) Token: 0x060016CD RID: 5837 RVA: 0x000993A8 File Offset: 0x000975A8
		public static Dictionary<string, List<FilterCond>> CondDict
		{
			get
			{
				return SymbFilterPanel.dictionary_0;
			}
		}

		// Token: 0x060016CE RID: 5838 RVA: 0x00009504 File Offset: 0x00007704
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060016CF RID: 5839 RVA: 0x000993C0 File Offset: 0x000975C0
		private void InitializeComponent()
		{
			this.tablePanel_bg = new TableLayoutPanel();
			this.tablePanel_header = new TableLayoutPanel();
			this.label_交易所 = new Label();
			this.label_CurrDate = new Label();
			this.cmbBox_Exchg = new ComboBox();
			this.label_板块 = new Label();
			this.cmbBox_Index = new ComboBox();
			this.label_日期 = new Label();
			this.btn_ChangeDate = new Button();
			this.tablePanel_body = new TableLayoutPanel();
			this.label_筛选结果 = new Label();
			this.tablePanel_CondClassHead = new TableLayoutPanel();
			this.btn_Filter = new Button();
			this.btn_LoadConds = new Button();
			this.btn_SaveConds = new Button();
			this.label_当前条件 = new Label();
			this.tablePanel_AvlbCondsHead = new TableLayoutPanel();
			this.btn_AddCond = new Button();
			this.label_可选条件 = new Label();
			this.filterCondsPanel = new Class294();
			this.panel_RsltDgv = new Class296();
			this.panel_AvblConds = new Class296();
			this.superTabCtrl_AvblConds = new SuperTabControl();
			this.tablePanel_bg.SuspendLayout();
			this.tablePanel_header.SuspendLayout();
			this.tablePanel_body.SuspendLayout();
			this.tablePanel_CondClassHead.SuspendLayout();
			this.tablePanel_AvlbCondsHead.SuspendLayout();
			this.panel_AvblConds.SuspendLayout();
			((ISupportInitialize)this.superTabCtrl_AvblConds).BeginInit();
			base.SuspendLayout();
			this.tablePanel_bg.ColumnCount = 1;
			this.tablePanel_bg.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
			this.tablePanel_bg.Controls.Add(this.tablePanel_header, 0, 0);
			this.tablePanel_bg.Controls.Add(this.tablePanel_body, 0, 1);
			this.tablePanel_bg.Dock = DockStyle.Fill;
			this.tablePanel_bg.Location = new Point(0, 0);
			this.tablePanel_bg.Margin = new System.Windows.Forms.Padding(0);
			this.tablePanel_bg.Name = "tablePanel_bg";
			this.tablePanel_bg.RowCount = 2;
			this.tablePanel_bg.RowStyles.Add(new RowStyle(SizeType.Absolute, 32f));
			this.tablePanel_bg.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tablePanel_bg.Size = new Size(1450, 362);
			this.tablePanel_bg.TabIndex = 0;
			this.tablePanel_header.ColumnCount = 8;
			this.tablePanel_header.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80f));
			this.tablePanel_header.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 160f));
			this.tablePanel_header.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80f));
			this.tablePanel_header.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 160f));
			this.tablePanel_header.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80f));
			this.tablePanel_header.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100f));
			this.tablePanel_header.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120f));
			this.tablePanel_header.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
			this.tablePanel_header.Controls.Add(this.label_交易所, 0, 0);
			this.tablePanel_header.Controls.Add(this.label_CurrDate, 5, 0);
			this.tablePanel_header.Controls.Add(this.cmbBox_Exchg, 1, 0);
			this.tablePanel_header.Controls.Add(this.label_板块, 2, 0);
			this.tablePanel_header.Controls.Add(this.cmbBox_Index, 3, 0);
			this.tablePanel_header.Controls.Add(this.label_日期, 4, 0);
			this.tablePanel_header.Controls.Add(this.btn_ChangeDate, 6, 0);
			this.tablePanel_header.Dock = DockStyle.Fill;
			this.tablePanel_header.Location = new Point(0, 0);
			this.tablePanel_header.Margin = new System.Windows.Forms.Padding(0);
			this.tablePanel_header.Name = "tablePanel_header";
			this.tablePanel_header.RowCount = 1;
			this.tablePanel_header.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tablePanel_header.Size = new Size(1450, 32);
			this.tablePanel_header.TabIndex = 1;
			this.label_交易所.Anchor = AnchorStyles.Right;
			this.label_交易所.AutoSize = true;
			this.label_交易所.BackColor = Color.Transparent;
			this.label_交易所.Location = new Point(17, 8);
			this.label_交易所.Name = "label_交易所";
			this.label_交易所.Size = new Size(60, 15);
			this.label_交易所.TabIndex = 3;
			this.label_交易所.Text = "交易所:";
			this.label_CurrDate.Anchor = AnchorStyles.Left;
			this.label_CurrDate.AutoSize = true;
			this.label_CurrDate.BackColor = Color.Transparent;
			this.label_CurrDate.Location = new Point(563, 8);
			this.label_CurrDate.Name = "label_CurrDate";
			this.label_CurrDate.Size = new Size(87, 15);
			this.label_CurrDate.TabIndex = 1;
			this.label_CurrDate.Text = "2005-06-30";
			this.cmbBox_Exchg.Anchor = (AnchorStyles.Left | AnchorStyles.Right);
			this.cmbBox_Exchg.DropDownStyle = ComboBoxStyle.DropDownList;
			this.cmbBox_Exchg.FormattingEnabled = true;
			this.cmbBox_Exchg.Location = new Point(80, 4);
			this.cmbBox_Exchg.Margin = new System.Windows.Forms.Padding(0);
			this.cmbBox_Exchg.Name = "cmbBox_Exchg";
			this.cmbBox_Exchg.Size = new Size(160, 23);
			this.cmbBox_Exchg.TabIndex = 6;
			this.label_板块.Anchor = AnchorStyles.Right;
			this.label_板块.AutoSize = true;
			this.label_板块.BackColor = Color.Transparent;
			this.label_板块.Location = new Point(272, 8);
			this.label_板块.Name = "label_板块";
			this.label_板块.Size = new Size(45, 15);
			this.label_板块.TabIndex = 5;
			this.label_板块.Text = "板块:";
			this.cmbBox_Index.Anchor = (AnchorStyles.Left | AnchorStyles.Right);
			this.cmbBox_Index.DropDownStyle = ComboBoxStyle.DropDownList;
			this.cmbBox_Index.FormattingEnabled = true;
			this.cmbBox_Index.Location = new Point(320, 4);
			this.cmbBox_Index.Margin = new System.Windows.Forms.Padding(0);
			this.cmbBox_Index.Name = "cmbBox_Index";
			this.cmbBox_Index.Size = new Size(160, 23);
			this.cmbBox_Index.TabIndex = 7;
			this.label_日期.Anchor = AnchorStyles.Right;
			this.label_日期.AutoSize = true;
			this.label_日期.BackColor = Color.Transparent;
			this.label_日期.Location = new Point(512, 8);
			this.label_日期.Name = "label_日期";
			this.label_日期.Size = new Size(45, 15);
			this.label_日期.TabIndex = 7;
			this.label_日期.Text = "日期:";
			this.btn_ChangeDate.Anchor = AnchorStyles.Left;
			this.btn_ChangeDate.BackColor = Color.Transparent;
			this.btn_ChangeDate.Location = new Point(662, 2);
			this.btn_ChangeDate.Margin = new System.Windows.Forms.Padding(2);
			this.btn_ChangeDate.MinimumSize = new Size(80, 25);
			this.btn_ChangeDate.Name = "btn_ChangeDate";
			this.btn_ChangeDate.Size = new Size(87, 27);
			this.btn_ChangeDate.TabIndex = 4;
			this.btn_ChangeDate.Text = "切换日期";
			this.btn_ChangeDate.TextImageRelation = TextImageRelation.ImageBeforeText;
			this.btn_ChangeDate.UseVisualStyleBackColor = false;
			this.tablePanel_body.ColumnCount = 5;
			this.tablePanel_body.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 290f));
			this.tablePanel_body.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 0.1f));
			this.tablePanel_body.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 460f));
			this.tablePanel_body.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 0.1f));
			this.tablePanel_body.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 99.8f));
			this.tablePanel_body.Controls.Add(this.label_筛选结果, 4, 0);
			this.tablePanel_body.Controls.Add(this.tablePanel_CondClassHead, 2, 0);
			this.tablePanel_body.Controls.Add(this.tablePanel_AvlbCondsHead, 0, 0);
			this.tablePanel_body.Controls.Add(this.filterCondsPanel, 2, 1);
			this.tablePanel_body.Controls.Add(this.panel_RsltDgv, 4, 1);
			this.tablePanel_body.Controls.Add(this.panel_AvblConds, 0, 1);
			this.tablePanel_body.Dock = DockStyle.Fill;
			this.tablePanel_body.Location = new Point(0, 32);
			this.tablePanel_body.Margin = new System.Windows.Forms.Padding(0);
			this.tablePanel_body.Name = "tablePanel_body";
			this.tablePanel_body.RowCount = 2;
			this.tablePanel_body.RowStyles.Add(new RowStyle(SizeType.Absolute, 31f));
			this.tablePanel_body.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tablePanel_body.Size = new Size(1450, 330);
			this.tablePanel_body.TabIndex = 0;
			this.label_筛选结果.Anchor = AnchorStyles.None;
			this.label_筛选结果.AutoSize = true;
			this.label_筛选结果.BackColor = Color.Transparent;
			this.label_筛选结果.Font = new Font("SimSun", 9f, FontStyle.Bold, GraphicsUnit.Point, 134);
			this.label_筛选结果.Location = new Point(1064, 8);
			this.label_筛选结果.Name = "label_筛选结果";
			this.label_筛选结果.Size = new Size(71, 15);
			this.label_筛选结果.TabIndex = 2;
			this.label_筛选结果.Text = "筛选结果";
			this.tablePanel_CondClassHead.ColumnCount = 5;
			this.tablePanel_CondClassHead.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 15f));
			this.tablePanel_CondClassHead.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30f));
			this.tablePanel_CondClassHead.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30f));
			this.tablePanel_CondClassHead.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20f));
			this.tablePanel_CondClassHead.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20f));
			this.tablePanel_CondClassHead.Controls.Add(this.btn_Filter, 2, 0);
			this.tablePanel_CondClassHead.Controls.Add(this.btn_LoadConds, 4, 0);
			this.tablePanel_CondClassHead.Controls.Add(this.btn_SaveConds, 3, 0);
			this.tablePanel_CondClassHead.Controls.Add(this.label_当前条件, 1, 0);
			this.tablePanel_CondClassHead.Dock = DockStyle.Fill;
			this.tablePanel_CondClassHead.Location = new Point(290, 0);
			this.tablePanel_CondClassHead.Margin = new System.Windows.Forms.Padding(0);
			this.tablePanel_CondClassHead.Name = "tablePanel_CondClassHead";
			this.tablePanel_CondClassHead.RowCount = 1;
			this.tablePanel_CondClassHead.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tablePanel_CondClassHead.Size = new Size(460, 31);
			this.tablePanel_CondClassHead.TabIndex = 5;
			this.btn_Filter.Anchor = AnchorStyles.Left;
			this.btn_Filter.BackColor = Color.Transparent;
			this.btn_Filter.Image = Class372.flash_16x16;
			this.btn_Filter.ImageAlign = ContentAlignment.MiddleLeft;
			this.btn_Filter.Location = new Point(150, 2);
			this.btn_Filter.Margin = new System.Windows.Forms.Padding(2);
			this.btn_Filter.MinimumSize = new Size(106, 25);
			this.btn_Filter.Name = "btn_Filter";
			this.btn_Filter.Padding = new System.Windows.Forms.Padding(3, 0, 0, 0);
			this.btn_Filter.Size = new Size(116, 27);
			this.btn_Filter.TabIndex = 1;
			this.btn_Filter.Text = " 执行筛选";
			this.btn_Filter.TextAlign = ContentAlignment.MiddleRight;
			this.btn_Filter.TextImageRelation = TextImageRelation.ImageBeforeText;
			this.btn_Filter.UseVisualStyleBackColor = false;
			this.btn_LoadConds.Anchor = AnchorStyles.Right;
			this.btn_LoadConds.BackColor = Color.Transparent;
			this.btn_LoadConds.Image = Class372.folder_up_16x16;
			this.btn_LoadConds.ImageAlign = ContentAlignment.MiddleLeft;
			this.btn_LoadConds.Location = new Point(382, 2);
			this.btn_LoadConds.Margin = new System.Windows.Forms.Padding(2);
			this.btn_LoadConds.MinimumSize = new Size(66, 25);
			this.btn_LoadConds.Name = "btn_LoadConds";
			this.btn_LoadConds.Padding = new System.Windows.Forms.Padding(3, 0, 0, 0);
			this.btn_LoadConds.Size = new Size(76, 27);
			this.btn_LoadConds.TabIndex = 3;
			this.btn_LoadConds.Text = "加载";
			this.btn_LoadConds.TextImageRelation = TextImageRelation.ImageBeforeText;
			this.btn_LoadConds.UseVisualStyleBackColor = false;
			this.btn_SaveConds.Anchor = AnchorStyles.Right;
			this.btn_SaveConds.BackColor = Color.Transparent;
			this.btn_SaveConds.Image = Class372.floppy_disc_16x16;
			this.btn_SaveConds.ImageAlign = ContentAlignment.MiddleLeft;
			this.btn_SaveConds.Location = new Point(292, 2);
			this.btn_SaveConds.Margin = new System.Windows.Forms.Padding(2);
			this.btn_SaveConds.MinimumSize = new Size(66, 25);
			this.btn_SaveConds.Name = "btn_SaveConds";
			this.btn_SaveConds.Padding = new System.Windows.Forms.Padding(3, 0, 0, 0);
			this.btn_SaveConds.Size = new Size(76, 27);
			this.btn_SaveConds.TabIndex = 2;
			this.btn_SaveConds.Text = "保存";
			this.btn_SaveConds.TextAlign = ContentAlignment.MiddleRight;
			this.btn_SaveConds.TextImageRelation = TextImageRelation.ImageBeforeText;
			this.btn_SaveConds.UseVisualStyleBackColor = false;
			this.label_当前条件.Anchor = AnchorStyles.None;
			this.label_当前条件.AutoSize = true;
			this.label_当前条件.BackColor = Color.Transparent;
			this.label_当前条件.Font = new Font("SimSun", 9f, FontStyle.Bold, GraphicsUnit.Point, 134);
			this.label_当前条件.Location = new Point(46, 8);
			this.label_当前条件.Name = "label_当前条件";
			this.label_当前条件.Size = new Size(71, 15);
			this.label_当前条件.TabIndex = 0;
			this.label_当前条件.Text = "当前条件";
			this.tablePanel_AvlbCondsHead.ColumnCount = 3;
			this.tablePanel_AvlbCondsHead.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 6.122449f));
			this.tablePanel_AvlbCondsHead.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 46.93877f));
			this.tablePanel_AvlbCondsHead.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 46.93877f));
			this.tablePanel_AvlbCondsHead.Controls.Add(this.btn_AddCond, 2, 0);
			this.tablePanel_AvlbCondsHead.Controls.Add(this.label_可选条件, 1, 0);
			this.tablePanel_AvlbCondsHead.Dock = DockStyle.Fill;
			this.tablePanel_AvlbCondsHead.Location = new Point(0, 0);
			this.tablePanel_AvlbCondsHead.Margin = new System.Windows.Forms.Padding(0);
			this.tablePanel_AvlbCondsHead.Name = "tablePanel_AvlbCondsHead";
			this.tablePanel_AvlbCondsHead.RowCount = 1;
			this.tablePanel_AvlbCondsHead.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tablePanel_AvlbCondsHead.Size = new Size(290, 31);
			this.tablePanel_AvlbCondsHead.TabIndex = 6;
			this.btn_AddCond.Anchor = AnchorStyles.Left;
			this.btn_AddCond.BackColor = Color.Transparent;
			this.btn_AddCond.Image = Class372.add_16x16;
			this.btn_AddCond.ImageAlign = ContentAlignment.MiddleLeft;
			this.btn_AddCond.Location = new Point(155, 2);
			this.btn_AddCond.Margin = new System.Windows.Forms.Padding(2);
			this.btn_AddCond.MinimumSize = new Size(66, 25);
			this.btn_AddCond.Name = "btn_AddCond";
			this.btn_AddCond.Padding = new System.Windows.Forms.Padding(3, 0, 0, 0);
			this.btn_AddCond.Size = new Size(76, 27);
			this.btn_AddCond.TabIndex = 5;
			this.btn_AddCond.Text = "添加";
			this.btn_AddCond.TextImageRelation = TextImageRelation.ImageBeforeText;
			this.btn_AddCond.UseVisualStyleBackColor = false;
			this.label_可选条件.Anchor = AnchorStyles.None;
			this.label_可选条件.AutoSize = true;
			this.label_可选条件.BackColor = Color.Transparent;
			this.label_可选条件.Font = new Font("SimSun", 9f, FontStyle.Bold, GraphicsUnit.Point, 134);
			this.label_可选条件.Location = new Point(49, 8);
			this.label_可选条件.Name = "label_可选条件";
			this.label_可选条件.Size = new Size(71, 15);
			this.label_可选条件.TabIndex = 1;
			this.label_可选条件.Text = "可选条件";
			this.filterCondsPanel.AutoScroll = true;
			this.filterCondsPanel.AutoSize = true;
			this.filterCondsPanel.BackColor = Color.Transparent;
			this.filterCondsPanel.BorderColor = Color.Empty;
			this.filterCondsPanel.Dock = DockStyle.Fill;
			this.filterCondsPanel.FlowDirection = FlowDirection.TopDown;
			this.filterCondsPanel.Location = new Point(293, 34);
			this.filterCondsPanel.Name = "filterCondsPanel";
			this.filterCondsPanel.Padding = new System.Windows.Forms.Padding(3, 6, 3, 6);
			this.filterCondsPanel.Size = new Size(454, 293);
			this.filterCondsPanel.TabIndex = 3;
			this.filterCondsPanel.WrapContents = false;
			this.panel_RsltDgv.BorderColor = Color.Empty;
			this.panel_RsltDgv.Dock = DockStyle.Fill;
			this.panel_RsltDgv.DrawCustomBorder = false;
			this.panel_RsltDgv.Location = new Point(753, 34);
			this.panel_RsltDgv.Name = "panel_RsltDgv";
			this.panel_RsltDgv.Padding = new System.Windows.Forms.Padding(1);
			this.panel_RsltDgv.Size = new Size(694, 293);
			this.panel_RsltDgv.TabIndex = 7;
			this.panel_AvblConds.BorderColor = Color.Empty;
			this.panel_AvblConds.Controls.Add(this.superTabCtrl_AvblConds);
			this.panel_AvblConds.Dock = DockStyle.Fill;
			this.panel_AvblConds.DrawCustomBorder = false;
			this.panel_AvblConds.Location = new Point(3, 34);
			this.panel_AvblConds.Name = "panel_AvblConds";
			this.panel_AvblConds.Size = new Size(284, 293);
			this.panel_AvblConds.TabIndex = 8;
			this.superTabCtrl_AvblConds.ControlBox.CloseBox.Name = "";
			this.superTabCtrl_AvblConds.ControlBox.MenuBox.Name = "";
			this.superTabCtrl_AvblConds.ControlBox.Name = "";
			this.superTabCtrl_AvblConds.ControlBox.SubItems.AddRange(new BaseItem[]
			{
				this.superTabCtrl_AvblConds.ControlBox.MenuBox,
				this.superTabCtrl_AvblConds.ControlBox.CloseBox
			});
			this.superTabCtrl_AvblConds.Dock = DockStyle.Fill;
			this.superTabCtrl_AvblConds.Location = new Point(0, 0);
			this.superTabCtrl_AvblConds.Margin = new System.Windows.Forms.Padding(0);
			this.superTabCtrl_AvblConds.Name = "superTabCtrl_AvblConds";
			this.superTabCtrl_AvblConds.ReorderTabsEnabled = true;
			this.superTabCtrl_AvblConds.SelectedTabFont = new Font("SimSun", 9.5f, FontStyle.Bold);
			this.superTabCtrl_AvblConds.SelectedTabIndex = 0;
			this.superTabCtrl_AvblConds.Size = new Size(284, 293);
			this.superTabCtrl_AvblConds.TabAlignment = eTabStripAlignment.Left;
			this.superTabCtrl_AvblConds.TabFont = new Font("SimSun", 9.5f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.superTabCtrl_AvblConds.TabIndex = 2;
			this.superTabCtrl_AvblConds.TabStyle = eSuperTabStyle.Office2010BackstageBlue;
			this.superTabCtrl_AvblConds.Text = "superTabControl1";
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.tablePanel_bg);
			this.DoubleBuffered = true;
			base.Name = "SymbFilterPanel";
			base.Size = new Size(1450, 362);
			this.tablePanel_bg.ResumeLayout(false);
			this.tablePanel_header.ResumeLayout(false);
			this.tablePanel_header.PerformLayout();
			this.tablePanel_body.ResumeLayout(false);
			this.tablePanel_body.PerformLayout();
			this.tablePanel_CondClassHead.ResumeLayout(false);
			this.tablePanel_CondClassHead.PerformLayout();
			this.tablePanel_AvlbCondsHead.ResumeLayout(false);
			this.tablePanel_AvlbCondsHead.PerformLayout();
			this.panel_AvblConds.ResumeLayout(false);
			((ISupportInitialize)this.superTabCtrl_AvblConds).EndInit();
			base.ResumeLayout(false);
		}

		// Token: 0x04000B86 RID: 2950
		private SymbFilterApiWorker symbFilterApiWorker_0;

		// Token: 0x04000B87 RID: 2951
		private Class289 class289_0;

		// Token: 0x04000B88 RID: 2952
		[CompilerGenerated]
		private MsgEventHandler msgEventHandler_0;

		// Token: 0x04000B89 RID: 2953
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04000B8A RID: 2954
		private static Dictionary<string, List<FilterCond>> dictionary_0;

		// Token: 0x04000B8B RID: 2955
		private IContainer icontainer_0;

		// Token: 0x04000B8C RID: 2956
		private TableLayoutPanel tablePanel_bg;

		// Token: 0x04000B8D RID: 2957
		private TableLayoutPanel tablePanel_header;

		// Token: 0x04000B8E RID: 2958
		private Label label_交易所;

		// Token: 0x04000B8F RID: 2959
		private Button btn_ChangeDate;

		// Token: 0x04000B90 RID: 2960
		private Label label_CurrDate;

		// Token: 0x04000B91 RID: 2961
		private ComboBox cmbBox_Exchg;

		// Token: 0x04000B92 RID: 2962
		private Label label_板块;

		// Token: 0x04000B93 RID: 2963
		private ComboBox cmbBox_Index;

		// Token: 0x04000B94 RID: 2964
		private Label label_日期;

		// Token: 0x04000B95 RID: 2965
		private TableLayoutPanel tablePanel_body;

		// Token: 0x04000B96 RID: 2966
		private Label label_当前条件;

		// Token: 0x04000B97 RID: 2967
		private Label label_可选条件;

		// Token: 0x04000B98 RID: 2968
		private Button btn_Filter;

		// Token: 0x04000B99 RID: 2969
		private Label label_筛选结果;

		// Token: 0x04000B9A RID: 2970
		private Class294 filterCondsPanel;

		// Token: 0x04000B9B RID: 2971
		private TableLayoutPanel tablePanel_CondClassHead;

		// Token: 0x04000B9C RID: 2972
		private Button btn_SaveConds;

		// Token: 0x04000B9D RID: 2973
		private Button btn_LoadConds;

		// Token: 0x04000B9E RID: 2974
		private TableLayoutPanel tablePanel_AvlbCondsHead;

		// Token: 0x04000B9F RID: 2975
		private Button btn_AddCond;

		// Token: 0x04000BA0 RID: 2976
		private Class296 panel_RsltDgv;

		// Token: 0x04000BA1 RID: 2977
		private Class296 panel_AvblConds;

		// Token: 0x04000BA2 RID: 2978
		private SuperTabControl superTabCtrl_AvblConds;

		// Token: 0x02000225 RID: 549
		[CompilerGenerated]
		private sealed class Class298
		{
			// Token: 0x060016D8 RID: 5848 RVA: 0x0009AA64 File Offset: 0x00098C64
			internal bool method_0(FilterCondItem filterCondItem_0)
			{
				return filterCondItem_0.FilterCond.Name == this.filterCond_0.Name;
			}

			// Token: 0x04000BA9 RID: 2985
			public FilterCond filterCond_0;
		}

		// Token: 0x02000226 RID: 550
		[CompilerGenerated]
		private sealed class Class299
		{
			// Token: 0x060016DA RID: 5850 RVA: 0x0009AA90 File Offset: 0x00098C90
			internal bool method_0(FilterCondItem filterCondItem_0)
			{
				return filterCondItem_0.FilterCond.Name == this.filterCond_0.Name;
			}

			// Token: 0x04000BAA RID: 2986
			public FilterCond filterCond_0;
		}
	}
}
