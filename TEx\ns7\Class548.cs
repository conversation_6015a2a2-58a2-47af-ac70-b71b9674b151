﻿using System;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

namespace ns7
{
	// Token: 0x0200041C RID: 1052
	[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
	[DebuggerNonUserCode]
	[CompilerGenerated]
	internal sealed class Class548
	{
		// Token: 0x06002876 RID: 10358 RVA: 0x00002D25 File Offset: 0x00000F25
		internal Class548()
		{
		}

		// Token: 0x170006E9 RID: 1769
		// (get) Token: 0x06002877 RID: 10359 RVA: 0x0000FD5D File Offset: 0x0000DF5D
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static ResourceManager ResourceManager
		{
			get
			{
				if (Class548.resourceManager_0 == null)
				{
					Class548.resourceManager_0 = new ResourceManager("ns7.Class548", typeof(Class548).Assembly);
				}
				return Class548.resourceManager_0;
			}
		}

		// Token: 0x170006EA RID: 1770
		// (get) Token: 0x06002878 RID: 10360 RVA: 0x0000FD89 File Offset: 0x0000DF89
		// (set) Token: 0x06002879 RID: 10361 RVA: 0x0000FD90 File Offset: 0x0000DF90
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static CultureInfo Culture
		{
			get
			{
				return Class548.cultureInfo_0;
			}
			set
			{
				Class548.cultureInfo_0 = value;
			}
		}

		// Token: 0x170006EB RID: 1771
		// (get) Token: 0x0600287A RID: 10362 RVA: 0x0000FD98 File Offset: 0x0000DF98
		internal static Bitmap TExLogo
		{
			get
			{
				return (Bitmap)Class548.ResourceManager.GetObject("TExLogo", Class548.cultureInfo_0);
			}
		}

		// Token: 0x04001444 RID: 5188
		private static ResourceManager resourceManager_0;

		// Token: 0x04001445 RID: 5189
		private static CultureInfo cultureInfo_0;
	}
}
