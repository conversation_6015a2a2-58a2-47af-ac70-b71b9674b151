﻿using System;
using System.Drawing;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns27
{
	// Token: 0x020002FA RID: 762
	internal sealed class Class395 : ShapeCurve
	{
		// Token: 0x06002134 RID: 8500 RVA: 0x000E383C File Offset: 0x000E1A3C
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			JapaneseCandleStickItem japaneseCandleStickItem = zedGraphControl_0.GraphPane.AddJapaneseCandleStick("KLINE", this.rollingPointPairList_0);
			this.curveItem_0 = japaneseCandleStickItem;
			this.curveItem_0.Tag = string_0 + "_" + base.IndData.Name;
			japaneseCandleStickItem.Stick.IsAutoSize = true;
			japaneseCandleStickItem.Stick.Width = (float)base.method_2();
		}

		// Token: 0x06002135 RID: 8501 RVA: 0x000E38A8 File Offset: 0x000E1AA8
		protected override PointPair vmethod_0(int int_0, DataArray dataArray_1)
		{
			if (dataArray_1.Data.Length < int_0 + 1)
			{
				int_0 = dataArray_1.Data.Length - 1;
			}
			if (dataArray_1.OtherDataArrayList.Count != 3)
			{
				throw new Exception("K线数据不全。");
			}
			if (dataArray_1.OtherDataArrayList[0].Length == dataArray_1.Data.Length && dataArray_1.OtherDataArrayList[1].Length == dataArray_1.Data.Length)
			{
				if (dataArray_1.OtherDataArrayList[2].Length == dataArray_1.Data.Length)
				{
					double date = new XDate(base.method_0(int_0));
					double high = dataArray_1.Data[int_0];
					double open = dataArray_1.OtherDataArrayList[0].Data[int_0];
					double low = dataArray_1.OtherDataArrayList[1].Data[int_0];
					double close = dataArray_1.OtherDataArrayList[2].Data[int_0];
					double vol = 0.0;
					return new StockPt(date, high, low, open, close, vol);
				}
			}
			throw new Exception("数据长度长度不一致。");
		}

		// Token: 0x06002136 RID: 8502 RVA: 0x0000D56B File Offset: 0x0000B76B
		public Class395(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}
	}
}
