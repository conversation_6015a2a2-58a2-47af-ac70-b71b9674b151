﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Runtime.Serialization;
using TEx.Chart;

namespace TEx
{
	// Token: 0x0200006E RID: 110
	[Serializable]
	internal class DrawSquare : DrawObj, ISerializable
	{
		// Token: 0x06000400 RID: 1024 RVA: 0x000037AA File Offset: 0x000019AA
		public DrawSquare()
		{
		}

		// Token: 0x06000401 RID: 1025 RVA: 0x00003B69 File Offset: 0x00001D69
		public DrawSquare(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = "矩形";
			base.CanChgColor = true;
			base.IsOneClickLoc = false;
		}

		// Token: 0x06000402 RID: 1026 RVA: 0x000037DC File Offset: 0x000019DC
		protected DrawSquare(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x06000403 RID: 1027 RVA: 0x000037ED File Offset: 0x000019ED
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x06000404 RID: 1028 RVA: 0x0002247C File Offset: 0x0002067C
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			BoxObj item = this.method_39(chartCS_1, double_1, double_2, double_3, double_4, string_5);
			list.Add(item);
			return list;
		}

		// Token: 0x06000405 RID: 1029 RVA: 0x000224AC File Offset: 0x000206AC
		protected BoxObj method_39(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			Color borderColor = base.method_26();
			double x = double_1;
			double y = double_2;
			if (double_3 > double_1)
			{
				if (double_4 > double_2)
				{
					y = double_4;
				}
			}
			else
			{
				x = double_3;
				if (double_4 > double_2)
				{
					y = double_4;
				}
			}
			return new BoxObj(x, y, Math.Abs(double_3 - double_1), Math.Abs(double_4 - double_2), borderColor, Color.Transparent)
			{
				IsClippedToChartRect = true,
				ZOrder = ZOrder.A_InFront,
				Tag = string_5
			};
		}

		// Token: 0x06000406 RID: 1030 RVA: 0x0001FB74 File Offset: 0x0001DD74
		protected override DrawLineStyle vmethod_23()
		{
			return null;
		}
	}
}
