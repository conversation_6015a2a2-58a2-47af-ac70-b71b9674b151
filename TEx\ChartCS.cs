﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns11;
using ns15;
using ns16;
using ns17;
using ns20;
using ns26;
using ns28;
using ns30;
using ns33;
using ns4;
using ns6;
using ns8;
using ns9;
using TEx.Chart;
using TEx.Comn;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x020001B2 RID: 434
	internal sealed class ChartCS : ChartKLine
	{
		// Token: 0x060010DD RID: 4317 RVA: 0x00073740 File Offset: 0x00071940
		public ChartCS(ChtCtrl_KLine xchartCtrl, SplitterPanel panel, bool isXAxisVisible, bool isYAxisLogType) : base(xchartCtrl, panel)
		{
			this.IsYAxisCoordsLogType = isYAxisLogType;
			base.IsXAxisVisible = isXAxisVisible;
			if (!this.IsYAxisCoordsLogType)
			{
				base.GraphPane.YAxis.ScaleFormatEvent += this.method_267;
			}
			base.SupportHighLowMark = true;
		}

		// Token: 0x060010DE RID: 4318 RVA: 0x000072D5 File Offset: 0x000054D5
		public ChartCS(ChtCtrl_KLine xchartCtrl, SplitterPanel panel, bool isYAxisLogType) : this(xchartCtrl, panel, false, isYAxisLogType)
		{
		}

		// Token: 0x060010DF RID: 4319 RVA: 0x00073794 File Offset: 0x00071994
		protected override void vmethod_0()
		{
			this.IsInitiating = true;
			base.vmethod_0();
			List<Indicator> indList = new List<Indicator>();
			base.IndList = indList;
			base.ChartType = ChartType.CandleStick;
			this.list_2 = new List<TransArrow>();
			this.vmethod_2();
			this.method_158();
			this.vmethod_3();
			this.ApplyTheme(Base.UI.Form.ChartTheme);
			base.ZedGraphControl.AxisChange();
			base.ZedGraphControl.Refresh();
			base.ZedGraphControl.MouseDownEvent += new ZedGraphControl.ZedMouseEventHandler(this.method_248);
			base.ZedGraphControl.MouseUpEvent += new ZedGraphControl.ZedMouseEventHandler(this.method_249);
			Base.Trading.TransCreated += this.method_209;
			Base.Trading.TransactionsUpdated += this.method_149;
			Base.Trading.OrderCreated += this.method_210;
			Base.Trading.OrderStatusUpdated += this.method_211;
			Base.Trading.OrderPriceUnitsUpdated += this.method_212;
			Base.Trading.CondOrderCreated += this.method_213;
			Base.Trading.CondOrderUpdated += this.method_215;
			Base.Trading.CondOrderStatusUpdated += this.method_214;
			Base.Data.CurrSymblChanging += this.method_148;
			Base.Data.CurrSymblChanged += this.method_150;
			Base.Acct.AccountChanging += this.method_151;
			Base.Acct.AccountChanged += this.method_152;
			Base.UI.Form.StockRestorationMethodChanged += this.method_153;
			Base.UI.Form.TransArrowTypeChanged += this.method_216;
			InfoMineMgr.InfoMineRetrieved += this.method_223;
			base.ChtCtrl.StkRationedPrcApplying += this.method_154;
			base.ChtCtrl.MouseUp += this.method_275;
			this.IsInitiating = false;
		}

		// Token: 0x060010E0 RID: 4320 RVA: 0x000072E1 File Offset: 0x000054E1
		private void method_148(EventArgs1 eventArgs1_0)
		{
			this.method_244(false, false);
			base.method_93();
			base.vmethod_17();
			this.method_226();
			this.InfoMineDict = null;
		}

		// Token: 0x060010E1 RID: 4321 RVA: 0x00007306 File Offset: 0x00005506
		private void method_149(object sender, EventArgs e)
		{
			this.method_200(false);
		}

		// Token: 0x060010E2 RID: 4322 RVA: 0x00007311 File Offset: 0x00005511
		private void method_150(EventArgs1 eventArgs1_0)
		{
			this.method_208(true);
		}

		// Token: 0x060010E3 RID: 4323 RVA: 0x0000731C File Offset: 0x0000551C
		private void method_151(object sender, EventArgs e)
		{
			this.method_203();
			this.method_222();
			this.method_244(false, false);
			base.vmethod_17();
		}

		// Token: 0x060010E4 RID: 4324 RVA: 0x00007311 File Offset: 0x00005511
		private void method_152(object sender, EventArgs e)
		{
			this.method_208(true);
		}

		// Token: 0x060010E5 RID: 4325 RVA: 0x0000733A File Offset: 0x0000553A
		private void method_153(object sender, EventArgs e)
		{
			this.method_217();
		}

		// Token: 0x060010E6 RID: 4326 RVA: 0x0000733A File Offset: 0x0000553A
		private void method_154(object sender, EventArgs e)
		{
			this.method_217();
		}

		// Token: 0x060010E7 RID: 4327 RVA: 0x00007344 File Offset: 0x00005544
		public override void vmethod_2()
		{
			base.vmethod_2();
			this.method_155(true);
			base.vmethod_24();
		}

		// Token: 0x060010E8 RID: 4328 RVA: 0x00073974 File Offset: 0x00071B74
		private bool method_155(bool bool_8)
		{
			bool result = false;
			if (this.curveItem_0 == null || bool_8)
			{
				if (Base.UI.Form.KLineType != null && Base.UI.Form.KLineType.Value != KLineType.CandleStick_EmptyUpK)
				{
					if (Base.UI.Form.KLineType.Value != KLineType.CandleStick_SolidUpK)
					{
						this.method_156(Base.UI.Form.KLineType.Value);
						result = true;
						goto IL_18A;
					}
				}
				this.method_156(KLineType.CandleStick_EmptyUpK);
				result = true;
			}
			else if (this.curveItem_0 != null && Base.UI.Form.KLineType != null)
			{
				if (Base.UI.Form.KLineType.Value != KLineType.CandleStick_EmptyUpK)
				{
					if (Base.UI.Form.KLineType.Value != KLineType.CandleStick_SolidUpK)
					{
						if (Base.UI.Form.KLineType.Value != KLineType.OHLCBar && Base.UI.Form.KLineType.Value != KLineType.HLCBar)
						{
							goto IL_18A;
						}
						bool flag = false;
						if (this.curveItem_0 is OHLCBarItem)
						{
							if ((this.curveItem_0 as OHLCBarItem).Bar.IsOpenVisible)
							{
								if (Base.UI.Form.KLineType.Value == KLineType.HLCBar)
								{
									flag = true;
								}
							}
							else if (Base.UI.Form.KLineType.Value == KLineType.OHLCBar)
							{
								flag = true;
							}
						}
						else
						{
							flag = true;
						}
						if (flag)
						{
							this.method_156(Base.UI.Form.KLineType.Value);
							result = true;
							goto IL_18A;
						}
						goto IL_18A;
					}
				}
				if (!(this.curveItem_0 is JapaneseCandleStickItem))
				{
					this.method_156(KLineType.CandleStick_EmptyUpK);
					result = true;
				}
			}
			IL_18A:
			return result;
		}

		// Token: 0x060010E9 RID: 4329 RVA: 0x00073B10 File Offset: 0x00071D10
		private void method_156(KLineType klineType_0)
		{
			if (this.curveItem_0 != null)
			{
				this.curveItem_0.Clear();
			}
			RollingPointPairList points = new RollingPointPairList(base.ChtCtrl_KLine.MaxSticksPerChart);
			if (klineType_0 != KLineType.CandleStick_EmptyUpK)
			{
				if (klineType_0 != KLineType.CandleStick_SolidUpK)
				{
					Color color = Color.Blue;
					if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
					{
						color = Color.DarkGreen;
					}
					bool value = klineType_0 == KLineType.OHLCBar;
					this.curveItem_0 = base.GraphPane.AddOHLCBar("CandleStick", points, color, new bool?(value));
					goto IL_81;
				}
			}
			this.curveItem_0 = base.GraphPane.AddJapaneseCandleStick("CandleStick", points);
			IL_81:
			this.curveItem_0.Tag = "CandleStick";
			this.method_157(this.curveItem_0);
			this.ipointListEdit_0 = (this.curveItem_0.Points as IPointListEdit);
		}

		// Token: 0x060010EA RID: 4330 RVA: 0x00073BD4 File Offset: 0x00071DD4
		public override void vmethod_3()
		{
			base.vmethod_3();
			base.ZedGraphControl.PointValueFormat = "F" + base.Symbol.DigitNb.ToString();
			GraphPane graphPane = base.GraphPane;
			graphPane.Margin.Top = 4f;
			graphPane.Margin.Bottom = 1f;
			graphPane.Margin.Right = 5f;
			this.method_266();
		}

		// Token: 0x060010EB RID: 4331 RVA: 0x0000735C File Offset: 0x0000555C
		private void method_157(CurveItem curveItem_1)
		{
			if (curveItem_1 is JapaneseCandleStickItem)
			{
				JapaneseCandleStickItem japaneseCandleStickItem = curveItem_1 as JapaneseCandleStickItem;
				japaneseCandleStickItem.Stick.IsAutoSize = true;
				japaneseCandleStickItem.Stick.Width = 1f;
			}
			curveItem_1.Label.IsVisible = false;
			curveItem_1.IsSelectable = true;
		}

		// Token: 0x060010EC RID: 4332 RVA: 0x00073C4C File Offset: 0x00071E4C
		private void method_158()
		{
			PaneBase graphPane = base.GraphPane;
			base.HeaderTextObj.FontSpec.Size = 12f;
			this.textObj_3 = new TextObj("", 0.364, 0.016, CoordType.ChartFraction, AlignH.Left, AlignV.Top);
			base.method_45(this.textObj_3);
			graphPane.GraphObjList.Add(this.textObj_3);
		}

		// Token: 0x060010ED RID: 4333 RVA: 0x00073CB8 File Offset: 0x00071EB8
		private void method_159(DrawMode drawMode_0, PointD pointD_0, PointD pointD_1)
		{
			Type type = this.method_166(drawMode_0);
			object[] args = new object[]
			{
				this,
				pointD_0.X,
				pointD_0.Y,
				pointD_1.X,
				pointD_1.Y
			};
			DrawObj item = Activator.CreateInstance(type, args) as DrawObj;
			Base.UI.DrawingObj.vmethod_20(this);
			Base.UI.DrawObjList.Add(item);
		}

		// Token: 0x060010EE RID: 4334 RVA: 0x0000739C File Offset: 0x0000559C
		private void method_160()
		{
			this.method_161(Cursors.Default);
		}

		// Token: 0x060010EF RID: 4335 RVA: 0x000073AB File Offset: 0x000055AB
		private void method_161(Cursor cursor_0)
		{
			if (base.ZedGraphControl != null && base.ZedGraphControl.Cursor != cursor_0)
			{
				base.ZedGraphControl.Cursor = cursor_0;
			}
		}

		// Token: 0x060010F0 RID: 4336 RVA: 0x00073D38 File Offset: 0x00071F38
		public void method_162()
		{
			foreach (TransArrow transArrow in this.TransArrowList)
			{
				if (transArrow.LightColor)
				{
					transArrow.LightColor = false;
					transArrow.method_11();
				}
				transArrow.ShowOCLines = false;
			}
		}

		// Token: 0x060010F1 RID: 4337 RVA: 0x00073DA4 File Offset: 0x00071FA4
		private void method_163()
		{
			if (this.TOdrLineList != null)
			{
				foreach (TOdrLine todrLine in this.TOdrLineList)
				{
					todrLine.HighLighted = false;
				}
			}
		}

		// Token: 0x060010F2 RID: 4338 RVA: 0x00073E00 File Offset: 0x00072000
		private DrawObj method_164(DrawMode drawMode_0, PointD pointD_0)
		{
			DrawObj drawObj = null;
			if (Base.UI.DrawingObj1stPt != null)
			{
				if (Base.UI.DrawingObj == null)
				{
					drawObj = this.method_165(drawMode_0, pointD_0);
					Base.UI.DrawingObj = drawObj;
				}
				else
				{
					drawObj = Base.UI.DrawingObj;
					Base.UI.DrawingObj.vmethod_18(this, Base.UI.DrawingObj1stPt.Value.X, Base.UI.DrawingObj1stPt.Value.Y, pointD_0.X, pointD_0.Y, true, false);
				}
				this.method_160();
			}
			else
			{
				this.method_161(Cursors.Cross);
			}
			return drawObj;
		}

		// Token: 0x060010F3 RID: 4339 RVA: 0x00073E94 File Offset: 0x00072094
		private DrawObj method_165(DrawMode drawMode_0, PointD pointD_0)
		{
			Type type = this.method_166(drawMode_0);
			object[] args = new object[]
			{
				this,
				Base.UI.DrawingObj1stPt.Value.X,
				Base.UI.DrawingObj1stPt.Value.Y,
				pointD_0.X,
				pointD_0.Y
			};
			return Activator.CreateInstance(type, args) as DrawObj;
		}

		// Token: 0x060010F4 RID: 4340 RVA: 0x00073F14 File Offset: 0x00072114
		private Type method_166(DrawMode drawMode_0)
		{
			return Type.GetType("TEx.Draw" + Enum.GetName(typeof(DrawMode), drawMode_0));
		}

		// Token: 0x060010F5 RID: 4341 RVA: 0x00073F4C File Offset: 0x0007214C
		private void method_167(object sender, EventArgs8 e)
		{
			if (!string.IsNullOrEmpty(e.Text.Trim()))
			{
				if (e.IfCreateNew)
				{
					if (Base.UI.DrawingObj1stPt != null)
					{
						DrawText item = new DrawText(this, Base.UI.DrawingObj1stPt.Value.X, Base.UI.DrawingObj1stPt.Value.Y, e.Text, e.Color, e.Font);
						Base.UI.DrawObjList.Add(item);
						Base.UI.DrawMode = DrawMode.Off;
						Base.UI.DrawingObj1stPt = null;
						Base.UI.DrawingObj = null;
						Base.UI.smethod_136();
					}
				}
				else
				{
					DrawObj selectedDrawObj = this.SelectedDrawObj;
					if (selectedDrawObj != null)
					{
						DrawText drawText = selectedDrawObj as DrawText;
						drawText.Text = e.Text;
						drawText.LineColor = e.Color;
						if (e.Font != null)
						{
							drawText.Font = (e.Font as Font);
						}
						Base.UI.smethod_136();
						drawText.vmethod_10(this, true, false);
					}
				}
			}
		}

		// Token: 0x060010F6 RID: 4342 RVA: 0x00074048 File Offset: 0x00072248
		private void method_168(object sender, EventArgs e)
		{
			Base.UI.DrawMode = DrawMode.Off;
			Base.UI.DrawingObj1stPt = null;
			Base.UI.DrawingObj = null;
		}

		// Token: 0x060010F7 RID: 4343 RVA: 0x00074074 File Offset: 0x00072274
		public void method_169()
		{
			foreach (DrawObj drawObj in Base.UI.DrawObjList)
			{
				drawObj.method_14(this);
			}
		}

		// Token: 0x060010F8 RID: 4344 RVA: 0x000740C8 File Offset: 0x000722C8
		public void method_170()
		{
			foreach (DrawObj drawObj in Base.UI.DrawObjList)
			{
				if (!drawObj.IsSelected)
				{
					drawObj.method_15(this);
				}
				drawObj.MouseHovering = false;
			}
		}

		// Token: 0x060010F9 RID: 4345 RVA: 0x000073D6 File Offset: 0x000055D6
		public override void vmethod_21(bool bool_8)
		{
			base.vmethod_21(bool_8);
			if (!bool_8)
			{
				this.method_169();
			}
		}

		// Token: 0x060010FA RID: 4346 RVA: 0x0007412C File Offset: 0x0007232C
		protected void method_171(ContextMenuStrip contextMenuStrip_0, DrawObj drawObj_1)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "删除画线";
			toolStripMenuItem.Click += this.method_172;
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = "调整参数";
			toolStripMenuItem2.Click += this.method_173;
			contextMenuStrip_0.Items.Add(toolStripMenuItem2);
		}

		// Token: 0x060010FB RID: 4347 RVA: 0x0007419C File Offset: 0x0007239C
		private void method_172(object sender, EventArgs e)
		{
			DrawObj selectedDrawObj = this.SelectedDrawObj;
			if (selectedDrawObj != null)
			{
				selectedDrawObj.method_30();
			}
		}

		// Token: 0x060010FC RID: 4348 RVA: 0x000741BC File Offset: 0x000723BC
		private void method_173(object sender, EventArgs e)
		{
			DrawObj selectedDrawObj = this.SelectedDrawObj;
			if (selectedDrawObj != null)
			{
				if (selectedDrawObj is DrawText)
				{
					DrawTxtWnd drawTxtWnd = new DrawTxtWnd(selectedDrawObj as DrawText);
					drawTxtWnd.DrawTxtSet += this.method_167;
					drawTxtWnd.ShowDialog();
				}
				else
				{
					DrawParamWnd drawParamWnd = new DrawParamWnd(selectedDrawObj);
					drawParamWnd.DrawObjParamSet += this.method_174;
					drawParamWnd.ShowDialog();
				}
			}
		}

		// Token: 0x060010FD RID: 4349 RVA: 0x00074220 File Offset: 0x00072420
		private void method_174(object sender, EventArgs7 e)
		{
			DrawObj selectedDrawObj = this.SelectedDrawObj;
			if (selectedDrawObj != null)
			{
				DTValLocation dtvalLocation = this.method_175(e.DTValLocation);
				selectedDrawObj.DTValLocation = dtvalLocation;
				selectedDrawObj.LineColor = e.Color;
				selectedDrawObj.LineStyle = e.DrawLineStyle;
				selectedDrawObj.SublineParamList = e.ParamList;
				int? num = this.method_184(dtvalLocation.X1DateTime);
				int? num2 = this.method_184(dtvalLocation.X2DateTime);
				if (num == null)
				{
					num = new int?(num2.Value - Convert.ToInt32(Math.Round(selectedDrawObj.XPtsGap)));
				}
				if (num2 == null)
				{
					num2 = new int?(num.Value + Convert.ToInt32(Math.Round(selectedDrawObj.XPtsGap)));
				}
				double y1Value = dtvalLocation.Y1Value;
				double y2Value = dtvalLocation.Y2Value;
				selectedDrawObj.vmethod_18(this, Convert.ToDouble(num + 1), y1Value, Convert.ToDouble(num2 + 1), y2Value, true, false);
				Base.UI.smethod_136();
			}
		}

		// Token: 0x060010FE RID: 4350 RVA: 0x00074360 File Offset: 0x00072560
		private DTValLocation method_175(DTValLocation dtvalLocation_0)
		{
			return new DTValLocation(this.method_182(dtvalLocation_0.X1DateTime), dtvalLocation_0.Y1Value, this.method_182(dtvalLocation_0.X2DateTime), dtvalLocation_0.Y2Value);
		}

		// Token: 0x060010FF RID: 4351 RVA: 0x0007439C File Offset: 0x0007259C
		protected void method_176(ContextMenuStrip contextMenuStrip_0, TransArrow transArrow_1)
		{
			if (string.IsNullOrEmpty(transArrow_1.Notes))
			{
				ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
				toolStripMenuItem.Text = "添加交易注释...";
				toolStripMenuItem.Click += this.method_177;
				contextMenuStrip_0.Items.Add(toolStripMenuItem);
			}
			else
			{
				ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
				toolStripMenuItem2.Text = "修改交易注释...";
				toolStripMenuItem2.Click += this.method_177;
				contextMenuStrip_0.Items.Add(toolStripMenuItem2);
				ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
				toolStripMenuItem3.Text = "删除交易注释";
				toolStripMenuItem3.Click += this.method_178;
				contextMenuStrip_0.Items.Add(toolStripMenuItem3);
			}
		}

		// Token: 0x06001100 RID: 4352 RVA: 0x0007444C File Offset: 0x0007264C
		private void method_177(object sender, EventArgs e)
		{
			TransArrow highLightedTransArrow = this.HighLightedTransArrow;
			if (highLightedTransArrow != null)
			{
				DrawTxtWnd drawTxtWnd = new DrawTxtWnd(highLightedTransArrow);
				drawTxtWnd.DrawTxtSet += this.method_179;
				drawTxtWnd.ShowDialog();
			}
		}

		// Token: 0x06001101 RID: 4353 RVA: 0x000073EA File Offset: 0x000055EA
		private void method_178(object sender, EventArgs e)
		{
			if (MessageBox.Show("删除该条交易注释吗？", "请确认", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) == DialogResult.OK)
			{
				this.method_180(string.Empty);
			}
		}

		// Token: 0x06001102 RID: 4354 RVA: 0x0000740E File Offset: 0x0000560E
		private void method_179(object sender, EventArgs8 e)
		{
			this.method_181(e.Text, e.Color, e.Font);
		}

		// Token: 0x06001103 RID: 4355 RVA: 0x00074484 File Offset: 0x00072684
		private void method_180(string string_12)
		{
			this.method_181(string_12, null, null);
		}

		// Token: 0x06001104 RID: 4356 RVA: 0x000744A4 File Offset: 0x000726A4
		private void method_181(string string_12, Color? nullable_3, object object_0)
		{
			ChartCS.Class240 @class = new ChartCS.Class240();
			@class.transArrow_0 = this.HighLightedTransArrow;
			@class.transArrow_0.method_25(string_12, nullable_3, object_0);
			Base.Trading.Transactions.Single(new Func<Transaction, bool>(@class.method_0)).Notes = @class.transArrow_0.Transaction.Notes;
			Base.Trading.smethod_126();
			if (Base.Trading.CurrHisTransList != null)
			{
				foreach (ShownHisTrans shownHisTrans in Base.Trading.CurrHisTransList)
				{
					if (shownHisTrans.AcctID == @class.transArrow_0.Transaction.AcctID && shownHisTrans.ID == @class.transArrow_0.Transaction.ID)
					{
						shownHisTrans.Notes = @class.transArrow_0.UserNote;
					}
				}
			}
			Base.UI.smethod_141(this, @class.transArrow_0);
		}

		// Token: 0x06001105 RID: 4357 RVA: 0x00074590 File Offset: 0x00072790
		private DateTime method_182(DateTime dateTime_0)
		{
			DateTime date;
			if (dateTime_0 < base.ChtCtrl.FirstItemShown.Date)
			{
				date = base.ChtCtrl.FirstItemShown.Date;
			}
			else
			{
				if (!(dateTime_0 > base.ChtCtrl.LastItemShown.Date))
				{
					for (int i = base.ChtCtrl.IndexOfFirstItemShown; i < base.ChtCtrl.IndexOfLastItemShown; i++)
					{
						HisData hisData = base.HisDataPeriodSet.PeriodHisDataList.Values[i];
						HisData hisData2 = base.HisDataPeriodSet.PeriodHisDataList.Values[i + 1];
						if (dateTime_0 > hisData.Date && dateTime_0 < hisData2.Date)
						{
							dateTime_0 = ((dateTime_0 - hisData.Date > hisData2.Date - dateTime_0) ? hisData2.Date : hisData.Date);
							IL_F9:
							return dateTime_0;
						}
					}
					goto IL_F9;
				}
				date = base.ChtCtrl.LastItemShown.Date;
			}
			return date;
		}

		// Token: 0x06001106 RID: 4358 RVA: 0x0007469C File Offset: 0x0007289C
		public int? method_183(DateTime dateTime_0)
		{
			GraphPane graphPane = base.GraphPane;
			if (graphPane != null && graphPane.CurveList.Count > 0)
			{
				CurveItem curveItem = graphPane.CurveList[0];
				XDate xDate = new XDate(dateTime_0);
				for (int i = 0; i < curveItem.NPts; i++)
				{
					if (curveItem.Points[i].X == xDate)
					{
						return new int?(i);
					}
				}
			}
			return null;
		}

		// Token: 0x06001107 RID: 4359 RVA: 0x00074720 File Offset: 0x00072920
		public int? method_184(DateTime dateTime_0)
		{
			int? num = new int?(base.HisDataPeriodSet.PeriodHisDataList.IndexOfKey(dateTime_0));
			int? num2 = num;
			int? result;
			if (num2.GetValueOrDefault() >= 0 & num2 != null)
			{
				num2 = num;
				int firstItemIndex = base.FirstItemIndex;
				if (num2 == null)
				{
					result = null;
				}
				else
				{
					result = new int?(num2.GetValueOrDefault() - firstItemIndex);
				}
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001108 RID: 4360 RVA: 0x000747A0 File Offset: 0x000729A0
		private bool method_185(bool bool_8 = false, bool bool_9 = false)
		{
			bool result = false;
			if (Base.UI.DrawObjList != null)
			{
				foreach (DrawObj drawObj in Base.UI.DrawObjList.Where(new Func<DrawObj, bool>(this.method_281)))
				{
					try
					{
						if (drawObj.vmethod_10(this, bool_8, bool_9))
						{
							result = true;
						}
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
			}
			return result;
		}

		// Token: 0x06001109 RID: 4361 RVA: 0x00074828 File Offset: 0x00072A28
		protected PointD? method_186(PointD pointD_0)
		{
			new PointF(Convert.ToSingle(pointD_0.X), Convert.ToSingle(pointD_0.Y));
			GraphPane graphPane = base.GraphPane;
			PointD? result;
			if (graphPane != null && graphPane.CurveList.Count > 0)
			{
				CurveItem curveItem = graphPane.CurveList[0];
				double x = Math.Round(this.method_246(pointD_0.X));
				result = new PointD?(new PointD(x, pointD_0.Y));
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x0600110A RID: 4362 RVA: 0x000748AC File Offset: 0x00072AAC
		private DrawObj method_187(GraphObj graphObj_0)
		{
			DrawObj result;
			if (graphObj_0 != null && graphObj_0.Tag != null)
			{
				DrawObj drawObj;
				try
				{
					ChartCS.Class241 @class = new ChartCS.Class241();
					@class.string_0 = graphObj_0.Tag.ToString();
					if (@class.string_0.Equals("revCross"))
					{
						drawObj = null;
					}
					else
					{
						if (@class.string_0.EndsWith(DrawObj.string_0))
						{
							@class.string_0 = @class.string_0.Replace(DrawObj.string_0, "");
						}
						drawObj = Base.UI.DrawObjList.SingleOrDefault(new Func<DrawObj, bool>(@class.method_0));
					}
				}
				catch (Exception)
				{
					drawObj = null;
				}
				result = drawObj;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x0600110B RID: 4363 RVA: 0x00074958 File Offset: 0x00072B58
		private DrawObj method_188(PointF pointF_0)
		{
			GraphObj graphObj_ = this.method_189(pointF_0);
			return this.method_187(graphObj_);
		}

		// Token: 0x0600110C RID: 4364 RVA: 0x00074978 File Offset: 0x00072B78
		private GraphObj method_189(PointF pointF_0)
		{
			GraphPane graphPane = base.GraphPane;
			GraphObj graphObj = this.method_197(pointF_0);
			GraphObj result;
			if (graphObj != null && this.method_190(graphObj))
			{
				result = graphObj;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x0600110D RID: 4365 RVA: 0x000749AC File Offset: 0x00072BAC
		private bool method_190(GraphObj graphObj_0)
		{
			bool result;
			if (graphObj_0.Tag != null)
			{
				result = graphObj_0.Tag.ToString().StartsWith(DrawObj.string_1);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600110E RID: 4366 RVA: 0x000749E0 File Offset: 0x00072BE0
		private bool method_191(GraphObj graphObj_0)
		{
			bool result;
			if (graphObj_0.Tag != null)
			{
				result = graphObj_0.Tag.ToString().StartsWith(TOdrLine.string_1);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600110F RID: 4367 RVA: 0x00074A14 File Offset: 0x00072C14
		private bool method_192(GraphObj graphObj_0)
		{
			bool result;
			if (graphObj_0.Tag != null)
			{
				result = graphObj_0.Tag.ToString().StartsWith(Class20.string_0);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06001110 RID: 4368 RVA: 0x00074A48 File Offset: 0x00072C48
		private TOdrLine method_193(GraphObj graphObj_0)
		{
			if (graphObj_0 != null && graphObj_0.Tag != null)
			{
				TOdrLine result;
				try
				{
					ChartCS.Class242 @class = new ChartCS.Class242();
					@class.string_0 = graphObj_0.Tag.ToString();
					if (!string.IsNullOrEmpty(@class.string_0))
					{
						if (Base.UI.TOdrLineList == null)
						{
							result = null;
							goto IL_5F;
						}
						TOdrLine todrLine = Base.UI.TOdrLineList.SingleOrDefault(new Func<TOdrLine, bool>(@class.method_0));
						if (todrLine != null)
						{
							result = todrLine;
							goto IL_5F;
						}
					}
					goto IL_63;
				}
				catch (Exception)
				{
					result = null;
				}
				IL_5F:
				return result;
			}
			IL_63:
			return null;
		}

		// Token: 0x06001111 RID: 4369 RVA: 0x00074AD0 File Offset: 0x00072CD0
		private TOdrLine method_194(PointF pointF_0)
		{
			GraphObj graphObj_ = this.method_189(pointF_0);
			return this.method_193(graphObj_);
		}

		// Token: 0x06001112 RID: 4370 RVA: 0x00074AF0 File Offset: 0x00072CF0
		private Class68 method_195(Transaction transaction_0)
		{
			if (Base.UI.TOdrLineList != null)
			{
				IEnumerable<TOdrLine> enumerable = Base.UI.TOdrLineList.Where(new Func<TOdrLine, bool>(ChartCS.<>c.<>9.method_0));
				if (enumerable.Any<TOdrLine>())
				{
					Class68 result;
					using (IEnumerator<TOdrLine> enumerator = enumerable.GetEnumerator())
					{
						while (enumerator.MoveNext())
						{
							TOdrLine todrLine = enumerator.Current;
							Class68 @class = todrLine as Class68;
							if (@class.OpenTrans.AcctID == transaction_0.AcctID && @class.OpenTrans.ID == transaction_0.ID)
							{
								result = @class;
								goto IL_92;
							}
						}
						goto IL_97;
					}
					IL_92:
					return result;
				}
			}
			IL_97:
			return null;
		}

		// Token: 0x06001113 RID: 4371 RVA: 0x00074BAC File Offset: 0x00072DAC
		private Class66 method_196(CondOrder condOrder_0)
		{
			if (this.TOdrLineList != null)
			{
				Class66 result;
				using (List<TOdrLine>.Enumerator enumerator = this.TOdrLineList.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						TOdrLine todrLine = enumerator.Current;
						if (todrLine is Class66)
						{
							Class66 @class = todrLine as Class66;
							if (@class.CondOrder.ID == condOrder_0.ID && @class.CondOrder.AcctID == condOrder_0.AcctID)
							{
								result = @class;
								goto IL_73;
							}
						}
					}
					goto IL_78;
				}
				IL_73:
				return result;
			}
			IL_78:
			return null;
		}

		// Token: 0x06001114 RID: 4372 RVA: 0x00074C48 File Offset: 0x00072E48
		private GraphObj method_197(PointF pointF_0)
		{
			object obj;
			int num;
			GraphObj result;
			if (base.GraphPane.FindNearestObject(pointF_0, base.ZedGraphControl.CreateGraphics(), out obj, out num) && obj is GraphObj)
			{
				result = (GraphObj)obj;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001115 RID: 4373 RVA: 0x00074C88 File Offset: 0x00072E88
		public void method_198()
		{
			for (int i = 0; i < base.GraphPane.GraphObjList.Count; i++)
			{
				GraphObj graphObj = base.GraphPane.GraphObjList[i];
				if (graphObj.Tag != null && graphObj.Tag.ToString().StartsWith(DrawObj.string_1))
				{
					base.GraphPane.GraphObjList.Remove(graphObj);
				}
			}
		}

		// Token: 0x06001116 RID: 4374 RVA: 0x0000742A File Offset: 0x0000562A
		public void method_199(Transaction transaction_0)
		{
			new TransArrow(this, transaction_0);
		}

		// Token: 0x06001117 RID: 4375 RVA: 0x00074CF8 File Offset: 0x00072EF8
		public void method_200(bool bool_8 = false)
		{
			TimeUnit timeUnit = Base.UI.Form.PeriodOfChartDispTransArrow;
			if (timeUnit == (TimeUnit)0)
			{
				timeUnit = TimeUnit.Day;
			}
			if (base.HisDataPeriodSet.PeriodType == PeriodType.ByMins)
			{
				int num = (int)timeUnit;
				int? periodUnits = base.HisDataPeriodSet.PeriodUnits;
				if (num < periodUnits.GetValueOrDefault() & periodUnits != null)
				{
					this.method_203();
					return;
				}
			}
			else if (base.HisDataPeriodSet.PeriodType == PeriodType.ByDay)
			{
				if (timeUnit < TimeUnit.Day)
				{
					this.method_203();
					return;
				}
			}
			else if (base.HisDataPeriodSet.PeriodType == PeriodType.ByWeek)
			{
				if (timeUnit < TimeUnit.Week)
				{
					this.method_203();
					return;
				}
			}
			else if (base.HisDataPeriodSet.PeriodType == PeriodType.ByMonth && timeUnit < TimeUnit.Month)
			{
				this.method_203();
				return;
			}
			if (Base.UI.Form.IfShowNoTransArrow)
			{
				this.method_203();
			}
			else
			{
				if (this.IfShowNoTransArrow || (!this.IfShowNoTransArrow && this.IfShowAllTransArrow != Base.UI.Form.IfShowAllTransArrow))
				{
					bool_8 = true;
				}
				this.method_201(bool_8 || !this.TransArrowList.Any<TransArrow>());
				this.IfShowNoTransArrow = Base.UI.Form.IfShowNoTransArrow;
				this.IfShowAllTransArrow = Base.UI.Form.IfShowAllTransArrow;
			}
		}

		// Token: 0x06001118 RID: 4376 RVA: 0x00074E28 File Offset: 0x00073028
		private void method_201(bool bool_8)
		{
			ChartCS.Class243 @class = new ChartCS.Class243();
			@class.chartCS_0 = this;
			if (base.PeriodHisDataList != null)
			{
				if (bool_8)
				{
					this.method_203();
					this.TransArrowList = new List<TransArrow>();
				}
				List<Transaction> list = new List<Transaction>();
				Class13 class2 = this.method_202();
				@class.dateTime_0 = class2.BeginDate;
				@class.dateTime_1 = class2.EndDate;
				if (Base.UI.Form.IfShowAllTransArrow)
				{
					list = Base.Trading.Transactions.Where(new Func<Transaction, bool>(@class.method_0)).ToList<Transaction>();
				}
				else
				{
					ChartCS.Class244 class3 = new ChartCS.Class244();
					class3.class243_0 = @class;
					class3.ienumerable_0 = Base.Trading.CurrHisTransList.Where(new Func<ShownHisTrans, bool>(class3.class243_0.method_1));
					list = Base.Trading.Transactions.Where(new Func<Transaction, bool>(class3.method_0)).ToList<Transaction>();
				}
				if (list != null && list.Any<Transaction>())
				{
					using (List<Transaction>.Enumerator enumerator = list.GetEnumerator())
					{
						while (enumerator.MoveNext())
						{
							ChartCS.Class246 class4 = new ChartCS.Class246();
							class4.transaction_0 = enumerator.Current;
							if (bool_8)
							{
								new TransArrow(this, class4.transaction_0);
							}
							else
							{
								IEnumerable<TransArrow> source = this.TransArrowList.Where(new Func<TransArrow, bool>(class4.method_0));
								if (source.Any<TransArrow>())
								{
									source.First<TransArrow>().method_10();
								}
								else
								{
									new TransArrow(this, class4.transaction_0);
								}
							}
						}
					}
				}
				if (this.TransArrowList != null)
				{
					using (List<TransArrow>.Enumerator enumerator2 = this.TransArrowList.GetEnumerator())
					{
						while (enumerator2.MoveNext())
						{
							ChartCS.Class247 class5 = new ChartCS.Class247();
							class5.transArrow_0 = enumerator2.Current;
							if (list == null || !list.Exists(new Predicate<Transaction>(class5.method_0)))
							{
								class5.transArrow_0.method_10();
							}
						}
					}
				}
			}
		}

		// Token: 0x06001119 RID: 4377 RVA: 0x00075020 File Offset: 0x00073220
		private Class13 method_202()
		{
			int num = base.ChtCtrl.IndexOfFirstItemShown - 1;
			int num2 = base.ChtCtrl.IndexOfLastItemShown;
			if (base.IsInRetroMode)
			{
				num = base.ChtCtrl_KLine.RetroModeFirstItemIdx - 1;
				num2 = base.ChtCtrl_KLine.RetroModeLastItemIdx;
			}
			int count = base.PeriodHisDataList.Count;
			Class13 result;
			if (count == 0)
			{
				Class182.smethod_0(new Exception("this.PeriodHisDataList.Count = 0!"));
				result = null;
			}
			else
			{
				if (num >= count)
				{
					num = count - 1;
				}
				if (num2 < 0)
				{
					num2 = 0;
				}
				DateTime dateTime_ = (num < 0) ? DateTime.MinValue : base.PeriodHisDataList.Keys[num];
				DateTime dateTime_2 = (num2 >= count) ? base.PeriodHisDataList.Keys.Last<DateTime>() : base.PeriodHisDataList.Keys[num2];
				result = new Class13(dateTime_, dateTime_2);
			}
			return result;
		}

		// Token: 0x0600111A RID: 4378 RVA: 0x000750EC File Offset: 0x000732EC
		private void method_203()
		{
			if (this.TransArrowList != null && this.TransArrowList.Any<TransArrow>())
			{
				foreach (TransArrow transArrow in this.TransArrowList)
				{
					try
					{
						if (transArrow.ArrowImgObj != null)
						{
							base.GraphPane.GraphObjList.Remove(transArrow.ArrowImgObj);
						}
						base.GraphPane.GraphObjList.RemoveAll(new Predicate<GraphObj>(ChartCS.<>c.<>9.method_1));
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
				this.TransArrowList = new List<TransArrow>();
			}
		}

		// Token: 0x0600111B RID: 4379 RVA: 0x000751C8 File Offset: 0x000733C8
		private TransArrow method_204(GraphObj graphObj_0)
		{
			ChartCS.Class248 @class = new ChartCS.Class248();
			@class.graphObj_0 = graphObj_0;
			if (@class.graphObj_0 != null && @class.graphObj_0 is ImageObj)
			{
				TransArrow result;
				try
				{
					IEnumerable<TransArrow> source = this.TransArrowList.Where(new Func<TransArrow, bool>(@class.method_0));
					if (!source.Any<TransArrow>())
					{
						goto IL_56;
					}
					result = source.First<TransArrow>();
				}
				catch
				{
					result = null;
				}
				return result;
			}
			IL_56:
			return null;
		}

		// Token: 0x0600111C RID: 4380 RVA: 0x00075240 File Offset: 0x00073440
		private TransArrow method_205(Transaction transaction_0)
		{
			ChartCS.Class249 @class = new ChartCS.Class249();
			@class.transaction_0 = transaction_0;
			if (@class.transaction_0 != null && this.TransArrowList != null)
			{
				IEnumerable<TransArrow> source = this.TransArrowList.Where(new Func<TransArrow, bool>(@class.method_0));
				if (source.Any<TransArrow>())
				{
					return source.First<TransArrow>();
				}
			}
			return null;
		}

		// Token: 0x0600111D RID: 4381 RVA: 0x00075298 File Offset: 0x00073498
		private void method_206()
		{
			foreach (TransArrow transArrow in this.TransArrowList)
			{
				transArrow.method_10();
			}
		}

		// Token: 0x0600111E RID: 4382 RVA: 0x00007436 File Offset: 0x00005636
		public void method_207()
		{
			this.method_208(false);
		}

		// Token: 0x0600111F RID: 4383 RVA: 0x000752EC File Offset: 0x000734EC
		private void method_208(bool bool_8)
		{
			SymbDataSet symbDataSet = base.ChtCtrl.SymbDataSet;
			bool flag = symbDataSet.CurrSymbol.IsStock && symbDataSet.CurrSymbStSpltList != null && Base.UI.Form.StockRestorationMethod != null && Base.UI.Form.StockRestorationMethod.Value == StockRestorationMethod.Later;
			if (Base.UI.Form.IfDisableTsOdrLine || bool_8 || flag)
			{
				if (this.TOdrLineList != null && this.TOdrLineList.Any<TOdrLine>())
				{
					this.method_222();
				}
				if (Base.UI.Form.IfDisableTsOdrLine || flag)
				{
					return;
				}
			}
			if (bool_8)
			{
				this.TOdrLineList = null;
			}
			else
			{
				if (this.TOdrLineList != null)
				{
					if (this.TOdrLineList.Any<TOdrLine>())
					{
						foreach (TOdrLine todrLine in this.TOdrLineList)
						{
							todrLine.vmethod_2();
						}
						this.TOdrLineList.RemoveAll(new Predicate<TOdrLine>(ChartCS.<>c.<>9.method_2));
					}
					return;
				}
				this.TOdrLineList = new List<TOdrLine>();
			}
			foreach (Transaction transaction_ in Base.Trading.smethod_143(Base.Acct.CurrAccount.ID, base.Symbol.ID))
			{
				new Class68(this, transaction_);
			}
			if (Base.Trading.CurrOrdersList != null && Base.Trading.CurrOrdersList.Any<ShownOrder>())
			{
				foreach (ShownOrder order_ in Base.Trading.CurrOrdersList.Where(new Func<ShownOrder, bool>(this.method_282)))
				{
					new Class67(this, order_);
				}
			}
			if (Base.Trading.ShownCondOrdersList != null && Base.Trading.ShownCondOrdersList.Any<ShownCondOrder>())
			{
				foreach (ShownCondOrder condOrder_ in Base.Trading.ShownCondOrdersList.Where(new Func<ShownCondOrder, bool>(this.method_283)))
				{
					new Class66(this, condOrder_);
				}
			}
		}

		// Token: 0x06001120 RID: 4384 RVA: 0x00075558 File Offset: 0x00073758
		private void method_209(EventArgs17 eventArgs17_0)
		{
			ChartCS.Class250 @class = new ChartCS.Class250();
			@class.transaction_0 = eventArgs17_0.Transaction;
			if (@class.transaction_0.SymbolID == base.Symbol.ID)
			{
				if (!Base.UI.Form.IfShowNoTransArrow)
				{
					this.method_199(@class.transaction_0);
				}
				Enum17 transType = (Enum17)@class.transaction_0.TransType;
				if (transType != Enum17.const_1)
				{
					if (transType != Enum17.const_3)
					{
						if (Base.UI.Form.IfDisableTsOdrLine)
						{
							return;
						}
						try
						{
							List<Class68> todrLineTransList = this.TOdrLineTransList;
							if (todrLineTransList != null)
							{
								ChartCS.Class251 class2 = new ChartCS.Class251();
								class2.class68_0 = todrLineTransList.Single(new Func<Class68, bool>(@class.method_0));
								Transaction transaction = Base.Trading.Transactions.Single(new Func<Transaction, bool>(class2.method_0));
								if (transaction.OpenUnits != null && transaction.OpenUnits.Value == 0L)
								{
									this.method_221(class2.class68_0);
								}
								else
								{
									class2.class68_0.OpenTrans.OpenUnits = transaction.OpenUnits;
									class2.class68_0.vmethod_1();
								}
							}
							return;
						}
						catch
						{
							return;
						}
					}
				}
				if (!Base.UI.Form.IfDisableTsOdrLine)
				{
					try
					{
						new Class68(this, @class.transaction_0);
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
			}
		}

		// Token: 0x06001121 RID: 4385 RVA: 0x000756B4 File Offset: 0x000738B4
		private void method_210(EventArgs14 eventArgs14_0)
		{
			if (!Base.UI.Form.IfDisableTsOdrLine)
			{
				Order shownOrder = eventArgs14_0.ShownOrder;
				if (shownOrder.OrderStatus == 0)
				{
					try
					{
						new Class67(this, shownOrder);
					}
					catch
					{
					}
				}
			}
		}

		// Token: 0x06001122 RID: 4386 RVA: 0x000756FC File Offset: 0x000738FC
		private void method_211(EventArgs14 eventArgs14_0)
		{
			if (!Base.UI.Form.IfDisableTsOdrLine)
			{
				ChartCS.Class252 @class = new ChartCS.Class252();
				@class.order_0 = eventArgs14_0.ShownOrder;
				OrderStatus orderStatus = (OrderStatus)@class.order_0.OrderStatus;
				try
				{
					if (orderStatus == OrderStatus.Canceled || orderStatus == OrderStatus.Executed)
					{
						List<Class67> orderLineList = this.OrderLineList;
						if (orderLineList != null)
						{
							Class67 todrLine_ = orderLineList.Single(new Func<Class67, bool>(@class.method_0));
							this.method_221(todrLine_);
						}
					}
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
		}

		// Token: 0x06001123 RID: 4387 RVA: 0x00007441 File Offset: 0x00005641
		private void method_212(EventArgs14 eventArgs14_0)
		{
			this.method_207();
		}

		// Token: 0x06001124 RID: 4388 RVA: 0x0007577C File Offset: 0x0007397C
		private void method_213(EventArgs15 eventArgs15_0)
		{
			if (!Base.UI.Form.IfDisableTsOdrLine)
			{
				ShownCondOrder shownCondOrder = eventArgs15_0.ShownCondOrder;
				if (shownCondOrder.OrderStatus == OrderStatus.Active)
				{
					try
					{
						new Class66(this, shownCondOrder);
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
			}
		}

		// Token: 0x06001125 RID: 4389 RVA: 0x000757C8 File Offset: 0x000739C8
		private void method_214(EventArgs15 eventArgs15_0)
		{
			if (!Base.UI.Form.IfDisableTsOdrLine)
			{
				ChartCS.Class253 @class = new ChartCS.Class253();
				@class.condOrder_0 = eventArgs15_0.ShownCondOrder;
				OrderStatus orderStatus = @class.condOrder_0.OrderStatus;
				try
				{
					if (orderStatus == OrderStatus.Canceled || orderStatus == OrderStatus.Executed)
					{
						List<Class66> todrLineCondOrderList = this.TOdrLineCondOrderList;
						if (todrLineCondOrderList != null)
						{
							IEnumerable<Class66> source = todrLineCondOrderList.Where(new Func<Class66, bool>(@class.method_0));
							if (source.Any<Class66>())
							{
								this.method_221(source.First<Class66>());
							}
						}
					}
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
		}

		// Token: 0x06001126 RID: 4390 RVA: 0x00007311 File Offset: 0x00005511
		private void method_215(EventArgs15 eventArgs15_0)
		{
			this.method_208(true);
		}

		// Token: 0x06001127 RID: 4391 RVA: 0x0000744B File Offset: 0x0000564B
		private void method_216(object sender, EventArgs e)
		{
			this.method_200(true);
		}

		// Token: 0x06001128 RID: 4392 RVA: 0x00075854 File Offset: 0x00073A54
		public void method_217()
		{
			if (base.Symbol.IsStock && base.SymbDataSet.CurrSymbStSpltList != null && base.SymbDataSet.CurrSymbStSpltList.Any<StSplit>())
			{
				this.method_218();
				if (this.method_185(true, true))
				{
					Base.UI.smethod_136();
				}
			}
		}

		// Token: 0x06001129 RID: 4393 RVA: 0x000758A4 File Offset: 0x00073AA4
		private void method_218()
		{
			if (this.TransArrowList != null && this.TransArrowList.Any<TransArrow>())
			{
				for (int i = 0; i < this.TransArrowList.Count; i++)
				{
					TransArrow transArrow_ = this.TransArrowList[i];
					this.method_219(transArrow_);
				}
			}
		}

		// Token: 0x0600112A RID: 4394 RVA: 0x000758F4 File Offset: 0x00073AF4
		public void method_219(TransArrow transArrow_1)
		{
			if (transArrow_1 != null)
			{
				if (transArrow_1.Transaction.SymbolID == base.Symbol.ID)
				{
					if (base.SymbDataSet.ShouldChkRationedPrice)
					{
						TranStock tranStock = transArrow_1.Transaction as TranStock;
						if (tranStock != null)
						{
							transArrow_1.RationedPrice = new double?(this.method_220(tranStock));
						}
					}
					else if (Base.UI.Form.StockRestorationMethod != null && Base.UI.Form.StockRestorationMethod.Value == StockRestorationMethod.None)
					{
						transArrow_1.RationedPrice = null;
					}
				}
			}
		}

		// Token: 0x0600112B RID: 4395 RVA: 0x0007598C File Offset: 0x00073B8C
		public double method_220(TranStock tranStock_0)
		{
			double double_;
			if (tranStock_0.IsOpen)
			{
				double_ = Convert.ToDouble(tranStock_0.OriPrice);
			}
			else
			{
				double_ = Convert.ToDouble(tranStock_0.Price);
			}
			return base.SymbDataSet.method_98(double_, tranStock_0.CreateTime);
		}

		// Token: 0x0600112C RID: 4396 RVA: 0x00007456 File Offset: 0x00005656
		private void method_221(TOdrLine todrLine_0)
		{
			todrLine_0.method_4();
			this.TOdrLineList.Remove(todrLine_0);
		}

		// Token: 0x0600112D RID: 4397 RVA: 0x000759D4 File Offset: 0x00073BD4
		public void method_222()
		{
			if (this.TOdrLineList != null)
			{
				foreach (TOdrLine todrLine in this.TOdrLineList)
				{
					todrLine.method_4();
				}
				this.TOdrLineList = null;
			}
		}

		// Token: 0x0600112E RID: 4398 RVA: 0x00075A38 File Offset: 0x00073C38
		private void method_223(object sender, EventArgs0 e)
		{
			List<Class18> infoMineList = e.InfoMineList;
			try
			{
				ChartCS.Class254 @class = new ChartCS.Class254();
				@class.string_0 = base.Symbol.method_1();
				if (!string.IsNullOrEmpty(@class.string_0))
				{
					Class18 class2 = infoMineList.FirstOrDefault(new Func<Class18, bool>(@class.method_0));
					if (class2 != null)
					{
						SortedDictionary<DateTime, string> infoMineDict = class2.method_0();
						this.InfoMineDict = infoMineDict;
						this.method_224();
						base.ZedGraphControl.Refresh();
					}
				}
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x0600112F RID: 4399 RVA: 0x00075AC0 File Offset: 0x00073CC0
		private void method_224()
		{
			ChartCS.Class255 @class = new ChartCS.Class255();
			this.method_226();
			this.InfoMineList = null;
			if (base.PeriodHisDataList != null && this.InfoMineDict != null)
			{
				Class13 class2 = this.method_202();
				@class.dateTime_0 = class2.BeginDate;
				@class.dateTime_1 = class2.EndDate;
				if (this.InfoMineDict != null && this.InfoMineDict.Count > 0)
				{
					try
					{
						Dictionary<DateTime, string> idictionary_ = this.InfoMineDict.Where(new Func<KeyValuePair<DateTime, string>, bool>(@class.method_0)).ToDictionary(new Func<KeyValuePair<DateTime, string>, DateTime>(ChartCS.<>c.<>9.method_3), new Func<KeyValuePair<DateTime, string>, string>(ChartCS.<>c.<>9.method_4));
						this.method_225(idictionary_);
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
			}
		}

		// Token: 0x06001130 RID: 4400 RVA: 0x00075BAC File Offset: 0x00073DAC
		private void method_225(IDictionary<DateTime, string> idictionary_0)
		{
			if (idictionary_0 != null && idictionary_0.Count > 0)
			{
				this.InfoMineList = new List<Class20>();
				foreach (KeyValuePair<DateTime, string> keyValuePair in idictionary_0)
				{
					Class20 item = new Class20(this, keyValuePair.Key, keyValuePair.Value);
					this.InfoMineList.Add(item);
				}
			}
		}

		// Token: 0x06001131 RID: 4401 RVA: 0x00075C28 File Offset: 0x00073E28
		private void method_226()
		{
			if (base.GraphPane != null && base.GraphPane.GraphObjList != null)
			{
				base.GraphPane.GraphObjList.RemoveAll(new Predicate<GraphObj>(ChartCS.<>c.<>9.method_5));
			}
		}

		// Token: 0x06001132 RID: 4402 RVA: 0x00075C7C File Offset: 0x00073E7C
		private Class20 method_227(GraphObj graphObj_0)
		{
			ChartCS.Class256 @class = new ChartCS.Class256();
			@class.graphObj_0 = graphObj_0;
			Class20 result;
			if (@class.graphObj_0 != null && @class.graphObj_0 is TextObj && this.InfoMineList != null)
			{
				result = this.InfoMineList.FirstOrDefault(new Func<Class20, bool>(@class.method_0));
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001133 RID: 4403 RVA: 0x00075CD4 File Offset: 0x00073ED4
		private void method_228()
		{
			if (this.InfoMineList != null)
			{
				foreach (Class20 @class in this.InfoMineList)
				{
					@class.ShowNoteBox = false;
				}
			}
		}

		// Token: 0x06001134 RID: 4404 RVA: 0x00075D30 File Offset: 0x00073F30
		protected override void vmethod_27(ZedGraphControl zedGraphControl_1, ContextMenuStrip contextMenuStrip_0, Point point_0, ZedGraphControl.ContextMenuObjectState contextMenuObjectState_0)
		{
			ChartCS.Class257 @class = new ChartCS.Class257();
			contextMenuStrip_0.Items.Clear();
			@class.graphObj_0 = this.method_197(point_0);
			if (@class.graphObj_0 != null)
			{
				DrawObj drawObj = this.method_187(@class.graphObj_0);
				if (drawObj != null)
				{
					this.method_169();
					drawObj.method_10(this);
					this.method_171(contextMenuStrip_0, drawObj);
					return;
				}
				TransArrow transArrow = this.method_204(@class.graphObj_0);
				if (transArrow == null && @class.graphObj_0 is TextObj && @class.graphObj_0.Tag != null && @class.graphObj_0.Tag.ToString().StartsWith(TransArrow.string_1) && this.TransArrowList != null)
				{
					transArrow = this.TransArrowList.FirstOrDefault(new Func<TransArrow, bool>(@class.method_0));
				}
				if (transArrow != null)
				{
					Class68 class2 = this.method_195(transArrow.Transaction);
					if (class2 != null)
					{
						this.method_231(contextMenuStrip_0, class2);
						this.method_232(contextMenuStrip_0, class2);
						base.method_38(contextMenuStrip_0);
					}
					this.HighLightedTransArrow = transArrow;
					this.method_176(contextMenuStrip_0, transArrow);
					return;
				}
				TOdrLine todrLine = this.method_193(@class.graphObj_0);
				if (todrLine != null)
				{
					if (!(todrLine is Class67) && !(todrLine is Class66))
					{
						this.method_231(contextMenuStrip_0, todrLine);
						this.method_232(contextMenuStrip_0, todrLine);
						TransArrow transArrow2 = this.method_205((todrLine as Class68).OpenTrans);
						if (transArrow2 != null)
						{
							this.HighLightedTransArrow = transArrow2;
							base.method_38(contextMenuStrip_0);
							this.method_176(contextMenuStrip_0, transArrow2);
						}
						return;
					}
					this.method_229(contextMenuStrip_0, todrLine);
					this.method_230(contextMenuStrip_0, todrLine);
					return;
				}
			}
			double num;
			double num2;
			base.GraphPane.ReverseTransform(point_0, out num, out num2);
			base.vmethod_27(zedGraphControl_1, contextMenuStrip_0, point_0, contextMenuObjectState_0);
			if (contextMenuStrip_0.Items[0].Text.StartsWith("@") && num2 > 0.0 && num2 < Convert.ToDouble(79228162514264337593543950335m))
			{
				ToolStripItem[] array = new ToolStripItem[contextMenuStrip_0.Items.Count];
				contextMenuStrip_0.Items.CopyTo(array, 0);
				contextMenuStrip_0.Items.Clear();
				this.method_238(contextMenuStrip_0, num2);
				contextMenuStrip_0.Items.AddRange(array);
			}
		}

		// Token: 0x06001135 RID: 4405 RVA: 0x00075F68 File Offset: 0x00074168
		private void method_229(ContextMenuStrip contextMenuStrip_0, TOdrLine todrLine_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "修改...";
			toolStripMenuItem.Tag = todrLine_0;
			toolStripMenuItem.Click += this.method_233;
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06001136 RID: 4406 RVA: 0x00075FB0 File Offset: 0x000741B0
		private void method_230(ContextMenuStrip contextMenuStrip_0, TOdrLine todrLine_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			if (todrLine_0 is Class66)
			{
				toolStripMenuItem.Text = "取销";
			}
			else
			{
				toolStripMenuItem.Text = "撤销";
			}
			toolStripMenuItem.Tag = todrLine_0;
			toolStripMenuItem.Click += this.method_234;
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06001137 RID: 4407 RVA: 0x0007600C File Offset: 0x0007420C
		private void method_231(ContextMenuStrip contextMenuStrip_0, TOdrLine todrLine_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "平仓";
			toolStripMenuItem.Tag = todrLine_0;
			toolStripMenuItem.Click += this.method_236;
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06001138 RID: 4408 RVA: 0x00076054 File Offset: 0x00074254
		private void method_232(ContextMenuStrip contextMenuStrip_0, TOdrLine todrLine_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "止损止盈...";
			toolStripMenuItem.Tag = todrLine_0;
			toolStripMenuItem.Click += this.method_237;
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06001139 RID: 4409 RVA: 0x0007609C File Offset: 0x0007429C
		private void method_233(object sender, EventArgs e)
		{
			TOdrLine todrLine = (sender as ToolStripMenuItem).Tag as TOdrLine;
			if (todrLine is Class67)
			{
				new EditOrderForm((todrLine as Class67).Order.ID)
				{
					Owner = Base.UI.MainForm
				}.Show();
			}
			else if (todrLine is Class66)
			{
				new SetCondOrdForm((todrLine as Class66).CondOrder)
				{
					Owner = Base.UI.MainForm
				}.Show();
			}
		}

		// Token: 0x0600113A RID: 4410 RVA: 0x00076114 File Offset: 0x00074314
		private void method_234(object sender, EventArgs e)
		{
			TOdrLine todrLine_ = (sender as ToolStripMenuItem).Tag as TOdrLine;
			this.method_235(todrLine_);
		}

		// Token: 0x0600113B RID: 4411 RVA: 0x0007613C File Offset: 0x0007433C
		private void method_235(TOdrLine todrLine_0)
		{
			if (todrLine_0 is Class67)
			{
				Class67 @class = todrLine_0 as Class67;
				if (@class.Order != null)
				{
					Base.Trading.smethod_73(@class.Order.ID);
				}
			}
			else if (todrLine_0 is Class66)
			{
				Class66 class2 = todrLine_0 as Class66;
				if (class2.CondOrder != null)
				{
					Base.Trading.smethod_113(class2.CondOrder.ID);
				}
			}
		}

		// Token: 0x0600113C RID: 4412 RVA: 0x0000746D File Offset: 0x0000566D
		private void method_236(object sender, EventArgs e)
		{
			Base.Trading.smethod_52(((sender as ToolStripMenuItem).Tag as Class68).OpenTrans.ID);
		}

		// Token: 0x0600113D RID: 4413 RVA: 0x0007619C File Offset: 0x0007439C
		private void method_237(object sender, EventArgs e)
		{
			Class68 @class = (sender as ToolStripMenuItem).Tag as Class68;
			if (@class != null && @class.OpenTrans != null && Base.Data.smethod_52(@class.OpenTrans.SymbolID) != null)
			{
				new SetStopLimitForm
				{
					Tag = @class.OpenTrans,
					ShowInTaskbar = false
				}.ShowDialog();
			}
		}

		// Token: 0x0600113E RID: 4414 RVA: 0x000761F8 File Offset: 0x000743F8
		protected void method_238(ContextMenuStrip contextMenuStrip_0, double double_0)
		{
			this.decimal_0 = base.SymbDataSet.method_69(double_0);
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "@ " + this.decimal_0.ToString();
			toolStripMenuItem.ToolTipText = "指定价委托";
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			string str = " " + Base.UI.Form.TradingUnits.ToString() + (base.Symbol.IsFutures ? "手" : "股");
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = "买开" + str;
			toolStripMenuItem2.Click += this.method_239;
			toolStripMenuItem.DropDownItems.Add(toolStripMenuItem2);
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = "卖平" + str;
			toolStripMenuItem3.Click += this.method_240;
			toolStripMenuItem.DropDownItems.Add(toolStripMenuItem3);
			if (base.SymbDataSet.CurrSymblSupportsShort)
			{
				ToolStripMenuItem toolStripMenuItem4 = Base.UI.smethod_76();
				toolStripMenuItem4.Text = "卖开" + str;
				toolStripMenuItem4.Click += this.method_241;
				toolStripMenuItem.DropDownItems.Add(toolStripMenuItem4);
				ToolStripMenuItem toolStripMenuItem5 = Base.UI.smethod_76();
				toolStripMenuItem5.Text = "买平" + str;
				toolStripMenuItem5.Click += this.method_242;
				toolStripMenuItem.DropDownItems.Add(toolStripMenuItem5);
			}
		}

		// Token: 0x0600113F RID: 4415 RVA: 0x00007491 File Offset: 0x00005691
		private void method_239(object sender, EventArgs e)
		{
			if (Base.Trading.smethod_218(base.Symbol.ID, OrderType.Order_OpenLong, this.decimal_0))
			{
				Base.Trading.smethod_198(base.Symbol.ID, OrderType.Order_OpenLong, new decimal?(this.decimal_0));
			}
		}

		// Token: 0x06001140 RID: 4416 RVA: 0x000074CA File Offset: 0x000056CA
		private void method_240(object sender, EventArgs e)
		{
			if (Base.Trading.smethod_218(base.Symbol.ID, OrderType.Order_CloseLong, this.decimal_0))
			{
				Base.Trading.smethod_198(base.Symbol.ID, OrderType.Order_CloseLong, new decimal?(this.decimal_0));
			}
		}

		// Token: 0x06001141 RID: 4417 RVA: 0x00007503 File Offset: 0x00005703
		private void method_241(object sender, EventArgs e)
		{
			if (Base.Trading.smethod_218(base.Symbol.ID, OrderType.Order_OpenShort, this.decimal_0))
			{
				Base.Trading.smethod_198(base.Symbol.ID, OrderType.Order_OpenShort, new decimal?(this.decimal_0));
			}
		}

		// Token: 0x06001142 RID: 4418 RVA: 0x0000753C File Offset: 0x0000573C
		private void method_242(object sender, EventArgs e)
		{
			if (Base.Trading.smethod_218(base.Symbol.ID, OrderType.Order_CloseShort, this.decimal_0))
			{
				Base.Trading.smethod_198(base.Symbol.ID, OrderType.Order_CloseShort, new decimal?(this.decimal_0));
			}
		}

		// Token: 0x06001143 RID: 4419 RVA: 0x00076378 File Offset: 0x00074578
		protected override void vmethod_31(object sender, MouseEventArgs e)
		{
			base.vmethod_31(sender, e);
			if (Base.UI.DrawMode == DrawMode.Off)
			{
				GraphPane graphPane = base.GraphPane;
				PointF pointF_ = new PointF((float)e.X, (float)e.Y);
				GraphObj graphObj = this.method_189(pointF_);
				DrawObj drawObj = this.method_187(graphObj);
				if (drawObj != null)
				{
					if (!graphObj.Tag.ToString().EndsWith(DrawObj.string_0))
					{
						this.method_169();
						drawObj.method_10(this);
						if (base.ChtCtrl_KLine.SelectedInd != null)
						{
							base.ChtCtrl_KLine.SelectedInd.IsSelected = false;
						}
					}
				}
				else
				{
					this.method_169();
				}
			}
			else
			{
				DrawMode drawMode = Base.UI.DrawMode;
				switch (drawMode)
				{
				case DrawMode.Text:
				case DrawMode.ArwLine:
				case DrawMode.Line:
				case DrawMode.LineD:
				case DrawMode.LineV:
				case DrawMode.LineH:
				case DrawMode.LineP:
				case DrawMode.Square:
					break;
				default:
					switch (drawMode)
					{
					case DrawMode.EraseAllDrawObj:
						if (MessageBox.Show("删除当前图表上的全部画线？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
						{
							this.method_243();
						}
						Base.UI.DrawMode = DrawMode.Off;
						break;
					}
					break;
				}
			}
		}

		// Token: 0x06001144 RID: 4420 RVA: 0x00007575 File Offset: 0x00005775
		public void method_243()
		{
			this.method_244(true, true);
		}

		// Token: 0x06001145 RID: 4421 RVA: 0x000764B8 File Offset: 0x000746B8
		public void method_244(bool bool_8, bool bool_9)
		{
			List<DrawObj> list;
			if (bool_8)
			{
				list = new List<DrawObj>();
				using (List<DrawObj>.Enumerator enumerator = Base.UI.DrawObjList.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						DrawObj drawObj = enumerator.Current;
						if (drawObj.method_21(this))
						{
							list.Add(drawObj);
						}
					}
					goto IL_4E;
				}
			}
			list = Base.UI.DrawObjList;
			IL_4E:
			foreach (DrawObj drawObj2 in list)
			{
				drawObj2.vmethod_20(this);
				if (bool_9)
				{
					Base.UI.DrawObjList.Remove(drawObj2);
				}
			}
			if (bool_9)
			{
				Base.UI.smethod_136();
			}
		}

		// Token: 0x06001146 RID: 4422 RVA: 0x0007657C File Offset: 0x0007477C
		protected override bool zedGraphControl_0_MouseMoveEvent(ZedGraphControl zedGraphControl_1, MouseEventArgs mouseEventArgs_0)
		{
			PointF pointF = new PointF((float)mouseEventArgs_0.X, (float)mouseEventArgs_0.Y);
			GraphPane graphPane = base.MasterPane.FindChartRect(pointF);
			if (graphPane != null)
			{
				double num;
				double num2;
				graphPane.ReverseTransform(pointF, out num, out num2);
				PointD pointD = new PointD(num, num2);
				if (Base.UI.DrawMode == DrawMode.Off)
				{
					if (mouseEventArgs_0.Button == MouseButtons.Left && Base.UI.DrawingObj != null)
					{
						if (Base.UI.DrawObjBoxMovingFixPt != null)
						{
							double double_ = this.method_246(pointD.X);
							Base.UI.DrawingObj.vmethod_18(this, Base.UI.DrawObjBoxMovingFixPt.Value.X, Base.UI.DrawObjBoxMovingFixPt.Value.Y, double_, pointD.Y, true, false);
						}
						else if (Base.UI.DrawObjStartMovingMousePt != null)
						{
							Base.UI.smethod_8();
							double num3 = num - Base.UI.DrawObjStartMovingMousePt.Value.X;
							double num4 = num2 - Base.UI.DrawObjStartMovingMousePt.Value.Y;
							if (num3 != 0.0 || num4 != 0.0)
							{
								Base.UI.DrawingObj.method_14(this);
								Location location = Base.UI.DrawingObj.vmethod_6(this);
								if (location != null)
								{
									Base.UI.DrawingObj.vmethod_18(this, location.X1 + num3, location.Y1 + num4, location.X2 + num3, location.Y2 + num4, true, false);
								}
							}
						}
					}
					else
					{
						if (mouseEventArgs_0.Button == MouseButtons.Left && Base.UI.SeletedTOdrLine != null)
						{
							Base.UI.SeletedTOdrLine.HighLighted = true;
							Base.UI.SeletedTOdrLine.vmethod_4(pointD.Y);
							return true;
						}
						Base.UI.SeletedTOdrLine = null;
					}
					GraphObj graphObj = this.method_197(pointF);
					if (graphObj != null)
					{
						if (this.method_190(graphObj))
						{
							DrawObj drawObj = this.method_187(graphObj);
							if (drawObj != null)
							{
								if (graphObj.Tag.ToString().EndsWith(DrawObj.string_0))
								{
									if (base.ZedGraphControl.Cursor != Cursors.Cross)
									{
										base.ZedGraphControl.Cursor = Cursors.Cross;
									}
								}
								else if (base.ZedGraphControl.Cursor != Cursors.Hand)
								{
									base.ZedGraphControl.Cursor = Cursors.Hand;
								}
								drawObj.method_11(this);
								drawObj.MouseHovering = true;
							}
							this.method_162();
						}
						else if (this.method_191(graphObj))
						{
							TOdrLine todrLine = this.method_193(graphObj);
							if (todrLine != null)
							{
								if (base.ZedGraphControl.Cursor != Cursors.Hand)
								{
									base.ZedGraphControl.Cursor = Cursors.Hand;
								}
								if (!todrLine.HighLighted)
								{
									this.method_163();
									todrLine.HighLighted = true;
									if (todrLine is Class68 && mouseEventArgs_0.Button == MouseButtons.None)
									{
										Class68 @class = todrLine as Class68;
										TransArrow transArrow = this.method_205(@class.OpenTrans);
										if (transArrow != null)
										{
											transArrow.method_34();
										}
									}
								}
							}
						}
						else if (this.method_192(graphObj))
						{
							Class20 class2 = this.method_227(graphObj);
							Class20.smethod_0(graphObj.Tag as string);
							if (class2 != null)
							{
								class2.ShowNoteBox = true;
							}
						}
						else
						{
							this.method_245();
							this.method_170();
							TransArrow transArrow2 = this.method_204(graphObj);
							if (transArrow2 != null && transArrow2.ArrowImgObj.IsVisible)
							{
								if (mouseEventArgs_0.Button == MouseButtons.None)
								{
									transArrow2.method_34();
									Class68 class3 = this.method_195(transArrow2.Transaction);
									if (class3 != null)
									{
										this.method_163();
										class3.HighLighted = true;
									}
								}
							}
							else
							{
								this.method_162();
							}
						}
					}
					else
					{
						this.method_245();
						this.method_170();
					}
				}
				else
				{
					if (Base.UI.DrawMode != DrawMode.EraseAllDrawOdr)
					{
						if (Base.UI.DrawMode != DrawMode.EraseAllDrawObj)
						{
							this.method_160();
							goto IL_3F8;
						}
					}
					Cursor cursor = new Cursor(Class372.DelDraw.GetHicon());
					base.ZedGraphControl.Cursor = cursor;
					IL_3F8:
					if (Base.UI.IsInDrawObjMode)
					{
						if (Base.UI.DrawMode != DrawMode.EraseAllDrawObj && Base.UI.DrawMode != DrawMode.Text && base.SymbDataSet.CurrHisDataSet != null)
						{
							this.method_164(Base.UI.DrawMode, pointD);
						}
					}
					else if (Base.UI.IsInDrawOdrMode)
					{
						if (base.SymbDataSet.CurrHisDataSet == null)
						{
							return base.zedGraphControl_0_MouseMoveEvent(zedGraphControl_1, mouseEventArgs_0);
						}
						decimal decimal_ = base.SymbDataSet.method_69(num2);
						double min = graphPane.XAxis.Scale.Min;
						string text = "(左键确认，Esc取消) ";
						double num5 = base.GraphPane.YAxis.Scale.Max - base.GraphPane.YAxis.Scale.Min;
						if (base.SymbDataSet.HasValidDataSet)
						{
							double close = base.SymbDataSet.CurrHisData.Close;
							bool flag = Math.Abs(num2 - close) / num5 < 0.01 && ((Base.UI.DrawMode == DrawMode.ROpenOdr && Base.UI.ROpenSLCondOdr != null) || Base.UI.DrawMode == DrawMode.OpenLongOdr || Base.UI.DrawMode == DrawMode.OpenShrtOdr);
							double num6 = num2;
							if (flag)
							{
								num6 = close;
								decimal_ = Convert.ToDecimal(close);
							}
							DrawLineH drawLineH;
							if (Base.UI.DrawingObj == null)
							{
								drawLineH = new DrawLineH(this, num, num6, num, num6, true);
								this.string_10 = drawLineH.Tag;
								if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
								{
									drawLineH.LineColor = new Color?(Color.LightGray);
								}
								else
								{
									drawLineH.LineColor = new Color?(Color.Gray);
								}
								Base.UI.DrawingObj = drawLineH;
								if (Base.UI.DrawingObjTextUp == null)
								{
									if (Base.UI.DrawMode == DrawMode.ROpenOdr)
									{
										if (Base.UI.ROpenSLCondOdr == null)
										{
											text += "以损定量 - 确定止损价";
										}
										else
										{
											CondOrder ropenSLCondOdr = Base.UI.ROpenSLCondOdr;
											long num7 = Base.Trading.smethod_209(base.Symbol, decimal_, ropenSLCondOdr.CondPrice);
											OrderType orderType_ = this.method_251(ropenSLCondOdr, decimal_);
											if (num7 > 0L)
											{
												text = string.Concat(new object[]
												{
													"以损定量 - ",
													Base.Trading.smethod_35(orderType_),
													" 数量:",
													num7,
													text
												});
											}
										}
									}
									this.method_256(text, close, num6);
								}
								if (flag && Base.UI.DrawingObjTextDn == null)
								{
									this.method_257(close, num6);
								}
							}
							else
							{
								if (Base.UI.DrawingObjTextUp != null)
								{
									Base.UI.DrawingObjTextUp.Location = new Location(min, num6, CoordType.AxisXYScale, AlignH.Left, AlignV.Bottom);
									if (Base.UI.DrawMode == DrawMode.ROpenOdr && Base.UI.ROpenSLCondOdr != null)
									{
										CondOrder ropenSLCondOdr2 = Base.UI.ROpenSLCondOdr;
										OrderType orderType_2 = this.method_251(ropenSLCondOdr2, decimal_);
										long num8 = Base.Trading.smethod_209(base.Symbol, decimal_, ropenSLCondOdr2.CondPrice);
										text = string.Concat(new object[]
										{
											text,
											"以损定量 - ",
											Base.Trading.smethod_35(orderType_2),
											" 数量:",
											num8
										});
										Base.UI.DrawingObjTextUp.Text = text;
									}
								}
								if (flag)
								{
									if (Base.UI.DrawingObjTextDn != null)
									{
										Base.UI.DrawingObjTextDn.Location = new Location(min, num6, CoordType.AxisXYScale, AlignH.Left, AlignV.Top);
										text = "当前价(" + base.SymbDataSet.method_69(close).ToString() + ")开仓";
										Base.UI.DrawingObjTextDn.Text = text;
									}
									else
									{
										this.method_257(close, num6);
									}
								}
								else
								{
									this.method_254();
								}
								drawLineH = (Base.UI.DrawingObj as DrawLineH);
								if (drawLineH != null)
								{
									drawLineH.vmethod_18(this, num, num6, num, num6, true, false);
								}
							}
							if (drawLineH != null && drawLineH.Line != null)
							{
								if (flag)
								{
									drawLineH.Line.Line.Style = DashStyle.Solid;
								}
								else
								{
									drawLineH.Line.Line.Style = DashStyle.Dot;
								}
							}
						}
					}
				}
			}
			bool flag2 = base.zedGraphControl_0_MouseMoveEvent(zedGraphControl_1, mouseEventArgs_0);
			if (flag2 && mouseEventArgs_0.Button == MouseButtons.Left && Base.UI.DrawingObj == null)
			{
				this.method_200(false);
			}
			return flag2;
		}

		// Token: 0x06001147 RID: 4423 RVA: 0x00007581 File Offset: 0x00005781
		public void method_245()
		{
			this.method_160();
			this.method_162();
			this.method_163();
			this.method_228();
		}

		// Token: 0x06001148 RID: 4424 RVA: 0x0000759D File Offset: 0x0000579D
		public override void vmethod_1(int int_0)
		{
			this.method_245();
			base.vmethod_1(int_0);
		}

		// Token: 0x06001149 RID: 4425 RVA: 0x00076D60 File Offset: 0x00074F60
		public double method_246(double double_0)
		{
			int num = Convert.ToInt32(Math.Round(double_0)) - 1;
			int num2 = base.FirstItemIndex - 1 + num;
			if (num2 < 0)
			{
				int num3 = -num2;
				double_0 += (double)num3;
			}
			int count = base.HisDataPeriodSet.PeriodHisDataList.Count;
			if (num2 >= count - 1)
			{
				int num4 = num2 - count + 2;
				double_0 -= (double)num4;
			}
			return double_0;
		}

		// Token: 0x0600114A RID: 4426 RVA: 0x00076DC0 File Offset: 0x00074FC0
		private bool method_247(double double_0)
		{
			return this.method_246(double_0) == double_0;
		}

		// Token: 0x0600114B RID: 4427 RVA: 0x00076DDC File Offset: 0x00074FDC
		private bool method_248(object object_0, MouseEventArgs mouseEventArgs_0)
		{
			bool result;
			if (base.SymbDataSet.CurrHisDataSet == null)
			{
				result = false;
			}
			else
			{
				GraphPane graphPane = base.GraphPane;
				PointF pointF = new PointF((float)mouseEventArgs_0.X, (float)mouseEventArgs_0.Y);
				GraphPane graphPane2 = base.MasterPane.FindChartRect(pointF);
				if (graphPane2 != null)
				{
					double x;
					double y;
					graphPane2.ReverseTransform(pointF, out x, out y);
					PointD pointD = new PointD(x, y);
					if (Base.UI.DrawMode != DrawMode.Off && Base.UI.DrawMode != DrawMode.EraseAllDrawOdr)
					{
						if (Base.UI.IsInDrawObjMode && Base.UI.DrawingObj1stPt == null && this.method_247(pointD.X))
						{
							Base.UI.DrawingObj1stPt = new PointD?(pointD);
							if (Base.UI.DrawMode == DrawMode.Text)
							{
								DrawTxtWnd drawTxtWnd = new DrawTxtWnd();
								drawTxtWnd.DrawTxtSet += this.method_167;
								drawTxtWnd.FormClosedWithoutTxtSet += this.method_168;
								drawTxtWnd.ShowDialog();
							}
						}
					}
					else
					{
						GraphObj graphObj = this.method_197(pointF);
						DrawObj drawObj = this.method_187(graphObj);
						if (drawObj != null)
						{
							if (graphObj.Tag.ToString().EndsWith(DrawObj.string_0))
							{
								PointD pointD_ = new PointD(graphObj.Location.X1 + graphObj.Location.Width / 2.0, graphObj.Location.Y1 - graphObj.Location.Height / 2.0);
								Base.UI.DrawObjBoxMovingFixPt = new PointD?(drawObj.vmethod_19(this, pointD_));
							}
							else
							{
								Base.UI.DrawObjStartMovingMousePt = new PointD?(pointD);
							}
							Base.UI.DrawingObj = drawObj;
						}
						else
						{
							TOdrLine todrLine = this.method_193(graphObj);
							if (todrLine != null)
							{
								if (!(todrLine is Class68))
								{
									Base.UI.SeletedTOdrLine = todrLine;
								}
							}
							else
							{
								this.method_162();
							}
						}
					}
				}
				result = false;
			}
			return result;
		}

		// Token: 0x0600114C RID: 4428 RVA: 0x00076FA8 File Offset: 0x000751A8
		private bool method_249(object object_0, MouseEventArgs mouseEventArgs_0)
		{
			bool result;
			if (base.SymbDataSet.CurrHisDataSet == null)
			{
				result = false;
			}
			else
			{
				GraphPane graphPane = base.GraphPane;
				PointF pointF = new PointF((float)mouseEventArgs_0.X, (float)mouseEventArgs_0.Y);
				GraphPane graphPane2 = base.MasterPane.FindChartRect(pointF);
				if (graphPane2 != null)
				{
					double x;
					double num;
					graphPane2.ReverseTransform(pointF, out x, out num);
					PointD pointD = new PointD(x, num);
					if (Base.UI.IsInDrawObjMode)
					{
						if (Base.UI.DrawingObj1stPt != null)
						{
							if (Base.UI.DrawModeSupportsOneClickCreate && this.method_247(Base.UI.DrawingObj1stPt.Value.X))
							{
								this.method_164(Base.UI.DrawMode, Base.UI.DrawingObj1stPt.Value);
							}
							if ((Base.UI.DrawModeSupportsOneClickCreate || !Base.UI.DrawingObj1stPt.Equals(pointD)) && Base.UI.DrawingObj != null)
							{
								PointD? pointD2 = this.method_186(Base.UI.DrawingObj1stPt.Value);
								if (pointD2 == null)
								{
									pointD2 = Base.UI.DrawingObj1stPt;
								}
								PointD? pointD3 = this.method_186(pointD);
								if (pointD3 == null)
								{
									pointD3 = new PointD?(new PointD(this.method_246(pointD.X), pointD.Y));
								}
								if (!Base.UI.DrawModeSupportsOneClickCreate && Math.Round(Math.Abs(pointD2.Value.X - pointD3.Value.X)) < 1.0)
								{
									Base.UI.DrawingObj.vmethod_20(this);
								}
								else
								{
									if (Base.UI.DrawMode != DrawMode.EraseAllDrawObj && Base.UI.DrawMode != DrawMode.Text)
									{
										if (Base.UI.DrawMode != DrawMode.LineV)
										{
											if (Base.UI.DrawMode != DrawMode.LineH)
											{
												this.method_159(Base.UI.DrawMode, pointD2.Value, pointD3.Value);
												goto IL_1D3;
											}
										}
										this.method_159(Base.UI.DrawMode, pointD3.Value, pointD3.Value);
									}
									IL_1D3:
									Base.UI.smethod_136();
									Base.UI.DrawMode = DrawMode.Off;
								}
								Base.UI.DrawingObj1stPt = null;
								Base.UI.DrawingObj = null;
							}
						}
					}
					else
					{
						if (Base.UI.IsInDrawOdrMode)
						{
							double num2 = 0.0;
							if (Base.UI.DrawingObj != null)
							{
								num2 = Base.UI.DrawingObj.Location.Y;
								Base.UI.DrawingObj.vmethod_20(this);
								Base.UI.DrawingObj = null;
							}
							this.method_253();
							this.method_254();
							if (mouseEventArgs_0.Button == MouseButtons.Left)
							{
								decimal num3 = base.SymbDataSet.method_69(num);
								ComparisonOpt comparisonOpt_ = ComparisonOpt.BiggerOrEqual;
								if (num3 < Convert.ToDecimal(base.SymbDataSet.CurrHisData.Close))
								{
									comparisonOpt_ = ComparisonOpt.LessOrEqual;
								}
								if (Base.UI.DrawMode != DrawMode.OpenLongOdr)
								{
									if (Base.UI.DrawMode != DrawMode.OpenShrtOdr)
									{
										if (Base.UI.DrawMode != DrawMode.StopLimitOdr && Base.UI.DrawMode != DrawMode.PrftTakeOdr)
										{
											if (Base.UI.DrawMode != DrawMode.RevTransOdr)
											{
												if (Base.UI.DrawMode != DrawMode.ROpenOdr)
												{
													goto IL_603;
												}
												if (Base.UI.ROpenSLCondOdr == null)
												{
													CondOrder condOrder = Base.Trading.smethod_86(base.Symbol.ID, comparisonOpt_, num3, num3, OrderType.Order_CloseLong, 0m);
													this.method_196(condOrder);
													if (condOrder != null)
													{
														condOrder.IsROpen = true;
														Base.Trading.smethod_90(condOrder, true);
														Base.UI.ROpenSLCondOdr = condOrder;
														goto IL_603;
													}
													goto IL_603;
												}
												else
												{
													double close = base.SymbDataSet.CurrHisData.Close;
													if (num2 != 0.0 && num2 == close)
													{
														num3 = Convert.ToDecimal(close);
													}
													CondOrder ropenSLCondOdr = Base.UI.ROpenSLCondOdr;
													OrderType orderType = this.method_251(ropenSLCondOdr, num3);
													long num4 = Base.Trading.smethod_209(base.Symbol, num3, ropenSLCondOdr.CondPrice);
													if (num4 > 0L)
													{
														CondOrder condOrder2 = Base.Trading.smethod_86(base.Symbol.ID, comparisonOpt_, num3, num3, orderType, num4);
														ropenSLCondOdr.Units = num4;
														ropenSLCondOdr.ROpenCondOdrID = new int?(condOrder2.ID);
														if (orderType == OrderType.Order_OpenShort)
														{
															ropenSLCondOdr.OrderType = OrderType.Order_CloseShort;
															ropenSLCondOdr.ComparisonOpt = ComparisonOpt.BiggerOrEqual;
														}
													}
													else
													{
														Base.Trading.smethod_113(ropenSLCondOdr.ID);
													}
													Base.Trading.smethod_90(ropenSLCondOdr, false);
													Base.UI.ROpenSLCondOdr = null;
													if (Convert.ToDouble(num3) == base.SymbDataSet.CurrHisData.Close)
													{
														Base.Trading.smethod_102();
														goto IL_603;
													}
													goto IL_603;
												}
											}
										}
										if (Base.Trading.CurrOpenTransList == null || !Base.Trading.CurrOpenTransList.Where(new Func<ShownOpenTrans, bool>(this.method_284)).Any<ShownOpenTrans>())
										{
											MessageBox.Show("该合约无持仓！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
											goto IL_603;
										}
										ChartCS.Class258 @class = new ChartCS.Class258();
										IEnumerable<ShownOpenTrans> source = Base.Trading.CurrOpenTransList.Where(new Func<ShownOpenTrans, bool>(this.method_285));
										@class.shownOpenTrans_0 = source.Last<ShownOpenTrans>();
										source.Where(new Func<ShownOpenTrans, bool>(@class.method_0)).Sum(new Func<ShownOpenTrans, long?>(ChartCS.<>c.<>9.method_6));
										OrderType orderType_ = Base.Trading.smethod_174((Enum17)@class.shownOpenTrans_0.TransType);
										if (Base.UI.DrawMode == DrawMode.RevTransOdr)
										{
											if (@class.shownOpenTrans_0.TransType == 1)
											{
												orderType_ = OrderType.Order_CloseLongRevOpen;
											}
											else
											{
												orderType_ = OrderType.Order_CloseShortRevOpen;
											}
											Base.Trading.smethod_86(base.Symbol.ID, comparisonOpt_, num3, 0m, orderType_, Base.UI.Form.TradingUnits);
											goto IL_603;
										}
										Base.Trading.smethod_86(base.Symbol.ID, comparisonOpt_, num3, 0m, orderType_, Base.UI.Form.TradingUnits);
										goto IL_603;
									}
								}
								double close2 = base.SymbDataSet.CurrHisData.Close;
								if (num2 != 0.0 && num2 == close2)
								{
									num3 = Convert.ToDecimal(close2);
								}
								if (Base.UI.DrawMode == DrawMode.OpenLongOdr)
								{
									Base.Trading.smethod_86(base.Symbol.ID, comparisonOpt_, num3, num3, OrderType.Order_OpenLong, Base.UI.Form.TradingUnits);
								}
								else
								{
									Base.Trading.smethod_86(base.Symbol.ID, comparisonOpt_, num3, num3, OrderType.Order_OpenShort, Base.UI.Form.TradingUnits);
								}
								if (Convert.ToDouble(num3) == close2)
								{
									Base.Trading.smethod_102();
								}
							}
							else
							{
								Base.UI.DrawMode = DrawMode.Off;
								this.method_258();
							}
							IL_603:
							if (Base.UI.ROpenSLCondOdr == null)
							{
								Base.UI.DrawMode = DrawMode.Off;
							}
						}
						else if ((Base.UI.DrawObjStartMovingMousePt != null && (pointD.X != Base.UI.DrawObjStartMovingMousePt.Value.X || pointD.Y != Base.UI.DrawObjStartMovingMousePt.Value.Y)) || Base.UI.DrawObjBoxMovingFixPt != null)
						{
							DrawObj drawObj = this.method_188(pointF);
							if (drawObj == null && (Base.UI.DrawObjBoxMovingFixPt != null & Base.UI.DrawingObj != null))
							{
								drawObj = Base.UI.DrawingObj;
							}
							if (drawObj != null)
							{
								PointD pointD4 = new PointD(drawObj.Location.X1, drawObj.Location.Y1);
								PointD pointD5 = new PointD(drawObj.Location.X2, drawObj.Location.Y2);
								PointD? pointD6 = this.method_186(pointD4);
								if (pointD6 == null)
								{
									pointD6 = new PointD?(pointD4);
								}
								PointD? pointD7 = this.method_186(pointD5);
								if (pointD7 == null)
								{
									pointD7 = new PointD?(pointD5);
								}
								drawObj.vmethod_18(this, pointD6.Value.X, pointD6.Value.Y, pointD7.Value.X, pointD7.Value.Y, true, false);
								drawObj.method_5(this, drawObj.Location);
								drawObj.method_14(this);
								drawObj.method_10(this);
								Base.UI.smethod_136();
							}
						}
						else if (Base.UI.SeletedTOdrLine != null)
						{
							if (Base.UI.DrawMode == DrawMode.EraseAllDrawOdr)
							{
								if (Base.UI.SeletedTOdrLine is Class67)
								{
									Base.Trading.smethod_61((Base.UI.SeletedTOdrLine as Class67).Order.ID, OrderStatus.Canceled);
								}
								else if (Base.UI.SeletedTOdrLine is Class66)
								{
									Base.Trading.smethod_94((Base.UI.SeletedTOdrLine as Class66).CondOrder.ID, OrderStatus.Canceled);
								}
								Base.UI.DrawMode = DrawMode.Off;
							}
							else if (mouseEventArgs_0.Button == MouseButtons.Left)
							{
								Base.UI.SeletedTOdrLine.vmethod_4(pointD.Y);
								if (Base.UI.SeletedTOdrLine is Class67)
								{
									Base.Trading.smethod_27();
								}
								else if (Base.UI.SeletedTOdrLine is Class66)
								{
									Base.Trading.smethod_80();
								}
							}
						}
						Base.UI.DrawObjStartMovingMousePt = null;
						Base.UI.DrawObjBoxMovingFixPt = null;
						Base.UI.DrawingObj = null;
						Base.UI.DrawingObjTextUp = null;
						Base.UI.DrawingObjTextDn = null;
						Base.UI.SeletedTOdrLine = null;
					}
				}
				result = false;
			}
			return result;
		}

		// Token: 0x0600114D RID: 4429 RVA: 0x00077820 File Offset: 0x00075A20
		private TextObj method_250(string string_12, double double_0, double double_1, AlignV alignV_0)
		{
			double min = base.GraphPane.XAxis.Scale.Min;
			TextObj textObj = new TextObj(string_12, min, double_1, CoordType.AxisXYScale, AlignH.Left, alignV_0);
			textObj.FontSpec.Border.IsVisible = false;
			textObj.FontSpec.StringAlignment = StringAlignment.Near;
			textObj.FontSpec.Fill.IsVisible = false;
			textObj.FontSpec.FontColor = Color.Silver;
			base.GraphPane.GraphObjList.Add(textObj);
			return textObj;
		}

		// Token: 0x0600114E RID: 4430 RVA: 0x000778A4 File Offset: 0x00075AA4
		private OrderType method_251(CondOrder condOrder_0, decimal decimal_1)
		{
			OrderType result;
			if (decimal_1 < condOrder_0.CondPrice)
			{
				result = OrderType.Order_OpenShort;
				if (condOrder_0.OrderType != OrderType.Order_CloseShort)
				{
					condOrder_0.OrderType = OrderType.Order_CloseShort;
				}
				if (condOrder_0.ComparisonOpt != ComparisonOpt.BiggerOrEqual)
				{
					condOrder_0.ComparisonOpt = ComparisonOpt.BiggerOrEqual;
				}
			}
			else
			{
				result = OrderType.Order_OpenLong;
				if (condOrder_0.OrderType != OrderType.Order_CloseLong)
				{
					condOrder_0.OrderType = OrderType.Order_CloseLong;
				}
				if (condOrder_0.ComparisonOpt != ComparisonOpt.LessOrEqual)
				{
					condOrder_0.ComparisonOpt = ComparisonOpt.LessOrEqual;
				}
			}
			return result;
		}

		// Token: 0x0600114F RID: 4431 RVA: 0x0007790C File Offset: 0x00075B0C
		private void method_252()
		{
			if (Base.UI.DrawingObj != null && Base.UI.DrawingObj is DrawLineH && Base.UI.DrawingObj.Tag == this.string_10)
			{
				Base.UI.DrawingObj.vmethod_20(this);
				Base.UI.DrawingObj = null;
			}
			this.method_253();
			this.method_254();
		}

		// Token: 0x06001150 RID: 4432 RVA: 0x000075AE File Offset: 0x000057AE
		private void method_253()
		{
			this.method_255(Base.UI.DrawingObjTextUp);
			Base.UI.DrawingObjTextUp = null;
		}

		// Token: 0x06001151 RID: 4433 RVA: 0x000075C3 File Offset: 0x000057C3
		private void method_254()
		{
			this.method_255(Base.UI.DrawingObjTextDn);
			Base.UI.DrawingObjTextDn = null;
		}

		// Token: 0x06001152 RID: 4434 RVA: 0x000075D8 File Offset: 0x000057D8
		private void method_255(TextObj textObj_20)
		{
			if (textObj_20 != null && base.GraphPane.GraphObjList.Contains(textObj_20))
			{
				base.GraphPane.GraphObjList.Remove(textObj_20);
			}
		}

		// Token: 0x06001153 RID: 4435 RVA: 0x00007604 File Offset: 0x00005804
		private void method_256(string string_12, double double_0, double double_1)
		{
			if (Base.UI.DrawingObjTextUp == null)
			{
				Base.UI.DrawingObjTextUp = this.method_250(string_12, double_0, double_1, AlignV.Bottom);
			}
		}

		// Token: 0x06001154 RID: 4436 RVA: 0x00077964 File Offset: 0x00075B64
		private void method_257(double double_0, double double_1)
		{
			if (Base.UI.DrawingObjTextDn == null)
			{
				string string_ = "(当前价:" + base.SymbDataSet.method_69(double_0).ToString() + "开仓)";
				Base.UI.DrawingObjTextDn = this.method_250(string_, double_0, double_1, AlignV.Top);
			}
		}

		// Token: 0x06001155 RID: 4437 RVA: 0x0000761E File Offset: 0x0000581E
		protected override void zedGraphControl_0_MouseLeave(object sender, EventArgs e)
		{
			this.method_162();
			this.method_163();
			this.method_252();
			this.method_170();
			base.zedGraphControl_0_MouseLeave(sender, e);
		}

		// Token: 0x06001156 RID: 4438 RVA: 0x000779B0 File Offset: 0x00075BB0
		public void method_258()
		{
			if (Base.UI.DrawingObj != null && Base.UI.DrawingObj.method_20(this))
			{
				Base.UI.DrawingObj.vmethod_20(this);
				Base.UI.DrawingObj = null;
			}
			if (this.TOdrLineList != null)
			{
				for (int i = 0; i < this.TOdrLineList.Count; i++)
				{
					TOdrLine todrLine = this.TOdrLineList[i];
					if (todrLine is Class66)
					{
						Class66 @class = todrLine as Class66;
						if (@class.IsInROpenState)
						{
							this.method_235(@class);
						}
					}
				}
			}
			if (Base.UI.DrawingObjTextUp != null && base.GraphPane.GraphObjList.Contains(Base.UI.DrawingObjTextUp))
			{
				base.GraphPane.GraphObjList.Remove(Base.UI.DrawingObjTextUp);
				Base.UI.DrawingObjTextUp = null;
			}
			if (Base.UI.DrawingObjTextDn != null && base.GraphPane.GraphObjList.Contains(Base.UI.DrawingObjTextDn))
			{
				base.GraphPane.GraphObjList.Remove(Base.UI.DrawingObjTextDn);
				Base.UI.DrawingObjTextDn = null;
			}
			if (Base.UI.IsInDrawObjMode && Base.UI.DrawingObj1stPt == null)
			{
				Base.UI.DrawMode = DrawMode.Off;
			}
			this.method_160();
		}

		// Token: 0x06001157 RID: 4439 RVA: 0x00077AC4 File Offset: 0x00075CC4
		private void method_259(HisData hisData_0, bool bool_8)
		{
			try
			{
				DateTime dateTime_ = base.method_2(hisData_0);
				this.method_261(hisData_0, dateTime_, bool_8);
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x06001158 RID: 4440 RVA: 0x00077B00 File Offset: 0x00075D00
		private void method_260(int int_0)
		{
			DateTime dateTime_ = base.method_3(int_0);
			bool bool_ = false;
			bool flag = base.method_69(int_0);
			bool flag2;
			if (base.HisDataPeriodSet.PeriodType == PeriodType.ByMins)
			{
				int? periodUnits = base.HisDataPeriodSet.PeriodUnits;
				flag2 = (periodUnits.GetValueOrDefault() <= 60 & periodUnits != null);
			}
			else
			{
				flag2 = false;
			}
			if (flag2 && flag)
			{
				bool_ = true;
			}
			this.method_261(base.HisDataPeriodSet.PeriodHisDataList.Values[int_0], dateTime_, bool_);
		}

		// Token: 0x06001159 RID: 4441 RVA: 0x00077B7C File Offset: 0x00075D7C
		private void method_261(HisData hisData_0, DateTime dateTime_0, bool bool_8)
		{
			double date = XDate.DateTimeToXLDate(dateTime_0);
			double high = hisData_0.High;
			double low = hisData_0.Low;
			double open = hisData_0.Open;
			double close = hisData_0.Close;
			double vol = hisData_0.Volume.Value;
			StockPt stockPt = new StockPt(date, high, low, open, close, vol);
			this.ipointListEdit_0.Add(stockPt);
			if (bool_8)
			{
				stockPt.Tag = ChartKLine.string_9;
			}
		}

		// Token: 0x0600115A RID: 4442 RVA: 0x00077BEC File Offset: 0x00075DEC
		protected override void vmethod_18()
		{
			base.vmethod_18();
			if (base.ChtCtrl_KLine.IsInCrossReviewMode && base.ChtCtrl_KLine.RevCrossXVal != null && this.curveItem_0.Points.Count > 0)
			{
				GraphPane graphPane = base.GraphPane;
				int num = Convert.ToInt32(Math.Round(base.ChtCtrl_KLine.RevCrossXVal.Value));
				if (num > this.Curve.NPts)
				{
					num = this.Curve.NPts;
				}
				else if (num < 1)
				{
					num = 1;
				}
				int num2 = num - 1;
				if (num2 < 0)
				{
					num2 = 0;
				}
				else if (num2 >= this.curveItem_0.Points.Count)
				{
					num2 = this.curveItem_0.Points.Count - 1;
				}
				StockPt stockPt = (StockPt)this.Curve.Points[num2];
				int num3 = base.ChtCtrl_KLine.IndexOfLastItemShown;
				if (base.ChtCtrl_KLine.IsInRetroMode)
				{
					num3 = base.ChtCtrl_KLine.RetroModeLastItemIdx;
				}
				if (num3 < 0)
				{
					num3 = 0;
				}
				if (num3 >= base.HisDataPeriodSet.PeriodHisDataList.Count)
				{
					num3 = base.HisDataPeriodSet.PeriodHisDataList.Count - 1;
				}
				int num4 = num3 - (this.Curve.NPts - num) - 1;
				if (num4 < 0)
				{
					num4 = 0;
				}
				else if (num4 >= base.HisDataPeriodSet.PeriodHisDataList.Count)
				{
					num4 = base.HisDataPeriodSet.PeriodHisDataList.Count - 1;
				}
				HisData hisData = base.HisDataPeriodSet.PeriodHisDataList.Values[num3];
				HisData hisData2 = base.HisDataPeriodSet.PeriodHisDataList.Values[num4];
				HisData hisData3 = this.method_263();
				string str = base.Symbol.DigitNb.ToString();
				if (base.RevCrossYVal == null && !base.ChtCtrl_KLine.ChartsHasRevCrossYVal)
				{
					Color color = Color.Gray;
					if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
					{
						color = Color.Silver;
					}
					double close = stockPt.Close;
					LineObj lineObj = new LineObj(color, graphPane.XAxis.Scale.Min, close, graphPane.XAxis.Scale.Max, close);
					lineObj.Tag = ChartBase.string_1;
					graphPane.GraphObjList.Add(lineObj);
					base.method_56(1.0, close);
					ChartBase chartBase = base.IsXAxisVisible ? this : base.ChtCtrl_KLine.ChartWithXAxisShown;
					if (chartBase != null)
					{
						string string_ = hisData3.Date.ToString("g");
						string_ = base.method_52(string_);
						base.method_59(chartBase, string_, (double)num);
					}
				}
				Color color2 = this.method_264();
				bool isFutures = base.Symbol.IsFutures;
				if (base.RevInfoBox == null)
				{
					double num5 = this.vmethod_20();
					double double_ = -num5 / 2.0;
					List<double> list = new List<double>();
					double num6 = 0.02;
					double num7;
					if (isFutures)
					{
						num7 = 0.06125;
					}
					else
					{
						num7 = 0.07;
					}
					while (num6 < 1.0)
					{
						list.Add(num6);
						num6 += num7;
					}
					this.textObj_4 = base.method_62("开    盘", double_, list[0]);
					this.textObj_5 = base.method_63(string.Format("{0:F" + str + "}", stockPt.Open), 0.0, list[1]);
					this.textObj_6 = base.method_62("最    高", double_, list[2]);
					this.textObj_7 = base.method_63(string.Format("{0:F" + str + "}", stockPt.High), 0.0, list[3]);
					this.textObj_8 = base.method_62("最    低", double_, list[4]);
					this.textObj_9 = base.method_63(string.Format("{0:F" + str + "}", stockPt.Low), 0.0, list[5]);
					this.textObj_10 = base.method_62("收    盘", double_, list[6]);
					this.textObj_11 = base.method_63(string.Format("{0:F" + str + "}", stockPt.Close), 0.0, list[7]);
					this.textObj_12 = base.method_62("成交量", double_, list[8]);
					this.textObj_13 = base.method_63(Math.Round(stockPt.Vol).ToString(), 0.0, list[9]);
					this.textObj_14 = base.method_62("涨    跌", double_, list[10]);
					this.textObj_15 = base.method_63(string.Format("{0:F" + str + "}", stockPt.Close - hisData2.Close), 0.0, list[11]);
					this.textObj_16 = base.method_62("幅    度", double_, list[12]);
					this.textObj_17 = base.method_63(((stockPt.Close - hisData2.Close) / hisData2.Close).ToString("P"), 0.0, list[13]);
					if (isFutures)
					{
						this.textObj_18 = base.method_62("持    仓", double_, list[14]);
						this.textObj_19 = base.method_63((hisData3.Amount != null) ? Math.Round(hisData3.Amount.Value).ToString() : "N/A", 0.0, list[15]);
					}
					Color color_ = (Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.Silver : Color.MidnightBlue;
					base.method_65(this.textObj_4, color_);
					base.method_65(this.textObj_6, color_);
					base.method_65(this.textObj_8, color_);
					base.method_65(this.textObj_10, color_);
					base.method_65(this.textObj_12, color_);
					base.method_65(this.textObj_13, (Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.Yellow : Color.MidnightBlue);
					base.method_65(this.textObj_14, color_);
					base.method_65(this.textObj_16, color_);
					base.method_65(this.textObj_5, (stockPt.Open - hisData2.Close > 0.0) ? Color.Red : ((stockPt.Open - hisData2.Close == 0.0) ? color2 : Color.Green));
					base.method_65(this.textObj_7, (stockPt.High - hisData2.Close > 0.0) ? Color.Red : ((stockPt.High - hisData2.Close == 0.0) ? color2 : Color.Green));
					base.method_65(this.textObj_9, (stockPt.Low - hisData2.Close > 0.0) ? Color.Red : ((stockPt.Low - hisData2.Close == 0.0) ? color2 : Color.Green));
					base.method_65(this.textObj_11, (stockPt.Close - hisData2.Close > 0.0) ? Color.Red : ((stockPt.Close - hisData2.Close == 0.0) ? color2 : Color.Green));
					base.method_65(this.textObj_15, (stockPt.Close - hisData2.Close > 0.0) ? Color.Red : ((stockPt.Close - hisData2.Close == 0.0) ? color2 : Color.Green));
					base.method_65(this.textObj_17, (stockPt.Close - hisData2.Close > 0.0) ? Color.Red : ((stockPt.Close - hisData2.Close == 0.0) ? color2 : Color.Green));
					graphPane.GraphObjList.Add(this.textObj_4);
					graphPane.GraphObjList.Add(this.textObj_5);
					graphPane.GraphObjList.Add(this.textObj_6);
					graphPane.GraphObjList.Add(this.textObj_7);
					graphPane.GraphObjList.Add(this.textObj_8);
					graphPane.GraphObjList.Add(this.textObj_9);
					graphPane.GraphObjList.Add(this.textObj_10);
					graphPane.GraphObjList.Add(this.textObj_11);
					graphPane.GraphObjList.Add(this.textObj_12);
					graphPane.GraphObjList.Add(this.textObj_13);
					graphPane.GraphObjList.Add(this.textObj_14);
					graphPane.GraphObjList.Add(this.textObj_15);
					graphPane.GraphObjList.Add(this.textObj_16);
					graphPane.GraphObjList.Add(this.textObj_17);
					if (isFutures)
					{
						base.method_65(this.textObj_18, color_);
						base.method_65(this.textObj_19, color_);
						graphPane.GraphObjList.Add(this.textObj_18);
						graphPane.GraphObjList.Add(this.textObj_19);
					}
					base.RevInfoBox = base.method_64(num5, 1.0);
					graphPane.GraphObjList.Add(base.RevInfoBox);
					this.vmethod_15();
				}
				else if (!base.RevInfoBox.IsVisible)
				{
					base.method_66(true);
				}
				this.textObj_5.Text = base.method_54(stockPt.Open);
				this.textObj_7.Text = base.method_54(stockPt.High);
				this.textObj_9.Text = base.method_54(stockPt.Low);
				this.textObj_11.Text = base.method_54(stockPt.Close);
				this.textObj_13.Text = Math.Round(stockPt.Vol).ToString();
				this.textObj_15.Text = base.method_54(stockPt.Close - hisData2.Close);
				this.textObj_17.Text = ((stockPt.Close - hisData2.Close) / hisData2.Close).ToString("P");
				if (isFutures)
				{
					this.textObj_19.Text = ((hisData3.Amount != null) ? Math.Round(hisData3.Amount.Value).ToString() : "N/A");
				}
				this.textObj_5.FontSpec.FontColor = ((stockPt.Open - hisData2.Close > 0.0) ? Color.Red : ((stockPt.Open - hisData2.Close == 0.0) ? color2 : Color.Green));
				this.textObj_7.FontSpec.FontColor = ((stockPt.High - hisData2.Close > 0.0) ? Color.Red : ((stockPt.High - hisData2.Close == 0.0) ? color2 : Color.Green));
				this.textObj_9.FontSpec.FontColor = ((stockPt.Low - hisData2.Close > 0.0) ? Color.Red : ((stockPt.Low - hisData2.Close == 0.0) ? color2 : Color.Green));
				this.textObj_11.FontSpec.FontColor = ((stockPt.Close - hisData2.Close > 0.0) ? Color.Red : ((stockPt.Close - hisData2.Close == 0.0) ? color2 : Color.Green));
				this.textObj_15.FontSpec.FontColor = ((stockPt.Close - hisData2.Close > 0.0) ? Color.Red : ((stockPt.Close - hisData2.Close == 0.0) ? color2 : Color.Green));
				this.textObj_17.FontSpec.FontColor = ((stockPt.Close - hisData2.Close > 0.0) ? Color.Red : ((stockPt.Close - hisData2.Close == 0.0) ? color2 : Color.Green));
			}
		}

		// Token: 0x0600115B RID: 4443 RVA: 0x000788FC File Offset: 0x00076AFC
		private List<int> method_262()
		{
			List<int> list = new List<int>();
			for (int i = 0; i < this.curveItem_0.Points.Count; i++)
			{
				PointPair pointPair = this.curveItem_0.Points[i];
				if (pointPair.Tag != null && pointPair.Tag.ToString() == ChartKLine.string_9)
				{
					list.Add(i + 1);
				}
			}
			return list;
		}

		// Token: 0x0600115C RID: 4444 RVA: 0x0007896C File Offset: 0x00076B6C
		private HisData method_263()
		{
			HisData hisData = null;
			if (base.ChtCtrl_KLine.IsInCrossReviewMode)
			{
				if (base.ChtCtrl_KLine.RevCrossXVal != null)
				{
					int num = Convert.ToInt32(Math.Round(base.ChtCtrl_KLine.RevCrossXVal.Value));
					if (num > this.curveItem_0.Points.Count)
					{
						num = this.curveItem_0.Points.Count;
					}
					else if (num < 1)
					{
						num = 1;
					}
					if (num == this.curveItem_0.NPts && (!base.ChtCtrl_KLine.IsInRetroMode || (base.SymbDataSet.HasValidDataSet && base.ChtCtrl_KLine.IsInRetroMode && base.ChtCtrl_KLine.IndexOfLastItemShown == base.ChtCtrl_KLine.RetroModeLastItemIdx)))
					{
						hisData = base.SymbDataSet.CurrHisDataSet.CurrHisData;
					}
					else if (this.curveItem_0.Points.Count > 0)
					{
						int num2 = base.FirstItemIndex + num - 1;
						if (base.SymbDataSet.HasValidDataSet && num2 >= 0 && num2 < base.HisDataPeriodSet.PeriodHisDataList.Count)
						{
							hisData = base.HisDataPeriodSet.PeriodHisDataList.Values[num2];
						}
						else
						{
							Class182.smethod_0(new Exception("Could not retrieve currHd in RevCross!"));
							hisData = new HisData();
							int num3 = num - 1;
							if (num3 < 0)
							{
								num3 = 0;
							}
							else if (num3 >= this.curveItem_0.Points.Count)
							{
								num3 = this.curveItem_0.Points.Count - 1;
							}
							StockPt stockPt = (StockPt)this.curveItem_0.Points[num3];
							hisData.Open = stockPt.Open;
							hisData.High = stockPt.High;
							hisData.Low = stockPt.Low;
							hisData.Close = stockPt.Close;
							hisData.Volume = new double?(stockPt.Vol);
							hisData.Date = XDate.XLDateToDateTime(stockPt.Date);
						}
					}
				}
				else if (base.SymbDataSet.HasValidDataSet)
				{
					hisData = base.SymbDataSet.CurrHisDataSet.CurrHisData;
				}
			}
			return hisData;
		}

		// Token: 0x0600115D RID: 4445 RVA: 0x00078B98 File Offset: 0x00076D98
		private Color method_264()
		{
			Color result;
			if (Base.UI.Form.ChartTheme != ChartTheme.Classic)
			{
				result = Color.MidnightBlue;
			}
			else
			{
				result = Color.White;
			}
			return result;
		}

		// Token: 0x0600115E RID: 4446 RVA: 0x00078BC4 File Offset: 0x00076DC4
		private void method_265()
		{
			GraphPane graphPane = base.GraphPane;
			if (graphPane != null)
			{
				if (!graphPane.YAxis.Scale.MajorStepAuto)
				{
					graphPane.YAxis.Scale.MajorStepAuto = true;
				}
				if (!graphPane.YAxis.Scale.MaxAuto)
				{
					graphPane.YAxis.Scale.MaxAuto = true;
				}
				if (!graphPane.YAxis.Scale.MinAuto)
				{
					graphPane.YAxis.Scale.MinAuto = true;
				}
			}
		}

		// Token: 0x0600115F RID: 4447 RVA: 0x00078C48 File Offset: 0x00076E48
		private void method_266()
		{
			GraphPane graphPane = base.GraphPane;
			if (graphPane != null && graphPane.CurveList.Any<CurveItem>())
			{
				if (graphPane.YAxis.Scale.MaxAuto)
				{
					graphPane.YAxis.Scale.MaxAuto = false;
				}
				if (graphPane.YAxis.Scale.MinAuto)
				{
					graphPane.YAxis.Scale.MinAuto = false;
				}
				if (graphPane.YAxis.Scale.MajorStepAuto)
				{
					graphPane.YAxis.Scale.MajorStepAuto = false;
				}
				if (graphPane.YAxis.Type != AxisType.Linear)
				{
					graphPane.YAxis.Type = AxisType.Linear;
					base.ZedGraphControl.AxisChange();
				}
				double num;
				double num2;
				double num3;
				double num4;
				this.method_268(out num, out num2, out num3, out num4, graphPane);
				double num5 = num4 - num3;
				if (num5 < 9.0)
				{
					double num6 = 0.01;
					if (base.Symbol.LeastPriceVar != null)
					{
						num6 = (double)base.Symbol.LeastPriceVar.Value * 10.0;
					}
					if (num5 < num6)
					{
						num5 = num6;
					}
					if (base.Symbol.ExchangeID == 1 && (base.Symbol.Code.StartsWith("IF") || base.Symbol.Code.StartsWith("IH") || base.Symbol.Code.StartsWith("IC") || base.Symbol.Code.StartsWith("IM")))
					{
						if (graphPane.YAxis.Scale.MajorStep < 1.0)
						{
							graphPane.YAxis.Scale.MajorStep = 1.0;
						}
					}
					else
					{
						double num7 = num5 / 3.0;
						if (num7 < num6)
						{
							num7 = num6;
						}
						graphPane.YAxis.Scale.MajorStep = num7;
					}
				}
				else if (num5 >= 9.0 && num5 <= 15.0)
				{
					graphPane.YAxis.Scale.Max += (15.0 - num5) / 2.0;
					graphPane.YAxis.Scale.Min -= (15.0 - num5) / 2.0;
					if (graphPane.YAxis.Scale.MajorStep < 3.0)
					{
						graphPane.YAxis.Scale.MajorStep = 3.0;
					}
				}
				else if (num5 > 15.0 && num5 <= 30.0)
				{
					graphPane.YAxis.Scale.Max += (30.0 - num5) / 2.0;
					graphPane.YAxis.Scale.Min -= (30.0 - num5) / 2.0;
					graphPane.YAxis.Scale.MajorStep = 5.0;
				}
				else if (num5 > 30.0 && num5 <= 60.0)
				{
					graphPane.YAxis.Scale.MajorStep = 10.0;
				}
				else if (num5 > 60.0 && num5 <= 200.0)
				{
					graphPane.YAxis.Scale.MajorStep = Convert.ToDouble((Math.Round(Convert.ToDouble(num5 / 30.0), 0) - 1.0) * 10.0);
				}
				else if (num5 > 200.0 && num5 <= 450.0)
				{
					graphPane.YAxis.Scale.MajorStep = Convert.ToDouble((Math.Floor(Convert.ToDouble(num5 / 150.0)) - 1.0) * 50.0);
				}
				else if (num5 > 450.0)
				{
					graphPane.YAxis.Scale.MajorStep = Convert.ToDouble((Math.Floor(Convert.ToDouble(num5 / 300.0)) - 1.0) * 100.0);
				}
				double num8 = (double)base.vmethod_16() / (double)base.ChtCtrl.Height * 2.5;
				if (this.bool_6)
				{
					if (num3 >= 1.0)
					{
						graphPane.YAxis.Scale.Min = num3 - num5 * num8 * 0.85;
						graphPane.YAxis.Scale.Max = num4 + num5 * num8 * 3.25;
					}
					else
					{
						graphPane.YAxis.Scale.Min = num3 - num5 * num8 * 0.75;
						graphPane.YAxis.Scale.Max = num4 + num5 * num8 * 3.0;
					}
				}
				else
				{
					graphPane.YAxis.Scale.Min = num3 - num5 * num8 * 1.25;
					graphPane.YAxis.Scale.Max = num4 + num5 * num8 * 3.0;
				}
				if (this.IsYAxisCoordsLogType)
				{
					this.method_276();
				}
				base.ZedGraphControl.AxisChange();
			}
		}

		// Token: 0x06001160 RID: 4448 RVA: 0x000791FC File Offset: 0x000773FC
		private string method_267(GraphPane graphPane_0, Axis axis_0, double double_0, int int_0)
		{
			return base.method_70(double_0);
		}

		// Token: 0x06001161 RID: 4449 RVA: 0x00079214 File Offset: 0x00077414
		private void method_268(out double double_0, out double double_1, out double double_2, out double double_3, GraphPane graphPane_0)
		{
			double num = 0.0;
			double num2 = 0.0;
			double num3 = 0.0;
			double num4 = 0.0;
			double? num5 = null;
			double? num6 = null;
			double? num7 = null;
			double? num8 = null;
			CurveList curveList = graphPane_0.CurveList;
			foreach (CurveItem curveItem in curveList)
			{
				if (curveItem.IsVisible && (curveItem.Tag == null || !(curveItem.Tag as string).EndsWith("NODRAW")))
				{
					if ((curveItem is BarItem && (graphPane_0.BarSettings.Type == BarType.Stack || graphPane_0.BarSettings.Type == BarType.PercentStack)) || (curveItem is LineItem && graphPane_0.LineType == LineType.Stack))
					{
						curveList.GetStackRange(graphPane_0, curveItem, out num, out num3, out num2, out num4);
					}
					else
					{
						curveItem.GetRange(out num, out num2, out num3, out num4, false, true, graphPane_0);
					}
					Scale scale = curveItem.GetYAxis(graphPane_0).Scale;
					Scale scale2 = curveItem.GetXAxis(graphPane_0).Scale;
					bool isAnyOrdinal = scale.IsAnyOrdinal;
					bool isAnyOrdinal2 = scale2.IsAnyOrdinal;
					if (isAnyOrdinal && !curveItem.IsOverrideOrdinal)
					{
						num3 = 1.0;
						num4 = (double)curveItem.NPts;
					}
					if (isAnyOrdinal2 && !curveItem.IsOverrideOrdinal)
					{
						num = 1.0;
						num2 = (double)curveItem.NPts;
					}
					if (curveItem.IsBar)
					{
						if (graphPane_0.BarSettings.Base != BarBase.X)
						{
							if (graphPane_0.BarSettings.Base != BarBase.X2)
							{
								if (!(curveItem is HiLowBarItem))
								{
									if (num > 0.0)
									{
										num = 0.0;
									}
									else if (num2 < 0.0)
									{
										num2 = 0.0;
									}
								}
								if (!isAnyOrdinal)
								{
									num3 -= graphPane_0.BarSettings.ClusterScaleWidth / 2.0;
									num4 += graphPane_0.BarSettings.ClusterScaleWidth / 2.0;
									goto IL_276;
								}
								goto IL_276;
							}
						}
						if (!(curveItem is HiLowBarItem))
						{
							if (num3 > 0.0)
							{
								num3 = 0.0;
							}
							else if (num4 < 0.0)
							{
								num4 = 0.0;
							}
						}
						if (!isAnyOrdinal2)
						{
							num -= graphPane_0.BarSettings.ClusterScaleWidth / 2.0;
							num2 += graphPane_0.BarSettings.ClusterScaleWidth / 2.0;
						}
					}
					IL_276:
					if (num5 == null)
					{
						num5 = new double?(num);
					}
					else
					{
						double? num9 = num5;
						double num10 = num;
						if (num9.GetValueOrDefault() > num10 & num9 != null)
						{
							num5 = new double?(num);
						}
					}
					if (num6 == null)
					{
						num6 = new double?(num2);
					}
					else
					{
						double? num9 = num6;
						double num10 = num2;
						if (num9.GetValueOrDefault() < num10 & num9 != null)
						{
							num6 = new double?(num2);
						}
					}
					if (num7 == null)
					{
						num7 = new double?(num3);
					}
					else
					{
						double? num9 = num7;
						double num10 = num3;
						if (num9.GetValueOrDefault() > num10 & num9 != null)
						{
							num7 = new double?(num3);
						}
					}
					if (num8 == null)
					{
						num8 = new double?(num4);
					}
					else
					{
						double? num9 = num8;
						double num10 = num4;
						if (num9.GetValueOrDefault() < num10 & num9 != null)
						{
							num8 = new double?(num4);
						}
					}
				}
			}
			if (num5 == null)
			{
				num5 = new double?(num);
			}
			if (num6 == null)
			{
				num6 = new double?(num2);
			}
			if (num7 == null)
			{
				num7 = new double?(num3);
			}
			if (num8 == null)
			{
				num8 = new double?(num4);
			}
			double_0 = num5.Value;
			double_1 = num6.Value;
			double_2 = num7.Value;
			double_3 = num8.Value;
		}

		// Token: 0x06001162 RID: 4450 RVA: 0x0007961C File Offset: 0x0007781C
		public override void vmethod_4(int int_0)
		{
			for (int i = base.method_4(int_0); i <= int_0; i++)
			{
				this.method_260(i);
			}
			base.vmethod_4(int_0);
		}

		// Token: 0x06001163 RID: 4451 RVA: 0x0007964C File Offset: 0x0007784C
		public override void vmethod_5(HisData hisData_0)
		{
			bool bool_ = false;
			if (base.HisDataPeriodSet.IsPeriod1m && base.SymbDataSet.method_35(hisData_0))
			{
				bool_ = true;
			}
			this.method_259(hisData_0, bool_);
			base.vmethod_5(hisData_0);
		}

		// Token: 0x06001164 RID: 4452 RVA: 0x0007968C File Offset: 0x0007788C
		protected override void vmethod_19()
		{
			foreach (Indicator indicator in base.IndList)
			{
				string text = base.HeaderTextObj.Text;
				int num = text.IndexOf(Environment.NewLine);
				if (num < 0)
				{
					num = text.Length;
				}
				base.HeaderTextObj.Text = text.Substring(0, num) + Environment.NewLine + indicator.GetInfoText();
			}
		}

		// Token: 0x06001165 RID: 4453 RVA: 0x00079724 File Offset: 0x00077924
		public void method_269()
		{
			string text = string.Empty;
			if (base.IndList != null)
			{
				foreach (Indicator indicator in base.IndList)
				{
					string infoText = indicator.GetInfoText();
					text = text + infoText + "     ";
				}
			}
			string text2 = base.HeaderTextObj.Text;
			int num = text2.IndexOf(Environment.NewLine);
			if (num < 0)
			{
				num = text2.Length;
			}
			string str = "";
			if (this.bool_6)
			{
				str = "对数坐标 ";
			}
			string text3 = text2.Substring(0, num);
			int num2 = text3.IndexOf("对数坐标");
			if (num2 >= 0)
			{
				text3 = text3.Substring(0, num2);
			}
			base.HeaderTextObj.Text = text3 + str + Environment.NewLine + text;
		}

		// Token: 0x06001166 RID: 4454 RVA: 0x00079810 File Offset: 0x00077A10
		public override void vmethod_9(int int_0, HDTick hdtick_0, bool bool_8)
		{
			HisData hisData_ = Base.Data.smethod_114(hdtick_0);
			this.vmethod_7(int_0, hisData_, bool_8);
		}

		// Token: 0x06001167 RID: 4455 RVA: 0x00079830 File Offset: 0x00077A30
		public override void vmethod_7(int int_0, HisData hisData_0, bool bool_8)
		{
			HisData hisData = hisData_0.Clone();
			if (bool_8)
			{
				StockPt stockPt = this.ipointListEdit_0[this.ipointListEdit_0.Count - 1] as StockPt;
				stockPt.Close = hisData_0.Close;
				stockPt.Vol += hisData_0.Volume.Value;
				hisData = base.method_90(int_0, hisData_0);
				if (hisData != null)
				{
					stockPt.High = hisData.High;
					stockPt.Low = hisData.Low;
				}
				this.method_271(hisData_0, stockPt);
			}
			else
			{
				this.method_270(hisData_0);
			}
			base.vmethod_7(int_0, hisData_0, bool_8);
		}

		// Token: 0x06001168 RID: 4456 RVA: 0x000798D0 File Offset: 0x00077AD0
		public void method_270(HisData hisData_0)
		{
			StockPt stockPt = this.ipointListEdit_0[this.ipointListEdit_0.Count - 1] as StockPt;
			if (hisData_0.High > stockPt.High)
			{
				stockPt.High = hisData_0.High;
			}
			if (hisData_0.Low < stockPt.Low)
			{
				stockPt.Low = hisData_0.Low;
			}
			stockPt.Close = hisData_0.Close;
			stockPt.Vol += hisData_0.Volume.Value;
			this.method_271(hisData_0, stockPt);
		}

		// Token: 0x06001169 RID: 4457 RVA: 0x00079968 File Offset: 0x00077B68
		private void method_271(HisData hisData_0, PointPair pointPair_0)
		{
			if (base.HisDataPeriodSet.PeriodType == PeriodType.ByMins)
			{
				int? periodUnits = base.HisDataPeriodSet.PeriodUnits;
				if ((periodUnits.GetValueOrDefault() <= 60 & periodUnits != null) && base.SymbDataSet.method_35(hisData_0))
				{
					pointPair_0.Tag = ChartKLine.string_9;
				}
			}
		}

		// Token: 0x0600116A RID: 4458 RVA: 0x000799C4 File Offset: 0x00077BC4
		public override void vmethod_22(HisData hisData_0)
		{
			try
			{
				if (hisData_0 != null && base.ChtCtrl_KLine.IsInCrossReviewMode)
				{
					hisData_0 = this.method_263();
				}
				string text = (!Base.UI.Form.IsInBlindTestMode || Base.UI.Form.IsSingleBlindTest) ? base.Symbol.CNName : "●●";
				string text2 = "";
				if (base.Symbol.IsStock)
				{
					if (Base.UI.Form.StockRestorationMethod == null)
					{
						Base.UI.Form.StockRestorationMethod = new StockRestorationMethod?(StockRestorationMethod.Prior);
					}
					StockRestorationMethod? stockRestorationMethod = Base.UI.Form.StockRestorationMethod;
					if (stockRestorationMethod.GetValueOrDefault() == StockRestorationMethod.Prior & stockRestorationMethod != null)
					{
						text2 = ",前复权";
					}
					else
					{
						stockRestorationMethod = Base.UI.Form.StockRestorationMethod;
						if (stockRestorationMethod.GetValueOrDefault() == StockRestorationMethod.Later & stockRestorationMethod != null)
						{
							text2 = ",后复权";
						}
					}
				}
				if (base.HeaderTextObj != null)
				{
					base.HeaderTextObj.Text = string.Concat(new string[]
					{
						text,
						"<",
						base.HisDataPeriodSet.PeriodDesc,
						text2,
						">"
					});
					if (!base.HeaderTextObj.IsVisible)
					{
						base.HeaderTextObj.IsVisible = true;
					}
				}
				this.method_269();
				if (base.GraphPane != null && base.GraphPane.YAxis.Scale.IsReverse)
				{
					TextObj headerTextObj = base.HeaderTextObj;
					headerTextObj.Text += ChtCtrl.string_0;
				}
				if (hisData_0 != null)
				{
					this.textObj_3.Text = string.Concat(new string[]
					{
						"[",
						string.Format("{0:u}", hisData_0.Date).Remove(19),
						Base.UI.Form.IfShowDayOfWeek ? (" " + Utility.GetCNDayOfWeek(hisData_0.Date)) : string.Empty,
						"]    ",
						base.method_54(hisData_0.Close)
					});
				}
				this.textObj_3.Text = base.method_52(this.textObj_3.Text);
				if (!this.textObj_3.IsVisible)
				{
					this.textObj_3.IsVisible = true;
				}
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x0600116B RID: 4459 RVA: 0x00079C18 File Offset: 0x00077E18
		public override void vmethod_11(HisData hisData_0)
		{
			this.vmethod_22(hisData_0);
			if (hisData_0 == null || hisData_0.Close > 0.0)
			{
				this.method_266();
				base.ZedGraphControl.AxisChange();
			}
			this.method_185(false, false);
			this.method_200(false);
			this.method_207();
			this.method_224();
			base.vmethod_11(hisData_0);
		}

		// Token: 0x0600116C RID: 4460 RVA: 0x00079C78 File Offset: 0x00077E78
		public override void vmethod_12()
		{
			this.ipointListEdit_0.Clear();
			if (this.method_155(false) || this.method_272())
			{
				this.ApplyTheme(Base.UI.Form.ChartTheme);
			}
			this.method_203();
			this.method_207();
			base.vmethod_12();
		}

		// Token: 0x0600116D RID: 4461 RVA: 0x00007642 File Offset: 0x00005842
		public override void vmethod_15()
		{
			base.vmethod_15();
			this.method_266();
		}

		// Token: 0x0600116E RID: 4462 RVA: 0x00079CC8 File Offset: 0x00077EC8
		private bool method_272()
		{
			if (this.curveItem_0 is JapaneseCandleStickItem)
			{
				JapaneseCandleStickItem japaneseCandleStickItem = this.curveItem_0 as JapaneseCandleStickItem;
				Color right = this.method_273();
				if (japaneseCandleStickItem.Stick.RisingFill.Color != right)
				{
					return true;
				}
			}
			return false;
		}

		// Token: 0x0600116F RID: 4463 RVA: 0x00079D14 File Offset: 0x00077F14
		private Color method_273()
		{
			return this.method_274(Base.UI.Form.ChartTheme);
		}

		// Token: 0x06001170 RID: 4464 RVA: 0x00079D38 File Offset: 0x00077F38
		private Color method_274(ChartTheme chartTheme_0)
		{
			Color result = default(Color);
			if (chartTheme_0 == ChartTheme.Classic)
			{
				result = Color.Black;
				if (Base.UI.Form.KLineType != null && Base.UI.Form.KLineType.Value == KLineType.CandleStick_SolidUpK)
				{
					result = Color.Red;
				}
			}
			else if (Base.UI.Form.KLineType != null && Base.UI.Form.KLineType.Value == KLineType.CandleStick_SolidUpK)
			{
				result = Color.Red;
			}
			else if (chartTheme_0 == ChartTheme.Modern)
			{
				result = Color.White;
			}
			else if (chartTheme_0 == ChartTheme.Yellow)
			{
				result = Color.FromArgb(255, 255, 236);
			}
			return result;
		}

		// Token: 0x06001171 RID: 4465 RVA: 0x00079DE4 File Offset: 0x00077FE4
		public override void ApplyTheme(ChartTheme theme)
		{
			base.ApplyTheme(theme);
			this.textObj_3.FontSpec.FontColor = base.HeaderTextObj.FontSpec.FontColor;
			if (theme == ChartTheme.Classic)
			{
				if (this.curveItem_0 is JapaneseCandleStickItem)
				{
					JapaneseCandleStickItem japaneseCandleStickItem = this.curveItem_0 as JapaneseCandleStickItem;
					japaneseCandleStickItem.Stick.Color = Color.Red;
					japaneseCandleStickItem.Stick.RisingFill.Color = this.method_274(theme);
					japaneseCandleStickItem.Stick.RisingBorder.Color = Color.Red;
					japaneseCandleStickItem.Stick.FallingColor = Color.Cyan;
					japaneseCandleStickItem.Stick.FallingBorder.Color = Color.Cyan;
					japaneseCandleStickItem.Stick.FallingFill.Color = Color.Cyan;
				}
				else if (this.curveItem_0 is OHLCBarItem)
				{
					(this.curveItem_0 as OHLCBarItem).Bar.Color = Color.Green;
				}
				if (this.textObj_13 != null)
				{
					this.textObj_13.FontSpec.FontColor = Color.Yellow;
				}
			}
			else
			{
				if (this.curveItem_0 is JapaneseCandleStickItem)
				{
					JapaneseCandleStickItem japaneseCandleStickItem2 = this.curveItem_0 as JapaneseCandleStickItem;
					japaneseCandleStickItem2.Stick.Color = Color.Red;
					japaneseCandleStickItem2.Stick.RisingBorder.Color = Color.Red;
					japaneseCandleStickItem2.Stick.FallingColor = Color.Green;
					japaneseCandleStickItem2.Stick.FallingBorder.Color = Color.Green;
					japaneseCandleStickItem2.Stick.FallingFill.Color = Color.Green;
					japaneseCandleStickItem2.Stick.RisingFill.Color = this.method_274(theme);
				}
				else if (this.curveItem_0 is OHLCBarItem)
				{
					(this.curveItem_0 as OHLCBarItem).Bar.Color = Color.Blue;
				}
				if (theme == ChartTheme.Modern)
				{
					base.GraphPane.Fill = new Fill(Color.White, Class179.color_14, 90f);
				}
				else if (theme == ChartTheme.Yellow)
				{
					base.GraphPane.Fill = new Fill(Class179.color_13);
				}
				if (this.textObj_13 != null)
				{
					this.textObj_13.FontSpec.FontColor = Color.MidnightBlue;
				}
			}
			this.method_185(true, false);
			this.method_206();
		}

		// Token: 0x06001172 RID: 4466 RVA: 0x00007652 File Offset: 0x00005852
		protected override void vmethod_30(object sender, EventArgs27 e)
		{
			this.method_269();
			base.vmethod_30(sender, e);
			this.vmethod_11(base.SymbDataSet.LastHisData);
		}

		// Token: 0x06001173 RID: 4467 RVA: 0x00007306 File Offset: 0x00005506
		private void method_275(object sender, MouseEventArgs e)
		{
			this.method_200(false);
		}

		// Token: 0x06001174 RID: 4468 RVA: 0x0007A024 File Offset: 0x00078224
		protected override void vmethod_23(bool bool_8)
		{
			base.vmethod_23(bool_8);
			Base.Trading.TransCreated -= this.method_209;
			Base.Trading.TransactionsUpdated -= this.method_149;
			Base.Trading.OrderCreated -= this.method_210;
			Base.Trading.OrderStatusUpdated -= this.method_211;
			Base.Trading.OrderPriceUnitsUpdated -= this.method_212;
			Base.Trading.CondOrderCreated -= this.method_213;
			Base.Trading.CondOrderUpdated -= this.method_215;
			Base.Trading.CondOrderStatusUpdated -= this.method_214;
			Base.Data.CurrSymblChanging -= this.method_148;
			Base.Data.CurrSymblChanged -= this.method_150;
			Base.Acct.AccountChanging -= this.method_151;
			Base.Acct.AccountChanged -= this.method_152;
			Base.UI.Form.StockRestorationMethodChanged -= this.method_153;
			Base.UI.Form.TransArrowTypeChanged -= this.method_216;
			InfoMineMgr.InfoMineRetrieved -= this.method_223;
			base.ChtCtrl.StkRationedPrcApplying -= this.method_154;
			base.ChtCtrl.MouseUp -= this.method_275;
		}

		// Token: 0x06001175 RID: 4469 RVA: 0x0007A174 File Offset: 0x00078374
		private void method_276()
		{
			if (base.GraphPane.YAxis.Type != AxisType.Log)
			{
				base.GraphPane.YAxis.Type = AxisType.Log;
			}
			if (base.GraphPane.YAxis.Title.IsOmitMag)
			{
				base.GraphPane.YAxis.Title.IsOmitMag = false;
			}
			if (base.GraphPane.YAxis.Scale.IsUseTenPower)
			{
				base.GraphPane.YAxis.Scale.IsUseTenPower = false;
			}
			double max = base.GraphPane.YAxis.Scale.Max;
			double min = base.GraphPane.YAxis.Scale.Min;
			double majorStep = ChartBase.smethod_0(max, min, 3);
			base.GraphPane.YAxis.Scale.MajorStep = majorStep;
			string format = string.Format("f{0}", base.Symbol.DigitNb);
			base.GraphPane.YAxis.Scale.Format = format;
		}

		// Token: 0x06001176 RID: 4470 RVA: 0x0007A27C File Offset: 0x0007847C
		protected override void vmethod_28(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "主图坐标";
			toolStripMenuItem.DropDownItems.Add(new ToolStripMenuItem());
			toolStripMenuItem.MouseEnter += this.method_277;
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
		}

		// Token: 0x06001177 RID: 4471 RVA: 0x0007A2CC File Offset: 0x000784CC
		private void method_277(object sender, EventArgs e)
		{
			ToolStripMenuItem toolStripMenuItem = sender as ToolStripMenuItem;
			if (toolStripMenuItem != null)
			{
				toolStripMenuItem.DropDownItems.Clear();
				ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
				toolStripMenuItem2.Text = "普通坐标";
				toolStripMenuItem2.Click += this.method_278;
				toolStripMenuItem2.Checked = !this.IsYAxisCoordsLogType;
				toolStripMenuItem.DropDownItems.Add(toolStripMenuItem2);
				ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
				toolStripMenuItem3.Text = "对数坐标";
				toolStripMenuItem3.Click += this.method_279;
				toolStripMenuItem3.Checked = this.IsYAxisCoordsLogType;
				toolStripMenuItem.DropDownItems.Add(toolStripMenuItem3);
			}
		}

		// Token: 0x06001178 RID: 4472 RVA: 0x00007675 File Offset: 0x00005875
		private void method_278(object sender, EventArgs e)
		{
			this.method_280(false);
		}

		// Token: 0x06001179 RID: 4473 RVA: 0x00007680 File Offset: 0x00005880
		private void method_279(object sender, EventArgs e)
		{
			this.method_280(true);
		}

		// Token: 0x0600117A RID: 4474 RVA: 0x0007A370 File Offset: 0x00078570
		private void method_280(bool bool_8)
		{
			if (this.IsYAxisCoordsLogType != bool_8)
			{
				this.IsYAxisCoordsLogType = bool_8;
				if (this.IsYAxisCoordsLogType)
				{
					base.GraphPane.YAxis.ScaleFormatEvent -= this.method_267;
				}
				else
				{
					base.GraphPane.YAxis.ScaleFormatEvent += this.method_267;
				}
				this.method_269();
				this.vmethod_3();
				base.ZedGraphControl.AxisChange();
				base.ZedGraphControl.Refresh();
			}
		}

		// Token: 0x17000289 RID: 649
		// (get) Token: 0x0600117B RID: 4475 RVA: 0x0007A3F4 File Offset: 0x000785F4
		public CurveItem Curve
		{
			get
			{
				return this.curveItem_0;
			}
		}

		// Token: 0x1700028A RID: 650
		// (get) Token: 0x0600117C RID: 4476 RVA: 0x0007A40C File Offset: 0x0007860C
		public List<int> XLstToDispDayDivLine
		{
			get
			{
				return this.method_262();
			}
		}

		// Token: 0x1700028B RID: 651
		// (get) Token: 0x0600117D RID: 4477 RVA: 0x0007A424 File Offset: 0x00078624
		// (set) Token: 0x0600117E RID: 4478 RVA: 0x0000768B File Offset: 0x0000588B
		public DrawObj SelectedDrawObj
		{
			get
			{
				return this.drawObj_0;
			}
			set
			{
				this.drawObj_0 = value;
			}
		}

		// Token: 0x1700028C RID: 652
		// (get) Token: 0x0600117F RID: 4479 RVA: 0x0007A43C File Offset: 0x0007863C
		// (set) Token: 0x06001180 RID: 4480 RVA: 0x00007696 File Offset: 0x00005896
		public List<TransArrow> TransArrowList
		{
			get
			{
				return this.list_2;
			}
			set
			{
				this.list_2 = value;
			}
		}

		// Token: 0x1700028D RID: 653
		// (get) Token: 0x06001181 RID: 4481 RVA: 0x0007A454 File Offset: 0x00078654
		// (set) Token: 0x06001182 RID: 4482 RVA: 0x000076A1 File Offset: 0x000058A1
		public List<Class20> InfoMineList { get; set; }

		// Token: 0x1700028E RID: 654
		// (get) Token: 0x06001183 RID: 4483 RVA: 0x0007A46C File Offset: 0x0007866C
		// (set) Token: 0x06001184 RID: 4484 RVA: 0x000076AC File Offset: 0x000058AC
		public SortedDictionary<DateTime, string> InfoMineDict { get; set; }

		// Token: 0x1700028F RID: 655
		// (get) Token: 0x06001185 RID: 4485 RVA: 0x0007A484 File Offset: 0x00078684
		// (set) Token: 0x06001186 RID: 4486 RVA: 0x000076B7 File Offset: 0x000058B7
		public bool IfShowNoTransArrow
		{
			get
			{
				return this.bool_4;
			}
			set
			{
				this.bool_4 = value;
			}
		}

		// Token: 0x17000290 RID: 656
		// (get) Token: 0x06001187 RID: 4487 RVA: 0x0007A49C File Offset: 0x0007869C
		// (set) Token: 0x06001188 RID: 4488 RVA: 0x000076C2 File Offset: 0x000058C2
		public bool IfShowAllTransArrow
		{
			get
			{
				return this.bool_5;
			}
			set
			{
				this.bool_5 = value;
			}
		}

		// Token: 0x17000291 RID: 657
		// (get) Token: 0x06001189 RID: 4489 RVA: 0x0007A4B4 File Offset: 0x000786B4
		// (set) Token: 0x0600118A RID: 4490 RVA: 0x000076CD File Offset: 0x000058CD
		public TransArrow HighLightedTransArrow
		{
			get
			{
				return this.transArrow_0;
			}
			set
			{
				this.transArrow_0 = value;
			}
		}

		// Token: 0x17000292 RID: 658
		// (get) Token: 0x0600118B RID: 4491 RVA: 0x0007A4CC File Offset: 0x000786CC
		// (set) Token: 0x0600118C RID: 4492 RVA: 0x000076D8 File Offset: 0x000058D8
		public List<TOdrLine> TOdrLineList
		{
			get
			{
				return this.list_3;
			}
			set
			{
				this.list_3 = value;
			}
		}

		// Token: 0x17000293 RID: 659
		// (get) Token: 0x0600118D RID: 4493 RVA: 0x0007A4E4 File Offset: 0x000786E4
		public List<Class68> TOdrLineTransList
		{
			get
			{
				List<Class68> result = null;
				if (this.TOdrLineList != null)
				{
					if (this.TOdrLineList.Where(new Func<TOdrLine, bool>(ChartCS.<>c.<>9.method_7)).Any<TOdrLine>())
					{
						result = this.TOdrLineList.Where(new Func<TOdrLine, bool>(ChartCS.<>c.<>9.method_8)).Cast<Class68>().ToList<Class68>();
					}
				}
				return result;
			}
		}

		// Token: 0x17000294 RID: 660
		// (get) Token: 0x0600118E RID: 4494 RVA: 0x0007A568 File Offset: 0x00078768
		public List<Class67> OrderLineList
		{
			get
			{
				List<Class67> result = null;
				if (this.TOdrLineList != null)
				{
					if (this.TOdrLineList.Where(new Func<TOdrLine, bool>(ChartCS.<>c.<>9.method_9)).Any<TOdrLine>())
					{
						result = this.TOdrLineList.Where(new Func<TOdrLine, bool>(ChartCS.<>c.<>9.method_10)).Cast<Class67>().ToList<Class67>();
					}
				}
				return result;
			}
		}

		// Token: 0x17000295 RID: 661
		// (get) Token: 0x0600118F RID: 4495 RVA: 0x0007A5EC File Offset: 0x000787EC
		public List<Class66> TOdrLineCondOrderList
		{
			get
			{
				List<Class66> result = null;
				if (this.TOdrLineList != null)
				{
					if (this.TOdrLineList.Where(new Func<TOdrLine, bool>(ChartCS.<>c.<>9.method_11)).Any<TOdrLine>())
					{
						result = this.TOdrLineList.Where(new Func<TOdrLine, bool>(ChartCS.<>c.<>9.method_12)).Cast<Class66>().ToList<Class66>();
					}
				}
				return result;
			}
		}

		// Token: 0x17000296 RID: 662
		// (get) Token: 0x06001191 RID: 4497 RVA: 0x0007A670 File Offset: 0x00078870
		// (set) Token: 0x06001190 RID: 4496 RVA: 0x000076E3 File Offset: 0x000058E3
		public bool IsYAxisCoordsLogType
		{
			get
			{
				return this.bool_6;
			}
			protected set
			{
				this.bool_6 = value;
			}
		}

		// Token: 0x17000297 RID: 663
		// (get) Token: 0x06001192 RID: 4498 RVA: 0x0007A688 File Offset: 0x00078888
		// (set) Token: 0x06001193 RID: 4499 RVA: 0x000076EE File Offset: 0x000058EE
		public bool IsInitiating { get; set; }

		// Token: 0x06001194 RID: 4500 RVA: 0x0007A6A0 File Offset: 0x000788A0
		[CompilerGenerated]
		private bool method_281(DrawObj drawObj_1)
		{
			bool result;
			if (drawObj_1.SymbolCode == base.Symbol.Code)
			{
				result = (drawObj_1.AcctID == Base.Acct.CurrAccount.ID);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06001195 RID: 4501 RVA: 0x0007A6E0 File Offset: 0x000788E0
		[CompilerGenerated]
		private bool method_282(ShownOrder shownOrder_0)
		{
			bool result;
			if (shownOrder_0.OrderStatus == 0 && shownOrder_0.AcctID == Base.Acct.CurrAccount.ID)
			{
				result = (shownOrder_0.SymbolID == base.Symbol.ID);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06001196 RID: 4502 RVA: 0x0007A724 File Offset: 0x00078924
		[CompilerGenerated]
		private bool method_283(ShownCondOrder shownCondOrder_0)
		{
			bool result;
			if (shownCondOrder_0.OrderStatus == OrderStatus.Active && shownCondOrder_0.AcctID == Base.Acct.CurrAccount.ID)
			{
				result = (shownCondOrder_0.SymbID == base.Symbol.ID);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06001197 RID: 4503 RVA: 0x0007A768 File Offset: 0x00078968
		[CompilerGenerated]
		private bool method_284(ShownOpenTrans shownOpenTrans_0)
		{
			return shownOpenTrans_0.SymbolID == base.Symbol.ID;
		}

		// Token: 0x06001198 RID: 4504 RVA: 0x0007A768 File Offset: 0x00078968
		[CompilerGenerated]
		private bool method_285(ShownOpenTrans shownOpenTrans_0)
		{
			return shownOpenTrans_0.SymbolID == base.Symbol.ID;
		}

		// Token: 0x04000902 RID: 2306
		private IPointListEdit ipointListEdit_0;

		// Token: 0x04000903 RID: 2307
		private CurveItem curveItem_0;

		// Token: 0x04000904 RID: 2308
		private TextObj textObj_3;

		// Token: 0x04000905 RID: 2309
		private TextObj textObj_4;

		// Token: 0x04000906 RID: 2310
		private TextObj textObj_5;

		// Token: 0x04000907 RID: 2311
		private TextObj textObj_6;

		// Token: 0x04000908 RID: 2312
		private TextObj textObj_7;

		// Token: 0x04000909 RID: 2313
		private TextObj textObj_8;

		// Token: 0x0400090A RID: 2314
		private TextObj textObj_9;

		// Token: 0x0400090B RID: 2315
		private TextObj textObj_10;

		// Token: 0x0400090C RID: 2316
		private TextObj textObj_11;

		// Token: 0x0400090D RID: 2317
		private TextObj textObj_12;

		// Token: 0x0400090E RID: 2318
		private TextObj textObj_13;

		// Token: 0x0400090F RID: 2319
		private TextObj textObj_14;

		// Token: 0x04000910 RID: 2320
		private TextObj textObj_15;

		// Token: 0x04000911 RID: 2321
		private TextObj textObj_16;

		// Token: 0x04000912 RID: 2322
		private TextObj textObj_17;

		// Token: 0x04000913 RID: 2323
		private TextObj textObj_18;

		// Token: 0x04000914 RID: 2324
		private TextObj textObj_19;

		// Token: 0x04000915 RID: 2325
		private DrawObj drawObj_0;

		// Token: 0x04000916 RID: 2326
		private List<TransArrow> list_2;

		// Token: 0x04000917 RID: 2327
		private bool bool_4;

		// Token: 0x04000918 RID: 2328
		private bool bool_5;

		// Token: 0x04000919 RID: 2329
		private TransArrow transArrow_0;

		// Token: 0x0400091A RID: 2330
		private List<TOdrLine> list_3;

		// Token: 0x0400091B RID: 2331
		private string string_10;

		// Token: 0x0400091C RID: 2332
		private decimal decimal_0;

		// Token: 0x0400091D RID: 2333
		private const string string_11 = "对数坐标";

		// Token: 0x0400091E RID: 2334
		[CompilerGenerated]
		private List<Class20> list_4;

		// Token: 0x0400091F RID: 2335
		[CompilerGenerated]
		private SortedDictionary<DateTime, string> sortedDictionary_0;

		// Token: 0x04000920 RID: 2336
		private bool bool_6;

		// Token: 0x04000921 RID: 2337
		[CompilerGenerated]
		private bool bool_7;

		// Token: 0x020001B3 RID: 435
		[CompilerGenerated]
		private sealed class Class240
		{
			// Token: 0x0600119A RID: 4506 RVA: 0x0007A78C File Offset: 0x0007898C
			internal bool method_0(Transaction transaction_0)
			{
				bool result;
				if (transaction_0.AcctID == this.transArrow_0.Transaction.AcctID)
				{
					result = (transaction_0.ID == this.transArrow_0.Transaction.ID);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000922 RID: 2338
			public TransArrow transArrow_0;
		}

		// Token: 0x020001B4 RID: 436
		[CompilerGenerated]
		private sealed class Class241
		{
			// Token: 0x0600119C RID: 4508 RVA: 0x0007A7D4 File Offset: 0x000789D4
			internal bool method_0(DrawObj drawObj_0)
			{
				return drawObj_0.Tag == this.string_0;
			}

			// Token: 0x04000923 RID: 2339
			public string string_0;
		}

		// Token: 0x020001B5 RID: 437
		[CompilerGenerated]
		private sealed class Class242
		{
			// Token: 0x0600119E RID: 4510 RVA: 0x0007A7F8 File Offset: 0x000789F8
			internal bool method_0(TOdrLine todrLine_0)
			{
				return todrLine_0.Line.Tag.ToString().Equals(this.string_0);
			}

			// Token: 0x04000924 RID: 2340
			public string string_0;
		}

		// Token: 0x020001B7 RID: 439
		[CompilerGenerated]
		private sealed class Class243
		{
			// Token: 0x060011AF RID: 4527 RVA: 0x0007A940 File Offset: 0x00078B40
			internal bool method_0(Transaction transaction_0)
			{
				bool result;
				if (transaction_0.AcctID == Base.Acct.CurrAccount.ID && transaction_0.SymbolID == this.chartCS_0.Symbol.ID && transaction_0.CreateTime > this.dateTime_0)
				{
					result = (transaction_0.CreateTime <= this.dateTime_1);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x060011B0 RID: 4528 RVA: 0x0007A940 File Offset: 0x00078B40
			internal bool method_1(ShownHisTrans shownHisTrans_0)
			{
				bool result;
				if (shownHisTrans_0.AcctID == Base.Acct.CurrAccount.ID && shownHisTrans_0.SymbolID == this.chartCS_0.Symbol.ID && shownHisTrans_0.CreateTime > this.dateTime_0)
				{
					result = (shownHisTrans_0.CreateTime <= this.dateTime_1);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000933 RID: 2355
			public ChartCS chartCS_0;

			// Token: 0x04000934 RID: 2356
			public DateTime dateTime_0;

			// Token: 0x04000935 RID: 2357
			public DateTime dateTime_1;
		}

		// Token: 0x020001B8 RID: 440
		[CompilerGenerated]
		private sealed class Class244
		{
			// Token: 0x060011B2 RID: 4530 RVA: 0x0007A9A4 File Offset: 0x00078BA4
			internal bool method_0(Transaction transaction_0)
			{
				ChartCS.Class245 @class = new ChartCS.Class245();
				@class.transaction_0 = transaction_0;
				if (@class.transaction_0.AcctID == Base.Acct.CurrAccount.ID && @class.transaction_0.SymbolID == this.class243_0.chartCS_0.Symbol.ID)
				{
					long? openUnits = @class.transaction_0.OpenUnits;
					if ((openUnits.GetValueOrDefault() > 0L & openUnits != null) && @class.transaction_0.CreateTime > this.class243_0.dateTime_0 && @class.transaction_0.CreateTime <= this.class243_0.dateTime_1)
					{
						return true;
					}
				}
				return this.ienumerable_0.Count(new Func<ShownHisTrans, bool>(@class.method_0)) > 0;
			}

			// Token: 0x04000936 RID: 2358
			public IEnumerable<ShownHisTrans> ienumerable_0;

			// Token: 0x04000937 RID: 2359
			public ChartCS.Class243 class243_0;
		}

		// Token: 0x020001B9 RID: 441
		[CompilerGenerated]
		private sealed class Class245
		{
			// Token: 0x060011B4 RID: 4532 RVA: 0x0007AA88 File Offset: 0x00078C88
			internal bool method_0(ShownHisTrans shownHisTrans_0)
			{
				bool result;
				if (shownHisTrans_0.AcctID == this.transaction_0.AcctID)
				{
					result = (shownHisTrans_0.ID == this.transaction_0.ID);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000938 RID: 2360
			public Transaction transaction_0;
		}

		// Token: 0x020001BA RID: 442
		[CompilerGenerated]
		private sealed class Class246
		{
			// Token: 0x060011B6 RID: 4534 RVA: 0x0007AAC4 File Offset: 0x00078CC4
			internal bool method_0(TransArrow transArrow_0)
			{
				return transArrow_0.Transaction.ID == this.transaction_0.ID;
			}

			// Token: 0x04000939 RID: 2361
			public Transaction transaction_0;
		}

		// Token: 0x020001BB RID: 443
		[CompilerGenerated]
		private sealed class Class247
		{
			// Token: 0x060011B8 RID: 4536 RVA: 0x0007AAF0 File Offset: 0x00078CF0
			internal bool method_0(Transaction transaction_0)
			{
				return transaction_0.ID == this.transArrow_0.Transaction.ID;
			}

			// Token: 0x0400093A RID: 2362
			public TransArrow transArrow_0;
		}

		// Token: 0x020001BC RID: 444
		[CompilerGenerated]
		private sealed class Class248
		{
			// Token: 0x060011BA RID: 4538 RVA: 0x0007AB1C File Offset: 0x00078D1C
			internal bool method_0(TransArrow transArrow_0)
			{
				return transArrow_0.ArrowImgObj == this.graphObj_0 as ImageObj;
			}

			// Token: 0x0400093B RID: 2363
			public GraphObj graphObj_0;
		}

		// Token: 0x020001BD RID: 445
		[CompilerGenerated]
		private sealed class Class249
		{
			// Token: 0x060011BC RID: 4540 RVA: 0x0007AB40 File Offset: 0x00078D40
			internal bool method_0(TransArrow transArrow_0)
			{
				bool result;
				if (transArrow_0.Transaction.AcctID == this.transaction_0.AcctID)
				{
					result = (transArrow_0.Transaction.ID == this.transaction_0.ID);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400093C RID: 2364
			public Transaction transaction_0;
		}

		// Token: 0x020001BE RID: 446
		[CompilerGenerated]
		private sealed class Class250
		{
			// Token: 0x060011BE RID: 4542 RVA: 0x0007AB88 File Offset: 0x00078D88
			internal bool method_0(Class68 class68_0)
			{
				bool result;
				if (class68_0.OpenTrans.AcctID == this.transaction_0.AcctID && this.transaction_0.ClosedTransID != null)
				{
					int id = class68_0.OpenTrans.ID;
					int? closedTransID = this.transaction_0.ClosedTransID;
					result = (id == closedTransID.GetValueOrDefault() & closedTransID != null);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400093D RID: 2365
			public Transaction transaction_0;
		}

		// Token: 0x020001BF RID: 447
		[CompilerGenerated]
		private sealed class Class251
		{
			// Token: 0x060011C0 RID: 4544 RVA: 0x0007ABF4 File Offset: 0x00078DF4
			internal bool method_0(Transaction transaction_0)
			{
				bool result;
				if (transaction_0.AcctID == this.class68_0.OpenTrans.AcctID)
				{
					result = (transaction_0.ID == this.class68_0.OpenTrans.ID);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400093E RID: 2366
			public Class68 class68_0;
		}

		// Token: 0x020001C0 RID: 448
		[CompilerGenerated]
		private sealed class Class252
		{
			// Token: 0x060011C2 RID: 4546 RVA: 0x0007AC3C File Offset: 0x00078E3C
			internal bool method_0(Class67 class67_0)
			{
				bool result;
				if (class67_0.Order != null && class67_0.Order.AcctID == this.order_0.AcctID)
				{
					result = (class67_0.Order.ID == this.order_0.ID);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400093F RID: 2367
			public Order order_0;
		}

		// Token: 0x020001C1 RID: 449
		[CompilerGenerated]
		private sealed class Class253
		{
			// Token: 0x060011C4 RID: 4548 RVA: 0x0007AC8C File Offset: 0x00078E8C
			internal bool method_0(Class66 class66_0)
			{
				bool result;
				if (class66_0.CondOrder != null && class66_0.CondOrder.AcctID == this.condOrder_0.AcctID)
				{
					result = (class66_0.CondOrder.ID == this.condOrder_0.ID);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000940 RID: 2368
			public CondOrder condOrder_0;
		}

		// Token: 0x020001C2 RID: 450
		[CompilerGenerated]
		private sealed class Class254
		{
			// Token: 0x060011C6 RID: 4550 RVA: 0x0007ACDC File Offset: 0x00078EDC
			internal bool method_0(Class18 class18_0)
			{
				return class18_0.tscode == this.string_0;
			}

			// Token: 0x04000941 RID: 2369
			public string string_0;
		}

		// Token: 0x020001C3 RID: 451
		[CompilerGenerated]
		private sealed class Class255
		{
			// Token: 0x060011C8 RID: 4552 RVA: 0x0007AD00 File Offset: 0x00078F00
			internal bool method_0(KeyValuePair<DateTime, string> keyValuePair_0)
			{
				bool result;
				if (keyValuePair_0.Key > this.dateTime_0)
				{
					result = (keyValuePair_0.Key <= this.dateTime_1);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000942 RID: 2370
			public DateTime dateTime_0;

			// Token: 0x04000943 RID: 2371
			public DateTime dateTime_1;
		}

		// Token: 0x020001C4 RID: 452
		[CompilerGenerated]
		private sealed class Class256
		{
			// Token: 0x060011CA RID: 4554 RVA: 0x0007AD3C File Offset: 0x00078F3C
			internal bool method_0(Class20 class20_0)
			{
				return class20_0.InfoTextObj == this.graphObj_0 as TextObj;
			}

			// Token: 0x04000944 RID: 2372
			public GraphObj graphObj_0;
		}

		// Token: 0x020001C5 RID: 453
		[CompilerGenerated]
		private sealed class Class257
		{
			// Token: 0x060011CC RID: 4556 RVA: 0x0007AD60 File Offset: 0x00078F60
			internal bool method_0(TransArrow transArrow_0)
			{
				bool result;
				if (transArrow_0.NoteBox != null)
				{
					result = (transArrow_0.NoteBox.Tag == this.graphObj_0.Tag);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000945 RID: 2373
			public GraphObj graphObj_0;
		}

		// Token: 0x020001C6 RID: 454
		[CompilerGenerated]
		private sealed class Class258
		{
			// Token: 0x060011CE RID: 4558 RVA: 0x0007AD98 File Offset: 0x00078F98
			internal bool method_0(ShownOpenTrans shownOpenTrans_1)
			{
				return shownOpenTrans_1.TransType == this.shownOpenTrans_0.TransType;
			}

			// Token: 0x04000946 RID: 2374
			public ShownOpenTrans shownOpenTrans_0;
		}
	}
}
