﻿using System;
using System.Runtime.Serialization;
using System.Security;

namespace SmartAssembly.SmartExceptionsCore
{
	// Token: 0x02000402 RID: 1026
	[Serializable]
	public sealed class SmartStackFrame : ISerializable
	{
		// Token: 0x060027D6 RID: 10198 RVA: 0x00102A20 File Offset: 0x00100C20
		[SecurityCritical]
		public void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			info.AddValue("UnhandledException.MethodID", this.MethodID, typeof(int));
			info.AddValue("UnhandledException.ILOffset", this.ILOffset, typeof(int));
			info.AddValue("UnhandledException.ExceptionStackDepth", this.ExceptionStackDepth, typeof(int));
			int num = (this.Objects == null) ? 0 : this.Objects.Length;
			info.AddValue("UnhandledException.Objects.Length", num, typeof(int));
			for (int i = 0; i < num; i++)
			{
				string name = string.Format("UnhandledException.Objects[{0}]", i);
				try
				{
					if (this.Objects[i] == null)
					{
						info.AddValue(name, null, typeof(object));
					}
					else
					{
						info.AddValue(name, this.Objects[i].GetType() + " - " + this.Objects[i], typeof(string));
					}
				}
				catch (Exception)
				{
				}
			}
		}

		// Token: 0x060027D7 RID: 10199 RVA: 0x00102B3C File Offset: 0x00100D3C
		internal SmartStackFrame(SerializationInfo info, StreamingContext context)
		{
			this.MethodID = info.GetInt32("UnhandledException.MethodID");
			this.ILOffset = info.GetInt32("UnhandledException.ILOffset");
			this.ExceptionStackDepth = info.GetInt32("UnhandledException.ExceptionStackDepth");
			int @int = info.GetInt32("UnhandledException.Objects.Length");
			this.Objects = new object[@int];
			for (int i = 0; i < @int; i++)
			{
				try
				{
					this.Objects[i] = info.GetValue(string.Format("UnhandledException.Objects[{0}]", i), typeof(string));
				}
				catch (Exception)
				{
					this.Objects[i] = "Could not deserialize the obect";
				}
			}
		}

		// Token: 0x060027D8 RID: 10200 RVA: 0x0000F4F1 File Offset: 0x0000D6F1
		internal SmartStackFrame(int methodID, object[] objects, int ilOffset, int exceptionStackDepth)
		{
			this.MethodID = methodID;
			this.ExceptionStackDepth = exceptionStackDepth;
			this.ILOffset = ilOffset;
			this.Objects = objects;
		}

		// Token: 0x040013C7 RID: 5063
		public readonly int MethodID;

		// Token: 0x040013C8 RID: 5064
		public readonly object[] Objects;

		// Token: 0x040013C9 RID: 5065
		public readonly int ILOffset;

		// Token: 0x040013CA RID: 5066
		public readonly int ExceptionStackDepth;
	}
}
