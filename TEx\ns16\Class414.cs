﻿using System;
using ns14;
using ns30;
using ns9;
using TEx.Inds;
using TEx.SIndicator;

namespace ns16
{
	// Token: 0x02000310 RID: 784
	internal sealed class Class414 : Class412
	{
		// Token: 0x060021D0 RID: 8656 RVA: 0x0000D8A3 File Offset: 0x0000BAA3
		public Class414(HToken htoken_1, Class408 class408_2, Class408 class408_3) : base(htoken_1, class408_2, class408_3)
		{
		}

		// Token: 0x060021D1 RID: 8657 RVA: 0x000E7A78 File Offset: 0x000E5C78
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			if (this.Token.Symbol.HSymbolType != Enum26.const_6 && this.Token.Symbol.HSymbolType != Enum26.const_7)
			{
				throw new Exception(this.Token.method_0("不是加号或者减号"));
			}
			object object_ = this.Left.vmethod_1(parserEnvironment_0);
			object object_2 = this.Right.vmethod_1(parserEnvironment_0);
			return this.vmethod_2(object_, object_2);
		}

		// Token: 0x060021D2 RID: 8658 RVA: 0x000E7AE8 File Offset: 0x000E5CE8
		protected override double vmethod_3(double double_0, double double_1)
		{
			Enum26 hsymbolType = this.Token.Symbol.HSymbolType;
			double result;
			if (hsymbolType != Enum26.const_6)
			{
				if (hsymbolType != Enum26.const_7)
				{
					throw new Exception(this.Token.method_0("不是加号或者减号"));
				}
				result = double_0 - double_1;
			}
			else
			{
				result = double_0 + double_1;
			}
			return result;
		}

		// Token: 0x060021D3 RID: 8659 RVA: 0x000E7B34 File Offset: 0x000E5D34
		protected override DataArray vmethod_4(DataArray dataArray_0, DataArray dataArray_1)
		{
			Enum26 hsymbolType = this.Token.Symbol.HSymbolType;
			DataArray result;
			if (hsymbolType != Enum26.const_6)
			{
				if (hsymbolType != Enum26.const_7)
				{
					throw new Exception(this.Token.method_0("不是加号或者减号"));
				}
				result = dataArray_0 - dataArray_1;
			}
			else
			{
				result = dataArray_0 + dataArray_1;
			}
			return result;
		}

		// Token: 0x060021D4 RID: 8660 RVA: 0x000E7B88 File Offset: 0x000E5D88
		public static Class408 smethod_0(Tokenes tokenes_0)
		{
			HToken htoken = tokenes_0.Current;
			Class408 @class = Class420.smethod_0(tokenes_0);
			if (@class == null)
			{
				throw new Exception(htoken.method_0("解析错误。"));
			}
			Class408 result;
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
			{
				result = @class;
			}
			else
			{
				tokenes_0.method_1();
				if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_6 | tokenes_0.Current.Symbol.HSymbolType == Enum26.const_7)
				{
					result = Class414.smethod_1(@class, tokenes_0);
				}
				else
				{
					tokenes_0.method_2();
					result = @class;
				}
			}
			return result;
		}

		// Token: 0x060021D5 RID: 8661 RVA: 0x000E7C18 File Offset: 0x000E5E18
		private static Class408 smethod_1(Class408 class408_2, Tokenes tokenes_0)
		{
			if (!(tokenes_0.Current.Symbol.HSymbolType == Enum26.const_6 | tokenes_0.Current.Symbol.HSymbolType == Enum26.const_7))
			{
				throw new Exception(tokenes_0.Current.method_0("不是加号或者减号"));
			}
			HToken htoken = tokenes_0.Current;
			tokenes_0.method_1();
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
			{
				throw new Exception(htoken.method_0("没有操作数"));
			}
			Class408 @class = Class420.smethod_0(tokenes_0);
			if (@class == null)
			{
				throw new Exception(htoken.method_0("操作数错误。"));
			}
			Class414 class2 = new Class414(htoken, class408_2, @class);
			tokenes_0.method_1();
			Class408 result;
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_6 | tokenes_0.Current.Symbol.HSymbolType == Enum26.const_7)
			{
				result = Class414.smethod_1(class2, tokenes_0);
			}
			else if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
			{
				result = class2;
			}
			else
			{
				tokenes_0.method_2();
				result = class2;
			}
			return result;
		}

		// Token: 0x060021D6 RID: 8662 RVA: 0x000E7D1C File Offset: 0x000E5F1C
		public string ToString()
		{
			return base.ToString();
		}

		// Token: 0x170005D1 RID: 1489
		// (get) Token: 0x060021D7 RID: 8663 RVA: 0x000E7D34 File Offset: 0x000E5F34
		// (set) Token: 0x060021D8 RID: 8664 RVA: 0x0000D8AE File Offset: 0x0000BAAE
		public override Class408 Left
		{
			get
			{
				return base.Left;
			}
			protected set
			{
				base.Left = value;
			}
		}

		// Token: 0x170005D2 RID: 1490
		// (get) Token: 0x060021D9 RID: 8665 RVA: 0x000E7D4C File Offset: 0x000E5F4C
		// (set) Token: 0x060021DA RID: 8666 RVA: 0x0000D8B9 File Offset: 0x0000BAB9
		public override Class408 Right
		{
			get
			{
				return base.Right;
			}
			protected set
			{
				base.Right = value;
			}
		}

		// Token: 0x170005D3 RID: 1491
		// (get) Token: 0x060021DB RID: 8667 RVA: 0x000E7D64 File Offset: 0x000E5F64
		// (set) Token: 0x060021DC RID: 8668 RVA: 0x0000D8C4 File Offset: 0x0000BAC4
		public override HToken Token
		{
			get
			{
				return base.Token;
			}
			protected set
			{
				base.Token = value;
			}
		}
	}
}
