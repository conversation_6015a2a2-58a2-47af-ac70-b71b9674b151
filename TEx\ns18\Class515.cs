﻿using System;
using ns15;
using ns17;
using ns22;
using ns34;

namespace ns18
{
	// Token: 0x020003CE RID: 974
	internal static class Class515
	{
		// Token: 0x060026F8 RID: 9976 RVA: 0x0000EEC8 File Offset: 0x0000D0C8
		public static Class513 smethod_0(UIntPtr uintptr_0, Enum34 enum34_0, Enum32 enum32_0, string string_0, out Enum33 enum33_0)
		{
			return Class516.OpenKey(uintptr_0, enum34_0, enum32_0, string_0, out enum33_0);
		}
	}
}
