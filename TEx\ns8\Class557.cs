﻿using System;
using System.Collections.Generic;
using System.Data;
using ns10;
using TEx.Comn;
using TEx.Util;

namespace ns8
{
	// Token: 0x02000026 RID: 38
	internal sealed class Class557 : Class556
	{
		// Token: 0x060000DE RID: 222 RVA: 0x00002D06 File Offset: 0x00000F06
		public Class557()
		{
			base.ResultCompressed = true;
		}

		// Token: 0x060000DF RID: 223 RVA: 0x00013F00 File Offset: 0x00012100
		protected void ProcessRsltData(ApiResult rslt, Dictionary<string, object> reqDict)
		{
			if (rslt != null && reqDict.ContainsKey("rptAnlysType"))
			{
				string string_ = rslt.data as string;
				this.method_3(string_, reqDict);
			}
		}

		// Token: 0x060000E0 RID: 224 RVA: 0x00013F34 File Offset: 0x00012134
		private void method_3(string string_1, Dictionary<string, object> dictionary_0)
		{
			if (!string.IsNullOrEmpty(string_1))
			{
				DataTable dataTable = Utility.GetDataTableFromCsv(string_1, new int[1]);
				InternalDataCollectionBase columns = dataTable.Columns;
				DataTable dataTable2 = new DataTable();
				DataTable dataTable3 = new DataTable();
				foreach (object obj in columns)
				{
					DataColumn dataColumn = (DataColumn)obj;
					dataTable2.Columns.Add(new DataColumn(dataColumn.ColumnName, dataColumn.DataType));
					dataTable3.Columns.Add(new DataColumn(dataColumn.ColumnName, dataColumn.DataType));
				}
				DataRow[] array = dataTable.Select();
				int num = (array.Length < 12) ? 0 : (array.Length - 12);
				for (int i = array.Length - 1; i >= num; i--)
				{
					object[] array2 = array[i].ItemArray.Clone() as object[];
					string text = array2[0] as string;
					string str = text.Substring(0, 4);
					if (text.EndsWith("0331"))
					{
						array2[0] = str + " Q1";
					}
					else if (text.EndsWith("0630"))
					{
						array2[0] = str + " Q2";
					}
					else if (text.EndsWith("0930"))
					{
						array2[0] = str + " Q3";
					}
					else if (text.EndsWith("1231"))
					{
						array2[0] = str + " Q4";
					}
					dataTable3.Rows.Add(array2);
				}
				DataRow[] array3 = dataTable.Select(dataTable.Columns[0] + " LIKE '%1231'");
				num = ((array3.Length < 12) ? 0 : (array3.Length - 12));
				for (int j = array3.Length - 1; j >= num; j--)
				{
					object[] array4 = array3[j].ItemArray.Clone() as object[];
					array4[0] = (array4[0] as string).Substring(0, 4);
					dataTable2.Rows.Add(array4);
				}
				dataTable = Utility.GetSortedDataTable(dataTable, "报告期");
				dataTable2 = Utility.GetSortedDataTable(dataTable2, "报告期");
				dataTable3 = Utility.GetSortedDataTable(dataTable3, "报告期");
				dictionary_0["dataTbl"] = dataTable;
				dictionary_0["yrDataTbl"] = dataTable2;
				dictionary_0["qtDataTbl"] = dataTable3;
			}
		}
	}
}
