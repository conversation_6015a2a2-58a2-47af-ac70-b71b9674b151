﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using TEx;

namespace ns26
{
	// Token: 0x0200023C RID: 572
	internal sealed class DrawObjDTVal : UserControl
	{
		// Token: 0x0600186D RID: 6253 RVA: 0x00009F8A File Offset: 0x0000818A
		public DrawObjDTVal()
		{
		}

		// Token: 0x0600186E RID: 6254 RVA: 0x00009F92 File Offset: 0x00008192
		public DrawObjDTVal(DrawObj drawObj_1, DateTime dateTime_1, double double_1) : this(drawObj_1, dateTime_1, double_1, 1)
		{
		}

		// Token: 0x0600186F RID: 6255 RVA: 0x00009F9E File Offset: 0x0000819E
		public DrawObjDTVal(DrawObj drawObj_1, DateTime dateTime_1, double double_1, int int_1)
		{
			this.InitializeComponent();
			this.drawObj_0 = drawObj_1;
			this.XDateTime = dateTime_1;
			this.YValue = double_1;
			this.Index = int_1;
		}

		// Token: 0x17000415 RID: 1045
		// (get) Token: 0x06001870 RID: 6256 RVA: 0x000A3218 File Offset: 0x000A1418
		// (set) Token: 0x06001871 RID: 6257 RVA: 0x00009FCB File Offset: 0x000081CB
		public DateTime XDateTime
		{
			get
			{
				DateTime result;
				try
				{
					result = this.dtPicker_date.Value;
					result = result.Date;
					result = result.Add(this.dtPicker_time.Value.TimeOfDay);
				}
				catch
				{
					result = this.dateTime_0;
				}
				return result;
			}
			set
			{
				this.dateTime_0 = value;
				this.dtPicker_date.Value = value;
				this.dtPicker_time.Value = value;
			}
		}

		// Token: 0x17000416 RID: 1046
		// (get) Token: 0x06001872 RID: 6258 RVA: 0x000A3278 File Offset: 0x000A1478
		// (set) Token: 0x06001873 RID: 6259 RVA: 0x000A32B8 File Offset: 0x000A14B8
		public double YValue
		{
			get
			{
				double result;
				try
				{
					result = Convert.ToDouble(this.txtBox_yVal.Text);
				}
				catch
				{
					result = this.double_0;
				}
				return result;
			}
			set
			{
				this.double_0 = value;
				int num = (this.drawObj_0.Chart.Symbol.DigitNb < 2) ? 2 : this.drawObj_0.Chart.Symbol.DigitNb;
				this.txtBox_yVal.Text = value.ToString("F" + num.ToString());
			}
		}

		// Token: 0x17000417 RID: 1047
		// (get) Token: 0x06001874 RID: 6260 RVA: 0x000A3324 File Offset: 0x000A1524
		// (set) Token: 0x06001875 RID: 6261 RVA: 0x000A333C File Offset: 0x000A153C
		public int Index
		{
			get
			{
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
				string str = "①";
				if (value == 2)
				{
					str = "②";
				}
				this.groupBox1.Text = "端点" + str + "坐标";
			}
		}

		// Token: 0x06001876 RID: 6262 RVA: 0x00009FEE File Offset: 0x000081EE
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001877 RID: 6263 RVA: 0x000A3380 File Offset: 0x000A1580
		private void InitializeComponent()
		{
			this.dtPicker_time = new DateTimePicker();
			this.dtPicker_date = new DateTimePicker();
			this.groupBox1 = new GroupBox();
			this.txtBox_yVal = new TextBox();
			this.label2 = new Label();
			this.label1 = new Label();
			this.groupBox1.SuspendLayout();
			base.SuspendLayout();
			this.dtPicker_time.Format = DateTimePickerFormat.Time;
			this.dtPicker_time.Location = new Point(236, 31);
			this.dtPicker_time.Name = "dtPicker_time";
			this.dtPicker_time.ShowUpDown = true;
			this.dtPicker_time.Size = new Size(113, 25);
			this.dtPicker_time.TabIndex = 1;
			this.dtPicker_date.Location = new Point(71, 31);
			this.dtPicker_date.Name = "dtPicker_date";
			this.dtPicker_date.Size = new Size(151, 25);
			this.dtPicker_date.TabIndex = 2;
			this.groupBox1.Controls.Add(this.txtBox_yVal);
			this.groupBox1.Controls.Add(this.label2);
			this.groupBox1.Controls.Add(this.label1);
			this.groupBox1.Controls.Add(this.dtPicker_time);
			this.groupBox1.Controls.Add(this.dtPicker_date);
			this.groupBox1.Location = new Point(3, 3);
			this.groupBox1.Name = "groupBox1";
			this.groupBox1.Size = new Size(370, 120);
			this.groupBox1.TabIndex = 3;
			this.groupBox1.TabStop = false;
			this.groupBox1.Text = "端点①坐标";
			this.txtBox_yVal.Location = new Point(71, 75);
			this.txtBox_yVal.Name = "txtBox_yVal";
			this.txtBox_yVal.Size = new Size(151, 25);
			this.txtBox_yVal.TabIndex = 5;
			this.label2.AutoSize = true;
			this.label2.Location = new Point(17, 78);
			this.label2.Name = "label2";
			this.label2.Size = new Size(45, 15);
			this.label2.TabIndex = 4;
			this.label2.Text = "价格:";
			this.label1.AutoSize = true;
			this.label1.Location = new Point(17, 35);
			this.label1.Name = "label1";
			this.label1.Size = new Size(45, 15);
			this.label1.TabIndex = 3;
			this.label1.Text = "时间:";
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.groupBox1);
			base.Name = "DrawObjDTVal";
			base.Size = new Size(376, 126);
			this.groupBox1.ResumeLayout(false);
			this.groupBox1.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x04000C38 RID: 3128
		private DateTime dateTime_0;

		// Token: 0x04000C39 RID: 3129
		private double double_0;

		// Token: 0x04000C3A RID: 3130
		private int int_0;

		// Token: 0x04000C3B RID: 3131
		private DrawObj drawObj_0;

		// Token: 0x04000C3C RID: 3132
		private IContainer icontainer_0;

		// Token: 0x04000C3D RID: 3133
		private DateTimePicker dtPicker_time;

		// Token: 0x04000C3E RID: 3134
		private DateTimePicker dtPicker_date;

		// Token: 0x04000C3F RID: 3135
		private GroupBox groupBox1;

		// Token: 0x04000C40 RID: 3136
		private TextBox txtBox_yVal;

		// Token: 0x04000C41 RID: 3137
		private Label label2;

		// Token: 0x04000C42 RID: 3138
		private Label label1;
	}
}
