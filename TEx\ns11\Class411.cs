﻿using System;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using ns28;
using TEx.SIndicator;

namespace ns11
{
	// Token: 0x0200032F RID: 815
	internal sealed class Class411 : Class409
	{
		// Token: 0x06002277 RID: 8823 RVA: 0x0000D8CF File Offset: 0x0000BACF
		public Class411(HToken htoken_1) : base(htoken_1)
		{
		}

		// Token: 0x06002278 RID: 8824 RVA: 0x000EAD94 File Offset: 0x000E8F94
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			return parserEnvironment_0.Properties.Single(new Func<PropertyInfo, bool>(this.method_1)).GetValue(parserEnvironment_0.UserDefineIns, null);
		}

		// Token: 0x06002279 RID: 8825 RVA: 0x000EADC8 File Offset: 0x000E8FC8
		[CompilerGenerated]
		private bool method_1(PropertyInfo propertyInfo_0)
		{
			return propertyInfo_0.Name == this.Token.Symbol.Name;
		}
	}
}
