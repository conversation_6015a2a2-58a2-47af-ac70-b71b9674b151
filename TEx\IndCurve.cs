﻿using System;
using TEx.Chart;

namespace TEx
{
	// Token: 0x020002A8 RID: 680
	internal sealed class IndCurve
	{
		// Token: 0x06001E20 RID: 7712 RVA: 0x0000C96E File Offset: 0x0000AB6E
		public IndCurve(string desc, CurveItem curve, bool isVisible)
		{
			this._Desc = desc;
			this._Curve = curve;
			this._IsVisible = isVisible;
		}

		// Token: 0x170004C6 RID: 1222
		// (get) Token: 0x06001E21 RID: 7713 RVA: 0x000CCF1C File Offset: 0x000CB11C
		// (set) Token: 0x06001E22 RID: 7714 RVA: 0x0000C98D File Offset: 0x0000AB8D
		public string Desc
		{
			get
			{
				return this._Desc;
			}
			private set
			{
				this._Desc = value;
			}
		}

		// Token: 0x170004C7 RID: 1223
		// (get) Token: 0x06001E23 RID: 7715 RVA: 0x000CCF34 File Offset: 0x000CB134
		// (set) Token: 0x06001E24 RID: 7716 RVA: 0x0000C998 File Offset: 0x0000AB98
		public CurveItem Curve
		{
			get
			{
				return this._Curve;
			}
			private set
			{
				this._Curve = value;
			}
		}

		// Token: 0x170004C8 RID: 1224
		// (get) Token: 0x06001E25 RID: 7717 RVA: 0x000CCF4C File Offset: 0x000CB14C
		public bool IsVisible
		{
			get
			{
				return this._IsVisible;
			}
		}

		// Token: 0x04000ED6 RID: 3798
		protected string _Desc;

		// Token: 0x04000ED7 RID: 3799
		protected CurveItem _Curve;

		// Token: 0x04000ED8 RID: 3800
		protected bool _IsVisible;
	}
}
