﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Runtime.Serialization;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000068 RID: 104
	[Serializable]
	internal sealed class DrawEllipse : DrawObj, ISerializable
	{
		// Token: 0x060003D8 RID: 984 RVA: 0x000037AA File Offset: 0x000019AA
		public DrawEllipse()
		{
		}

		// Token: 0x060003D9 RID: 985 RVA: 0x00003A13 File Offset: 0x00001C13
		public DrawEllipse(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = "椭圆";
			base.CanChgColor = true;
			base.IsOneClickLoc = false;
		}

		// Token: 0x060003DA RID: 986 RVA: 0x000037DC File Offset: 0x000019DC
		protected DrawEllipse(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x060003DB RID: 987 RVA: 0x000037ED File Offset: 0x000019ED
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x060003DC RID: 988 RVA: 0x00021A78 File Offset: 0x0001FC78
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			EllipseObj item = this.method_39(chartCS_1, double_1, double_2, double_3, double_4, string_5);
			list.Add(item);
			return list;
		}

		// Token: 0x060003DD RID: 989 RVA: 0x00021AA8 File Offset: 0x0001FCA8
		protected EllipseObj method_39(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			Color borderColor = base.method_26();
			double num = Math.Min(double_1, double_3);
			double num2 = Math.Min(double_2, double_4);
			double num3 = Math.Abs(double_3 - double_1);
			double num4 = Math.Abs(double_4 - double_2);
			double num5 = num4 * 2.0;
			double num6 = 0.0;
			if ((double_1 < double_3 && double_2 > double_4) || (double_3 < double_1 && double_4 > double_2))
			{
				num6 = 0.0;
			}
			else if ((double_1 < double_3 && double_4 > double_2) || (double_3 < double_1 && double_2 > double_4))
			{
				num6 = -num3;
			}
			return new EllipseObj(num + num6, num2 + num5, num3 * 2.0, num4 * 2.0, borderColor, Color.Transparent)
			{
				Location = 
				{
					AlignV = AlignV.Bottom
				},
				IsClippedToChartRect = true,
				ZOrder = ZOrder.A_InFront,
				Tag = string_5
			};
		}

		// Token: 0x060003DE RID: 990 RVA: 0x0001FB74 File Offset: 0x0001DD74
		protected override DrawLineStyle vmethod_23()
		{
			return null;
		}
	}
}
