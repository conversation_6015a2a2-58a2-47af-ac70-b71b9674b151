﻿using System;
using System.Collections.Generic;
using System.Drawing.Drawing2D;
using System.Runtime.Serialization;
using ns15;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000066 RID: 102
	[Serializable]
	internal class DrawDegree45Up : DrawObj, ISerializable
	{
		// Token: 0x0600035C RID: 860 RVA: 0x000037AA File Offset: 0x000019AA
		public DrawDegree45Up()
		{
		}

		// Token: 0x0600035D RID: 861 RVA: 0x000037B2 File Offset: 0x000019B2
		public DrawDegree45Up(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = "上45度";
			base.CanChgColor = true;
			base.IsOneClickLoc = true;
		}

		// Token: 0x0600035E RID: 862 RVA: 0x000037DC File Offset: 0x000019DC
		protected DrawDegree45Up(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x0600035F RID: 863 RVA: 0x000037ED File Offset: 0x000019ED
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x06000360 RID: 864 RVA: 0x0001FA3C File Offset: 0x0001DC3C
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			foreach (LineObj item in this.vmethod_24(chartCS_1, double_3, double_4, string_5))
			{
				list.Add(item);
			}
			return list;
		}

		// Token: 0x06000361 RID: 865 RVA: 0x0001FAA4 File Offset: 0x0001DCA4
		protected virtual List<LineObj> vmethod_24(ChartCS chartCS_1, double double_1, double double_2, string string_5)
		{
			return this.method_39(chartCS_1, double_1, double_2, string_5, true);
		}

		// Token: 0x06000362 RID: 866 RVA: 0x0001FAC4 File Offset: 0x0001DCC4
		protected List<LineObj> method_39(ChartCS chartCS_1, double double_1, double double_2, string string_5, bool bool_6)
		{
			List<LineObj> list = new List<LineObj>();
			base.method_26();
			double max = chartCS_1.GraphPane.XAxis.Scale.Max;
			LineObj lineObj = base.method_23(double_1, double_2, max, double_2, string_5);
			lineObj.Line.Style = DashStyle.Dash;
			list.Add(lineObj);
			double num = double_1 + (max - double_1) / 2.0;
			double num2 = (num - double_1) * chartCS_1.WidthHeightAdjRatio;
			double double_3;
			if (bool_6)
			{
				double_3 = double_2 + num2;
			}
			else
			{
				double_3 = double_2 - num2;
			}
			Class58 @class = DrawLine.smethod_0(chartCS_1, double_1, double_2, num, double_3);
			LineObj item = base.method_23(double_1, double_2, @class.X2, @class.Y2, string_5);
			list.Add(item);
			return list;
		}

		// Token: 0x06000363 RID: 867 RVA: 0x000037F9 File Offset: 0x000019F9
		public override void vmethod_17(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4)
		{
			base.vmethod_17(chartCS_1, double_3, double_4, double_3, double_4);
		}

		// Token: 0x06000364 RID: 868 RVA: 0x0000380C File Offset: 0x00001A0C
		public override void vmethod_18(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, bool bool_6, bool bool_7)
		{
			base.vmethod_18(chartCS_1, double_3, double_4, double_3, double_4, bool_6, bool_7);
		}

		// Token: 0x06000365 RID: 869 RVA: 0x0001FB74 File Offset: 0x0001DD74
		protected override DrawLineStyle vmethod_23()
		{
			return null;
		}
	}
}
