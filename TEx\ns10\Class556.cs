﻿using System;
using TEx;
using TEx.Util;

namespace ns10
{
	// Token: 0x0200001F RID: 31
	internal class Class556 : WebApiBgWorker
	{
		// Token: 0x060000B1 RID: 177 RVA: 0x00002D2D File Offset: 0x00000F2D
		public Class556() : base(TApp.FULLHOST + "/Api/TExApi.ashx", 30000)
		{
		}

		// Token: 0x060000B2 RID: 178 RVA: 0x00012838 File Offset: 0x00010A38
		protected string method_2(string string_1)
		{
			int length = string_1.IndexOf(".");
			return string_1.Substring(0, length);
		}

		// Token: 0x04000042 RID: 66
		private const int int_1 = 30000;
	}
}
