﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns14;
using ns18;
using ns7;
using TEx.Comn;
using TEx.Inds;

namespace TEx.SIndicator
{
	// Token: 0x0200033A RID: 826
	public sealed class UserDefineInd : SIndicatorBase
	{
		// Token: 0x060022CE RID: 8910 RVA: 0x0000DBDB File Offset: 0x0000BDDB
		public UserDefineInd(DataProvider dp, UserDefineIndScript uds) : base(dp)
		{
			this.uds = uds;
		}

		// Token: 0x060022CF RID: 8911 RVA: 0x0000DBED File Offset: 0x0000BDED
		public UserDefineInd(DataProvider dp) : base(dp)
		{
			this.uds = new UserDefineIndScript();
		}

		// Token: 0x060022D0 RID: 8912 RVA: 0x0000DC03 File Offset: 0x0000BE03
		public void method_6(UserDefineIndScript userDefineIndScript_0)
		{
			this.UDS = userDefineIndScript_0;
		}

		// Token: 0x060022D1 RID: 8913 RVA: 0x000EC924 File Offset: 0x000EAB24
		public bool method_7(DataProvider dataProvider_1)
		{
			base.DataProvider = dataProvider_1;
			return this.method_8();
		}

		// Token: 0x060022D2 RID: 8914 RVA: 0x000EC944 File Offset: 0x000EAB44
		protected bool method_8()
		{
			this.list_0 = new List<DataArray>();
			DataArray[] array = this.method_9();
			bool result;
			if (array != null)
			{
				this.list_0 = array.ToList<DataArray>();
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060022D3 RID: 8915 RVA: 0x000EC97C File Offset: 0x000EAB7C
		private static void smethod_4(ref SortedList<DateTime, HisData> sortedList_0)
		{
			for (int i = 0; i < 10; i++)
			{
				DateTime key = DateTime.Now.AddDays((double)i);
				HisData hisData = new HisData();
				hisData.Close = (double)(i + 3);
				hisData.Open = (double)(i + 2);
				hisData.High = (double)(i + 5);
				hisData.Low = (double)(i + 1);
				sortedList_0.Add(key, hisData);
			}
		}

		// Token: 0x060022D4 RID: 8916 RVA: 0x000EC9E0 File Offset: 0x000EABE0
		public static bool smethod_5(UserDefineIndScript userDefineIndScript_0, TradingSymbol tradingSymbol_0)
		{
			bool result;
			try
			{
				SortedList<DateTime, HisData> datalist = new SortedList<DateTime, HisData>();
				UserDefineInd.smethod_4(ref datalist);
				result = (new UserDefineInd(new DataProvider(datalist, tradingSymbol_0), userDefineIndScript_0).method_9() != null);
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message, "文本错误", MessageBoxButtons.OK, MessageBoxIcon.Hand);
				result = false;
			}
			return result;
		}

		// Token: 0x060022D5 RID: 8917 RVA: 0x000ECA40 File Offset: 0x000EAC40
		public DataArray[] method_9()
		{
			DataArray[] result;
			try
			{
				result = this.method_10();
			}
			catch (Exception ex)
			{
				string text = "公式解析错误！";
				if (!ex.Message.StartsWith("调用的目标"))
				{
					text = ex.Message;
				}
				if (ex.InnerException != null)
				{
					text += ex.InnerException.Message;
				}
				MessageBox.Show(text, "解析错误", MessageBoxButtons.OK, MessageBoxIcon.Hand);
				result = null;
			}
			return result;
		}

		// Token: 0x060022D6 RID: 8918 RVA: 0x000ECAB8 File Offset: 0x000EACB8
		private DataArray[] method_10()
		{
			this.parserEnvironment_0 = new ParserEnvironment(this.UDS.UserDefineParams, this);
			Tokenes tokenes = new Class437(this.parserEnvironment_0).method_1(this.CodeText);
			double num = ParserEnvironment.smethod_1(tokenes.method_0());
			this.UDS.Ver = num;
			if (num > TApp.double_0)
			{
				throw new Exception(string.Concat(new object[]
				{
					"指标",
					this.Name,
					"的版本号",
					num,
					"大于系统所允许的版本号，请升级软件。"
				}));
			}
			this.UDS.Ver = num;
			Class441 @class = new Class441(this.parserEnvironment_0);
			this.class408_0 = @class.method_0(tokenes);
			object obj = this.class408_0.vmethod_1(this.parserEnvironment_0);
			this.list_0 = (List<DataArray>)obj;
			this.HasLast = this.list_0.Any(new Func<DataArray, bool>(UserDefineInd.<>c.<>9.method_0));
			return this.list_0.ToArray();
		}

		// Token: 0x060022D7 RID: 8919 RVA: 0x000ECBD4 File Offset: 0x000EADD4
		public DataArray[] method_11()
		{
			DataArray[] result;
			try
			{
				object obj = this.class408_0.vmethod_1(this.parserEnvironment_0);
				this.list_0 = (List<DataArray>)obj;
				result = this.list_0.ToArray();
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Hand);
				result = null;
			}
			return result;
		}

		// Token: 0x170005FA RID: 1530
		// (get) Token: 0x060022D8 RID: 8920 RVA: 0x000ECC3C File Offset: 0x000EAE3C
		// (set) Token: 0x060022D9 RID: 8921 RVA: 0x0000DC0E File Offset: 0x0000BE0E
		public string CodeText
		{
			get
			{
				return this.uds.Code;
			}
			set
			{
				this.uds.Code = value;
			}
		}

		// Token: 0x170005FB RID: 1531
		// (get) Token: 0x060022DA RID: 8922 RVA: 0x000ECC58 File Offset: 0x000EAE58
		// (set) Token: 0x060022DB RID: 8923 RVA: 0x0000DC1E File Offset: 0x0000BE1E
		public bool HasLast { get; private set; }

		// Token: 0x170005FC RID: 1532
		// (get) Token: 0x060022DC RID: 8924 RVA: 0x000ECC70 File Offset: 0x000EAE70
		public List<DataArray> DataList
		{
			get
			{
				return this.list_0;
			}
		}

		// Token: 0x170005FD RID: 1533
		// (get) Token: 0x060022DD RID: 8925 RVA: 0x000ECC88 File Offset: 0x000EAE88
		// (set) Token: 0x060022DE RID: 8926 RVA: 0x0000DC29 File Offset: 0x0000BE29
		public UserDefineIndScript UDS
		{
			get
			{
				return this.uds;
			}
			private set
			{
				this.uds = value;
			}
		}

		// Token: 0x170005FE RID: 1534
		// (get) Token: 0x060022DF RID: 8927 RVA: 0x000ECCA0 File Offset: 0x000EAEA0
		public string Name
		{
			get
			{
				return this.uds.Name;
			}
		}

		// Token: 0x170005FF RID: 1535
		// (get) Token: 0x060022E0 RID: 8928 RVA: 0x000ECCBC File Offset: 0x000EAEBC
		// (set) Token: 0x060022E1 RID: 8929 RVA: 0x0000DC34 File Offset: 0x0000BE34
		public Class408 ProgrameTree
		{
			get
			{
				return this.class408_0;
			}
			set
			{
				this.class408_0 = value;
			}
		}

		// Token: 0x17000600 RID: 1536
		// (get) Token: 0x060022E2 RID: 8930 RVA: 0x000ECCD4 File Offset: 0x000EAED4
		// (set) Token: 0x060022E3 RID: 8931 RVA: 0x0000DC3F File Offset: 0x0000BE3F
		public ParserEnvironment PE
		{
			get
			{
				return this.parserEnvironment_0;
			}
			set
			{
				this.parserEnvironment_0 = value;
			}
		}

		// Token: 0x040010E3 RID: 4323
		[CompilerGenerated]
		private bool bool_0;

		// Token: 0x040010E4 RID: 4324
		private List<DataArray> list_0;

		// Token: 0x040010E5 RID: 4325
		private UserDefineIndScript uds;

		// Token: 0x040010E6 RID: 4326
		private Class408 class408_0;

		// Token: 0x040010E7 RID: 4327
		private ParserEnvironment parserEnvironment_0;
	}
}
