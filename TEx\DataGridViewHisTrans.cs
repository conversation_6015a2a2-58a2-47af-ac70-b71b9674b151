﻿using System;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using ns21;
using ns27;
using ns28;
using ns33;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x0200020F RID: 527
	internal sealed class DataGridViewHisTrans : Class290
	{
		// Token: 0x1400007F RID: 127
		// (add) Token: 0x06001592 RID: 5522 RVA: 0x0008E820 File Offset: 0x0008CA20
		// (remove) Token: 0x06001593 RID: 5523 RVA: 0x0008E858 File Offset: 0x0008CA58
		public event Delegate22 ChangeToHisTransDTRequested
		{
			[CompilerGenerated]
			add
			{
				Delegate22 @delegate = this.delegate22_0;
				Delegate22 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate22 value2 = (Delegate22)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate22>(ref this.delegate22_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate22 @delegate = this.delegate22_0;
				Delegate22 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate22 value2 = (Delegate22)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate22>(ref this.delegate22_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06001594 RID: 5524 RVA: 0x0008E890 File Offset: 0x0008CA90
		protected void method_5(ShownHisTrans shownHisTrans_0)
		{
			EventArgs16 e = new EventArgs16(shownHisTrans_0);
			Delegate22 @delegate = this.delegate22_0;
			if (@delegate != null)
			{
				@delegate(e);
			}
		}

		// Token: 0x06001595 RID: 5525 RVA: 0x000089BA File Offset: 0x00006BBA
		public DataGridViewHisTrans()
		{
			base.MouseDoubleClick += this.DataGridViewHisTrans_MouseDoubleClick;
			base.Resize += this.DataGridViewHisTrans_Resize;
		}

		// Token: 0x06001596 RID: 5526 RVA: 0x000089E8 File Offset: 0x00006BE8
		public DataGridViewHisTrans(SortableBindingList<ShownHisTrans> hisLst) : this()
		{
			this.hisLst = hisLst;
		}

		// Token: 0x06001597 RID: 5527 RVA: 0x000089F9 File Offset: 0x00006BF9
		protected override void vmethod_1()
		{
			this.hisLst = Base.Trading.CurrHisTransList;
			base.DataSource = this.hisLst;
			this.method_12();
		}

		// Token: 0x06001598 RID: 5528 RVA: 0x00008A1A File Offset: 0x00006C1A
		public void method_6()
		{
			base.DataSource = null;
			this.vmethod_1();
		}

		// Token: 0x06001599 RID: 5529 RVA: 0x00008A2B File Offset: 0x00006C2B
		public void method_7(SortableBindingList<ShownHisTrans> sortableBindingList_0)
		{
			this.hisLst = sortableBindingList_0;
			this.method_6();
		}

		// Token: 0x0600159A RID: 5530 RVA: 0x0008E8B8 File Offset: 0x0008CAB8
		protected override void vmethod_0()
		{
			ToolStripMenuItem toolStripMenuItem = new ToolStripMenuItem();
			toolStripMenuItem.Name = "otItemExport";
			toolStripMenuItem.Text = "导出记录...";
			toolStripMenuItem.Click += this.method_8;
			ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
			contextMenuStrip.Items.Add(toolStripMenuItem);
			contextMenuStrip.Opening += this.method_9;
			Base.UI.smethod_73(contextMenuStrip);
			this.ContextMenuStrip = contextMenuStrip;
		}

		// Token: 0x0600159B RID: 5531 RVA: 0x0008E928 File Offset: 0x0008CB28
		protected override void vmethod_3(DataGridViewCellFormattingEventArgs dataGridViewCellFormattingEventArgs_0)
		{
			base.vmethod_3(dataGridViewCellFormattingEventArgs_0);
			if (base.Columns[dataGridViewCellFormattingEventArgs_0.ColumnIndex].DataPropertyName == Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => (object)t.Profit))
			{
				DataGridViewCell dataGridViewCell_ = base.Rows[dataGridViewCellFormattingEventArgs_0.RowIndex].Cells[dataGridViewCellFormattingEventArgs_0.ColumnIndex];
				base.method_3(dataGridViewCell_);
			}
		}

		// Token: 0x0600159C RID: 5532 RVA: 0x00008A3C File Offset: 0x00006C3C
		private void method_8(object sender, EventArgs e)
		{
			this.method_10(base.DataSource as SortableBindingList<ShownHisTrans>);
		}

		// Token: 0x0600159D RID: 5533 RVA: 0x0008E9D4 File Offset: 0x0008CBD4
		private void method_9(object sender, CancelEventArgs e)
		{
			if (base.DataSource != null && (base.DataSource as SortableBindingList<ShownHisTrans>).Count >= 1)
			{
				this.ContextMenuStrip.Items[0].Enabled = true;
			}
			else
			{
				this.ContextMenuStrip.Items[0].Enabled = false;
			}
		}

		// Token: 0x0600159E RID: 5534 RVA: 0x0008EA30 File Offset: 0x0008CC30
		public void method_10(SortableBindingList<ShownHisTrans> sortableBindingList_0)
		{
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.Title = "导出记录至...";
			saveFileDialog.Filter = "csv文件|*.csv";
			saveFileDialog.FileName = "TExTrans.csv";
			saveFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Personal);
			try
			{
				if (saveFileDialog.ShowDialog() == DialogResult.OK)
				{
					string fileName = saveFileDialog.FileName;
					this.method_11(sortableBindingList_0, saveFileDialog.FileName);
					MessageBox.Show("记录已成功导出！", "确认", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
				}
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x0600159F RID: 5535 RVA: 0x0008EABC File Offset: 0x0008CCBC
		private void method_11(SortableBindingList<ShownHisTrans> sortableBindingList_0, string string_0)
		{
			try
			{
				using (StreamWriter streamWriter = new StreamWriter(string_0, false, Encoding.GetEncoding("GB2312")))
				{
					streamWriter.WriteLine("代码,买卖,开平,成交时间,价格,手数,盈利,手续费,注释");
					foreach (ShownHisTrans shownHisTrans in sortableBindingList_0)
					{
						string value = string.Concat(new object[]
						{
							shownHisTrans.SymblCode,
							",",
							shownHisTrans.TransTypeDesc.Substring(0, 1),
							",",
							shownHisTrans.TransTypeDesc.Substring(1, 1),
							",",
							shownHisTrans.CreateTime,
							",",
							Utility.GetStringWithoutEndZero(new decimal?(shownHisTrans.Price)),
							",",
							shownHisTrans.Units,
							",",
							Utility.GetStringWithoutEndZero(shownHisTrans.Profit),
							",",
							Utility.GetStringWithoutEndZero(shownHisTrans.Fee),
							",",
							shownHisTrans.Notes
						});
						streamWriter.WriteLine(value);
					}
				}
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x060015A0 RID: 5536 RVA: 0x0008EC50 File Offset: 0x0008CE50
		private void DataGridViewHisTrans_MouseDoubleClick(object sender, MouseEventArgs e)
		{
			DataGridView dataGridView = sender as DataGridView;
			int columnIndex = dataGridView.HitTest(e.X, e.Y).ColumnIndex;
			int rowIndex = dataGridView.HitTest(e.X, e.Y).RowIndex;
			if (columnIndex >= 0 && rowIndex >= 0)
			{
				ShownHisTrans shownHisTrans = dataGridView.Rows[rowIndex].DataBoundItem as ShownHisTrans;
				if (Base.Data.UsrStkSymbols.ContainsKey(shownHisTrans.SymbolID))
				{
					if ((shownHisTrans.SymbolID != Base.Data.CurrSelectedSymbol.ID || !(shownHisTrans.CreateTime == Base.Data.CurrDate)) && !Base.UI.Form.IsInBlindTestMode && MessageBox.Show("跳转行情至该历史记录的成交时间吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
					{
						this.method_5(shownHisTrans);
					}
				}
				else
				{
					MessageBox.Show("您未拥有该品种历史行情数据，无法跳转至该品种。", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
			}
		}

		// Token: 0x060015A1 RID: 5537 RVA: 0x0008ED30 File Offset: 0x0008CF30
		private void method_12()
		{
			if (base.Columns.Count > 0)
			{
				base.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => (object)t.Price)].HeaderText = "成交价格";
				base.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => (object)t.OpenUnits)].Visible = false;
				base.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => (object)t.Profit)].HeaderText = "盈亏";
				base.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => (object)t.UpdateTime)].Visible = false;
				if (Base.UI.Form.IsInBlindTestMode)
				{
					if (!Base.UI.Form.IsSingleBlindTest)
					{
						base.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => t.SymblCode)].Visible = false;
					}
					base.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => (object)t.CreateTime)].Visible = false;
				}
				else
				{
					base.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => t.SymblCode)].Visible = true;
					base.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => (object)t.CreateTime)].Visible = true;
				}
				this.Refresh();
			}
		}

		// Token: 0x060015A2 RID: 5538 RVA: 0x000041AE File Offset: 0x000023AE
		private void DataGridViewHisTrans_Resize(object sender, EventArgs e)
		{
		}

		// Token: 0x060015A3 RID: 5539 RVA: 0x0008F050 File Offset: 0x0008D250
		private void method_13(DataGridView dataGridView_0)
		{
			int num = 0;
			VScrollBar vscrollBar = dataGridView_0.Controls.OfType<VScrollBar>().First<VScrollBar>();
			if (vscrollBar.Visible)
			{
				num = vscrollBar.Width;
			}
			int num2 = dataGridView_0.Parent.Width - num;
			int num3 = 0;
			decimal d;
			bool flag;
			if (!Base.UI.Form.IsInBlindTestMode)
			{
				d = 669m;
				flag = (num2 - num3 > d);
				num2 -= num3;
				try
				{
					dataGridView_0.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => t.SymblCode)].Width = (flag ? Convert.ToInt32(Math.Floor(60 * num2 / d)) : 60);
					dataGridView_0.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => t.TransTypeDesc)].Width = (flag ? Convert.ToInt32(Math.Floor(44 * num2 / d)) : 44);
					dataGridView_0.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => (object)t.Units)].Width = (flag ? Convert.ToInt32(Math.Floor(75 * num2 / d)) : 75);
					dataGridView_0.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => (object)t.Price)].Width = (flag ? Convert.ToInt32(Math.Floor(75 * num2 / d)) : 75);
					dataGridView_0.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => (object)t.Fee)].Width = (flag ? Convert.ToInt32(Math.Floor(75 * num2 / d)) : 75);
					dataGridView_0.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => (object)t.Profit)].Width = (flag ? Convert.ToInt32(Math.Floor(80 * num2 / d)) : 80);
					dataGridView_0.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => (object)t.CreateTime)].Width = (flag ? Convert.ToInt32(Math.Floor(120 * num2 / d)) : 120);
					dataGridView_0.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => t.Notes)].Width = (flag ? Convert.ToInt32(Math.Floor(140 * num2 / d)) : 140);
					return;
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
					return;
				}
			}
			if (!Base.UI.Form.IsSingleBlindTest)
			{
				d = 489m;
				flag = (num2 - num3 > d);
				num2 -= num3;
			}
			else
			{
				d = 609m;
				flag = (num2 - num3 > d);
				num2 -= num3;
				dataGridView_0.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => t.SymblCode)].Width = (flag ? Convert.ToInt32(Math.Floor(60 * num2 / d)) : 60);
			}
			try
			{
				dataGridView_0.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => t.TransTypeDesc)].Width = (flag ? Convert.ToInt32(Math.Floor(44 * num2 / d)) : 44);
				dataGridView_0.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => (object)t.Units)].Width = (flag ? Convert.ToInt32(Math.Floor(75 * num2 / d)) : 75);
				dataGridView_0.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => (object)t.Price)].Width = (flag ? Convert.ToInt32(Math.Floor(75 * num2 / d)) : 75);
				dataGridView_0.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => (object)t.Fee)].Width = (flag ? Convert.ToInt32(Math.Floor(75 * num2 / d)) : 75);
				dataGridView_0.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => (object)t.Profit)].Width = (flag ? Convert.ToInt32(Math.Floor(80 * num2 / d)) : 80);
				dataGridView_0.Columns[Utility.GetPropertyName<ShownHisTrans>((ShownHisTrans t) => t.Notes)].Width = (flag ? Convert.ToInt32(Math.Floor(140 * num2 / d)) : 140);
			}
			catch (Exception exception_2)
			{
				Class182.smethod_0(exception_2);
			}
		}

		// Token: 0x04000B0E RID: 2830
		private SortableBindingList<ShownHisTrans> hisLst;

		// Token: 0x04000B0F RID: 2831
		[CompilerGenerated]
		private Delegate22 delegate22_0;
	}
}
