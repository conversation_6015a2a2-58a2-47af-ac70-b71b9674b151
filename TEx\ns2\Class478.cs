﻿using System;
using System.Runtime.CompilerServices;
using TEx.ImportTrans;

namespace ns2
{
	// Token: 0x02000377 RID: 887
	internal sealed class Class478
	{
		// Token: 0x1700063E RID: 1598
		// (get) Token: 0x060024C9 RID: 9417 RVA: 0x000F57F8 File Offset: 0x000F39F8
		// (set) Token: 0x060024CA RID: 9418 RVA: 0x0000E3F0 File Offset: 0x0000C5F0
		public bool TimerEnabled { get; set; }

		// Token: 0x1700063F RID: 1599
		// (get) Token: 0x060024CB RID: 9419 RVA: 0x000F5810 File Offset: 0x000F3A10
		// (set) Token: 0x060024CC RID: 9420 RVA: 0x0000E3FB File Offset: 0x0000C5FB
		public int TimerInterval { get; set; }

		// Token: 0x17000640 RID: 1600
		// (get) Token: 0x060024CD RID: 9421 RVA: 0x000F5828 File Offset: 0x000F3A28
		// (set) Token: 0x060024CE RID: 9422 RVA: 0x0000E406 File Offset: 0x0000C606
		public bool DownEnable { get; set; }

		// Token: 0x060024CF RID: 9423 RVA: 0x000F5840 File Offset: 0x000F3A40
		public Class478(CfmmcAutoDnldConfig cfmmcAutoDnldConfig_0)
		{
			DateTime now = DateTime.Now;
			if (cfmmcAutoDnldConfig_0 == null)
			{
				this.TimerEnabled = false;
				this.DownEnable = false;
			}
			else
			{
				if (cfmmcAutoDnldConfig_0.AutoDownOnStartup)
				{
					this.DownEnable = true;
				}
				if (cfmmcAutoDnldConfig_0.AutoDownPeriodly)
				{
					DateTime beginTime = cfmmcAutoDnldConfig_0.BeginTime;
					if (beginTime.TimeOfDay > now.TimeOfDay)
					{
						double totalMilliseconds = (beginTime.TimeOfDay - now.TimeOfDay).TotalMilliseconds;
						this.TimerInterval = Convert.ToInt32(totalMilliseconds);
						this.DownEnable = false;
					}
					else
					{
						DateTime d;
						if (cfmmcAutoDnldConfig_0.Frequency == AutoDownCfmmcFrequencyEnum.每天)
						{
							d = now.Date.AddDays(1.0).Add(cfmmcAutoDnldConfig_0.BeginTime.TimeOfDay);
						}
						else
						{
							int num = cfmmcAutoDnldConfig_0.WklyDnldDayOfWeek - now.DayOfWeek;
							if (num < 0)
							{
								num += 7;
							}
							d = now.Date.AddDays((double)num).Add(cfmmcAutoDnldConfig_0.BeginTime.TimeOfDay);
						}
						double totalMilliseconds2 = (d - now).TotalMilliseconds;
						this.TimerInterval = Convert.ToInt32(totalMilliseconds2);
						this.DownEnable = true;
					}
					this.TimerEnabled = true;
				}
			}
		}

		// Token: 0x040011B7 RID: 4535
		[CompilerGenerated]
		private bool bool_0;

		// Token: 0x040011B8 RID: 4536
		[CompilerGenerated]
		private int int_0;

		// Token: 0x040011B9 RID: 4537
		[CompilerGenerated]
		private bool bool_1;
	}
}
