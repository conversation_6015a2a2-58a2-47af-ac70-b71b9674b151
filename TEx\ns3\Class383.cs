﻿using System;
using System.Drawing;
using ns5;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns3
{
	// Token: 0x020002FD RID: 765
	internal sealed class Class383 : Class381
	{
		// Token: 0x06002142 RID: 8514 RVA: 0x000E3DBC File Offset: 0x000E1FBC
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			Ind_TrenderLines lineItem_ = zedGraphControl_0.GraphPane.AddTenderLines(base.IndData.Name, base.DataView, color_0, SymbolType.None);
			base.method_3(string_0, lineItem_);
			this.int_0 = 1;
		}

		// Token: 0x06002143 RID: 8515 RVA: 0x0000D560 File Offset: 0x0000B760
		public Class383(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}
	}
}
