﻿using System;
using System.Runtime.CompilerServices;

namespace ns33
{
	// Token: 0x02000304 RID: 772
	internal sealed class EventArgs33 : EventArgs
	{
		// Token: 0x170005C4 RID: 1476
		// (get) Token: 0x0600215A RID: 8538 RVA: 0x000E44F4 File Offset: 0x000E26F4
		// (set) Token: 0x0600215B RID: 8539 RVA: 0x0000D67A File Offset: 0x0000B87A
		public string Group { get; set; }

		// Token: 0x0600215C RID: 8540 RVA: 0x0000D685 File Offset: 0x0000B885
		public EventArgs33(string string_1)
		{
			this.Group = string_1;
		}

		// Token: 0x04001039 RID: 4153
		[CompilerGenerated]
		private string string_0;
	}
}
