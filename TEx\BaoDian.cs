﻿using System;
using System.Drawing;
using TEx.Comn;

namespace TEx
{
	// Token: 0x02000033 RID: 51
	[Serializable]
	public sealed class BaoDian
	{
		// Token: 0x17000059 RID: 89
		// (get) Token: 0x06000158 RID: 344 RVA: 0x00016A88 File Offset: 0x00014C88
		// (set) Token: 0x06000159 RID: 345 RVA: 0x00002F98 File Offset: 0x00001198
		public string Name { get; set; }

		// Token: 0x1700005A RID: 90
		// (get) Token: 0x0600015A RID: 346 RVA: 0x00016AA0 File Offset: 0x00014CA0
		// (set) Token: 0x0600015B RID: 347 RVA: 0x00002FA3 File Offset: 0x000011A3
		public string UID { get; set; }

		// Token: 0x1700005B RID: 91
		// (get) Token: 0x0600015C RID: 348 RVA: 0x00016AB8 File Offset: 0x00014CB8
		// (set) Token: 0x0600015D RID: 349 RVA: 0x00002FAE File Offset: 0x000011AE
		public string Group { get; set; }

		// Token: 0x1700005C RID: 92
		// (get) Token: 0x0600015E RID: 350 RVA: 0x00016AD0 File Offset: 0x00014CD0
		// (set) Token: 0x0600015F RID: 351 RVA: 0x00002FB9 File Offset: 0x000011B9
		public int SymbolID { get; set; }

		// Token: 0x1700005D RID: 93
		// (get) Token: 0x06000160 RID: 352 RVA: 0x00016AE8 File Offset: 0x00014CE8
		// (set) Token: 0x06000161 RID: 353 RVA: 0x00002FC4 File Offset: 0x000011C4
		public PeriodType PeriodType { get; set; }

		// Token: 0x1700005E RID: 94
		// (get) Token: 0x06000162 RID: 354 RVA: 0x00016B00 File Offset: 0x00014D00
		// (set) Token: 0x06000163 RID: 355 RVA: 0x00002FCF File Offset: 0x000011CF
		public int? PeriodUnit { get; set; }

		// Token: 0x1700005F RID: 95
		// (get) Token: 0x06000164 RID: 356 RVA: 0x00016B18 File Offset: 0x00014D18
		// (set) Token: 0x06000165 RID: 357 RVA: 0x00002FDA File Offset: 0x000011DA
		public DateTime SymbolTime { get; set; }

		// Token: 0x17000060 RID: 96
		// (get) Token: 0x06000166 RID: 358 RVA: 0x00016B30 File Offset: 0x00014D30
		// (set) Token: 0x06000167 RID: 359 RVA: 0x00002FE5 File Offset: 0x000011E5
		public Bitmap ScreenShot { get; set; }

		// Token: 0x17000061 RID: 97
		// (get) Token: 0x06000168 RID: 360 RVA: 0x00016B48 File Offset: 0x00014D48
		// (set) Token: 0x06000169 RID: 361 RVA: 0x00002FF0 File Offset: 0x000011F0
		public string Note { get; set; }

		// Token: 0x17000062 RID: 98
		// (get) Token: 0x0600016A RID: 362 RVA: 0x00016B60 File Offset: 0x00014D60
		// (set) Token: 0x0600016B RID: 363 RVA: 0x00002FFB File Offset: 0x000011FB
		public DateTime CreateTime { get; set; }
	}
}
