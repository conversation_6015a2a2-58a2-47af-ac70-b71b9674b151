﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns25;
using ns8;
using TEx;
using TEx.Trading;

namespace ns22
{
	// Token: 0x02000246 RID: 582
	internal sealed partial class CreateAcctForm : Form
	{
		// Token: 0x1400008F RID: 143
		// (add) Token: 0x060018C6 RID: 6342 RVA: 0x000A4EA8 File Offset: 0x000A30A8
		// (remove) Token: 0x060018C7 RID: 6343 RVA: 0x000A4EE0 File Offset: 0x000A30E0
		public event Delegate17 NewAcctCreated
		{
			[CompilerGenerated]
			add
			{
				Delegate17 @delegate = this.delegate17_0;
				Delegate17 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate17 value2 = (Delegate17)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate17>(ref this.delegate17_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate17 @delegate = this.delegate17_0;
				Delegate17 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate17 value2 = (Delegate17)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate17>(ref this.delegate17_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x060018C8 RID: 6344 RVA: 0x000A4F18 File Offset: 0x000A3118
		protected void method_0(int int_0, bool bool_0)
		{
			EventArgs13 e = new EventArgs13(int_0, bool_0);
			Delegate17 @delegate = this.delegate17_0;
			if (@delegate != null)
			{
				@delegate(this, e);
			}
		}

		// Token: 0x060018C9 RID: 6345 RVA: 0x0000A2D7 File Offset: 0x000084D7
		public CreateAcctForm()
		{
			this.InitializeComponent();
		}

		// Token: 0x060018CA RID: 6346 RVA: 0x000A4F44 File Offset: 0x000A3144
		private void button_OK_Click(object sender, EventArgs e)
		{
			CreateAcctForm.Class311 @class = new CreateAcctForm.Class311();
			@class.string_0 = this.textBox_AcctName.Text;
			if (@class.string_0.Length == 0)
			{
				MessageBox.Show("请输入账户名称（1-16字符）！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
			else if (Base.Acct.Accounts.Where(new Func<Account, bool>(@class.method_0)).Any<Account>())
			{
				MessageBox.Show("已有重名账户，请修改账户名称！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				this.textBox_AcctName.Focus();
			}
			else
			{
				int num = 0;
				try
				{
					num = Base.Acct.smethod_9(@class.string_0, this.numericUpDown_IniBal.Value, this.textBox_Notes.Text, this.chkBox_IsReadOnly.Checked);
				}
				catch (Exception ex)
				{
					throw ex;
				}
				if (num > 0)
				{
					bool bool_ = false;
					if (MessageBox.Show("新账户创建成功！现在就切换到该新账户吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
					{
						bool_ = true;
					}
					this.method_0(num, bool_);
					base.Close();
				}
			}
		}

		// Token: 0x060018CB RID: 6347 RVA: 0x000041AE File Offset: 0x000023AE
		private void CreateAcctForm_Load(object sender, EventArgs e)
		{
		}

		// Token: 0x060018CC RID: 6348 RVA: 0x0000A2E7 File Offset: 0x000084E7
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000C5A RID: 3162
		[CompilerGenerated]
		private Delegate17 delegate17_0;

		// Token: 0x04000C5B RID: 3163
		private IContainer icontainer_0;

		// Token: 0x02000247 RID: 583
		[CompilerGenerated]
		private sealed class Class311
		{
			// Token: 0x060018CF RID: 6351 RVA: 0x000A56C0 File Offset: 0x000A38C0
			internal bool method_0(Account account_0)
			{
				bool result;
				if (account_0.UserName == TApp.UserName)
				{
					result = (account_0.AcctName == this.string_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000C66 RID: 3174
			public string string_0;
		}
	}
}
