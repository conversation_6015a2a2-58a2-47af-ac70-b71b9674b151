﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using TEx.Chart;

namespace TEx
{
	// Token: 0x0200006A RID: 106
	[Serializable]
	internal sealed class DrawFibonacciLines : DrawLineV, ISerializable
	{
		// Token: 0x060003E5 RID: 997 RVA: 0x00003A67 File Offset: 0x00001C67
		public DrawFibonacciLines()
		{
		}

		// Token: 0x060003E6 RID: 998 RVA: 0x00003A6F File Offset: 0x00001C6F
		public DrawFibonacciLines(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = "斐波那契时间序列";
			base.CanChgColor = true;
		}

		// Token: 0x060003E7 RID: 999 RVA: 0x00003A92 File Offset: 0x00001C92
		protected DrawFibonacciLines(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x060003E8 RID: 1000 RVA: 0x00003AA3 File Offset: 0x00001CA3
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x060003E9 RID: 1001 RVA: 0x00021D50 File Offset: 0x0001FF50
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			LineObj item = base.method_22(chartCS_1, double_1, string_5);
			list.Add(item);
			double max = chartCS_1.GraphPane.XAxis.Scale.Max;
			double min = chartCS_1.GraphPane.YAxis.Scale.Min;
			foreach (DrawSublineParam drawSublineParam in base.SublineParamList)
			{
				if (drawSublineParam.Enabled)
				{
					double num = double_1 + drawSublineParam.Value;
					if (num >= max)
					{
						break;
					}
					LineObj item2 = base.method_22(chartCS_1, num, string_5);
					list.Add(item2);
					TextObj textObj = base.method_27(chartCS_1, num, min, drawSublineParam.Value.ToString("F" + drawSublineParam.DigitNb.ToString()), null, string_5);
					textObj.Location.AlignV = AlignV.Bottom;
					textObj.Location.AlignH = AlignH.Left;
					list.Add(textObj);
				}
			}
			return list;
		}

		// Token: 0x060003EA RID: 1002 RVA: 0x00021E7C File Offset: 0x0002007C
		protected override List<DrawSublineParam> vmethod_22()
		{
			List<double> list_ = new List<double>(new double[]
			{
				1.0,
				2.0,
				3.0,
				5.0,
				8.0,
				13.0,
				21.0,
				34.0,
				55.0,
				89.0
			});
			return base.method_28(list_, 1.0, 5000.0, 0);
		}
	}
}
