// 引入必要的命名空间
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Threading;
using System.Xml;
using NAppUpdate.Framework;
using NAppUpdate.Framework.Sources;
using NAppUpdate.Framework.Tasks;
using ns23;   // 内部命名空间，可能包含更新相关逻辑
using ns25;   // 内部命名空间，可能包含工具类
using TEx;    // 顶层命名空间
using TEx.Comn;
using TEx.Util;

namespace ns19
{
    /// <summary>
    /// 内部密封类 Class180，用于处理某种数据更新任务。
    /// 通过后台线程异步执行数据获取与更新操作。
    /// </summary>
    internal sealed class Class180
    {
        // ========== 构造函数 ==========

        /// <summary>
        /// 构造函数，初始化股票ID。
        /// </summary>
        /// <param name="int_1">股票ID</param>
        public Class180(int int_1)
        {
            this.int_0 = int_1;
        }

        /// <summary>
        /// 构造函数，初始化股票ID、起始年份和结束年份。
        /// </summary>
        /// <param name="int_1">股票ID</param>
        /// <param name="nullable_2">起始年份（可为空）</param>
        /// <param name="nullable_3">结束年份（可为空）</param>
        public Class180(int int_1, int? nullable_2, int? nullable_3) : this(int_1)
        {
            this.BeginYear = nullable_2;
            this.EndYear = nullable_3;
        }

        // ========== 公共方法 ==========

        /// <summary>
        /// 启动异步任务，获取指定股票的数据。
        /// 使用 Class181 封装参数并调用 method_1 启动后台任务。
        /// </summary>
        public void method_0()
        {
            this.method_1(new Class181
            {
                nullable_0 = this.BeginYear,
                nullable_1 = this.EndYear,
                int_0 = this.StkId,
                bool_0 = false,
                bool_1 = true
            });
        }

        /// <summary>
        /// 根据年份获取数据，并可选择是否异步获取下一年数据。
        /// </summary>
        /// <param name="int_1">股票ID</param>
        /// <param name="int_2">年份</param>
        /// <param name="bool_1">是否显示UI提示</param>
        public void method_2(int int_1, int int_2, bool bool_1 = true)
        {
            // 当前年份数据
            this.method_4(int_1, null, new int?(int_2), bool_1, false);

            // 如果启用自动获取下一年数据
            if (this.AutoBgFetchFollowingYrData)
            {
                this.method_1(new Class181(int_1, new int?(int_2 + 1), null, false, false));
            }
        }

        // ========== 私有方法 ==========

        /// <summary>
        /// 启动后台任务，异步执行数据更新。
        /// </summary>
        /// <param name="class181_0">封装了任务参数的 Class181 实例</param>
        private void method_1(Class181 class181_0)
        {
            this.backgroundWorker_0 = new BackgroundWorker
            {
                WorkerReportsProgress = true,
                WorkerSupportsCancellation = true
            };
            this.backgroundWorker_0.DoWork += this.backgroundWorker_0_DoWork;
            this.backgroundWorker_0.RunWorkerCompleted += this.backgroundWorker_0_RunWorkerCompleted;
            this.backgroundWorker_0.RunWorkerAsync(class181_0);
        }

        /// <summary>
        /// 后台线程执行的任务入口。
        /// </summary>
        private void backgroundWorker_0_DoWork(object sender, DoWorkEventArgs e)
        {
            Class181 class181_ = e.Argument as Class181;
            this.method_3(class181_);
        }

        /// <summary>
        /// 根据 Class181 参数调用 method_4 执行任务。
        /// </summary>
        private void method_3(Class181 class181_0)
        {
            this.method_4(class181_0.int_0, class181_0.nullable_0, class181_0.nullable_1, class181_0.bool_0, class181_0.bool_1);
        }

        /// <summary>
        /// 根据参数生成 XML 文档并执行更新任务。
        /// </summary>
        /// <param name="int_1">股票ID</param>
        /// <param name="nullable_2">起始年份（可为空）</param>
        /// <param name="nullable_3">结束年份（可为空）</param>
        /// <param name="bool_1">是否显示UI提示</param>
        /// <param name="bool_2">是否强制更新</param>
        public void method_4(int int_1, int? nullable_2, int? nullable_3, bool bool_1, bool bool_2)
        {
            XmlDocument xmlDocument_ = this.method_8(int_1, nullable_2, nullable_3);
            this.nullable_0 = nullable_2;
            this.nullable_1 = nullable_3;
            this.method_5(xmlDocument_, bool_1, bool_2);
        }

        /// <summary>
        /// 使用 NAppUpdate 框架执行更新任务。
        /// </summary>
        /// <param name="xmlDocument_0">包含更新任务定义的 XML 文档</param>
        /// <param name="bool_1">是否显示UI提示</param>
        /// <param name="bool_2">是否强制更新</param>
        private void method_5(XmlDocument xmlDocument_0, bool bool_1, bool bool_2)
        {
            UpdateManager instance = UpdateManager.Instance;
            const int timeout = 20000; // 最大等待时间 20 秒
            int waited = 0;

            // 等待更新管理器空闲
            while (instance.IsWorking)
            {
                waited += 200;
                if (waited > timeout)
                {
                    if (bool_1) Base.UI.smethod_178(); // 显示错误提示
                    return;
                }
                Thread.Sleep(200);
            }

            // 如果主界面未加载，则不显示UI提示
            if (!TApp.EnteredMainForm)
            {
                bool_1 = false;
            }

            // 将 XML 转换为字符串并替换路径
            string xmlText = Utility.ConvertXMLDocToString(xmlDocument_0);
            Utility.CreateDir(TApp.string_7); // 确保目录存在

            // 使用 MemorySource 加载更新任务
            if (Class183.smethod_0(new MemorySource(xmlText.Replace(".\\Data\\", TApp.string_7))) > 0)
            {
                if (bool_1) Base.UI.smethod_176(Base.Data.string_3); // 显示提示

                try
                {
                    instance.PrepareUpdates(); // 准备更新
                    List<string> updatedFiles = new List<string>();

                    // 收集所有更新的文件名（去掉扩展名）
                    foreach (IUpdateTask updateTask in instance.Tasks)
                    {
                        string fileName = Path.GetFileName(((FileUpdateTask)updateTask).LocalPath);
                        string baseName = fileName.Substring(0, fileName.Length - 6); // 去掉最后6个字符（如 .dat01）
                        if (!updatedFiles.Contains(baseName))
                        {
                            updatedFiles.Add(baseName);
                        }
                    }

                    instance.ApplyUpdates(false); // 应用更新
                    instance.CleanUp(); // 清理
                }
                catch (Exception)
                {
                    instance.CleanUp(); // 清理
                    if (bool_1) Base.UI.smethod_178(); // 显示错误提示
                    throw;
                }
            }

            instance.CleanUp();
            if (bool_1) Base.UI.smethod_178(); // 完成提示
        }

        // ========== XML 构建方法 ==========

        /// <summary>
        /// 获取某股票全部年份的 XML 文档。
        /// </summary>
        private XmlDocument method_6(int int_1)
        {
            return this.method_8(int_1, null, null);
        }

        /// <summary>
        /// 获取某股票指定年份的 XML 文档。
        /// </summary>
        private XmlDocument method_7(int int_1, int int_2)
        {
            return this.method_8(int_1, null, new int?(int_2));
        }

        /// <summary>
        /// 根据参数生成 XML 文档。
        /// </summary>
        private XmlDocument method_8(int int_1, int? nullable_2, int? nullable_3)
        {
            if (TApp.SrvParams.GetHDFileInfoFromSrvApi)
            {
                // 从服务器 API 获取文件列表
                this.ienumerable_0 = HDFileMgr.smethod_6(int_1, nullable_2, nullable_3, true);
            }
            else
            {
                // 从本地缓存或数据库获取
                this.ienumerable_0 = Base.Data.smethod_118(int_1, nullable_2, nullable_3);
            }
            return this.method_9(this.ienumerable_0);
        }

        /// <summary>
        /// 将 HDFileInfo 列表转换为 XML 文档，用于 NAppUpdate。
        /// </summary>
        private XmlDocument method_9(IEnumerable<HDFileInfo> ienumerable_1)
        {
            XmlDocument xmlDocument = new XmlDocument();
            xmlDocument.AppendChild(xmlDocument.CreateXmlDeclaration("1.0", "utf-8", null));

            XmlElement feed = xmlDocument.CreateElement("Feed");
            feed.SetAttribute("BaseUrl", TApp.SrvParams.HdDatFileBaseUrl);
            xmlDocument.AppendChild(feed);

            XmlElement tasks = xmlDocument.CreateElement("Tasks");
            feed.AppendChild(tasks);

            // 将文件列表写入 XML
            HDFileMgr.smethod_12(xmlDocument, tasks, ienumerable_1);

            return xmlDocument;
        }

        // ========== 后台任务完成回调 ==========

        /// <summary>
        /// 后台任务完成后触发（当前为空实现）。
        /// </summary>
        private void backgroundWorker_0_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            // 可在此处理完成后的逻辑
        }

        // ========== 属性定义 ==========

        /// <summary>股票ID</summary>
        public int StkId
        {
            get => this.int_0;
            set => this.int_0 = value;
        }

        /// <summary>起始年份（可为空）</summary>
        public int? BeginYear
        {
            get => this.nullable_0;
            set => this.nullable_0 = value;
        }

        /// <summary>结束年份（可为空）</summary>
        public int? EndYear
        {
            get => this.nullable_1;
            set => this.nullable_1 = value;
        }

        /// <summary>后台工作线程对象</summary>
        public BackgroundWorker BgWorker => this.backgroundWorker_0;

        /// <summary>是否自动获取下一年数据</summary>
        public bool AutoBgFetchFollowingYrData
        {
            get => this.bool_0;
            set => this.bool_0 = value;
        }

        // ========== 私有字段 ==========

        private int int_0;                 // 股票ID
        private int? nullable_0;           // 起始年份
        private int? nullable_1;           // 结束年份
        private bool bool_0;               // 是否自动获取下一年数据
        private IEnumerable<HDFileInfo> ienumerable_0; // 文件信息列表
        private BackgroundWorker backgroundWorker_0;   // 后台任务线程
    }
}