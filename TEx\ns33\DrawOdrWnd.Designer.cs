﻿namespace ns33
{
	// Token: 0x02000163 RID: 355
	internal sealed partial class DrawOdrWnd : global::System.Windows.Forms.Form
	{
		// Token: 0x06000D8F RID: 3471 RVA: 0x00054D2C File Offset: 0x00052F2C
		private void InitializeComponent()
		{
			this.btnX_Long = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_Shrt = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_condClose = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_clsRevOpen = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_delOdrLine = new global::DevComponents.DotNetBar.ButtonX();
			this.btnX_ROpen = new global::DevComponents.DotNetBar.ButtonX();
			base.SuspendLayout();
			this.btnX_Long.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_Long.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_Long.Image = global::ns28.Class372.DrawOpenLongOdr;
			this.btnX_Long.Location = new global::System.Drawing.Point(15, 8);
			this.btnX_Long.Name = "btnX_Long";
			this.btnX_Long.Size = new global::System.Drawing.Size(32, 30);
			this.btnX_Long.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_Long.TabIndex = 0;
			this.btnX_Long.Tooltip = "买入开仓";
			this.btnX_Shrt.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_Shrt.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_Shrt.Image = global::ns28.Class372.DrawOpenShrtOdr;
			this.btnX_Shrt.Location = new global::System.Drawing.Point(63, 8);
			this.btnX_Shrt.Name = "btnX_Shrt";
			this.btnX_Shrt.Size = new global::System.Drawing.Size(32, 30);
			this.btnX_Shrt.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_Shrt.TabIndex = 1;
			this.btnX_Shrt.Tooltip = "卖出开仓";
			this.btnX_condClose.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_condClose.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_condClose.Image = global::ns28.Class372.DrawCondClose;
			this.btnX_condClose.Location = new global::System.Drawing.Point(111, 8);
			this.btnX_condClose.Name = "btnX_condClose";
			this.btnX_condClose.Size = new global::System.Drawing.Size(32, 30);
			this.btnX_condClose.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_condClose.TabIndex = 2;
			this.btnX_condClose.Tooltip = "止损止盈";
			this.btnX_clsRevOpen.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_clsRevOpen.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_clsRevOpen.Image = global::ns28.Class372.DrawClsRevOpen;
			this.btnX_clsRevOpen.Location = new global::System.Drawing.Point(159, 8);
			this.btnX_clsRevOpen.Name = "btnX_clsRevOpen";
			this.btnX_clsRevOpen.Size = new global::System.Drawing.Size(32, 30);
			this.btnX_clsRevOpen.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_clsRevOpen.TabIndex = 3;
			this.btnX_clsRevOpen.Tooltip = "平仓反手";
			this.btnX_delOdrLine.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_delOdrLine.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_delOdrLine.Image = global::ns28.Class372.DelDraw;
			this.btnX_delOdrLine.Location = new global::System.Drawing.Point(264, 8);
			this.btnX_delOdrLine.Name = "btnX_delOdrLine";
			this.btnX_delOdrLine.Size = new global::System.Drawing.Size(32, 30);
			this.btnX_delOdrLine.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_delOdrLine.TabIndex = 5;
			this.btnX_delOdrLine.Tooltip = "删除";
			this.btnX_ROpen.AccessibleRole = global::System.Windows.Forms.AccessibleRole.PushButton;
			this.btnX_ROpen.ColorTable = global::DevComponents.DotNetBar.eButtonColor.Flat;
			this.btnX_ROpen.Image = global::ns28.Class372.DrawROpen;
			this.btnX_ROpen.Location = new global::System.Drawing.Point(207, 8);
			this.btnX_ROpen.Name = "btnX_ROpen";
			this.btnX_ROpen.Size = new global::System.Drawing.Size(32, 30);
			this.btnX_ROpen.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
			this.btnX_ROpen.TabIndex = 4;
			this.btnX_ROpen.Tooltip = "以损定量开仓";
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(8f, 15f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new global::System.Drawing.Size(310, 46);
			base.Controls.Add(this.btnX_ROpen);
			base.Controls.Add(this.btnX_delOdrLine);
			base.Controls.Add(this.btnX_clsRevOpen);
			base.Controls.Add(this.btnX_condClose);
			base.Controls.Add(this.btnX_Shrt);
			base.Controls.Add(this.btnX_Long);
			this.DoubleBuffered = true;
			base.FormBorderStyle = global::System.Windows.Forms.FormBorderStyle.FixedSingle;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = "DrawOdrWnd";
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.SizeGripStyle = global::System.Windows.Forms.SizeGripStyle.Hide;
			this.Text = "画线下单";
			base.TopMost = true;
			base.ResumeLayout(false);
		}

		// Token: 0x040006DE RID: 1758
		private global::DevComponents.DotNetBar.ButtonX btnX_Long;

		// Token: 0x040006DF RID: 1759
		private global::DevComponents.DotNetBar.ButtonX btnX_Shrt;

		// Token: 0x040006E0 RID: 1760
		private global::DevComponents.DotNetBar.ButtonX btnX_condClose;

		// Token: 0x040006E1 RID: 1761
		private global::DevComponents.DotNetBar.ButtonX btnX_clsRevOpen;

		// Token: 0x040006E2 RID: 1762
		private global::DevComponents.DotNetBar.ButtonX btnX_delOdrLine;

		// Token: 0x040006E3 RID: 1763
		private global::DevComponents.DotNetBar.ButtonX btnX_ROpen;
	}
}
