﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using TEx;
using TEx.Trading;

namespace ns20
{
	// Token: 0x020000AE RID: 174
	internal sealed class Class67 : TOdrLine
	{
		// Token: 0x0600072E RID: 1838 RVA: 0x00004F01 File Offset: 0x00003101
		public Class67(ChartCS chartCS_0, Order order_1) : base(chartCS_0, order_1.Price)
		{
			this.order_0 = order_1;
			this.vmethod_1();
			base.method_0();
		}

		// Token: 0x0600072F RID: 1839 RVA: 0x00004F25 File Offset: 0x00003125
		public override void vmethod_1()
		{
			base.Text = this.method_8();
			if (base.TextBox != null)
			{
				base.TextBox.Text = base.Text;
			}
		}

		// Token: 0x06000730 RID: 1840 RVA: 0x0002DDC0 File Offset: 0x0002BFC0
		private string method_8()
		{
			return Base.Trading.smethod_35((OrderType)this.Order.OrderType) + this.method_10() + this.Order.Price;
		}

		// Token: 0x06000731 RID: 1841 RVA: 0x0002DDFC File Offset: 0x0002BFFC
		private string method_9()
		{
			return string.Concat(new string[]
			{
				Base.Trading.smethod_35((OrderType)this.Order.OrderType),
				this.method_10(),
				"(委价",
				base.method_5(base.Price),
				")"
			});
		}

		// Token: 0x06000732 RID: 1842 RVA: 0x0002DE54 File Offset: 0x0002C054
		private string method_10()
		{
			return this.Order.Units.ToString() + (base.Chart.Symbol.IsFutures ? "手" : "股");
		}

		// Token: 0x06000733 RID: 1843 RVA: 0x0002DE9C File Offset: 0x0002C09C
		protected override Color vmethod_7()
		{
			Color result;
			if (this.IsLong)
			{
				result = Color.Red;
			}
			else if (this.IsShort)
			{
				result = Color.Green;
			}
			else
			{
				result = default(Color);
			}
			return result;
		}

		// Token: 0x06000734 RID: 1844 RVA: 0x0002C160 File Offset: 0x0002A360
		protected override DashStyle vmethod_8()
		{
			return DashStyle.Dot;
		}

		// Token: 0x06000735 RID: 1845 RVA: 0x0002DED8 File Offset: 0x0002C0D8
		public override void vmethod_4(double double_0)
		{
			decimal num = base.Chart.SymbDataSet.method_69(double_0);
			Base.Trading.smethod_64(this.order_0.ID, num, this.order_0.Units, false);
			base.Price = num;
			base.vmethod_4(Convert.ToDouble(num));
		}

		// Token: 0x06000736 RID: 1846 RVA: 0x0002DF2C File Offset: 0x0002C12C
		protected override double vmethod_3()
		{
			return Convert.ToDouble(this.order_0.Price);
		}

		// Token: 0x170001AB RID: 427
		// (get) Token: 0x06000737 RID: 1847 RVA: 0x0002DF50 File Offset: 0x0002C150
		// (set) Token: 0x06000738 RID: 1848 RVA: 0x00004F4E File Offset: 0x0000314E
		public Order Order
		{
			get
			{
				return this.order_0;
			}
			set
			{
				this.order_0 = value;
			}
		}

		// Token: 0x170001AC RID: 428
		// (get) Token: 0x06000739 RID: 1849 RVA: 0x0002DF68 File Offset: 0x0002C168
		public bool IsLong
		{
			get
			{
				bool result;
				if (this.Order.OrderType != 0)
				{
					result = (this.Order.OrderType == 3);
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x170001AD RID: 429
		// (get) Token: 0x0600073A RID: 1850 RVA: 0x0002DF98 File Offset: 0x0002C198
		public bool IsShort
		{
			get
			{
				bool result;
				if (this.Order.OrderType != 2)
				{
					result = (this.Order.OrderType == 1);
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x170001AE RID: 430
		// (get) Token: 0x0600073B RID: 1851 RVA: 0x0002DFCC File Offset: 0x0002C1CC
		public override bool IsCurrent
		{
			get
			{
				bool result;
				if (this.Order.AcctID == Base.Acct.CurrAccount.ID)
				{
					result = (this.Order.SymbolID == base.Chart.Symbol.ID);
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x170001AF RID: 431
		// (get) Token: 0x0600073C RID: 1852 RVA: 0x0002E018 File Offset: 0x0002C218
		// (set) Token: 0x0600073D RID: 1853 RVA: 0x0002E068 File Offset: 0x0002C268
		public override bool HighLighted
		{
			get
			{
				bool result;
				if (this.IsLong)
				{
					result = (base.Line.Line.Color != Color.Red);
				}
				else
				{
					result = (base.Line.Line.Color != Color.Green);
				}
				return result;
			}
			set
			{
				Color color;
				if (this.IsLong)
				{
					color = (value ? Color.Coral : Color.Red);
				}
				else
				{
					color = (value ? Color.LimeGreen : Color.Green);
				}
				base.Line.Line.Color = color;
				base.TextBox.FontSpec.FontColor = color;
				if (value)
				{
					base.TextBox.Text = this.method_9();
				}
				else
				{
					base.TextBox.Text = this.method_8();
				}
			}
		}

		// Token: 0x0400032A RID: 810
		private Order order_0;
	}
}
