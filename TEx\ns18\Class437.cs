﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using ns13;
using ns25;
using ns9;
using TEx.Inds;
using TEx.SIndicator;

namespace ns18
{
	// Token: 0x02000324 RID: 804
	internal sealed class Class437
	{
		// Token: 0x170005DE RID: 1502
		// (get) Token: 0x0600223B RID: 8763 RVA: 0x000E9DB4 File Offset: 0x000E7FB4
		// (set) Token: 0x0600223C RID: 8764 RVA: 0x0000D946 File Offset: 0x0000BB46
		public ParserEnvironment Envi { get; private set; }

		// Token: 0x170005DF RID: 1503
		// (get) Token: 0x0600223D RID: 8765 RVA: 0x000E9DCC File Offset: 0x000E7FCC
		// (set) Token: 0x0600223E RID: 8766 RVA: 0x0000D951 File Offset: 0x0000BB51
		public string WrongMsg { get; private set; }

		// Token: 0x0600223F RID: 8767 RVA: 0x0000D95C File Offset: 0x0000BB5C
		public Class437(ParserEnvironment parserEnvironment_1)
		{
			this.Envi = parserEnvironment_1;
		}

		// Token: 0x06002240 RID: 8768 RVA: 0x000E9DE4 File Offset: 0x000E7FE4
		private Class440 method_0(int int_0, int int_1, string string_1)
		{
			return new Class440(int_0, int_1, string_1);
		}

		// Token: 0x06002241 RID: 8769 RVA: 0x000E9E00 File Offset: 0x000E8000
		private static string smethod_0(ref int int_0, ref int int_1, string string_1)
		{
			string text = "";
			bool flag = false;
			int i = int_0;
			while (i < string_1.Count<char>())
			{
				if (char.IsDigit(string_1[i]))
				{
					text += string_1[i].ToString();
					int_1++;
					if (i == string_1.Count<char>() - 1)
					{
						int_0 = i;
						break;
					}
				}
				else
				{
					if (string_1[i] != '.')
					{
						int_0 = i - 1;
						int_1--;
						break;
					}
					if (flag)
					{
						throw new Exception(string.Format("{0}行{1}列,不应该为{3}", int_0, int_1, "."));
					}
					flag = true;
					text += string_1[i].ToString();
					int_1++;
				}
				i++;
				continue;
				IL_B2:
				return text;
			}
			goto IL_B2;
		}

		// Token: 0x06002242 RID: 8770 RVA: 0x000E9EC8 File Offset: 0x000E80C8
		private static string smethod_1(ref int int_0, ref int int_1, string string_1)
		{
			string text = "";
			if (int_0 >= string_1.Length - 1)
			{
				throw new Exception("单引号后面没有内容。");
			}
			for (int i = int_0 + 1; i < string_1.Count<char>(); i++)
			{
				if (string_1[i] == '\'')
				{
					int_0 = i;
					int_1++;
					return text;
				}
				text += string_1[i].ToString();
				int_1++;
			}
			throw new Exception("单引号后面没有内容。");
		}

		// Token: 0x06002243 RID: 8771 RVA: 0x000E9F48 File Offset: 0x000E8148
		private static string smethod_2(ref int int_0, ref int int_1, string string_1, Class437.Delegate36 delegate36_0)
		{
			string text = "";
			for (int i = int_0; i < string_1.Count<char>(); i++)
			{
				if (!delegate36_0(string_1[i]))
				{
					int_0 = i - 1;
					int_1--;
					return text;
				}
				text += string_1[i].ToString();
				int_1++;
			}
			int_0 = string_1.Count<char>() - 1;
			return text;
		}

		// Token: 0x06002244 RID: 8772 RVA: 0x000E9FB8 File Offset: 0x000E81B8
		public Tokenes method_1(string string_1)
		{
			string text = string_1.ToUpper();
			List<HToken> list = new List<HToken>();
			int num = 1;
			int num2 = 0;
			for (int i = 0; i < text.Count<char>(); i++)
			{
				num2++;
				char c = text[i];
				if (c != ' ' && c != '\t')
				{
					if (c == '/' && Class437.smethod_3(i, '/', text))
					{
						for (int j = i + 2; j < text.Count<char>(); j++)
						{
							char c2 = text[j];
							if (c2 == '\r')
							{
								if (Class437.smethod_3(j, '\n', text))
								{
									j++;
								}
								num++;
								num2 = 0;
								i = j;
								break;
							}
							if (c2 == '\n')
							{
								num++;
								num2 = 0;
								i = j;
								break;
							}
							if (j == text.Count<char>() - 1)
							{
								return new Tokenes(list);
							}
						}
					}
					else if (c == '\r')
					{
						if (Class437.smethod_3(i, '\n', text))
						{
							i++;
						}
						num++;
						num2 = 0;
					}
					else if (c == '\n')
					{
						num++;
						num2 = 0;
					}
					else if (char.IsLetter(c))
					{
						Class437.Class438 @class = new Class437.Class438();
						@class.string_0 = "";
						Enum26 enum26_ = Enum26.const_4;
						int col = num2;
						@class.string_0 += Class437.smethod_2(ref i, ref num2, text, new Class437.Delegate36(this.method_2));
						if (this.Envi.Functions.Any(new Func<MethodInfo, bool>(@class.method_0)))
						{
							enum26_ = Enum26.const_1;
						}
						else if (@class.string_0 == "AND")
						{
							enum26_ = Enum26.const_17;
						}
						else if (@class.string_0 == "OR")
						{
							enum26_ = Enum26.const_18;
						}
						else if (this.Envi.UserDefineParams.Any(new Func<UserDefineParam, bool>(@class.method_1)))
						{
							enum26_ = Enum26.const_2;
						}
						else if (this.Envi.Properties.Any(new Func<PropertyInfo, bool>(@class.method_2)))
						{
							enum26_ = Enum26.const_0;
						}
						else if (ParserEnvironment.smethod_4(@class.string_0.ToUpper()))
						{
							enum26_ = Enum26.const_34;
						}
						else if (ParserEnvironment.smethod_8(@class.string_0.ToUpper()))
						{
							enum26_ = Enum26.const_35;
						}
						else if (ParserEnvironment.smethod_9(@class.string_0.ToUpper()))
						{
							enum26_ = Enum26.const_36;
						}
						else if (ParserEnvironment.smethod_10(@class.string_0.ToUpper()))
						{
							enum26_ = Enum26.const_37;
						}
						HToken item = new HToken(col, num, new Class439(enum26_, @class.string_0));
						list.Add(item);
					}
					else if (char.IsDigit(c))
					{
						string text2 = "";
						int col2 = num2;
						text2 += Class437.smethod_0(ref i, ref num2, text);
						HToken item2 = new HToken(col2, num, new Class439(Enum26.const_5, text2));
						list.Add(item2);
					}
					else
					{
						HToken item3;
						if (c == '+')
						{
							item3 = new HToken(num2, num, new Class439(Enum26.const_6, "+"));
						}
						else if (c == '\'')
						{
							string text3 = Class437.smethod_1(ref i, ref num2, text);
							if (!(text3 != "") || i >= text.Count<char>() - 1)
							{
								throw new Exception(new HToken(num2, num, new Class439(Enum26.const_40, text3)).method_0("没有发现单引号后半部分"));
							}
							item3 = new HToken(num2, num, new Class439(Enum26.const_38, text3));
						}
						else if (c == '-')
						{
							item3 = new HToken(num2, num, new Class439(Enum26.const_7, "-"));
						}
						else if (c == '*')
						{
							item3 = new HToken(num2, num, new Class439(Enum26.const_8, "*"));
						}
						else if (c == '/')
						{
							item3 = new HToken(num2, num, new Class439(Enum26.const_9, "/"));
						}
						else if (c == ':')
						{
							if (Class437.smethod_3(i, '=', text))
							{
								i++;
								item3 = new HToken(num2, num, new Class439(Enum26.const_11, ":="));
							}
							else
							{
								item3 = new HToken(num2, num, new Class439(Enum26.const_10, ":"));
							}
						}
						else if (c == '>')
						{
							if (Class437.smethod_3(i, '=', text))
							{
								i++;
								item3 = new HToken(num2, num, new Class439(Enum26.const_14, ">="));
							}
							else
							{
								item3 = new HToken(num2, num, new Class439(Enum26.const_12, ">"));
							}
						}
						else if (c == '&')
						{
							if (Class437.smethod_3(i, '&', text))
							{
								i++;
								item3 = new HToken(num2, num, new Class439(Enum26.const_17, "&&"));
							}
							else
							{
								item3 = new HToken(num2, num, new Class439(Enum26.const_17, "&"));
							}
						}
						else if (c == '|')
						{
							if (Class437.smethod_3(i, '|', text))
							{
								i++;
								item3 = new HToken(num2, num, new Class439(Enum26.const_18, "||"));
							}
							else
							{
								item3 = new HToken(num2, num, new Class439(Enum26.const_18, "|"));
							}
						}
						else if (c == '<')
						{
							if (Class437.smethod_3(i, '=', text))
							{
								i++;
								item3 = new HToken(num2, num, new Class439(Enum26.const_16, "<="));
							}
							else if (Class437.smethod_3(i, '>', text))
							{
								i++;
								item3 = new HToken(num2, num, new Class439(Enum26.const_13, "<>"));
							}
							else
							{
								item3 = new HToken(num2, num, new Class439(Enum26.const_15, "<"));
							}
						}
						else if (c == '!')
						{
							if (!Class437.smethod_3(i, '=', text))
							{
								throw new Exception(new HToken(num2, num, new Class439(Enum26.const_40, c.ToString())).method_0("后面必须跟等号组成不等于号"));
							}
							i++;
							item3 = new HToken(num2, num, new Class439(Enum26.const_13, "!="));
						}
						else if (c == '=')
						{
							item3 = new HToken(num2, num, new Class439(Enum26.const_20, "="));
						}
						else if (c == ',')
						{
							item3 = new HToken(num2, num, new Class439(Enum26.const_21, ","));
						}
						else if (c == '.')
						{
							item3 = new HToken(num2, num, new Class439(Enum26.const_19, "."));
						}
						else if (c == ';')
						{
							item3 = new HToken(num2, num, new Class439(Enum26.const_22, ";"));
						}
						else if (c == '(')
						{
							item3 = new HToken(num2, num, new Class439(Enum26.const_23, "("));
						}
						else
						{
							if (c != ')')
							{
								throw new Exception(new HToken(num2, num, new Class439(Enum26.const_40, c.ToString())).method_0("不是可识别的符号"));
							}
							item3 = new HToken(num2, num, new Class439(Enum26.const_24, ")"));
						}
						list.Add(item3);
					}
				}
			}
			if (list.Count > 0 && list.Last<HToken>().Symbol.HSymbolType != Enum26.const_22)
			{
				HToken htoken = list.Last<HToken>();
				HToken item4 = new HToken(htoken.Col + 1, htoken.Line, new Class439(Enum26.const_22, ";"));
				list.Add(item4);
			}
			return new Tokenes(list);
		}

		// Token: 0x06002245 RID: 8773 RVA: 0x000EA6C0 File Offset: 0x000E88C0
		private bool method_2(char char_0)
		{
			if (!char.IsLetterOrDigit(char_0))
			{
				if (char_0 != '_')
				{
					return false;
				}
			}
			return true;
		}

		// Token: 0x06002246 RID: 8774 RVA: 0x000EA6E8 File Offset: 0x000E88E8
		private static bool smethod_3(int int_0, char char_0, string string_1)
		{
			bool result;
			if (int_0 + 1 < string_1.Count<char>() && string_1[int_0 + 1] == char_0)
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0400108A RID: 4234
		private List<HToken> list_0 = new List<HToken>();

		// Token: 0x0400108B RID: 4235
		[CompilerGenerated]
		private ParserEnvironment parserEnvironment_0;

		// Token: 0x0400108C RID: 4236
		[CompilerGenerated]
		private string string_0;

		// Token: 0x02000325 RID: 805
		// (Invoke) Token: 0x06002248 RID: 8776
		private delegate bool Delegate36(char achar);

		// Token: 0x02000326 RID: 806
		[CompilerGenerated]
		private sealed class Class438
		{
			// Token: 0x0600224C RID: 8780 RVA: 0x000EA718 File Offset: 0x000E8918
			internal bool method_0(MethodInfo methodInfo_0)
			{
				return methodInfo_0.Name == this.string_0;
			}

			// Token: 0x0600224D RID: 8781 RVA: 0x000EA73C File Offset: 0x000E893C
			internal bool method_1(UserDefineParam userDefineParam_0)
			{
				return userDefineParam_0.Name == this.string_0;
			}

			// Token: 0x0600224E RID: 8782 RVA: 0x000EA718 File Offset: 0x000E8918
			internal bool method_2(PropertyInfo propertyInfo_0)
			{
				return propertyInfo_0.Name == this.string_0;
			}

			// Token: 0x0400108D RID: 4237
			public string string_0;
		}
	}
}
