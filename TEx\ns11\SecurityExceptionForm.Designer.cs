﻿namespace ns11
{
	// Token: 0x0200041A RID: 1050
	internal sealed partial class SecurityExceptionForm : global::System.Windows.Forms.Form
	{
		// Token: 0x0600286D RID: 10349 RVA: 0x00105A10 File Offset: 0x00103C10
		private void InitializeComponent()
		{
			this.quitButton = new global::System.Windows.Forms.Button();
			this.continueButton = new global::System.Windows.Forms.Button();
			this.headerControl1 = new global::ns23.Control4();
			this.errorMessage = new global::ns30.Class545();
			this.poweredBy = new global::ns30.Control5();
			base.SuspendLayout();
			this.quitButton.Anchor = (global::System.Windows.Forms.AnchorStyles.Bottom | global::System.Windows.Forms.AnchorStyles.Right);
			this.quitButton.FlatStyle = global::System.Windows.Forms.FlatStyle.System;
			this.quitButton.Location = new global::System.Drawing.Point(406, 183);
			this.quitButton.Name = "quitButton";
			this.quitButton.Size = new global::System.Drawing.Size(140, 28);
			this.quitButton.TabIndex = 0;
			this.quitButton.Text = "&Quit";
			this.quitButton.Click += new global::System.EventHandler(this.quitButton_Click);
			this.continueButton.Anchor = (global::System.Windows.Forms.AnchorStyles.Bottom | global::System.Windows.Forms.AnchorStyles.Right);
			this.continueButton.FlatStyle = global::System.Windows.Forms.FlatStyle.System;
			this.continueButton.Location = new global::System.Drawing.Point(258, 183);
			this.continueButton.Name = "continueButton";
			this.continueButton.Size = new global::System.Drawing.Size(140, 28);
			this.continueButton.TabIndex = 1;
			this.continueButton.Text = "&Continue";
			this.continueButton.Click += new global::System.EventHandler(this.continueButton_Click);
			this.headerControl1.BackColor = global::System.Drawing.Color.FromArgb(36, 96, 179);
			this.headerControl1.Dock = global::System.Windows.Forms.DockStyle.Top;
			this.headerControl1.ForeColor = global::System.Drawing.Color.White;
			this.headerControl1.IconState = global::ns24.Enum36.const_2;
			this.headerControl1.Image = null;
			this.headerControl1.Location = new global::System.Drawing.Point(0, 0);
			this.headerControl1.Name = "headerControl1";
			this.headerControl1.Size = new global::System.Drawing.Size(560, 67);
			this.headerControl1.TabIndex = 7;
			this.headerControl1.TabStop = false;
			this.headerControl1.Text = "%AppName% attempted to perform an operation not allowed by the security policy.";
			this.errorMessage.Anchor = (global::System.Windows.Forms.AnchorStyles.Top | global::System.Windows.Forms.AnchorStyles.Left | global::System.Windows.Forms.AnchorStyles.Right);
			this.errorMessage.FlatStyle = global::System.Windows.Forms.FlatStyle.System;
			this.errorMessage.Location = new global::System.Drawing.Point(28, 83);
			this.errorMessage.Name = "errorMessage";
			this.errorMessage.Size = new global::System.Drawing.Size(510, 13);
			this.errorMessage.TabIndex = 14;
			this.errorMessage.Text = "errorMessage";
			this.errorMessage.UseMnemonic = false;
			this.poweredBy.Anchor = (global::System.Windows.Forms.AnchorStyles.Bottom | global::System.Windows.Forms.AnchorStyles.Left);
			this.poweredBy.Cursor = global::System.Windows.Forms.Cursors.Hand;
			this.poweredBy.Location = new global::System.Drawing.Point(8, 181);
			this.poweredBy.Name = "poweredBy";
			this.poweredBy.Size = new global::System.Drawing.Size(157, 37);
			this.poweredBy.TabIndex = 15;
			this.poweredBy.TabStop = false;
			this.poweredBy.Text = "poweredBy1";
			this.AutoScaleBaseSize = new global::System.Drawing.Size(7, 15);
			this.BackColor = global::System.Drawing.SystemColors.Window;
			base.ClientSize = new global::System.Drawing.Size(560, 224);
			base.ControlBox = false;
			base.Controls.Add(this.continueButton);
			base.Controls.Add(this.quitButton);
			base.Controls.Add(this.headerControl1);
			base.Controls.Add(this.errorMessage);
			base.Controls.Add(this.poweredBy);
			base.FormBorderStyle = global::System.Windows.Forms.FormBorderStyle.FixedSingle;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = "SecurityExceptionForm";
			base.ShowInTaskbar = false;
			base.StartPosition = global::System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "%AppName%";
			base.ResumeLayout(false);
		}

		// Token: 0x0400143E RID: 5182
		private global::System.Windows.Forms.Button continueButton;

		// Token: 0x0400143F RID: 5183
		private global::System.Windows.Forms.Button quitButton;

		// Token: 0x04001440 RID: 5184
		private global::ns23.Control4 headerControl1;

		// Token: 0x04001441 RID: 5185
		private global::ns30.Class545 errorMessage;

		// Token: 0x04001442 RID: 5186
		private global::ns30.Control5 poweredBy;
	}
}
