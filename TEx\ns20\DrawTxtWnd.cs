﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns11;
using ns8;
using TEx;

namespace ns20
{
	// Token: 0x02000169 RID: 361
	internal sealed partial class DrawTxtWnd : Form
	{
		// Token: 0x14000072 RID: 114
		// (add) Token: 0x06000DAC RID: 3500 RVA: 0x000564A4 File Offset: 0x000546A4
		// (remove) Token: 0x06000DAD RID: 3501 RVA: 0x000564DC File Offset: 0x000546DC
		public event Delegate9 DrawTxtSet
		{
			[CompilerGenerated]
			add
			{
				Delegate9 @delegate = this.delegate9_0;
				Delegate9 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate9 value2 = (Delegate9)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate9>(ref this.delegate9_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate9 @delegate = this.delegate9_0;
				Delegate9 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate9 value2 = (Delegate9)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate9>(ref this.delegate9_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06000DAE RID: 3502 RVA: 0x00056514 File Offset: 0x00054714
		protected void method_0(EventArgs8 eventArgs8_0)
		{
			Delegate9 @delegate = this.delegate9_0;
			if (@delegate != null)
			{
				@delegate(this, eventArgs8_0);
			}
		}

		// Token: 0x14000073 RID: 115
		// (add) Token: 0x06000DAF RID: 3503 RVA: 0x00056538 File Offset: 0x00054738
		// (remove) Token: 0x06000DB0 RID: 3504 RVA: 0x00056570 File Offset: 0x00054770
		public event EventHandler FormClosedWithoutTxtSet
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000DB1 RID: 3505 RVA: 0x000565A8 File Offset: 0x000547A8
		protected void method_1(object sender, EventArgs e)
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x06000DB2 RID: 3506 RVA: 0x000565CC File Offset: 0x000547CC
		public DrawTxtWnd()
		{
			this.InitializeComponent();
			if (!TApp.IsHighDpiScreen)
			{
				base.AutoScaleMode = AutoScaleMode.Dpi;
			}
			if (!TApp.IsHighDpiScreen)
			{
				Font font = new Font("SimSun", (float)(11.25 / TApp.DpiScale));
				this.textBox.Font = font;
				this.btn_Cancel.Font = font;
				this.btn_OK.Font = font;
				this.btn_Font.Font = font;
				this.btn_color.Font = font;
			}
			base.FormClosing += this.DrawTxtWnd_FormClosing;
			if (this.object_0 == null)
			{
				this.object_0 = Base.UI.Form.LastSelectedFont;
			}
			if (this.nullable_0 == null)
			{
				this.nullable_0 = Base.UI.Form.LastSelectedFontColor;
			}
		}

		// Token: 0x06000DB3 RID: 3507 RVA: 0x0000612E File Offset: 0x0000432E
		public DrawTxtWnd(TransArrow transArrow_0) : this()
		{
			this.TextInput = transArrow_0.Notes;
			this.object_0 = transArrow_0.NoteBoxFont;
			this.nullable_0 = transArrow_0.NoteBoxFontColor;
		}

		// Token: 0x06000DB4 RID: 3508 RVA: 0x0000615C File Offset: 0x0000435C
		public DrawTxtWnd(DrawText drawText_1) : this()
		{
			this.drawText_0 = drawText_1;
			this.TextInput = drawText_1.Text;
			this.object_0 = drawText_1.Font;
			this.nullable_0 = drawText_1.LineColor;
		}

		// Token: 0x06000DB5 RID: 3509 RVA: 0x00006191 File Offset: 0x00004391
		private void DrawTxtWnd_FormClosing(object sender, FormClosingEventArgs e)
		{
			if (!this.bool_0)
			{
				this.method_1(this, new EventArgs());
			}
		}

		// Token: 0x06000DB6 RID: 3510 RVA: 0x0005669C File Offset: 0x0005489C
		protected bool ProcessCmdKey(ref Message msg, Keys keyData)
		{
			if (keyData == Keys.Escape)
			{
				base.Close();
			}
			return base.ProcessCmdKey(ref msg, keyData);
		}

		// Token: 0x17000223 RID: 547
		// (get) Token: 0x06000DB7 RID: 3511 RVA: 0x000566C0 File Offset: 0x000548C0
		protected CreateParams CreateParams
		{
			get
			{
				CreateParams createParams = base.CreateParams;
				createParams.ExStyle |= 33554432;
				return createParams;
			}
		}

		// Token: 0x06000DB8 RID: 3512 RVA: 0x000566EC File Offset: 0x000548EC
		private void btn_OK_Click(object sender, EventArgs e)
		{
			string textInput = this.TextInput;
			bool bool_ = this.drawText_0 == null;
			this.method_0(new EventArgs8(textInput, this.object_0, this.nullable_0, bool_));
			this.bool_0 = true;
			base.Close();
		}

		// Token: 0x06000DB9 RID: 3513 RVA: 0x00056738 File Offset: 0x00054938
		private void btn_color_Click(object sender, EventArgs e)
		{
			if (this.nullable_0 != null)
			{
				this.colorDialog_0.Color = this.nullable_0.Value;
			}
			if (this.colorDialog_0.ShowDialog() == DialogResult.OK)
			{
				this.nullable_0 = new Color?(this.colorDialog_0.Color);
				Base.UI.Form.LastSelectedFontColor = this.nullable_0;
			}
		}

		// Token: 0x06000DBA RID: 3514 RVA: 0x000567A0 File Offset: 0x000549A0
		private void btn_Font_Click(object sender, EventArgs e)
		{
			if (this.object_0 != null)
			{
				this.fontDialog_0.Font = (this.object_0 as Font);
			}
			if (this.fontDialog_0.ShowDialog() == DialogResult.OK)
			{
				this.object_0 = this.fontDialog_0.Font;
				Base.UI.Form.LastSelectedFont = (this.object_0 as Font);
			}
		}

		// Token: 0x06000DBB RID: 3515 RVA: 0x00004268 File Offset: 0x00002468
		private void btn_Cancel_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x17000224 RID: 548
		// (get) Token: 0x06000DBC RID: 3516 RVA: 0x00056804 File Offset: 0x00054A04
		// (set) Token: 0x06000DBD RID: 3517 RVA: 0x000061A9 File Offset: 0x000043A9
		public string TextInput
		{
			get
			{
				return this.textBox.Text;
			}
			set
			{
				this.textBox.Text = value;
			}
		}

		// Token: 0x06000DBE RID: 3518 RVA: 0x000061B9 File Offset: 0x000043B9
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x040006FE RID: 1790
		[CompilerGenerated]
		private Delegate9 delegate9_0;

		// Token: 0x040006FF RID: 1791
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000700 RID: 1792
		private Color? nullable_0;

		// Token: 0x04000701 RID: 1793
		private object object_0;

		// Token: 0x04000702 RID: 1794
		private bool bool_0;

		// Token: 0x04000703 RID: 1795
		private DrawText drawText_0;

		// Token: 0x04000704 RID: 1796
		private IContainer icontainer_0;
	}
}
