﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using ns27;
using ns4;

namespace TEx.ImportTrans.Captcha
{
	// Token: 0x02000391 RID: 913
	public sealed class LevenClassifer : Class482
	{
		// Token: 0x06002546 RID: 9542 RVA: 0x0000E5F5 File Offset: 0x0000C7F5
		public LevenClassifer()
		{
			this.list_0 = new List<LevenClassifer.LevenFeatrue>();
		}

		// Token: 0x06002547 RID: 9543 RVA: 0x000F7B38 File Offset: 0x000F5D38
		public override void vmethod_0(string string_0)
		{
			string[] files = Directory.GetFiles(string_0, "*.bmp");
			this.list_0.Clear();
			foreach (string text in files)
			{
				LevenClassifer.LevenFeatrue levenFeatrue = new LevenClassifer.LevenFeatrue();
				levenFeatrue.string_2 = Path.GetFileName(text);
				levenFeatrue.Name = Path.GetFileNameWithoutExtension(text).Split(new char[]
				{
					'-'
				})[0];
				string string_ = ImageProcessor.smethod_22(text);
				levenFeatrue.string_1 = string_;
				this.list_0.Add(levenFeatrue);
			}
		}

		// Token: 0x06002548 RID: 9544 RVA: 0x000F7BBC File Offset: 0x000F5DBC
		public override void vmethod_1(string string_0)
		{
			this.list_0.Clear();
			foreach (string text in File.ReadAllLines(string_0))
			{
				if (!(text.Trim() == ""))
				{
					LevenClassifer.LevenFeatrue levenFeatrue = new LevenClassifer.LevenFeatrue();
					string[] array2 = text.Split(new char[]
					{
						'='
					});
					if (array2.Length == 2)
					{
						levenFeatrue.string_2 = "";
						levenFeatrue.Name = array2[0];
						levenFeatrue.string_1 = array2[1];
						this.list_0.Add(levenFeatrue);
					}
				}
			}
		}

		// Token: 0x06002549 RID: 9545 RVA: 0x000F7C4C File Offset: 0x000F5E4C
		public override void vmethod_2(string string_0)
		{
			string[] contents = this.list_0.Select(new Func<LevenClassifer.LevenFeatrue, string>(LevenClassifer.<>c.<>9.method_0)).ToArray<string>();
			File.WriteAllLines(string_0, contents);
		}

		// Token: 0x0600254A RID: 9546 RVA: 0x000F7C94 File Offset: 0x000F5E94
		private string method_0(string string_0)
		{
			LevenClassifer.Class489 @class = new LevenClassifer.Class489();
			@class.string_0 = string_0;
			@class.class494_0 = new Class494();
			double[] array = this.list_0.Select(new Func<LevenClassifer.LevenFeatrue, double>(@class.method_0)).ToArray<double>();
			int index = Array.IndexOf<double>(array, array.Max());
			return this.list_0[index].Name;
		}

		// Token: 0x0600254B RID: 9547 RVA: 0x000F7CF8 File Offset: 0x000F5EF8
		public string[] method_1(string[] string_0)
		{
			return string_0.Select(new Func<string, string>(this.method_4)).ToArray<string>();
		}

		// Token: 0x0600254C RID: 9548 RVA: 0x000F7D20 File Offset: 0x000F5F20
		private Reg method_2(List<Point> list_1)
		{
			new Reg("", 0.0);
			Rectangle rectangle = ImageProcessor.smethod_16(list_1);
			string text = "";
			LevenClassifer.Class490 @class = new LevenClassifer.Class490();
			@class.int_0 = rectangle.Left;
			while (@class.int_0 < rectangle.Right)
			{
				LevenClassifer.Class491 class2 = new LevenClassifer.Class491();
				class2.class490_0 = @class;
				class2.int_0 = rectangle.Top;
				int int_;
				while (class2.int_0 < rectangle.Bottom)
				{
					if (list_1.Count(new Func<Point, bool>(class2.method_0)) != 0)
					{
						text += "1";
					}
					else
					{
						text += "0";
					}
					int_ = class2.int_0;
					class2.int_0 = int_ + 1;
				}
				int_ = @class.int_0;
				@class.int_0 = int_ + 1;
			}
			return this.method_3(text);
		}

		// Token: 0x0600254D RID: 9549 RVA: 0x000F7E00 File Offset: 0x000F6000
		private Reg method_3(string string_0)
		{
			LevenClassifer.Class492 @class = new LevenClassifer.Class492();
			@class.string_0 = string_0;
			@class.class494_0 = new Class494();
			double[] array = this.list_0.Select(new Func<LevenClassifer.LevenFeatrue, double>(@class.method_0)).ToArray<double>();
			int num = Array.IndexOf<double>(array, array.Max());
			return new Reg("", 0.0)
			{
				Name = this.list_0[num].Name,
				Persent = array[num]
			};
		}

		// Token: 0x0600254E RID: 9550 RVA: 0x000F7E88 File Offset: 0x000F6088
		public override Reg[] vmethod_3(Bitmap bitmap_0, List<Rectangle> list_1)
		{
			string[] array = ImageProcessor.smethod_13(bitmap_0, list_1);
			Reg[] array2 = new Reg[list_1.Count];
			new Class494();
			for (int i = 0; i < array.Length; i++)
			{
				string string_ = array[i];
				array2[i] = this.method_3(string_);
			}
			return array2;
		}

		// Token: 0x0600254F RID: 9551 RVA: 0x000F7ED4 File Offset: 0x000F60D4
		public override Reg[] vmethod_4(List<List<Point>> list_1)
		{
			return list_1.Select(new Func<List<Point>, Reg>(this.method_5)).ToArray<Reg>();
		}

		// Token: 0x06002550 RID: 9552 RVA: 0x000F7EFC File Offset: 0x000F60FC
		public override string vmethod_5(Bitmap bitmap_0)
		{
			LevenClassifer.Class493 @class = new LevenClassifer.Class493();
			List<Rectangle> list = new List<Rectangle>();
			List<Rectangle> list_ = new List<Rectangle>();
			List<int> list_2 = new List<int>();
			Bitmap bitmap_ = CutBigComponent.smethod_1(bitmap_0, list, list_, this, list_2);
			Reg[] array = this.vmethod_3(bitmap_, list);
			@class.string_0 = "";
			Array.ForEach<Reg>(array, new Action<Reg>(@class.method_0));
			return @class.string_0;
		}

		// Token: 0x06002551 RID: 9553 RVA: 0x000F7F60 File Offset: 0x000F6160
		[CompilerGenerated]
		private string method_4(string string_0)
		{
			return this.method_0(string_0);
		}

		// Token: 0x06002552 RID: 9554 RVA: 0x000F7F78 File Offset: 0x000F6178
		[CompilerGenerated]
		private Reg method_5(List<Point> list_1)
		{
			return this.method_2(list_1);
		}

		// Token: 0x040011F6 RID: 4598
		private List<LevenClassifer.LevenFeatrue> list_0;

		// Token: 0x02000392 RID: 914
		public sealed class LevenFeatrue
		{
			// Token: 0x17000649 RID: 1609
			// (get) Token: 0x06002553 RID: 9555 RVA: 0x000F7F90 File Offset: 0x000F6190
			// (set) Token: 0x06002554 RID: 9556 RVA: 0x0000E60A File Offset: 0x0000C80A
			public string Name { get; set; }

			// Token: 0x040011F7 RID: 4599
			[CompilerGenerated]
			private string string_0;

			// Token: 0x040011F8 RID: 4600
			public string string_1;

			// Token: 0x040011F9 RID: 4601
			public string string_2;
		}

		// Token: 0x02000394 RID: 916
		[CompilerGenerated]
		private sealed class Class489
		{
			// Token: 0x0600255A RID: 9562 RVA: 0x000F7FD4 File Offset: 0x000F61D4
			internal double method_0(LevenClassifer.LevenFeatrue levenFeatrue_0)
			{
				return this.class494_0.method_2(levenFeatrue_0.string_1, this.string_0);
			}

			// Token: 0x040011FC RID: 4604
			public Class494 class494_0;

			// Token: 0x040011FD RID: 4605
			public string string_0;
		}

		// Token: 0x02000395 RID: 917
		[CompilerGenerated]
		private sealed class Class490
		{
			// Token: 0x040011FE RID: 4606
			public int int_0;
		}

		// Token: 0x02000396 RID: 918
		[CompilerGenerated]
		private sealed class Class491
		{
			// Token: 0x0600255D RID: 9565 RVA: 0x000F7FFC File Offset: 0x000F61FC
			internal bool method_0(Point point_0)
			{
				bool result;
				if (point_0.X == this.class490_0.int_0)
				{
					result = (point_0.Y == this.int_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x040011FF RID: 4607
			public int int_0;

			// Token: 0x04001200 RID: 4608
			public LevenClassifer.Class490 class490_0;
		}

		// Token: 0x02000397 RID: 919
		[CompilerGenerated]
		private sealed class Class492
		{
			// Token: 0x0600255F RID: 9567 RVA: 0x000F8034 File Offset: 0x000F6234
			internal double method_0(LevenClassifer.LevenFeatrue levenFeatrue_0)
			{
				return this.class494_0.method_2(levenFeatrue_0.string_1, this.string_0);
			}

			// Token: 0x04001201 RID: 4609
			public Class494 class494_0;

			// Token: 0x04001202 RID: 4610
			public string string_0;
		}

		// Token: 0x02000398 RID: 920
		[CompilerGenerated]
		private sealed class Class493
		{
			// Token: 0x06002561 RID: 9569 RVA: 0x0000E623 File Offset: 0x0000C823
			internal void method_0(Reg reg_0)
			{
				this.string_0 += reg_0.Name;
			}

			// Token: 0x04001203 RID: 4611
			public string string_0;
		}
	}
}
