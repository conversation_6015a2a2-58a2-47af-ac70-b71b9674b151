﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using TEx.Chart;

namespace TEx
{
	// Token: 0x020001D5 RID: 469
	[Serializable]
	internal sealed class DrawLineP : DrawLine, ISerializable
	{
		// Token: 0x06001257 RID: 4695 RVA: 0x000079B3 File Offset: 0x00005BB3
		public DrawLineP(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = "平行线";
			base.CanChgColor = true;
			base.IsOneClickLoc = false;
		}

		// Token: 0x06001258 RID: 4696 RVA: 0x000079DD File Offset: 0x00005BDD
		protected DrawLineP(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
			this.bool_6 = info.GetBoolean("IsLocLineLeft");
		}

		// Token: 0x06001259 RID: 4697 RVA: 0x000079FF File Offset: 0x00005BFF
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
			info.AddValue("IsLocLineLeft", this.bool_6);
		}

		// Token: 0x0600125A RID: 4698 RVA: 0x0007ECC4 File Offset: 0x0007CEC4
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			return this.method_41(chartCS_1, double_1, double_2, double_3, double_4, string_5);
		}

		// Token: 0x0600125B RID: 4699 RVA: 0x0007ECE4 File Offset: 0x0007CEE4
		private List<GraphObj> method_41(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			LineObj item = base.method_40(chartCS_1, double_1, double_2, double_3, double_4, string_5);
			Location location_ = this.method_43(chartCS_1, double_1, double_2, double_3, double_4);
			LineObj item2 = base.method_39(chartCS_1, location_, string_5);
			return new List<GraphObj>
			{
				item,
				item2
			};
		}

		// Token: 0x0600125C RID: 4700 RVA: 0x0007ED34 File Offset: 0x0007CF34
		private Location method_42(ChartCS chartCS_1)
		{
			return this.method_43(chartCS_1, base.Location.X1, base.Location.Y1, base.Location.X2, base.Location.Y2);
		}

		// Token: 0x0600125D RID: 4701 RVA: 0x0007ED78 File Offset: 0x0007CF78
		private Location method_43(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4)
		{
			GraphPane graphPane = chartCS_1.GraphPane;
			double num = (double)graphPane.Rect.Height;
			double num2 = (double)graphPane.Rect.Width;
			double num3 = Math.Abs(double_3 - double_1);
			double num4 = Math.Abs(double_4 - double_2);
			double num5 = (graphPane.XAxis.Scale.Max - graphPane.XAxis.Scale.Min) * num / num2 / (graphPane.YAxis.Scale.Max - graphPane.YAxis.Scale.Min);
			double num6 = num4 * num5;
			double num7 = Math.Sqrt(Math.Pow(num3, 2.0) + Math.Pow(num6, 2.0));
			double num8 = 0.8;
			double num9 = Math.Sqrt(Math.Pow(num7 * num8, 2.0) / (1.0 + Math.Pow(num3 / num6, 2.0)));
			double num10 = num9 * num3 / num6;
			if ((double_3 < double_1 && double_4 > double_2) || (double_1 < double_3 && double_2 > double_4))
			{
				num10 = -num10;
			}
			else if ((double_4 < double_2 && double_3 > double_1) || (double_2 < double_4 && double_1 > double_3))
			{
				num9 = -num9;
			}
			if (this.IsLocLineLeft)
			{
				num9 = -num9;
				num10 = -num10;
			}
			return base.vmethod_3(double_1 - num9, double_2 + num10 / num5, double_3 - num9, double_4 + num10 / num5);
		}

		// Token: 0x0600125E RID: 4702 RVA: 0x0007EEE8 File Offset: 0x0007D0E8
		protected override List<Location> vmethod_13(ChartCS chartCS_1)
		{
			return new List<Location>
			{
				base.Location,
				this.method_43(chartCS_1, base.Location.X1, base.Location.Y1, base.Location.X2, base.Location.Y2)
			};
		}

		// Token: 0x0600125F RID: 4703 RVA: 0x0007EF44 File Offset: 0x0007D144
		public override PointD vmethod_19(ChartCS chartCS_1, PointD pointD_0)
		{
			Location location = base.Location;
			Location location2 = this.method_42(chartCS_1);
			bool flag = base.method_18(pointD_0, location);
			PointD pointD = new PointD(location.X1, location.Y1);
			PointD pointD2 = new PointD(location.X2, location.Y2);
			PointD pointD_ = flag ? pointD : pointD2;
			bool flag2 = base.method_18(pointD_0, location2);
			PointD pointD3 = new PointD(location2.X1, location2.Y1);
			PointD pointD4 = new PointD(location2.X2, location2.Y2);
			PointD pointD_2 = flag2 ? pointD3 : pointD4;
			PointD result;
			if (base.method_19(pointD_0, pointD_, pointD_2))
			{
				if (!flag)
				{
					result = pointD;
				}
				else
				{
					result = pointD2;
				}
			}
			else
			{
				this.IsLocLineLeft = !this.IsLocLineLeft;
				if (!flag)
				{
					result = pointD3;
				}
				else
				{
					result = pointD4;
				}
			}
			return result;
		}

		// Token: 0x170002B7 RID: 695
		// (get) Token: 0x06001260 RID: 4704 RVA: 0x0007F00C File Offset: 0x0007D20C
		// (set) Token: 0x06001261 RID: 4705 RVA: 0x00007A1C File Offset: 0x00005C1C
		public bool IsLocLineLeft
		{
			get
			{
				return this.bool_6;
			}
			set
			{
				this.bool_6 = value;
			}
		}

		// Token: 0x04000997 RID: 2455
		private bool bool_6;
	}
}
