﻿using System;

namespace TEx.ImportTrans
{
	// Token: 0x02000369 RID: 873
	public sealed class CWrongUserName : IStoreElement
	{
		// Token: 0x17000639 RID: 1593
		// (get) Token: 0x06002486 RID: 9350 RVA: 0x000F4D54 File Offset: 0x000F2F54
		public string ID
		{
			get
			{
				return this.name + this.password;
			}
		}

		// Token: 0x06002487 RID: 9351 RVA: 0x0000E2D4 File Offset: 0x0000C4D4
		public CWrongUserName(string name, string password, DateTime date)
		{
			this.name = name;
			this.password = password;
			this.date = date;
		}

		// Token: 0x040011A5 RID: 4517
		public string name;

		// Token: 0x040011A6 RID: 4518
		public string password;

		// Token: 0x040011A7 RID: 4519
		public DateTime date;
	}
}
