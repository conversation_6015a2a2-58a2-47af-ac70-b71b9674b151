﻿using System;
using System.Windows.Forms;
using ns28;
using ns7;
using TEx;

namespace ns16
{
	// Token: 0x0200029B RID: 667
	internal sealed class Class54 : Class51
	{
		// Token: 0x06001D96 RID: 7574 RVA: 0x0000C5C3 File Offset: 0x0000A7C3
		public Class54(Panel panel_1, int int_4, int int_5, ChtCtrl_KLine chtCtrl_KLine_0, bool bool_3) : base(panel_1, int_4, int_5, chtCtrl_KLine_0, bool_3)
		{
			base.method_0(Class372.zoomin);
		}

		// Token: 0x06001D97 RID: 7575 RVA: 0x0000C5DF File Offset: 0x0000A7DF
		public Class54(Panel panel_1, int int_4, int int_5, ChtCtrl_KLine chtCtrl_KLine_0) : this(panel_1, int_4, int_5, chtCtrl_KLine_0, true)
		{
		}

		// Token: 0x06001D98 RID: 7576 RVA: 0x0000C5ED File Offset: 0x0000A7ED
		protected override void Class48_MouseEnter(object sender, EventArgs e)
		{
			base.Class48_MouseEnter(sender, e);
			base.method_0(Class372.zoomin_red);
		}

		// Token: 0x06001D99 RID: 7577 RVA: 0x0000C604 File Offset: 0x0000A804
		protected override void Class48_MouseLeave(object sender, EventArgs e)
		{
			base.Class48_MouseLeave(sender, e);
			base.method_0(Class372.zoomin);
		}

		// Token: 0x06001D9A RID: 7578 RVA: 0x0000C61B File Offset: 0x0000A81B
		protected override void Class48_Click(object sender, EventArgs e)
		{
			base.Class48_Click(sender, e);
			((ChtCtrl_KLine)base.ChtCtrl).method_117(false);
		}
	}
}
