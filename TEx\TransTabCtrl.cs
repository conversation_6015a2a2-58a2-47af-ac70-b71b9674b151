﻿using System;
using System.Windows.Forms;
using ns6;

namespace TEx
{
	// Token: 0x02000254 RID: 596
	internal sealed class TransTabCtrl : TransTabCtrl_1
	{
		// Token: 0x06001972 RID: 6514 RVA: 0x000AB1A0 File Offset: 0x000A93A0
		public TransTabCtrl(SplitterPanel panel) : base(panel)
		{
			if (Base.UI.SwitchedBehindTransTabs != null)
			{
				Base.UI.SwitchedBehindTransTabs.method_7(base.ContainerPanel);
				this._TransTabs = Base.UI.SwitchedBehindTransTabs;
				Base.UI.SwitchedBehindTransTabs = null;
			}
			else
			{
				this._TransTabs = new TransTabs(base.ContainerPanel);
			}
			this._TransTabs.ParentTransTabCtrl = this;
			base.PanelHeaderText = " 功能栏";
		}

		// Token: 0x06001973 RID: 6515 RVA: 0x0000A96E File Offset: 0x00008B6E
		protected override void button_Max_Click(object sender, EventArgs e)
		{
			this._TransTabs.Focus();
			base.button_Max_Click(sender, e);
		}

		// Token: 0x06001974 RID: 6516 RVA: 0x0000A986 File Offset: 0x00008B86
		public override void SetTransTabsMaximization()
		{
			base.SetTransTabsMaximization();
			Base.UI.Form.IsTransTabMaximized = !Base.UI.Form.IsTransTabMaximized;
		}

		// Token: 0x1700043C RID: 1084
		// (get) Token: 0x06001975 RID: 6517 RVA: 0x000AB208 File Offset: 0x000A9408
		public TransTabs TransTabs
		{
			get
			{
				return this._TransTabs;
			}
		}

		// Token: 0x04000CC9 RID: 3273
		private TransTabs _TransTabs;
	}
}
