﻿using System;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace ns7
{
	// Token: 0x02000415 RID: 1045
	[CompilerGenerated]
	internal sealed class Class546
	{
		// Token: 0x0400141E RID: 5150 RVA: 0x00002960 File Offset: 0x00000B60
		internal static readonly Class546.Struct33 struct33_0;

		// Token: 0x0400141F RID: 5151 RVA: 0x000029E0 File Offset: 0x00000BE0
		internal static readonly Class546.Struct32 struct32_0;

		// Token: 0x02000416 RID: 1046
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 116)]
		private struct Struct32
		{
		}

		// Token: 0x02000417 RID: 1047
		[StructLayout(LayoutKind.Explicit, Pack = 1, Size = 124)]
		private struct Struct33
		{
		}
	}
}
