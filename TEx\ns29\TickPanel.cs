﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using TEx.Comn;

namespace ns29
{
	// Token: 0x02000236 RID: 566
	internal sealed class TickPanel : UserControl
	{
		// Token: 0x06001799 RID: 6041 RVA: 0x000099F3 File Offset: 0x00007BF3
		public TickPanel()
		{
			this.InitializeComponent();
		}

		// Token: 0x0600179A RID: 6042 RVA: 0x0009E610 File Offset: 0x0009C810
		private void method_0(HDTick hdtick_1)
		{
			this.labelX_S1.Text = this.method_2(hdtick_1, true);
			this.labelX_B1.Text = this.method_2(hdtick_1, false);
			double num = hdtick_1.Price - this.hdtick_0.Price;
			this.labelItem_lastPrc.Text = this.method_3("最新", hdtick_1.Price.ToString(), (num > 0.0) ? "Red" : ((num == 0.0) ? "#E7E7E7" : "#00E700"));
			num = this.double_0 - this.double_3;
			this.labelItem_lastPrc.Text = this.method_3("开盘", this.double_0.ToString(), (num > 0.0) ? "Red" : ((num == 0.0) ? "#E7E7E7" : "#00E700"));
			if (hdtick_1.Price > this.double_1)
			{
				this.double_1 = hdtick_1.Price;
			}
			if (hdtick_1.Price < this.double_2)
			{
				this.double_2 = hdtick_1.Price;
			}
			num = this.double_1 - this.hdtick_0.Price;
			this.labelItem_lastPrc.Text = this.method_3("最高", this.double_1.ToString(), (num > 0.0) ? "Red" : ((num == 0.0) ? "#E7E7E7" : "#00E700"));
			num = this.double_2 - this.hdtick_0.Price;
			this.labelItem_lastPrc.Text = this.method_3("最低", this.double_2.ToString(), (num > 0.0) ? "Red" : ((num == 0.0) ? "#E7E7E7" : "#00E700"));
			num = hdtick_1.Price - this.double_3;
			this.labelItem_var.Text = this.method_3("涨跌", num.ToString(), (num > 0.0) ? "Red" : ((num == 0.0) ? "#E7E7E7" : "#00E700"));
			this.labelItem_varRatio.Text = this.method_3("幅度", (num / this.double_3).ToString("P"), (num > 0.0) ? "Red" : ((num == 0.0) ? "#E7E7E7" : "#00E700"));
			this.labelItem_totalVol.Text = this.method_3("总手", this.int_1.ToString(), "Yellow");
			this.labelItem_totalVol.Text = this.method_3("现手", hdtick_1.Volume.ToString(), "Yellow");
			this.labelItem_totalVol.Text = this.method_3("持仓", hdtick_1.Amount.ToString(), "Yellow");
			this.labelItem_currVol.Text = this.method_3("仓差", (hdtick_1.Amount - this.int_0).ToString(), "Yellow");
			this.labelItem_currVol.Text = this.method_3("外盘", this.int_2.ToString(), "Red");
			this.labelItem_currVol.Text = this.method_3("内盘", this.int_3.ToString(), "#00E700");
			int count = this.itemPanel_tickLines.Items.Count;
			for (int i = 1; i < count; i++)
			{
				BaseItem baseItem = this.itemPanel_tickLines.Items[i];
				if (i < count - 1)
				{
					baseItem.Text = this.itemPanel_tickLines.Items[i + 1].Text;
				}
				else
				{
					num = hdtick_1.Price - this.hdtick_0.Price;
					baseItem.Text = string.Concat(new string[]
					{
						"<span width=\"42\"><font color=\"White\">",
						hdtick_1.Date.ToString("hh:mm"),
						"</font></span><span align=\"right\" width=\"60\"><font color=\"",
						(hdtick_1.Price > this.double_3) ? "Red" : "#00E700",
						"\">",
						hdtick_1.Price.ToString(),
						"</font></span><span align=\"right\" width=\"35\"><font color=\"",
						hdtick_1.IsBuy ? "Red" : "#00E700",
						"\">",
						hdtick_1.Volume.ToString(),
						"</font></span><span align=\"right\" width=\"35\"><font color=\"Yellow\">",
						(hdtick_1.Amount - this.hdtick_0.Amount).ToString("E+"),
						"</font></span><span align=\"right\" padding=\"0,0,2,0\"><font color=\"",
						hdtick_1.IsBuy ? "Red" : "#00E700",
						"\" size=\"-1\">",
						this.method_1(hdtick_1),
						"</font></span>"
					});
				}
			}
		}

		// Token: 0x0600179B RID: 6043 RVA: 0x0009EB20 File Offset: 0x0009CD20
		private string method_1(HDTick hdtick_1)
		{
			int num = hdtick_1.Amount - this.hdtick_0.Amount;
			string result;
			if (hdtick_1.Volume - num == 0)
			{
				result = "双平";
			}
			else if (hdtick_1.IsBuy)
			{
				result = ((num == 0) ? "多换" : ((num < 0) ? "空平" : "多开"));
			}
			else
			{
				result = ((num == 0) ? "空换" : ((num < 0) ? "多平" : "空开"));
			}
			return result;
		}

		// Token: 0x0600179C RID: 6044 RVA: 0x0009EB98 File Offset: 0x0009CD98
		private string method_2(HDTick hdtick_1, bool bool_0)
		{
			return string.Concat(new string[]
			{
				"<span padding=\"0,40,3,0\"><font color=\"#E7E7E7\">",
				bool_0 ? "卖出" : "买入",
				"</font></span><span padding=\"0,40,0,0\"><font color=\"",
				(hdtick_1.S1Prc > this.hdtick_0.Price) ? "Red" : "LightGreen",
				"\" size=\"+3\">",
				hdtick_1.S1Prc.ToString(),
				"</font></span><span padding=\"0,0,2,0\"><font color=\"Yellow\" size=\"+2\">",
				hdtick_1.S1Vol.ToString(),
				"</font></span>"
			});
		}

		// Token: 0x0600179D RID: 6045 RVA: 0x0009EC34 File Offset: 0x0009CE34
		private string method_3(string string_0, string string_1, string string_2)
		{
			return string.Concat(new string[]
			{
				"<span padding=\"0,0,2,0\"><font color=\"#E7E7E7\">",
				string_0,
				"</font></span><span align=\"right\"><font color=\"",
				string_2,
				"\" size=\"+1\">",
				string_1,
				"</font></span>"
			});
		}

		// Token: 0x0600179E RID: 6046 RVA: 0x0009EC7C File Offset: 0x0009CE7C
		public void method_4(int int_4)
		{
			int num = int_4 - base.Height;
			this.itemPanel_tickLines.Height += num;
			if (this.itemPanel_tickLines.Controls.Count > 1 && this.itemPanel_tickLines.Height / 22 < this.itemPanel_tickLines.Items.Count)
			{
				int num2 = Convert.ToInt32(Math.Floor(Convert.ToDecimal(this.itemPanel_tickLines.Height) / 22m));
				if (num2 > 1)
				{
					for (int i = num2 - 1; i < this.itemPanel_tickLines.Controls.Count; i++)
					{
						this.itemPanel_tickLines.Items.RemoveAt(this.itemPanel_tickLines.Controls.Count - 1);
					}
				}
			}
			this.panelEx1.Height += num;
			base.Height = int_4;
		}

		// Token: 0x0600179F RID: 6047 RVA: 0x000041AE File Offset: 0x000023AE
		private void method_5(HDTick hdtick_1)
		{
		}

		// Token: 0x060017A0 RID: 6048 RVA: 0x00009A03 File Offset: 0x00007C03
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060017A1 RID: 6049 RVA: 0x0009ED64 File Offset: 0x0009CF64
		private void InitializeComponent()
		{
			ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof(TickPanel));
			this.panelEx1 = new PanelEx();
			this.panelEx2 = new PanelEx();
			this.itemPanel_ExtInfo2 = new ItemPanel();
			this.labelItem1_open = new LabelItem();
			this.labelItem1_high = new LabelItem();
			this.labelItem1_low = new LabelItem();
			this.labelItem_currVol = new LabelItem();
			this.labelItem1_amtVar = new LabelItem();
			this.labelItem_intVol = new LabelItem();
			this.itemPanel_ExtInfo1 = new ItemPanel();
			this.labelItem_lastPrc = new LabelItem();
			this.labelItem_var = new LabelItem();
			this.labelItem_varRatio = new LabelItem();
			this.labelItem_totalVol = new LabelItem();
			this.labelItem9_Amt = new LabelItem();
			this.labelItem_extVol = new LabelItem();
			this.labelX_B1 = new LabelX();
			this.labelX_S1 = new LabelX();
			this.labelX_Symb = new LabelX();
			this.itemPanel_tickLines = new ItemPanel();
			this.labelItem_heading = new LabelItem();
			this.labelItem2 = new LabelItem();
			this.labelItem3 = new LabelItem();
			this.labelItem1 = new LabelItem();
			this.panelEx1.SuspendLayout();
			this.panelEx2.SuspendLayout();
			base.SuspendLayout();
			this.panelEx1.AntiAlias = false;
			this.panelEx1.CanvasColor = SystemColors.Control;
			this.panelEx1.ColorSchemeStyle = eDotNetBarStyle.StyleManagerControlled;
			this.panelEx1.Controls.Add(this.panelEx2);
			this.panelEx1.Controls.Add(this.labelX_B1);
			this.panelEx1.Controls.Add(this.labelX_S1);
			this.panelEx1.Controls.Add(this.labelX_Symb);
			this.panelEx1.Controls.Add(this.itemPanel_tickLines);
			this.panelEx1.Font = new Font("SimSun", 9.5f);
			this.panelEx1.Location = new Point(0, 2);
			this.panelEx1.Name = "panelEx1";
			this.panelEx1.Size = new Size(220, 445);
			this.panelEx1.Style.Alignment = StringAlignment.Center;
			this.panelEx1.Style.BackColor1.Color = Color.Black;
			this.panelEx1.Style.BackColor2.Color = Color.Black;
			this.panelEx1.Style.Border = eBorderType.SingleLine;
			this.panelEx1.Style.BorderColor.Color = Color.DarkRed;
			this.panelEx1.Style.ForeColor.ColorSchemePart = eColorSchemePart.PanelText;
			this.panelEx1.Style.GradientAngle = 90;
			this.panelEx1.TabIndex = 1;
			this.panelEx1.Text = "panelEx1";
			this.panelEx2.CanvasColor = SystemColors.Control;
			this.panelEx2.ColorSchemeStyle = eDotNetBarStyle.StyleManagerControlled;
			this.panelEx2.Controls.Add(this.itemPanel_ExtInfo2);
			this.panelEx2.Controls.Add(this.itemPanel_ExtInfo1);
			this.panelEx2.Font = new Font("SimSun", 8.5f);
			this.panelEx2.Location = new Point(0, 85);
			this.panelEx2.Name = "panelEx2";
			this.panelEx2.Size = new Size(220, 128);
			this.panelEx2.Style.Alignment = StringAlignment.Center;
			this.panelEx2.Style.BackColor1.Alpha = 0;
			this.panelEx2.Style.BackColor1.Color = Color.Transparent;
			this.panelEx2.Style.BackColor2.Alpha = 0;
			this.panelEx2.Style.BackColor2.Color = Color.Transparent;
			this.panelEx2.Style.Border = eBorderType.SingleLine;
			this.panelEx2.Style.BorderColor.Color = Color.DarkRed;
			this.panelEx2.Style.ForeColor.ColorSchemePart = eColorSchemePart.PanelText;
			this.panelEx2.Style.GradientAngle = 90;
			this.panelEx2.TabIndex = 6;
			this.panelEx2.Text = "panelEx2";
			this.itemPanel_ExtInfo2.AntiAlias = false;
			this.itemPanel_ExtInfo2.BackgroundStyle.BackColor = Color.Transparent;
			this.itemPanel_ExtInfo2.BackgroundStyle.BorderColor = Color.Transparent;
			this.itemPanel_ExtInfo2.BackgroundStyle.Class = "ItemPanel";
			this.itemPanel_ExtInfo2.BackgroundStyle.CornerType = eCornerType.Square;
			this.itemPanel_ExtInfo2.ContainerControlProcessDialogKey = true;
			this.itemPanel_ExtInfo2.FadeEffect = false;
			this.itemPanel_ExtInfo2.Font = new Font("Microsoft Sans Serif", 9f);
			this.itemPanel_ExtInfo2.Items.AddRange(new BaseItem[]
			{
				this.labelItem1_open,
				this.labelItem1_high,
				this.labelItem1_low,
				this.labelItem_currVol,
				this.labelItem1_amtVar,
				this.labelItem_intVol
			});
			this.itemPanel_ExtInfo2.ItemSpacing = 4;
			this.itemPanel_ExtInfo2.LayoutOrientation = eOrientation.Vertical;
			this.itemPanel_ExtInfo2.Location = new Point(110, 0);
			this.itemPanel_ExtInfo2.Name = "itemPanel_ExtInfo2";
			this.itemPanel_ExtInfo2.Size = new Size(110, 128);
			this.itemPanel_ExtInfo2.TabIndex = 7;
			this.itemPanel_ExtInfo2.Text = "itemPanel2";
			this.labelItem1_open.Name = "labelItem1_open";
			this.labelItem1_open.PaddingLeft = 3;
			this.labelItem1_open.PaddingRight = 3;
			this.labelItem1_open.Text = "<span padding=\"0,0,2,0\"><font color=\"#E7E7E7\">开盘</font></span><span align=\"right\"><font color=\"#00E700\" size=\"+1\">2290.6</font></span>";
			this.labelItem1_high.Name = "labelItem1_high";
			this.labelItem1_high.PaddingLeft = 3;
			this.labelItem1_high.PaddingRight = 3;
			this.labelItem1_high.Text = "<span padding=\"0,0,2,0\"><font color=\"#E7E7E7\">最高</font></span><span align=\"right\"><font color=\"Red\" size=\"+1\">2303.2</font></span>";
			this.labelItem1_low.Name = "labelItem1_low";
			this.labelItem1_low.PaddingLeft = 3;
			this.labelItem1_low.PaddingRight = 3;
			this.labelItem1_low.Text = "<span padding=\"0,0,2,0\"><font color=\"#E7E7E7\">最低</font></span><span align=\"right\"><font color=\"#00E700\" size=\"+1\">2288.6</font></span>";
			this.labelItem_currVol.Name = "labelItem_currVol";
			this.labelItem_currVol.PaddingLeft = 3;
			this.labelItem_currVol.PaddingRight = 3;
			this.labelItem_currVol.Text = "<span padding=\"0,0,2,0\"><font color=\"#E7E7E7\">现手</font></span><span align=\"right\"><font color=\"Yellow\" size=\"+1\">12</font></span>";
			this.labelItem1_amtVar.Name = "labelItem1_amtVar";
			this.labelItem1_amtVar.PaddingLeft = 3;
			this.labelItem1_amtVar.PaddingRight = 3;
			this.labelItem1_amtVar.Text = "<span padding=\"0,0,2,0\"><font color=\"#E7E7E7\">仓差</font></span><span align=\"right\"><font color=\"Yellow\" size=\"+1\">-2345</font></span>";
			this.labelItem_intVol.Name = "labelItem_intVol";
			this.labelItem_intVol.PaddingLeft = 3;
			this.labelItem_intVol.PaddingRight = 3;
			this.labelItem_intVol.Text = "<span padding=\"0,0,2,0\"><font color=\"#E7E7E7\">内盘</font></span><span align=\"right\"><font color=\"#00E700\" size=\"+1\">194395</font></span>";
			this.itemPanel_ExtInfo1.AntiAlias = false;
			this.itemPanel_ExtInfo1.BackgroundStyle.Class = "";
			this.itemPanel_ExtInfo1.BackgroundStyle.CornerType = eCornerType.Square;
			this.itemPanel_ExtInfo1.ContainerControlProcessDialogKey = true;
			this.itemPanel_ExtInfo1.FadeEffect = false;
			this.itemPanel_ExtInfo1.Font = new Font("Microsoft Sans Serif", 9f);
			this.itemPanel_ExtInfo1.Items.AddRange(new BaseItem[]
			{
				this.labelItem_lastPrc,
				this.labelItem_var,
				this.labelItem_varRatio,
				this.labelItem_totalVol,
				this.labelItem9_Amt,
				this.labelItem_extVol
			});
			this.itemPanel_ExtInfo1.ItemSpacing = 4;
			this.itemPanel_ExtInfo1.LayoutOrientation = eOrientation.Vertical;
			this.itemPanel_ExtInfo1.Location = new Point(0, 0);
			this.itemPanel_ExtInfo1.Name = "itemPanel_ExtInfo1";
			this.itemPanel_ExtInfo1.Size = new Size(110, 128);
			this.itemPanel_ExtInfo1.TabIndex = 6;
			this.itemPanel_ExtInfo1.Text = "itemPanel2";
			this.labelItem_lastPrc.Name = "labelItem_lastPrc";
			this.labelItem_lastPrc.PaddingLeft = 3;
			this.labelItem_lastPrc.PaddingRight = 3;
			this.labelItem_lastPrc.PaddingTop = 2;
			this.labelItem_lastPrc.Text = "<span padding=\"0,0,2,0\"><font color=\"#E7E7E7\">最新</font></span><span align=\"right\"><font color=\"Red\" size=\"+1\">2299.0</font></span>";
			this.labelItem_var.Name = "labelItem_var";
			this.labelItem_var.PaddingLeft = 3;
			this.labelItem_var.PaddingRight = 3;
			this.labelItem_var.Text = "<span padding=\"0,0,2,0\"><font color=\"#E7E7E7\">涨跌</font></span><span align=\"right\"><font color=\"Red\" size=\"+1\">9</font></span>";
			this.labelItem_varRatio.Name = "labelItem_varRatio";
			this.labelItem_varRatio.PaddingLeft = 3;
			this.labelItem_varRatio.PaddingRight = 3;
			this.labelItem_varRatio.Text = "<span padding=\"0,0,2,0\"><font color=\"#E7E7E7\">幅度</font></span><span align=\"right\"><font color=\"Red\" size=\"+1\">0.21%</font></span>";
			this.labelItem_totalVol.Name = "labelItem_totalVol";
			this.labelItem_totalVol.PaddingLeft = 3;
			this.labelItem_totalVol.PaddingRight = 3;
			this.labelItem_totalVol.Text = "<span padding=\"0,0,2,0\"><font color=\"#E7E7E7\">总手</font></span><span align=\"right\"><font color=\"Yellow\" size=\"+1\">827890</font></span>";
			this.labelItem9_Amt.Name = "labelItem9_Amt";
			this.labelItem9_Amt.PaddingLeft = 3;
			this.labelItem9_Amt.PaddingRight = 3;
			this.labelItem9_Amt.Text = "<span padding=\"0,0,2,0\"><font color=\"#E7E7E7\">持仓</font></span><span align=\"right\"><font color=\"Yellow\" size=\"+1\">85623</font></span>";
			this.labelItem_extVol.Name = "labelItem_extVol";
			this.labelItem_extVol.PaddingLeft = 3;
			this.labelItem_extVol.PaddingRight = 3;
			this.labelItem_extVol.Text = "<span padding=\"0,0,2,0\"><font color=\"#E7E7E7\">外盘</font></span><span align=\"right\"><font color=\"Red\" size=\"+1\">233456</font></span>";
			this.labelX_B1.BackgroundStyle.BorderBottom = eStyleBorderType.Solid;
			this.labelX_B1.BackgroundStyle.BorderColor = Color.DarkRed;
			this.labelX_B1.BackgroundStyle.BorderColor2 = Color.DarkRed;
			this.labelX_B1.BackgroundStyle.BorderLeft = eStyleBorderType.Solid;
			this.labelX_B1.BackgroundStyle.BorderRight = eStyleBorderType.Solid;
			this.labelX_B1.BackgroundStyle.BorderTop = eStyleBorderType.Solid;
			this.labelX_B1.BackgroundStyle.Class = "";
			this.labelX_B1.BackgroundStyle.CornerType = eCornerType.Square;
			this.labelX_B1.Font = new Font("Microsoft Sans Serif", 9.6f);
			this.labelX_B1.Location = new Point(0, 57);
			this.labelX_B1.Name = "labelX_B1";
			this.labelX_B1.PaddingBottom = 2;
			this.labelX_B1.PaddingLeft = 6;
			this.labelX_B1.PaddingRight = 6;
			this.labelX_B1.PaddingTop = 2;
			this.labelX_B1.Size = new Size(220, 26);
			this.labelX_B1.TabIndex = 5;
			this.labelX_B1.Text = componentResourceManager.GetString("labelX_B1.Text");
			this.labelX_S1.BackgroundStyle.BorderBottom = eStyleBorderType.Solid;
			this.labelX_S1.BackgroundStyle.BorderColor = Color.DarkRed;
			this.labelX_S1.BackgroundStyle.BorderColor2 = Color.DarkRed;
			this.labelX_S1.BackgroundStyle.BorderLeft = eStyleBorderType.Solid;
			this.labelX_S1.BackgroundStyle.BorderRight = eStyleBorderType.Solid;
			this.labelX_S1.BackgroundStyle.BorderTop = eStyleBorderType.Solid;
			this.labelX_S1.BackgroundStyle.Class = "";
			this.labelX_S1.BackgroundStyle.CornerType = eCornerType.Square;
			this.labelX_S1.Font = new Font("Microsoft Sans Serif", 9.6f);
			this.labelX_S1.Location = new Point(0, 29);
			this.labelX_S1.Name = "labelX_S1";
			this.labelX_S1.PaddingBottom = 2;
			this.labelX_S1.PaddingLeft = 6;
			this.labelX_S1.PaddingRight = 6;
			this.labelX_S1.PaddingTop = 2;
			this.labelX_S1.Size = new Size(220, 26);
			this.labelX_S1.TabIndex = 4;
			this.labelX_S1.Text = componentResourceManager.GetString("labelX_S1.Text");
			this.labelX_Symb.AntiAlias = false;
			this.labelX_Symb.BackgroundStyle.Class = "";
			this.labelX_Symb.BackgroundStyle.CornerType = eCornerType.Square;
			this.labelX_Symb.Font = new Font("SimSun", 13.5f);
			this.labelX_Symb.ForeColor = Color.Yellow;
			this.labelX_Symb.Location = new Point(0, 5);
			this.labelX_Symb.Name = "labelX_Symb";
			this.labelX_Symb.Size = new Size(220, 26);
			this.labelX_Symb.TabIndex = 3;
			this.labelX_Symb.Text = "股指连续 (IF)";
			this.labelX_Symb.TextAlignment = StringAlignment.Center;
			this.itemPanel_tickLines.AntiAlias = false;
			this.itemPanel_tickLines.BackgroundStyle.BackColor = Color.Black;
			this.itemPanel_tickLines.BackgroundStyle.BorderColor = Color.DarkRed;
			this.itemPanel_tickLines.BackgroundStyle.Class = "ItemPanel";
			this.itemPanel_tickLines.BackgroundStyle.CornerType = eCornerType.Square;
			this.itemPanel_tickLines.ContainerControlProcessDialogKey = true;
			this.itemPanel_tickLines.FadeEffect = false;
			this.itemPanel_tickLines.Font = new Font("Microsoft Sans Serif", 10f);
			this.itemPanel_tickLines.Items.AddRange(new BaseItem[]
			{
				this.labelItem_heading,
				this.labelItem2,
				this.labelItem3,
				this.labelItem1
			});
			this.itemPanel_tickLines.ItemSpacing = 4;
			this.itemPanel_tickLines.LayoutOrientation = eOrientation.Vertical;
			this.itemPanel_tickLines.Location = new Point(0, 212);
			this.itemPanel_tickLines.Name = "itemPanel_tickLines";
			this.itemPanel_tickLines.Size = new Size(220, 233);
			this.itemPanel_tickLines.TabIndex = 2;
			this.labelItem_heading.Name = "labelItem_heading";
			this.labelItem_heading.PaddingLeft = 5;
			this.labelItem_heading.PaddingRight = 4;
			this.labelItem_heading.PaddingTop = 3;
			this.labelItem_heading.Text = componentResourceManager.GetString("labelItem_heading.Text");
			this.labelItem2.Name = "labelItem2";
			this.labelItem2.PaddingLeft = 3;
			this.labelItem2.PaddingRight = 4;
			this.labelItem2.Text = componentResourceManager.GetString("labelItem2.Text");
			this.labelItem2.TextAlignment = StringAlignment.Far;
			this.labelItem3.Name = "labelItem3";
			this.labelItem3.PaddingLeft = 3;
			this.labelItem3.PaddingRight = 4;
			this.labelItem3.Text = componentResourceManager.GetString("labelItem3.Text");
			this.labelItem3.TextAlignment = StringAlignment.Far;
			this.labelItem1.Name = "labelItem1";
			this.labelItem1.PaddingLeft = 3;
			this.labelItem1.PaddingRight = 4;
			this.labelItem1.Text = componentResourceManager.GetString("labelItem1.Text");
			base.AutoScaleDimensions = new SizeF(7f, 13f);
			base.AutoScaleMode = AutoScaleMode.Font;
			this.BackColor = Color.Black;
			base.Controls.Add(this.panelEx1);
			base.Name = "TickPanel";
			base.Size = new Size(220, 447);
			this.panelEx1.ResumeLayout(false);
			this.panelEx2.ResumeLayout(false);
			base.ResumeLayout(false);
		}

		// Token: 0x04000BF2 RID: 3058
		private HDTick hdtick_0;

		// Token: 0x04000BF3 RID: 3059
		private double double_0;

		// Token: 0x04000BF4 RID: 3060
		private double double_1;

		// Token: 0x04000BF5 RID: 3061
		private double double_2;

		// Token: 0x04000BF6 RID: 3062
		private double double_3;

		// Token: 0x04000BF7 RID: 3063
		private int int_0;

		// Token: 0x04000BF8 RID: 3064
		private int int_1;

		// Token: 0x04000BF9 RID: 3065
		private int int_2;

		// Token: 0x04000BFA RID: 3066
		private int int_3;

		// Token: 0x04000BFB RID: 3067
		private IContainer icontainer_0;

		// Token: 0x04000BFC RID: 3068
		private PanelEx panelEx1;

		// Token: 0x04000BFD RID: 3069
		private ItemPanel itemPanel_tickLines;

		// Token: 0x04000BFE RID: 3070
		private LabelItem labelItem_heading;

		// Token: 0x04000BFF RID: 3071
		private LabelItem labelItem2;

		// Token: 0x04000C00 RID: 3072
		private LabelItem labelItem3;

		// Token: 0x04000C01 RID: 3073
		private LabelX labelX_S1;

		// Token: 0x04000C02 RID: 3074
		private LabelX labelX_Symb;

		// Token: 0x04000C03 RID: 3075
		private LabelX labelX_B1;

		// Token: 0x04000C04 RID: 3076
		private ItemPanel itemPanel_ExtInfo1;

		// Token: 0x04000C05 RID: 3077
		private PanelEx panelEx2;

		// Token: 0x04000C06 RID: 3078
		private LabelItem labelItem_lastPrc;

		// Token: 0x04000C07 RID: 3079
		private LabelItem labelItem_var;

		// Token: 0x04000C08 RID: 3080
		private LabelItem labelItem_varRatio;

		// Token: 0x04000C09 RID: 3081
		private LabelItem labelItem_totalVol;

		// Token: 0x04000C0A RID: 3082
		private LabelItem labelItem9_Amt;

		// Token: 0x04000C0B RID: 3083
		private LabelItem labelItem_extVol;

		// Token: 0x04000C0C RID: 3084
		private ItemPanel itemPanel_ExtInfo2;

		// Token: 0x04000C0D RID: 3085
		private LabelItem labelItem1_open;

		// Token: 0x04000C0E RID: 3086
		private LabelItem labelItem1_high;

		// Token: 0x04000C0F RID: 3087
		private LabelItem labelItem1_low;

		// Token: 0x04000C10 RID: 3088
		private LabelItem labelItem_currVol;

		// Token: 0x04000C11 RID: 3089
		private LabelItem labelItem1_amtVar;

		// Token: 0x04000C12 RID: 3090
		private LabelItem labelItem_intVol;

		// Token: 0x04000C13 RID: 3091
		private LabelItem labelItem1;
	}
}
