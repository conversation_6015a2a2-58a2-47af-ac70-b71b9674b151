﻿using System;

namespace ns26
{
	// Token: 0x02000086 RID: 134
	internal sealed class EventArgs2 : EventArgs
	{
		// Token: 0x0600047B RID: 1147 RVA: 0x00003EEE File Offset: 0x000020EE
		public EventArgs2(int int_2, int int_3, string string_1)
		{
			this.int_0 = int_2;
			this.int_1 = int_3;
			this.string_0 = string_1;
		}

		// Token: 0x170000E7 RID: 231
		// (get) Token: 0x0600047C RID: 1148 RVA: 0x00023F1C File Offset: 0x0002211C
		public int TaskIndex
		{
			get
			{
				return this.int_0;
			}
		}

		// Token: 0x170000E8 RID: 232
		// (get) Token: 0x0600047D RID: 1149 RVA: 0x00023F34 File Offset: 0x00022134
		public int TaskCount
		{
			get
			{
				return this.int_1;
			}
		}

		// Token: 0x170000E9 RID: 233
		// (get) Token: 0x0600047E RID: 1150 RVA: 0x00023F4C File Offset: 0x0002214C
		public string FileName
		{
			get
			{
				return this.string_0;
			}
		}

		// Token: 0x040001B5 RID: 437
		private int int_0;

		// Token: 0x040001B6 RID: 438
		private int int_1;

		// Token: 0x040001B7 RID: 439
		private string string_0;
	}
}
