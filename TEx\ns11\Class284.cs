﻿using System;
using System.Data;
using System.Windows.Forms;
using ns14;
using TEx;

namespace ns11
{
	// Token: 0x02000208 RID: 520
	internal sealed class Class284 : Class283
	{
		// Token: 0x0600154F RID: 5455 RVA: 0x00008830 File Offset: 0x00006A30
		public Class284() : base(true)
		{
			base.ParentChanged += this.Class284_ParentChanged;
			base.CellDoubleClick += this.Class284_CellDoubleClick;
		}

		// Token: 0x06001550 RID: 5456 RVA: 0x0008D03C File Offset: 0x0008B23C
		private void Class284_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
		{
			if (e.RowIndex < base.Rows.Count && e.RowIndex >= 0)
			{
				DataRowView dataRowView = base.Rows[e.RowIndex].DataBoundItem as DataRowView;
				if (dataRowView != null)
				{
					DataRow row = dataRowView.Row;
					string string_ = row[0] as string;
					string string_2 = row[1] as string;
					StkSymbol stkSymbol = SymbMgr.smethod_6(string_, string_2);
					if ((stkSymbol != null && Base.Data.CurrSelectedSymbol == null) || stkSymbol.ID != Base.Data.CurrSelectedSymbol.ID)
					{
						Base.UI.smethod_176(Base.Data.string_2);
						Base.Data.smethod_69(stkSymbol, false, false, false, Base.Data.smethod_54());
						Base.UI.smethod_178();
					}
				}
			}
		}

		// Token: 0x06001551 RID: 5457 RVA: 0x0000885F File Offset: 0x00006A5F
		private void Class284_ParentChanged(object sender, EventArgs e)
		{
			if (base.Parent != null)
			{
				base.Parent.SizeChanged += this.method_5;
			}
		}

		// Token: 0x06001552 RID: 5458 RVA: 0x00008882 File Offset: 0x00006A82
		private void method_5(object sender, EventArgs e)
		{
			this.vmethod_3();
		}

		// Token: 0x06001553 RID: 5459 RVA: 0x0008D0F0 File Offset: 0x0008B2F0
		protected override void Class283_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
		{
			base.Class283_CellFormatting(sender, e);
			if ((sender as DataGridView).Columns[e.ColumnIndex].Name == "报告期")
			{
				string text = e.Value.ToString();
				if (!text.Contains("-"))
				{
					e.Value = string.Concat(new string[]
					{
						text.Substring(0, 4),
						"-",
						text.Substring(4, 2),
						"-",
						text.Substring(6, 2)
					});
				}
			}
		}

		// Token: 0x06001554 RID: 5460 RVA: 0x0002DC84 File Offset: 0x0002BE84
		protected override bool vmethod_4(DataGridView dataGridView_0, DataGridViewCellFormattingEventArgs dataGridViewCellFormattingEventArgs_0)
		{
			return false;
		}

		// Token: 0x06001555 RID: 5461 RVA: 0x0008D18C File Offset: 0x0008B38C
		protected override bool vmethod_5(DataGridView dataGridView_0, DataGridViewCellFormattingEventArgs dataGridViewCellFormattingEventArgs_0)
		{
			DataGridViewColumn dataGridViewColumn_ = dataGridView_0.Columns[dataGridViewCellFormattingEventArgs_0.ColumnIndex];
			bool result;
			if (dataGridViewCellFormattingEventArgs_0.ColumnIndex != 0 && dataGridViewCellFormattingEventArgs_0.ColumnIndex != 1 && !this.method_7(dataGridViewColumn_))
			{
				result = false;
			}
			else
			{
				result = true;
			}
			return result;
		}

		// Token: 0x06001556 RID: 5462 RVA: 0x0008D1D0 File Offset: 0x0008B3D0
		protected override void vmethod_3()
		{
			if (base.Columns.Count > 0)
			{
				if (base.Parent != null)
				{
					if (base.Parent.Width / base.ColumnCount > 120)
					{
						base.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
					}
					else
					{
						base.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.AllCells;
					}
				}
				if (base.AutoSizeColumnsMode != DataGridViewAutoSizeColumnsMode.AllCells)
				{
					base.Columns[0].AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
					base.Columns[1].AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
					if (this.method_6(base.Columns[2]))
					{
						base.Columns[2].AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
					}
					DataGridViewColumn dataGridViewColumn = base.Columns[base.Columns.Count - 1];
					if (this.method_7(dataGridViewColumn))
					{
						dataGridViewColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
					}
				}
			}
			base.vmethod_3();
		}

		// Token: 0x06001557 RID: 5463 RVA: 0x0008D2A4 File Offset: 0x0008B4A4
		private bool method_6(DataGridViewColumn dataGridViewColumn_0)
		{
			bool result;
			if (!(dataGridViewColumn_0.Name == "收盘价格") && !dataGridViewColumn_0.Name.Contains("昨收"))
			{
				result = false;
			}
			else
			{
				result = true;
			}
			return result;
		}

		// Token: 0x06001558 RID: 5464 RVA: 0x0008D2E0 File Offset: 0x0008B4E0
		private bool method_7(DataGridViewColumn dataGridViewColumn_0)
		{
			bool result;
			if (dataGridViewColumn_0.Name == "报告期")
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}
	}
}
