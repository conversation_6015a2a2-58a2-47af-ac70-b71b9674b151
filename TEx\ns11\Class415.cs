﻿using System;
using System.Collections.Generic;
using ns12;
using ns13;
using ns14;
using ns23;
using ns28;
using ns30;
using ns9;
using TEx.SIndicator;

namespace ns11
{
	// Token: 0x02000315 RID: 789
	internal sealed class Class415 : Class412
	{
		// Token: 0x060021F4 RID: 8692 RVA: 0x0000D8A3 File Offset: 0x0000BAA3
		public Class415(HToken htoken_1, Class408 class408_2, Class408 class408_3) : base(htoken_1, class408_2, class408_3)
		{
		}

		// Token: 0x060021F5 RID: 8693 RVA: 0x000E85E0 File Offset: 0x000E67E0
		public static Class408 smethod_0(Tokenes tokenes_0, HToken htoken_1)
		{
			HToken htoken_2 = new HToken(tokenes_0.Current.Col, tokenes_0.Current.Line, new Class439(Enum26.const_33, ""));
			Class408 result;
			if (tokenes_0.Current.Symbol.Name == ")")
			{
				Class439 symbol = new Class439(Enum26.const_31, "");
				HToken htoken_3 = new HToken(tokenes_0.Current.Col, tokenes_0.Current.Line, symbol);
				Class409 class408_ = new Class409(htoken_3);
				Class409 class408_2 = new Class409(htoken_3);
				result = new Class415(htoken_2, class408_, class408_2);
			}
			else
			{
				Class408 class408_3 = Class413.smethod_0(tokenes_0);
				tokenes_0.method_1();
				if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_24)
				{
					Class409 class408_4 = new Class409(new HToken(0, 0, new Class439(Enum26.const_31, "")));
					result = new Class415(htoken_2, class408_3, class408_4);
				}
				else
				{
					if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_21)
					{
						throw new Exception(htoken_1.method_0("函数参数错误"));
					}
					tokenes_0.method_1();
					Class408 class408_5 = Class415.smethod_1(tokenes_0, htoken_1);
					result = new Class415(htoken_2, class408_3, class408_5);
				}
			}
			return result;
		}

		// Token: 0x060021F6 RID: 8694 RVA: 0x000E8708 File Offset: 0x000E6908
		private static Class408 smethod_1(Tokenes tokenes_0, HToken htoken_1)
		{
			HToken htoken_2 = new HToken(tokenes_0.Current.Col, tokenes_0.Current.Line, new Class439(Enum26.const_32, "参数"));
			Class408 class408_ = Class413.smethod_0(tokenes_0);
			tokenes_0.method_1();
			Class408 result;
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_21)
			{
				tokenes_0.method_1();
				Class408 class408_2 = Class415.smethod_1(tokenes_0, htoken_1);
				result = new Class421(htoken_2, class408_, class408_2);
			}
			else
			{
				if (tokenes_0.Current.Symbol.HSymbolType != Enum26.const_24)
				{
					throw new Exception(htoken_1.method_0("函数参数错误"));
				}
				Class409 class408_3 = new Class409(new HToken(0, 0, new Class439(Enum26.const_31, "")));
				result = new Class421(htoken_2, class408_, class408_3);
			}
			return result;
		}

		// Token: 0x060021F7 RID: 8695 RVA: 0x000E87C8 File Offset: 0x000E69C8
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			List<object> list = new List<object>();
			object obj = this.Left.vmethod_1(parserEnvironment_0);
			if (obj != null)
			{
				list.Add(obj);
			}
			Class408 right = this.Right;
			while (right.GetType() == typeof(Class421))
			{
				list.Add(right.Left.vmethod_1(parserEnvironment_0));
				if (right.Right.Token.Symbol.HSymbolType == Enum26.const_31)
				{
					IL_9B:
					return list;
				}
				right = right.Right;
			}
			if (right.Token.Symbol.HSymbolType != Enum26.const_31)
			{
				throw new Exception(this.Right.Token.method_0("类型错误"));
			}
			goto IL_9B;
		}

		// Token: 0x060021F8 RID: 8696 RVA: 0x000E8564 File Offset: 0x000E6764
		public override string vmethod_0()
		{
			return base.vmethod_0();
		}
	}
}
