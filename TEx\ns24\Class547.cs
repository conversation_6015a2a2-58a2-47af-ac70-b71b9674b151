﻿using System;
using Microsoft.Win32;

namespace ns24
{
	// Token: 0x02000419 RID: 1049
	internal sealed class Class547
	{
		// Token: 0x06002866 RID: 10342 RVA: 0x00105784 File Offset: 0x00103984
		public static string smethod_0(string string_1)
		{
			string result;
			try
			{
				RegistryKey registryKey = Registry.LocalMachine.OpenSubKey("SOFTWARE\\RedGate\\SmartAssembly");
				if (registryKey == null)
				{
					result = string.Empty;
				}
				else
				{
					string text = (string)registryKey.GetValue(string_1, string.Empty);
					registryKey.Close();
					result = text;
				}
			}
			catch
			{
				result = string.Empty;
			}
			return result;
		}

		// Token: 0x06002867 RID: 10343 RVA: 0x001057E0 File Offset: 0x001039E0
		public static void smethod_1(string string_1, string string_2)
		{
			try
			{
				RegistryKey registryKey = Registry.LocalMachine.OpenSubKey("SOFTWARE\\RedGate\\SmartAssembly", true);
				if (registryKey == null)
				{
					registryKey = Registry.LocalMachine.CreateSubKey("SOFTWARE\\RedGate\\SmartAssembly");
				}
				registryKey.SetValue(string_1, string_2);
				registryKey.Close();
			}
			catch
			{
			}
		}

		// Token: 0x0400143C RID: 5180
		private const string string_0 = "SOFTWARE\\RedGate\\SmartAssembly";
	}
}
