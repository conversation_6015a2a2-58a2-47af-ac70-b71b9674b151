﻿using System;
using System.ComponentModel;
using System.Runtime.Serialization;

namespace TEx.Trading
{
	// Token: 0x020003AF RID: 943
	[Serializable]
	public class Transaction : ISerializable
	{
		// Token: 0x06002626 RID: 9766 RVA: 0x00002D25 File Offset: 0x00000F25
		public Transaction()
		{
		}

		// Token: 0x06002627 RID: 9767 RVA: 0x000FB05C File Offset: 0x000F925C
		protected Transaction(SerializationInfo info, StreamingContext context)
		{
			this.int_0 = info.GetInt32("_ID");
			this.int_1 = info.GetInt32("_AcctID");
			this.int_2 = info.GetInt32("_TransType");
			this.int_3 = info.GetInt32("_SymbolID");
			this.decimal_0 = info.GetDecimal("_Price");
			this.long_0 = Convert.ToInt64(info.GetInt32("_Units"));
			int? num = (int?)info.GetValue("_OpenUnits", typeof(int?));
			if (num != null)
			{
				this.nullable_0 = new long?(Convert.ToInt64(num.Value));
			}
			this.nullable_1 = (decimal?)info.GetValue("_Fee", typeof(decimal?));
			this.nullable_2 = (decimal?)info.GetValue("_Profit", typeof(decimal?));
			this.nullable_3 = (int?)info.GetValue("_ClosedTransID", typeof(int?));
			this.dateTime_0 = info.GetDateTime("_CreateTime");
			this.nullable_4 = (DateTime?)info.GetValue("_UpdateTime", typeof(DateTime?));
			try
			{
				this.string_0 = info.GetString("_Notes");
			}
			catch
			{
			}
			try
			{
				this.dateTime_1 = info.GetDateTime("_CreateTimeN");
				this.nullable_5 = (DateTime?)info.GetValue("_UpdateTimeN", typeof(DateTime?));
			}
			catch
			{
			}
		}

		// Token: 0x06002628 RID: 9768 RVA: 0x000FB214 File Offset: 0x000F9414
		public virtual void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			info.AddValue("_ID", this.int_0);
			info.AddValue("_AcctID", this.int_1);
			info.AddValue("_TransType", this.int_2);
			info.AddValue("_SymbolID", this.int_3);
			info.AddValue("_Price", this.decimal_0);
			int value;
			if (this.long_0 > 2147483647L)
			{
				value = int.MaxValue;
			}
			else
			{
				value = Convert.ToInt32(this.long_0);
			}
			info.AddValue("_Units", value);
			long? num = this.nullable_0;
			int value2;
			if (num.GetValueOrDefault() > 2147483647L & num != null)
			{
				value2 = int.MaxValue;
			}
			else
			{
				value2 = Convert.ToInt32(this.nullable_0);
			}
			info.AddValue("_OpenUnits", value2);
			info.AddValue("_Fee", this.nullable_1);
			info.AddValue("_Profit", this.nullable_2);
			info.AddValue("_ClosedTransID", this.nullable_3);
			info.AddValue("_Notes", this.string_0);
			info.AddValue("_CreateTime", this.dateTime_0);
			info.AddValue("_UpdateTime", this.nullable_4);
			info.AddValue("_CreateTimeN", this.dateTime_1);
			info.AddValue("_UpdateTimeN", this.nullable_5);
		}

		// Token: 0x17000671 RID: 1649
		// (get) Token: 0x06002629 RID: 9769 RVA: 0x000FB39C File Offset: 0x000F959C
		// (set) Token: 0x0600262A RID: 9770 RVA: 0x0000E89C File Offset: 0x0000CA9C
		[Browsable(false)]
		public int ID
		{
			get
			{
				return this.int_0;
			}
			set
			{
				if (this.int_0 != value)
				{
					this.int_0 = value;
				}
			}
		}

		// Token: 0x17000672 RID: 1650
		// (get) Token: 0x0600262B RID: 9771 RVA: 0x000FB3B4 File Offset: 0x000F95B4
		// (set) Token: 0x0600262C RID: 9772 RVA: 0x0000E8B0 File Offset: 0x0000CAB0
		[Browsable(false)]
		public int AcctID
		{
			get
			{
				return this.int_1;
			}
			set
			{
				if (this.int_1 != value)
				{
					this.int_1 = value;
				}
			}
		}

		// Token: 0x17000673 RID: 1651
		// (get) Token: 0x0600262D RID: 9773 RVA: 0x000FB3CC File Offset: 0x000F95CC
		// (set) Token: 0x0600262E RID: 9774 RVA: 0x0000E8C4 File Offset: 0x0000CAC4
		[Browsable(false)]
		public int TransType
		{
			get
			{
				return this.int_2;
			}
			set
			{
				if (this.int_2 != value)
				{
					this.int_2 = value;
				}
			}
		}

		// Token: 0x17000674 RID: 1652
		// (get) Token: 0x0600262F RID: 9775 RVA: 0x000FB3E4 File Offset: 0x000F95E4
		// (set) Token: 0x06002630 RID: 9776 RVA: 0x0000E8D8 File Offset: 0x0000CAD8
		[Browsable(false)]
		public int SymbolID
		{
			get
			{
				return this.int_3;
			}
			set
			{
				if (this.int_3 != value)
				{
					this.int_3 = value;
				}
			}
		}

		// Token: 0x17000675 RID: 1653
		// (get) Token: 0x06002631 RID: 9777 RVA: 0x000FB3FC File Offset: 0x000F95FC
		// (set) Token: 0x06002632 RID: 9778 RVA: 0x0000E8EC File Offset: 0x0000CAEC
		[DisplayName("数量")]
		public long Units
		{
			get
			{
				return this.long_0;
			}
			set
			{
				if (this.long_0 != value)
				{
					this.long_0 = value;
				}
			}
		}

		// Token: 0x17000676 RID: 1654
		// (get) Token: 0x06002633 RID: 9779 RVA: 0x000FB414 File Offset: 0x000F9614
		// (set) Token: 0x06002634 RID: 9780 RVA: 0x0000E900 File Offset: 0x0000CB00
		[DisplayName("开仓均价")]
		public decimal Price
		{
			get
			{
				return this.decimal_0;
			}
			set
			{
				if (this.decimal_0 != value)
				{
					this.decimal_0 = value;
				}
			}
		}

		// Token: 0x17000677 RID: 1655
		// (get) Token: 0x06002635 RID: 9781 RVA: 0x000FB42C File Offset: 0x000F962C
		// (set) Token: 0x06002636 RID: 9782 RVA: 0x000FB444 File Offset: 0x000F9644
		[DisplayName("持仓")]
		public long? OpenUnits
		{
			get
			{
				return this.nullable_0;
			}
			set
			{
				long? num = this.nullable_0;
				long? num2 = value;
				if (!(num.GetValueOrDefault() == num2.GetValueOrDefault() & num != null == (num2 != null)))
				{
					this.nullable_0 = value;
				}
			}
		}

		// Token: 0x17000678 RID: 1656
		// (get) Token: 0x06002637 RID: 9783 RVA: 0x000FB488 File Offset: 0x000F9688
		// (set) Token: 0x06002638 RID: 9784 RVA: 0x000FB4A0 File Offset: 0x000F96A0
		[DisplayName("手续费")]
		public decimal? Fee
		{
			get
			{
				return this.nullable_1;
			}
			set
			{
				decimal? num = this.nullable_1;
				decimal? num2 = value;
				if (!(num.GetValueOrDefault() == num2.GetValueOrDefault() & num != null == (num2 != null)))
				{
					this.nullable_1 = value;
				}
			}
		}

		// Token: 0x17000679 RID: 1657
		// (get) Token: 0x06002639 RID: 9785 RVA: 0x000FB4E8 File Offset: 0x000F96E8
		// (set) Token: 0x0600263A RID: 9786 RVA: 0x000FB500 File Offset: 0x000F9700
		[DisplayName("盈利")]
		public decimal? Profit
		{
			get
			{
				return this.nullable_2;
			}
			set
			{
				decimal? num = this.nullable_2;
				decimal? num2 = value;
				if (!(num.GetValueOrDefault() == num2.GetValueOrDefault() & num != null == (num2 != null)))
				{
					this.nullable_2 = value;
				}
			}
		}

		// Token: 0x1700067A RID: 1658
		// (get) Token: 0x0600263B RID: 9787 RVA: 0x000FB548 File Offset: 0x000F9748
		// (set) Token: 0x0600263C RID: 9788 RVA: 0x000FB560 File Offset: 0x000F9760
		[Browsable(false)]
		public int? ClosedTransID
		{
			get
			{
				return this.nullable_3;
			}
			set
			{
				int? num = this.nullable_3;
				int? num2 = value;
				if (!(num.GetValueOrDefault() == num2.GetValueOrDefault() & num != null == (num2 != null)))
				{
					this.nullable_3 = value;
				}
			}
		}

		// Token: 0x1700067B RID: 1659
		// (get) Token: 0x0600263D RID: 9789 RVA: 0x000FB5A4 File Offset: 0x000F97A4
		// (set) Token: 0x0600263E RID: 9790 RVA: 0x0000E919 File Offset: 0x0000CB19
		[DisplayName("成交时间")]
		public DateTime CreateTime
		{
			get
			{
				return this.dateTime_0;
			}
			set
			{
				if (this.dateTime_0 != value)
				{
					this.dateTime_0 = value;
				}
			}
		}

		// Token: 0x1700067C RID: 1660
		// (get) Token: 0x0600263F RID: 9791 RVA: 0x000FB5BC File Offset: 0x000F97BC
		// (set) Token: 0x06002640 RID: 9792 RVA: 0x000FB5D4 File Offset: 0x000F97D4
		[DisplayName("更新时间")]
		public DateTime? UpdateTime
		{
			get
			{
				return this.nullable_4;
			}
			set
			{
				if (this.nullable_4 != value)
				{
					this.nullable_4 = value;
				}
			}
		}

		// Token: 0x1700067D RID: 1661
		// (get) Token: 0x06002641 RID: 9793 RVA: 0x000FB628 File Offset: 0x000F9828
		// (set) Token: 0x06002642 RID: 9794 RVA: 0x0000E932 File Offset: 0x0000CB32
		[Browsable(false)]
		public DateTime CreateTimeN
		{
			get
			{
				return this.dateTime_1;
			}
			set
			{
				if (this.dateTime_1 != value)
				{
					this.dateTime_1 = value;
				}
			}
		}

		// Token: 0x1700067E RID: 1662
		// (get) Token: 0x06002643 RID: 9795 RVA: 0x000FB640 File Offset: 0x000F9840
		// (set) Token: 0x06002644 RID: 9796 RVA: 0x000FB658 File Offset: 0x000F9858
		[Browsable(false)]
		public DateTime? UpdateTimeN
		{
			get
			{
				return this.nullable_5;
			}
			set
			{
				if (this.nullable_5 != value)
				{
					this.nullable_5 = value;
				}
			}
		}

		// Token: 0x1700067F RID: 1663
		// (get) Token: 0x06002645 RID: 9797 RVA: 0x000FB6AC File Offset: 0x000F98AC
		// (set) Token: 0x06002646 RID: 9798 RVA: 0x0000E94B File Offset: 0x0000CB4B
		[DisplayName("注释")]
		public string Notes
		{
			get
			{
				return this.string_0;
			}
			set
			{
				if (this.string_0 != value)
				{
					this.string_0 = value;
				}
			}
		}

		// Token: 0x17000680 RID: 1664
		// (get) Token: 0x06002647 RID: 9799 RVA: 0x000FB6C4 File Offset: 0x000F98C4
		[Browsable(false)]
		public bool IsOpen
		{
			get
			{
				bool result;
				if (this.TransType != 1)
				{
					result = (this.TransType == 3);
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x04001263 RID: 4707
		private int int_0;

		// Token: 0x04001264 RID: 4708
		private int int_1;

		// Token: 0x04001265 RID: 4709
		private int int_2;

		// Token: 0x04001266 RID: 4710
		private int int_3;

		// Token: 0x04001267 RID: 4711
		private long long_0;

		// Token: 0x04001268 RID: 4712
		private decimal decimal_0;

		// Token: 0x04001269 RID: 4713
		private long? nullable_0;

		// Token: 0x0400126A RID: 4714
		private decimal? nullable_1;

		// Token: 0x0400126B RID: 4715
		private decimal? nullable_2;

		// Token: 0x0400126C RID: 4716
		private int? nullable_3;

		// Token: 0x0400126D RID: 4717
		private DateTime dateTime_0;

		// Token: 0x0400126E RID: 4718
		private DateTime? nullable_4;

		// Token: 0x0400126F RID: 4719
		private DateTime dateTime_1;

		// Token: 0x04001270 RID: 4720
		private DateTime? nullable_5;

		// Token: 0x04001271 RID: 4721
		private string string_0;
	}
}
