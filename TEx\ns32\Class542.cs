﻿using System;
using System.Net;
using ns19;
using SmartAssembly.SmartExceptionsCore;

namespace ns32
{
	// Token: 0x02000409 RID: 1033
	internal sealed class Class542
	{
		// Token: 0x06002809 RID: 10249 RVA: 0x0000F7E9 File Offset: 0x0000D9E9
		public void method_0(IWebProxy iwebProxy_1)
		{
			this.iwebProxy_0 = iwebProxy_1;
		}

		// Token: 0x0600280A RID: 10250 RVA: 0x00103380 File Offset: 0x00101580
		public void method_1(Delegate39 delegate39_0)
		{
			if (this.string_2 == null)
			{
				try
				{
					UploadReportLoginService uploadReportLoginService = new UploadReportLoginService();
					if (this.iwebProxy_0 != null)
					{
						uploadReportLoginService.Proxy = this.iwebProxy_0;
					}
					this.string_2 = uploadReportLoginService.GetServerURL(this.string_1);
					if (this.string_2.Length == 0)
					{
						throw new ApplicationException("Cannot connect to webservice");
					}
					if (this.string_2 == "ditto")
					{
						this.string_2 = Class542.string_0;
					}
				}
				catch (Exception ex)
				{
					delegate39_0("ERR 2001: " + ex.Message);
					return;
				}
			}
			delegate39_0(this.string_2.StartsWith("ERR") ? this.string_2 : "OK");
		}

		// Token: 0x0600280B RID: 10251 RVA: 0x00103448 File Offset: 0x00101648
		public void method_2(byte[] byte_0, string string_3, string string_4, string string_5, Delegate39 delegate39_0)
		{
			try
			{
				ReportingService reportingService = new ReportingService(this.string_2);
				if (this.iwebProxy_0 != null)
				{
					reportingService.Proxy = this.iwebProxy_0;
				}
				delegate39_0(reportingService.UploadReport2(this.string_1, byte_0, string_3, string_4, string_5));
			}
			catch (Exception ex)
			{
				delegate39_0("ERR 2002: " + ex.Message);
			}
		}

		// Token: 0x0600280C RID: 10252 RVA: 0x0000F7F2 File Offset: 0x0000D9F2
		public Class542(string string_3)
		{
			this.string_1 = string_3;
		}

		// Token: 0x040013D7 RID: 5079
		internal static readonly string string_0 = "http://sawebservice.red-gate.com/";

		// Token: 0x040013D8 RID: 5080
		private string string_1;

		// Token: 0x040013D9 RID: 5081
		private string string_2;

		// Token: 0x040013DA RID: 5082
		private IWebProxy iwebProxy_0;
	}
}
