﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.CompilerServices;
using Newtonsoft.Json;
using ns28;
using TEx;
using TEx.Comn;
using TEx.Util;

namespace ns4
{
	// Token: 0x0200003B RID: 59
	internal sealed class Class18
	{
		// Token: 0x17000071 RID: 113
		// (get) Token: 0x060001AB RID: 427 RVA: 0x000179C8 File Offset: 0x00015BC8
		// (set) Token: 0x060001AC RID: 428 RVA: 0x0000314E File Offset: 0x0000134E
		public string tscode { get; set; }

		// Token: 0x17000072 RID: 114
		// (get) Token: 0x060001AD RID: 429 RVA: 0x000179E0 File Offset: 0x00015BE0
		// (set) Token: 0x060001AE RID: 430 RVA: 0x00003159 File Offset: 0x00001359
		public List<Forecast> forecasts { get; set; }

		// Token: 0x17000073 RID: 115
		// (get) Token: 0x060001AF RID: 431 RVA: 0x000179F8 File Offset: 0x00015BF8
		// (set) Token: 0x060001B0 RID: 432 RVA: 0x00003164 File Offset: 0x00001364
		public List<Express> expresses { get; set; }

		// Token: 0x17000074 RID: 116
		// (get) Token: 0x060001B1 RID: 433 RVA: 0x00017A10 File Offset: 0x00015C10
		// (set) Token: 0x060001B2 RID: 434 RVA: 0x0000316F File Offset: 0x0000136F
		public DateTime FetchDate { get; set; }

		// Token: 0x060001B3 RID: 435 RVA: 0x00017A28 File Offset: 0x00015C28
		public SortedDictionary<DateTime, string> method_0()
		{
			SortedDictionary<DateTime, string> sortedDictionary = new SortedDictionary<DateTime, string>();
			if (this.forecasts != null)
			{
				foreach (Forecast forecast in this.forecasts)
				{
					string value = string.Concat(new string[]
					{
						"【业绩预告】",
						Environment.NewLine,
						forecast.summary,
						Environment.NewLine,
						"报告期：",
						forecast.end_date,
						Environment.NewLine,
						"公告日期：",
						forecast.ann_date
					});
					try
					{
						sortedDictionary[this.method_1(forecast.ann_date)] = value;
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
				foreach (Express express in this.expresses)
				{
					string text = string.Concat(new object[]
					{
						"【业绩快报】",
						Environment.NewLine,
						"每股收益：",
						express.diluted_eps,
						"元",
						Environment.NewLine
					});
					if (express.total_profit != null)
					{
						text = text + "利润总额：" + Utility.GetUnitAbbrNbString(express.total_profit.Value) + Environment.NewLine;
					}
					if (express.n_income != null)
					{
						text = text + "净利润：" + Utility.GetUnitAbbrNbString(express.n_income.Value) + Environment.NewLine;
					}
					text = string.Concat(new string[]
					{
						text,
						"报告期：",
						express.end_date,
						Environment.NewLine,
						"公告日期：",
						express.ann_date
					});
					try
					{
						sortedDictionary[this.method_1(express.ann_date)] = text;
					}
					catch (Exception exception_2)
					{
						Class182.smethod_0(exception_2);
					}
				}
			}
			return sortedDictionary;
		}

		// Token: 0x060001B4 RID: 436 RVA: 0x00017CA4 File Offset: 0x00015EA4
		private DateTime method_1(string string_1)
		{
			return new DateTime(Convert.ToInt32(string_1.Substring(0, 4)), Convert.ToInt32(string_1.Substring(4, 2)), Convert.ToInt32(string_1.Substring(6, 2)), 15, 0, 0);
		}

		// Token: 0x060001B5 RID: 437 RVA: 0x00017CE8 File Offset: 0x00015EE8
		public void method_2()
		{
			string filePath = Path.Combine(TApp.string_7, this.tscode + ".ifm");
			try
			{
				string content = JsonConvert.SerializeObject(this);
				Utility.SaveFile(filePath, content, null);
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x060001B6 RID: 438 RVA: 0x00017D3C File Offset: 0x00015F3C
		public bool method_3()
		{
			int num = 15;
			int month = DateTime.Now.Month;
			if (month > 2 && month < 5)
			{
				num = 5;
			}
			bool result;
			if (DateTime.Now - this.FetchDate > TimeSpan.FromDays((double)num))
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x040000AB RID: 171
		[CompilerGenerated]
		private string string_0;

		// Token: 0x040000AC RID: 172
		[CompilerGenerated]
		private List<Forecast> list_0;

		// Token: 0x040000AD RID: 173
		[CompilerGenerated]
		private List<Express> list_1;

		// Token: 0x040000AE RID: 174
		[CompilerGenerated]
		private DateTime dateTime_0;
	}
}
