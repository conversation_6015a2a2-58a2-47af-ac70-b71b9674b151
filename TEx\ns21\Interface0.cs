﻿using System;
using System.Collections.Generic;
using TEx;
using TEx.Comn;

namespace ns21
{
	// Token: 0x020000A8 RID: 168
	internal interface Interface0
	{
		// Token: 0x0600059F RID: 1439
		void imethod_0();

		// Token: 0x060005A0 RID: 1440
		void imethod_1();

		// Token: 0x060005A1 RID: 1441
		bool imethod_2(HisData hisData_0);

		// Token: 0x060005A2 RID: 1442
		int imethod_3(DateTime dateTime_0);

		// Token: 0x060005A3 RID: 1443
		void imethod_4(Interface0 interface0_0);

		// Token: 0x060005A4 RID: 1444
		bool imethod_5(PeriodType periodType_0, int? nullable_0);

		// Token: 0x060005A5 RID: 1445
		DateTime? imethod_6(DateTime dateTime_0);

		// Token: 0x060005A6 RID: 1446
		bool imethod_7(PeriodType periodType_0, int? nullable_0);

		// Token: 0x060005A7 RID: 1447
		Interface0 imethod_8(int int_0, PeriodType periodType_0, int? nullable_0);

		// Token: 0x060005A8 RID: 1448
		Interface0 imethod_9(int int_0, PeriodType periodType_0, int? nullable_0, DateTime? nullable_1, DateTime? nullable_2);

		// Token: 0x060005A9 RID: 1449
		object imethod_10();

		// Token: 0x060005AA RID: 1450
		bool imethod_11(Interface0 interface0_0);

		// Token: 0x17000111 RID: 273
		// (get) Token: 0x060005AB RID: 1451
		// (set) Token: 0x060005AC RID: 1452
		int SymbId { get; set; }

		// Token: 0x17000112 RID: 274
		// (get) Token: 0x060005AD RID: 1453
		StkSymbol Symbol { get; }

		// Token: 0x17000113 RID: 275
		// (get) Token: 0x060005AE RID: 1454
		// (set) Token: 0x060005AF RID: 1455
		SortedList<DateTime, HisData> PeriodHisDataList { get; set; }

		// Token: 0x17000114 RID: 276
		// (get) Token: 0x060005B0 RID: 1456
		// (set) Token: 0x060005B1 RID: 1457
		SortedList<DateTime, HisData> HisDataList { get; set; }

		// Token: 0x17000115 RID: 277
		// (get) Token: 0x060005B2 RID: 1458
		// (set) Token: 0x060005B3 RID: 1459
		PeriodType PeriodType { get; set; }

		// Token: 0x17000116 RID: 278
		// (get) Token: 0x060005B4 RID: 1460
		// (set) Token: 0x060005B5 RID: 1461
		int? PeriodUnits { get; set; }

		// Token: 0x17000117 RID: 279
		// (get) Token: 0x060005B6 RID: 1462
		// (set) Token: 0x060005B7 RID: 1463
		double[] CloseDataArray { get; set; }

		// Token: 0x17000118 RID: 280
		// (get) Token: 0x060005B8 RID: 1464
		// (set) Token: 0x060005B9 RID: 1465
		double[] HighDataArray { get; set; }

		// Token: 0x17000119 RID: 281
		// (get) Token: 0x060005BA RID: 1466
		// (set) Token: 0x060005BB RID: 1467
		double[] LowDataArray { get; set; }

		// Token: 0x1700011A RID: 282
		// (get) Token: 0x060005BC RID: 1468
		// (set) Token: 0x060005BD RID: 1469
		double[] VolDataArray { get; set; }

		// Token: 0x1700011B RID: 283
		// (get) Token: 0x060005BE RID: 1470
		// (set) Token: 0x060005BF RID: 1471
		int? NumberOfStickItemsPerDay { get; set; }

		// Token: 0x1700011C RID: 284
		// (get) Token: 0x060005C0 RID: 1472
		// (set) Token: 0x060005C1 RID: 1473
		string ChtCtrl_KLineTag { get; set; }

		// Token: 0x1700011D RID: 285
		// (get) Token: 0x060005C2 RID: 1474
		bool IsPeriodLong { get; }

		// Token: 0x1700011E RID: 286
		// (get) Token: 0x060005C3 RID: 1475
		bool IsPeriod1m { get; }

		// Token: 0x1700011F RID: 287
		// (get) Token: 0x060005C4 RID: 1476
		string PeriodDesc { get; }

		// Token: 0x17000120 RID: 288
		// (get) Token: 0x060005C5 RID: 1477
		int Count { get; }

		// Token: 0x17000121 RID: 289
		// (get) Token: 0x060005C6 RID: 1478
		int FirstYear { get; }

		// Token: 0x17000122 RID: 290
		// (get) Token: 0x060005C7 RID: 1479
		int LastYear { get; }

		// Token: 0x17000123 RID: 291
		// (get) Token: 0x060005C8 RID: 1480
		// (set) Token: 0x060005C9 RID: 1481
		bool IsStockPriceRestored { get; set; }

		// Token: 0x17000124 RID: 292
		// (get) Token: 0x060005CA RID: 1482
		bool IsNMinsPeriod { get; }
	}
}
