﻿using System;
using System.Windows.Forms;
using ns28;
using ns7;
using TEx;

namespace ns2
{
	// Token: 0x02000299 RID: 665
	internal sealed class Class52 : Class51
	{
		// Token: 0x06001D8C RID: 7564 RVA: 0x0000C513 File Offset: 0x0000A713
		public Class52(Panel panel_1, int int_4, int int_5, ChtCtrl_KLine chtCtrl_KLine_0, bool bool_3) : base(panel_1, int_4, int_5, chtCtrl_KLine_0, bool_3)
		{
			base.Image = Class372.rewind;
		}

		// Token: 0x06001D8D RID: 7565 RVA: 0x0000C52F File Offset: 0x0000A72F
		public Class52(Panel panel_1, int int_4, int int_5, ChtCtrl_KLine chtCtrl_KLine_0) : this(panel_1, int_4, int_5, chtCtrl_KLine_0, true)
		{
		}

		// Token: 0x06001D8E RID: 7566 RVA: 0x0000C53D File Offset: 0x0000A73D
		protected override void Class48_MouseEnter(object sender, EventArgs e)
		{
			base.Class48_MouseEnter(sender, e);
			base.Image = Class372.rewind_red;
		}

		// Token: 0x06001D8F RID: 7567 RVA: 0x0000C554 File Offset: 0x0000A754
		protected override void Class48_MouseLeave(object sender, EventArgs e)
		{
			base.Class48_MouseLeave(sender, e);
			base.Image = Class372.rewind;
		}

		// Token: 0x06001D90 RID: 7568 RVA: 0x000C92E0 File Offset: 0x000C74E0
		protected override void Class48_Click(object sender, EventArgs e)
		{
			base.Class48_Click(sender, e);
			ChtCtrl_KLine chtCtrl_KLine = (ChtCtrl_KLine)base.ChtCtrl;
			if (chtCtrl_KLine.IsInCrossReviewMode)
			{
				chtCtrl_KLine.IsInCrossReviewMode = false;
			}
			Base.UI.smethod_120();
		}
	}
}
