﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using TEx.Comn;

namespace TEx
{
	// Token: 0x020001FB RID: 507
	[Serializable]
	internal sealed class SrvParams
	{
		// Token: 0x0600143D RID: 5181 RVA: 0x000081B5 File Offset: 0x000063B5
		public SrvParams()
		{
			this._RefreshHdDatUrlTimer = new Timer(new TimerCallback(this.method_0), "", 300000, 300000);
		}

		// Token: 0x0600143E RID: 5182 RVA: 0x000081E5 File Offset: 0x000063E5
		public void method_0(object object_0)
		{
			if (this.EnableRefreshHdDatUrlTimer)
			{
				this.method_1();
			}
		}

		// Token: 0x0600143F RID: 5183 RVA: 0x00085CD0 File Offset: 0x00083ED0
		public void method_1()
		{
			string newValue = TApp.FULLHOST;
			if (!string.IsNullOrEmpty(TApp.string_1))
			{
				newValue = "http://" + TApp.string_1;
			}
			else if (!string.IsNullOrEmpty(this.StaticSrvAddr))
			{
				if (ConnMgr.smethod_10(this.StaticSrvAddr, default(TimeSpan)))
				{
					newValue = "http://" + this.StaticSrvAddr;
					TApp.StaticSrvAvailable = true;
				}
				else
				{
					TApp.StaticSrvAvailable = false;
				}
			}
			string hdDatFileBaseUrl = this.HdDatFileBaseUrlRawString.Replace(TApp.string_13, newValue);
			this.HdDatFileBaseUrl = hdDatFileBaseUrl;
		}

		// Token: 0x1700032D RID: 813
		// (get) Token: 0x06001440 RID: 5184 RVA: 0x00085D60 File Offset: 0x00083F60
		// (set) Token: 0x06001441 RID: 5185 RVA: 0x000081F7 File Offset: 0x000063F7
		public List<ExchgHouse> ExchgHsList
		{
			get
			{
				return this._ExchgHsList;
			}
			set
			{
				this._ExchgHsList = value;
			}
		}

		// Token: 0x1700032E RID: 814
		// (get) Token: 0x06001442 RID: 5186 RVA: 0x00085D78 File Offset: 0x00083F78
		// (set) Token: 0x06001443 RID: 5187 RVA: 0x00008202 File Offset: 0x00006402
		public List<TradingSymbol> MstSymblList
		{
			get
			{
				return this._MstSymblList;
			}
			set
			{
				this._MstSymblList = value;
			}
		}

		// Token: 0x1700032F RID: 815
		// (get) Token: 0x06001444 RID: 5188 RVA: 0x00085D90 File Offset: 0x00083F90
		// (set) Token: 0x06001445 RID: 5189 RVA: 0x0000820D File Offset: 0x0000640D
		public List<UsrStkMeta> UsrStkMetaList
		{
			get
			{
				return this._UsrStkMetaList;
			}
			set
			{
				this._UsrStkMetaList = value;
			}
		}

		// Token: 0x17000330 RID: 816
		// (get) Token: 0x06001446 RID: 5190 RVA: 0x00085DA8 File Offset: 0x00083FA8
		// (set) Token: 0x06001447 RID: 5191 RVA: 0x00008218 File Offset: 0x00006418
		public AppUpdInfo AppUpdInfo
		{
			get
			{
				return this._AppUpdInfo;
			}
			set
			{
				this._AppUpdInfo = value;
			}
		}

		// Token: 0x17000331 RID: 817
		// (get) Token: 0x06001448 RID: 5192 RVA: 0x00085DC0 File Offset: 0x00083FC0
		// (set) Token: 0x06001449 RID: 5193 RVA: 0x00008223 File Offset: 0x00006423
		public LogonNoticeInfo LogonNoticeInfo
		{
			get
			{
				return this._LogonNoticeInfo;
			}
			set
			{
				this._LogonNoticeInfo = value;
			}
		}

		// Token: 0x17000332 RID: 818
		// (get) Token: 0x0600144A RID: 5194 RVA: 0x00085DD8 File Offset: 0x00083FD8
		// (set) Token: 0x0600144B RID: 5195 RVA: 0x0000822E File Offset: 0x0000642E
		public string InfoHTML
		{
			get
			{
				return this._InfoHTML;
			}
			set
			{
				this._InfoHTML = value;
			}
		}

		// Token: 0x17000333 RID: 819
		// (get) Token: 0x0600144C RID: 5196 RVA: 0x00085DF0 File Offset: 0x00083FF0
		// (set) Token: 0x0600144D RID: 5197 RVA: 0x00008239 File Offset: 0x00006439
		public TExPackage? TExPkg
		{
			get
			{
				return this._TExPkg;
			}
			set
			{
				this._TExPkg = value;
			}
		}

		// Token: 0x17000334 RID: 820
		// (get) Token: 0x0600144E RID: 5198 RVA: 0x00085E08 File Offset: 0x00084008
		// (set) Token: 0x0600144F RID: 5199 RVA: 0x00008244 File Offset: 0x00006444
		public bool IsFirstLogin
		{
			get
			{
				return this._IsFirstLogin;
			}
			set
			{
				this._IsFirstLogin = value;
			}
		}

		// Token: 0x17000335 RID: 821
		// (get) Token: 0x06001450 RID: 5200 RVA: 0x00085E20 File Offset: 0x00084020
		// (set) Token: 0x06001451 RID: 5201 RVA: 0x0000824F File Offset: 0x0000644F
		public bool IsNewMachine
		{
			get
			{
				return this._IsNewMachine;
			}
			set
			{
				this._IsNewMachine = value;
			}
		}

		// Token: 0x17000336 RID: 822
		// (get) Token: 0x06001452 RID: 5202 RVA: 0x00085E38 File Offset: 0x00084038
		// (set) Token: 0x06001453 RID: 5203 RVA: 0x0000825A File Offset: 0x0000645A
		public string HdDatFileBaseUrlRawString
		{
			get
			{
				return this._HdDatFileBaseUrlRawString;
			}
			set
			{
				this._HdDatFileBaseUrlRawString = value;
			}
		}

		// Token: 0x17000337 RID: 823
		// (get) Token: 0x06001454 RID: 5204 RVA: 0x00085E50 File Offset: 0x00084050
		// (set) Token: 0x06001455 RID: 5205 RVA: 0x00008265 File Offset: 0x00006465
		public string HdDatFileBaseUrl
		{
			get
			{
				return this._HdDatFileBaseUrl;
			}
			set
			{
				this._HdDatFileBaseUrl = value;
			}
		}

		// Token: 0x17000338 RID: 824
		// (get) Token: 0x06001456 RID: 5206 RVA: 0x00085E68 File Offset: 0x00084068
		// (set) Token: 0x06001457 RID: 5207 RVA: 0x00008270 File Offset: 0x00006470
		public List<ExchgOBT> ExchgOBTList
		{
			get
			{
				return this._ExchgOBTList;
			}
			set
			{
				this._ExchgOBTList = value;
			}
		}

		// Token: 0x17000339 RID: 825
		// (get) Token: 0x06001458 RID: 5208 RVA: 0x00085E80 File Offset: 0x00084080
		// (set) Token: 0x06001459 RID: 5209 RVA: 0x0000827B File Offset: 0x0000647B
		public List<SymbNtTrDate> SymbNtTrDateList
		{
			get
			{
				return this._SymbNtTrDateList;
			}
			set
			{
				this._SymbNtTrDateList = value;
			}
		}

		// Token: 0x1700033A RID: 826
		// (get) Token: 0x0600145A RID: 5210 RVA: 0x00085E98 File Offset: 0x00084098
		// (set) Token: 0x0600145B RID: 5211 RVA: 0x00008286 File Offset: 0x00006486
		public Dictionary<int, StkSymbol> StkSymbols
		{
			get
			{
				return this._StkSymbols;
			}
			set
			{
				this._StkSymbols = value;
			}
		}

		// Token: 0x1700033B RID: 827
		// (get) Token: 0x0600145C RID: 5212 RVA: 0x00085EB0 File Offset: 0x000840B0
		// (set) Token: 0x0600145D RID: 5213 RVA: 0x00008291 File Offset: 0x00006491
		public Dictionary<int, StkSymbol> UsrStkSymbols
		{
			get
			{
				return this._UsrStkSymbols;
			}
			set
			{
				this._UsrStkSymbols = value;
			}
		}

		// Token: 0x1700033C RID: 828
		// (get) Token: 0x0600145E RID: 5214 RVA: 0x00085EC8 File Offset: 0x000840C8
		// (set) Token: 0x0600145F RID: 5215 RVA: 0x0000829C File Offset: 0x0000649C
		public List<HDFileInfo> HFileInfoList
		{
			get
			{
				return this._HFileInfoList;
			}
			set
			{
				this._HFileInfoList = value;
			}
		}

		// Token: 0x1700033D RID: 829
		// (get) Token: 0x06001460 RID: 5216 RVA: 0x00085EE0 File Offset: 0x000840E0
		public List<StkBegEndDate> StkBegEndDtLst
		{
			get
			{
				if (this._StkBegEndDtLst == null)
				{
					IEnumerable<StkBegEndDate> source = this._HFileInfoList.GroupBy(new Func<HDFileInfo, int>(SrvParams.<>c.<>9.method_0)).Where(new Func<IGrouping<int, HDFileInfo>, bool>(SrvParams.<>c.<>9.method_1)).Select(new Func<IGrouping<int, HDFileInfo>, StkBegEndDate>(SrvParams.<>c.<>9.method_3));
					this._StkBegEndDtLst = source.ToList<StkBegEndDate>();
				}
				return this._StkBegEndDtLst;
			}
		}

		// Token: 0x1700033E RID: 830
		// (get) Token: 0x06001461 RID: 5217 RVA: 0x00085F80 File Offset: 0x00084180
		// (set) Token: 0x06001462 RID: 5218 RVA: 0x000082A7 File Offset: 0x000064A7
		public int MinHDExpPeriodUnits
		{
			get
			{
				return this._MinHDExpPeriodUnits;
			}
			set
			{
				this._MinHDExpPeriodUnits = value;
			}
		}

		// Token: 0x1700033F RID: 831
		// (get) Token: 0x06001463 RID: 5219 RVA: 0x00085F98 File Offset: 0x00084198
		// (set) Token: 0x06001464 RID: 5220 RVA: 0x000082B2 File Offset: 0x000064B2
		public List<StSplit> StSplitList
		{
			get
			{
				return this._StSplitList;
			}
			set
			{
				this._StSplitList = value;
			}
		}

		// Token: 0x17000340 RID: 832
		// (get) Token: 0x06001465 RID: 5221 RVA: 0x00085FB0 File Offset: 0x000841B0
		// (set) Token: 0x06001466 RID: 5222 RVA: 0x000082BD File Offset: 0x000064BD
		public string StaticSrvAddr
		{
			get
			{
				return this._StaticSrvAddr;
			}
			set
			{
				this._StaticSrvAddr = value;
			}
		}

		// Token: 0x17000341 RID: 833
		// (get) Token: 0x06001467 RID: 5223 RVA: 0x00085FC8 File Offset: 0x000841C8
		// (set) Token: 0x06001468 RID: 5224 RVA: 0x000082C8 File Offset: 0x000064C8
		public bool EnableRefreshHdDatUrlTimer { get; set; }

		// Token: 0x17000342 RID: 834
		// (get) Token: 0x06001469 RID: 5225 RVA: 0x00085FE0 File Offset: 0x000841E0
		// (set) Token: 0x0600146A RID: 5226 RVA: 0x000082D3 File Offset: 0x000064D3
		public bool GetHDFileInfoFromSrvApi { get; set; }

		// Token: 0x04000A7C RID: 2684
		private List<ExchgHouse> _ExchgHsList;

		// Token: 0x04000A7D RID: 2685
		private List<TradingSymbol> _MstSymblList;

		// Token: 0x04000A7E RID: 2686
		private List<UsrStkMeta> _UsrStkMetaList;

		// Token: 0x04000A7F RID: 2687
		private AppUpdInfo _AppUpdInfo;

		// Token: 0x04000A80 RID: 2688
		private LogonNoticeInfo _LogonNoticeInfo;

		// Token: 0x04000A81 RID: 2689
		private string _InfoHTML;

		// Token: 0x04000A82 RID: 2690
		private TExPackage? _TExPkg;

		// Token: 0x04000A83 RID: 2691
		private bool _IsNewMachine;

		// Token: 0x04000A84 RID: 2692
		private bool _IsFirstLogin;

		// Token: 0x04000A85 RID: 2693
		private string _HdDatFileBaseUrl;

		// Token: 0x04000A86 RID: 2694
		private string _HdDatFileBaseUrlRawString;

		// Token: 0x04000A87 RID: 2695
		private List<ExchgOBT> _ExchgOBTList;

		// Token: 0x04000A88 RID: 2696
		private List<SymbNtTrDate> _SymbNtTrDateList;

		// Token: 0x04000A89 RID: 2697
		private List<HDFileInfo> _HFileInfoList;

		// Token: 0x04000A8A RID: 2698
		private List<StkBegEndDate> _StkBegEndDtLst;

		// Token: 0x04000A8B RID: 2699
		private int _MinHDExpPeriodUnits;

		// Token: 0x04000A8C RID: 2700
		private List<StSplit> _StSplitList;

		// Token: 0x04000A8D RID: 2701
		private string _StaticSrvAddr;

		// Token: 0x04000A8E RID: 2702
		private Timer _RefreshHdDatUrlTimer;

		// Token: 0x04000A8F RID: 2703
		private Dictionary<int, StkSymbol> _StkSymbols;

		// Token: 0x04000A90 RID: 2704
		private Dictionary<int, StkSymbol> _UsrStkSymbols;
	}
}
