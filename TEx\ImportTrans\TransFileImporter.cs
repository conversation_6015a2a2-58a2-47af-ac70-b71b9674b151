﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using NPOI.POIFS.FileSystem;
using ns10;
using ns12;
using ns13;
using ns2;
using ns20;
using ns26;
using ns28;
using ns4;
using TEx.Trading;
using TEx.Util;

namespace TEx.ImportTrans
{
	// Token: 0x02000348 RID: 840
	public sealed class TransFileImporter
	{
		// Token: 0x17000618 RID: 1560
		// (get) Token: 0x0600235C RID: 9052 RVA: 0x000EEBCC File Offset: 0x000ECDCC
		public Class448 BackData
		{
			get
			{
				return this.class448_0;
			}
		}

		// Token: 0x17000619 RID: 1561
		// (get) Token: 0x0600235D RID: 9053 RVA: 0x000EEBE4 File Offset: 0x000ECDE4
		// (set) Token: 0x0600235E RID: 9054 RVA: 0x0000DE91 File Offset: 0x0000C091
		public List<Class479> DefaultVar
		{
			get
			{
				return this.list_1;
			}
			set
			{
				this.list_1 = value;
			}
		}

		// Token: 0x0600235F RID: 9055 RVA: 0x000EEBFC File Offset: 0x000ECDFC
		public TransFileImporter(Interface3 msg, DateTime date)
		{
			this.msg = msg;
			this.BeginTime = date;
			this.list_0 = new List<string>();
			this.class448_0 = new Class448(Class448.string_2);
			this.list_1 = new List<Class479>();
			this.int_0 = new int[]
			{
				-1,
				-1,
				-1,
				-1,
				-1,
				-1,
				-1,
				-1,
				-1,
				-1
			};
		}

		// Token: 0x06002360 RID: 9056 RVA: 0x000EEC60 File Offset: 0x000ECE60
		private char method_0(string string_1)
		{
			int num = 0;
			int num2 = -1;
			TransFileImporter.Class452 @class = new TransFileImporter.Class452();
			@class.int_0 = 0;
			while (@class.int_0 < TransFileImporter.char_1.Length)
			{
				int num3 = string_1.ToCharArray().Where(new Func<char, bool>(@class.method_0)).Count<char>();
				if (num < num3)
				{
					num = num3;
					num2 = @class.int_0;
				}
				int num4 = @class.int_0;
				@class.int_0 = num4 + 1;
			}
			char result;
			if (num2 == -1)
			{
				result = ' ';
			}
			else
			{
				result = TransFileImporter.char_1[num2];
			}
			return result;
		}

		// Token: 0x06002361 RID: 9057 RVA: 0x000EECE4 File Offset: 0x000ECEE4
		private void method_1(string string_1, char char_2)
		{
			string[] string_2 = string_1.Trim().Split(new char[]
			{
				char_2
			}).Where(new Func<string, bool>(TransFileImporter.<>c.<>9.method_0)).ToArray<string>();
			for (int i = 0; i < TransFileImporter.string_0.Length; i++)
			{
				string string_3 = TransFileImporter.string_0[i];
				int num = this.method_2(string_2, string_3);
				if (num >= 0)
				{
					this.int_0[i] = num;
				}
			}
		}

		// Token: 0x06002362 RID: 9058 RVA: 0x000EED64 File Offset: 0x000ECF64
		private int method_2(string[] string_1, string string_2)
		{
			for (int i = 0; i < string_1.Length; i++)
			{
				string value = string_1[i].Replace("/", string.Empty).Replace(" ", string.Empty).Replace("\0", string.Empty);
				if (string_2.Contains(value))
				{
					return i;
				}
			}
			return -1;
		}

		// Token: 0x06002363 RID: 9059 RVA: 0x000EEDC4 File Offset: 0x000ECFC4
		private bool method_3(string string_1)
		{
			return string_1.Contains("盈");
		}

		// Token: 0x06002364 RID: 9060 RVA: 0x000EEDE0 File Offset: 0x000ECFE0
		private void method_4(int int_1, string string_1)
		{
			Class479 @class = new Class479();
			@class.int_0 = int_1;
			@class.string_0 = string_1;
			this.list_1.Add(@class);
		}

		// Token: 0x06002365 RID: 9061 RVA: 0x000EEE10 File Offset: 0x000ED010
		private string method_5(string string_1)
		{
			this.class448_0.Data.Clear();
			this.list_1.Clear();
			FileInfo fileInfo = new FileInfo(string_1);
			string result;
			if (!fileInfo.Exists)
			{
				result = "未找到指定文件！";
			}
			else if (Utility.IsFileLocked(fileInfo))
			{
				result = "文件无法打开，请检查文件当前是否被其他程序占用。";
			}
			else
			{
				string[] source;
				string text;
				if (Path.GetExtension(string_1).Contains("xls"))
				{
					Class450 @class = new Class450(string_1);
					try
					{
						source = @class.LinesInCSVFormat;
					}
					catch (Exception ex)
					{
						@class.method_4();
						if (ex is NotOLE2FileException)
						{
							try
							{
								source = File.ReadAllLines(string_1, Encoding.Default);
							}
							catch
							{
								text = "未识别的文件格式！";
								goto IL_6D7;
							}
						}
						else
						{
							if (ex is Exception0)
							{
								text = ex.Message;
								goto IL_6D7;
							}
							text = "未识别的文件格式！";
							goto IL_6D7;
						}
					}
					@class.method_4();
				}
				else
				{
					try
					{
						source = File.ReadAllLines(string_1, Encoding.Default);
					}
					catch
					{
						text = "未识别的文件格式！";
						goto IL_6D7;
					}
				}
				string[] array = source.Where(new Func<string, bool>(TransFileImporter.<>c.<>9.method_1)).ToArray<string>();
				if (array.Count<string>() < 2)
				{
					return "文件应包含字段名头记录，以及至少一条交易记录。";
				}
				int num = 0;
				string text2 = array[0];
				if (text2.Contains("------"))
				{
					num = 1;
					text2 = array[1];
				}
				text2 = this.method_8(text2);
				char c = this.method_0(text2);
				this.method_1(text2, c);
				bool flag = this.method_3(text2);
				int i = num + 1;
				while (i < array.Length)
				{
					TransData transData = new TransData();
					string[] array2 = this.method_8(this.method_6(array[i])).Split(new char[]
					{
						c
					}).Where(new Func<string, bool>(TransFileImporter.<>c.<>9.method_2)).ToArray<string>();
					if (!flag)
					{
						goto IL_6BB;
					}
					if (array2.Length != 2)
					{
						if (this.int_0[3] != -1 && this.method_6(array2[this.int_0[3]]).Contains("平"))
						{
							try
							{
								if (this.int_0[9] != -1)
								{
									transData.Profit = new decimal?(Convert.ToDecimal(array2[this.int_0[9]]));
								}
								goto IL_6BB;
							}
							catch
							{
								transData.Profit = null;
								goto IL_6BB;
							}
							goto IL_24E;
						}
						goto IL_6BB;
					}
					IL_6B0:
					i++;
					continue;
					IL_32A:
					if (this.int_0[0] != -1)
					{
						try
						{
							transData.RecordID = Convert.ToString(this.method_6(array2[this.int_0[0]]));
							goto IL_367;
						}
						catch
						{
							transData.RecordID = null;
							goto IL_367;
						}
						goto IL_35F;
					}
					goto IL_35F;
					IL_367:
					if (this.int_0[1] != -1)
					{
						try
						{
							transData.Contract = this.method_6(array2[this.int_0[1]]);
							goto IL_39F;
						}
						catch
						{
							transData.Contract = null;
							goto IL_39F;
						}
						goto IL_397;
					}
					goto IL_397;
					IL_39F:
					if (this.int_0[2] != -1)
					{
						try
						{
							string text3 = this.method_6(array2[this.int_0[2]]).ToUpper();
							if (!text3.Contains("买") && !text3.Contains("BUY") && !text3.Contains("LONG"))
							{
								if (!text3.Contains("卖") && !text3.Contains("SELL") && !text3.Contains("SHORT"))
								{
									this.method_7();
									text = string.Empty;
									goto IL_6D7;
								}
								text3 = "卖";
							}
							else
							{
								text3 = "买";
							}
							transData.BuyOrSell = text3;
							goto IL_459;
						}
						catch
						{
							transData.BuyOrSell = null;
							goto IL_459;
						}
						goto IL_451;
					}
					goto IL_451;
					IL_459:
					if (this.int_0[3] != -1)
					{
						string text4 = this.method_6(array2[this.int_0[3]]);
						if (!text4.Contains("开") && !text4.Contains("平") && !text4.Contains("买入") && !text4.Contains("卖出"))
						{
							this.method_7();
						}
						try
						{
							transData.OpenOrClose = TransData.smethod_0(text4);
							goto IL_52F;
						}
						catch
						{
							transData.OpenOrClose = null;
							goto IL_52F;
						}
						goto IL_4D1;
					}
					goto IL_4D1;
					IL_52F:
					if (this.int_0[4] != -1)
					{
						try
						{
							transData.Price = new double?(Convert.ToDouble(this.method_6(array2[this.int_0[4]])));
							goto IL_583;
						}
						catch
						{
							transData.Price = null;
							goto IL_583;
						}
						goto IL_572;
					}
					goto IL_572;
					IL_583:
					if (this.int_0[5] != -1)
					{
						try
						{
							transData.Count = new int?(Convert.ToInt32(this.method_6(array2[this.int_0[5]])));
							goto IL_5D7;
						}
						catch
						{
							transData.Count = null;
							goto IL_5D7;
						}
						goto IL_5C6;
					}
					goto IL_5C6;
					IL_5D7:
					if (this.int_0[6] != -1)
					{
						try
						{
							transData.Poundage = new double?(Convert.ToDouble(this.method_6(array2[this.int_0[6]])));
							goto IL_62B;
						}
						catch
						{
							transData.Poundage = null;
							goto IL_62B;
						}
						goto IL_61A;
					}
					goto IL_61A;
					IL_62B:
					if (this.int_0[8] != -1)
					{
						string text5 = this.method_6(array2[this.int_0[8]]);
						try
						{
							DateTime value = Convert.ToDateTime(text5);
							transData.Time = new DateTime?(value);
							if (Utility.IfDTStrContainsFullDateTime(text5))
							{
								transData.Day = new DateTime?(value);
							}
							goto IL_69E;
						}
						catch
						{
							transData.Time = null;
							goto IL_69E;
						}
						goto IL_68D;
					}
					goto IL_68D;
					IL_69E:
					this.class448_0.Data.Add(transData);
					goto IL_6B0;
					IL_68D:
					transData.Time = null;
					goto IL_69E;
					IL_61A:
					transData.Poundage = null;
					goto IL_62B;
					IL_5C6:
					transData.Count = null;
					goto IL_5D7;
					IL_572:
					transData.Price = null;
					goto IL_583;
					IL_4D1:
					if (this.int_0[2] == -1)
					{
						transData.OpenOrClose = null;
						goto IL_52F;
					}
					string text6 = this.method_6(array2[this.int_0[2]]);
					if (text6.Contains("买入"))
					{
						transData.OpenOrClose = "开";
						goto IL_52F;
					}
					if (text6.Contains("卖出"))
					{
						transData.OpenOrClose = "平";
						goto IL_52F;
					}
					goto IL_52F;
					IL_451:
					transData.BuyOrSell = null;
					goto IL_459;
					IL_397:
					transData.Contract = null;
					goto IL_39F;
					IL_35F:
					transData.RecordID = null;
					goto IL_367;
					IL_24E:
					string text7 = this.method_6(array2[this.int_0[7]]);
					try
					{
						DateTime? time = null;
						if (!text7.Contains("/") && !text7.Contains("-"))
						{
							if (text7.Count<char>() == 8)
							{
								time = new DateTime?(DateTime.ParseExact(text7, "yyyyMMdd", CultureInfo.InvariantCulture));
							}
						}
						else
						{
							time = new DateTime?(Convert.ToDateTime(text7));
						}
						if (time != null)
						{
							transData.Day = new DateTime?(time.Value);
						}
						if (Utility.IfDTStrContainsFullDateTime(text7))
						{
							transData.Time = time;
						}
						goto IL_32A;
					}
					catch
					{
						transData.Day = new DateTime?(DateTime.Now);
						this.method_4(i - 1, "Day");
						goto IL_32A;
					}
					IL_30A:
					transData.Day = new DateTime?(DateTime.Now);
					this.method_4(i - 1, "Day");
					goto IL_32A;
					IL_6BB:
					if (this.int_0[7] != -1)
					{
						goto IL_24E;
					}
					goto IL_30A;
				}
				return "ok";
				IL_6D7:
				result = text;
			}
			return result;
		}

		// Token: 0x06002366 RID: 9062 RVA: 0x000EF59C File Offset: 0x000ED79C
		private string method_6(string string_1)
		{
			return string_1.Replace("\0", string.Empty).Trim();
		}

		// Token: 0x06002367 RID: 9063 RVA: 0x0000DE9C File Offset: 0x0000C09C
		private void method_7()
		{
			throw new Exception("解析错误！请检查源文件格式和内容是否符合要求。");
		}

		// Token: 0x06002368 RID: 9064 RVA: 0x000EF5C4 File Offset: 0x000ED7C4
		private string method_8(string string_1)
		{
			if (string_1.Contains("="))
			{
				string_1 = string_1.Replace("=", string.Empty);
			}
			if (string_1.Contains("\""))
			{
				string_1 = string_1.Replace("\"", " ");
			}
			if (string_1.Contains("'"))
			{
				string_1 = string_1.Replace("'", " ");
			}
			return string_1;
		}

		// Token: 0x1700061A RID: 1562
		// (get) Token: 0x06002369 RID: 9065 RVA: 0x000EF634 File Offset: 0x000ED834
		// (set) Token: 0x0600236A RID: 9066 RVA: 0x0000DEA8 File Offset: 0x0000C0A8
		public DateTime BeginTime { get; private set; }

		// Token: 0x0600236B RID: 9067 RVA: 0x000EF64C File Offset: 0x000ED84C
		public string method_9(string string_1)
		{
			return this.method_5(string_1);
		}

		// Token: 0x0600236C RID: 9068 RVA: 0x000EEBCC File Offset: 0x000ECDCC
		public Class448 method_10()
		{
			return this.class448_0;
		}

		// Token: 0x0600236D RID: 9069 RVA: 0x000EF664 File Offset: 0x000ED864
		public bool method_11(out string string_1)
		{
			for (int i = 0; i < this.class448_0.Data.Count; i++)
			{
				string arg;
				if (!TransFileImporter.smethod_13(this.class448_0.Data[i], out arg))
				{
					string_1 = string.Format("第{0}行，{1}字段有误，请修改。", i + 1, arg);
					return false;
				}
			}
			string_1 = "";
			return true;
		}

		// Token: 0x0600236E RID: 9070 RVA: 0x000EF6CC File Offset: 0x000ED8CC
		public static List<Transaction> smethod_0(Class448 class448_1, string string_1)
		{
			TransFileImporter.Class453 @class = new TransFileImporter.Class453();
			@class.string_0 = string_1;
			int id = Base.Acct.Accounts.Single(new Func<Account, bool>(@class.method_0)).ID;
			return TransFileImporter.smethod_1(class448_1, id);
		}

		// Token: 0x0600236F RID: 9071 RVA: 0x000EF710 File Offset: 0x000ED910
		public static List<Transaction> smethod_1(Class448 class448_1, int int_1)
		{
			List<Transaction> list = Base.Trading.smethod_124(int_1);
			TransFileImporter.smethod_3(list, class448_1, int_1);
			List<Transaction> result;
			if (list.Any<Transaction>())
			{
				Base.Trading.Transactions = list;
				Base.Trading.smethod_128(int_1, list);
				result = list;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06002370 RID: 9072 RVA: 0x000EF74C File Offset: 0x000ED94C
		public static List<Transaction> smethod_2(List<Transaction> list_2, int int_1)
		{
			List<Transaction> list = Base.Trading.smethod_124(int_1);
			new List<Transaction>();
			List<Transaction> result;
			if (TransFileImporter.smethod_4(list, list_2, int_1))
			{
				Base.Trading.smethod_128(int_1, list);
				Base.Trading.smethod_17();
				result = list;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06002371 RID: 9073 RVA: 0x000EF788 File Offset: 0x000ED988
		public static bool smethod_3(List<Transaction> list_2, Class448 class448_1, int int_1)
		{
			int value = 0;
			if (list_2.Any<Transaction>())
			{
				value = list_2.Max(new Func<Transaction, int>(TransFileImporter.<>c.<>9.method_3)) + 1;
			}
			List<Transaction> list = TransFileImporter.smethod_6(class448_1, new int?(int_1), new int?(value));
			List<Transaction> list2 = new List<Transaction>();
			using (List<Transaction>.Enumerator enumerator = list.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					TransFileImporter.Class454 @class = new TransFileImporter.Class454();
					@class.transaction_0 = enumerator.Current;
					if (!(class448_1.From == TransData.string_0) || !list_2.Where(new Func<Transaction, bool>(@class.method_0)).Any<Transaction>())
					{
						list2.Add(@class.transaction_0);
					}
				}
			}
			bool result;
			if (list2.Any<Transaction>())
			{
				list_2.AddRange(list2);
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06002372 RID: 9074 RVA: 0x000EF878 File Offset: 0x000EDA78
		public static bool smethod_4(List<Transaction> list_2, List<Transaction> list_3, int int_1)
		{
			int num = 0;
			if (list_2.Any<Transaction>())
			{
				num = list_2.Max(new Func<Transaction, int>(TransFileImporter.<>c.<>9.method_4)) + 1;
			}
			List<Transaction> list = new List<Transaction>();
			using (List<Transaction>.Enumerator enumerator = list_3.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					TransFileImporter.Class455 @class = new TransFileImporter.Class455();
					@class.transaction_0 = enumerator.Current;
					if (!list_2.Where(new Func<Transaction, bool>(@class.method_0)).Any<Transaction>())
					{
						@class.transaction_0.AcctID = int_1;
						@class.transaction_0.ID += num;
						if (@class.transaction_0.ClosedTransID != null)
						{
							@class.transaction_0.ClosedTransID += num;
						}
						list.Add(@class.transaction_0);
					}
				}
			}
			bool result;
			if (list.Any<Transaction>())
			{
				list_2.AddRange(list);
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06002373 RID: 9075 RVA: 0x000EF9B8 File Offset: 0x000EDBB8
		public static List<Transaction> smethod_5(Class448 class448_1)
		{
			return TransFileImporter.smethod_6(class448_1, null, null);
		}

		// Token: 0x06002374 RID: 9076 RVA: 0x000EF9E4 File Offset: 0x000EDBE4
		private static List<Transaction> smethod_6(Class448 class448_1, int? nullable_0, int? nullable_1)
		{
			bool flag = class448_1.From == TransData.string_0;
			List<Transaction> list = new List<Transaction>();
			for (int i = 0; i < class448_1.Data.Count; i++)
			{
				try
				{
					TransData transData = class448_1.Data[i];
					Transaction transaction = new Transaction();
					if (nullable_0 != null)
					{
						transaction.AcctID = nullable_0.Value;
					}
					transaction.ID = i + ((nullable_1 != null) ? nullable_1.Value : 0);
					transaction.TransType = TransFileImporter.smethod_10(transData);
					DateTime value = transData.Day.Value;
					transaction.CreateTime = value;
					int? num = TransFileImporter.smethod_12(transData.Contract, new DateTime?(value));
					if (num == null)
					{
						transaction.SymbolID = -1;
					}
					else
					{
						transaction.SymbolID = num.Value;
					}
					transaction.Units = (long)transData.Count.Value;
					transaction.Price = (decimal)transData.Price.Value;
					transaction.Notes = transData.Note;
					transaction.Profit = transData.Profit;
					if (class448_1.From == Class448.string_2)
					{
						if (!string.IsNullOrEmpty(transData.CloseID))
						{
							transaction.ClosedTransID = new int?(Convert.ToInt32(transData.CloseID) + ((nullable_1 != null) ? nullable_1.Value : 0));
						}
						if (transData.Time != null)
						{
							transaction.CreateTime = value.Date + transData.Time.Value.TimeOfDay;
						}
					}
					if (transData.Poundage == null)
					{
						transaction.Fee = new decimal?(0m);
					}
					else
					{
						transaction.Fee = new decimal?((decimal)transData.Poundage.Value);
					}
					list.Add(transaction);
				}
				catch (Exception ex)
				{
					Class182.smethod_0(ex);
					string message = ex.Message;
				}
			}
			if (flag)
			{
				for (int j = 0; j < class448_1.Data.Count; j++)
				{
					Transaction transaction_ = list[j];
					TransData transData2 = class448_1.Data[j];
					if (!string.IsNullOrEmpty(transData2.CloseID))
					{
						TransFileImporter.smethod_9(transaction_, transData2.CloseID, list);
					}
				}
			}
			else
			{
				TransFileImporter.smethod_7(list);
			}
			return list;
		}

		// Token: 0x06002375 RID: 9077 RVA: 0x000EFC7C File Offset: 0x000EDE7C
		private static void smethod_7(List<Transaction> list_2)
		{
			foreach (var <>f__AnonymousType in list_2.GroupBy(new Func<Transaction, int>(TransFileImporter.<>c.<>9.method_5)).Select(new Func<IGrouping<int, Transaction>, <>f__AnonymousType9<int, List<Transaction>>>(TransFileImporter.<>c.<>9.method_6)))
			{
				List<Transaction> transList = <>f__AnonymousType.TransList;
				TransFileImporter.smethod_8(list_2, transList, Enum17.const_1);
				TransFileImporter.smethod_8(list_2, transList, Enum17.const_3);
			}
		}

		// Token: 0x06002376 RID: 9078 RVA: 0x000EFD20 File Offset: 0x000EDF20
		private static void smethod_8(List<Transaction> list_2, List<Transaction> list_3, Enum17 enum17_0)
		{
			TransFileImporter.Class456 @class = new TransFileImporter.Class456();
			@class.enum17_0 = enum17_0;
			List<Transaction> list = list_3.Where(new Func<Transaction, bool>(@class.method_0)).OrderByDescending(new Func<Transaction, DateTime>(TransFileImporter.<>c.<>9.method_7)).ToList<Transaction>();
			if (list.Any<Transaction>())
			{
				TransFileImporter.Class457 class2 = new TransFileImporter.Class457();
				class2.dateTime_0 = list.Min(new Func<Transaction, DateTime>(TransFileImporter.<>c.<>9.method_8));
				class2.enum17_0 = ((@class.enum17_0 == Enum17.const_1) ? Enum17.const_2 : Enum17.const_4);
				List<Transaction> source = list_3.Where(new Func<Transaction, bool>(class2.method_0)).ToList<Transaction>();
				if (source.Any<Transaction>())
				{
					long num = list.Sum(new Func<Transaction, long>(TransFileImporter.<>c.<>9.method_9));
					long num2 = source.Sum(new Func<Transaction, long>(TransFileImporter.<>c.<>9.method_10));
					long num3 = num - num2;
					if (num3 > 0L)
					{
						for (int i = 0; i < list.Count; i++)
						{
							TransFileImporter.Class458 class3 = new TransFileImporter.Class458();
							class3.transaction_0 = list[i];
							class3.transaction_0.OpenUnits = new long?((num3 > class3.transaction_0.Units) ? class3.transaction_0.Units : num3);
							list_2.Single(new Func<Transaction, bool>(class3.method_0)).OpenUnits = class3.transaction_0.OpenUnits;
							num3 -= class3.transaction_0.OpenUnits.Value;
							if (num3 <= 0L)
							{
								break;
							}
						}
					}
				}
			}
		}

		// Token: 0x06002377 RID: 9079 RVA: 0x000EFEF8 File Offset: 0x000EE0F8
		private static bool smethod_9(Transaction transaction_0, string string_1, List<Transaction> list_2)
		{
			TransFileImporter.Class459 @class = new TransFileImporter.Class459();
			@class.string_1 = string_1;
			@class.string_0 = Class463.smethod_18(transaction_0.Notes);
			Transaction transaction = list_2.FirstOrDefault(new Func<Transaction, bool>(@class.method_0));
			bool result;
			if (transaction != null)
			{
				transaction_0.ClosedTransID = new int?(transaction.ID);
				transaction.OpenUnits -= transaction_0.Units;
				long? openUnits = transaction.OpenUnits;
				if (openUnits.GetValueOrDefault() < 0L & openUnits != null)
				{
					transaction.OpenUnits = new long?(0L);
				}
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06002378 RID: 9080 RVA: 0x000EFFD4 File Offset: 0x000EE1D4
		private static int smethod_10(TransData transData_0)
		{
			Enum17 result = Enum17.const_2;
			if (transData_0.BuyOrSell == "卖" && transData_0.OpenOrClose == "开")
			{
				result = Enum17.const_3;
			}
			else if (transData_0.BuyOrSell == "卖" && (transData_0.OpenOrClose == "平" || transData_0.OpenOrClose == "平今"))
			{
				result = Enum17.const_2;
			}
			else if (transData_0.BuyOrSell == "买" && transData_0.OpenOrClose == "开")
			{
				result = Enum17.const_1;
			}
			else if (transData_0.BuyOrSell == "买" && (transData_0.OpenOrClose == "平" || transData_0.OpenOrClose == "平今"))
			{
				result = Enum17.const_4;
			}
			return (int)result;
		}

		// Token: 0x06002379 RID: 9081 RVA: 0x000F00B0 File Offset: 0x000EE2B0
		private static bool smethod_11(string string_1, DateTime? nullable_0 = null)
		{
			int? num = TransFileImporter.smethod_12(string_1, nullable_0);
			bool result;
			if (num != null)
			{
				int? num2 = num;
				result = (num2.GetValueOrDefault() > -1 & num2 != null);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600237A RID: 9082 RVA: 0x000F00EC File Offset: 0x000EE2EC
		public static int? smethod_12(string string_1, DateTime? nullable_0 = null)
		{
			int? result;
			if (string.IsNullOrEmpty(string_1))
			{
				result = null;
			}
			else
			{
				string_1 = string_1.Trim();
				Dictionary<int, StkSymbol> stkSymbols = TApp.SrvParams.StkSymbols;
				if (Utility.IfContainCNChar(string_1))
				{
					TransFileImporter.Class460 @class = new TransFileImporter.Class460();
					@class.string_0 = string_1;
					IEnumerable<KeyValuePair<int, StkSymbol>> source = stkSymbols.Where(new Func<KeyValuePair<int, StkSymbol>, bool>(@class.method_0));
					if (source.Any<KeyValuePair<int, StkSymbol>>())
					{
						return new int?(source.Last<KeyValuePair<int, StkSymbol>>().Key);
					}
				}
				else
				{
					TransFileImporter.Class461 class2 = new TransFileImporter.Class461();
					class2.string_0 = string_1;
					class2.nullable_0 = null;
					try
					{
						if (!Utility.IsDigitChars(string_1.Substring(0, 1), false, false))
						{
							string text = string_1.Substring(0, 2);
							if (text.Equals("SH", StringComparison.InvariantCultureIgnoreCase))
							{
								class2.nullable_0 = new int?(5);
							}
							else if (text.Equals("SZ", StringComparison.InvariantCultureIgnoreCase))
							{
								class2.nullable_0 = new int?(6);
							}
							if (class2.nullable_0 != null)
							{
								class2.string_0 = string_1.Substring(2, string_1.Length - 2);
							}
							else
							{
								class2.string_0 = TransFileImporter.smethod_14(string_1, nullable_0);
							}
						}
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
					if (!string.IsNullOrEmpty(class2.string_0))
					{
						IEnumerable<KeyValuePair<int, StkSymbol>> source2 = stkSymbols.Where(new Func<KeyValuePair<int, StkSymbol>, bool>(class2.method_0));
						if (class2.nullable_0 != null)
						{
							source2 = source2.Where(new Func<KeyValuePair<int, StkSymbol>, bool>(class2.method_1));
						}
						if (source2.Any<KeyValuePair<int, StkSymbol>>())
						{
							return new int?(source2.Last<KeyValuePair<int, StkSymbol>>().Key);
						}
						return new int?(-1);
					}
				}
				result = new int?(-1);
			}
			return result;
		}

		// Token: 0x0600237B RID: 9083 RVA: 0x000F02B0 File Offset: 0x000EE4B0
		public static bool smethod_13(TransData transData_0, out string string_1)
		{
			bool result;
			if (!TransData.FieldBuyOrSell.Contains(transData_0.BuyOrSell))
			{
				string_1 = "买卖";
				result = false;
			}
			else if (transData_0.Day == null)
			{
				string_1 = "日期";
				result = false;
			}
			else if (transData_0.Time == null)
			{
				string_1 = "时间";
				result = false;
			}
			else if (!TransFileImporter.smethod_11(transData_0.Contract, transData_0.Day))
			{
				string_1 = "合约";
				result = false;
			}
			else if (transData_0.Price == null)
			{
				string_1 = "价格";
				result = false;
			}
			else if (transData_0.Count == null)
			{
				string_1 = "手数";
				result = false;
			}
			else if (!TransData.FieldOpenOrClose.Contains(transData_0.OpenOrClose))
			{
				string_1 = "开平";
				result = false;
			}
			else if (transData_0.OpenOrClose.Contains("平") && transData_0.Profit == null)
			{
				string_1 = "平仓盈亏";
				result = false;
			}
			else
			{
				string_1 = "";
				result = true;
			}
			return result;
		}

		// Token: 0x0600237C RID: 9084 RVA: 0x000F03D0 File Offset: 0x000EE5D0
		public static string smethod_14(string string_1, DateTime? nullable_0 = null)
		{
			string result;
			if (string_1 == null)
			{
				result = null;
			}
			else if (string_1.Length < 3)
			{
				result = null;
			}
			else
			{
				string text = string_1.Substring(string_1.Length - 2, 2);
				string text2 = "";
				int num = 0;
				while (num < string_1.Length && !Utility.IsDigitChars(string_1.Substring(num, 1), false, false))
				{
					text2 += string_1.Substring(num, 1);
					num++;
				}
				text2 = text2.ToUpper();
				if (!(text2 == "BU") && !(text2 == "A") && !(text2 == "SR"))
				{
					result = text2 + text;
				}
				else
				{
					string value;
					if (text2.Length + text.Length < string_1.Length)
					{
						if (text2 == "SR" && string_1.Length == 5)
						{
							value = string_1.Substring(2, 1);
						}
						else
						{
							value = string_1.Substring(2, 2);
						}
					}
					else
					{
						if (nullable_0 == null)
						{
							throw new Exception("代码分析失败，合约年份无法判断！（代码：" + string_1 + ")");
						}
						int num2 = nullable_0.Value.Year;
						if (nullable_0.Value > new DateTime(num2, Convert.ToInt32(text), 1))
						{
							num2++;
						}
						value = num2.ToString().Substring(3, 1);
					}
					if (Convert.ToInt32(value) % 2 == 0)
					{
						result = text2 + "X" + text;
					}
					else
					{
						result = text2 + "Y" + text;
					}
				}
			}
			return result;
		}

		// Token: 0x0600237D RID: 9085 RVA: 0x000F0554 File Offset: 0x000EE754
		public static void smethod_15(Class448 class448_1)
		{
			TransFileImporter.Class462 @class = new TransFileImporter.Class462();
			@class.class448_0 = class448_1;
			try
			{
				CfmmcAcct cfmmcAcct = Class463.smethod_9().Single(new Func<CfmmcAcct, bool>(@class.method_0));
				for (int i = 0; i < cfmmcAcct.BindingAccts.Count; i++)
				{
					if (cfmmcAcct.BindingAccts[i].EndDate == null)
					{
						TransFileImporter.smethod_1(@class.class448_0, cfmmcAcct.BindingAccts[i].Id);
					}
					else if (cfmmcAcct.BindingAccts[i].EndDate > cfmmcAcct.EndDate)
					{
						TransFileImporter.smethod_1(@class.class448_0, cfmmcAcct.BindingAccts[i].Id);
					}
				}
			}
			catch
			{
			}
		}

		// Token: 0x04001112 RID: 4370
		private Interface3 msg;

		// Token: 0x04001113 RID: 4371
		private const char char_0 = '\t';

		// Token: 0x04001114 RID: 4372
		private static readonly char[] char_1 = new char[]
		{
			',',
			'|',
			'\t'
		};

		// Token: 0x04001115 RID: 4373
		private static readonly string[] string_0 = new string[]
		{
			"编号,成交编号",
			"合约,合约号,代码,证券代码,品种,品种代码,股票,Symbol",
			"买卖,买卖标志,操作",
			"开平",
			"成交价,成交价格,成交均价,价格,Price",
			"手数,成交手数,成交量,成交数量",
			"手续费,Fee",
			"日期,成交日期,Date",
			"时间,成交时间,Time",
			"平仓盈亏,盈亏,盈利,Profit"
		};

		// Token: 0x04001116 RID: 4374
		private int[] int_0;

		// Token: 0x04001117 RID: 4375
		private List<string> list_0;

		// Token: 0x04001118 RID: 4376
		private Class448 class448_0;

		// Token: 0x04001119 RID: 4377
		private List<Class479> list_1;

		// Token: 0x0400111A RID: 4378
		[CompilerGenerated]
		private DateTime dateTime_0;

		// Token: 0x02000349 RID: 841
		[CompilerGenerated]
		private sealed class Class452
		{
			// Token: 0x06002380 RID: 9088 RVA: 0x000F06D8 File Offset: 0x000EE8D8
			internal bool method_0(char char_0)
			{
				return char_0 == TransFileImporter.char_1[this.int_0];
			}

			// Token: 0x0400111B RID: 4379
			public int int_0;
		}

		// Token: 0x0200034B RID: 843
		[CompilerGenerated]
		private sealed class Class453
		{
			// Token: 0x0600238F RID: 9103 RVA: 0x000F0768 File Offset: 0x000EE968
			internal bool method_0(Account account_0)
			{
				bool result;
				if (account_0.AcctName == this.string_0)
				{
					result = (account_0.UserName == TApp.UserName);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04001128 RID: 4392
			public string string_0;
		}

		// Token: 0x0200034C RID: 844
		[CompilerGenerated]
		private sealed class Class454
		{
			// Token: 0x06002391 RID: 9105 RVA: 0x000F07A0 File Offset: 0x000EE9A0
			internal bool method_0(Transaction transaction_1)
			{
				bool result;
				if (transaction_1.SymbolID == this.transaction_0.SymbolID && transaction_1.CreateTime == this.transaction_0.CreateTime && transaction_1.Price == this.transaction_0.Price && transaction_1.Units == this.transaction_0.Units && transaction_1.TransType == this.transaction_0.TransType && !string.IsNullOrEmpty(transaction_1.Notes))
				{
					result = transaction_1.Notes.Contains("Cfmmc");
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04001129 RID: 4393
			public Transaction transaction_0;
		}

		// Token: 0x0200034D RID: 845
		[CompilerGenerated]
		private sealed class Class455
		{
			// Token: 0x06002393 RID: 9107 RVA: 0x000F083C File Offset: 0x000EEA3C
			internal bool method_0(Transaction transaction_1)
			{
				bool result;
				if (transaction_1.SymbolID == this.transaction_0.SymbolID && transaction_1.CreateTime == this.transaction_0.CreateTime && transaction_1.Price == this.transaction_0.Price && transaction_1.Units == this.transaction_0.Units && transaction_1.TransType == this.transaction_0.TransType && !string.IsNullOrEmpty(transaction_1.Notes))
				{
					result = transaction_1.Notes.Contains("Cfmmc");
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400112A RID: 4394
			public Transaction transaction_0;
		}

		// Token: 0x0200034E RID: 846
		[CompilerGenerated]
		private sealed class Class456
		{
			// Token: 0x06002395 RID: 9109 RVA: 0x000F08D8 File Offset: 0x000EEAD8
			internal bool method_0(Transaction transaction_0)
			{
				return transaction_0.TransType == (int)this.enum17_0;
			}

			// Token: 0x0400112B RID: 4395
			public Enum17 enum17_0;
		}

		// Token: 0x0200034F RID: 847
		[CompilerGenerated]
		private sealed class Class457
		{
			// Token: 0x06002397 RID: 9111 RVA: 0x000F08F8 File Offset: 0x000EEAF8
			internal bool method_0(Transaction transaction_0)
			{
				bool result;
				if (transaction_0.TransType == (int)this.enum17_0)
				{
					result = (transaction_0.CreateTime >= this.dateTime_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400112C RID: 4396
			public Enum17 enum17_0;

			// Token: 0x0400112D RID: 4397
			public DateTime dateTime_0;
		}

		// Token: 0x02000350 RID: 848
		[CompilerGenerated]
		private sealed class Class458
		{
			// Token: 0x06002399 RID: 9113 RVA: 0x000F092C File Offset: 0x000EEB2C
			internal bool method_0(Transaction transaction_1)
			{
				return transaction_1.ID == this.transaction_0.ID;
			}

			// Token: 0x0400112E RID: 4398
			public Transaction transaction_0;
		}

		// Token: 0x02000351 RID: 849
		[CompilerGenerated]
		private sealed class Class459
		{
			// Token: 0x0600239B RID: 9115 RVA: 0x000F0950 File Offset: 0x000EEB50
			internal bool method_0(Transaction transaction_0)
			{
				bool result;
				if (!string.IsNullOrEmpty(transaction_0.Notes))
				{
					if (transaction_0.Notes.Contains(this.string_0))
					{
						result = transaction_0.Notes.Contains(this.string_1);
					}
					else
					{
						result = false;
					}
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400112F RID: 4399
			public string string_0;

			// Token: 0x04001130 RID: 4400
			public string string_1;
		}

		// Token: 0x02000352 RID: 850
		[CompilerGenerated]
		private sealed class Class460
		{
			// Token: 0x0600239D RID: 9117 RVA: 0x000F099C File Offset: 0x000EEB9C
			internal bool method_0(KeyValuePair<int, StkSymbol> keyValuePair_0)
			{
				return keyValuePair_0.Value.CNName.Equals(this.string_0, StringComparison.InvariantCultureIgnoreCase);
			}

			// Token: 0x04001131 RID: 4401
			public string string_0;
		}

		// Token: 0x02000353 RID: 851
		[CompilerGenerated]
		private sealed class Class461
		{
			// Token: 0x0600239F RID: 9119 RVA: 0x000F09C8 File Offset: 0x000EEBC8
			internal bool method_0(KeyValuePair<int, StkSymbol> keyValuePair_0)
			{
				return keyValuePair_0.Value.Code.Equals(this.string_0, StringComparison.InvariantCultureIgnoreCase);
			}

			// Token: 0x060023A0 RID: 9120 RVA: 0x000F09F4 File Offset: 0x000EEBF4
			internal bool method_1(KeyValuePair<int, StkSymbol> keyValuePair_0)
			{
				return keyValuePair_0.Value.ExchangeID == this.nullable_0.Value;
			}

			// Token: 0x04001132 RID: 4402
			public string string_0;

			// Token: 0x04001133 RID: 4403
			public int? nullable_0;
		}

		// Token: 0x02000354 RID: 852
		[CompilerGenerated]
		private sealed class Class462
		{
			// Token: 0x060023A2 RID: 9122 RVA: 0x000F0A20 File Offset: 0x000EEC20
			internal bool method_0(CfmmcAcct cfmmcAcct_0)
			{
				return cfmmcAcct_0.ID == this.class448_0.CfmmcAcctID;
			}

			// Token: 0x04001134 RID: 4404
			public Class448 class448_0;
		}
	}
}
