﻿using System;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;
using mshtml;
using ns22;
using ns27;
using ns4;
using SHDocVw;

namespace ns8
{
	// Token: 0x0200037A RID: 890
	internal sealed partial class CfmmcWebFrm : Form
	{
		// Token: 0x060024D1 RID: 9425 RVA: 0x0000E411 File Offset: 0x0000C611
		public CfmmcWebFrm()
		{
			this.InitializeComponent();
			this.webBrowserMain.AllowWebBrowserDrop = false;
			(this.webBrowserMain.ActiveXInstance as SHDocVw.WebBrowser).NewWindow2 += new DWebBrowserEvents2_NewWindow2EventHandler(this, (UIntPtr)ldftn(method_1));
		}

		// Token: 0x060024D2 RID: 9426 RVA: 0x000F5990 File Offset: 0x000F3B90
		protected void Dispose(bool disposing)
		{
			if (disposing)
			{
				if (this.icontainer_0 != null)
				{
					this.icontainer_0.Dispose();
				}
				try
				{
					(this.webBrowserMain.ActiveXInstance as SHDocVw.WebBrowser).NewWindow2 -= new DWebBrowserEvents2_NewWindow2EventHandler(this, (UIntPtr)ldftn(method_1));
				}
				catch
				{
				}
			}
			base.Dispose(disposing);
		}

		// Token: 0x060024D3 RID: 9427 RVA: 0x000F59F4 File Offset: 0x000F3BF4
		public string method_0()
		{
			string result;
			try
			{
				HtmlElementCollection elementsByTagName = this.webBrowserMain.Document.GetElementsByTagName("input");
				string text = "";
				foreach (object obj in elementsByTagName)
				{
					HtmlElement htmlElement = (HtmlElement)obj;
					text = text + htmlElement.GetAttribute("name") + " ";
					if (htmlElement.GetAttribute("name") == "logout" && htmlElement.GetAttribute("value") == "退出系统")
					{
						htmlElement.InvokeMember("click");
						result = string.Empty;
						goto IL_BD;
					}
				}
				result = string.Format("无法找到logout元素,元素列表为{0}", text);
			}
			catch (Exception ex)
			{
				result = ex.Message;
			}
			IL_BD:
			return result;
		}

		// Token: 0x060024D4 RID: 9428 RVA: 0x0000E44E File Offset: 0x0000C64E
		private void method_1(ref object object_0, ref bool bool_0)
		{
			object_0 = this.webBrowserQryRslt.ActiveXInstance;
		}

		// Token: 0x060024D5 RID: 9429 RVA: 0x0000E45F File Offset: 0x0000C65F
		[Obsolete]
		public void method_2()
		{
			this.webBrowserMain.Refresh();
		}

		// Token: 0x060024D6 RID: 9430 RVA: 0x0000E46E File Offset: 0x0000C66E
		public void method_3(string string_0)
		{
			this.webBrowserMain.Navigate(string_0);
		}

		// Token: 0x060024D7 RID: 9431 RVA: 0x000F5AE4 File Offset: 0x000F3CE4
		private HtmlElement method_4()
		{
			HtmlElement result;
			foreach (object obj in this.webBrowserMain.Document.GetElementsByTagName("img"))
			{
				HtmlElement htmlElement = (HtmlElement)obj;
				if (htmlElement.GetAttribute("src").Contains("veriCode.do?t="))
				{
					result = htmlElement;
					goto IL_65;
				}
			}
			return null;
			IL_65:
			return result;
		}

		// Token: 0x060024D8 RID: 9432 RVA: 0x000F5B70 File Offset: 0x000F3D70
		public Bitmap method_5(HtmlElement htmlElement_0)
		{
			IHTMLImgElement ihtmlimgElement = (IHTMLImgElement)htmlElement_0.DomElement;
			CfmmcWebFrm.Interface5 @interface = (CfmmcWebFrm.Interface5)ihtmlimgElement;
			Bitmap bitmap = new Bitmap(ihtmlimgElement.width, ihtmlimgElement.height);
			Graphics graphics = Graphics.FromImage(bitmap);
			IntPtr hdc = graphics.GetHdc();
			@interface.imethod_0(hdc);
			graphics.ReleaseHdc(hdc);
			return bitmap;
		}

		// Token: 0x060024D9 RID: 9433
		[DllImport("urlmon.dll")]
		[return: MarshalAs(UnmanagedType.Error)]
		private static extern int CoInternetSetFeatureEnabled(int int_2, [MarshalAs(UnmanagedType.U4)] int int_3, bool bool_0);

		// Token: 0x060024DA RID: 9434 RVA: 0x0000E47E File Offset: 0x0000C67E
		private static void smethod_0()
		{
			CfmmcWebFrm.CoInternetSetFeatureEnabled(21, 2, true);
		}

		// Token: 0x060024DB RID: 9435 RVA: 0x000F5BC0 File Offset: 0x000F3DC0
		public string method_6(string string_0, string string_1)
		{
			string result;
			try
			{
				CfmmcWebFrm.smethod_0();
				bool flag = false;
				bool flag2 = false;
				bool flag3 = false;
				HtmlElement htmlElement_ = this.method_4();
				foreach (object obj in this.webBrowserMain.Document.GetElementsByTagName("input"))
				{
					HtmlElement htmlElement = (HtmlElement)obj;
					if (htmlElement.GetAttribute("name") == "userID")
					{
						Thread.Sleep(200);
						htmlElement.InnerText = string_0;
						flag = true;
					}
					else if (htmlElement.GetAttribute("name") == "password")
					{
						Thread.Sleep(200);
						htmlElement.InnerText = string_1;
						flag2 = true;
					}
					else if (htmlElement.GetAttribute("name") == "vericode")
					{
						Thread.Sleep(200);
						Bitmap bitmap_ = this.method_5(htmlElement_);
						Class482 @class = Class483.smethod_0("leven");
						string levenFeature = Class488.levenFeature;
						File.WriteAllText("leven.txt", levenFeature);
						@class.vmethod_1("leven.txt");
						string innerText = @class.vmethod_5(bitmap_);
						htmlElement.InnerText = innerText;
						flag3 = true;
					}
					else if (htmlElement.GetAttribute("value") == "提交|Submit")
					{
						if (flag && flag2 && flag3)
						{
							Thread.Sleep(200);
							htmlElement.InvokeMember("click");
							result = string.Empty;
							goto IL_18A;
						}
						result = "登录页面没有找到，请检查网络。";
						goto IL_18A;
					}
				}
				result = "导航失败，请检查网络。";
			}
			catch (Exception ex)
			{
				result = ex.Message;
			}
			IL_18A:
			return result;
		}

		// Token: 0x060024DC RID: 9436 RVA: 0x000F5D94 File Offset: 0x000F3F94
		public int method_7(HtmlElement htmlElement_0)
		{
			int num = htmlElement_0.OffsetRectangle.Left;
			HtmlElement offsetParent = htmlElement_0.OffsetParent;
			while (offsetParent != null)
			{
				num += offsetParent.OffsetRectangle.Left;
				offsetParent = offsetParent.OffsetParent;
			}
			return num;
		}

		// Token: 0x060024DD RID: 9437 RVA: 0x000F5DE0 File Offset: 0x000F3FE0
		public int method_8(HtmlElement htmlElement_0)
		{
			int num = htmlElement_0.OffsetRectangle.Top;
			HtmlElement offsetParent = htmlElement_0.OffsetParent;
			while (offsetParent != null)
			{
				num += offsetParent.OffsetRectangle.Top;
				offsetParent = offsetParent.OffsetParent;
			}
			return num;
		}

		// Token: 0x060024DE RID: 9438 RVA: 0x000F5E2C File Offset: 0x000F402C
		public string method_9()
		{
			string result;
			try
			{
				foreach (object obj in this.webBrowserMain.Document.GetElementsByTagName("font"))
				{
					HtmlElement htmlElement = (HtmlElement)obj;
					if (htmlElement.InnerText.Contains("用户名或密码错误"))
					{
						result = "用户名或密码错误";
						goto IL_89;
					}
					if (htmlElement.InnerText.Contains("错误尝试"))
					{
						result = "登录错误尝试次数超过系统限制";
						goto IL_89;
					}
				}
				result = string.Empty;
			}
			catch (Exception ex)
			{
				result = ex.Message;
			}
			IL_89:
			return result;
		}

		// Token: 0x060024DF RID: 9439 RVA: 0x000F5EE8 File Offset: 0x000F40E8
		public string method_10()
		{
			string result;
			try
			{
				using (IEnumerator enumerator = this.webBrowserMain.Document.GetElementsByTagName("font").GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						if (((HtmlElement)enumerator.Current).InnerText.Contains("验证码错误"))
						{
							result = "验证码错误";
							goto IL_6D;
						}
					}
				}
				result = string.Empty;
			}
			catch (Exception ex)
			{
				result = ex.Message;
			}
			IL_6D:
			return result;
		}

		// Token: 0x060024E0 RID: 9440 RVA: 0x000F5F84 File Offset: 0x000F4184
		public WebBrowserReadyState method_11(int int_2)
		{
			WebBrowserReadyState readyState;
			if (int_2 == 1)
			{
				readyState = this.webBrowserMain.ReadyState;
			}
			else
			{
				if (int_2 != 2)
				{
					throw new Exception("请输入正确的浏览器号码");
				}
				readyState = this.webBrowserQryRslt.ReadyState;
			}
			return readyState;
		}

		// Token: 0x060024E1 RID: 9441 RVA: 0x000F5FC4 File Offset: 0x000F41C4
		public string method_12()
		{
			string result;
			foreach (object obj in this.webBrowserMain.Document.GetElementsByTagName("span"))
			{
				HtmlElement htmlElement = (HtmlElement)obj;
				if (htmlElement.InnerText.Contains("客户交易结算日报"))
				{
					htmlElement.InvokeMember("click");
					result = "";
					goto IL_74;
				}
			}
			return "点击日报表失败";
			IL_74:
			return result;
		}

		// Token: 0x060024E2 RID: 9442 RVA: 0x000F605C File Offset: 0x000F425C
		public bool method_13(object object_0)
		{
			bool result;
			if ((object_0 as System.Windows.Forms.WebBrowser).ReadyState == WebBrowserReadyState.Complete)
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x060024E3 RID: 9443 RVA: 0x000F6080 File Offset: 0x000F4280
		public string method_14(DateTime dateTime_0)
		{
			HtmlElementCollection elementsByTagName = this.webBrowserMain.Document.GetElementsByTagName("input");
			bool flag = false;
			string value = dateTime_0.ToString("yyyy-MM-dd");
			string text = "";
			foreach (object obj in elementsByTagName)
			{
				HtmlElement htmlElement = (HtmlElement)obj;
				text = text + htmlElement.GetAttribute("name") + " ";
				if (htmlElement.GetAttribute("name") == "tradeDate")
				{
					Thread.Sleep(200);
					htmlElement.SetAttribute("value", value);
					flag = true;
					break;
				}
			}
			string result;
			if (!flag)
			{
				result = string.Format("没有在web中找到tradeDate元素,搜索元素为{0}", text);
			}
			else
			{
				string text2;
				try
				{
					foreach (object obj2 in this.webBrowserMain.Document.GetElementsByTagName("input"))
					{
						HtmlElement htmlElement2 = (HtmlElement)obj2;
						if (htmlElement2.GetAttribute("value") == "提交")
						{
							Thread.Sleep(200);
							htmlElement2.InvokeMember("click");
							text2 = "";
							goto IL_16B;
						}
					}
					text2 = "点击提交日期错误";
				}
				catch (Exception ex)
				{
					MessageBox.Show(string.Format("点击提交日期错误{0}", ex.Message));
					text2 = "点击提交日期错误";
				}
				IL_16B:
				result = text2;
			}
			return result;
		}

		// Token: 0x060024E4 RID: 9444 RVA: 0x000F6228 File Offset: 0x000F4428
		public string method_15()
		{
			string result;
			try
			{
				foreach (object obj in this.webBrowserMain.Document.GetElementsByTagName("a"))
				{
					HtmlElement htmlElement = (HtmlElement)obj;
					if ((htmlElement.InnerText ?? "").Contains("平仓明细"))
					{
						htmlElement.InvokeMember("click");
						result = "";
						goto IL_9D;
					}
				}
				result = "点击平仓明细失败";
			}
			catch (Exception ex)
			{
				MessageBox.Show(string.Format("点击平仓明细错误{0}", ex.Message));
				result = "点击平仓明细失败";
			}
			IL_9D:
			return result;
		}

		// Token: 0x060024E5 RID: 9445 RVA: 0x000F62F8 File Offset: 0x000F44F8
		public string method_16()
		{
			string result;
			try
			{
				foreach (object obj in this.webBrowserMain.Document.GetElementsByTagName("a"))
				{
					HtmlElement htmlElement = (HtmlElement)obj;
					if ((htmlElement.InnerText ?? "").Contains("成交明细"))
					{
						htmlElement.InvokeMember("click");
						result = "";
						goto IL_9D;
					}
				}
				result = "点击成交明细失败";
			}
			catch (Exception ex)
			{
				MessageBox.Show(string.Format("点击平仓明细错误{0}", ex.Message));
				result = "点击成交明细失败";
			}
			IL_9D:
			return result;
		}

		// Token: 0x17000641 RID: 1601
		// (get) Token: 0x060024E6 RID: 9446 RVA: 0x000F63C8 File Offset: 0x000F45C8
		public System.Windows.Forms.WebBrowser WebBrowserMain
		{
			get
			{
				return this.webBrowserMain;
			}
		}

		// Token: 0x17000642 RID: 1602
		// (get) Token: 0x060024E7 RID: 9447 RVA: 0x000F63E0 File Offset: 0x000F45E0
		public System.Windows.Forms.WebBrowser WebBrowserQryRslt
		{
			get
			{
				return this.webBrowserQryRslt;
			}
		}

		// Token: 0x040011C5 RID: 4549
		private const int int_0 = 21;

		// Token: 0x040011C6 RID: 4550
		private const int int_1 = 2;

		// Token: 0x040011C7 RID: 4551
		private IContainer icontainer_0;

		// Token: 0x0200037B RID: 891
		[InterfaceType(1)]
		[Guid("3050F669-98B5-11CF-BB82-00AA00BDCE0B")]
		[ComImport]
		private interface Interface5
		{
			// Token: 0x060024E9 RID: 9449
			void imethod_0(IntPtr intptr_0);

			// Token: 0x060024EA RID: 9450
			void imethod_1(string string_0, IntPtr intptr_0);
		}
	}
}
