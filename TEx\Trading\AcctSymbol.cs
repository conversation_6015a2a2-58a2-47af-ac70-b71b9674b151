﻿using System;
using TEx.Comn;

namespace TEx.Trading
{
	// Token: 0x020003B1 RID: 945
	[Serializable]
	internal sealed class AcctSymbol : TradingSymbol
	{
		// Token: 0x1700068D RID: 1677
		// (get) Token: 0x0600266A RID: 9834 RVA: 0x000FBD24 File Offset: 0x000F9F24
		// (set) Token: 0x0600266B RID: 9835 RVA: 0x0000EA99 File Offset: 0x0000CC99
		public int AcctID
		{
			get
			{
				return this._AcctID;
			}
			set
			{
				this._AcctID = value;
			}
		}

		// Token: 0x1700068E RID: 1678
		// (get) Token: 0x0600266C RID: 9836 RVA: 0x000FBD3C File Offset: 0x000F9F3C
		public DateTime? LastDT
		{
			get
			{
				DateTime? result;
				if (this._LastHisData != null)
				{
					result = new DateTime?(this._LastHisData.Date);
				}
				else
				{
					result = null;
				}
				return result;
			}
		}

		// Token: 0x1700068F RID: 1679
		// (get) Token: 0x0600266D RID: 9837 RVA: 0x000FBD74 File Offset: 0x000F9F74
		// (set) Token: 0x0600266E RID: 9838 RVA: 0x0000EAA4 File Offset: 0x0000CCA4
		[Obsolete]
		public HisData LastHisData
		{
			get
			{
				return this._LastHisData;
			}
			set
			{
				this._LastHisData = value;
			}
		}

		// Token: 0x17000690 RID: 1680
		// (get) Token: 0x0600266F RID: 9839 RVA: 0x000FBD8C File Offset: 0x000F9F8C
		// (set) Token: 0x06002670 RID: 9840 RVA: 0x0000EAAF File Offset: 0x0000CCAF
		public bool IfAutoStopLoss
		{
			get
			{
				return this._IfAutoStopLoss;
			}
			set
			{
				if (this._IfAutoStopLoss != value)
				{
					this._IfAutoStopLoss = value;
				}
			}
		}

		// Token: 0x17000691 RID: 1681
		// (get) Token: 0x06002671 RID: 9841 RVA: 0x000FBDA4 File Offset: 0x000F9FA4
		// (set) Token: 0x06002672 RID: 9842 RVA: 0x0000EAC3 File Offset: 0x0000CCC3
		public bool IfAutoLimitTake
		{
			get
			{
				return this._IfAutoLimitTake;
			}
			set
			{
				if (this._IfAutoLimitTake != value)
				{
					this._IfAutoLimitTake = value;
				}
			}
		}

		// Token: 0x06002673 RID: 9843 RVA: 0x000FBDBC File Offset: 0x000F9FBC
		public void method_0(TradingSymbol tradingSymbol_0)
		{
			base.ID = tradingSymbol_0.ID;
			this.ExchangeID = tradingSymbol_0.ExchangeID;
			base.Code = tradingSymbol_0.Code;
			this.DigitNb = tradingSymbol_0.DigitNb;
			this.LeastPriceVar = tradingSymbol_0.LeastPriceVar;
			this.FeeType = tradingSymbol_0.FeeType;
			this.CNName = tradingSymbol_0.CNName;
			this.ENName = tradingSymbol_0.ENName;
			base.Type = tradingSymbol_0.Type;
			this.TonsPerUnit = tradingSymbol_0.TonsPerUnit;
			this.IsOneSideFee = tradingSymbol_0.IsOneSideFee;
			this.AvgSlipg = tradingSymbol_0.AvgSlipg;
			this.AutoLimitTakePoints = tradingSymbol_0.AutoLimitTakePoints;
			this.AutoStopLossPoints = tradingSymbol_0.AutoStopLossPoints;
			this.FeePerUnit = tradingSymbol_0.FeePerUnit;
			this.FeeRate = tradingSymbol_0.FeeRate;
			this.MarginRate = tradingSymbol_0.MarginRate;
			this.DefaultUnits = tradingSymbol_0.DefaultUnits;
		}

		// Token: 0x04001281 RID: 4737
		private int _AcctID;

		// Token: 0x04001282 RID: 4738
		private HisData _LastHisData;

		// Token: 0x04001283 RID: 4739
		private bool _IfAutoStopLoss;

		// Token: 0x04001284 RID: 4740
		private bool _IfAutoLimitTake;
	}
}
