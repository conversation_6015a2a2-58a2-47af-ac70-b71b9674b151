﻿using System;
using System.Runtime.CompilerServices;
using System.Threading;
using ns10;
using ns28;
using ns8;
using TEx;
using TEx.Comn;

namespace ns14
{
	// Token: 0x02000037 RID: 55
	internal sealed class Class16
	{
		// Token: 0x14000005 RID: 5
		// (add) Token: 0x06000183 RID: 387 RVA: 0x00016CC0 File Offset: 0x00014EC0
		// (remove) Token: 0x06000184 RID: 388 RVA: 0x00016CF8 File Offset: 0x00014EF8
		public event MsgEventHandler HeartBeatResponsed
		{
			[CompilerGenerated]
			add
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Combine(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Remove(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
		}

		// Token: 0x06000185 RID: 389 RVA: 0x00016D30 File Offset: 0x00014F30
		private void method_0(ApiResult apiResult_0)
		{
			MsgEventArgs e = new MsgEventArgs(apiResult_0.msg, apiResult_0);
			MsgEventHandler msgEventHandler = this.msgEventHandler_0;
			if (msgEventHandler != null)
			{
				msgEventHandler(this, e);
			}
		}

		// Token: 0x06000186 RID: 390 RVA: 0x00016D60 File Offset: 0x00014F60
		public Class16(int int_1 = 20000)
		{
			this.Interval = int_1;
			this.thread_0 = new Thread(new ThreadStart(this.method_1));
			this.thread_0.Name = "HeartBeatThread";
			this.thread_0.IsBackground = true;
			this.thread_0.Start();
		}

		// Token: 0x06000187 RID: 391 RVA: 0x00016DBC File Offset: 0x00014FBC
		private void method_1()
		{
			for (;;)
			{
				if (TApp.IsLoggedIn && !string.IsNullOrEmpty(TApp.LoginCode))
				{
					Class7<string, string> postDataObj = new Class7<string, string>("Heartbeat", TApp.LoginCode);
					Class556 @class = new Class556();
					try
					{
						ApiResult apiResultByPost = @class.GetApiResultByPost(postDataObj);
						if (apiResultByPost != null)
						{
							this.method_0(apiResultByPost);
						}
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
				Thread.Sleep(this.Interval);
			}
		}

		// Token: 0x1700006B RID: 107
		// (get) Token: 0x06000188 RID: 392 RVA: 0x00016E2C File Offset: 0x0001502C
		// (set) Token: 0x06000189 RID: 393 RVA: 0x000030DB File Offset: 0x000012DB
		public int Interval { get; set; }

		// Token: 0x04000098 RID: 152
		[CompilerGenerated]
		private MsgEventHandler msgEventHandler_0;

		// Token: 0x04000099 RID: 153
		private Thread thread_0;

		// Token: 0x0400009A RID: 154
		[CompilerGenerated]
		private int int_0;
	}
}
