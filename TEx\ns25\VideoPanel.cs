﻿using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns12;
using ns14;
using ns22;
using ns28;
using ns29;
using ns6;
using TEx;
using TEx.Util;
using Vlc.DotNet.Core;
using Vlc.DotNet.Forms;

namespace ns25
{
	// Token: 0x020002B6 RID: 694
	[Docking(DockingBehavior.AutoDock)]
	internal sealed class VideoPanel : UserControl
	{
		// Token: 0x06001E9A RID: 7834 RVA: 0x000D6AC0 File Offset: 0x000D4CC0
		public VideoPanel()
		{
			this.InitializeComponent();
			this.panel_Play.MouseHover += this.panel_Play_MouseHover;
			this.panel_Play.MouseLeave += this.panel_Play_MouseLeave;
			this.panel_Play.Click += this.panel_Play_Click;
			this.panel_Stop.MouseHover += this.panel_Stop_MouseHover;
			this.panel_Stop.MouseLeave += this.panel_Stop_MouseLeave;
			this.panel_Stop.Click += this.panel_Stop_Click;
			this.panel_VolIncrease.MouseHover += this.panel_VolIncrease_MouseHover;
			this.panel_VolIncrease.MouseLeave += this.panel_VolIncrease_MouseLeave;
			this.panel_VolIncrease.Click += this.panel_VolIncrease_Click;
			this.panel_VolDecrease.MouseHover += this.panel_VolDecrease_MouseHover;
			this.panel_VolDecrease.MouseLeave += this.panel_VolDecrease_MouseLeave;
			this.panel_VolDecrease.Click += this.panel_VolDecrease_Click;
			this.slider_Video.MouseUp += this.slider_Video_MouseUp;
			this.slider_Volume.ValueChanged += this.method_22;
			base.HandleCreated += this.VideoPanel_HandleCreated;
			Base.UI.ChartThemeChanged += this.method_4;
		}

		// Token: 0x06001E9B RID: 7835 RVA: 0x0000CC59 File Offset: 0x0000AE59
		private void method_0(bool bool_2 = false)
		{
			base.Invoke(new Action(this.method_33));
		}

		// Token: 0x06001E9C RID: 7836 RVA: 0x000D6C64 File Offset: 0x000D4E64
		private void method_1()
		{
			Color color = Base.UI.smethod_34();
			Color color2 = Base.UI.smethod_35();
			this.itemPanel_Chapters.BackgroundStyle.BackColor = color;
			this.itemPanel_Chapters.BackgroundStyle.BackColor2 = color;
			this.itemPanel_Chapters.BackColor = color;
			this.itemPanel_Chapters.BackgroundStyle.TextColor = color2;
			this.itemPanel_Chapters.ForeColor = color2;
			eButtonColor colorTable = this.method_2();
			foreach (object obj in this.itemPanel_Chapters.Items)
			{
				ButtonItem buttonItem = (ButtonItem)obj;
				buttonItem.ColorTable = colorTable;
				if (Base.UI.Form.ChartTheme != ChartTheme.Classic)
				{
					buttonItem.ForeColor = Class179.color_3;
				}
				else
				{
					buttonItem.ForeColor = Color.White;
				}
			}
		}

		// Token: 0x06001E9D RID: 7837 RVA: 0x000D6D50 File Offset: 0x000D4F50
		private eButtonColor method_2()
		{
			eButtonColor result;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				result = eButtonColor.BlueOrb;
			}
			else
			{
				result = eButtonColor.Blue;
			}
			return result;
		}

		// Token: 0x06001E9E RID: 7838 RVA: 0x000041AE File Offset: 0x000023AE
		private void method_3()
		{
		}

		// Token: 0x06001E9F RID: 7839 RVA: 0x0000CC70 File Offset: 0x0000AE70
		private void method_4(object sender, EventArgs e)
		{
			this.method_1();
		}

		// Token: 0x06001EA0 RID: 7840 RVA: 0x0000CC7A File Offset: 0x0000AE7A
		private void VideoPanel_HandleCreated(object sender, EventArgs e)
		{
			this.method_1();
			this.method_0(false);
			this.method_3();
			this.method_5();
		}

		// Token: 0x06001EA1 RID: 7841 RVA: 0x000D6D74 File Offset: 0x000D4F74
		private void method_5()
		{
			ButtonItem buttonItem = this.itemPanel_Chapters.Items[0] as ButtonItem;
			Class356 @class = buttonItem.Tag as Class356;
			this.string_0 = @class.VideoUrl;
			this.method_7(buttonItem);
		}

		// Token: 0x06001EA2 RID: 7842 RVA: 0x000D6DBC File Offset: 0x000D4FBC
		private void method_6(object sender, EventArgs e)
		{
			ButtonItem buttonItem = sender as ButtonItem;
			Class356 @class = buttonItem.Tag as Class356;
			if (TApp.IsTrialUser && !@class.IsFree)
			{
				if (MessageBox.Show("所选视频章节需正式版用户权限，点击确定键将为您转至相关网页。", "请确认", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) != DialogResult.OK)
				{
					return;
				}
				try
				{
					Process.Start(new ProcessStartInfo("http://" + TApp.HOST + "/buy"));
					return;
				}
				catch
				{
					return;
				}
			}
			this.method_7(buttonItem);
		}

		// Token: 0x06001EA3 RID: 7843 RVA: 0x000D6E40 File Offset: 0x000D5040
		private void method_7(ButtonItem buttonItem_0)
		{
			this.IsPlaying = false;
			buttonItem_0.Checked = true;
			int num = 0;
			while (this.bool_0)
			{
				if (Utility.CanExactDiv(num, 100))
				{
					this.IsPlaying = false;
				}
				Thread.Sleep(10);
				num = num;
			}
			Class356 @class = buttonItem_0.Tag as Class356;
			this.string_0 = @class.VideoUrl;
			ThreadPool.QueueUserWorkItem(new WaitCallback(this.method_34));
		}

		// Token: 0x06001EA4 RID: 7844 RVA: 0x0000CC97 File Offset: 0x0000AE97
		private void method_8(object sender, EventArgs e)
		{
			this.Cursor = Cursors.Hand;
		}

		// Token: 0x06001EA5 RID: 7845 RVA: 0x0000A3B7 File Offset: 0x000085B7
		private void method_9(object sender, EventArgs e)
		{
			this.Cursor = Cursors.Default;
		}

		// Token: 0x06001EA6 RID: 7846 RVA: 0x0000CCA6 File Offset: 0x0000AEA6
		private void method_10(object sender, VlcMediaPlayerPlayingEventArgs e)
		{
			this.bool_0 = true;
		}

		// Token: 0x06001EA7 RID: 7847 RVA: 0x0000CCB1 File Offset: 0x0000AEB1
		private void vlcControl_0_MouseClick(object sender, MouseEventArgs e)
		{
			this.method_23();
		}

		// Token: 0x06001EA8 RID: 7848 RVA: 0x0000CCBB File Offset: 0x0000AEBB
		private void method_11(object sender, VlcMediaPlayerVideoOutChangedEventArgs e)
		{
			if (e.NewCount > 0)
			{
				this.method_20();
				if (this.IsStartPausing)
				{
					this.IsPlaying = false;
					this.IsStartPausing = false;
					base.Invoke(new Action(this.method_35));
				}
			}
		}

		// Token: 0x06001EA9 RID: 7849 RVA: 0x0000CCF7 File Offset: 0x0000AEF7
		private void method_12(object sender, VlcMediaPlayerPausedEventArgs e)
		{
			this.bool_0 = false;
		}

		// Token: 0x06001EAA RID: 7850 RVA: 0x000041AE File Offset: 0x000023AE
		private void method_13(object sender, VlcMediaPlayerScrambledChangedEventArgs e)
		{
		}

		// Token: 0x06001EAB RID: 7851 RVA: 0x000041AE File Offset: 0x000023AE
		private void method_14(object sender, VlcMediaPlayerPausableChangedEventArgs e)
		{
		}

		// Token: 0x06001EAC RID: 7852 RVA: 0x000041AE File Offset: 0x000023AE
		private void method_15(object sender, VlcMediaPlayerSeekableChangedEventArgs e)
		{
		}

		// Token: 0x06001EAD RID: 7853 RVA: 0x0000CCF7 File Offset: 0x0000AEF7
		private void method_16(object sender, VlcMediaPlayerStoppedEventArgs e)
		{
			this.bool_0 = false;
		}

		// Token: 0x06001EAE RID: 7854 RVA: 0x000D6EB0 File Offset: 0x000D50B0
		private void method_17(object sender, VlcMediaPlayerTimeChangedEventArgs e)
		{
			if (this.vlcControl_0.Length > 0L && this.vlcControl_0.Length - e.NewTime < 500L)
			{
				base.Invoke(new Action(this.method_36));
			}
		}

		// Token: 0x06001EAF RID: 7855 RVA: 0x000D6F08 File Offset: 0x000D5108
		private void method_18(object sender, VlcMediaPlayerPositionChangedEventArgs e)
		{
			VideoPanel.Class354 @class = new VideoPanel.Class354();
			@class.videoPanel_0 = this;
			double num = (double)Math.Abs(e.NewPosition - this.float_0);
			@class.decimal_0 = Convert.ToDecimal(e.NewPosition * 100f);
			if (@class.decimal_0 > 99m)
			{
				@class.decimal_0 = 100m;
			}
			base.Invoke(new Action(@class.method_0));
			this.float_0 = e.NewPosition;
		}

		// Token: 0x06001EB0 RID: 7856 RVA: 0x000D6F90 File Offset: 0x000D5190
		private void method_19(object sender, VlcMediaPlayerLengthChangedEventArgs e)
		{
			if (this.vlcControl_0.Length >= 0L)
			{
				VideoPanel.Class355 @class = new VideoPanel.Class355();
				@class.videoPanel_0 = this;
				@class.string_0 = this.method_21(this.vlcControl_0.Length);
				if (this.label_TotalTime.Text != @class.string_0)
				{
					base.Invoke(new Action(@class.method_0));
				}
			}
		}

		// Token: 0x06001EB1 RID: 7857 RVA: 0x000D7004 File Offset: 0x000D5204
		private void method_20()
		{
			if (this.vlcControl_0.BackgroundImage != null)
			{
				base.Invoke(new Action(this.method_37));
				int num = Convert.ToInt32(this.slider_Volume.Value);
				if (this.vlcControl_0.Audio.Volume != num)
				{
					this.vlcControl_0.Audio.Volume = num;
				}
			}
		}

		// Token: 0x06001EB2 RID: 7858 RVA: 0x000D7068 File Offset: 0x000D5268
		private string method_21(long long_0)
		{
			TimeSpan timeSpan = TimeSpan.FromMilliseconds((double)long_0);
			return string.Concat(new string[]
			{
				timeSpan.Hours.ToString("D2"),
				":",
				timeSpan.Minutes.ToString("D2"),
				":",
				timeSpan.Seconds.ToString("D2")
			});
		}

		// Token: 0x06001EB3 RID: 7859 RVA: 0x0000CD02 File Offset: 0x0000AF02
		private void method_22(object sender, EventArgs e)
		{
			this.vlcControl_0.Audio.Volume = Convert.ToInt32(Math.Round(this.slider_Volume.Value));
		}

		// Token: 0x06001EB4 RID: 7860 RVA: 0x0000CCB1 File Offset: 0x0000AEB1
		private void panel_Play_Click(object sender, EventArgs e)
		{
			this.method_23();
		}

		// Token: 0x06001EB5 RID: 7861 RVA: 0x000D70E4 File Offset: 0x000D52E4
		public void method_23()
		{
			if (!this.IsPlaying)
			{
				this.IsStartPausing = false;
				if ((double)this.vlcControl_0.Position > 0.99)
				{
					this.vlcControl_0.Position = 0.002f;
				}
			}
			this.IsPlaying = !this.IsPlaying;
		}

		// Token: 0x06001EB6 RID: 7862 RVA: 0x0000CD2B File Offset: 0x0000AF2B
		public void method_24()
		{
			if (this.IsPlaying)
			{
				this.IsPlaying = false;
			}
		}

		// Token: 0x06001EB7 RID: 7863 RVA: 0x0000CD3E File Offset: 0x0000AF3E
		public void method_25(bool bool_2)
		{
			if (this.vlcControl_0 != null)
			{
				this.vlcControl_0.Visible = bool_2;
				this.vlcControl_0.Refresh();
			}
		}

		// Token: 0x06001EB8 RID: 7864 RVA: 0x000D7138 File Offset: 0x000D5338
		private void panel_Play_MouseHover(object sender, EventArgs e)
		{
			Control control_ = sender as Panel;
			Image image_;
			if (this.IsPlaying)
			{
				image_ = Class372.pause_blue2_28px;
			}
			else
			{
				image_ = Class372.play_blue2_28px;
			}
			this.method_29(control_, image_);
		}

		// Token: 0x06001EB9 RID: 7865 RVA: 0x000D716C File Offset: 0x000D536C
		private void panel_Play_MouseLeave(object sender, EventArgs e)
		{
			Control control_ = sender as Panel;
			Image image_;
			if (this.IsPlaying)
			{
				image_ = Class372.pause_blue1_28px;
			}
			else
			{
				image_ = Class372.play_blue1_28px;
			}
			this.method_28(control_, image_);
		}

		// Token: 0x06001EBA RID: 7866 RVA: 0x000D71A0 File Offset: 0x000D53A0
		private void panel_Stop_MouseHover(object sender, EventArgs e)
		{
			Control control_ = sender as Panel;
			this.method_29(control_, Class372.stop_blue2_28px);
		}

		// Token: 0x06001EBB RID: 7867 RVA: 0x000D71C4 File Offset: 0x000D53C4
		private void panel_Stop_MouseLeave(object sender, EventArgs e)
		{
			Control control_ = sender as Panel;
			this.method_28(control_, Class372.stop_blue1_28px);
		}

		// Token: 0x06001EBC RID: 7868 RVA: 0x0000CD61 File Offset: 0x0000AF61
		private void panel_Stop_Click(object sender, EventArgs e)
		{
			this.method_26();
		}

		// Token: 0x06001EBD RID: 7869 RVA: 0x0000CD6B File Offset: 0x0000AF6B
		private void method_26()
		{
			base.Invoke(new Action(this.method_38));
			this.method_27();
		}

		// Token: 0x06001EBE RID: 7870 RVA: 0x0000CD88 File Offset: 0x0000AF88
		private void method_27()
		{
			base.Invoke(new Action(this.method_39));
		}

		// Token: 0x06001EBF RID: 7871 RVA: 0x000D71E8 File Offset: 0x000D53E8
		private void panel_VolIncrease_Click(object sender, EventArgs e)
		{
			decimal num = this.slider_Volume.Value + 10m;
			if (num > 100m)
			{
				num = 100m;
			}
			this.slider_Volume.Value = num;
		}

		// Token: 0x06001EC0 RID: 7872 RVA: 0x000D7234 File Offset: 0x000D5434
		private void panel_VolIncrease_MouseHover(object sender, EventArgs e)
		{
			Control control_ = sender as Panel;
			this.method_29(control_, Class372.volume_inc_blue2_28px);
		}

		// Token: 0x06001EC1 RID: 7873 RVA: 0x000D7258 File Offset: 0x000D5458
		private void panel_VolIncrease_MouseLeave(object sender, EventArgs e)
		{
			Control control_ = sender as Panel;
			this.method_28(control_, Class372.volume_inc_blue1_28px);
		}

		// Token: 0x06001EC2 RID: 7874 RVA: 0x000D727C File Offset: 0x000D547C
		private void panel_VolDecrease_Click(object sender, EventArgs e)
		{
			decimal num = this.slider_Volume.Value - 10m;
			if (num < 0m)
			{
				num = 0m;
			}
			this.slider_Volume.Value = num;
		}

		// Token: 0x06001EC3 RID: 7875 RVA: 0x000D72C4 File Offset: 0x000D54C4
		private void panel_VolDecrease_MouseHover(object sender, EventArgs e)
		{
			Control control_ = sender as Panel;
			this.method_29(control_, Class372.volume_dec_blue2_28px);
		}

		// Token: 0x06001EC4 RID: 7876 RVA: 0x000D72E8 File Offset: 0x000D54E8
		private void panel_VolDecrease_MouseLeave(object sender, EventArgs e)
		{
			Control control_ = sender as Panel;
			this.method_28(control_, Class372.volume_dec_blue1_28px);
		}

		// Token: 0x06001EC5 RID: 7877 RVA: 0x0000CD9F File Offset: 0x0000AF9F
		private void method_28(Control control_0, Image image_0 = null)
		{
			this.method_30(control_0, this.color_1, image_0);
		}

		// Token: 0x06001EC6 RID: 7878 RVA: 0x0000CDB1 File Offset: 0x0000AFB1
		private void method_29(Control control_0, Image image_0 = null)
		{
			this.method_30(control_0, this.color_0, image_0);
		}

		// Token: 0x06001EC7 RID: 7879 RVA: 0x0000CDC3 File Offset: 0x0000AFC3
		private void method_30(Control control_0, Color color_2, Image image_0 = null)
		{
			if (control_0.BackColor != color_2)
			{
				control_0.BackColor = color_2;
			}
			if (image_0 != null && control_0.BackgroundImage != image_0)
			{
				control_0.BackgroundImage = image_0;
			}
		}

		// Token: 0x06001EC8 RID: 7880 RVA: 0x000D730C File Offset: 0x000D550C
		private void slider_Video_MouseUp(object sender, MouseEventArgs e)
		{
			Class46.smethod_2("SliderVideo_MouseUp");
			float num = Convert.ToSingle(this.slider_Video.Value / this.slider_Video.Maximum);
			if (this.vlcControl_0.Length > 0L)
			{
				if ((double)Math.Abs(num - this.vlcControl_0.Position) > 0.01)
				{
					this.vlcControl_0.Position = num;
				}
			}
			else
			{
				this.IsPlaying = true;
				if (this.vlcControl_0.Length > 0L)
				{
					this.vlcControl_0.Position = num;
				}
			}
		}

		// Token: 0x06001EC9 RID: 7881 RVA: 0x0000CDEF File Offset: 0x0000AFEF
		private void method_31()
		{
			if (!this.IsPlaying)
			{
				this.IsStartPausing = true;
				this.IsPlaying = true;
				this.vlcControl_0.Audio.Volume = 0;
				this.method_27();
			}
		}

		// Token: 0x06001ECA RID: 7882 RVA: 0x0000CE20 File Offset: 0x0000B020
		public void method_32()
		{
			base.Invoke(new Action(this.method_40));
		}

		// Token: 0x170004CB RID: 1227
		// (get) Token: 0x06001ECB RID: 7883 RVA: 0x000D73B4 File Offset: 0x000D55B4
		// (set) Token: 0x06001ECC RID: 7884 RVA: 0x000D73D0 File Offset: 0x000D55D0
		public bool IsPlaying
		{
			get
			{
				return this.vlcControl_0.IsPlaying;
			}
			set
			{
				if (value)
				{
					base.Invoke(new Action(this.method_41));
					ThreadPool.QueueUserWorkItem(new WaitCallback(this.method_42));
				}
				else
				{
					ThreadPool.QueueUserWorkItem(new WaitCallback(this.method_43));
					base.Invoke(new Action(this.method_44));
				}
			}
		}

		// Token: 0x170004CC RID: 1228
		// (get) Token: 0x06001ECD RID: 7885 RVA: 0x000D7430 File Offset: 0x000D5630
		// (set) Token: 0x06001ECE RID: 7886 RVA: 0x0000CE37 File Offset: 0x0000B037
		private bool IsStartPausing { get; set; }

		// Token: 0x06001ECF RID: 7887 RVA: 0x0000CE42 File Offset: 0x0000B042
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001ED0 RID: 7888 RVA: 0x000D7448 File Offset: 0x000D5648
		private void InitializeComponent()
		{
			this.panel_Lessons = new Panel();
			this.itemPanel_Chapters = new ItemPanel();
			this.expSpliter = new ExpandableSplitter();
			this.tablePanel_Video = new TableLayoutPanel();
			this.tablePanel_VideoCtrls = new TableLayoutPanel();
			this.panel_VolDecrease = new Panel();
			this.panel_VolIncrease = new Panel();
			this.panel_Stop = new Panel();
			this.slider_Video = new Control0();
			this.panel_Play = new Panel();
			this.tablePanel_Times = new TableLayoutPanel();
			this.label_TotalTime = new Label();
			this.label_CurrTime = new Label();
			this.slider_Volume = new Control0();
			this.panel_Lessons.SuspendLayout();
			this.tablePanel_Video.SuspendLayout();
			this.tablePanel_VideoCtrls.SuspendLayout();
			this.tablePanel_Times.SuspendLayout();
			base.SuspendLayout();
			this.panel_Lessons.Controls.Add(this.itemPanel_Chapters);
			this.panel_Lessons.Dock = DockStyle.Left;
			this.panel_Lessons.Location = new Point(0, 0);
			this.panel_Lessons.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.panel_Lessons.Name = "panel_Lessons";
			this.panel_Lessons.Size = new Size(465, 414);
			this.panel_Lessons.TabIndex = 3;
			this.itemPanel_Chapters.AutoScroll = true;
			this.itemPanel_Chapters.BackColor = SystemColors.Control;
			this.itemPanel_Chapters.BackgroundStyle.BackColor = Color.FromArgb(59, 59, 59);
			this.itemPanel_Chapters.BackgroundStyle.BackColor2 = Color.FromArgb(59, 59, 59);
			this.itemPanel_Chapters.BackgroundStyle.Class = "ItemPanel";
			this.itemPanel_Chapters.BackgroundStyle.CornerType = eCornerType.Square;
			this.itemPanel_Chapters.ContainerControlProcessDialogKey = true;
			this.itemPanel_Chapters.Dock = DockStyle.Fill;
			this.itemPanel_Chapters.LayoutOrientation = eOrientation.Vertical;
			this.itemPanel_Chapters.Location = new Point(0, 0);
			this.itemPanel_Chapters.Margin = new System.Windows.Forms.Padding(0);
			this.itemPanel_Chapters.Name = "itemPanel_Chapters";
			this.itemPanel_Chapters.Size = new Size(465, 414);
			this.itemPanel_Chapters.TabIndex = 0;
			this.itemPanel_Chapters.Text = "itemPanel_Chapters";
			this.expSpliter.BackColor = SystemColors.ControlLight;
			this.expSpliter.BackColor2 = Color.Empty;
			this.expSpliter.BackColor2SchemePart = eColorSchemePart.None;
			this.expSpliter.BackColorSchemePart = eColorSchemePart.None;
			this.expSpliter.ExpandableControl = this.panel_Lessons;
			this.expSpliter.ExpandFillColor = Color.FromArgb(254, 142, 75);
			this.expSpliter.ExpandFillColorSchemePart = eColorSchemePart.ItemPressedBackground;
			this.expSpliter.ExpandLineColor = Color.FromArgb(0, 0, 128);
			this.expSpliter.ExpandLineColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expSpliter.GripDarkColor = Color.FromArgb(0, 0, 128);
			this.expSpliter.GripDarkColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expSpliter.GripLightColor = Color.FromArgb(246, 246, 246);
			this.expSpliter.GripLightColorSchemePart = eColorSchemePart.MenuBackground;
			this.expSpliter.HotBackColor = Color.FromArgb(255, 213, 140);
			this.expSpliter.HotBackColor2 = Color.Empty;
			this.expSpliter.HotBackColor2SchemePart = eColorSchemePart.None;
			this.expSpliter.HotBackColorSchemePart = eColorSchemePart.ItemCheckedBackground;
			this.expSpliter.HotExpandFillColor = Color.FromArgb(254, 142, 75);
			this.expSpliter.HotExpandFillColorSchemePart = eColorSchemePart.ItemPressedBackground;
			this.expSpliter.HotExpandLineColor = Color.FromArgb(0, 0, 128);
			this.expSpliter.HotExpandLineColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expSpliter.HotGripDarkColor = Color.FromArgb(0, 0, 128);
			this.expSpliter.HotGripDarkColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expSpliter.HotGripLightColor = Color.FromArgb(246, 246, 246);
			this.expSpliter.HotGripLightColorSchemePart = eColorSchemePart.MenuBackground;
			this.expSpliter.Location = new Point(465, 0);
			this.expSpliter.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
			this.expSpliter.Name = "expSpliter";
			this.expSpliter.Size = new Size(4, 414);
			this.expSpliter.Style = eSplitterStyle.Mozilla;
			this.expSpliter.TabIndex = 5;
			this.expSpliter.TabStop = false;
			this.tablePanel_Video.ColumnCount = 1;
			this.tablePanel_Video.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
			this.tablePanel_Video.Controls.Add(this.tablePanel_VideoCtrls, 0, 1);
			this.tablePanel_Video.Dock = DockStyle.Fill;
			this.tablePanel_Video.Location = new Point(469, 0);
			this.tablePanel_Video.Margin = new System.Windows.Forms.Padding(0);
			this.tablePanel_Video.Name = "tablePanel_Video";
			this.tablePanel_Video.RowCount = 2;
			this.tablePanel_Video.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tablePanel_Video.RowStyles.Add(new RowStyle(SizeType.Absolute, 34f));
			this.tablePanel_Video.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
			this.tablePanel_Video.Size = new Size(841, 414);
			this.tablePanel_Video.TabIndex = 0;
			this.tablePanel_VideoCtrls.ColumnCount = 7;
			this.tablePanel_VideoCtrls.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 36f));
			this.tablePanel_VideoCtrls.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 36f));
			this.tablePanel_VideoCtrls.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 72f));
			this.tablePanel_VideoCtrls.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
			this.tablePanel_VideoCtrls.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 36f));
			this.tablePanel_VideoCtrls.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 98f));
			this.tablePanel_VideoCtrls.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 36f));
			this.tablePanel_VideoCtrls.Controls.Add(this.panel_VolDecrease, 4, 0);
			this.tablePanel_VideoCtrls.Controls.Add(this.panel_VolIncrease, 6, 0);
			this.tablePanel_VideoCtrls.Controls.Add(this.panel_Stop, 1, 0);
			this.tablePanel_VideoCtrls.Controls.Add(this.slider_Video, 3, 0);
			this.tablePanel_VideoCtrls.Controls.Add(this.panel_Play, 0, 0);
			this.tablePanel_VideoCtrls.Controls.Add(this.tablePanel_Times, 2, 0);
			this.tablePanel_VideoCtrls.Controls.Add(this.slider_Volume, 5, 0);
			this.tablePanel_VideoCtrls.Dock = DockStyle.Fill;
			this.tablePanel_VideoCtrls.Location = new Point(0, 380);
			this.tablePanel_VideoCtrls.Margin = new System.Windows.Forms.Padding(0);
			this.tablePanel_VideoCtrls.Name = "tablePanel_VideoCtrls";
			this.tablePanel_VideoCtrls.RowCount = 1;
			this.tablePanel_VideoCtrls.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tablePanel_VideoCtrls.Size = new Size(841, 34);
			this.tablePanel_VideoCtrls.TabIndex = 1;
			this.panel_VolDecrease.BackColor = Color.FromArgb(70, 77, 95);
			this.panel_VolDecrease.BackgroundImage = Class372.volume_dec_blue1_28px;
			this.panel_VolDecrease.BackgroundImageLayout = ImageLayout.Center;
			this.panel_VolDecrease.Dock = DockStyle.Fill;
			this.panel_VolDecrease.Location = new Point(671, 0);
			this.panel_VolDecrease.Margin = new System.Windows.Forms.Padding(0);
			this.panel_VolDecrease.Name = "panel_VolDecrease";
			this.panel_VolDecrease.Size = new Size(36, 34);
			this.panel_VolDecrease.TabIndex = 4;
			this.panel_VolIncrease.BackColor = Color.FromArgb(70, 77, 95);
			this.panel_VolIncrease.BackgroundImage = Class372.volume_inc_blue1_28px;
			this.panel_VolIncrease.BackgroundImageLayout = ImageLayout.Center;
			this.panel_VolIncrease.Dock = DockStyle.Fill;
			this.panel_VolIncrease.Location = new Point(805, 0);
			this.panel_VolIncrease.Margin = new System.Windows.Forms.Padding(0);
			this.panel_VolIncrease.Name = "panel_VolIncrease";
			this.panel_VolIncrease.Size = new Size(36, 34);
			this.panel_VolIncrease.TabIndex = 3;
			this.panel_Stop.BackColor = Color.FromArgb(70, 77, 95);
			this.panel_Stop.BackgroundImage = Class372.stop_blue1_28px;
			this.panel_Stop.BackgroundImageLayout = ImageLayout.Center;
			this.panel_Stop.Dock = DockStyle.Fill;
			this.panel_Stop.Location = new Point(36, 0);
			this.panel_Stop.Margin = new System.Windows.Forms.Padding(0);
			this.panel_Stop.Name = "panel_Stop";
			this.panel_Stop.Size = new Size(36, 34);
			this.panel_Stop.TabIndex = 2;
			this.slider_Video.BackColor = Color.FromArgb(70, 77, 95);
			this.slider_Video.BarPenColorBottom = Color.FromArgb(87, 94, 110);
			this.slider_Video.BarPenColorTop = Color.FromArgb(55, 60, 74);
			this.slider_Video.BorderRoundRectSize = new Size(8, 8);
			this.slider_Video.Dock = DockStyle.Fill;
			this.slider_Video.ElapsedInnerColor = Color.FromArgb(21, 56, 152);
			this.slider_Video.ElapsedPenColorBottom = Color.FromArgb(99, 130, 208);
			this.slider_Video.ElapsedPenColorTop = Color.FromArgb(95, 140, 180);
			this.slider_Video.Font = new Font("Microsoft Sans Serif", 6f);
			this.slider_Video.ForeColor = Color.White;
			Control0 control = this.slider_Video;
			int[] array = new int[4];
			array[0] = 5;
			control.LargeChange = new decimal(array);
			this.slider_Video.Location = new Point(144, 0);
			this.slider_Video.Margin = new System.Windows.Forms.Padding(0);
			Control0 control2 = this.slider_Video;
			int[] array2 = new int[4];
			array2[0] = 100;
			control2.Maximum = new decimal(array2);
			this.slider_Video.Minimum = new decimal(new int[4]);
			this.slider_Video.Name = "slider_Video";
			this.slider_Video.Padding = 3;
			Control0 control3 = this.slider_Video;
			int[] array3 = new int[4];
			array3[0] = 10;
			control3.ScaleDivisions = new decimal(array3);
			Control0 control4 = this.slider_Video;
			int[] array4 = new int[4];
			array4[0] = 5;
			control4.ScaleSubDivisions = new decimal(array4);
			this.slider_Video.ShowDivisionsText = false;
			this.slider_Video.ShowSmallScale = false;
			this.slider_Video.Size = new Size(527, 34);
			Control0 control5 = this.slider_Video;
			int[] array5 = new int[4];
			array5[0] = 1;
			control5.SmallChange = new decimal(array5);
			this.slider_Video.TabIndex = 0;
			this.slider_Video.Text = "colorSlider1";
			this.slider_Video.ThumbInnerColor = Color.FromArgb(21, 56, 152);
			this.slider_Video.ThumbPenColor = Color.FromArgb(21, 56, 152);
			this.slider_Video.ThumbRoundRectSize = new Size(16, 16);
			this.slider_Video.ThumbSize = new Size(16, 16);
			this.slider_Video.TickAdd = 0f;
			this.slider_Video.TickColor = Color.White;
			this.slider_Video.TickDivide = 0f;
			this.slider_Video.TickStyle = TickStyle.None;
			this.slider_Video.Value = new decimal(new int[4]);
			this.panel_Play.BackColor = Color.FromArgb(70, 77, 95);
			this.panel_Play.BackgroundImage = Class372.play_blue1_28px;
			this.panel_Play.BackgroundImageLayout = ImageLayout.Center;
			this.panel_Play.Dock = DockStyle.Fill;
			this.panel_Play.Location = new Point(0, 0);
			this.panel_Play.Margin = new System.Windows.Forms.Padding(0);
			this.panel_Play.Name = "panel_Play";
			this.panel_Play.Size = new Size(36, 34);
			this.panel_Play.TabIndex = 1;
			this.tablePanel_Times.ColumnCount = 1;
			this.tablePanel_Times.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
			this.tablePanel_Times.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20f));
			this.tablePanel_Times.Controls.Add(this.label_TotalTime, 0, 1);
			this.tablePanel_Times.Controls.Add(this.label_CurrTime, 0, 0);
			this.tablePanel_Times.Location = new Point(72, 0);
			this.tablePanel_Times.Margin = new System.Windows.Forms.Padding(0);
			this.tablePanel_Times.Name = "tablePanel_Times";
			this.tablePanel_Times.RowCount = 2;
			this.tablePanel_Times.RowStyles.Add(new RowStyle(SizeType.Percent, 50f));
			this.tablePanel_Times.RowStyles.Add(new RowStyle(SizeType.Percent, 50f));
			this.tablePanel_Times.Size = new Size(72, 34);
			this.tablePanel_Times.TabIndex = 4;
			this.label_TotalTime.AutoSize = true;
			this.label_TotalTime.BackColor = Color.FromArgb(70, 77, 95);
			this.label_TotalTime.Dock = DockStyle.Fill;
			this.label_TotalTime.Font = new Font("Microsoft Sans Serif", 7.8f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_TotalTime.ForeColor = Color.FromArgb(79, 111, 189);
			this.label_TotalTime.Location = new Point(0, 17);
			this.label_TotalTime.Margin = new System.Windows.Forms.Padding(0);
			this.label_TotalTime.Name = "label_TotalTime";
			this.label_TotalTime.Size = new Size(72, 17);
			this.label_TotalTime.TabIndex = 2;
			this.label_TotalTime.Text = "00:00:00";
			this.label_TotalTime.TextAlign = ContentAlignment.TopCenter;
			this.label_CurrTime.AutoSize = true;
			this.label_CurrTime.BackColor = Color.FromArgb(70, 77, 95);
			this.label_CurrTime.Dock = DockStyle.Fill;
			this.label_CurrTime.Font = new Font("Microsoft Sans Serif", 7.8f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_CurrTime.ForeColor = Color.FromArgb(137, 159, 220);
			this.label_CurrTime.Location = new Point(0, 0);
			this.label_CurrTime.Margin = new System.Windows.Forms.Padding(0);
			this.label_CurrTime.Name = "label_CurrTime";
			this.label_CurrTime.Size = new Size(72, 17);
			this.label_CurrTime.TabIndex = 0;
			this.label_CurrTime.Text = "00:00:00";
			this.label_CurrTime.TextAlign = ContentAlignment.BottomCenter;
			this.slider_Volume.BackColor = Color.FromArgb(70, 77, 95);
			this.slider_Volume.BarPenColorBottom = Color.FromArgb(87, 94, 110);
			this.slider_Volume.BarPenColorTop = Color.FromArgb(55, 60, 74);
			this.slider_Volume.BorderRoundRectSize = new Size(8, 8);
			this.slider_Volume.ElapsedInnerColor = Color.Olive;
			this.slider_Volume.ElapsedPenColorBottom = Color.Goldenrod;
			this.slider_Volume.ElapsedPenColorTop = Color.DarkGoldenrod;
			this.slider_Volume.Font = new Font("Microsoft Sans Serif", 6f);
			this.slider_Volume.ForeColor = Color.White;
			Control0 control6 = this.slider_Volume;
			int[] array6 = new int[4];
			array6[0] = 5;
			control6.LargeChange = new decimal(array6);
			this.slider_Volume.Location = new Point(707, 0);
			this.slider_Volume.Margin = new System.Windows.Forms.Padding(0);
			Control0 control7 = this.slider_Volume;
			int[] array7 = new int[4];
			array7[0] = 100;
			control7.Maximum = new decimal(array7);
			this.slider_Volume.Minimum = new decimal(new int[4]);
			this.slider_Volume.Name = "slider_Volume";
			Control0 control8 = this.slider_Volume;
			int[] array8 = new int[4];
			array8[0] = 10;
			control8.ScaleDivisions = new decimal(array8);
			Control0 control9 = this.slider_Volume;
			int[] array9 = new int[4];
			array9[0] = 5;
			control9.ScaleSubDivisions = new decimal(array9);
			this.slider_Volume.ShowDivisionsText = true;
			this.slider_Volume.ShowSmallScale = false;
			this.slider_Volume.Size = new Size(98, 34);
			Control0 control10 = this.slider_Volume;
			int[] array10 = new int[4];
			array10[0] = 1;
			control10.SmallChange = new decimal(array10);
			this.slider_Volume.TabIndex = 5;
			this.slider_Volume.Text = "colorSlider1";
			this.slider_Volume.ThumbInnerColor = Color.FromArgb(21, 56, 152);
			this.slider_Volume.ThumbPenColor = Color.FromArgb(21, 56, 152);
			this.slider_Volume.ThumbRoundRectSize = new Size(16, 16);
			this.slider_Volume.ThumbSize = new Size(14, 14);
			this.slider_Volume.TickAdd = 0f;
			this.slider_Volume.TickColor = Color.White;
			this.slider_Volume.TickDivide = 0f;
			this.slider_Volume.TickStyle = TickStyle.None;
			Control0 control11 = this.slider_Volume;
			int[] array11 = new int[4];
			array11[0] = 90;
			control11.Value = new decimal(array11);
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.tablePanel_Video);
			base.Controls.Add(this.expSpliter);
			base.Controls.Add(this.panel_Lessons);
			base.Name = "VideoPanel";
			base.Size = new Size(1310, 414);
			this.panel_Lessons.ResumeLayout(false);
			this.tablePanel_Video.ResumeLayout(false);
			this.tablePanel_VideoCtrls.ResumeLayout(false);
			this.tablePanel_Times.ResumeLayout(false);
			this.tablePanel_Times.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x06001ED1 RID: 7889 RVA: 0x000D86DC File Offset: 0x000D68DC
		[CompilerGenerated]
		private void method_33()
		{
			if (this.vlcControl_0 != null)
			{
				VlcControl vlcControl = this.vlcControl_0;
				vlcControl.Playing -= this.method_10;
				vlcControl.Paused -= this.method_12;
				vlcControl.Stopped -= this.method_16;
				vlcControl.MouseClick -= this.vlcControl_0_MouseClick;
				vlcControl.PositionChanged -= this.method_18;
				vlcControl.TimeChanged -= this.method_17;
				vlcControl.LengthChanged -= this.method_19;
				vlcControl.ScrambledChanged -= this.method_13;
				vlcControl.VideoOutChanged -= this.method_11;
				vlcControl.SeekableChanged -= this.method_15;
				vlcControl.PausableChanged -= this.method_14;
				vlcControl.ResetMedia();
			}
			this.vlcControl_0 = new VlcControl();
			this.vlcControl_0.BeginInit();
			this.vlcControl_0.VlcLibDirectory = new DirectoryInfo(Path.Combine(Directory.GetCurrentDirectory(), "libvlc"));
			this.vlcControl_0.VlcMediaplayerOptions = new string[]
			{
				"-vv"
			};
			this.vlcControl_0.CreateControl();
			this.vlcControl_0.Location = new Point(0, 0);
			this.vlcControl_0.Dock = DockStyle.Fill;
			this.vlcControl_0.Margin = new System.Windows.Forms.Padding(0);
			this.vlcControl_0.EndInit();
			this.vlcControl_0.Playing += this.method_10;
			this.vlcControl_0.Paused += this.method_12;
			this.vlcControl_0.Stopped += this.method_16;
			this.vlcControl_0.MouseClick += this.vlcControl_0_MouseClick;
			this.vlcControl_0.PositionChanged += this.method_18;
			this.vlcControl_0.TimeChanged += this.method_17;
			this.vlcControl_0.LengthChanged += this.method_19;
			this.vlcControl_0.ScrambledChanged += this.method_13;
			this.vlcControl_0.VideoOutChanged += this.method_11;
			this.vlcControl_0.SeekableChanged += this.method_15;
			this.vlcControl_0.PausableChanged += this.method_14;
			this.tablePanel_Video.Controls.Add(this.vlcControl_0, 0, 0);
		}

		// Token: 0x06001ED2 RID: 7890 RVA: 0x0000CE63 File Offset: 0x0000B063
		[CompilerGenerated]
		private void method_34(object object_0)
		{
			this.vlcControl_0.SetMedia(new Uri(this.string_0), new string[0]);
			this.method_31();
		}

		// Token: 0x06001ED3 RID: 7891 RVA: 0x0000CE89 File Offset: 0x0000B089
		[CompilerGenerated]
		private void method_35()
		{
			this.vlcControl_0.smethod_1();
			this.vlcControl_0.Refresh();
		}

		// Token: 0x06001ED4 RID: 7892 RVA: 0x000D8970 File Offset: 0x000D6B70
		[CompilerGenerated]
		private void method_36()
		{
			this.method_26();
			this.panel_Play.BackgroundImage = Class372.play_blue1_28px;
			this.slider_Video.Value = 100m;
			string text = this.method_21(this.vlcControl_0.Length);
			this.label_CurrTime.Text = text;
		}

		// Token: 0x06001ED5 RID: 7893 RVA: 0x0000CEA3 File Offset: 0x0000B0A3
		[CompilerGenerated]
		private void method_37()
		{
			this.vlcControl_0.BackgroundImage = null;
			this.vlcControl_0.smethod_1();
			this.vlcControl_0.Refresh();
		}

		// Token: 0x06001ED6 RID: 7894 RVA: 0x0000CEC9 File Offset: 0x0000B0C9
		[CompilerGenerated]
		private void method_38()
		{
			if (this.IsPlaying)
			{
				this.IsPlaying = false;
			}
			this.vlcControl_0.Position = 0.002f;
		}

		// Token: 0x06001ED7 RID: 7895 RVA: 0x0000CEEC File Offset: 0x0000B0EC
		[CompilerGenerated]
		private void method_39()
		{
			this.slider_Video.Value = 0m;
			this.label_CurrTime.Text = "00:00:00";
		}

		// Token: 0x06001ED8 RID: 7896 RVA: 0x0000CF10 File Offset: 0x0000B110
		[CompilerGenerated]
		private void method_40()
		{
			this.vlcControl_0.Stop();
			while (this.bool_0)
			{
				Thread.Sleep(20);
			}
			this.vlcControl_0.ResetMedia();
			this.vlcControl_0.Dispose();
		}

		// Token: 0x06001ED9 RID: 7897 RVA: 0x0000CF46 File Offset: 0x0000B146
		[CompilerGenerated]
		private void method_41()
		{
			if (this.panel_Play.BackgroundImage != Class372.pause_blue1_28px)
			{
				this.panel_Play.BackgroundImage = Class372.pause_blue1_28px;
			}
			this.panel_Play.Refresh();
		}

		// Token: 0x06001EDA RID: 7898 RVA: 0x000D89C8 File Offset: 0x000D6BC8
		[CompilerGenerated]
		private void method_42(object object_0)
		{
			this.vlcControl_0.Play();
			int num = Convert.ToInt32(Math.Round(this.slider_Volume.Value));
			if (this.vlcControl_0.Audio.Volume != num)
			{
				this.vlcControl_0.Audio.Volume = num;
			}
		}

		// Token: 0x06001EDB RID: 7899 RVA: 0x0000CF77 File Offset: 0x0000B177
		[CompilerGenerated]
		private void method_43(object object_0)
		{
			this.vlcControl_0.Pause();
		}

		// Token: 0x06001EDC RID: 7900 RVA: 0x0000CF86 File Offset: 0x0000B186
		[CompilerGenerated]
		private void method_44()
		{
			if (this.panel_Play.BackgroundImage != Class372.play_blue1_28px)
			{
				this.panel_Play.BackgroundImage = Class372.play_blue1_28px;
			}
			this.panel_Play.Refresh();
		}

		// Token: 0x04000F75 RID: 3957
		private Color color_0 = Color.FromArgb(85, 92, 110);

		// Token: 0x04000F76 RID: 3958
		private Color color_1 = Color.FromArgb(70, 77, 95);

		// Token: 0x04000F77 RID: 3959
		private VlcControl vlcControl_0;

		// Token: 0x04000F78 RID: 3960
		private float float_0;

		// Token: 0x04000F79 RID: 3961
		private bool bool_0;

		// Token: 0x04000F7A RID: 3962
		private string string_0;

		// Token: 0x04000F7B RID: 3963
		[CompilerGenerated]
		private bool bool_1;

		// Token: 0x04000F7C RID: 3964
		private IContainer icontainer_0;

		// Token: 0x04000F7D RID: 3965
		private Panel panel_Lessons;

		// Token: 0x04000F7E RID: 3966
		private ExpandableSplitter expSpliter;

		// Token: 0x04000F7F RID: 3967
		private TableLayoutPanel tablePanel_Video;

		// Token: 0x04000F80 RID: 3968
		private Control0 slider_Video;

		// Token: 0x04000F81 RID: 3969
		private TableLayoutPanel tablePanel_VideoCtrls;

		// Token: 0x04000F82 RID: 3970
		private Panel panel_Play;

		// Token: 0x04000F83 RID: 3971
		private Panel panel_Stop;

		// Token: 0x04000F84 RID: 3972
		private Panel panel_VolIncrease;

		// Token: 0x04000F85 RID: 3973
		private TableLayoutPanel tablePanel_Times;

		// Token: 0x04000F86 RID: 3974
		private Label label_TotalTime;

		// Token: 0x04000F87 RID: 3975
		private Label label_CurrTime;

		// Token: 0x04000F88 RID: 3976
		private Panel panel_VolDecrease;

		// Token: 0x04000F89 RID: 3977
		private Control0 slider_Volume;

		// Token: 0x04000F8A RID: 3978
		private ItemPanel itemPanel_Chapters;

		// Token: 0x020002B7 RID: 695
		[CompilerGenerated]
		private sealed class Class354
		{
			// Token: 0x06001EDE RID: 7902 RVA: 0x000D8A1C File Offset: 0x000D6C1C
			internal void method_0()
			{
				this.videoPanel_0.slider_Video.Value = this.decimal_0;
				string text = this.videoPanel_0.method_21(this.videoPanel_0.vlcControl_0.Time);
				if (this.videoPanel_0.label_CurrTime.Text != text)
				{
					this.videoPanel_0.label_CurrTime.Text = text;
				}
			}

			// Token: 0x04000F8B RID: 3979
			public VideoPanel videoPanel_0;

			// Token: 0x04000F8C RID: 3980
			public decimal decimal_0;
		}

		// Token: 0x020002B8 RID: 696
		[CompilerGenerated]
		private sealed class Class355
		{
			// Token: 0x06001EE0 RID: 7904 RVA: 0x0000CFB7 File Offset: 0x0000B1B7
			internal void method_0()
			{
				this.videoPanel_0.label_TotalTime.Text = this.string_0;
			}

			// Token: 0x04000F8D RID: 3981
			public string string_0;

			// Token: 0x04000F8E RID: 3982
			public VideoPanel videoPanel_0;
		}
	}
}
