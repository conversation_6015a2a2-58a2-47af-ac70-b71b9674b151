﻿using System;

namespace TEx.Trading
{
	// Token: 0x020003AC RID: 940
	[Serializable]
	internal sealed class ShownCondOrder : CondOrder
	{
		// Token: 0x060025ED RID: 9709 RVA: 0x0000E722 File Offset: 0x0000C922
		public ShownCondOrder()
		{
		}

		// Token: 0x060025EE RID: 9710 RVA: 0x0000E72A File Offset: 0x0000C92A
		public ShownCondOrder(CondOrder co)
		{
			base.method_0(co);
			if (base.StkSymbol != null)
			{
				this.SymbCode = base.StkSymbol.Code;
			}
		}

		// Token: 0x17000657 RID: 1623
		// (get) Token: 0x060025EF RID: 9711 RVA: 0x000FA3D4 File Offset: 0x000F85D4
		// (set) Token: 0x060025F0 RID: 9712 RVA: 0x0000E754 File Offset: 0x0000C954
		public string SymbCode
		{
			get
			{
				return this._SymbCode;
			}
			set
			{
				this._SymbCode = value;
			}
		}

		// Token: 0x17000658 RID: 1624
		// (get) Token: 0x060025F1 RID: 9713 RVA: 0x000FA3EC File Offset: 0x000F85EC
		public string CondDesc
		{
			get
			{
				string result;
				if (base.TrailingStopPts != null)
				{
					if (base.CondPrice == 0m)
					{
						result = base.TrailingStopPts.Value / 1.00000000000000000m + "点跟踪止损";
					}
					else
					{
						result = this.method_2() + "(跟踪止损)";
					}
				}
				else
				{
					result = this.method_2();
				}
				return result;
			}
		}

		// Token: 0x060025F2 RID: 9714 RVA: 0x000FA474 File Offset: 0x000F8674
		private string method_2()
		{
			string arg = ">";
			if (base.ComparisonOpt == ComparisonOpt.BiggerOrEqual)
			{
				arg = ">=";
			}
			else if (base.ComparisonOpt == ComparisonOpt.Less)
			{
				arg = "<";
			}
			else if (base.ComparisonOpt == ComparisonOpt.LessOrEqual)
			{
				arg = "<=";
			}
			return "价格" + arg + base.CondPrice / 1.00000000000000000m;
		}

		// Token: 0x17000659 RID: 1625
		// (get) Token: 0x060025F3 RID: 9715 RVA: 0x000FA4EC File Offset: 0x000F86EC
		public string StatusDesc
		{
			get
			{
				string result = "未触发";
				if (base.OrderStatus == OrderStatus.Executed)
				{
					result = "已触发";
				}
				else if (base.OrderStatus == OrderStatus.Canceled)
				{
					result = "已取消";
				}
				return result;
			}
		}

		// Token: 0x1700065A RID: 1626
		// (get) Token: 0x060025F4 RID: 9716 RVA: 0x000FA524 File Offset: 0x000F8724
		public string LongShortDesc
		{
			get
			{
				string result = "-";
				if (base.OrderType != OrderType.Order_CloseLong && base.OrderType != OrderType.Order_OpenShort)
				{
					if (base.OrderType != OrderType.Order_CloseLongRevOpen)
					{
						if (base.OrderType == OrderType.Order_OpenLong || base.OrderType == OrderType.Order_CloseShort || base.OrderType == OrderType.Order_CloseShortRevOpen)
						{
							result = "买";
							goto IL_4B;
						}
						goto IL_4B;
					}
				}
				result = "卖";
				IL_4B:
				return result;
			}
		}

		// Token: 0x1700065B RID: 1627
		// (get) Token: 0x060025F5 RID: 9717 RVA: 0x000FA584 File Offset: 0x000F8784
		public string OpenCloseDesc
		{
			get
			{
				string result = "-";
				if (base.OrderType == OrderType.Order_OpenLong || base.OrderType == OrderType.Order_OpenShort)
				{
					result = "开仓";
				}
				if (base.OrderType != OrderType.Order_CloseLong)
				{
					if (base.OrderType != OrderType.Order_CloseShort)
					{
						if (base.OrderType == OrderType.Order_CloseLongRevOpen || base.OrderType == OrderType.Order_CloseShortRevOpen)
						{
							result = "反手";
							goto IL_51;
						}
						goto IL_51;
					}
				}
				result = "平仓";
				IL_51:
				return result;
			}
		}

		// Token: 0x0400124E RID: 4686
		private string _SymbCode;
	}
}
