﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using ns11;
using ns28;
using TEx.Chart;
using TEx.Comn;
using TEx.Inds;

namespace TEx.SIndicator
{
	// Token: 0x020002E6 RID: 742
	internal class ShapeCurve : Class380
	{
		// Token: 0x060020EB RID: 8427 RVA: 0x0000D45C File Offset: 0x0000B65C
		public ShapeCurve(DataArray data, DataProvider dp, IndEx indEx) : base(data, dp, indEx)
		{
			if (this.rollingPointPairList_0 != null)
			{
				this.rollingPointPairList_0.Clear();
			}
		}

		// Token: 0x060020EC RID: 8428 RVA: 0x000E1F40 File Offset: 0x000E0140
		protected PointPair method_1(int int_0)
		{
			return this.vmethod_0(int_0, base.IndData);
		}

		// Token: 0x060020ED RID: 8429 RVA: 0x000E1F60 File Offset: 0x000E0160
		protected override PointPair vmethod_0(int int_0, DataArray dataArray_1)
		{
			if (dataArray_1.Data.Length < int_0 + 1)
			{
				Class182.smethod_0(new Exception(this.indEx_0.EnName + " 数据长度溢出。位置为" + int_0));
				int_0 = dataArray_1.Data.Length - 1;
			}
			double x = new XDate(base.method_0(int_0));
			double y = dataArray_1.Data[int_0];
			return new PointPair(x, y);
		}

		// Token: 0x060020EE RID: 8430 RVA: 0x000E1FD0 File Offset: 0x000E01D0
		protected int method_2()
		{
			int result;
			try
			{
				int length = "LINETHICK".Length;
				if (base.IndData.LineWithStr != null && base.IndData.LineWithStr.Length > length && base.IndData.LineWithStr.Substring(0, length).ToUpper() == "LINETHICK")
				{
					string text = base.IndData.LineWithStr.Substring(length, base.IndData.LineWithStr.Length - length);
					bool flag = true;
					int i = 0;
					while (i < text.Length)
					{
						if (char.IsDigit(text[i]))
						{
							i++;
						}
						else
						{
							flag = false;
							IL_9A:
							if (flag)
							{
								result = int.Parse(text);
								goto IL_B5;
							}
							goto IL_A7;
						}
					}
					goto IL_9A;
				}
				IL_A7:;
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
			return 1;
			IL_B5:
			return result;
		}

		// Token: 0x060020EF RID: 8431 RVA: 0x000E20AC File Offset: 0x000E02AC
		protected void method_3(string string_0, LineItem lineItem_0)
		{
			this.curveItem_0 = lineItem_0;
			lineItem_0.Tag = string_0 + (string.IsNullOrEmpty(base.IndData.Name) ? "" : ("_" + base.IndData.Name)) + (base.IndData.NumVisibleLineNot ? "_NODRAW" : "");
			lineItem_0.Line.Width = (float)this.method_2();
			if (base.IndData.LineTypeStr != null)
			{
				if (!(base.IndData.LineTypeStr == "DOT") && !(base.IndData.LineTypeStr == "DOTLINE") && !(base.IndData.LineTypeStr == "LINEDOT") && !(base.IndData.LineTypeStr == "POINTDOT"))
				{
					if (base.IndData.LineTypeStr == "DASH")
					{
						lineItem_0.Line.Style = DashStyle.Dash;
					}
					else if (base.IndData.LineTypeStr == "DASHDOT")
					{
						lineItem_0.Line.Style = DashStyle.DashDot;
					}
				}
				else
				{
					lineItem_0.Line.Style = DashStyle.Custom;
					lineItem_0.Line.DashOn = 1f;
					lineItem_0.Line.DashOff = 10f;
				}
			}
			lineItem_0.Line.IsAntiAlias = true;
			if (base.IndData.NumVisibleLineNot)
			{
				lineItem_0.Line.IsVisible = false;
			}
			if (base.IndData.ShapeStr == "DRAWTEXT" || base.IndData.ShapeStr == "DRAWNUMBER" || base.IndData.ShapeStr == "DRAWICON")
			{
				lineItem_0.Line.IsVisible = false;
			}
		}

		// Token: 0x060020F0 RID: 8432 RVA: 0x000E2284 File Offset: 0x000E0484
		public virtual void vmethod_8(ref double double_0, ref double double_1)
		{
			for (int i = 0; i < this.rollingPointPairList_0.Count; i++)
			{
				double y = this.rollingPointPairList_0[i].Y;
				if (!double.IsNaN(y))
				{
					if (y > double_0)
					{
						double_0 = y;
					}
					if (y < double_1)
					{
						double_1 = y;
					}
				}
			}
		}

		// Token: 0x060020F1 RID: 8433 RVA: 0x0000D47C File Offset: 0x0000B67C
		public override void vmethod_1(ZedGraphControl zedGraphControl_0)
		{
			if (this.Curve != null && zedGraphControl_0.GraphPane != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.Curve);
			}
		}

		// Token: 0x060020F2 RID: 8434 RVA: 0x0000D4A7 File Offset: 0x0000B6A7
		public override void vmethod_2(int int_0)
		{
			if (base.KCount != base.IndData.Data.Count<double>())
			{
				Class182.smethod_0(new Exception("数据长度错误"));
			}
			else
			{
				this.vmethod_3(int_0, base.IndData);
			}
		}

		// Token: 0x060020F3 RID: 8435 RVA: 0x000E22D4 File Offset: 0x000E04D4
		public override void vmethod_3(int int_0, DataArray dataArray_1)
		{
			if (this.method_5(dataArray_1))
			{
				this.method_4(int_0, dataArray_1);
			}
			PointPair point = this.vmethod_0(int_0, dataArray_1);
			((IPointListEdit)this.rollingPointPairList_0).Add(point);
		}

		// Token: 0x060020F4 RID: 8436 RVA: 0x000E230C File Offset: 0x000E050C
		public override void vmethod_4(int int_0, DataArray dataArray_1)
		{
			if (this.method_5(dataArray_1))
			{
				this.method_4(int_0, dataArray_1);
			}
			PointPair value = this.vmethod_0(int_0, dataArray_1);
			IPointListEdit rollingPointPairList_ = this.rollingPointPairList_0;
			if (rollingPointPairList_.Count > 0)
			{
				rollingPointPairList_[rollingPointPairList_.Count - 1] = value;
			}
		}

		// Token: 0x060020F5 RID: 8437 RVA: 0x0000D377 File Offset: 0x0000B577
		public override double vmethod_5(int int_0, HisData hisData_0)
		{
			throw new NotImplementedException();
		}

		// Token: 0x060020F6 RID: 8438 RVA: 0x000041AE File Offset: 0x000023AE
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
		}

		// Token: 0x060020F7 RID: 8439 RVA: 0x000E2354 File Offset: 0x000E0554
		protected void method_4(int int_0, DataArray dataArray_1)
		{
			int count = this.rollingPointPairList_0.Count;
			if (count < int_0)
			{
				IPointListEdit rollingPointPairList_ = this.rollingPointPairList_0;
				int num = 0;
				for (int i = count - 1; i >= 0; i--)
				{
					num++;
					rollingPointPairList_[i] = this.vmethod_0(int_0 - num, dataArray_1);
				}
			}
		}

		// Token: 0x060020F8 RID: 8440 RVA: 0x000E23A0 File Offset: 0x000E05A0
		public bool method_5(DataArray dataArray_1)
		{
			bool result;
			if (dataArray_1.HasLast)
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x170005BF RID: 1471
		// (get) Token: 0x060020F9 RID: 8441 RVA: 0x000E23C0 File Offset: 0x000E05C0
		public CurveItem Curve
		{
			get
			{
				return this.curveItem_0;
			}
		}

		// Token: 0x04001021 RID: 4129
		protected CurveItem curveItem_0;
	}
}
