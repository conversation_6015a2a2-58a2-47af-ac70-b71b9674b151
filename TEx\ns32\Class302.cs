﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;

namespace ns32
{
	// Token: 0x0200022B RID: 555
	[DesignerCategory("code")]
	internal sealed class Class302 : NumericUpDown
	{
		// Token: 0x060016FD RID: 5885 RVA: 0x0009AE2C File Offset: 0x0009902C
		public Class302()
		{
			base.SetStyle(ControlStyles.SupportsTransparentBackColor, true);
			base.SetStyle(ControlStyles.Opaque, true);
			this.textBox_0 = Class302.smethod_0<TextBox>(this, "upDownEdit");
			if (this.textBox_0 == null)
			{
				throw new ArgumentNullException(base.GetType().FullName + ": Can't find internal TextBox field.");
			}
			this.control_0 = Class302.smethod_0<Control>(this, "upDownButtons");
			if (this.control_0 == null)
			{
				throw new ArgumentNullException(base.GetType().FullName + ": Can't find internal UpDown buttons field.");
			}
			this.textBox_0.MouseEnter += this.Class302_MouseLeave;
			this.textBox_0.MouseLeave += this.Class302_MouseLeave;
			this.control_0.MouseEnter += this.Class302_MouseLeave;
			this.control_0.MouseLeave += this.Class302_MouseLeave;
			base.MouseEnter += this.Class302_MouseLeave;
			base.MouseLeave += this.Class302_MouseLeave;
		}

		// Token: 0x060016FE RID: 5886 RVA: 0x0009AF40 File Offset: 0x00099140
		protected internal static T smethod_0<T>(Class302 class302_0, string string_0) where T : Control
		{
			FieldInfo field = typeof(NumericUpDown).GetField(string_0, BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.FlattenHierarchy);
			T result;
			if (field == null)
			{
				result = default(T);
			}
			else
			{
				result = (field.GetValue(class302_0) as T);
			}
			return result;
		}

		// Token: 0x060016FF RID: 5887 RVA: 0x000095EE File Offset: 0x000077EE
		protected void OnPaint(PaintEventArgs e)
		{
			if (!this.control_0.Visible)
			{
				e.Graphics.Clear(this.BackColor);
			}
			base.OnPaint(e);
		}

		// Token: 0x06001700 RID: 5888 RVA: 0x0009AF84 File Offset: 0x00099184
		protected void WndProc(ref Message m)
		{
			if (m.Msg == 522)
			{
				switch (this.enum21_0)
				{
				case Class302.Enum21.const_0:
					base.WndProc(ref m);
					break;
				case Class302.Enum21.const_1:
					if (this.bool_2)
					{
						base.WndProc(ref m);
					}
					break;
				}
			}
			else
			{
				base.WndProc(ref m);
			}
		}

		// Token: 0x170003BF RID: 959
		// (get) Token: 0x06001701 RID: 5889 RVA: 0x0009AFE0 File Offset: 0x000991E0
		// (set) Token: 0x06001702 RID: 5890 RVA: 0x00009617 File Offset: 0x00007817
		[DefaultValue(false)]
		[Category("Behavior")]
		[Description("Automatically select control text when it receives focus.")]
		public bool AutoSelect
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x170003C0 RID: 960
		// (get) Token: 0x06001703 RID: 5891 RVA: 0x0009AFF8 File Offset: 0x000991F8
		// (set) Token: 0x06001704 RID: 5892 RVA: 0x00009622 File Offset: 0x00007822
		[Browsable(false)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		public int SelectionStart
		{
			get
			{
				return this.textBox_0.SelectionStart;
			}
			set
			{
				this.textBox_0.SelectionStart = value;
			}
		}

		// Token: 0x170003C1 RID: 961
		// (get) Token: 0x06001705 RID: 5893 RVA: 0x0009B014 File Offset: 0x00099214
		// (set) Token: 0x06001706 RID: 5894 RVA: 0x00009632 File Offset: 0x00007832
		[Browsable(false)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		public int SelectionLength
		{
			get
			{
				return this.textBox_0.SelectionLength;
			}
			set
			{
				this.textBox_0.SelectionLength = value;
			}
		}

		// Token: 0x170003C2 RID: 962
		// (get) Token: 0x06001707 RID: 5895 RVA: 0x0009B030 File Offset: 0x00099230
		// (set) Token: 0x06001708 RID: 5896 RVA: 0x00009642 File Offset: 0x00007842
		[Browsable(false)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		public string SelectedText
		{
			get
			{
				return this.textBox_0.SelectedText;
			}
			set
			{
				this.textBox_0.SelectedText = value;
			}
		}

		// Token: 0x170003C3 RID: 963
		// (get) Token: 0x06001709 RID: 5897 RVA: 0x0009B04C File Offset: 0x0009924C
		// (set) Token: 0x0600170A RID: 5898 RVA: 0x00009652 File Offset: 0x00007852
		[DefaultValue(typeof(Class302.Enum21), "Always")]
		[Category("Behavior")]
		[Description("Enables MouseWheel only under certain conditions.")]
		public Class302.Enum21 InterceptMouseWheel
		{
			get
			{
				return this.enum21_0;
			}
			set
			{
				this.enum21_0 = value;
			}
		}

		// Token: 0x170003C4 RID: 964
		// (get) Token: 0x0600170B RID: 5899 RVA: 0x0009B064 File Offset: 0x00099264
		// (set) Token: 0x0600170C RID: 5900 RVA: 0x0000965D File Offset: 0x0000785D
		[DefaultValue(typeof(Class302.Enum22), "Always")]
		[Category("Behavior")]
		[Description("Set UpDownButtons visibility mode.")]
		public Class302.Enum22 ShowUpDownButtons
		{
			get
			{
				return this.enum22_0;
			}
			set
			{
				this.enum22_0 = value;
				this.method_0();
			}
		}

		// Token: 0x170003C5 RID: 965
		// (get) Token: 0x0600170D RID: 5901 RVA: 0x0009B07C File Offset: 0x0009927C
		// (set) Token: 0x0600170E RID: 5902 RVA: 0x0000966E File Offset: 0x0000786E
		[DefaultValue(false)]
		[Category("Behavior")]
		[Description("If set, incrementing value will cause it to restart from Minimum when Maximum is reached (and viceversa).")]
		public bool WrapValue
		{
			get
			{
				return this.bool_1;
			}
			set
			{
				this.bool_1 = value;
			}
		}

		// Token: 0x0600170F RID: 5903 RVA: 0x00009679 File Offset: 0x00007879
		protected void OnGotFocus(EventArgs e)
		{
			this.bool_3 = true;
			if (this.bool_0)
			{
				this.textBox_0.SelectAll();
			}
			if (this.enum22_0 == Class302.Enum22.const_2 | this.enum22_0 == Class302.Enum22.const_3)
			{
				this.method_0();
			}
			base.OnGotFocus(e);
		}

		// Token: 0x06001710 RID: 5904 RVA: 0x000096B9 File Offset: 0x000078B9
		protected void OnLostFocus(EventArgs e)
		{
			this.bool_3 = false;
			if (this.enum22_0 == Class302.Enum22.const_2 | this.enum22_0 == Class302.Enum22.const_3)
			{
				this.method_0();
			}
			base.OnLostFocus(e);
		}

		// Token: 0x06001711 RID: 5905 RVA: 0x000096E6 File Offset: 0x000078E6
		protected void OnMouseUp(MouseEventArgs mevent)
		{
			if (this.bool_0 && this.textBox_0.SelectionLength == 0)
			{
				this.textBox_0.SelectAll();
			}
			base.OnMouseUp(mevent);
		}

		// Token: 0x14000083 RID: 131
		// (add) Token: 0x06001712 RID: 5906 RVA: 0x0009B094 File Offset: 0x00099294
		// (remove) Token: 0x06001713 RID: 5907 RVA: 0x0009B0CC File Offset: 0x000992CC
		public new event EventHandler<EventArgs> MouseEnter
		{
			[CompilerGenerated]
			add
			{
				EventHandler<EventArgs> eventHandler = this.eventHandler_0;
				EventHandler<EventArgs> eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler<EventArgs> value2 = (EventHandler<EventArgs>)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler<EventArgs>>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler<EventArgs> eventHandler = this.eventHandler_0;
				EventHandler<EventArgs> eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler<EventArgs> value2 = (EventHandler<EventArgs>)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler<EventArgs>>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x14000084 RID: 132
		// (add) Token: 0x06001714 RID: 5908 RVA: 0x0009B104 File Offset: 0x00099304
		// (remove) Token: 0x06001715 RID: 5909 RVA: 0x0009B13C File Offset: 0x0009933C
		public new event EventHandler<EventArgs> MouseLeave
		{
			[CompilerGenerated]
			add
			{
				EventHandler<EventArgs> eventHandler = this.eventHandler_1;
				EventHandler<EventArgs> eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler<EventArgs> value2 = (EventHandler<EventArgs>)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler<EventArgs>>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler<EventArgs> eventHandler = this.eventHandler_1;
				EventHandler<EventArgs> eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler<EventArgs> value2 = (EventHandler<EventArgs>)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler<EventArgs>>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x14000085 RID: 133
		// (add) Token: 0x06001716 RID: 5910 RVA: 0x0009B174 File Offset: 0x00099374
		// (remove) Token: 0x06001717 RID: 5911 RVA: 0x0009B1AC File Offset: 0x000993AC
		public event CancelEventHandler BeforeValueDecrement
		{
			[CompilerGenerated]
			add
			{
				CancelEventHandler cancelEventHandler = this.cancelEventHandler_0;
				CancelEventHandler cancelEventHandler2;
				do
				{
					cancelEventHandler2 = cancelEventHandler;
					CancelEventHandler value2 = (CancelEventHandler)Delegate.Combine(cancelEventHandler2, value);
					cancelEventHandler = Interlocked.CompareExchange<CancelEventHandler>(ref this.cancelEventHandler_0, value2, cancelEventHandler2);
				}
				while (cancelEventHandler != cancelEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				CancelEventHandler cancelEventHandler = this.cancelEventHandler_0;
				CancelEventHandler cancelEventHandler2;
				do
				{
					cancelEventHandler2 = cancelEventHandler;
					CancelEventHandler value2 = (CancelEventHandler)Delegate.Remove(cancelEventHandler2, value);
					cancelEventHandler = Interlocked.CompareExchange<CancelEventHandler>(ref this.cancelEventHandler_0, value2, cancelEventHandler2);
				}
				while (cancelEventHandler != cancelEventHandler2);
			}
		}

		// Token: 0x14000086 RID: 134
		// (add) Token: 0x06001718 RID: 5912 RVA: 0x0009B1E4 File Offset: 0x000993E4
		// (remove) Token: 0x06001719 RID: 5913 RVA: 0x0009B21C File Offset: 0x0009941C
		public event CancelEventHandler BeforeValueIncrement
		{
			[CompilerGenerated]
			add
			{
				CancelEventHandler cancelEventHandler = this.cancelEventHandler_1;
				CancelEventHandler cancelEventHandler2;
				do
				{
					cancelEventHandler2 = cancelEventHandler;
					CancelEventHandler value2 = (CancelEventHandler)Delegate.Combine(cancelEventHandler2, value);
					cancelEventHandler = Interlocked.CompareExchange<CancelEventHandler>(ref this.cancelEventHandler_1, value2, cancelEventHandler2);
				}
				while (cancelEventHandler != cancelEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				CancelEventHandler cancelEventHandler = this.cancelEventHandler_1;
				CancelEventHandler cancelEventHandler2;
				do
				{
					cancelEventHandler2 = cancelEventHandler;
					CancelEventHandler value2 = (CancelEventHandler)Delegate.Remove(cancelEventHandler2, value);
					cancelEventHandler = Interlocked.CompareExchange<CancelEventHandler>(ref this.cancelEventHandler_1, value2, cancelEventHandler2);
				}
				while (cancelEventHandler != cancelEventHandler2);
			}
		}

		// Token: 0x0600171A RID: 5914 RVA: 0x0009B254 File Offset: 0x00099454
		private void Class302_MouseLeave(object sender, EventArgs e)
		{
			Rectangle rectangle = base.RectangleToScreen(base.ClientRectangle);
			Point mousePosition = Control.MousePosition;
			bool flag = rectangle.Contains(mousePosition);
			if (this.bool_2 ^ flag)
			{
				this.bool_2 = flag;
				if (this.bool_2)
				{
					if (this.eventHandler_0 != null)
					{
						this.eventHandler_0(this, EventArgs.Empty);
					}
				}
				else if (this.eventHandler_1 != null)
				{
					this.eventHandler_1(this, EventArgs.Empty);
				}
			}
			if (this.enum22_0 != Class302.Enum22.const_0)
			{
				this.method_0();
			}
		}

		// Token: 0x0600171B RID: 5915 RVA: 0x0009B2DC File Offset: 0x000994DC
		public void DownButton()
		{
			if (!base.ReadOnly)
			{
				CancelEventArgs cancelEventArgs = new CancelEventArgs();
				if (this.cancelEventHandler_0 != null)
				{
					this.cancelEventHandler_0(this, cancelEventArgs);
				}
				if (!cancelEventArgs.Cancel)
				{
					if (this.bool_1 && base.Value - base.Increment < base.Minimum)
					{
						base.Value = base.Maximum;
					}
					else
					{
						base.DownButton();
					}
				}
			}
		}

		// Token: 0x0600171C RID: 5916 RVA: 0x0009B354 File Offset: 0x00099554
		public void UpButton()
		{
			if (!base.ReadOnly)
			{
				CancelEventArgs cancelEventArgs = new CancelEventArgs();
				if (this.cancelEventHandler_1 != null)
				{
					this.cancelEventHandler_1(this, cancelEventArgs);
				}
				if (!cancelEventArgs.Cancel)
				{
					if (this.bool_1 && base.Value + base.Increment > base.Maximum)
					{
						base.Value = base.Minimum;
					}
					else
					{
						base.UpButton();
					}
				}
			}
		}

		// Token: 0x0600171D RID: 5917 RVA: 0x0009B3CC File Offset: 0x000995CC
		public void method_0()
		{
			bool flag;
			switch (this.enum22_0)
			{
			case Class302.Enum22.const_1:
				flag = this.bool_2;
				break;
			case Class302.Enum22.const_2:
				flag = this.bool_3;
				break;
			case Class302.Enum22.const_3:
				flag = (this.bool_2 | this.bool_3);
				break;
			default:
				flag = true;
				break;
			}
			if (this.control_0.Visible != flag)
			{
				if (flag)
				{
					this.textBox_0.Width = base.ClientRectangle.Width - this.control_0.Width;
				}
				else
				{
					this.textBox_0.Width = base.ClientRectangle.Width;
				}
				this.control_0.Visible = flag;
				this.OnTextBoxResize(this.textBox_0, EventArgs.Empty);
				base.Invalidate();
			}
		}

		// Token: 0x0600171E RID: 5918 RVA: 0x0009B490 File Offset: 0x00099690
		protected void OnTextBoxResize(object source, EventArgs e)
		{
			if (this.textBox_0 != null)
			{
				if (this.enum22_0 == Class302.Enum22.const_0)
				{
					base.OnTextBoxResize(source, e);
				}
				else
				{
					bool flag = this.RightToLeft == RightToLeft.Yes ^ base.UpDownAlign == LeftRightAlignment.Left;
					if (this.bool_2)
					{
						this.textBox_0.Width = base.ClientSize.Width - this.textBox_0.Left - this.control_0.Width - 2;
						if (flag)
						{
							this.textBox_0.Location = new Point(16, this.textBox_0.Location.Y);
						}
					}
					else
					{
						if (flag)
						{
							this.textBox_0.Location = new Point(2, this.textBox_0.Location.Y);
						}
						this.textBox_0.Width = base.ClientSize.Width - this.textBox_0.Left - 2;
					}
				}
			}
		}

		// Token: 0x04000BBC RID: 3004
		private TextBox textBox_0;

		// Token: 0x04000BBD RID: 3005
		private Control control_0;

		// Token: 0x04000BBE RID: 3006
		private bool bool_0;

		// Token: 0x04000BBF RID: 3007
		private Class302.Enum21 enum21_0;

		// Token: 0x04000BC0 RID: 3008
		private Class302.Enum22 enum22_0;

		// Token: 0x04000BC1 RID: 3009
		private bool bool_1;

		// Token: 0x04000BC2 RID: 3010
		[CompilerGenerated]
		private EventHandler<EventArgs> eventHandler_0;

		// Token: 0x04000BC3 RID: 3011
		[CompilerGenerated]
		private EventHandler<EventArgs> eventHandler_1;

		// Token: 0x04000BC4 RID: 3012
		[CompilerGenerated]
		private CancelEventHandler cancelEventHandler_0;

		// Token: 0x04000BC5 RID: 3013
		[CompilerGenerated]
		private CancelEventHandler cancelEventHandler_1;

		// Token: 0x04000BC6 RID: 3014
		private bool bool_2;

		// Token: 0x04000BC7 RID: 3015
		private bool bool_3;

		// Token: 0x0200022C RID: 556
		public enum Enum21
		{
			// Token: 0x04000BC9 RID: 3017
			const_0,
			// Token: 0x04000BCA RID: 3018
			const_1,
			// Token: 0x04000BCB RID: 3019
			const_2
		}

		// Token: 0x0200022D RID: 557
		public enum Enum22
		{
			// Token: 0x04000BCD RID: 3021
			const_0,
			// Token: 0x04000BCE RID: 3022
			const_1,
			// Token: 0x04000BCF RID: 3023
			const_2,
			// Token: 0x04000BD0 RID: 3024
			const_3
		}
	}
}
