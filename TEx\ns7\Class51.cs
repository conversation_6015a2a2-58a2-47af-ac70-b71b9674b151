﻿using System;
using System.Windows.Forms;
using ns11;
using TEx;

namespace ns7
{
	// Token: 0x02000298 RID: 664
	internal abstract class Class51 : Class48
	{
		// Token: 0x06001D87 RID: 7559 RVA: 0x0000C4C9 File Offset: 0x0000A6C9
		public Class51(Panel panel_1, int int_4, int int_5, ChtCtrl chtCtrl_1, bool bool_3) : base(panel_1, int_4, int_5, bool_3)
		{
			this.chtCtrl_0 = chtCtrl_1;
		}

		// Token: 0x06001D88 RID: 7560 RVA: 0x0000C4E0 File Offset: 0x0000A6E0
		protected override void Class48_Click(object sender, EventArgs e)
		{
			this.chtCtrl_0.Focus();
		}

		// Token: 0x06001D89 RID: 7561 RVA: 0x0000C4F0 File Offset: 0x0000A6F0
		protected override void Class48_MouseDown(object sender, EventArgs e)
		{
			base.Class48_MouseDown(sender, e);
			this.chtCtrl_0.Focus();
		}

		// Token: 0x170004AB RID: 1195
		// (get) Token: 0x06001D8A RID: 7562 RVA: 0x000C92C8 File Offset: 0x000C74C8
		// (set) Token: 0x06001D8B RID: 7563 RVA: 0x0000C508 File Offset: 0x0000A708
		public ChtCtrl ChtCtrl
		{
			get
			{
				return this.chtCtrl_0;
			}
			set
			{
				this.chtCtrl_0 = value;
			}
		}

		// Token: 0x04000E8E RID: 3726
		private ChtCtrl chtCtrl_0;
	}
}
