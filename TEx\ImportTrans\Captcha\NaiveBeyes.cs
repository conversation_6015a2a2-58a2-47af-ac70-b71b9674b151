﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using ns4;

namespace TEx.ImportTrans.Captcha
{
	// Token: 0x02000388 RID: 904
	public sealed class NaiveBeyes : Class482
	{
		// Token: 0x0600251A RID: 9498 RVA: 0x0000E553 File Offset: 0x0000C753
		public NaiveBeyes(int vectorLen = 25)
		{
			this.vectorLen = vectorLen;
		}

		// Token: 0x0600251B RID: 9499 RVA: 0x000F731C File Offset: 0x000F551C
		public override string vmethod_5(Bitmap bitmap_0)
		{
			return "";
		}

		// Token: 0x0600251C RID: 9500 RVA: 0x000F7334 File Offset: 0x000F5534
		private Reg method_0(List<double> list_1)
		{
			Reg reg = new Reg("", 0.0);
			double num = 0.0;
			int num2 = -1;
			for (int i = 0; i < this.list_0.Count; i++)
			{
				double num3 = this.list_0[i].method_4(list_1, this.int_0);
				if (num3 > num)
				{
					num = num3;
					num2 = i;
				}
			}
			if (num2 != -1)
			{
				reg.Name = this.list_0[num2].string_0;
				reg.Persent = num;
			}
			return reg;
		}

		// Token: 0x0600251D RID: 9501 RVA: 0x000F73C4 File Offset: 0x000F55C4
		public override Reg[] vmethod_3(Bitmap bitmap_0, List<Rectangle> list_1)
		{
			return ImageBeyes.smethod_2(bitmap_0, list_1).Select(new Func<List<double>, Reg>(this.method_3)).ToArray<Reg>();
		}

		// Token: 0x0600251E RID: 9502 RVA: 0x000F73F4 File Offset: 0x000F55F4
		public override Reg[] vmethod_4(List<List<Point>> list_1)
		{
			return list_1.Select(new Func<List<Point>, List<double>>(NaiveBeyes.<>c.<>9.method_0)).Select(new Func<List<double>, Reg>(this.method_4)).ToArray<Reg>();
		}

		// Token: 0x0600251F RID: 9503 RVA: 0x000F7440 File Offset: 0x000F5640
		public override void vmethod_0(string string_0)
		{
			string[] files = Directory.GetFiles(string_0, "*.bmp");
			this.list_0.Clear();
			foreach (string text in files)
			{
				Path.GetFileName(text);
				string string_ = Path.GetFileNameWithoutExtension(text).Split(new char[]
				{
					'-'
				})[0];
				List<double> list_ = ImageBeyes.smethod_0(text);
				this.method_1(string_, list_);
			}
			this.int_0 = this.list_0.Count<NaiveBeyes.symbol>();
			this.list_0.ForEach(new Action<NaiveBeyes.symbol>(NaiveBeyes.<>c.<>9.method_1));
		}

		// Token: 0x06002520 RID: 9504 RVA: 0x000F74E4 File Offset: 0x000F56E4
		private void method_1(string string_0, List<double> list_1)
		{
			NaiveBeyes.Class487 @class = new NaiveBeyes.Class487();
			@class.string_0 = string_0;
			NaiveBeyes.symbol[] array = this.list_0.Where(new Func<NaiveBeyes.symbol, bool>(@class.method_0)).ToArray<NaiveBeyes.symbol>();
			if (array.Length == 0)
			{
				NaiveBeyes.symbol symbol = new NaiveBeyes.symbol();
				symbol.string_0 = @class.string_0;
				symbol.list_0.Add(list_1);
				this.list_0.Add(symbol);
			}
			else
			{
				array[0].list_0.Add(list_1);
			}
		}

		// Token: 0x06002521 RID: 9505 RVA: 0x000041AE File Offset: 0x000023AE
		private void method_2(string string_0)
		{
		}

		// Token: 0x06002522 RID: 9506 RVA: 0x000F755C File Offset: 0x000F575C
		public override void vmethod_1(string string_0)
		{
			new List<NaiveBeyes.symbol>();
			string[] array = File.ReadAllLines(string_0);
			this.int_0 = array.Length;
			for (int i = 0; i < array.Length; i++)
			{
				string text = array[i].Trim();
				if (!(text == ""))
				{
					string[] array2 = text.Split(new char[]
					{
						'='
					});
					if (array2.Length == 2)
					{
						string string_ = array2[0];
						List<double> list = new List<double>(Convert.ToString(array2[1].Trim()).Split(new char[]
						{
							' '
						}).Select(new Func<string, double>(NaiveBeyes.<>c.<>9.method_2)));
						if (list.Count == this.vectorLen)
						{
							this.method_1(string_, list);
						}
					}
				}
			}
			this.list_0.ForEach(new Action<NaiveBeyes.symbol>(NaiveBeyes.<>c.<>9.method_3));
		}

		// Token: 0x06002523 RID: 9507 RVA: 0x000F7654 File Offset: 0x000F5854
		public override void vmethod_2(string string_0)
		{
			string[] contents = this.list_0.Select(new Func<NaiveBeyes.symbol, string>(NaiveBeyes.<>c.<>9.method_4)).ToArray<string>();
			File.WriteAllLines(string_0, contents);
		}

		// Token: 0x06002524 RID: 9508 RVA: 0x000F769C File Offset: 0x000F589C
		[CompilerGenerated]
		private Reg method_3(List<double> list_1)
		{
			return this.method_0(list_1);
		}

		// Token: 0x06002525 RID: 9509 RVA: 0x000F769C File Offset: 0x000F589C
		[CompilerGenerated]
		private Reg method_4(List<double> list_1)
		{
			return this.method_0(list_1);
		}

		// Token: 0x040011DF RID: 4575
		private int vectorLen;

		// Token: 0x040011E0 RID: 4576
		private List<NaiveBeyes.symbol> list_0 = new List<NaiveBeyes.symbol>();

		// Token: 0x040011E1 RID: 4577
		private int int_0;

		// Token: 0x02000389 RID: 905
		public sealed class GClass0
		{
			// Token: 0x17000645 RID: 1605
			// (get) Token: 0x06002526 RID: 9510 RVA: 0x000F76B4 File Offset: 0x000F58B4
			// (set) Token: 0x06002527 RID: 9511 RVA: 0x0000E56F File Offset: 0x0000C76F
			public List<double> Vector
			{
				get
				{
					return this.list_0;
				}
				set
				{
					this.list_0 = value;
				}
			}

			// Token: 0x06002528 RID: 9512 RVA: 0x0000E57A File Offset: 0x0000C77A
			public GClass0(List<double> list_1)
			{
				this.list_0 = list_1;
			}

			// Token: 0x040011E2 RID: 4578
			private List<double> list_0;
		}

		// Token: 0x0200038A RID: 906
		public sealed class symbol
		{
			// Token: 0x06002529 RID: 9513 RVA: 0x0000E58B File Offset: 0x0000C78B
			public symbol()
			{
				this.string_0 = "";
				this.double_0 = new double[25];
				this.double_1 = new double[25];
				this.list_0 = new List<List<double>>();
			}

			// Token: 0x0600252A RID: 9514 RVA: 0x000F76CC File Offset: 0x000F58CC
			public void method_0()
			{
				NaiveBeyes.symbol.Class485 @class = new NaiveBeyes.symbol.Class485();
				@class.symbol_0 = this;
				@class.int_0 = 0;
				while (@class.int_0 < this.list_0[0].Count)
				{
					double[] array = this.list_0.Select(new Func<List<double>, double>(@class.method_0)).ToArray<double>();
					this.double_1[@class.int_0] = array.Average();
					this.double_0[@class.int_0] = array.Sum(new Func<double, double>(@class.method_1)) / (double)(array.Length - 1);
					int int_ = @class.int_0;
					@class.int_0 = int_ + 1;
				}
			}

			// Token: 0x0600252B RID: 9515 RVA: 0x000F7770 File Offset: 0x000F5970
			private double method_1(double[] double_2, double double_3)
			{
				NaiveBeyes.symbol.Class486 @class = new NaiveBeyes.symbol.Class486();
				@class.double_0 = double_2.Average();
				double num = double_2.Sum(new Func<double, double>(@class.method_0)) / (double)(double_2.Length - 1);
				if (num == 0.0)
				{
					num = 1E-11;
				}
				double num2 = 1.0 / Math.Pow(6.283185307179586 * num, 0.5);
				double num3 = Math.Exp(-Math.Pow(double_3 - @class.double_0, 2.0) / (2.0 * num));
				return num2 * num3;
			}

			// Token: 0x0600252C RID: 9516 RVA: 0x000F7814 File Offset: 0x000F5A14
			private double method_2(int int_0, double double_2)
			{
				double num = this.double_1[int_0];
				double num2 = this.double_0[int_0];
				double result;
				if (num2 != 0.0)
				{
					double num3 = 1.0 / Math.Pow(6.283185307179586 * num2, 0.5);
					double num4 = Math.Exp(-Math.Pow(double_2 - num, 2.0) / (2.0 * num2));
					result = num3 * num4;
				}
				else if (num == double_2)
				{
					result = 1.0;
				}
				else
				{
					result = 1E-09;
				}
				return result;
			}

			// Token: 0x0600252D RID: 9517 RVA: 0x000F78A8 File Offset: 0x000F5AA8
			public string method_3()
			{
				string text = "";
				foreach (string str in this.list_0.Select(new Func<List<double>, string>(NaiveBeyes.symbol.<>c.<>9.method_0)))
				{
					text = text + this.string_0 + "=" + str;
				}
				return text;
			}

			// Token: 0x0600252E RID: 9518 RVA: 0x000F7934 File Offset: 0x000F5B34
			public double method_4(List<double> list_1, int int_0)
			{
				double num = 1.0;
				for (int i = 0; i < list_1.Count; i++)
				{
					num *= this.method_2(i, list_1[i]);
				}
				return num * (Convert.ToDouble(this.list_0.Count) / (double)int_0);
			}

			// Token: 0x040011E3 RID: 4579
			public string string_0;

			// Token: 0x040011E4 RID: 4580
			public List<List<double>> list_0;

			// Token: 0x040011E5 RID: 4581
			public double[] double_0;

			// Token: 0x040011E6 RID: 4582
			public double[] double_1;

			// Token: 0x0200038B RID: 907
			[CompilerGenerated]
			private sealed class Class485
			{
				// Token: 0x06002530 RID: 9520 RVA: 0x000F7988 File Offset: 0x000F5B88
				internal double method_0(List<double> list_0)
				{
					return list_0[this.int_0];
				}

				// Token: 0x06002531 RID: 9521 RVA: 0x000F79A8 File Offset: 0x000F5BA8
				internal double method_1(double double_0)
				{
					return Math.Pow(double_0 - this.symbol_0.double_1[this.int_0], 2.0);
				}

				// Token: 0x040011E7 RID: 4583
				public int int_0;

				// Token: 0x040011E8 RID: 4584
				public NaiveBeyes.symbol symbol_0;
			}

			// Token: 0x0200038C RID: 908
			[CompilerGenerated]
			private sealed class Class486
			{
				// Token: 0x06002533 RID: 9523 RVA: 0x000F79DC File Offset: 0x000F5BDC
				internal double method_0(double double_1)
				{
					return Math.Pow(double_1 - this.double_0, 2.0);
				}

				// Token: 0x040011E9 RID: 4585
				public double double_0;
			}
		}

		// Token: 0x0200038F RID: 911
		[CompilerGenerated]
		private sealed class Class487
		{
			// Token: 0x06002540 RID: 9536 RVA: 0x000F7A98 File Offset: 0x000F5C98
			internal bool method_0(NaiveBeyes.symbol symbol_0)
			{
				return symbol_0.string_0 == this.string_0;
			}

			// Token: 0x040011F3 RID: 4595
			public string string_0;
		}
	}
}
