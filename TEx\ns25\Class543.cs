﻿using System;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using ns28;

namespace ns25
{
	// Token: 0x0200040A RID: 1034
	internal sealed class Class543
	{
		// Token: 0x0600280E RID: 10254
		[DllImport("shell32")]
		private static extern int ExtractIconEx(string string_0, int int_6, out IntPtr intptr_0, out IntPtr intptr_1, int int_7);

		// Token: 0x0600280F RID: 10255
		[DllImport("user32", CharSet = CharSet.Unicode)]
		private static extern int DrawText(IntPtr intptr_0, string string_0, int int_6, ref Class543.Struct29 struct29_0, int int_7);

		// Token: 0x06002810 RID: 10256
		[DllImport("gdi32.dll")]
		private static extern IntPtr SelectObject(IntPtr intptr_0, IntPtr intptr_1);

		// Token: 0x06002811 RID: 10257
		[DllImport("kernel32.Dll")]
		private static extern short GetVersionEx(ref Class543.Struct30 struct30_1);

		// Token: 0x06002812 RID: 10258
		[DllImport("user32.dll")]
		private static extern int GetSystemMetrics(int int_6);

		// Token: 0x06002813 RID: 10259
		[DllImport("kernel32.dll")]
		private static extern void GetSystemInfo(ref Class543.Struct31 struct31_0);

		// Token: 0x170006E0 RID: 1760
		// (get) Token: 0x06002814 RID: 10260 RVA: 0x001034BC File Offset: 0x001016BC
		private static Class543.Struct30 VersionInfo
		{
			get
			{
				if (!Class543.bool_0)
				{
					Class543.struct30_0 = default(Class543.Struct30);
					try
					{
						Class543.struct30_0.int_0 = Marshal.SizeOf(typeof(Class543.Struct30));
						Class543.GetVersionEx(ref Class543.struct30_0);
						Class543.bool_0 = true;
					}
					catch
					{
					}
				}
				return Class543.struct30_0;
			}
		}

		// Token: 0x170006E1 RID: 1761
		// (get) Token: 0x06002815 RID: 10261 RVA: 0x00103520 File Offset: 0x00101720
		internal static bool IsX64
		{
			get
			{
				bool result;
				try
				{
					Class543.Struct31 @struct = default(Class543.Struct31);
					Class543.GetSystemInfo(ref @struct);
					result = (@struct.ushort_0 == 9);
				}
				catch
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x170006E2 RID: 1762
		// (get) Token: 0x06002816 RID: 10262 RVA: 0x00103560 File Offset: 0x00101760
		internal static bool IsServerR2
		{
			get
			{
				bool result;
				try
				{
					result = (Class543.GetSystemMetrics(89) != 0);
				}
				catch
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x170006E3 RID: 1763
		// (get) Token: 0x06002817 RID: 10263 RVA: 0x0000F80D File Offset: 0x0000DA0D
		internal static bool IsWorkstation
		{
			get
			{
				return Class543.VersionInfo.byte_0 == 1;
			}
		}

		// Token: 0x170006E4 RID: 1764
		// (get) Token: 0x06002818 RID: 10264 RVA: 0x0000F81C File Offset: 0x0000DA1C
		internal static string ServicePack
		{
			get
			{
				return Class543.VersionInfo.string_0;
			}
		}

		// Token: 0x06002819 RID: 10265 RVA: 0x00103590 File Offset: 0x00101790
		public static Icon smethod_0()
		{
			Icon result;
			try
			{
				result = Class543.smethod_1();
			}
			catch (Exception)
			{
				result = Class537.smethod_1("default");
			}
			return result;
		}

		// Token: 0x0600281A RID: 10266 RVA: 0x001035C4 File Offset: 0x001017C4
		private static Icon smethod_1()
		{
			IntPtr zero = IntPtr.Zero;
			IntPtr zero2 = IntPtr.Zero;
			if (Class543.ExtractIconEx(Application.ExecutablePath, -1, out zero2, out zero2, 1) > 0)
			{
				Class543.ExtractIconEx(Application.ExecutablePath, 0, out zero, out zero2, 1);
				if (zero != IntPtr.Zero)
				{
					return Icon.FromHandle(zero);
				}
			}
			return null;
		}

		// Token: 0x0600281B RID: 10267 RVA: 0x00103618 File Offset: 0x00101818
		internal static int smethod_2(Graphics graphics_0, string string_0, Font font_0, int int_6)
		{
			try
			{
				return Class543.smethod_4(graphics_0, string_0, font_0, int_6);
			}
			catch (Exception)
			{
				try
				{
					return Convert.ToInt32((double)Class543.smethod_3(graphics_0, string_0, font_0, int_6) * 1.1);
				}
				catch (Exception)
				{
				}
			}
			return 0;
		}

		// Token: 0x0600281C RID: 10268 RVA: 0x00103674 File Offset: 0x00101874
		private static int smethod_3(Graphics graphics_0, string string_0, Font font_0, int int_6)
		{
			return Size.Ceiling(graphics_0.MeasureString(string_0, font_0, int_6)).Height;
		}

		// Token: 0x0600281D RID: 10269 RVA: 0x00103698 File Offset: 0x00101898
		private static int smethod_4(Graphics graphics_0, string string_0, Font font_0, int int_6)
		{
			Class543.Struct29 @struct = new Class543.Struct29(new Rectangle(0, 0, int_6, 10000));
			IntPtr hdc = graphics_0.GetHdc();
			IntPtr intptr_ = font_0.ToHfont();
			IntPtr intptr_2 = Class543.SelectObject(hdc, intptr_);
			Class543.DrawText(hdc, string_0, -1, ref @struct, 3088);
			Class543.SelectObject(hdc, intptr_2);
			graphics_0.ReleaseHdc(hdc);
			return @struct.int_3 - @struct.int_1;
		}

		// Token: 0x040013DB RID: 5083
		private const int int_0 = 16;

		// Token: 0x040013DC RID: 5084
		private const int int_1 = 1024;

		// Token: 0x040013DD RID: 5085
		private const int int_2 = 2048;

		// Token: 0x040013DE RID: 5086
		private const int int_3 = 1;

		// Token: 0x040013DF RID: 5087
		private const int int_4 = 89;

		// Token: 0x040013E0 RID: 5088
		private const int int_5 = 9;

		// Token: 0x040013E1 RID: 5089
		private static bool bool_0;

		// Token: 0x040013E2 RID: 5090
		private static Class543.Struct30 struct30_0;

		// Token: 0x0200040B RID: 1035
		private struct Struct29
		{
			// Token: 0x0600281F RID: 10271 RVA: 0x0000F828 File Offset: 0x0000DA28
			public Struct29(Rectangle rectangle_0)
			{
				this.int_0 = rectangle_0.Left;
				this.int_1 = rectangle_0.Top;
				this.int_3 = rectangle_0.Bottom;
				this.int_2 = rectangle_0.Right;
			}

			// Token: 0x040013E3 RID: 5091
			public int int_0;

			// Token: 0x040013E4 RID: 5092
			public int int_1;

			// Token: 0x040013E5 RID: 5093
			public int int_2;

			// Token: 0x040013E6 RID: 5094
			public int int_3;
		}

		// Token: 0x0200040C RID: 1036
		private struct Struct30
		{
			// Token: 0x040013E7 RID: 5095
			public int int_0;

			// Token: 0x040013E8 RID: 5096
			public uint uint_0;

			// Token: 0x040013E9 RID: 5097
			public uint uint_1;

			// Token: 0x040013EA RID: 5098
			public uint uint_2;

			// Token: 0x040013EB RID: 5099
			public uint uint_3;

			// Token: 0x040013EC RID: 5100
			[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 128)]
			public string string_0;

			// Token: 0x040013ED RID: 5101
			public ushort ushort_0;

			// Token: 0x040013EE RID: 5102
			public ushort ushort_1;

			// Token: 0x040013EF RID: 5103
			public ushort ushort_2;

			// Token: 0x040013F0 RID: 5104
			public byte byte_0;

			// Token: 0x040013F1 RID: 5105
			private byte byte_1;
		}

		// Token: 0x0200040D RID: 1037
		public struct Struct31
		{
			// Token: 0x040013F2 RID: 5106
			public ushort ushort_0;

			// Token: 0x040013F3 RID: 5107
			private ushort ushort_1;

			// Token: 0x040013F4 RID: 5108
			public uint uint_0;

			// Token: 0x040013F5 RID: 5109
			public IntPtr intptr_0;

			// Token: 0x040013F6 RID: 5110
			public IntPtr intptr_1;

			// Token: 0x040013F7 RID: 5111
			public IntPtr intptr_2;

			// Token: 0x040013F8 RID: 5112
			public uint uint_1;

			// Token: 0x040013F9 RID: 5113
			public uint uint_2;

			// Token: 0x040013FA RID: 5114
			public uint uint_3;

			// Token: 0x040013FB RID: 5115
			public ushort ushort_2;

			// Token: 0x040013FC RID: 5116
			public ushort ushort_3;
		}
	}
}
