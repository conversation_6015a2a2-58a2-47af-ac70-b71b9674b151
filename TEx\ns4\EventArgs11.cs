﻿using System;

namespace ns4
{
	// Token: 0x02000220 RID: 544
	internal sealed class EventArgs11 : EventArgs
	{
		// Token: 0x06001690 RID: 5776 RVA: 0x0000931C File Offset: 0x0000751C
		public EventArgs11(DateTime dateTime_2, DateTime dateTime_3)
		{
			this.dateTime_0 = dateTime_2;
			this.dateTime_1 = dateTime_3;
		}

		// Token: 0x170003AB RID: 939
		// (get) Token: 0x06001691 RID: 5777 RVA: 0x00097470 File Offset: 0x00095670
		// (set) Token: 0x06001692 RID: 5778 RVA: 0x00009334 File Offset: 0x00007534
		public DateTime Range1
		{
			get
			{
				return this.dateTime_0;
			}
			set
			{
				this.dateTime_0 = value;
			}
		}

		// Token: 0x170003AC RID: 940
		// (get) Token: 0x06001693 RID: 5779 RVA: 0x00097488 File Offset: 0x00095688
		// (set) Token: 0x06001694 RID: 5780 RVA: 0x0000933F File Offset: 0x0000753F
		public DateTime Range2
		{
			get
			{
				return this.dateTime_1;
			}
			set
			{
				this.dateTime_1 = value;
			}
		}

		// Token: 0x04000B80 RID: 2944
		private DateTime dateTime_0;

		// Token: 0x04000B81 RID: 2945
		private DateTime dateTime_1;
	}
}
