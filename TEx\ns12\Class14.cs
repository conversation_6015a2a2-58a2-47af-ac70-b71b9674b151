﻿using System;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace ns12
{
	// Token: 0x02000035 RID: 53
	internal static class Class14
	{
		// Token: 0x06000172 RID: 370
		[DllImport("user32.dll")]
		public static extern int SendMessage(IntPtr intptr_0, int int_1, bool bool_0, int int_2);

		// Token: 0x06000173 RID: 371 RVA: 0x00016BA8 File Offset: 0x00014DA8
		public static void smethod_0(this Control control_0)
		{
			if (control_0 != null && !control_0.IsDisposed)
			{
				try
				{
					Class14.SendMessage(control_0.<PERSON>le, 11, false, 0);
				}
				catch
				{
				}
			}
		}

		// Token: 0x06000174 RID: 372 RVA: 0x00016BE8 File Offset: 0x00014DE8
		public static void smethod_1(this Control control_0)
		{
			if (control_0 != null && !control_0.IsDisposed)
			{
				try
				{
					Class14.SendMessage(control_0.Handle, 11, true, 0);
					control_0.Refresh();
				}
				catch
				{
				}
			}
		}

		// Token: 0x06000175 RID: 373 RVA: 0x00003034 File Offset: 0x00001234
		public static void smethod_2(this Control control_0, bool bool_0)
		{
			if (control_0 != null && !control_0.IsDisposed)
			{
				control_0.GetType().GetProperty("DoubleBuffered", BindingFlags.Instance | BindingFlags.NonPublic).SetValue(control_0, bool_0, null);
			}
		}

		// Token: 0x04000091 RID: 145
		private const int int_0 = 11;
	}
}
