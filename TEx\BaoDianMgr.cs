﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Windows.Forms;
using System.Xml.Linq;
using DevComponents.AdvTree;
using ns13;
using ns22;
using ns28;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x0200005C RID: 92
	internal static class BaoDianMgr
	{
		// Token: 0x0600032C RID: 812 RVA: 0x0001E540 File Offset: 0x0001C740
		public static List<Node> smethod_0()
		{
			List<Node> list = new List<Node>();
			string text = Path.Combine(TApp.UserAcctFolder, BaoDianMgr.string_0);
			if (File.Exists(text))
			{
				List<Node> result;
				try
				{
					XDocument xdocument = XDocument.Load(text);
					if (xdocument == null)
					{
						goto IL_180;
					}
					foreach (XElement xelement in xdocument.Root.Elements())
					{
						if (xelement.Name == "group")
						{
							Node node = BaoDianMgr.smethod_1(xelement);
							list.Add(node);
							foreach (XElement xelement2 in xelement.Elements())
							{
								if (xelement2.Name == "group")
								{
									Node node2 = BaoDianMgr.smethod_1(xelement2);
									node.Nodes.Add(node2);
									using (IEnumerator<XElement> enumerator3 = xelement2.Elements().GetEnumerator())
									{
										while (enumerator3.MoveNext())
										{
											XElement xelement_ = enumerator3.Current;
											Node node3 = BaoDianMgr.smethod_3(xelement_);
											node2.Nodes.Add(node3);
										}
										continue;
									}
								}
								if (xelement2.Name == "item")
								{
									Node node4 = BaoDianMgr.smethod_3(xelement2);
									node.Nodes.Add(node4);
								}
							}
						}
					}
					result = list;
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
					goto IL_180;
				}
				return result;
			}
			IL_180:
			list.Add(BaoDianMgr.smethod_2("默认分组"));
			return list;
		}

		// Token: 0x0600032D RID: 813 RVA: 0x0001E748 File Offset: 0x0001C948
		private static Node smethod_1(XElement xelement_0)
		{
			return BaoDianMgr.smethod_2(xelement_0.Attribute("Text").Value.ToString());
		}

		// Token: 0x0600032E RID: 814 RVA: 0x0001E778 File Offset: 0x0001C978
		public static Node smethod_2(string string_1)
		{
			return new Node
			{
				Text = string_1,
				Image = Class372.FolderClosed,
				ImageExpanded = Class372.FolderOpen,
				ExpandVisibility = eNodeExpandVisibility.Auto
			};
		}

		// Token: 0x0600032F RID: 815 RVA: 0x0001E7B4 File Offset: 0x0001C9B4
		private static Node smethod_3(XElement xelement_0)
		{
			return BaoDianMgr.smethod_4(BaoDianMgr.smethod_5(xelement_0));
		}

		// Token: 0x06000330 RID: 816 RVA: 0x0001E7D0 File Offset: 0x0001C9D0
		public static Node smethod_4(BaoDian baoDian_0)
		{
			Node result;
			if (baoDian_0 != null)
			{
				Node node = new Node();
				node.Text = baoDian_0.Name;
				node.Image = Class372.Book_angleHS;
				node.Tag = baoDian_0;
				node.Tooltip = "双击加载宝典行情";
				if (Base.Data.UsrStkSymbols.ContainsKey(baoDian_0.SymbolID))
				{
					string cnname = Base.Data.UsrStkSymbols[baoDian_0.SymbolID].CNName;
					node.Cells.Add(new Cell(cnname));
				}
				node.Cells.Add(new Cell(baoDian_0.SymbolTime.ToString("yyyy-MM-dd")));
				result = node;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06000331 RID: 817 RVA: 0x0001E87C File Offset: 0x0001CA7C
		private static BaoDian smethod_5(XElement xelement_0)
		{
			BaoDian baoDian = new BaoDian();
			try
			{
				baoDian.Group = xelement_0.Attribute(Utility.GetPropertyName<BaoDian>((BaoDian p) => p.Group)).Value.ToString();
				baoDian.Name = xelement_0.Attribute(Utility.GetPropertyName<BaoDian>((BaoDian p) => p.Name)).Value.ToString();
				baoDian.UID = xelement_0.Attribute(Utility.GetPropertyName<BaoDian>((BaoDian p) => p.UID)).Value.ToString();
				baoDian.Note = xelement_0.Attribute(Utility.GetPropertyName<BaoDian>((BaoDian p) => p.Note)).Value.ToString();
				baoDian.SymbolID = int.Parse(xelement_0.Attribute(Utility.GetPropertyName<BaoDian>((BaoDian prop) => (object)prop.SymbolID)).Value);
				baoDian.SymbolTime = DateTime.Parse(xelement_0.Attribute(Utility.GetPropertyName<BaoDian>((BaoDian prop) => (object)prop.SymbolTime)).Value);
				baoDian.PeriodType = (PeriodType)Enum.Parse(typeof(PeriodType), xelement_0.Attribute(Utility.GetPropertyName<BaoDian>((BaoDian prop) => (object)prop.PeriodType)).Value);
				try
				{
					baoDian.PeriodUnit = new int?(int.Parse(xelement_0.Attribute(Utility.GetPropertyName<BaoDian>((BaoDian prop) => (object)prop.PeriodUnit)).Value));
				}
				catch
				{
					baoDian.PeriodUnit = null;
				}
				XAttribute xattribute = xelement_0.Attribute(Utility.GetPropertyName<BaoDian>((BaoDian p) => p.ScreenShot));
				if (xattribute != null && !string.IsNullOrEmpty(xattribute.Value))
				{
					baoDian.ScreenShot = Utility.Base64StrToBitmap(xattribute.Value, false, CompressAlgm.GZip);
				}
				string text = xelement_0.Attribute(Utility.GetPropertyName<BaoDian>((BaoDian p) => (object)p.CreateTime)).Value.ToString();
				try
				{
					baoDian.CreateTime = DateTime.Parse(text);
				}
				catch
				{
					try
					{
						int year = int.Parse(text.Substring(0, 4));
						int month = int.Parse(text.Substring(4, 2));
						int day = int.Parse(text.Substring(6, 2));
						int hour = int.Parse(text.Substring(8, 2));
						int minute = int.Parse(text.Substring(10, 2));
						int second = int.Parse(text.Substring(12, 2));
						baoDian.CreateTime = new DateTime(year, month, day, hour, minute, second);
					}
					catch
					{
					}
				}
			}
			catch (Exception exception_)
			{
				Class46.smethod_4(exception_, false, "读取宝典Xml发生错误");
				baoDian = null;
			}
			return baoDian;
		}

		// Token: 0x06000332 RID: 818 RVA: 0x0001ED94 File Offset: 0x0001CF94
		public static void smethod_6(ChtCtrl chtCtrl_0, string string_1 = null)
		{
			try
			{
				Bitmap bitmap = new Bitmap(chtCtrl_0.ClientSize.Width, chtCtrl_0.ClientSize.Height);
				chtCtrl_0.DrawToBitmap(bitmap, new Rectangle(0, 0, chtCtrl_0.ClientRectangle.Width, chtCtrl_0.ClientRectangle.Height));
				if (bitmap.Width > 600)
				{
					bitmap = Utility.SizeImage(bitmap, 600, Convert.ToInt32(Math.Round((double)(bitmap.Height * 600) * 1.0 / (double)bitmap.Width)));
				}
				Bitmap bitmap_ = bitmap.Clone(new Rectangle(0, 0, bitmap.Width, bitmap.Height), PixelFormat.Format4bppIndexed);
				AddBaoDianWnd addBaoDianWnd = new AddBaoDianWnd();
				TransTabs transTabs = Base.UI.TransTabs;
				if (transTabs == null)
				{
					transTabs = Base.UI.SwitchedBehindTransTabs;
				}
				List<string> list_ = transTabs.method_94();
				addBaoDianWnd.method_0(chtCtrl_0, list_, bitmap_, string_1);
				addBaoDianWnd.StartPosition = FormStartPosition.CenterParent;
				addBaoDianWnd.ShowDialog(Base.UI.MainForm);
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x06000333 RID: 819 RVA: 0x0001EEA4 File Offset: 0x0001D0A4
		public static void smethod_7(Node node_0)
		{
			AddBaoDianWnd addBaoDianWnd = new AddBaoDianWnd();
			List<string> list_ = Base.UI.TransTabs.method_94();
			addBaoDianWnd.method_1(node_0, list_);
			addBaoDianWnd.StartPosition = FormStartPosition.CenterParent;
			addBaoDianWnd.ShowDialog(Base.UI.MainForm);
		}

		// Token: 0x06000334 RID: 820 RVA: 0x0000360E File Offset: 0x0000180E
		public static void smethod_8()
		{
			BaoDianMgr.smethod_9();
		}

		// Token: 0x06000335 RID: 821 RVA: 0x0001EEE0 File Offset: 0x0001D0E0
		private static void smethod_9()
		{
			if (Base.UI.TransTabs != null)
			{
				string fileName = Path.Combine(TApp.UserAcctFolder, BaoDianMgr.string_0);
				XDocument xdocument = new XDocument();
				XElement xelement = new XElement("root");
				xdocument.Add(xelement);
				foreach (object obj in Base.UI.TransTabs.method_95().Nodes)
				{
					Node node = (Node)obj;
					XElement xelement2 = BaoDianMgr.smethod_10(node);
					foreach (object obj2 in node.Nodes)
					{
						Node node2 = (Node)obj2;
						BaoDian baoDian = node2.Tag as BaoDian;
						if (baoDian == null)
						{
							XElement xelement3 = BaoDianMgr.smethod_10(node2);
							foreach (object obj3 in node2.Nodes)
							{
								XElement content = BaoDianMgr.smethod_11(((Node)obj3).Tag as BaoDian);
								xelement3.Add(content);
							}
							xelement2.Add(xelement3);
						}
						else
						{
							XElement content2 = BaoDianMgr.smethod_11(baoDian);
							xelement2.Add(content2);
						}
					}
					xelement.Add(xelement2);
				}
				xdocument.Save(fileName);
			}
		}

		// Token: 0x06000336 RID: 822 RVA: 0x0001F0A4 File Offset: 0x0001D2A4
		private static XElement smethod_10(Node node_0)
		{
			XElement xelement = new XElement("group");
			xelement.Add(new XAttribute("Text", node_0.Text));
			xelement.Add(new XAttribute("FullPath", node_0.FullPath));
			return xelement;
		}

		// Token: 0x06000337 RID: 823 RVA: 0x0001F0FC File Offset: 0x0001D2FC
		private static XElement smethod_11(BaoDian baoDian_0)
		{
			XElement xelement = new XElement("item");
			xelement.Add(new XAttribute(Utility.GetPropertyName<BaoDian>((BaoDian p) => p.Group), baoDian_0.Group));
			xelement.Add(new XAttribute(Utility.GetPropertyName<BaoDian>((BaoDian p) => p.Name), baoDian_0.Name));
			if (string.IsNullOrEmpty(baoDian_0.UID))
			{
				baoDian_0.UID = Utility.GetUniqueString(20);
			}
			xelement.Add(new XAttribute(Utility.GetPropertyName<BaoDian>((BaoDian p) => p.UID), baoDian_0.UID));
			xelement.Add(new XAttribute(Utility.GetPropertyName<BaoDian>((BaoDian p) => (object)p.SymbolID), baoDian_0.SymbolID));
			xelement.Add(new XAttribute(Utility.GetPropertyName<BaoDian>((BaoDian p) => (object)p.SymbolTime), baoDian_0.SymbolTime.ToString("yyyy-MM-dd HH:mm")));
			xelement.Add(new XAttribute(Utility.GetPropertyName<BaoDian>((BaoDian p) => (object)p.PeriodType), baoDian_0.PeriodType));
			xelement.Add(new XAttribute(Utility.GetPropertyName<BaoDian>((BaoDian p) => (object)p.PeriodUnit), (baoDian_0.PeriodUnit == null) ? "1" : baoDian_0.PeriodUnit.ToString()));
			xelement.Add(new XAttribute(Utility.GetPropertyName<BaoDian>((BaoDian p) => p.Note), baoDian_0.Note));
			xelement.Add(new XAttribute(Utility.GetPropertyName<BaoDian>((BaoDian p) => (object)p.CreateTime), baoDian_0.CreateTime.ToString("yyyy-MM-dd HH:mm:ss")));
			string value = Utility.BitmapToBase64Str(baoDian_0.ScreenShot, false, CompressAlgm.GZip);
			xelement.Add(new XAttribute(Utility.GetPropertyName<BaoDian>((BaoDian p) => p.ScreenShot), value));
			return xelement;
		}

		// Token: 0x04000122 RID: 290
		public static Action<BaoDian> action_0;

		// Token: 0x04000123 RID: 291
		public static Action<Node> action_1;

		// Token: 0x04000124 RID: 292
		public static string string_0 = "baodian.dat";
	}
}
