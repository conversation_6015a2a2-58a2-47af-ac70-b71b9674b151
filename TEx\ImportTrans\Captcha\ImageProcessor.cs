﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;

namespace TEx.ImportTrans.Captcha
{
	// Token: 0x0200039D RID: 925
	public static class ImageProcessor
	{
		// Token: 0x0600257D RID: 9597 RVA: 0x000F8A6C File Offset: 0x000F6C6C
		public static void smethod_0(List<List<Point>> list_0)
		{
			for (int i = 0; i < list_0.Count; i++)
			{
				ImageProcessor.smethod_2(list_0[i]);
			}
			list_0.RemoveAll(new Predicate<List<Point>>(ImageProcessor.<>c.<>9.method_0));
		}

		// Token: 0x0600257E RID: 9598 RVA: 0x000F8AC0 File Offset: 0x000F6CC0
		public static List<Point> smethod_1(Bitmap bitmap_0, Rectangle rectangle_0)
		{
			List<Point> list = new List<Point>();
			for (int i = rectangle_0.Left; i < rectangle_0.Right + 1; i++)
			{
				for (int j = rectangle_0.Top; j < rectangle_0.Bottom + 1; j++)
				{
					byte r = bitmap_0.GetPixel(i, j).R;
					if (r == 0)
					{
						list.Add(new Point(i, j));
					}
					else if (r != 255)
					{
						throw new Exception("不是二值图像");
					}
				}
			}
			return list;
		}

		// Token: 0x0600257F RID: 9599 RVA: 0x000F8B48 File Offset: 0x000F6D48
		private static void smethod_2(List<Point> list_0)
		{
			if (list_0 != null && list_0.Count >= 1)
			{
				int num = 3;
				for (;;)
				{
					ImageProcessor.Class496 @class = new ImageProcessor.Class496();
					if (list_0.Count == 0)
					{
						break;
					}
					@class.int_0 = list_0.Max(new Func<Point, int>(ImageProcessor.<>c.<>9.method_1));
					IEnumerable<Point> source = list_0.Where(new Func<Point, bool>(@class.method_0));
					int num2 = source.Count<Point>();
					if (num2 < num && num2 > 0)
					{
						list_0.RemoveAll(new Predicate<Point>(@class.method_1));
					}
					else
					{
						if (num2 != num)
						{
							break;
						}
						int num3 = source.Min(new Func<Point, int>(ImageProcessor.<>c.<>9.method_2));
						if (source.Max(new Func<Point, int>(ImageProcessor.<>c.<>9.method_3)) - num3 == 2)
						{
							if (source.Sum(new Func<Point, int>(ImageProcessor.<>c.<>9.method_4)) == (num3 + 1) * 3)
							{
								break;
							}
						}
						list_0.RemoveAll(new Predicate<Point>(@class.method_2));
					}
				}
				for (;;)
				{
					ImageProcessor.Class497 class2 = new ImageProcessor.Class497();
					if (list_0.Count == 0)
					{
						break;
					}
					class2.int_0 = list_0.Min(new Func<Point, int>(ImageProcessor.<>c.<>9.method_5));
					IEnumerable<Point> source2 = list_0.Where(new Func<Point, bool>(class2.method_0));
					int num4 = source2.Count<Point>();
					if (num4 < num && num4 > 0)
					{
						list_0.RemoveAll(new Predicate<Point>(class2.method_1));
					}
					else
					{
						if (num4 != num)
						{
							break;
						}
						int num5 = source2.Min(new Func<Point, int>(ImageProcessor.<>c.<>9.method_6));
						if (source2.Max(new Func<Point, int>(ImageProcessor.<>c.<>9.method_7)) - num5 == 2)
						{
							if (source2.Sum(new Func<Point, int>(ImageProcessor.<>c.<>9.method_8)) == (num5 + 1) * 3)
							{
								break;
							}
						}
						list_0.RemoveAll(new Predicate<Point>(class2.method_2));
					}
				}
				for (;;)
				{
					ImageProcessor.Class498 class3 = new ImageProcessor.Class498();
					if (list_0.Count == 0)
					{
						break;
					}
					class3.int_0 = list_0.Max(new Func<Point, int>(ImageProcessor.<>c.<>9.method_9));
					int num6 = list_0.Where(new Func<Point, bool>(class3.method_0)).Count<Point>();
					if (num6 >= 2 || num6 <= 0)
					{
						break;
					}
					list_0.RemoveAll(new Predicate<Point>(class3.method_1));
				}
				for (;;)
				{
					ImageProcessor.Class499 class4 = new ImageProcessor.Class499();
					if (list_0.Count == 0)
					{
						break;
					}
					class4.int_0 = list_0.Min(new Func<Point, int>(ImageProcessor.<>c.<>9.method_10));
					int num7 = list_0.Where(new Func<Point, bool>(class4.method_0)).Count<Point>();
					if (num7 >= 2 || num7 <= 0)
					{
						break;
					}
					list_0.RemoveAll(new Predicate<Point>(class4.method_1));
				}
			}
		}

		// Token: 0x06002580 RID: 9600 RVA: 0x000F8E9C File Offset: 0x000F709C
		public static void smethod_3(List<List<Point>> list_0)
		{
			ImageProcessor.Class500 @class = new ImageProcessor.Class500();
			@class.int_0 = 20;
			list_0.RemoveAll(new Predicate<List<Point>>(@class.method_0));
		}

		// Token: 0x06002581 RID: 9601 RVA: 0x000F8ECC File Offset: 0x000F70CC
		public static void smethod_4(Bitmap bitmap_0, int int_0)
		{
			ImageProcessor.smethod_7(bitmap_0, int_0, 255, ImageProcessor.GEnum0.const_0);
			int num = 0;
			int num2 = 0;
			for (int i = 0; i < bitmap_0.Width; i++)
			{
				for (int j = 0; j < bitmap_0.Height; j++)
				{
					if (bitmap_0.GetPixel(i, j).R == 0)
					{
						num++;
					}
					else
					{
						num2++;
					}
				}
			}
			if (num > num2)
			{
				ImageProcessor.smethod_9(bitmap_0);
			}
		}

		// Token: 0x06002582 RID: 9602 RVA: 0x000F8F38 File Offset: 0x000F7138
		public static int smethod_5(Color color_0)
		{
			return (int)color_0.R * 19595 + (int)color_0.G * 38469 + (int)color_0.B * 7472 >> 16;
		}

		// Token: 0x06002583 RID: 9603 RVA: 0x000F8F78 File Offset: 0x000F7178
		public static void smethod_6(Bitmap bitmap_0)
		{
			for (int i = 0; i < bitmap_0.Width; i++)
			{
				for (int j = 0; j < bitmap_0.Height; j++)
				{
					int num = ImageProcessor.smethod_5(bitmap_0.GetPixel(i, j));
					bitmap_0.SetPixel(i, j, Color.FromArgb(num, num, num));
				}
			}
		}

		// Token: 0x06002584 RID: 9604 RVA: 0x000F8FC8 File Offset: 0x000F71C8
		public static void smethod_7(Bitmap bitmap_0, int int_0, int int_1, ImageProcessor.GEnum0 genum0_0)
		{
			if (genum0_0 != ImageProcessor.GEnum0.const_0)
			{
				if (genum0_0 == ImageProcessor.GEnum0.const_1)
				{
					for (int i = 0; i < bitmap_0.Width; i++)
					{
						for (int j = 0; j < bitmap_0.Height; j++)
						{
							if (ImageProcessor.smethod_5(bitmap_0.GetPixel(i, j)) > int_0)
							{
								bitmap_0.SetPixel(i, j, Color.FromArgb(0, 0, 0));
							}
							else
							{
								bitmap_0.SetPixel(i, j, Color.FromArgb(int_1, int_1, int_1));
							}
						}
					}
				}
			}
			else
			{
				for (int k = 0; k < bitmap_0.Width; k++)
				{
					for (int l = 0; l < bitmap_0.Height; l++)
					{
						if (ImageProcessor.smethod_5(bitmap_0.GetPixel(k, l)) > int_0)
						{
							bitmap_0.SetPixel(k, l, Color.FromArgb(int_1, int_1, int_1));
						}
						else
						{
							bitmap_0.SetPixel(k, l, Color.FromArgb(0, 0, 0));
						}
					}
				}
			}
		}

		// Token: 0x06002585 RID: 9605 RVA: 0x0000E65C File Offset: 0x0000C85C
		public static void smethod_8(Bitmap bitmap_0, int int_0)
		{
			ImageProcessor.smethod_7(bitmap_0, int_0, 255, ImageProcessor.GEnum0.const_0);
		}

		// Token: 0x06002586 RID: 9606 RVA: 0x000F9090 File Offset: 0x000F7290
		public static void smethod_9(Bitmap bitmap_0)
		{
			int width = bitmap_0.Width;
			int height = bitmap_0.Height;
			for (int i = 0; i < width; i++)
			{
				for (int j = 0; j < height; j++)
				{
					Color pixel = bitmap_0.GetPixel(i, j);
					bitmap_0.SetPixel(i, j, Color.FromArgb((int)(byte.MaxValue - pixel.R), (int)(byte.MaxValue - pixel.G), (int)(byte.MaxValue - pixel.B)));
				}
			}
		}

		// Token: 0x06002587 RID: 9607 RVA: 0x000F9104 File Offset: 0x000F7304
		public static int smethod_10(List<List<Point>> list_0, Bitmap bitmap_0)
		{
			ImageProcessor.Class501 @class = new ImageProcessor.Class501();
			if (list_0.Count == 0)
			{
				throw new Exception("连通域个数为0");
			}
			if (list_0.Count >= 6)
			{
				throw new Exception("流通域个数大于6");
			}
			List<int> list = new List<int>();
			for (int i = 0; i < list_0.Count; i++)
			{
				List<Point> list2 = list_0[i];
				if (list2.Count > 0)
				{
					list2.Max(new Func<Point, int>(ImageProcessor.<>c.<>9.method_11));
					int num = list2.Min(new Func<Point, int>(ImageProcessor.<>c.<>9.method_12));
					int item = list2.OrderBy(new Func<Point, int>(ImageProcessor.<>c.<>9.method_13)).Select(new Func<Point, int>(ImageProcessor.<>c.<>9.method_14)).Distinct<int>().ToArray<int>().Length;
					list.Add(item);
				}
				else
				{
					list_0.RemoveAt(i);
				}
			}
			@class.int_0 = list.Max();
			int index = list.FindIndex(new Predicate<int>(@class.method_0));
			List<Point> list_ = list_0[index];
			int result;
			if (@class.int_0 <= 10)
			{
				result = -1;
			}
			else
			{
				result = ImageProcessor.smethod_11(list_, bitmap_0);
			}
			return result;
		}

		// Token: 0x06002588 RID: 9608 RVA: 0x000F9274 File Offset: 0x000F7474
		public static int smethod_11(List<Point> list_0, Bitmap bitmap_0)
		{
			ImageProcessor.Class502 @class = new ImageProcessor.Class502();
			int num = list_0.Max(new Func<Point, int>(ImageProcessor.<>c.<>9.method_15));
			int num2 = list_0.Min(new Func<Point, int>(ImageProcessor.<>c.<>9.method_16));
			List<int> list = new List<int>();
			ImageProcessor.Class503 class2 = new ImageProcessor.Class503();
			class2.int_0 = num2;
			while (class2.int_0 <= num)
			{
				int item = list_0.Count(new Func<Point, bool>(class2.method_0));
				list.Add(item);
				int int_ = class2.int_0;
				class2.int_0 = int_ + 1;
			}
			list.RemoveRange(0, 10);
			@class.int_0 = list.Min();
			int num3 = list.FindIndex(new Predicate<int>(@class.method_0));
			@class.int_1 = num2 + 10 + num3;
			list_0.RemoveAll(new Predicate<Point>(@class.method_1));
			return @class.int_1;
		}

		// Token: 0x06002589 RID: 9609 RVA: 0x000F937C File Offset: 0x000F757C
		public static void smethod_12(List<List<Point>> list_0, Bitmap bitmap_0)
		{
			ImageProcessor.Class504 @class = new ImageProcessor.Class504();
			@class.int_0 = 0;
			while (@class.int_0 < bitmap_0.Width)
			{
				ImageProcessor.Class505 class2 = new ImageProcessor.Class505();
				class2.class504_0 = @class;
				class2.int_0 = 0;
				int int_;
				while (class2.int_0 < bitmap_0.Height)
				{
					bool flag = false;
					using (List<List<Point>>.Enumerator enumerator = list_0.GetEnumerator())
					{
						while (enumerator.MoveNext())
						{
							List<Point> list = enumerator.Current;
							Predicate<Point> match;
							if ((match = class2.predicate_0) == null)
							{
								match = (class2.predicate_0 = new Predicate<Point>(class2.method_0));
							}
							if (!list.Find(match).IsEmpty)
							{
								flag = true;
								break;
							}
						}
						goto IL_FD;
					}
					goto IL_A4;
					IL_CF:
					int_ = class2.int_0;
					class2.int_0 = int_ + 1;
					continue;
					IL_A4:
					bitmap_0.SetPixel(class2.class504_0.int_0, class2.int_0, Color.FromArgb(255, 255, 255));
					goto IL_CF;
					IL_FD:
					if (!flag)
					{
						goto IL_A4;
					}
					goto IL_CF;
				}
				int_ = @class.int_0;
				@class.int_0 = int_ + 1;
			}
		}

		// Token: 0x0600258A RID: 9610 RVA: 0x000F94A0 File Offset: 0x000F76A0
		public static string[] smethod_13(Bitmap bitmap_0, List<Rectangle> list_0)
		{
			string[] array = new string[list_0.Count];
			for (int i = 0; i < list_0.Count; i++)
			{
				Rectangle rectangle = new Rectangle(list_0[i].X, list_0[i].Y, list_0[i].Width + 1, list_0[i].Height + 1);
				string text = "";
				for (int j = rectangle.X; j < rectangle.Width + rectangle.X; j++)
				{
					for (int k = rectangle.Y; k < rectangle.Height + rectangle.Y; k++)
					{
						if (bitmap_0.GetPixel(j, k).R < 200)
						{
							text += "1";
						}
						else
						{
							text += "0";
						}
					}
				}
				array[i] = text;
			}
			return array;
		}

		// Token: 0x0600258B RID: 9611 RVA: 0x000F95A8 File Offset: 0x000F77A8
		public static void smethod_14(Bitmap bitmap_0, Point[] point_0)
		{
			List<Point> list = new List<Point>();
			for (int i = 0; i < bitmap_0.Width; i++)
			{
				for (int j = 0; j < bitmap_0.Height; j++)
				{
					if (bitmap_0.GetPixel(i, j).R == 0)
					{
						for (int k = 0; k < point_0.Length; k++)
						{
							int num = i + point_0[k].X;
							int num2 = j + point_0[k].Y;
							if (num >= 0 && num < bitmap_0.Width && num2 >= 0 && num2 < bitmap_0.Height && bitmap_0.GetPixel(num, num2).R > 0)
							{
								list.Add(new Point(num, num2));
							}
						}
					}
				}
			}
			foreach (Point point in list)
			{
				bitmap_0.SetPixel(point.X, point.Y, Color.FromArgb(0, 0, 0));
			}
		}

		// Token: 0x0600258C RID: 9612 RVA: 0x000F96CC File Offset: 0x000F78CC
		public static void smethod_15(Bitmap bitmap_0, Point[] point_0)
		{
			List<Point> list = new List<Point>();
			for (int i = 0; i < bitmap_0.Width; i++)
			{
				for (int j = 0; j < bitmap_0.Height; j++)
				{
					if (bitmap_0.GetPixel(i, j).R == 0)
					{
						bool flag = false;
						int num = 0;
						int k = 0;
						while (k < point_0.Length)
						{
							if (i + point_0[k].X >= 0 && i + point_0[k].X < bitmap_0.Width)
							{
								if (j + point_0[k].Y >= 0 && j + point_0[k].Y < bitmap_0.Height)
								{
									num += (int)bitmap_0.GetPixel(i + point_0[k].X, j + point_0[k].Y).R;
									k++;
									continue;
								}
								flag = true;
							}
							else
							{
								flag = true;
							}
							IL_D7:
							if (flag)
							{
								list.Add(new Point(i, j));
							}
							if (num > 0)
							{
								list.Add(new Point(i, j));
								goto IL_FA;
							}
							goto IL_FA;
						}
						goto IL_D7;
					}
					IL_FA:;
				}
			}
			foreach (Point point in list)
			{
				bitmap_0.SetPixel(point.X, point.Y, Color.FromArgb(255, 255, 255));
			}
		}

		// Token: 0x0600258D RID: 9613 RVA: 0x000F985C File Offset: 0x000F7A5C
		public static Rectangle smethod_16(List<Point> list_0)
		{
			int num = list_0.Max(new Func<Point, int>(ImageProcessor.<>c.<>9.method_17));
			int num2 = list_0.Max(new Func<Point, int>(ImageProcessor.<>c.<>9.method_18));
			int num3 = list_0.Min(new Func<Point, int>(ImageProcessor.<>c.<>9.method_19));
			int num4 = list_0.Min(new Func<Point, int>(ImageProcessor.<>c.<>9.method_20));
			return new Rectangle(num3, num4, num - num3 + 1, num2 - num4 + 1);
		}

		// Token: 0x0600258E RID: 9614 RVA: 0x000F9918 File Offset: 0x000F7B18
		public static int[] smethod_17(Bitmap bitmap_0)
		{
			int[] array = new int[bitmap_0.Width];
			for (int i = 0; i < bitmap_0.Width; i++)
			{
				int num = 0;
				for (int j = 0; j < bitmap_0.Height; j++)
				{
					if (bitmap_0.GetPixel(i, j).R < 100)
					{
						num++;
					}
				}
				array[i] = num;
			}
			return array;
		}

		// Token: 0x0600258F RID: 9615 RVA: 0x000F997C File Offset: 0x000F7B7C
		public static Bitmap smethod_18(int[] int_0)
		{
			int num = int_0.Max();
			Bitmap bitmap = new Bitmap(int_0.Length, num);
			Graphics graphics = Graphics.FromImage(bitmap);
			Pen pen = new Pen(Color.FromArgb(0, 0, 255));
			for (int i = 0; i < bitmap.Width; i++)
			{
				Point pt = new Point(i, num - int_0[i]);
				Point pt2 = new Point(i, num);
				graphics.DrawLine(pen, pt, pt2);
			}
			graphics.Dispose();
			return bitmap;
		}

		// Token: 0x06002590 RID: 9616 RVA: 0x000F99FC File Offset: 0x000F7BFC
		public static int smethod_19(List<List<Point>> list_0)
		{
			int result = -1;
			int num = 0;
			if (list_0.Count < 6)
			{
				for (int i = 0; i < list_0.Count; i++)
				{
					List<Point> source = list_0[i];
					int num2 = source.Max(new Func<Point, int>(ImageProcessor.<>c.<>9.method_21)) - source.Min(new Func<Point, int>(ImageProcessor.<>c.<>9.method_22));
					if (num2 > num)
					{
						num = num2;
						result = i;
					}
				}
			}
			return result;
		}

		// Token: 0x06002591 RID: 9617 RVA: 0x000F9A90 File Offset: 0x000F7C90
		public static Bitmap smethod_20(Bitmap bitmap_0, List<Rectangle> list_0)
		{
			list_0.Clear();
			int num = 0;
			List<int> list = new List<int>();
			ImageProcessor.smethod_4(bitmap_0, 200);
			bitmap_0 = ImageProcessor.smethod_26(bitmap_0, 2, 4, 10);
			ConnectedComponent connectedComponent = new ConnectedComponent();
			connectedComponent.Roi = new Rectangle(1, 1, bitmap_0.Width - 2, bitmap_0.Height - 2);
			List<List<Point>> list2;
			do
			{
				list2 = connectedComponent.method_0(bitmap_0);
				ImageProcessor.smethod_3(list2);
				for (int i = 0; i < list2.Count; i++)
				{
				}
				ImageProcessor.smethod_0(list2);
				if (list2.Count >= 6)
				{
					break;
				}
				int num2 = ImageProcessor.smethod_10(list2, bitmap_0);
				if (num2 != -1)
				{
					list.Add(num2);
				}
				ImageProcessor.smethod_12(list2, bitmap_0);
				num++;
			}
			while (num < 6);
			Bitmap result = new Bitmap(bitmap_0);
			foreach (List<Point> list3 in list2)
			{
				if (list3.Count != 0)
				{
					int num3 = list3.Max(new Func<Point, int>(ImageProcessor.<>c.<>9.method_23));
					int num4 = list3.Max(new Func<Point, int>(ImageProcessor.<>c.<>9.method_24));
					int num5 = list3.Min(new Func<Point, int>(ImageProcessor.<>c.<>9.method_25));
					int num6 = list3.Min(new Func<Point, int>(ImageProcessor.<>c.<>9.method_26));
					Rectangle rectangle = new Rectangle(num6, num5, num4 - num6, num3 - num5);
					list_0.Add(rectangle);
					Graphics graphics = Graphics.FromImage(bitmap_0);
					Pen pen = new Pen(Color.FromArgb(0, 0, 255));
					graphics.DrawRectangle(pen, rectangle);
					Pen pen2 = new Pen(Color.FromArgb(255, 0, 0));
					for (int j = 0; j < list3.Count - 1; j++)
					{
						graphics.DrawLine(pen2, list3[j], list3[j + 1]);
					}
					Pen pen3 = new Pen(Color.FromArgb(0, 255, 0));
					for (int k = 0; k < list.Count; k++)
					{
						graphics.DrawLine(pen3, list[k], 0, list[k], bitmap_0.Height);
					}
					graphics.Dispose();
				}
			}
			return result;
		}

		// Token: 0x06002592 RID: 9618 RVA: 0x000F9D24 File Offset: 0x000F7F24
		public static string smethod_21(Bitmap bitmap_0)
		{
			string text = "";
			for (int i = 0; i < bitmap_0.Width; i++)
			{
				for (int j = 0; j < bitmap_0.Height; j++)
				{
					if (bitmap_0.GetPixel(i, j).R < 100)
					{
						text += "1";
					}
					else
					{
						text += "0";
					}
				}
			}
			return text;
		}

		// Token: 0x06002593 RID: 9619 RVA: 0x000F9D90 File Offset: 0x000F7F90
		public static string smethod_22(string string_0)
		{
			return ImageProcessor.smethod_21(new Bitmap(string_0));
		}

		// Token: 0x06002594 RID: 9620 RVA: 0x000F9DAC File Offset: 0x000F7FAC
		public static Bitmap smethod_23(Bitmap bitmap_0)
		{
			for (int i = 0; i < bitmap_0.Width; i++)
			{
				for (int j = 0; j < bitmap_0.Height; j++)
				{
					Color pixel = bitmap_0.GetPixel(i, j);
					int num = (int)((double)pixel.R * 0.3 + (double)pixel.G * 0.59 + (double)pixel.B * 0.11);
					Color color = Color.FromArgb(num, num, num);
					bitmap_0.SetPixel(i, j, color);
				}
			}
			return bitmap_0;
		}

		// Token: 0x06002595 RID: 9621 RVA: 0x000F9E38 File Offset: 0x000F8038
		public static int[] smethod_24(Bitmap bitmap_0)
		{
			int[] array = new int[256];
			for (int i = 0; i < array.Length; i++)
			{
				array[i] = 0;
			}
			for (int j = 0; j < bitmap_0.Width; j++)
			{
				for (int k = 0; k < bitmap_0.Height; k++)
				{
					int r = (int)bitmap_0.GetPixel(j, k).R;
					array[r]++;
				}
			}
			return array;
		}

		// Token: 0x06002596 RID: 9622 RVA: 0x000F9EA8 File Offset: 0x000F80A8
		public static Bitmap smethod_25(Bitmap bitmap_0, int int_0 = 200)
		{
			for (int i = 0; i < bitmap_0.Width; i++)
			{
				for (int j = 0; j < bitmap_0.Height; j++)
				{
					if ((int)bitmap_0.GetPixel(i, j).R > int_0)
					{
						bitmap_0.SetPixel(i, j, Color.FromArgb(255, 255, 255));
					}
					else
					{
						bitmap_0.SetPixel(i, j, Color.FromArgb(0, 0, 0));
					}
				}
			}
			return bitmap_0;
		}

		// Token: 0x06002597 RID: 9623 RVA: 0x000F9F20 File Offset: 0x000F8120
		public static Bitmap smethod_26(Bitmap bitmap_0, int int_0 = 2, int int_1 = 4, int int_2 = 10)
		{
			for (int i = 0; i < bitmap_0.Width; i++)
			{
				for (int j = 0; j < bitmap_0.Height; j++)
				{
					if (i < int_2)
					{
						bitmap_0.SetPixel(i, j, Color.FromArgb(255, 255, 255));
					}
					if (j < int_0)
					{
						bitmap_0.SetPixel(i, j, Color.FromArgb(255, 255, 255));
					}
					if (j > bitmap_0.Height - int_1 - 1)
					{
						bitmap_0.SetPixel(i, j, Color.FromArgb(255, 255, 255));
					}
				}
			}
			return bitmap_0;
		}

		// Token: 0x06002598 RID: 9624 RVA: 0x000F9FC0 File Offset: 0x000F81C0
		public static Bitmap smethod_27(Bitmap bitmap_0, int int_0 = 50)
		{
			Bitmap bitmap = new Bitmap(bitmap_0);
			int[] array = new int[8];
			int[] array2 = new int[]
			{
				1,
				0,
				-1,
				0,
				1,
				-1,
				1,
				-1
			};
			int[] array3 = new int[]
			{
				0,
				1,
				0,
				-1,
				-1,
				1,
				1,
				-1
			};
			for (int i = 1; i < bitmap.Width - 1; i++)
			{
				for (int j = 1; j < bitmap.Height - 1; j++)
				{
					int r = (int)bitmap.GetPixel(i, j).R;
					for (int k = 0; k < array.Length; k++)
					{
						array[k] = (int)bitmap.GetPixel(i + array2[k], j + array3[k]).R;
					}
					if (Math.Abs(array.OrderBy(new Func<int, int>(ImageProcessor.<>c.<>9.method_27)).ToArray<int>()[4] - r) < int_0)
					{
						if (r < 255 - r)
						{
							bitmap.SetPixel(i, j, Color.FromArgb(0, 0, 0));
						}
						else
						{
							bitmap.SetPixel(i, j, Color.FromArgb(255, 255, 255));
						}
					}
					else
					{
						bitmap.SetPixel(i, j, bitmap_0.GetPixel(i, j));
					}
				}
			}
			return bitmap;
		}

		// Token: 0x0200039E RID: 926
		public enum GEnum0
		{
			// Token: 0x04001215 RID: 4629
			const_0,
			// Token: 0x04001216 RID: 4630
			const_1
		}

		// Token: 0x020003A0 RID: 928
		[CompilerGenerated]
		private sealed class Class496
		{
			// Token: 0x060025B8 RID: 9656 RVA: 0x000FA14C File Offset: 0x000F834C
			internal bool method_0(Point point_0)
			{
				return point_0.Y == this.int_0;
			}

			// Token: 0x060025B9 RID: 9657 RVA: 0x000FA14C File Offset: 0x000F834C
			internal bool method_1(Point point_0)
			{
				return point_0.Y == this.int_0;
			}

			// Token: 0x060025BA RID: 9658 RVA: 0x000FA14C File Offset: 0x000F834C
			internal bool method_2(Point point_0)
			{
				return point_0.Y == this.int_0;
			}

			// Token: 0x04001234 RID: 4660
			public int int_0;
		}

		// Token: 0x020003A1 RID: 929
		[CompilerGenerated]
		private sealed class Class497
		{
			// Token: 0x060025BC RID: 9660 RVA: 0x000FA16C File Offset: 0x000F836C
			internal bool method_0(Point point_0)
			{
				return point_0.Y == this.int_0;
			}

			// Token: 0x060025BD RID: 9661 RVA: 0x000FA16C File Offset: 0x000F836C
			internal bool method_1(Point point_0)
			{
				return point_0.Y == this.int_0;
			}

			// Token: 0x060025BE RID: 9662 RVA: 0x000FA16C File Offset: 0x000F836C
			internal bool method_2(Point point_0)
			{
				return point_0.Y == this.int_0;
			}

			// Token: 0x04001235 RID: 4661
			public int int_0;
		}

		// Token: 0x020003A2 RID: 930
		[CompilerGenerated]
		private sealed class Class498
		{
			// Token: 0x060025C0 RID: 9664 RVA: 0x000FA18C File Offset: 0x000F838C
			internal bool method_0(Point point_0)
			{
				return point_0.X == this.int_0;
			}

			// Token: 0x060025C1 RID: 9665 RVA: 0x000FA18C File Offset: 0x000F838C
			internal bool method_1(Point point_0)
			{
				return point_0.X == this.int_0;
			}

			// Token: 0x04001236 RID: 4662
			public int int_0;
		}

		// Token: 0x020003A3 RID: 931
		[CompilerGenerated]
		private sealed class Class499
		{
			// Token: 0x060025C3 RID: 9667 RVA: 0x000FA1AC File Offset: 0x000F83AC
			internal bool method_0(Point point_0)
			{
				return point_0.X == this.int_0;
			}

			// Token: 0x060025C4 RID: 9668 RVA: 0x000FA1AC File Offset: 0x000F83AC
			internal bool method_1(Point point_0)
			{
				return point_0.X == this.int_0;
			}

			// Token: 0x04001237 RID: 4663
			public int int_0;
		}

		// Token: 0x020003A4 RID: 932
		[CompilerGenerated]
		private sealed class Class500
		{
			// Token: 0x060025C6 RID: 9670 RVA: 0x000FA1CC File Offset: 0x000F83CC
			internal bool method_0(List<Point> list_0)
			{
				return list_0.Count < this.int_0;
			}

			// Token: 0x04001238 RID: 4664
			public int int_0;
		}

		// Token: 0x020003A5 RID: 933
		[CompilerGenerated]
		private sealed class Class501
		{
			// Token: 0x060025C8 RID: 9672 RVA: 0x000FA1EC File Offset: 0x000F83EC
			internal bool method_0(int int_1)
			{
				return int_1 == this.int_0;
			}

			// Token: 0x04001239 RID: 4665
			public int int_0;
		}

		// Token: 0x020003A6 RID: 934
		[CompilerGenerated]
		private sealed class Class502
		{
			// Token: 0x060025CA RID: 9674 RVA: 0x000FA208 File Offset: 0x000F8408
			internal bool method_0(int int_2)
			{
				return int_2 == this.int_0;
			}

			// Token: 0x060025CB RID: 9675 RVA: 0x000FA224 File Offset: 0x000F8424
			internal bool method_1(Point point_0)
			{
				return point_0.X == this.int_1;
			}

			// Token: 0x0400123A RID: 4666
			public int int_0;

			// Token: 0x0400123B RID: 4667
			public int int_1;
		}

		// Token: 0x020003A7 RID: 935
		[CompilerGenerated]
		private sealed class Class503
		{
			// Token: 0x060025CD RID: 9677 RVA: 0x000FA244 File Offset: 0x000F8444
			internal bool method_0(Point point_0)
			{
				return point_0.X == this.int_0;
			}

			// Token: 0x0400123C RID: 4668
			public int int_0;
		}

		// Token: 0x020003A8 RID: 936
		[CompilerGenerated]
		private sealed class Class504
		{
			// Token: 0x0400123D RID: 4669
			public int int_0;
		}

		// Token: 0x020003A9 RID: 937
		[CompilerGenerated]
		private sealed class Class505
		{
			// Token: 0x060025D0 RID: 9680 RVA: 0x000FA264 File Offset: 0x000F8464
			internal bool method_0(Point point_0)
			{
				bool result;
				if (point_0.X == this.class504_0.int_0)
				{
					result = (point_0.Y == this.int_0);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400123E RID: 4670
			public int int_0;

			// Token: 0x0400123F RID: 4671
			public ImageProcessor.Class504 class504_0;

			// Token: 0x04001240 RID: 4672
			public Predicate<Point> predicate_0;
		}
	}
}
