﻿using System;
using ns14;
using ns15;
using ns30;
using ns9;
using TEx.Inds;
using TEx.SIndicator;

namespace ns12
{
	// Token: 0x0200030F RID: 783
	internal sealed class Class413 : Class412
	{
		// Token: 0x060021CB RID: 8651 RVA: 0x0000D8A3 File Offset: 0x0000BAA3
		public Class413(HToken htoken_1, Class408 class408_2, Class408 class408_3) : base(htoken_1, class408_2, class408_3)
		{
		}

		// Token: 0x060021CC RID: 8652 RVA: 0x000E7928 File Offset: 0x000E5B28
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			object object_ = this.Left.vmethod_1(parserEnvironment_0);
			object object_2 = this.Right.vmethod_1(parserEnvironment_0);
			return this.vmethod_2(object_, object_2);
		}

		// Token: 0x060021CD RID: 8653 RVA: 0x000E795C File Offset: 0x000E5B5C
		protected override double vmethod_3(double double_0, double double_1)
		{
			Enum26 hsymbolType = this.Token.Symbol.HSymbolType;
			double result;
			if (hsymbolType != Enum26.const_17)
			{
				if (hsymbolType != Enum26.const_18)
				{
					throw new Exception(this.Token.method_0("不是AND或者OR符号"));
				}
				result = double_0 + double_1;
			}
			else
			{
				result = double_0 * double_1;
			}
			return result;
		}

		// Token: 0x060021CE RID: 8654 RVA: 0x000E79A8 File Offset: 0x000E5BA8
		protected override DataArray vmethod_4(DataArray dataArray_0, DataArray dataArray_1)
		{
			Enum26 hsymbolType = this.Token.Symbol.HSymbolType;
			DataArray result;
			if (hsymbolType != Enum26.const_17)
			{
				if (hsymbolType != Enum26.const_18)
				{
					throw new Exception(this.Token.method_0("不是AND或者OR符号"));
				}
				result = (dataArray_0 | dataArray_1);
			}
			else
			{
				result = (dataArray_0 & dataArray_1);
			}
			return result;
		}

		// Token: 0x060021CF RID: 8655 RVA: 0x000E79FC File Offset: 0x000E5BFC
		public static Class408 smethod_0(Tokenes tokenes_0)
		{
			Class408 @class = Class416.smethod_0(tokenes_0);
			tokenes_0.method_1();
			HToken htoken = tokenes_0.Current;
			while (htoken.Symbol.HSymbolType == Enum26.const_18 || htoken.Symbol.HSymbolType == Enum26.const_17)
			{
				HToken htoken_ = htoken;
				tokenes_0.method_1();
				Class408 class408_ = Class416.smethod_0(tokenes_0);
				@class = new Class413(htoken_, @class, class408_);
				tokenes_0.method_1();
				htoken = tokenes_0.Current;
			}
			tokenes_0.method_2();
			return @class;
		}
	}
}
