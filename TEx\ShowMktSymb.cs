﻿using System;
using System.ComponentModel;
using System.Text;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x020001F9 RID: 505
	[Serializable]
	internal sealed class ShowMktSymb
	{
		// Token: 0x060013B8 RID: 5048 RVA: 0x00002D25 File Offset: 0x00000F25
		public ShowMktSymb()
		{
		}

		// Token: 0x060013B9 RID: 5049 RVA: 0x00007F56 File Offset: 0x00006156
		public ShowMktSymb(StkSymbol s)
		{
			this.UsrName = TApp.UserName;
			this.method_16(s);
		}

		// Token: 0x060013BA RID: 5050 RVA: 0x00084340 File Offset: 0x00082540
		public string method_0()
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append(this.method_6(this.StkId));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_11(this.StkCode));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_11(this.CNName));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_7(this.Price));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_7(this.Vol));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_7(this.Amount));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_7(this.Open));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_7(this.High));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_7(this.Low));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_7(this.LastDayClose));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_7(this.LastDayAmt));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_9(this.LastDT));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_10(this.IsDownloaded));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_10(this.IsInZiXuan));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_11(""));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_6(this.ExchgId));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_11(this.index_name));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.close));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.total_profit));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.net_profit));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.roe));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.grossprofit_margin));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.accounts_receiv));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.adv_receipts));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.turnover_rate));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.turnover_rate_f));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.volume_ratio));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.pe));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.pe_ttm));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.pb));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.ps));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.dv_ratio));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.total_share));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.total_mv));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.float_share));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.circ_mv));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_11(this.cb_stk_code));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_11(this.cb_stk_name));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.maturity));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.issue_size));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.par));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_11(this.value_date));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_11(this.maturity_date));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_11(this.list_date));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_11(this.delist_date));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.first_conv_price));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.conv_price));
			stringBuilder.Append(",");
			stringBuilder.Append(this.method_8(this.maturity_put_price));
			return stringBuilder.ToString();
		}

		// Token: 0x060013BB RID: 5051 RVA: 0x00084920 File Offset: 0x00082B20
		public void method_1(string string_0)
		{
			string[] array = string_0.Split(new char[]
			{
				','
			});
			this.StkId = Convert.ToInt32(array[0]);
			this.StkCode = array[1];
			this.CNName = array[2];
			this.Price = this.method_2(array[3]);
			this.Vol = this.method_2(array[4]);
			this.Amount = this.method_2(array[5]);
			this.Open = this.method_2(array[6]);
			this.High = this.method_2(array[7]);
			this.Low = this.method_2(array[8]);
			this.LastDayClose = this.method_2(array[9]);
			this.LastDayAmt = this.method_2(array[10]);
			this.LastDT = this.method_4(array[11]);
			this.IsDownloaded = this.method_5(array[12]);
			this.IsInZiXuan = this.method_5(array[13]);
			this.UsrName = array[14];
			this.ExchgId = Convert.ToInt32(array[15]);
			this.index_name = array[16];
			this.close = this.method_3(array[17]);
			this.total_profit = this.method_3(array[18]);
			this.net_profit = this.method_3(array[19]);
			this.roe = this.method_3(array[20]);
			this.grossprofit_margin = this.method_3(array[21]);
			this.accounts_receiv = this.method_3(array[22]);
			this.adv_receipts = this.method_3(array[23]);
			this.turnover_rate = this.method_3(array[24]);
			this.turnover_rate_f = this.method_3(array[25]);
			this.volume_ratio = this.method_3(array[26]);
			this.pe = this.method_3(array[27]);
			this.pe_ttm = this.method_3(array[28]);
			this.pb = this.method_3(array[29]);
			this.ps = this.method_3(array[30]);
			this.dv_ratio = this.method_3(array[31]);
			this.total_share = this.method_3(array[32]);
			this.total_mv = this.method_3(array[33]);
			this.float_share = this.method_3(array[34]);
			this.circ_mv = this.method_3(array[35]);
			this.cb_stk_code = array[36];
			this.cb_stk_name = array[37];
			this.maturity = this.method_3(array[38]);
			this.issue_size = this.method_3(array[39]);
			this.par = this.method_3(array[40]);
			this.value_date = array[41];
			this.maturity_date = array[42];
			this.list_date = array[43];
			this.delist_date = array[44];
			this.first_conv_price = this.method_3(array[45]);
			this.conv_price = this.method_3(array[46]);
			this.maturity_put_price = this.method_3(array[47]);
		}

		// Token: 0x060013BC RID: 5052 RVA: 0x00084BFC File Offset: 0x00082DFC
		private decimal? method_2(string string_0)
		{
			decimal? result = null;
			if (!string.IsNullOrEmpty(string_0))
			{
				result = new decimal?(Convert.ToDecimal(string_0));
			}
			return result;
		}

		// Token: 0x060013BD RID: 5053 RVA: 0x00084C2C File Offset: 0x00082E2C
		private double? method_3(string string_0)
		{
			double? result = null;
			if (!string.IsNullOrEmpty(string_0))
			{
				result = new double?(Convert.ToDouble(string_0));
			}
			return result;
		}

		// Token: 0x060013BE RID: 5054 RVA: 0x00084C5C File Offset: 0x00082E5C
		private DateTime? method_4(string string_0)
		{
			DateTime? result = null;
			if (!string.IsNullOrEmpty(string_0))
			{
				result = new DateTime?(Convert.ToDateTime(string_0));
			}
			return result;
		}

		// Token: 0x060013BF RID: 5055 RVA: 0x00084C8C File Offset: 0x00082E8C
		private bool method_5(string string_0)
		{
			bool result;
			if (!string_0.Equals("1") && !string_0.Equals("True", StringComparison.InvariantCultureIgnoreCase))
			{
				result = false;
			}
			else
			{
				result = true;
			}
			return result;
		}

		// Token: 0x060013C0 RID: 5056 RVA: 0x00084CC0 File Offset: 0x00082EC0
		private string method_6(int int_0)
		{
			return int_0.ToString();
		}

		// Token: 0x060013C1 RID: 5057 RVA: 0x00084CD8 File Offset: 0x00082ED8
		private string method_7(decimal? nullable_0)
		{
			string result;
			if (nullable_0 == null)
			{
				result = "";
			}
			else
			{
				result = nullable_0.Value.ToString();
			}
			return result;
		}

		// Token: 0x060013C2 RID: 5058 RVA: 0x00084D0C File Offset: 0x00082F0C
		private string method_8(double? nullable_0)
		{
			string result;
			if (nullable_0 == null)
			{
				result = "";
			}
			else
			{
				result = nullable_0.Value.ToString();
			}
			return result;
		}

		// Token: 0x060013C3 RID: 5059 RVA: 0x00084D40 File Offset: 0x00082F40
		private string method_9(DateTime? nullable_0)
		{
			string result;
			if (nullable_0 == null)
			{
				result = "";
			}
			else
			{
				result = nullable_0.Value.ToString("yyyy-MM-dd HH:mm");
			}
			return result;
		}

		// Token: 0x060013C4 RID: 5060 RVA: 0x00084D78 File Offset: 0x00082F78
		private string method_10(bool bool_0)
		{
			string result;
			if (!bool_0)
			{
				result = "0";
			}
			else
			{
				result = "1";
			}
			return result;
		}

		// Token: 0x060013C5 RID: 5061 RVA: 0x00084D9C File Offset: 0x00082F9C
		private string method_11(string string_0)
		{
			string result;
			if (!string.IsNullOrEmpty(string_0))
			{
				result = string_0;
			}
			else
			{
				result = "";
			}
			return result;
		}

		// Token: 0x060013C6 RID: 5062 RVA: 0x00084DC0 File Offset: 0x00082FC0
		private string method_12(double double_0)
		{
			return double_0.ToString();
		}

		// Token: 0x060013C7 RID: 5063 RVA: 0x00084DD8 File Offset: 0x00082FD8
		public void method_13(ShowMktSymb showMktSymb_0, bool bool_0 = false, bool bool_1 = false)
		{
			this.StkId = showMktSymb_0.StkId;
			this.StkCode = showMktSymb_0._StkCode;
			this.CNName = showMktSymb_0.CNName;
			this.Price = showMktSymb_0.Price;
			this.Vol = showMktSymb_0.Vol;
			this.Amount = showMktSymb_0.Amount;
			this.Open = showMktSymb_0.Open;
			this.High = showMktSymb_0.High;
			this.Low = showMktSymb_0.Low;
			this.LastDayClose = showMktSymb_0.LastDayClose;
			this.LastDayAmt = showMktSymb_0.LastDayAmt;
			this.LastDT = showMktSymb_0.LastDT;
			this.IsDownloaded = showMktSymb_0.IsDownloaded;
			this.IsInZiXuan = showMktSymb_0.IsInZiXuan;
			this.UsrName = showMktSymb_0.UsrName;
			this.ExchgId = showMktSymb_0.ExchgId;
			this.index_name = showMktSymb_0.index_name;
			if (bool_0)
			{
				this.method_14(showMktSymb_0);
			}
			if (bool_1)
			{
				this.method_15(showMktSymb_0);
			}
		}

		// Token: 0x060013C8 RID: 5064 RVA: 0x00084EC8 File Offset: 0x000830C8
		private void method_14(ShowMktSymb showMktSymb_0)
		{
			this.close = showMktSymb_0.close;
			this.total_profit = showMktSymb_0.total_profit;
			this.net_profit = showMktSymb_0.net_profit;
			this.roe = showMktSymb_0.roe;
			this.grossprofit_margin = showMktSymb_0.grossprofit_margin;
			this.accounts_receiv = showMktSymb_0.accounts_receiv;
			this.adv_receipts = showMktSymb_0.adv_receipts;
			this.turnover_rate = showMktSymb_0.turnover_rate;
			this.turnover_rate_f = showMktSymb_0.turnover_rate_f;
			this.volume_ratio = showMktSymb_0.volume_ratio;
			this.pe = showMktSymb_0.pe;
			this.pe_ttm = showMktSymb_0.pe_ttm;
			this.pb = showMktSymb_0.pb;
			this.ps = showMktSymb_0.ps;
			this.dv_ratio = showMktSymb_0.dv_ratio;
			this.total_share = showMktSymb_0.total_share;
			this.total_mv = showMktSymb_0.total_mv;
			this.float_share = showMktSymb_0.float_share;
			this.circ_mv = showMktSymb_0.circ_mv;
		}

		// Token: 0x060013C9 RID: 5065 RVA: 0x00084FBC File Offset: 0x000831BC
		private void method_15(ShowMktSymb showMktSymb_0)
		{
			this.cb_stk_code = showMktSymb_0.cb_stk_code;
			this.cb_stk_name = showMktSymb_0.cb_stk_name;
			this.maturity = showMktSymb_0.maturity;
			this.issue_size = showMktSymb_0.issue_size;
			this.par = showMktSymb_0.par;
			this.value_date = showMktSymb_0.value_date;
			this.maturity_date = showMktSymb_0.maturity_date;
			this.list_date = showMktSymb_0.list_date;
			this.delist_date = showMktSymb_0.delist_date;
			this.first_conv_price = showMktSymb_0.first_conv_price;
			this.conv_price = showMktSymb_0.conv_price;
			this.maturity_put_price = showMktSymb_0.maturity_put_price;
		}

		// Token: 0x060013CA RID: 5066 RVA: 0x0008505C File Offset: 0x0008325C
		public void method_16(StkSymbol stkSymbol_0)
		{
			this.StkId = stkSymbol_0.ID;
			this.StkCode = stkSymbol_0.Code;
			this.CNName = stkSymbol_0.CNName;
			this.ExchgId = stkSymbol_0.ExchangeID;
			this.index_name = (string.IsNullOrEmpty(stkSymbol_0.IdxClassLv2) ? stkSymbol_0.IdxClassLv1 : stkSymbol_0.IdxClassLv2);
		}

		// Token: 0x060013CB RID: 5067 RVA: 0x000850BC File Offset: 0x000832BC
		public void method_17(HisData hisData_0)
		{
			decimal num = Convert.ToDecimal(hisData_0.Close);
			this.Price = new decimal?(num);
			this.Vol = new decimal?(Convert.ToDecimal(hisData_0.Volume));
			this.Amount = new decimal?(Convert.ToDecimal(hisData_0.Amount));
			if (this.High != null && num > this.High.Value)
			{
				this.High = new decimal?(num);
			}
			if (this.Low != null && num < this.Low.Value)
			{
				this.Low = new decimal?(num);
			}
			this.LastDT = new DateTime?(hisData_0.Date);
		}

		// Token: 0x060013CC RID: 5068 RVA: 0x00085190 File Offset: 0x00083390
		public string method_18()
		{
			return TExRoutine.GetCodeForFnDataApi(this.StkCode, this.ExchgId);
		}

		// Token: 0x060013CD RID: 5069 RVA: 0x000851B4 File Offset: 0x000833B4
		public static int smethod_0(string string_0)
		{
			int result;
			if (string_0 == "INE")
			{
				result = 0;
			}
			else if (string_0 == "CFFEX")
			{
				result = 1;
			}
			else if (string_0 == "SHFE")
			{
				result = 2;
			}
			else if (string_0 == "CZCE")
			{
				result = 3;
			}
			else if (string_0 == "DCE")
			{
				result = 4;
			}
			else if (string_0 == "SH")
			{
				result = 5;
			}
			else
			{
				if (!(string_0 == "SZ"))
				{
					throw new Exception("Exchg Abbr not recognised!");
				}
				result = 6;
			}
			return result;
		}

		// Token: 0x170002F9 RID: 761
		// (get) Token: 0x060013CE RID: 5070 RVA: 0x00085244 File Offset: 0x00083444
		// (set) Token: 0x060013CF RID: 5071 RVA: 0x00007F72 File Offset: 0x00006172
		public int StkId
		{
			get
			{
				return this._StkId;
			}
			set
			{
				this._StkId = value;
			}
		}

		// Token: 0x170002FA RID: 762
		// (get) Token: 0x060013D0 RID: 5072 RVA: 0x0008525C File Offset: 0x0008345C
		// (set) Token: 0x060013D1 RID: 5073 RVA: 0x00007F7D File Offset: 0x0000617D
		[DisplayName("代码")]
		public string StkCode
		{
			get
			{
				return this._StkCode;
			}
			set
			{
				this._StkCode = value;
			}
		}

		// Token: 0x170002FB RID: 763
		// (get) Token: 0x060013D2 RID: 5074 RVA: 0x00085274 File Offset: 0x00083474
		// (set) Token: 0x060013D3 RID: 5075 RVA: 0x00007F88 File Offset: 0x00006188
		[DisplayName("名称")]
		public string CNName
		{
			get
			{
				return this._CNName;
			}
			set
			{
				this._CNName = value;
			}
		}

		// Token: 0x170002FC RID: 764
		// (get) Token: 0x060013D4 RID: 5076 RVA: 0x0008528C File Offset: 0x0008348C
		// (set) Token: 0x060013D5 RID: 5077 RVA: 0x00007F93 File Offset: 0x00006193
		[DisplayName("最新价")]
		public decimal? Price
		{
			get
			{
				return this._Price;
			}
			set
			{
				this._Price = value;
			}
		}

		// Token: 0x170002FD RID: 765
		// (get) Token: 0x060013D6 RID: 5078 RVA: 0x000852A4 File Offset: 0x000834A4
		// (set) Token: 0x060013D7 RID: 5079 RVA: 0x00007F9E File Offset: 0x0000619E
		[DisplayName("成交量")]
		public decimal? Vol
		{
			get
			{
				return this._Vol;
			}
			set
			{
				this._Vol = value;
			}
		}

		// Token: 0x170002FE RID: 766
		// (get) Token: 0x060013D8 RID: 5080 RVA: 0x000852BC File Offset: 0x000834BC
		// (set) Token: 0x060013D9 RID: 5081 RVA: 0x00007FA9 File Offset: 0x000061A9
		[DisplayName("持仓量")]
		public decimal? Amount
		{
			get
			{
				return this._Amount;
			}
			set
			{
				this._Amount = value;
			}
		}

		// Token: 0x170002FF RID: 767
		// (get) Token: 0x060013DA RID: 5082 RVA: 0x000852D4 File Offset: 0x000834D4
		[DisplayName("涨跌")]
		public decimal? PriceVar
		{
			get
			{
				decimal? result;
				if (this.Price != null && this.LastDayClose != null)
				{
					result = new decimal?((this._Price.Value - this.LastDayClose.Value) / 1.0000000000000000000000000000m);
				}
				else
				{
					result = null;
				}
				return result;
			}
		}

		// Token: 0x17000300 RID: 768
		// (get) Token: 0x060013DB RID: 5083 RVA: 0x00085350 File Offset: 0x00083550
		[DisplayName("涨幅%")]
		public string PriceVarP
		{
			get
			{
				string result;
				if (this.PriceVar != null)
				{
					result = (this.PriceVar.Value / this._LastDayClose.Value).ToString("P") + "%";
				}
				else
				{
					result = string.Empty;
				}
				return result;
			}
		}

		// Token: 0x17000301 RID: 769
		// (get) Token: 0x060013DC RID: 5084 RVA: 0x000853B0 File Offset: 0x000835B0
		// (set) Token: 0x060013DD RID: 5085 RVA: 0x00007FB4 File Offset: 0x000061B4
		[DisplayName("开盘")]
		public decimal? Open
		{
			get
			{
				return this._Open;
			}
			set
			{
				this._Open = value;
			}
		}

		// Token: 0x17000302 RID: 770
		// (get) Token: 0x060013DE RID: 5086 RVA: 0x000853C8 File Offset: 0x000835C8
		// (set) Token: 0x060013DF RID: 5087 RVA: 0x00007FBF File Offset: 0x000061BF
		[DisplayName("最高")]
		public decimal? High
		{
			get
			{
				return this._High;
			}
			set
			{
				this._High = value;
			}
		}

		// Token: 0x17000303 RID: 771
		// (get) Token: 0x060013E0 RID: 5088 RVA: 0x000853E0 File Offset: 0x000835E0
		// (set) Token: 0x060013E1 RID: 5089 RVA: 0x00007FCA File Offset: 0x000061CA
		[DisplayName("最低")]
		public decimal? Low
		{
			get
			{
				return this._Low;
			}
			set
			{
				this._Low = value;
			}
		}

		// Token: 0x17000304 RID: 772
		// (get) Token: 0x060013E2 RID: 5090 RVA: 0x000853F8 File Offset: 0x000835F8
		// (set) Token: 0x060013E3 RID: 5091 RVA: 0x00007FD5 File Offset: 0x000061D5
		[DisplayName("昨收")]
		public decimal? LastDayClose
		{
			get
			{
				return this._LastDayClose;
			}
			set
			{
				this._LastDayClose = value;
			}
		}

		// Token: 0x17000305 RID: 773
		// (get) Token: 0x060013E4 RID: 5092 RVA: 0x00085410 File Offset: 0x00083610
		// (set) Token: 0x060013E5 RID: 5093 RVA: 0x00007FE0 File Offset: 0x000061E0
		[DisplayName("昨持仓量")]
		public decimal? LastDayAmt
		{
			get
			{
				return this._LastDayAmt;
			}
			set
			{
				this._LastDayAmt = value;
			}
		}

		// Token: 0x17000306 RID: 774
		// (get) Token: 0x060013E6 RID: 5094 RVA: 0x00085428 File Offset: 0x00083628
		// (set) Token: 0x060013E7 RID: 5095 RVA: 0x00007FEB File Offset: 0x000061EB
		[DisplayName("行情时间")]
		public DateTime? LastDT
		{
			get
			{
				return this._LastDT;
			}
			set
			{
				this._LastDT = value;
			}
		}

		// Token: 0x17000307 RID: 775
		// (get) Token: 0x060013E8 RID: 5096 RVA: 0x00085440 File Offset: 0x00083640
		[DisplayName("已下载")]
		public string DownLoadedStatusDesc
		{
			get
			{
				string result;
				if (this.IsDownloaded)
				{
					result = "✔";
				}
				else
				{
					result = "";
				}
				return result;
			}
		}

		// Token: 0x17000308 RID: 776
		// (get) Token: 0x060013E9 RID: 5097 RVA: 0x00085468 File Offset: 0x00083668
		// (set) Token: 0x060013EA RID: 5098 RVA: 0x00007FF6 File Offset: 0x000061F6
		public bool IsDownloaded
		{
			get
			{
				return this._IsDownloaded;
			}
			set
			{
				this._IsDownloaded = value;
			}
		}

		// Token: 0x17000309 RID: 777
		// (get) Token: 0x060013EB RID: 5099 RVA: 0x00085480 File Offset: 0x00083680
		// (set) Token: 0x060013EC RID: 5100 RVA: 0x00008001 File Offset: 0x00006201
		public bool IsInZiXuan
		{
			get
			{
				return this._IsInZiXuan;
			}
			set
			{
				this._IsInZiXuan = value;
			}
		}

		// Token: 0x1700030A RID: 778
		// (get) Token: 0x060013ED RID: 5101 RVA: 0x00085498 File Offset: 0x00083698
		// (set) Token: 0x060013EE RID: 5102 RVA: 0x0000800C File Offset: 0x0000620C
		public string UsrName
		{
			get
			{
				return this._UsrName;
			}
			set
			{
				this._UsrName = value;
			}
		}

		// Token: 0x1700030B RID: 779
		// (get) Token: 0x060013EF RID: 5103 RVA: 0x000854B0 File Offset: 0x000836B0
		// (set) Token: 0x060013F0 RID: 5104 RVA: 0x00008017 File Offset: 0x00006217
		public int ExchgId
		{
			get
			{
				return this._ExchgId;
			}
			set
			{
				this._ExchgId = value;
			}
		}

		// Token: 0x1700030C RID: 780
		// (get) Token: 0x060013F1 RID: 5105 RVA: 0x000854C8 File Offset: 0x000836C8
		// (set) Token: 0x060013F2 RID: 5106 RVA: 0x00008022 File Offset: 0x00006222
		public int? IdxInZixuanDGV
		{
			get
			{
				return this._IdxInZixuanDGV;
			}
			set
			{
				this._IdxInZixuanDGV = value;
			}
		}

		// Token: 0x1700030D RID: 781
		// (get) Token: 0x060013F3 RID: 5107 RVA: 0x000854E0 File Offset: 0x000836E0
		// (set) Token: 0x060013F4 RID: 5108 RVA: 0x0000802D File Offset: 0x0000622D
		[DisplayName("行业")]
		public string index_name { get; set; }

		// Token: 0x1700030E RID: 782
		// (get) Token: 0x060013F5 RID: 5109 RVA: 0x000854F8 File Offset: 0x000836F8
		// (set) Token: 0x060013F6 RID: 5110 RVA: 0x00008038 File Offset: 0x00006238
		[DisplayName("昨收")]
		public double? close { get; set; }

		// Token: 0x1700030F RID: 783
		// (get) Token: 0x060013F7 RID: 5111 RVA: 0x00085510 File Offset: 0x00083710
		// (set) Token: 0x060013F8 RID: 5112 RVA: 0x00008043 File Offset: 0x00006243
		[DisplayName("利润总额(万)")]
		public double? total_profit { get; set; }

		// Token: 0x17000310 RID: 784
		// (get) Token: 0x060013F9 RID: 5113 RVA: 0x00085528 File Offset: 0x00083728
		// (set) Token: 0x060013FA RID: 5114 RVA: 0x0000804E File Offset: 0x0000624E
		[DisplayName("净利润(万)")]
		public double? net_profit { get; set; }

		// Token: 0x17000311 RID: 785
		// (get) Token: 0x060013FB RID: 5115 RVA: 0x00085540 File Offset: 0x00083740
		// (set) Token: 0x060013FC RID: 5116 RVA: 0x00008059 File Offset: 0x00006259
		[DisplayName("净资产收益率(%)")]
		public double? roe { get; set; }

		// Token: 0x17000312 RID: 786
		// (get) Token: 0x060013FD RID: 5117 RVA: 0x00085558 File Offset: 0x00083758
		// (set) Token: 0x060013FE RID: 5118 RVA: 0x00008064 File Offset: 0x00006264
		[DisplayName("产品毛利率(%)")]
		public double? grossprofit_margin { get; set; }

		// Token: 0x17000313 RID: 787
		// (get) Token: 0x060013FF RID: 5119 RVA: 0x00085570 File Offset: 0x00083770
		// (set) Token: 0x06001400 RID: 5120 RVA: 0x0000806F File Offset: 0x0000626F
		[DisplayName("应收账款(万)")]
		public double? accounts_receiv { get; set; }

		// Token: 0x17000314 RID: 788
		// (get) Token: 0x06001401 RID: 5121 RVA: 0x00085588 File Offset: 0x00083788
		// (set) Token: 0x06001402 RID: 5122 RVA: 0x0000807A File Offset: 0x0000627A
		[DisplayName("预收款(万)")]
		public double? adv_receipts { get; set; }

		// Token: 0x17000315 RID: 789
		// (get) Token: 0x06001403 RID: 5123 RVA: 0x000855A0 File Offset: 0x000837A0
		// (set) Token: 0x06001404 RID: 5124 RVA: 0x00008085 File Offset: 0x00006285
		[DisplayName("换手率(%)")]
		public double? turnover_rate { get; set; }

		// Token: 0x17000316 RID: 790
		// (get) Token: 0x06001405 RID: 5125 RVA: 0x000855B8 File Offset: 0x000837B8
		// (set) Token: 0x06001406 RID: 5126 RVA: 0x00008090 File Offset: 0x00006290
		[DisplayName("换手率(流通)")]
		public double? turnover_rate_f { get; set; }

		// Token: 0x17000317 RID: 791
		// (get) Token: 0x06001407 RID: 5127 RVA: 0x000855D0 File Offset: 0x000837D0
		// (set) Token: 0x06001408 RID: 5128 RVA: 0x0000809B File Offset: 0x0000629B
		[DisplayName("量比")]
		public double? volume_ratio { get; set; }

		// Token: 0x17000318 RID: 792
		// (get) Token: 0x06001409 RID: 5129 RVA: 0x000855E8 File Offset: 0x000837E8
		// (set) Token: 0x0600140A RID: 5130 RVA: 0x000080A6 File Offset: 0x000062A6
		[DisplayName("市盈率")]
		public double? pe { get; set; }

		// Token: 0x17000319 RID: 793
		// (get) Token: 0x0600140B RID: 5131 RVA: 0x00085600 File Offset: 0x00083800
		// (set) Token: 0x0600140C RID: 5132 RVA: 0x000080B1 File Offset: 0x000062B1
		[DisplayName("市盈率(动)")]
		public double? pe_ttm { get; set; }

		// Token: 0x1700031A RID: 794
		// (get) Token: 0x0600140D RID: 5133 RVA: 0x00085618 File Offset: 0x00083818
		// (set) Token: 0x0600140E RID: 5134 RVA: 0x000080BC File Offset: 0x000062BC
		[DisplayName("市净率")]
		public double? pb { get; set; }

		// Token: 0x1700031B RID: 795
		// (get) Token: 0x0600140F RID: 5135 RVA: 0x00085630 File Offset: 0x00083830
		// (set) Token: 0x06001410 RID: 5136 RVA: 0x000080C7 File Offset: 0x000062C7
		[DisplayName("市销率")]
		public double? ps { get; set; }

		// Token: 0x1700031C RID: 796
		// (get) Token: 0x06001411 RID: 5137 RVA: 0x00085648 File Offset: 0x00083848
		// (set) Token: 0x06001412 RID: 5138 RVA: 0x000080D2 File Offset: 0x000062D2
		[DisplayName("股息率(%)")]
		public double? dv_ratio { get; set; }

		// Token: 0x1700031D RID: 797
		// (get) Token: 0x06001413 RID: 5139 RVA: 0x00085660 File Offset: 0x00083860
		// (set) Token: 0x06001414 RID: 5140 RVA: 0x000080DD File Offset: 0x000062DD
		[DisplayName("总股本(万)")]
		public double? total_share { get; set; }

		// Token: 0x1700031E RID: 798
		// (get) Token: 0x06001415 RID: 5141 RVA: 0x00085678 File Offset: 0x00083878
		// (set) Token: 0x06001416 RID: 5142 RVA: 0x000080E8 File Offset: 0x000062E8
		[DisplayName("总市值(亿)")]
		public double? total_mv { get; set; }

		// Token: 0x1700031F RID: 799
		// (get) Token: 0x06001417 RID: 5143 RVA: 0x00085690 File Offset: 0x00083890
		// (set) Token: 0x06001418 RID: 5144 RVA: 0x000080F3 File Offset: 0x000062F3
		[DisplayName("流通股本(万)")]
		public double? float_share { get; set; }

		// Token: 0x17000320 RID: 800
		// (get) Token: 0x06001419 RID: 5145 RVA: 0x000856A8 File Offset: 0x000838A8
		// (set) Token: 0x0600141A RID: 5146 RVA: 0x000080FE File Offset: 0x000062FE
		[DisplayName("流通市值(亿)")]
		public double? circ_mv { get; set; }

		// Token: 0x17000321 RID: 801
		// (get) Token: 0x0600141B RID: 5147 RVA: 0x000856C0 File Offset: 0x000838C0
		// (set) Token: 0x0600141C RID: 5148 RVA: 0x00008109 File Offset: 0x00006309
		[DisplayName("正股代码")]
		public string cb_stk_code { get; set; }

		// Token: 0x17000322 RID: 802
		// (get) Token: 0x0600141D RID: 5149 RVA: 0x000856D8 File Offset: 0x000838D8
		// (set) Token: 0x0600141E RID: 5150 RVA: 0x00008114 File Offset: 0x00006314
		[DisplayName("正股名称")]
		public string cb_stk_name { get; set; }

		// Token: 0x17000323 RID: 803
		// (get) Token: 0x0600141F RID: 5151 RVA: 0x000856F0 File Offset: 0x000838F0
		// (set) Token: 0x06001420 RID: 5152 RVA: 0x0000811F File Offset: 0x0000631F
		[DisplayName("发行期限(年)")]
		public double? maturity { get; set; }

		// Token: 0x17000324 RID: 804
		// (get) Token: 0x06001421 RID: 5153 RVA: 0x00085708 File Offset: 0x00083908
		// (set) Token: 0x06001422 RID: 5154 RVA: 0x0000812A File Offset: 0x0000632A
		[DisplayName("发行总额(万)")]
		public double? issue_size { get; set; }

		// Token: 0x17000325 RID: 805
		// (get) Token: 0x06001423 RID: 5155 RVA: 0x00085720 File Offset: 0x00083920
		// (set) Token: 0x06001424 RID: 5156 RVA: 0x00008135 File Offset: 0x00006335
		[DisplayName("面值")]
		public double? par { get; set; }

		// Token: 0x17000326 RID: 806
		// (get) Token: 0x06001425 RID: 5157 RVA: 0x00085738 File Offset: 0x00083938
		// (set) Token: 0x06001426 RID: 5158 RVA: 0x00008140 File Offset: 0x00006340
		[DisplayName("起息日期")]
		public string value_date { get; set; }

		// Token: 0x17000327 RID: 807
		// (get) Token: 0x06001427 RID: 5159 RVA: 0x00085750 File Offset: 0x00083950
		// (set) Token: 0x06001428 RID: 5160 RVA: 0x0000814B File Offset: 0x0000634B
		[DisplayName("到期日期")]
		public string maturity_date { get; set; }

		// Token: 0x17000328 RID: 808
		// (get) Token: 0x06001429 RID: 5161 RVA: 0x00085768 File Offset: 0x00083968
		// (set) Token: 0x0600142A RID: 5162 RVA: 0x00008156 File Offset: 0x00006356
		[DisplayName("上市日期")]
		public string list_date { get; set; }

		// Token: 0x17000329 RID: 809
		// (get) Token: 0x0600142B RID: 5163 RVA: 0x00085780 File Offset: 0x00083980
		// (set) Token: 0x0600142C RID: 5164 RVA: 0x00008161 File Offset: 0x00006361
		[DisplayName("摘牌日")]
		public string delist_date { get; set; }

		// Token: 0x1700032A RID: 810
		// (get) Token: 0x0600142D RID: 5165 RVA: 0x00085798 File Offset: 0x00083998
		// (set) Token: 0x0600142E RID: 5166 RVA: 0x0000816C File Offset: 0x0000636C
		[DisplayName("初始转股价")]
		public double? first_conv_price { get; set; }

		// Token: 0x1700032B RID: 811
		// (get) Token: 0x0600142F RID: 5167 RVA: 0x000857B0 File Offset: 0x000839B0
		// (set) Token: 0x06001430 RID: 5168 RVA: 0x00008177 File Offset: 0x00006377
		[DisplayName("最新转股价")]
		public double? conv_price { get; set; }

		// Token: 0x1700032C RID: 812
		// (get) Token: 0x06001431 RID: 5169 RVA: 0x000857C8 File Offset: 0x000839C8
		// (set) Token: 0x06001432 RID: 5170 RVA: 0x00008182 File Offset: 0x00006382
		[DisplayName("到期赎回价格")]
		public double? maturity_put_price { get; set; }

		// Token: 0x04000A48 RID: 2632
		private int _StkId;

		// Token: 0x04000A49 RID: 2633
		private string _StkCode;

		// Token: 0x04000A4A RID: 2634
		private string _CNName;

		// Token: 0x04000A4B RID: 2635
		private decimal? _Price;

		// Token: 0x04000A4C RID: 2636
		private decimal? _Vol;

		// Token: 0x04000A4D RID: 2637
		private decimal? _Amount;

		// Token: 0x04000A4E RID: 2638
		private decimal? _Open;

		// Token: 0x04000A4F RID: 2639
		private decimal? _High;

		// Token: 0x04000A50 RID: 2640
		private decimal? _Low;

		// Token: 0x04000A51 RID: 2641
		private decimal? _LastDayClose;

		// Token: 0x04000A52 RID: 2642
		private decimal? _LastDayAmt;

		// Token: 0x04000A53 RID: 2643
		private DateTime? _LastDT;

		// Token: 0x04000A54 RID: 2644
		private bool _IsDownloaded;

		// Token: 0x04000A55 RID: 2645
		private bool _IsInZiXuan;

		// Token: 0x04000A56 RID: 2646
		private string _UsrName;

		// Token: 0x04000A57 RID: 2647
		private int _ExchgId;

		// Token: 0x04000A58 RID: 2648
		private int? _IdxInZixuanDGV;
	}
}
