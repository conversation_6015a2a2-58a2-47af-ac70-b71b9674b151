﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ns33
{
	// Token: 0x0200021B RID: 539
	[DesignerCategory("Code")]
	internal sealed class Class296 : Panel
	{
		// Token: 0x06001643 RID: 5699 RVA: 0x00008F69 File Offset: 0x00007169
		public Class296()
		{
			base.SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.AllPaintingInWmPaint | ControlStyles.OptimizedDoubleBuffer, true);
		}

		// Token: 0x06001644 RID: 5700 RVA: 0x00095B2C File Offset: 0x00093D2C
		protected void OnPaint(PaintEventArgs e)
		{
			if (this.DrawCustomBorder)
			{
				using (SolidBrush solidBrush = new SolidBrush(this.BackColor))
				{
					e.Graphics.FillRectangle(solidBrush, base.ClientRectangle);
				}
				Pen pen = new Pen(this.BorderColor);
				e.Graphics.DrawRectangle(pen, 0, 0, base.ClientSize.Width - 1, base.ClientSize.Height - 1);
			}
		}

		// Token: 0x17000393 RID: 915
		// (get) Token: 0x06001645 RID: 5701 RVA: 0x00095BB8 File Offset: 0x00093DB8
		// (set) Token: 0x06001646 RID: 5702 RVA: 0x00008F7F File Offset: 0x0000717F
		public bool DrawCustomBorder { get; set; }

		// Token: 0x17000394 RID: 916
		// (get) Token: 0x06001647 RID: 5703 RVA: 0x00095BD0 File Offset: 0x00093DD0
		// (set) Token: 0x06001648 RID: 5704 RVA: 0x00008F8A File Offset: 0x0000718A
		public Color BorderColor { get; set; }

		// Token: 0x04000B42 RID: 2882
		[CompilerGenerated]
		private bool bool_0;

		// Token: 0x04000B43 RID: 2883
		[CompilerGenerated]
		private Color color_0;
	}
}
