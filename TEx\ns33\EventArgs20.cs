﻿using System;

namespace ns33
{
	// Token: 0x02000277 RID: 631
	internal sealed class EventArgs20 : EventArgs
	{
		// Token: 0x06001B7D RID: 7037 RVA: 0x0000B554 File Offset: 0x00009754
		public EventArgs20(object object_2, object object_3)
		{
			this.object_0 = object_2;
			this.object_1 = object_3;
		}

		// Token: 0x1700047B RID: 1147
		// (get) Token: 0x06001B7E RID: 7038 RVA: 0x000B9B9C File Offset: 0x000B7D9C
		// (set) Token: 0x06001B7F RID: 7039 RVA: 0x0000B56C File Offset: 0x0000976C
		public object Trigger
		{
			get
			{
				return this.object_0;
			}
			set
			{
				this.object_0 = value;
			}
		}

		// Token: 0x1700047C RID: 1148
		// (get) Token: 0x06001B80 RID: 7040 RVA: 0x000B9BB4 File Offset: 0x000B7DB4
		// (set) Token: 0x06001B81 RID: 7041 RVA: 0x0000B577 File Offset: 0x00009777
		public object InfrontCtrl
		{
			get
			{
				return this.object_1;
			}
			set
			{
				this.object_1 = value;
			}
		}

		// Token: 0x04000D97 RID: 3479
		private object object_0;

		// Token: 0x04000D98 RID: 3480
		private object object_1;
	}
}
