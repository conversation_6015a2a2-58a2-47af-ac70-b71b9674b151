﻿namespace ns22
{
	// Token: 0x020001A7 RID: 423
	internal sealed partial class MsgWindow : global::System.Windows.Forms.Form
	{
		// Token: 0x06001051 RID: 4177 RVA: 0x0006A608 File Offset: 0x00068808
		private void InitializeComponent()
		{
			this.btn_Close = new global::System.Windows.Forms.Button();
			this.webBrowser1 = new global::System.Windows.Forms.WebBrowser();
			this.chkBox_noticeAgain = new global::System.Windows.Forms.CheckBox();
			base.SuspendLayout();
			this.btn_Close.Font = new global::System.Drawing.Font("SimSun", 9f);
			this.btn_Close.Location = new global::System.Drawing.Point(572, 512);
			this.btn_Close.Name = "btn_Close";
			this.btn_Close.Size = new global::System.Drawing.Size(136, 35);
			this.btn_Close.TabIndex = 1;
			this.btn_Close.Text = "关闭";
			this.btn_Close.UseVisualStyleBackColor = true;
			this.btn_Close.Click += new global::System.EventHandler(this.btn_Close_Click);
			this.webBrowser1.AllowWebBrowserDrop = false;
			this.webBrowser1.IsWebBrowserContextMenuEnabled = false;
			this.webBrowser1.Location = new global::System.Drawing.Point(-1, 0);
			this.webBrowser1.MinimumSize = new global::System.Drawing.Size(23, 23);
			this.webBrowser1.Name = "webBrowser1";
			this.webBrowser1.Size = new global::System.Drawing.Size(818, 498);
			this.webBrowser1.TabIndex = 2;
			this.chkBox_noticeAgain.AutoSize = true;
			this.chkBox_noticeAgain.Font = new global::System.Drawing.Font("SimSun", 9f);
			this.chkBox_noticeAgain.Location = new global::System.Drawing.Point(86, 521);
			this.chkBox_noticeAgain.Name = "chkBox_noticeAgain";
			this.chkBox_noticeAgain.Size = new global::System.Drawing.Size(209, 19);
			this.chkBox_noticeAgain.TabIndex = 3;
			this.chkBox_noticeAgain.Text = "下次登录相同信息不再显示";
			this.chkBox_noticeAgain.UseVisualStyleBackColor = true;
			base.AcceptButton = this.btn_Close;
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(8f, 15f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new global::System.Drawing.Size(818, 557);
			base.Controls.Add(this.chkBox_noticeAgain);
			base.Controls.Add(this.webBrowser1);
			base.Controls.Add(this.btn_Close);
			this.DoubleBuffered = true;
			base.FormBorderStyle = global::System.Windows.Forms.FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = "MsgWindow";
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.SizeGripStyle = global::System.Windows.Forms.SizeGripStyle.Hide;
			base.StartPosition = global::System.Windows.Forms.FormStartPosition.CenterParent;
			this.Text = "提示";
			base.Load += new global::System.EventHandler(this.MsgWindow_Load);
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000827 RID: 2087
		private global::System.Windows.Forms.Button btn_Close;

		// Token: 0x04000828 RID: 2088
		private global::System.Windows.Forms.WebBrowser webBrowser1;

		// Token: 0x04000829 RID: 2089
		private global::System.Windows.Forms.CheckBox chkBox_noticeAgain;
	}
}
