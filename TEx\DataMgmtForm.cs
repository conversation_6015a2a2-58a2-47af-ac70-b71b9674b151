﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using DevComponents.Editors;
using DevComponents.Editors.DateTimeAdv;
using ns13;
using ns14;
using ns22;
using ns28;
using ns32;
using ns9;
using TEx.Comn;
using TEx.ImportTrans;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000156 RID: 342
	public sealed partial class DataMgmtForm : Form
	{
		// Token: 0x06000CF8 RID: 3320 RVA: 0x0004BFD8 File Offset: 0x0004A1D8
		public DataMgmtForm()
		{
			this.InitializeComponent();
			base.Load += this.DataMgmtForm_Load;
			base.FormClosed += this.DataMgmtForm_FormClosed;
			this.button_OK.Click += this.button_OK_Click;
		}

		// Token: 0x06000CF9 RID: 3321 RVA: 0x0004C030 File Offset: 0x0004A230
		private void DataMgmtForm_Load(object sender, EventArgs e)
		{
			DataMgmtForm.Class194 @class = new DataMgmtForm.Class194();
			this.list_0 = Base.Acct.CurrAcctMstSymbols.Cast<TradingSymbol>().ToList<TradingSymbol>();
			if (string.IsNullOrEmpty(Base.UI.Form.LastHisDataExportDir))
			{
				this.string_0 = Environment.GetFolderPath(Environment.SpecialFolder.Personal);
			}
			else
			{
				this.string_0 = Base.UI.Form.LastHisDataExportDir;
			}
			List<ExchgHouse> list = Base.Data.smethod_87();
			this.comboBox_Exchg.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_Exchg.DisplayMember = "Name_CN";
			this.comboBox_Exchg.DataSource = list;
			this.comboBox_Exchg.SelectedIndexChanged += this.comboBox_Exchg_SelectedIndexChanged;
			@class.stkSymbol_0 = null;
			@class.exchgHouse_0 = null;
			if (Base.Data.SymbDataSets.Exists(new Predicate<SymbDataSet>(DataMgmtForm.<>c.<>9.method_0)))
			{
				SymbDataSet symbDataSet = Base.Data.SymbDataSets.FirstOrDefault(new Func<SymbDataSet, bool>(DataMgmtForm.<>c.<>9.method_1));
				@class.stkSymbol_0 = symbDataSet.CurrSymbol;
				@class.exchgHouse_0 = list.SingleOrDefault(new Func<ExchgHouse, bool>(@class.method_0));
				this.comboBox_Exchg.SelectedIndex = list.IndexOf(@class.exchgHouse_0);
			}
			if (@class.exchgHouse_0 == null)
			{
				@class.exchgHouse_0 = list.First<ExchgHouse>();
			}
			List<StkSymbol> list2 = Base.Data.UsrStkSymbols.Values.Where(new Func<StkSymbol, bool>(@class.method_1)).ToList<StkSymbol>();
			this.comboBox_HDExpSymbl.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_HDExpSymbl.DisplayMember = "CNName";
			this.comboBox_HDExpSymbl.DataSource = list2;
			this.comboBox_HDExpSymbl.SelectedIndexChanged += this.comboBox_HDExpSymbl_SelectedIndexChanged;
			if (@class.stkSymbol_0 != null)
			{
				int num = list2.IndexOf(@class.stkSymbol_0);
				if (num >= 0)
				{
					this.comboBox_HDExpSymbl.SelectedIndex = num;
				}
			}
			else
			{
				@class.stkSymbol_0 = list2.First<StkSymbol>();
			}
			this.method_17(@class.stkSymbol_0);
			this.comboBox_HDExpDataFormat.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_HDExpDataFormat.SelectedIndexChanged += this.comboBox_HDExpDataFormat_SelectedIndexChanged;
			this.comboBox_PeriodType.DropDownStyle = ComboBoxStyle.DropDownList;
			Base.UI.smethod_80(this.dateTimeInput_HDExpStart);
			Base.UI.smethod_80(this.dateTimeInput_HDExpEnd);
			this.dateTimeInput_HDExpEnd.ValueChanged += this.dateTimeInput_HDExpEnd_ValueChanged;
			this.dateTimeInput_HDExpEnd.Leave += this.dateTimeInput_HDExpEnd_Leave;
			this.btn_HDExport.Click += this.btn_HDExport_Click;
			this.btn_HDExpOpenDFolder.Click += this.btn_HDExpOpenDFolder_Click;
			this.comboBox_PeriodType.DropDownStyle = ComboBoxStyle.DropDownList;
			if (TApp.SrvParams.MinHDExpPeriodUnits <= 1)
			{
				this.comboBox_PeriodType.Items.Add("1分钟");
			}
			if (TApp.SrvParams.MinHDExpPeriodUnits <= 3)
			{
				this.comboBox_PeriodType.Items.Add("3分钟");
			}
			if (TApp.SrvParams.MinHDExpPeriodUnits <= 5)
			{
				this.comboBox_PeriodType.Items.Add("5分钟");
			}
			if (TApp.SrvParams.MinHDExpPeriodUnits <= 15)
			{
				this.comboBox_PeriodType.Items.Add("15分钟");
			}
			if (TApp.SrvParams.MinHDExpPeriodUnits <= 30)
			{
				this.comboBox_PeriodType.Items.Add("30分钟");
			}
			if (TApp.SrvParams.MinHDExpPeriodUnits <= 60)
			{
				this.comboBox_PeriodType.Items.Add("60分钟");
			}
			this.comboBox_PeriodType.Items.Add("日线");
			this.comboBox_PeriodType.Items.Add("周线");
			this.comboBox_PeriodType.Items.Add("月线");
			this.comboBox_PeriodType.SelectedIndex = 0;
			this.comboBox_HDExpDataFormat.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_HDExpDataFormat.Items.Add("CSV格式(逗号分隔)");
			this.comboBox_HDExpDataFormat.Items.Add("TXT格式(空格分隔)");
			this.comboBox_HDExpDataFormat.SelectedIndex = 0;
			this.checkBox_inclFirstLine.Checked = true;
			this.textBox_HDExpFilePath.Text = this.string_0 + "\\TExHisData.csv";
			this.label_HDExpStatus.Text = string.Empty;
			this.progressBar_HDExp.Visible = false;
			this.backgroundWorker_0 = new BackgroundWorker();
			this.backgroundWorker_0.WorkerReportsProgress = true;
			this.backgroundWorker_0.WorkerSupportsCancellation = true;
			this.backgroundWorker_0.DoWork += this.backgroundWorker_0_DoWork;
			this.backgroundWorker_0.ProgressChanged += this.backgroundWorker_0_ProgressChanged;
			this.backgroundWorker_0.RunWorkerCompleted += this.backgroundWorker_0_RunWorkerCompleted;
			List<Account> currAccounts = Base.Acct.CurrAccounts;
			this.comboBox_AcctToOpt.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_AcctToOpt.DisplayMember = "AcctName";
			this.comboBox_AcctToOpt.DataSource = currAccounts;
			for (int i = 0; i < currAccounts.Count; i++)
			{
				if (currAccounts[i] == Base.Acct.CurrAccount)
				{
					this.comboBox_AcctToOpt.SelectedIndex = i;
				}
			}
			this.comboBox_AcctToOpt.SelectedIndexChanged += this.comboBox_AcctToOpt_SelectedIndexChanged;
			this.comboBox_AcctSymbToOpt.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_AcctSymbToOpt.DisplayMember = "CNName";
			this.comboBox_AcctSymbToOpt.SelectedIndexChanged += this.comboBox_AcctSymbToOpt_SelectedIndexChanged;
			this.method_18();
			this.checkBox_IfAllAcctSymbForOpt.CheckedChanged += this.checkBox_IfAllAcctSymbForOpt_CheckedChanged;
			this.comboBox_AcctToReceiveTrans.DisplayMember = "AcctName";
			this.comboBox_AcctToReceiveTrans.DropDownStyle = ComboBoxStyle.DropDownList;
			this.btn_CopyTrans.Click += this.btn_CopyTrans_Click;
			this.btn_MoveTrans.Click += this.btn_MoveTrans_Click;
			this.btn_DelAcctTrans.Click += this.btn_DelAcctTrans_Click;
			this.btn_TransExpOpenDFolder.Click += this.btn_TransExpOpenDFolder_Click;
			this.btn_TransExp.Click += this.btn_TransExp_Click;
			if (string.IsNullOrEmpty(Base.UI.Form.LastAcctTransExportDir))
			{
				this.string_1 = Environment.GetFolderPath(Environment.SpecialFolder.Personal);
			}
			else
			{
				this.string_1 = Base.UI.Form.LastAcctTransExportDir;
			}
			if (!string.IsNullOrEmpty(Base.UI.Form.LastTransImportFilePath))
			{
				this.string_4 = Base.UI.Form.LastTransImportFilePath;
			}
			this.textBox_TransExpFilePath.Text = this.string_1 + "\\TExTrans.csv";
			this.textBox_TransImpFilePath.Text = this.string_4;
			this.btn_TransImpOpenDFolder.Click += this.btn_TransImpOpenDFolder_Click;
			this.button_LoadTrans.Click += this.button_LoadTrans_Click;
			this.method_2();
			this.method_34(true);
			this.method_32();
			this.label_CfmmcDnStatus.Text = string.Empty;
			CfmmcRecImporter.LoggingIn += this.method_23;
			CfmmcRecImporter.LoginSuccess += this.method_24;
			CfmmcRecImporter.StartDownloadRecs += this.method_25;
			CfmmcRecImporter.NotifyDnRecIndex += this.method_26;
			CfmmcRecImporter.RecImportSuccess += this.method_27;
			CfmmcRecImporter.RecImportFailed += this.method_28;
			CfmmcRecImporter.NoRecNeedToDnldDetected += this.method_30;
			this.btn_AddCfmmcAcct.Click += this.btn_AddCfmmcAcct_Click;
			this.btn_EditCfmmcAcct.Click += this.btn_EditCfmmcAcct_Click;
			this.btn_DelCfmmcAcct.Click += this.btn_DelCfmmcAcct_Click;
			this.btn_CfmmcDownBg.Click += this.btn_CfmmcDownBg_Click;
			this.comboBox_CfmmcAcct.SelectedIndexChanged += this.comboBox_CfmmcAcct_SelectedIndexChanged;
			this.TimePicker_AutoDown.ValueChanged += this.TimePicker_AutoDown_ValueChanged;
			if (this.list_2 == null)
			{
				this.list_2 = new List<string>();
			}
			if (this.list_3 == null)
			{
				this.list_3 = new List<string>();
			}
			List<string> list3 = Class463.smethod_12();
			if (list3 != null && list3.Any<string>())
			{
				this.list_2.Clear();
				this.list_3.Clear();
				for (int j = 0; j < list3.Count; j++)
				{
					string[] array = list3[j].Split(new char[]
					{
						' '
					});
					if (array.Length == 2)
					{
						this.list_2.Add(array[0]);
						this.list_3.Add(array[1]);
					}
				}
			}
			this.method_3();
			this.label_cfmmcNotice.ForeColor = Color.Black;
			if (TApp.IsTrialUser)
			{
				this.label_cfmmcNotice.Text = "通过添加中国期货市场监控中心(CFMMC)查询账号，免费版用户可下载近1个月（正式版用户可下载监控中心提供的所有历史记录）的实盘交易记录到本地模拟交易账户，进行实盘交易统计和复盘分析。此查询账号和密码请咨询您的期货公司获得。";
			}
			LabelX labelX = this.label_cfmmcNotice;
			labelX.Text = labelX.Text + Environment.NewLine + Environment.NewLine + "下载记录后，建议在参数设置窗口「界面参数」栏内，「交易显示」-「显示开平仓标示箭头」项，选中「所有交易」，即可在相应品种K线图上自动显示箭头标示。";
		}

		// Token: 0x06000CFA RID: 3322 RVA: 0x0004C900 File Offset: 0x0004AB00
		private void DataMgmtForm_FormClosed(object sender, FormClosedEventArgs e)
		{
			CfmmcRecImporter.LoggingIn -= this.method_23;
			CfmmcRecImporter.LoginSuccess -= this.method_24;
			CfmmcRecImporter.StartDownloadRecs -= this.method_25;
			CfmmcRecImporter.NotifyDnRecIndex -= this.method_26;
			CfmmcRecImporter.RecImportSuccess -= this.method_27;
			CfmmcRecImporter.RecImportFailed -= this.method_28;
			CfmmcRecImporter.NoRecNeedToDnldDetected -= this.method_30;
			CfmmcRecImporter.smethod_44(true);
			this.btn_CfmmcDownBg.Click -= this.btn_CfmmcDownBg_Click;
		}

		// Token: 0x06000CFB RID: 3323 RVA: 0x0004C9A4 File Offset: 0x0004ABA4
		private void button_OK_Click(object sender, EventArgs e)
		{
			CfmmcAutoDnldConfig cfmmcAutoDnldConfig = Base.UI.Form.CfmmcAutoDnldConfig;
			if (this.radioBtn_StartUpDnldCfmmc.Checked)
			{
				cfmmcAutoDnldConfig.AutoDownOnStartup = true;
			}
			else
			{
				cfmmcAutoDnldConfig.AutoDownOnStartup = false;
			}
			if (this.radioBtn_PeriodlyDnldCfmmc.Checked)
			{
				cfmmcAutoDnldConfig.AutoDownPeriodly = true;
				if (this.radio_autoDay.Checked)
				{
					cfmmcAutoDnldConfig.Frequency = AutoDownCfmmcFrequencyEnum.每天;
				}
				else
				{
					cfmmcAutoDnldConfig.Frequency = AutoDownCfmmcFrequencyEnum.每周;
				}
				int num = this.comboBox_DayOfWeekDnCfmmc.SelectedIndex - 1;
				if (num < 0)
				{
					num = 6;
				}
				cfmmcAutoDnldConfig.WklyDnldDayOfWeek = (DayOfWeek)num;
			}
			else
			{
				cfmmcAutoDnldConfig.AutoDownPeriodly = false;
			}
			Base.UI.smethod_47();
			CfmmcRecImporter.smethod_12();
		}

		// Token: 0x06000CFC RID: 3324 RVA: 0x0004CA3C File Offset: 0x0004AC3C
		private void btn_AddCfmmcAcct_Click(object sender, EventArgs e)
		{
			if (TApp.IsTrialUser && this.comboBox_CfmmcAcct.Items.Count > 0)
			{
				MessageBox.Show("免费版用户仅能添加一个期货监控中心查询账号（正式版用户无此限制）。 ", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
			else
			{
				CreateCfmmcAcctFrm createCfmmcAcctFrm = new CreateCfmmcAcctFrm();
				createCfmmcAcctFrm.RecAdded += this.method_0;
				createCfmmcAcctFrm.ShowDialog();
			}
		}

		// Token: 0x06000CFD RID: 3325 RVA: 0x0004CA98 File Offset: 0x0004AC98
		private void btn_EditCfmmcAcct_Click(object sender, EventArgs e)
		{
			if (this.comboBox_CfmmcAcct.Items.Count > 0)
			{
				CreateCfmmcAcctFrm createCfmmcAcctFrm = new CreateCfmmcAcctFrm(Class463.smethod_11(((this.comboBox_CfmmcAcct.SelectedItem as TEx.Util.ComboBoxItem).Value as CfmmcAcct).ID));
				createCfmmcAcctFrm.RecUpdated += this.method_1;
				createCfmmcAcctFrm.ShowDialog();
			}
		}

		// Token: 0x06000CFE RID: 3326 RVA: 0x0004CAFC File Offset: 0x0004ACFC
		private void btn_DelCfmmcAcct_Click(object sender, EventArgs e)
		{
			if (this.comboBox_CfmmcAcct.Items.Count > 0)
			{
				CfmmcAcct cfmmcAcct = (this.comboBox_CfmmcAcct.SelectedItem as TEx.Util.ComboBoxItem).Value as CfmmcAcct;
				if (MessageBox.Show("删除选中的监控中心账号（" + cfmmcAcct.ID + "）吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
				{
					Class463.smethod_7(cfmmcAcct);
					this.method_2();
					this.method_3();
				}
			}
		}

		// Token: 0x06000CFF RID: 3327 RVA: 0x00005CB8 File Offset: 0x00003EB8
		private void method_0(object sender, EventArgs e)
		{
			this.method_2();
			this.method_3();
		}

		// Token: 0x06000D00 RID: 3328 RVA: 0x00005CC8 File Offset: 0x00003EC8
		private void method_1(object sender, EventArgs e)
		{
			this.method_3();
		}

		// Token: 0x06000D01 RID: 3329 RVA: 0x0004CB74 File Offset: 0x0004AD74
		private void method_2()
		{
			ComboBox comboBox = this.comboBox_CfmmcAcct;
			comboBox.Items.Clear();
			List<CfmmcAcct> list = Class463.smethod_9();
			if (list != null && list.Any<CfmmcAcct>())
			{
				foreach (CfmmcAcct cfmmcAcct in list)
				{
					if (cfmmcAcct.BindingAccts != null)
					{
						if (cfmmcAcct.BindingAccts.Exists(new Predicate<BindingAcct>(DataMgmtForm.<>c.<>9.method_2)))
						{
							TEx.Util.ComboBoxItem comboBoxItem = new TEx.Util.ComboBoxItem();
							comboBoxItem.Text = cfmmcAcct.ID;
							comboBoxItem.Value = cfmmcAcct;
							comboBox.Items.Add(comboBoxItem);
						}
					}
				}
				if (comboBox.Items.Count > 0)
				{
					comboBox.SelectedIndex = 0;
				}
			}
		}

		// Token: 0x06000D02 RID: 3330 RVA: 0x0004CC5C File Offset: 0x0004AE5C
		private void method_3()
		{
			string str = "（无）";
			int num = 0;
			if (this.comboBox_CfmmcAcct.Items.Count > 0)
			{
				CfmmcAcct cfmmcAcct = Class463.smethod_11(((this.comboBox_CfmmcAcct.SelectedItem as TEx.Util.ComboBoxItem).Value as CfmmcAcct).ID);
				if (!string.IsNullOrEmpty(cfmmcAcct.Note))
				{
					str = cfmmcAcct.Note;
				}
				List<List<string>> list = Class463.smethod_15(cfmmcAcct.ID);
				if (list != null)
				{
					num = list.Count;
				}
			}
			this.label_cfmmcAcctNote.Text = "备注：" + str;
			this.label_DnldCfmmcRecNb.Text = "已下载记录数：" + num.ToString();
		}

		// Token: 0x06000D03 RID: 3331 RVA: 0x0004CD08 File Offset: 0x0004AF08
		private void btn_HDExport_Click(object sender, EventArgs e)
		{
			if (this.btn_HDExport.Text == "导出")
			{
				try
				{
					string text = this.textBox_HDExpFilePath.Text;
					if (string.IsNullOrEmpty(text))
					{
						return;
					}
					if (!text.ToLower().EndsWith(".csv"))
					{
						text += ".csv";
					}
					FileInfo fileInfo;
					try
					{
						fileInfo = new FileInfo(text);
					}
					catch
					{
						MessageBox.Show("文件路径非法，请指定有效的导出文件路径！", "请确认", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
						return;
					}
					if (!fileInfo.Directory.Exists)
					{
						if (MessageBox.Show("指定文件目录不存在，创建该目录吗？ ", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
						{
							return;
						}
						fileInfo.Directory.Create();
					}
					if (fileInfo.Exists && MessageBox.Show("文件已存在，覆盖该文件吗？ ", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
					{
						return;
					}
					this.method_16(false);
					Struct4 @struct = default(Struct4);
					@struct.stkSymbol_0 = (this.comboBox_HDExpSymbl.SelectedItem as StkSymbol);
					@struct.dateTime_0 = this.dateTimeInput_HDExpStart.Value;
					@struct.dateTime_1 = this.dateTimeInput_HDExpEnd.Value;
					@struct.string_1 = text;
					@struct.string_0 = ((this.comboBox_HDExpDataFormat.SelectedIndex == 0) ? "," : " ");
					@struct.bool_0 = this.checkBox_inclFirstLine.Checked;
					@struct.bool_1 = this.checkBox_IfCombinedDT.Checked;
					@struct.periodType_0 = this.method_5();
					@struct.nullable_0 = this.method_6();
					while (!this.backgroundWorker_0.IsBusy)
					{
						this.backgroundWorker_0.RunWorkerAsync(@struct);
					}
					return;
				}
				catch (Exception ex)
				{
					this.method_4("未能成功导出数据文件。", "错误原因：" + ex.Message);
					return;
				}
			}
			this.backgroundWorker_0.CancelAsync();
		}

		// Token: 0x06000D04 RID: 3332 RVA: 0x00005CD2 File Offset: 0x00003ED2
		private void method_4(string string_5, string string_6)
		{
			MessageBox.Show(string_5 + Environment.NewLine + Environment.NewLine + string_6, "错误", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
		}

		// Token: 0x06000D05 RID: 3333 RVA: 0x0004CF10 File Offset: 0x0004B110
		private PeriodType method_5()
		{
			string text = this.comboBox_PeriodType.Text;
			PeriodType result;
			if (text == "日线")
			{
				result = PeriodType.ByDay;
			}
			else if (text == "周线")
			{
				result = PeriodType.ByWeek;
			}
			else if (text == "月线")
			{
				result = PeriodType.ByMonth;
			}
			else
			{
				result = PeriodType.ByMins;
			}
			return result;
		}

		// Token: 0x06000D06 RID: 3334 RVA: 0x0004CF64 File Offset: 0x0004B164
		private int? method_6()
		{
			string text = this.comboBox_PeriodType.Text;
			int? result = null;
			if (text.EndsWith("分钟"))
			{
				result = new int?(Convert.ToInt32(text.Replace("分钟", "")));
			}
			return result;
		}

		// Token: 0x06000D07 RID: 3335 RVA: 0x0004CFB4 File Offset: 0x0004B1B4
		private void method_7(TabPage tabPage_0, bool bool_0)
		{
			foreach (object obj in tabPage_0.Controls)
			{
				((Control)obj).Enabled = bool_0;
			}
		}

		// Token: 0x06000D08 RID: 3336 RVA: 0x0004D010 File Offset: 0x0004B210
		private void backgroundWorker_0_DoWork(object sender, DoWorkEventArgs e)
		{
			BackgroundWorker backgroundWorker = sender as BackgroundWorker;
			Struct4 @struct = (Struct4)e.Argument;
			DateTime dateTime_ = @struct.dateTime_0;
			DateTime dateTime_2 = @struct.dateTime_1;
			StkSymbol stkSymbol_ = @struct.stkSymbol_0;
			string text = @struct.string_1;
			bool bool_ = @struct.bool_0;
			bool bool_2 = @struct.bool_1;
			string text2 = @struct.string_0;
			PeriodType periodType_ = @struct.periodType_0;
			int? nullable_ = @struct.nullable_0;
			if (DataMgmtForm.smethod_0(text))
			{
				MessageBox.Show("该文件正被其他程序占用，无法覆盖！ ", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
			else
			{
				this.method_10(backgroundWorker, 0, "正在提取数据...");
				SortedList<DateTime, HisData> sortedList = Base.Data.smethod_59(stkSymbol_, dateTime_, dateTime_2);
				if (backgroundWorker.CancellationPending)
				{
					e.Cancel = true;
				}
				else if (sortedList != null && sortedList.Count >= 1)
				{
					this.method_10(backgroundWorker, 100, "提取数据完毕。");
					SortedList<DateTime, HisData> sortedList2 = new SortedList<DateTime, HisData>();
					if (periodType_ == PeriodType.ByMins)
					{
						if (nullable_.Value == 1)
						{
							sortedList2 = sortedList;
						}
						else
						{
							if (nullable_.Value < 60)
							{
								this.method_10(backgroundWorker, 0, "正在整理数据...");
								int num = Convert.ToInt32((dateTime_2.Date - dateTime_.Date).TotalDays);
								int num2 = 0;
								DateTime dateTime = dateTime_;
								for (;;)
								{
									num2 += 30;
									DateTime dateTime_3 = dateTime.AddDays(30.0);
									if (dateTime_3.Date > dateTime_2.Date)
									{
										dateTime_3 = dateTime_2;
									}
									SortedList<DateTime, HisData> sortedList3 = null;
									try
									{
										sortedList3 = Base.Data.smethod_46(sortedList, stkSymbol_, dateTime, dateTime_3, nullable_.Value, null);
									}
									catch
									{
										continue;
									}
									if (sortedList3 != null && sortedList3.Any<KeyValuePair<DateTime, HisData>>())
									{
										foreach (KeyValuePair<DateTime, HisData> keyValuePair in new HisDataPeriodSet(Base.Data.smethod_50(stkSymbol_.ID, true, false), sortedList3, periodType_, nullable_).PeriodHisDataList)
										{
											sortedList2.Add(keyValuePair.Key, keyValuePair.Value);
										}
										if (num2 > num)
										{
											num2 = num;
										}
										if (backgroundWorker.CancellationPending)
										{
											goto IL_26A;
										}
										this.method_11(backgroundWorker, "正在整理数据...", new int?(num2), new int?(num));
										dateTime = dateTime_3.AddDays(1.0);
										if (dateTime > dateTime_2)
										{
											break;
										}
									}
									else
									{
										dateTime = dateTime_3.AddDays(1.0);
										if (dateTime > dateTime_2)
										{
											break;
										}
									}
								}
								goto IL_2E0;
								IL_26A:
								e.Cancel = true;
								return;
							}
							if (nullable_.Value == 60)
							{
								sortedList2 = this.method_8(stkSymbol_.ID);
							}
						}
					}
					else
					{
						this.method_10(backgroundWorker, 0, "正在整理数据...");
						SortedList<DateTime, HisData> hisDataList = this.method_8(stkSymbol_.ID);
						if (backgroundWorker.CancellationPending)
						{
							e.Cancel = true;
							return;
						}
						sortedList2 = new HisDataPeriodSet(stkSymbol_.ID, hisDataList, periodType_, null).PeriodHisDataList;
					}
					IL_2E0:
					if (backgroundWorker.CancellationPending)
					{
						e.Cancel = true;
					}
					else
					{
						using (StreamWriter streamWriter = new StreamWriter(text, false, Encoding.GetEncoding("GB2312")))
						{
							if (bool_)
							{
								streamWriter.WriteLine(string.Concat(new string[]
								{
									"Date",
									text2,
									"Time",
									text2,
									"Open",
									text2,
									"Close",
									text2,
									"High",
									text2,
									"Low",
									text2,
									"Volume",
									text2,
									"Amount"
								}));
							}
							for (int i = 0; i < sortedList2.Count; i++)
							{
								HisData hisData = sortedList2.Values[i];
								string text3 = bool_2 ? hisData.Date.ToString("yyyy-MM-dd HH:mm:ss") : (hisData.Date.ToString("yyyy-MM-dd") + text2 + hisData.Date.ToString("HH:mm:ss"));
								string value = string.Concat(new object[]
								{
									text3,
									text2,
									hisData.Open,
									text2,
									hisData.Close,
									text2,
									hisData.High,
									text2,
									hisData.Low,
									text2,
									hisData.Volume,
									text2,
									hisData.Amount
								});
								streamWriter.WriteLine(value);
								if (backgroundWorker.CancellationPending)
								{
									e.Cancel = true;
									goto IL_521;
								}
								if (Utility.CanExactDiv(i + 1, 100))
								{
									this.method_11(backgroundWorker, "正在导出记录...", new int?(i + 1), new int?(sortedList2.Count));
								}
							}
						}
						this.method_11(backgroundWorker, "导出完毕。共导出" + sortedList2.Count + "条记录。", new int?(sortedList2.Count), new int?(sortedList2.Count));
						IL_521:;
					}
				}
				else
				{
					this.method_12(backgroundWorker, "所选期间内没有历史行情数据！", "");
				}
			}
		}

		// Token: 0x06000D09 RID: 3337 RVA: 0x0004D5A0 File Offset: 0x0004B7A0
		private SortedList<DateTime, HisData> method_8(int int_0)
		{
			UsrStkMeta usrStkMeta_ = Base.Data.smethod_90(int_0);
			return Base.Data.smethod_50(int_0, true, false).method_52(usrStkMeta_).PeriodHisDataList;
		}

		// Token: 0x06000D0A RID: 3338 RVA: 0x00005CF5 File Offset: 0x00003EF5
		private void method_9(BackgroundWorker backgroundWorker_1, string string_5)
		{
			this.method_10(backgroundWorker_1, -1, string_5);
		}

		// Token: 0x06000D0B RID: 3339 RVA: 0x00005D02 File Offset: 0x00003F02
		private void method_10(BackgroundWorker backgroundWorker_1, int int_0, string string_5)
		{
			backgroundWorker_1.ReportProgress(int_0, this.method_14(string.Empty, string_5));
		}

		// Token: 0x06000D0C RID: 3340 RVA: 0x00005D1E File Offset: 0x00003F1E
		private void method_11(BackgroundWorker backgroundWorker_1, string string_5, int? nullable_0, int? nullable_1)
		{
			this.method_13(backgroundWorker_1, string.Empty, string_5, nullable_0, nullable_1);
		}

		// Token: 0x06000D0D RID: 3341 RVA: 0x00005D32 File Offset: 0x00003F32
		private void method_12(BackgroundWorker backgroundWorker_1, string string_5, string string_6)
		{
			backgroundWorker_1.ReportProgress(-1, this.method_14(string_5, string_6));
		}

		// Token: 0x06000D0E RID: 3342 RVA: 0x00005D4A File Offset: 0x00003F4A
		private void method_13(BackgroundWorker backgroundWorker_1, string string_5, string string_6, int? nullable_0, int? nullable_1)
		{
			backgroundWorker_1.ReportProgress(-1, this.method_15(string_5, string_6, nullable_0, nullable_1));
		}

		// Token: 0x06000D0F RID: 3343 RVA: 0x0004D5CC File Offset: 0x0004B7CC
		private Struct5 method_14(string string_5, string string_6)
		{
			return this.method_15(string_5, string_6, null, null);
		}

		// Token: 0x06000D10 RID: 3344 RVA: 0x0004D5F8 File Offset: 0x0004B7F8
		private Struct5 method_15(string string_5, string string_6, int? nullable_0, int? nullable_1)
		{
			return new Struct5
			{
				string_0 = string_5,
				string_1 = string_6,
				nullable_0 = nullable_0,
				nullable_1 = nullable_1
			};
		}

		// Token: 0x06000D11 RID: 3345 RVA: 0x0004D634 File Offset: 0x0004B834
		private void backgroundWorker_0_ProgressChanged(object sender, ProgressChangedEventArgs e)
		{
			if (e.UserState != null)
			{
				Struct5 @struct = (Struct5)e.UserState;
				if (!string.IsNullOrEmpty(@struct.string_0))
				{
					MessageBox.Show(@struct.string_0, "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					if (this.progressBar_HDExp.Visible)
					{
						this.progressBar_HDExp.Visible = false;
					}
					this.label_HDExpStatus.Text = string.Empty;
					return;
				}
				if (!string.IsNullOrEmpty(@struct.string_1))
				{
					this.label_HDExpStatus.Text = @struct.string_1;
					if (!this.label_HDExpStatus.Visible)
					{
						this.label_HDExpStatus.Visible = true;
					}
					this.label_HDExpStatus.Refresh();
				}
				if (@struct.nullable_0 != null)
				{
					if (this.progressBar_HDExp.Minimum != 0)
					{
						this.progressBar_HDExp.Minimum = 0;
					}
					if (this.progressBar_HDExp.Maximum != @struct.nullable_1.Value)
					{
						this.progressBar_HDExp.Maximum = @struct.nullable_1.Value;
					}
					this.progressBar_HDExp.Value = @struct.nullable_0.Value;
					if (!this.progressBar_HDExp.Visible)
					{
						this.progressBar_HDExp.Visible = true;
					}
				}
			}
			int progressPercentage = e.ProgressPercentage;
			if (progressPercentage >= 0 && this.progressBar_HDExp.Value != progressPercentage)
			{
				if (this.progressBar_HDExp.Maximum != 100)
				{
					this.progressBar_HDExp.Maximum = 100;
				}
				this.progressBar_HDExp.Value = progressPercentage;
				if (!this.progressBar_HDExp.Visible)
				{
					this.progressBar_HDExp.Visible = true;
				}
			}
		}

		// Token: 0x06000D12 RID: 3346 RVA: 0x0004D7CC File Offset: 0x0004B9CC
		private void backgroundWorker_0_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
		{
			Exception error = e.Error;
			if (e.Cancelled)
			{
				this.method_16(true);
				this.label_HDExpStatus.Text = string.Empty;
				this.progressBar_HDExp.Visible = false;
			}
			else
			{
				this.method_16(true);
				Base.UI.Form.LastHisDataExportDir = Path.GetDirectoryName(this.textBox_HDExpFilePath.Text);
			}
		}

		// Token: 0x06000D13 RID: 3347 RVA: 0x0004D830 File Offset: 0x0004BA30
		private void method_16(bool bool_0)
		{
			if (bool_0)
			{
				this.btn_HDExport.Text = "导出";
			}
			else
			{
				this.btn_HDExport.Text = "取消";
			}
			this.groupBox_SelSymb.Enabled = bool_0;
			this.groupBox_HDExpFormat.Enabled = bool_0;
			this.groupBox_HDExpDates.Enabled = bool_0;
			this.btn_HDExpOpenDFolder.Enabled = bool_0;
			this.button_OK.Enabled = bool_0;
			this.method_7(this.tabPage_Acct, bool_0);
		}

		// Token: 0x06000D14 RID: 3348 RVA: 0x0004D8B0 File Offset: 0x0004BAB0
		private void comboBox_Exchg_SelectedIndexChanged(object sender, EventArgs e)
		{
			DataMgmtForm.Class195 @class = new DataMgmtForm.Class195();
			@class.exchgHouse_0 = (ExchgHouse)this.comboBox_Exchg.SelectedItem;
			this.comboBox_HDExpSymbl.DataSource = Base.Data.UsrStkSymbols.Values.Where(new Func<StkSymbol, bool>(@class.method_0)).ToList<StkSymbol>();
		}

		// Token: 0x06000D15 RID: 3349 RVA: 0x0004D908 File Offset: 0x0004BB08
		private void comboBox_HDExpSymbl_SelectedIndexChanged(object sender, EventArgs e)
		{
			StkSymbol stkSymbol_ = (StkSymbol)this.comboBox_HDExpSymbl.SelectedItem;
			this.method_17(stkSymbol_);
			this.label_HDExpStatus.Text = string.Empty;
			this.progressBar_HDExp.Visible = false;
		}

		// Token: 0x06000D16 RID: 3350 RVA: 0x0004D94C File Offset: 0x0004BB4C
		private void method_17(StkSymbol stkSymbol_0)
		{
			UsrStkMeta usrStkMeta = Base.Data.smethod_90(stkSymbol_0.ID);
			if (usrStkMeta == null)
			{
				this.dateTimeInput_HDExpStart.Enabled = false;
				this.dateTimeInput_HDExpEnd.Enabled = false;
				this.btn_HDExport.Enabled = false;
			}
			else
			{
				this.dateTimeInput_HDExpStart.Enabled = true;
				this.dateTimeInput_HDExpEnd.Enabled = true;
				this.btn_HDExport.Enabled = true;
				DateTime value = this.dateTimeInput_HDExpStart.Value;
				if (this.dateTimeInput_HDExpStart.Value == DateTime.MinValue || this.dateTimeInput_HDExpStart.Value.Date < usrStkMeta.BeginDate.Value.Date)
				{
					this.dateTimeInput_HDExpStart.Value = usrStkMeta.BeginDate.Value;
				}
				this.dateTimeInput_HDExpStart.MinDate = usrStkMeta.BeginDate.Value;
				this.dateTimeInput_HDExpStart.MaxDate = usrStkMeta.EndDate.Value;
				DateTime value2 = this.dateTimeInput_HDExpEnd.Value;
				if (this.dateTimeInput_HDExpEnd.Value == DateTime.MinValue || this.dateTimeInput_HDExpEnd.Value.Date > usrStkMeta.EndDate.Value.Date)
				{
					this.dateTimeInput_HDExpEnd.Value = usrStkMeta.EndDate.Value;
				}
				this.dateTimeInput_HDExpEnd.MinDate = usrStkMeta.BeginDate.Value;
				this.dateTimeInput_HDExpEnd.MaxDate = usrStkMeta.EndDate.Value;
				if (this.dateTimeInput_HDExpEnd.Value < this.dateTimeInput_HDExpStart.Value)
				{
					this.dateTimeInput_HDExpEnd.Value = usrStkMeta.EndDate.Value;
				}
			}
		}

		// Token: 0x06000D17 RID: 3351 RVA: 0x00005D66 File Offset: 0x00003F66
		private void comboBox_AcctToOpt_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.method_18();
		}

		// Token: 0x06000D18 RID: 3352 RVA: 0x00005D70 File Offset: 0x00003F70
		private void comboBox_AcctSymbToOpt_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.method_19();
		}

		// Token: 0x06000D19 RID: 3353 RVA: 0x0004DB2C File Offset: 0x0004BD2C
		private void method_18()
		{
			DataMgmtForm.Class196 @class = new DataMgmtForm.Class196();
			@class.account_0 = (Account)this.comboBox_AcctToOpt.SelectedItem;
			this.list_1 = Base.Trading.smethod_124(@class.account_0.ID);
			this.label_AccTransTotal.Text = "记录数:" + this.list_1.Count;
			List<StkSymbol> list = Base.Trading.smethod_125(this.list_1);
			this.comboBox_AcctSymbToOpt.DataSource = list;
			this.method_19();
			List<Account> list2 = (this.comboBox_AcctToOpt.DataSource as List<Account>).Where(new Func<Account, bool>(@class.method_0)).ToList<Account>();
			this.comboBox_AcctToReceiveTrans.DataSource = list2;
			if (list2.Count < 1)
			{
				this.comboBox_AcctToReceiveTrans.Enabled = false;
				this.btn_MoveTrans.Enabled = false;
				this.btn_CopyTrans.Enabled = false;
			}
			else
			{
				this.comboBox_AcctToReceiveTrans.Enabled = true;
				this.btn_MoveTrans.Enabled = true;
				this.btn_CopyTrans.Enabled = true;
			}
			if (this.checkBox_IfAllAcctSymbForOpt.Checked && list.Count < 1)
			{
				this.comboBox_AcctSymbToOpt.Text = string.Empty;
			}
		}

		// Token: 0x06000D1A RID: 3354 RVA: 0x0004DC5C File Offset: 0x0004BE5C
		private void method_19()
		{
			try
			{
				DataMgmtForm.Class197 @class = new DataMgmtForm.Class197();
				@class.tradingSymbol_0 = (TradingSymbol)this.comboBox_AcctSymbToOpt.SelectedItem;
				this.label1_AcctSymbTransTotal.Text = "记录数:" + this.list_1.Where(new Func<Transaction, bool>(@class.method_0)).Count<Transaction>();
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x06000D1B RID: 3355 RVA: 0x0004DCD8 File Offset: 0x0004BED8
		private void checkBox_IfAllAcctSymbForOpt_CheckedChanged(object sender, EventArgs e)
		{
			if (this.checkBox_IfAllAcctSymbForOpt.Checked)
			{
				this.comboBox_AcctSymbToOpt.Enabled = false;
				this.label1_AcctSymbTransTotal.Text = this.label_AccTransTotal.Text;
			}
			else
			{
				this.comboBox_AcctSymbToOpt.Enabled = true;
				this.method_19();
			}
		}

		// Token: 0x06000D1C RID: 3356 RVA: 0x00005D7A File Offset: 0x00003F7A
		private void btn_CopyTrans_Click(object sender, EventArgs e)
		{
			this.method_20("复制指定交易记录到目标账户吗？", false);
		}

		// Token: 0x06000D1D RID: 3357 RVA: 0x00005D8A File Offset: 0x00003F8A
		private void btn_MoveTrans_Click(object sender, EventArgs e)
		{
			this.method_20("移动指定交易记录到目标账户吗？", true);
		}

		// Token: 0x06000D1E RID: 3358 RVA: 0x0004DD2C File Offset: 0x0004BF2C
		private void method_20(string string_5, bool bool_0)
		{
			DataMgmtForm.Class198 @class = new DataMgmtForm.Class198();
			Account account = (Account)this.comboBox_AcctToOpt.SelectedItem;
			Account account2 = (Account)this.comboBox_AcctToReceiveTrans.SelectedItem;
			bool @checked = this.checkBox_IfAllAcctSymbForOpt.Checked;
			List<Transaction> list = Base.Trading.smethod_124(account.ID);
			List<Transaction> list2 = new List<Transaction>();
			@class.tradingSymbol_0 = null;
			if (@checked)
			{
				list2 = list;
			}
			else
			{
				@class.tradingSymbol_0 = (TradingSymbol)this.comboBox_AcctSymbToOpt.SelectedItem;
				list2 = list.Where(new Func<Transaction, bool>(@class.method_0)).ToList<Transaction>();
			}
			if (list2.Count > 0 && MessageBox.Show(string_5, "请确认", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) == DialogResult.OK)
			{
				if (list2.Where(new Func<Transaction, bool>(DataMgmtForm.<>c.<>9.method_3)).Any<Transaction>())
				{
					MessageBox.Show("所选交易记录包含未平仓交易，请平仓后再操作。", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
				else
				{
					List<Transaction> list3 = Base.Trading.smethod_124(account2.ID);
					for (int i = 0; i < list2.Count; i++)
					{
						DataMgmtForm.Class199 class2 = new DataMgmtForm.Class199();
						class2.transaction_0 = list2[i];
						class2.transaction_0.AcctID = account2.ID;
						if (!list3.Where(new Func<Transaction, bool>(class2.method_0)).Any<Transaction>())
						{
							list3.Add(class2.transaction_0);
						}
					}
					Base.Trading.smethod_128(account2.ID, list3);
					List<Order> list4 = Base.Trading.smethod_24(account2.ID);
					List<Order> list5 = Base.Trading.smethod_24(account.ID);
					List<Order> list6 = new List<Order>();
					if (@checked)
					{
						list6 = list5;
					}
					else
					{
						list6 = list5.Where(new Func<Order, bool>(@class.method_1)).ToList<Order>();
					}
					for (int j = 0; j < list6.Count; j++)
					{
						DataMgmtForm.Class200 class3 = new DataMgmtForm.Class200();
						class3.order_0 = list6[j];
						class3.order_0.AcctID = account2.ID;
						if (!list4.Where(new Func<Order, bool>(class3.method_0)).Any<Order>())
						{
							list4.Add(class3.order_0);
						}
					}
					Base.Trading.smethod_26(account2.ID, list4);
					if (Base.Acct.CurrAccount.ID == account2.ID)
					{
						Base.Trading.smethod_17();
						Base.UI.smethod_155();
					}
					if (bool_0)
					{
						if (@checked)
						{
							list = new List<Transaction>();
						}
						else
						{
							list.RemoveAll(new Predicate<Transaction>(@class.method_2));
						}
						Base.Trading.smethod_128(account.ID, list);
						list5 = Base.Trading.smethod_24(account.ID);
						if (list5.Count > 0)
						{
							if (@checked)
							{
								list5 = new List<Order>();
							}
							else
							{
								list5.RemoveAll(new Predicate<Order>(@class.method_3));
							}
						}
						Base.Trading.smethod_26(account.ID, list5);
						List<CondOrder> list7 = Base.Trading.smethod_78(account.ID);
						if (list7.Any<CondOrder>())
						{
							list7.RemoveAll(new Predicate<CondOrder>(@class.method_4));
						}
						Base.Trading.smethod_80();
						if (Base.Acct.CurrAccount.ID == account.ID)
						{
							Base.Trading.smethod_17();
							Base.UI.smethod_155();
						}
					}
					this.method_18();
				}
			}
		}

		// Token: 0x06000D1F RID: 3359 RVA: 0x0004E048 File Offset: 0x0004C248
		private void btn_DelAcctTrans_Click(object sender, EventArgs e)
		{
			DataMgmtForm.Class201 @class = new DataMgmtForm.Class201();
			Account account = (Account)this.comboBox_AcctToOpt.SelectedItem;
			List<Transaction> list = Base.Trading.smethod_124(account.ID);
			bool @checked = this.checkBox_IfAllAcctSymbForOpt.Checked;
			@class.tradingSymbol_0 = null;
			List<Transaction> list2 = list;
			if (!@checked)
			{
				@class.tradingSymbol_0 = (TradingSymbol)this.comboBox_AcctSymbToOpt.SelectedItem;
				list2 = list.Where(new Func<Transaction, bool>(@class.method_0)).ToList<Transaction>();
			}
			if (list2.Count > 0 && MessageBox.Show("删除所选择的交易记录吗？请注意相关所有持仓、委托单、止损单记录等也将被一并删除。", "请确认", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) == DialogResult.OK)
			{
				if (@checked)
				{
					list = new List<Transaction>();
				}
				else
				{
					list.RemoveAll(new Predicate<Transaction>(@class.method_1));
				}
				Base.Trading.smethod_128(account.ID, list);
				this.method_18();
				if (Base.Acct.CurrAccount.ID == account.ID)
				{
					Base.Trading.smethod_17();
					Base.UI.smethod_155();
					if (Base.UI.ChtCtrl_KLineList != null)
					{
						foreach (ChtCtrl_KLine chtCtrl_KLine in Base.UI.ChtCtrl_KLineList)
						{
							chtCtrl_KLine.Chart_CS.method_200(true);
							chtCtrl_KLine.Chart_CS.method_207();
						}
					}
				}
			}
		}

		// Token: 0x06000D20 RID: 3360 RVA: 0x0004E190 File Offset: 0x0004C390
		private void btn_TransExp_Click(object sender, EventArgs e)
		{
			List<Transaction> list = Base.Trading.smethod_124(((Account)this.comboBox_AcctToOpt.SelectedItem).ID);
			if (!this.checkBox_IfAllAcctSymbForOpt.Checked)
			{
				DataMgmtForm.Class202 @class = new DataMgmtForm.Class202();
				@class.tradingSymbol_0 = (TradingSymbol)this.comboBox_AcctSymbToOpt.SelectedItem;
				list = list.Where(new Func<Transaction, bool>(@class.method_0)).ToList<Transaction>();
			}
			string text = string.Empty;
			if (list.Count > 0)
			{
				try
				{
					string text2 = this.textBox_TransExpFilePath.Text;
					if (string.IsNullOrEmpty(text2))
					{
						return;
					}
					if (!text2.ToLower().EndsWith(".csv"))
					{
						text2 += ".csv";
					}
					FileInfo fileInfo;
					try
					{
						fileInfo = new FileInfo(text2);
					}
					catch
					{
						MessageBox.Show("文件路径非法，请指定有效的导出文件路径！", "请确认", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
						return;
					}
					text = fileInfo.FullName;
					if (!fileInfo.Directory.Exists)
					{
						if (MessageBox.Show("指定文件目录不存在，创建该目录吗？ ", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
						{
							return;
						}
						fileInfo.Directory.Create();
					}
					if (fileInfo.Exists)
					{
						if (MessageBox.Show("文件已存在，覆盖该文件吗？ ", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
						{
							return;
						}
						if (DataMgmtForm.smethod_0(text))
						{
							MessageBox.Show("该文件正被其他程序占用，无法覆盖！ ", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
							return;
						}
					}
				}
				catch (Exception ex)
				{
					this.method_4("未能成功导出交易记录。", "错误原因：" + ex.Message);
					return;
				}
				using (StreamWriter streamWriter = new StreamWriter(text, false, Encoding.GetEncoding("GB2312")))
				{
					streamWriter.WriteLine("代码,买卖,开平,成交时间,价格,手数,盈利,手续费");
					foreach (Transaction transaction in list)
					{
						string text3 = (transaction.TransType == 1 || transaction.TransType == 4) ? "买" : "卖";
						string text4 = (transaction.TransType == 1 || transaction.TransType == 3) ? "开" : "平";
						string text5 = "N/A";
						StkSymbol stkSymbol = SymbMgr.smethod_4(transaction.SymbolID, false);
						if (stkSymbol != null)
						{
							text5 = stkSymbol.Code;
						}
						string value = string.Concat(new object[]
						{
							text5,
							",",
							text3,
							",",
							text4,
							",",
							transaction.CreateTime,
							",",
							Utility.GetStringWithoutEndZero(new decimal?(transaction.Price)),
							",",
							transaction.Units,
							",",
							Utility.GetStringWithoutEndZero(transaction.Profit),
							",",
							Utility.GetStringWithoutEndZero(transaction.Fee)
						});
						streamWriter.WriteLine(value);
					}
				}
				MessageBox.Show("导出交易记录成功！共导出" + list.Count + "条记录。", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				Base.UI.Form.LastAcctTransExportDir = Path.GetDirectoryName(this.textBox_TransExpFilePath.Text);
			}
		}

		// Token: 0x06000D21 RID: 3361 RVA: 0x0004E530 File Offset: 0x0004C730
		public static bool smethod_0(string string_5)
		{
			bool result = true;
			FileStream fileStream = null;
			try
			{
				fileStream = new FileStream(string_5, FileMode.Open, FileAccess.Read, FileShare.None);
				result = false;
			}
			catch (Exception ex)
			{
				result = !(ex is FileNotFoundException);
			}
			finally
			{
				if (fileStream != null)
				{
					fileStream.Close();
				}
			}
			return result;
		}

		// Token: 0x06000D22 RID: 3362 RVA: 0x0004E58C File Offset: 0x0004C78C
		private void comboBox_HDExpDataFormat_SelectedIndexChanged(object sender, EventArgs e)
		{
			if (!string.IsNullOrEmpty(this.textBox_HDExpFilePath.Text))
			{
				string text = ".csv";
				string text2 = ".txt";
				if (this.comboBox_HDExpDataFormat.SelectedIndex == 0)
				{
					if (this.textBox_HDExpFilePath.Text.EndsWith(text2))
					{
						this.textBox_HDExpFilePath.Text = this.textBox_HDExpFilePath.Text.Replace(text2, text);
					}
					this.checkBox_IfCombinedDT.Enabled = true;
				}
				else
				{
					if (this.textBox_HDExpFilePath.Text.EndsWith(text))
					{
						this.textBox_HDExpFilePath.Text = this.textBox_HDExpFilePath.Text.Replace(text, text2);
					}
					this.checkBox_IfCombinedDT.Checked = false;
					this.checkBox_IfCombinedDT.Enabled = false;
				}
			}
		}

		// Token: 0x06000D23 RID: 3363 RVA: 0x00005D9A File Offset: 0x00003F9A
		private void dateTimeInput_HDExpEnd_ValueChanged(object sender, EventArgs e)
		{
			this.method_21();
		}

		// Token: 0x06000D24 RID: 3364 RVA: 0x00005D9A File Offset: 0x00003F9A
		private void dateTimeInput_HDExpEnd_Leave(object sender, EventArgs e)
		{
			this.method_21();
		}

		// Token: 0x06000D25 RID: 3365 RVA: 0x0004E650 File Offset: 0x0004C850
		private void method_21()
		{
			if (this.dateTimeInput_HDExpEnd.Value.Date < this.dateTimeInput_HDExpStart.Value.Date)
			{
				this.dateTimeInput_HDExpEnd.Value = this.dateTimeInput_HDExpStart.Value;
			}
		}

		// Token: 0x06000D26 RID: 3366 RVA: 0x0004E6A4 File Offset: 0x0004C8A4
		private void btn_TransExpOpenDFolder_Click(object sender, EventArgs e)
		{
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.Title = "保存文件至...";
			saveFileDialog.Filter = "CSV文件(*.csv)|*.*";
			if (!string.IsNullOrEmpty(this.textBox_TransExpFilePath.Text))
			{
				try
				{
					FileInfo fileInfo = new FileInfo(this.textBox_TransExpFilePath.Text);
					saveFileDialog.InitialDirectory = fileInfo.DirectoryName;
					saveFileDialog.FileName = fileInfo.Name;
				}
				catch
				{
					saveFileDialog.InitialDirectory = this.string_0;
				}
			}
			if (saveFileDialog.ShowDialog() == DialogResult.OK)
			{
				this.textBox_TransExpFilePath.Text = saveFileDialog.FileName;
				Base.UI.Form.LastAcctTransExportDir = Path.GetDirectoryName(this.textBox_TransExpFilePath.Text);
			}
		}

		// Token: 0x06000D27 RID: 3367 RVA: 0x0004E760 File Offset: 0x0004C960
		private void btn_HDExpOpenDFolder_Click(object sender, EventArgs e)
		{
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.Title = "保存文件至...";
			saveFileDialog.Filter = ((this.comboBox_HDExpDataFormat.SelectedIndex == 0) ? "CSV文件(*.csv)|*.*" : "TXT文件(*.txt)|*.*");
			if (!string.IsNullOrEmpty(this.textBox_HDExpFilePath.Text))
			{
				try
				{
					FileInfo fileInfo = new FileInfo(this.textBox_HDExpFilePath.Text);
					saveFileDialog.InitialDirectory = fileInfo.DirectoryName;
					saveFileDialog.FileName = fileInfo.Name;
				}
				catch
				{
					saveFileDialog.InitialDirectory = this.string_0;
				}
			}
			if (saveFileDialog.ShowDialog() == DialogResult.OK)
			{
				this.textBox_HDExpFilePath.Text = saveFileDialog.FileName;
				Base.UI.Form.LastHisDataExportDir = Path.GetDirectoryName(this.textBox_HDExpFilePath.Text);
			}
		}

		// Token: 0x06000D28 RID: 3368 RVA: 0x0004E830 File Offset: 0x0004CA30
		private void button_LoadTrans_Click(object sender, EventArgs e)
		{
			string text = this.textBox_TransImpFilePath.Text;
			if (!string.IsNullOrEmpty(text) && File.Exists(text))
			{
				Base.UI.Form.LastTransImportFilePath = this.textBox_TransImpFilePath.Text;
				new ImportTransForm(text)
				{
					StartPosition = FormStartPosition.CenterScreen
				}.ShowDialog();
			}
			else
			{
				MessageBox.Show("请指定有效的导入文件路径！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				this.method_22();
			}
		}

		// Token: 0x06000D29 RID: 3369 RVA: 0x00005DA4 File Offset: 0x00003FA4
		private void btn_TransImpOpenDFolder_Click(object sender, EventArgs e)
		{
			this.method_22();
		}

		// Token: 0x06000D2A RID: 3370 RVA: 0x0004E8A0 File Offset: 0x0004CAA0
		private void method_22()
		{
			OpenFileDialog openFileDialog = new OpenFileDialog();
			openFileDialog.Filter = "文件(*.txt,*.csv,*.xls,*.xlsx)|*.txt;*.csv;*.xls;*.xlsx";
			if (openFileDialog.ShowDialog() == DialogResult.OK)
			{
				this.textBox_TransImpFilePath.Text = openFileDialog.FileName;
			}
		}

		// Token: 0x06000D2B RID: 3371 RVA: 0x00005DAE File Offset: 0x00003FAE
		private void TimePicker_AutoDown_ValueChanged(object sender, EventArgs e)
		{
			Base.UI.Form.CfmmcAutoDnldConfig.BeginTime = this.TimePicker_AutoDown.Value;
		}

		// Token: 0x06000D2C RID: 3372 RVA: 0x00005CC8 File Offset: 0x00003EC8
		private void comboBox_CfmmcAcct_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.method_3();
		}

		// Token: 0x06000D2D RID: 3373 RVA: 0x0004E8DC File Offset: 0x0004CADC
		public void btn_CfmmcDownBg_Click(object sender, EventArgs e)
		{
			if (CfmmcRecImporter.IEMajor <= 6)
			{
				this.method_33("IE浏览器版本过低，无法下载数据，请升级IE。", 0);
			}
			else if (CfmmcRecImporter.IsDownloading)
			{
				this.method_33("自动下载正在运行中。", 0);
			}
			else
			{
				CfmmcAcct cfmmcAcct = this.method_31();
				if (cfmmcAcct != null && CfmmcRecImporter.smethod_31(cfmmcAcct))
				{
					this.method_34(false);
				}
			}
		}

		// Token: 0x06000D2E RID: 3374 RVA: 0x00005DCC File Offset: 0x00003FCC
		private void method_23(object sender, EventArgs e)
		{
			this.label_CfmmcDnStatus.Text = "正在登陆...";
			this.toolTip_0 = null;
		}

		// Token: 0x06000D2F RID: 3375 RVA: 0x00005DE7 File Offset: 0x00003FE7
		private void method_24(object sender, EventArgs e)
		{
			this.label_CfmmcDnStatus.Text = "登陆成功！开始下载记录...";
			this.toolTip_0 = null;
		}

		// Token: 0x06000D30 RID: 3376 RVA: 0x0004E930 File Offset: 0x0004CB30
		private void method_25(object sender, EventArgs25 e)
		{
			this.label_CfmmcDnStatus.Text = "准备下载记录...";
			this.toolTip_0 = null;
			if (!this.progressBar_Cfmmc.Visible)
			{
				this.progressBar_Cfmmc.Visible = true;
			}
			this.progressBar_Cfmmc.Value = 0;
			this.progressBar_Cfmmc.Maximum = 0;
			int totalRecs = e.TotalRecs;
			if (totalRecs > 0)
			{
				this.progressBar_Cfmmc.Maximum += totalRecs;
			}
		}

		// Token: 0x06000D31 RID: 3377 RVA: 0x0004E9A8 File Offset: 0x0004CBA8
		private void method_26(object sender, EventArgs25 e)
		{
			int totalRecs = e.TotalRecs;
			if (totalRecs > 0)
			{
				this.progressBar_Cfmmc.Value = totalRecs + 1;
			}
			this.label_CfmmcDnStatus.Text = "正在下载记录...";
			this.toolTip_0 = null;
		}

		// Token: 0x06000D32 RID: 3378 RVA: 0x0004E9E8 File Offset: 0x0004CBE8
		private void method_27(object sender, EventArgs25 e)
		{
			this.label_CfmmcDnStatus.Text = string.Empty;
			this.toolTip_0 = null;
			MessageBox.Show(this, e.Msg, "确认", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
			this.method_34(true);
			this.progressBar_Cfmmc.Visible = false;
			this.method_3();
			this.method_18();
			if ((Base.UI.Form.IfShowNoTransArrow || !Base.UI.Form.IfShowAllTransArrow) && MessageBox.Show("显示所有交易的开平仓标示箭头吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
			{
				Base.UI.Form.IfShowNoTransArrow = false;
				Base.UI.Form.IfShowAllTransArrow = true;
				Base.UI.smethod_36();
			}
		}

		// Token: 0x06000D33 RID: 3379 RVA: 0x0004EA8C File Offset: 0x0004CC8C
		private void method_28(object sender, EventArgs25 e)
		{
			string string_ = "下载错误：" + e.Msg;
			this.method_29(string_, 18);
			this.method_34(true);
			this.progressBar_Cfmmc.Visible = false;
		}

		// Token: 0x06000D34 RID: 3380 RVA: 0x0004EACC File Offset: 0x0004CCCC
		private void method_29(string string_5, int int_0)
		{
			if (string_5.Length > int_0)
			{
				this.label_CfmmcDnStatus.Text = string_5.Substring(0, int_0) + "...";
				this.toolTip_0 = new System.Windows.Forms.ToolTip();
				this.toolTip_0.SetToolTip(this.label_CfmmcDnStatus, string_5);
			}
			else
			{
				this.label_CfmmcDnStatus.Text = string_5;
				this.toolTip_0 = null;
			}
		}

		// Token: 0x06000D35 RID: 3381 RVA: 0x00005E02 File Offset: 0x00004002
		private void method_30(object sender, EventArgs e)
		{
			this.label_CfmmcDnStatus.Text = string.Empty;
			this.toolTip_0 = null;
			MessageBox.Show(this, "没有需要下载的新交易数据。", "确认", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
		}

		// Token: 0x06000D36 RID: 3382 RVA: 0x0004EB34 File Offset: 0x0004CD34
		private CfmmcAcct method_31()
		{
			if (this.comboBox_CfmmcAcct.Items.Count > 0)
			{
				object selectedItem = this.comboBox_CfmmcAcct.SelectedItem;
				CfmmcAcct cfmmcAcct = Class463.smethod_11(this.comboBox_CfmmcAcct.Text);
				if (cfmmcAcct != null)
				{
					return cfmmcAcct;
				}
			}
			else
			{
				MessageBox.Show(this, "未创建有效的期货监控中心账号！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
			return null;
		}

		// Token: 0x06000D37 RID: 3383 RVA: 0x0004EB94 File Offset: 0x0004CD94
		public void method_32()
		{
			if (Base.UI.Form.CfmmcAutoDnldConfig == null)
			{
				Base.UI.Form.CfmmcAutoDnldConfig = new CfmmcAutoDnldConfig();
				Base.UI.Form.CfmmcAutoDnldConfig.AutoDownOnStartup = false;
				Base.UI.Form.CfmmcAutoDnldConfig.AutoDownPeriodly = true;
				Base.UI.Form.CfmmcAutoDnldConfig.Frequency = AutoDownCfmmcFrequencyEnum.每周;
				Base.UI.Form.CfmmcAutoDnldConfig.BeginTime = new DateTime(DateTime.Now.Year, 1, 1, 17, 0, 0);
			}
			CfmmcAutoDnldConfig cfmmcAutoDnldConfig = Base.UI.Form.CfmmcAutoDnldConfig;
			if (cfmmcAutoDnldConfig.AutoDownOnStartup)
			{
				this.radioBtn_StartUpDnldCfmmc.Checked = true;
			}
			this.radioBtn_PeriodlyDnldCfmmc.CheckedChanged += this.radioBtn_PeriodlyDnldCfmmc_CheckedChanged;
			if (cfmmcAutoDnldConfig.AutoDownPeriodly)
			{
				this.radioBtn_PeriodlyDnldCfmmc.Checked = true;
				this.groupBox_CfmmcDnldFrequency.Enabled = true;
				this.groupBox_CfmmcDnldTime.Enabled = true;
			}
			else
			{
				this.radioBtn_PeriodlyDnldCfmmc.Checked = false;
				this.groupBox_CfmmcDnldFrequency.Enabled = false;
				this.groupBox_CfmmcDnldTime.Enabled = false;
			}
			if (cfmmcAutoDnldConfig.Frequency == AutoDownCfmmcFrequencyEnum.每天)
			{
				this.radio_autoDay.Checked = true;
			}
			else if (cfmmcAutoDnldConfig.Frequency == AutoDownCfmmcFrequencyEnum.每周)
			{
				this.radio_autoWeek.Checked = true;
			}
			int num = (int)(cfmmcAutoDnldConfig.WklyDnldDayOfWeek + 1);
			if (num > 6)
			{
				num = 0;
			}
			this.comboBox_DayOfWeekDnCfmmc.SelectedIndex = num;
			if (this.TimePicker_AutoDown.MinDate <= cfmmcAutoDnldConfig.BeginTime && this.TimePicker_AutoDown.MaxDate >= cfmmcAutoDnldConfig.BeginTime)
			{
				this.TimePicker_AutoDown.Value = cfmmcAutoDnldConfig.BeginTime;
			}
			this.progressBar_Cfmmc.Visible = false;
		}

		// Token: 0x06000D38 RID: 3384 RVA: 0x0004ED30 File Offset: 0x0004CF30
		private void radioBtn_PeriodlyDnldCfmmc_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioBtn_PeriodlyDnldCfmmc.Checked)
			{
				this.groupBox_CfmmcDnldFrequency.Enabled = true;
				this.groupBox_CfmmcDnldTime.Enabled = true;
			}
			else
			{
				this.groupBox_CfmmcDnldFrequency.Enabled = false;
				this.groupBox_CfmmcDnldTime.Enabled = false;
			}
		}

		// Token: 0x06000D39 RID: 3385 RVA: 0x00005E31 File Offset: 0x00004031
		public void method_33(string string_5, int int_0)
		{
			if (!(string_5 == ""))
			{
				if (int_0 == 0)
				{
					this.method_29(string_5, 18);
				}
			}
		}

		// Token: 0x06000D3A RID: 3386 RVA: 0x00005E50 File Offset: 0x00004050
		private void method_34(bool bool_0)
		{
			this.btn_AddCfmmcAcct.Enabled = bool_0;
			this.btn_EditCfmmcAcct.Enabled = bool_0;
			this.btn_DelCfmmcAcct.Enabled = bool_0;
			this.btn_CfmmcDownBg.Enabled = bool_0;
			this.comboBox_CfmmcAcct.Enabled = bool_0;
		}

		// Token: 0x06000D3B RID: 3387 RVA: 0x00005E90 File Offset: 0x00004090
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000655 RID: 1621
		private List<TradingSymbol> list_0;

		// Token: 0x04000656 RID: 1622
		private List<Transaction> list_1;

		// Token: 0x04000657 RID: 1623
		private string string_0;

		// Token: 0x04000658 RID: 1624
		private string string_1;

		// Token: 0x04000659 RID: 1625
		private const string string_2 = "TExHisData.csv";

		// Token: 0x0400065A RID: 1626
		private const string string_3 = "TExTrans.csv";

		// Token: 0x0400065B RID: 1627
		private string string_4;

		// Token: 0x0400065C RID: 1628
		private BackgroundWorker backgroundWorker_0;

		// Token: 0x0400065D RID: 1629
		private List<string> list_2;

		// Token: 0x0400065E RID: 1630
		private List<string> list_3;

		// Token: 0x0400065F RID: 1631
		private System.Windows.Forms.ToolTip toolTip_0;

		// Token: 0x04000660 RID: 1632
		private IContainer icontainer_0;

		// Token: 0x02000157 RID: 343
		[CompilerGenerated]
		private sealed class Class194
		{
			// Token: 0x06000D3E RID: 3390 RVA: 0x00052118 File Offset: 0x00050318
			internal bool method_0(ExchgHouse exchgHouse_1)
			{
				return exchgHouse_1.ID == this.stkSymbol_0.ExchangeID;
			}

			// Token: 0x06000D3F RID: 3391 RVA: 0x0005213C File Offset: 0x0005033C
			internal bool method_1(StkSymbol stkSymbol_1)
			{
				return stkSymbol_1.ExchangeID == this.exchgHouse_0.ID;
			}

			// Token: 0x040006AC RID: 1708
			public StkSymbol stkSymbol_0;

			// Token: 0x040006AD RID: 1709
			public ExchgHouse exchgHouse_0;
		}

		// Token: 0x02000159 RID: 345
		[CompilerGenerated]
		private sealed class Class195
		{
			// Token: 0x06000D47 RID: 3399 RVA: 0x000521C0 File Offset: 0x000503C0
			internal bool method_0(StkSymbol stkSymbol_0)
			{
				return stkSymbol_0.ExchangeID == this.exchgHouse_0.ID;
			}

			// Token: 0x040006B3 RID: 1715
			public ExchgHouse exchgHouse_0;
		}

		// Token: 0x0200015A RID: 346
		[CompilerGenerated]
		private sealed class Class196
		{
			// Token: 0x06000D49 RID: 3401 RVA: 0x000521E4 File Offset: 0x000503E4
			internal bool method_0(Account account_1)
			{
				return account_1.ID != this.account_0.ID;
			}

			// Token: 0x040006B4 RID: 1716
			public Account account_0;
		}

		// Token: 0x0200015B RID: 347
		[CompilerGenerated]
		private sealed class Class197
		{
			// Token: 0x06000D4B RID: 3403 RVA: 0x0005220C File Offset: 0x0005040C
			internal bool method_0(Transaction transaction_0)
			{
				return transaction_0.SymbolID == this.tradingSymbol_0.ID;
			}

			// Token: 0x040006B5 RID: 1717
			public TradingSymbol tradingSymbol_0;
		}

		// Token: 0x0200015C RID: 348
		[CompilerGenerated]
		private sealed class Class198
		{
			// Token: 0x06000D4D RID: 3405 RVA: 0x00052230 File Offset: 0x00050430
			internal bool method_0(Transaction transaction_0)
			{
				return transaction_0.SymbolID == this.tradingSymbol_0.ID;
			}

			// Token: 0x06000D4E RID: 3406 RVA: 0x00052254 File Offset: 0x00050454
			internal bool method_1(Order order_0)
			{
				return order_0.SymbolID == this.tradingSymbol_0.ID;
			}

			// Token: 0x06000D4F RID: 3407 RVA: 0x00052230 File Offset: 0x00050430
			internal bool method_2(Transaction transaction_0)
			{
				return transaction_0.SymbolID == this.tradingSymbol_0.ID;
			}

			// Token: 0x06000D50 RID: 3408 RVA: 0x00052254 File Offset: 0x00050454
			internal bool method_3(Order order_0)
			{
				return order_0.SymbolID == this.tradingSymbol_0.ID;
			}

			// Token: 0x06000D51 RID: 3409 RVA: 0x00052278 File Offset: 0x00050478
			internal bool method_4(CondOrder condOrder_0)
			{
				return condOrder_0.SymbID == this.tradingSymbol_0.ID;
			}

			// Token: 0x040006B6 RID: 1718
			public TradingSymbol tradingSymbol_0;
		}

		// Token: 0x0200015D RID: 349
		[CompilerGenerated]
		private sealed class Class199
		{
			// Token: 0x06000D53 RID: 3411 RVA: 0x0005229C File Offset: 0x0005049C
			internal bool method_0(Transaction transaction_1)
			{
				if (transaction_1.ID == this.transaction_0.ID && transaction_1.CreateTime == this.transaction_0.CreateTime)
				{
					decimal? num = transaction_1.Fee;
					decimal? num2 = this.transaction_0.Fee;
					if ((num.GetValueOrDefault() == num2.GetValueOrDefault() & num != null == (num2 != null)) && transaction_1.Units == this.transaction_0.Units && transaction_1.TransType == this.transaction_0.TransType && transaction_1.Price == this.transaction_0.Price)
					{
						num2 = transaction_1.Profit;
						num = this.transaction_0.Profit;
						return num2.GetValueOrDefault() == num.GetValueOrDefault() & num2 != null == (num != null);
					}
				}
				return false;
			}

			// Token: 0x040006B7 RID: 1719
			public Transaction transaction_0;
		}

		// Token: 0x0200015E RID: 350
		[CompilerGenerated]
		private sealed class Class200
		{
			// Token: 0x06000D55 RID: 3413 RVA: 0x00052390 File Offset: 0x00050590
			internal bool method_0(Order order_1)
			{
				bool result;
				if (order_1.SymbolID == this.order_0.SymbolID && order_1.CreateTime == this.order_0.CreateTime && order_1.OrderType == this.order_0.OrderType && order_1.Price == this.order_0.Price && order_1.OrderStatus == order_1.OrderStatus)
				{
					result = (order_1.Units == this.order_0.Units);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x040006B8 RID: 1720
			public Order order_0;
		}

		// Token: 0x0200015F RID: 351
		[CompilerGenerated]
		private sealed class Class201
		{
			// Token: 0x06000D57 RID: 3415 RVA: 0x0005241C File Offset: 0x0005061C
			internal bool method_0(Transaction transaction_0)
			{
				return transaction_0.SymbolID == this.tradingSymbol_0.ID;
			}

			// Token: 0x06000D58 RID: 3416 RVA: 0x0005241C File Offset: 0x0005061C
			internal bool method_1(Transaction transaction_0)
			{
				return transaction_0.SymbolID == this.tradingSymbol_0.ID;
			}

			// Token: 0x040006B9 RID: 1721
			public TradingSymbol tradingSymbol_0;
		}

		// Token: 0x02000160 RID: 352
		[CompilerGenerated]
		private sealed class Class202
		{
			// Token: 0x06000D5A RID: 3418 RVA: 0x00052440 File Offset: 0x00050640
			internal bool method_0(Transaction transaction_0)
			{
				return transaction_0.SymbolID == this.tradingSymbol_0.ID;
			}

			// Token: 0x040006BA RID: 1722
			public TradingSymbol tradingSymbol_0;
		}
	}
}
