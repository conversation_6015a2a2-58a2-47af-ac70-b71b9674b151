﻿using System;

namespace ns23
{
	// Token: 0x0200007F RID: 127
	internal enum Enum3
	{
		// Token: 0x0400016E RID: 366
		const_0 = 200,
		// Token: 0x0400016F RID: 367
		const_1,
		// Token: 0x04000170 RID: 368
		const_2,
		// Token: 0x04000171 RID: 369
		const_3,
		// Token: 0x04000172 RID: 370
		const_4,
		// Token: 0x04000173 RID: 371
		const_5,
		// Token: 0x04000174 RID: 372
		const_6,
		// Token: 0x04000175 RID: 373
		const_7,
		// Token: 0x04000176 RID: 374
		const_8,
		// Token: 0x04000177 RID: 375
		const_9,
		// Token: 0x04000178 RID: 376
		const_10 = 300,
		// Token: 0x04000179 RID: 377
		const_11,
		// Token: 0x0400017A RID: 378
		const_12,
		// Token: 0x0400017B RID: 379
		const_13,
		// Token: 0x0400017C RID: 380
		const_14 = 306,
		// Token: 0x0400017D RID: 381
		const_15,
		// Token: 0x0400017E RID: 382
		const_16,
		// Token: 0x0400017F RID: 383
		const_17,
		// Token: 0x04000180 RID: 384
		const_18,
		// Token: 0x04000181 RID: 385
		const_19,
		// Token: 0x04000182 RID: 386
		const_20,
		// Token: 0x04000183 RID: 387
		const_21,
		// Token: 0x04000184 RID: 388
		const_22 = 330,
		// Token: 0x04000185 RID: 389
		const_23 = 340,
		// Token: 0x04000186 RID: 390
		const_24 = 350,
		// Token: 0x04000187 RID: 391
		const_25 = 250,
		// Token: 0x04000188 RID: 392
		const_26,
		// Token: 0x04000189 RID: 393
		const_27,
		// Token: 0x0400018A RID: 394
		const_28,
		// Token: 0x0400018B RID: 395
		const_29,
		// Token: 0x0400018C RID: 396
		const_30,
		// Token: 0x0400018D RID: 397
		const_31,
		// Token: 0x0400018E RID: 398
		const_32,
		// Token: 0x0400018F RID: 399
		const_33,
		// Token: 0x04000190 RID: 400
		const_34,
		// Token: 0x04000191 RID: 401
		const_35,
		// Token: 0x04000192 RID: 402
		const_36,
		// Token: 0x04000193 RID: 403
		const_37 = 270,
		// Token: 0x04000194 RID: 404
		const_38,
		// Token: 0x04000195 RID: 405
		const_39
	}
}
