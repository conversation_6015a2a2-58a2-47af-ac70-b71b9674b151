﻿using System;
using System.Collections;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Runtime.Remoting;
using System.Runtime.Remoting.Channels;
using System.Runtime.Remoting.Channels.Http;
using ns28;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x0200009D RID: 157
	internal static class ConnMgr
	{
		// Token: 0x0600053F RID: 1343 RVA: 0x00028B74 File Offset: 0x00026D74
		public static ITExSrv smethod_0()
		{
			if (ChannelServices.RegisteredChannels.Count<IChannel>() != 0)
			{
				if (ChannelServices.RegisteredChannels.Count(new Func<IChannel, bool>(ConnMgr.<>c.<>9.method_0)) >= 1)
				{
					return null;
				}
			}
			return ConnMgr.smethod_1(TApp.HOST);
		}

		// Token: 0x06000540 RID: 1344 RVA: 0x00028BCC File Offset: 0x00026DCC
		public static ITExSrv smethod_1(string string_1)
		{
			ITExSrv result;
			try
			{
				IServerChannelSinkProvider serverSinkProvider = new BinaryServerFormatterSinkProvider();
				IClientChannelSinkProvider clientSinkProvider = new BinaryClientFormatterSinkProvider();
				Hashtable hashtable = new Hashtable();
				((IDictionary)hashtable)["name"] = "TExSrv";
				((IDictionary)hashtable)["secure"] = true;
				((IDictionary)hashtable)["clientConnectionLimit"] = 10;
				HttpChannel httpChannel = new HttpChannel(hashtable, clientSinkProvider, serverSinkProvider);
				if (ConnMgr.smethod_6())
				{
					IWebProxy systemWebProxy = WebRequest.GetSystemWebProxy();
					ConnMgr.smethod_2(httpChannel, systemWebProxy);
				}
				ChannelServices.RegisterChannel(httpChannel, true);
				try
				{
					if (RemotingConfiguration.CustomErrorsMode != CustomErrorsModes.On)
					{
						RemotingConfiguration.CustomErrorsMode = CustomErrorsModes.On;
					}
				}
				catch
				{
				}
				result = (ITExSrv)Activator.GetObject(typeof(ITExSrv), "http://" + string_1 + "/TExSrv/TExSrv.rem");
				goto IL_B1;
			}
			catch (Exception ex)
			{
				if (!(ex is RemotingException))
				{
					throw;
				}
			}
			return null;
			IL_B1:
			return result;
		}

		// Token: 0x06000541 RID: 1345 RVA: 0x00028CB0 File Offset: 0x00026EB0
		public static void smethod_2(HttpChannel httpChannel_0, IWebProxy iwebProxy_0)
		{
			if (iwebProxy_0 != null)
			{
				HttpClientChannel obj = (HttpClientChannel)typeof(HttpChannel).GetField("_clientChannel", BindingFlags.Instance | BindingFlags.NonPublic).GetValue(httpChannel_0);
				typeof(HttpClientChannel).GetField("_proxyObject", BindingFlags.Instance | BindingFlags.NonPublic).SetValue(obj, iwebProxy_0);
				WebRequest.DefaultWebProxy = iwebProxy_0;
			}
		}

		// Token: 0x06000542 RID: 1346 RVA: 0x00004525 File Offset: 0x00002725
		public static void smethod_3()
		{
			ConnMgr.smethod_4("TExSrv");
		}

		// Token: 0x06000543 RID: 1347 RVA: 0x00028D0C File Offset: 0x00026F0C
		public static void smethod_4(string string_1)
		{
			IChannel[] registeredChannels = ChannelServices.RegisteredChannels;
			if (registeredChannels != null)
			{
				foreach (IChannel channel in registeredChannels)
				{
					if (channel.ChannelName == string_1)
					{
						HttpChannel httpChannel = (HttpChannel)channel;
						httpChannel.StopListening(null);
						ChannelServices.UnregisterChannel(httpChannel);
					}
				}
			}
		}

		// Token: 0x06000544 RID: 1348 RVA: 0x00028D58 File Offset: 0x00026F58
		public static bool smethod_5()
		{
			int num = 0;
			return ConnMgr.InternetGetConnectedState(ref num, 0);
		}

		// Token: 0x06000545 RID: 1349 RVA: 0x00028D74 File Offset: 0x00026F74
		public static bool smethod_6()
		{
			int num = 0;
			bool result;
			if (!ConnMgr.InternetGetConnectedState(ref num, 0))
			{
				if ((num & 4) != 0)
				{
					result = true;
				}
				else
				{
					result = false;
				}
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06000546 RID: 1350 RVA: 0x00028DA0 File Offset: 0x00026FA0
		public static IWebProxy smethod_7()
		{
			WebClient webClient = new WebClient();
			webClient.Proxy = WebRequest.GetSystemWebProxy();
			WebClient webClient2;
			webClient = (webClient2 = new WebClient());
			IWebProxy proxy;
			try
			{
				webClient.Proxy = WebRequest.GetSystemWebProxy();
				proxy = webClient.Proxy;
			}
			finally
			{
				if (webClient2 != null)
				{
					((IDisposable)webClient2).Dispose();
				}
			}
			return proxy;
		}

		// Token: 0x06000547 RID: 1351
		[DllImport("winInet.dll")]
		private static extern bool InternetGetConnectedState(ref int int_4, int int_5);

		// Token: 0x06000548 RID: 1352 RVA: 0x00028DFC File Offset: 0x00026FFC
		private static string smethod_8()
		{
			string result = "";
			int num = 0;
			if (!ConnMgr.InternetGetConnectedState(ref num, 0))
			{
				result = "未连接网络!";
			}
			else if ((num & 1) != 0)
			{
				result = "采用调治解调器上网。";
			}
			else if ((num & 2) != 0)
			{
				result = "采用网卡上网。";
			}
			return result;
		}

		// Token: 0x06000549 RID: 1353 RVA: 0x00028E40 File Offset: 0x00027040
		public static bool smethod_9(string string_1)
		{
			bool result;
			if (string.IsNullOrEmpty(string_1))
			{
				result = false;
			}
			else
			{
				WebApiWorker webApiWorker = new WebApiWorker("http://" + string_1 + "/Api/ChkConn.ashx", 800, false);
				ApiResult apiResult = null;
				try
				{
					apiResult = webApiWorker.GetApiResultByGet();
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
				if (apiResult != null && apiResult.msg == "ok")
				{
					result = true;
				}
				else
				{
					result = false;
				}
			}
			return result;
		}

		// Token: 0x0600054A RID: 1354 RVA: 0x00028EB8 File Offset: 0x000270B8
		public static bool smethod_10(string string_1, TimeSpan timeSpan_0 = default(TimeSpan))
		{
			string string_2 = ConnMgr.smethod_12(string_1);
			int int_ = ConnMgr.smethod_13(string_1);
			return ConnMgr.smethod_11(string_2, int_, timeSpan_0);
		}

		// Token: 0x0600054B RID: 1355 RVA: 0x00028EE0 File Offset: 0x000270E0
		public static bool smethod_11(string string_1, int int_4, TimeSpan timeSpan_0 = default(TimeSpan))
		{
			bool result;
			try
			{
				if (timeSpan_0 == default(TimeSpan))
				{
					timeSpan_0 = TimeSpan.FromMilliseconds(500.0);
				}
				using (TcpClient tcpClient = new TcpClient())
				{
					IAsyncResult asyncResult = tcpClient.BeginConnect(string_1, int_4, null, null);
					bool flag = asyncResult.AsyncWaitHandle.WaitOne(timeSpan_0);
					if (flag)
					{
						tcpClient.EndConnect(asyncResult);
					}
					else
					{
						tcpClient.Close();
					}
					result = flag;
				}
			}
			catch (Exception)
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600054C RID: 1356 RVA: 0x00028F74 File Offset: 0x00027174
		public static string smethod_12(string string_1)
		{
			string[] array = string_1.Split(new char[]
			{
				':'
			});
			string result;
			if (array.Length > 1)
			{
				result = array[0];
			}
			else
			{
				result = string_1;
			}
			return result;
		}

		// Token: 0x0600054D RID: 1357 RVA: 0x00028FA4 File Offset: 0x000271A4
		public static int smethod_13(string string_1)
		{
			int result = 80;
			string[] array = string_1.Split(new char[]
			{
				':'
			});
			if (array.Length > 1)
			{
				result = Convert.ToInt32(array[1]);
			}
			return result;
		}

		// Token: 0x04000230 RID: 560
		private const string string_0 = "TExSrv";

		// Token: 0x04000231 RID: 561
		private const int int_0 = 1;

		// Token: 0x04000232 RID: 562
		private const int int_1 = 2;

		// Token: 0x04000233 RID: 563
		private const int int_2 = 4;

		// Token: 0x04000234 RID: 564
		private const int int_3 = 8;
	}
}
