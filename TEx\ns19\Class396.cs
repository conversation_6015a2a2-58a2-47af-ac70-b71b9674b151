﻿using System;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using ns28;
using TEx.Chart;
using TEx.Comn;
using TEx.Inds;
using TEx.SIndicator;

namespace ns19
{
	// Token: 0x020002FB RID: 763
	internal sealed class Class396 : ShapeCurve
	{
		// Token: 0x06002137 RID: 8503 RVA: 0x000E39C0 File Offset: 0x000E1BC0
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			JapaneseCandleStickItem japaneseCandleStickItem = zedGraphControl_0.GraphPane.AddJapaneseCandleStick(base.IndData.Name, base.DataView);
			japaneseCandleStickItem.Label.IsVisible = false;
			japaneseCandleStickItem.Stick.Width = 1f;
			japaneseCandleStickItem.Stick.Color = color_0;
			japaneseCandleStickItem.Stick.RisingFill.Color = color_0;
			japaneseCandleStickItem.Stick.RisingBorder.Color = color_0;
			japaneseCandleStickItem.Stick.FallingColor = color_0;
			japaneseCandleStickItem.Stick.FallingBorder.Color = color_0;
			japaneseCandleStickItem.Stick.FallingFill.Color = color_0;
			zedGraphControl_0.GraphPane.BarSettings.Type = BarType.Overlay;
			if (base.IndData.SingleData.Contains("EMPTY") && (int)base.IndData.SingleData["EMPTY"] == 1)
			{
				japaneseCandleStickItem.Stick.RisingFill.Color = zedGraphControl_0.GraphPane.Fill.Color;
			}
			if (base.IndData.SingleData.Contains("WITH"))
			{
				double num = (double)base.IndData.SingleData["WITH"];
				japaneseCandleStickItem.Stick.Width = (float)((int)num);
			}
			this.curveItem_0 = japaneseCandleStickItem;
			japaneseCandleStickItem.Tag = string_0 + "_" + base.IndData.Name;
			japaneseCandleStickItem.Stick.Width = (float)base.method_2();
		}

		// Token: 0x06002138 RID: 8504 RVA: 0x0000D56B File Offset: 0x0000B76B
		public Class396(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}

		// Token: 0x06002139 RID: 8505 RVA: 0x0000D377 File Offset: 0x0000B577
		public override double vmethod_5(int int_0, HisData hisData_0)
		{
			throw new NotImplementedException();
		}

		// Token: 0x0600213A RID: 8506 RVA: 0x000E1F40 File Offset: 0x000E0140
		protected PointPair method_6(int int_0)
		{
			return this.vmethod_0(int_0, base.IndData);
		}

		// Token: 0x0600213B RID: 8507 RVA: 0x000E3B60 File Offset: 0x000E1D60
		protected override PointPair vmethod_0(int int_0, DataArray dataArray_1)
		{
			Class396.Class407 @class = new Class396.Class407();
			@class.dataArray_0 = dataArray_1;
			DateTime dateTime = base.method_0(int_0);
			PointPair result;
			if (@class.dataArray_0.Data.Length == 0)
			{
				Class182.smethod_0(new Exception("DataArray数组长度为零。"));
				result = new PointPair(new XDate(dateTime), double.NaN, double.NaN);
			}
			else
			{
				if (@class.dataArray_0.Data.Length < int_0 + 1)
				{
					Class182.smethod_0(new Exception("DataArray数组长度溢出。"));
					int_0 = @class.dataArray_0.Data.Length - 1;
					if (int_0 < 0)
					{
						int_0 = 0;
					}
				}
				if (@class.dataArray_0.OtherDataArrayList.Count != 2)
				{
					throw new Exception("柱线包含数据不足，请检查。");
				}
				if (@class.dataArray_0.OtherDataArrayList.Any(new Func<DataArray, bool>(@class.method_0)))
				{
					throw new Exception("柱线数据长度不相等。请检查数据。");
				}
				double num = @class.dataArray_0.Data[int_0];
				double num2 = @class.dataArray_0.OtherDataArrayList[1].Data[int_0];
				if (@class.dataArray_0.OtherDataArrayList[0].Data[int_0] == 1.0)
				{
					result = new StockPt(new XDate(dateTime), num, num2, num2, num, 0.0);
				}
				else
				{
					result = new PointPair(new XDate(dateTime), double.NaN, double.NaN);
				}
			}
			return result;
		}

		// Token: 0x0600213C RID: 8508 RVA: 0x000E3CDC File Offset: 0x000E1EDC
		public override void vmethod_8(ref double double_0, ref double double_1)
		{
			base.vmethod_8(ref double_0, ref double_1);
			for (int i = 0; i < this.rollingPointPairList_0.Count; i++)
			{
				double z = this.rollingPointPairList_0[i].Z;
				if (!double.IsNaN(z))
				{
					if (z > double_0)
					{
						double_0 = z;
					}
					if (z < double_1)
					{
						double_1 = z;
					}
				}
			}
		}

		// Token: 0x0600213D RID: 8509 RVA: 0x0000D5D1 File Offset: 0x0000B7D1
		public override void vmethod_2(int int_0)
		{
			if (base.KCount != base.IndData.Data.Count<double>())
			{
				throw new Exception("数据长度错误");
			}
			((IPointListEdit)this.rollingPointPairList_0).Add(this.method_6(int_0));
		}

		// Token: 0x0600213E RID: 8510 RVA: 0x000E3D34 File Offset: 0x000E1F34
		public override void vmethod_3(int int_0, DataArray dataArray_1)
		{
			PointPair point = this.vmethod_0(int_0, dataArray_1);
			((IPointListEdit)this.rollingPointPairList_0).Add(point);
		}

		// Token: 0x0600213F RID: 8511 RVA: 0x000E3D58 File Offset: 0x000E1F58
		public override void vmethod_4(int int_0, DataArray dataArray_1)
		{
			PointPair value = this.vmethod_0(int_0, dataArray_1);
			IPointListEdit rollingPointPairList_ = this.rollingPointPairList_0;
			if (rollingPointPairList_.Count > 0)
			{
				rollingPointPairList_[rollingPointPairList_.Count - 1] = value;
			}
		}

		// Token: 0x020002FC RID: 764
		[CompilerGenerated]
		private sealed class Class407
		{
			// Token: 0x06002141 RID: 8513 RVA: 0x000E3D90 File Offset: 0x000E1F90
			internal bool method_0(DataArray dataArray_1)
			{
				return dataArray_1.Data.Length != this.dataArray_0.Data.Length;
			}

			// Token: 0x04001032 RID: 4146
			public DataArray dataArray_0;
		}
	}
}
