﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using ns11;
using ns18;
using ns28;
using ns9;
using TEx.Inds;
using TEx.Util;

namespace TEx.SIndicator
{
	// Token: 0x02000319 RID: 793
	public sealed class ParserEnvironment
	{
		// Token: 0x06002203 RID: 8707 RVA: 0x000E8D04 File Offset: 0x000E6F04
		public ParserEnvironment(List<UserDefineParam> ups, UserDefineInd udf)
		{
			this.UserDefineIns = udf;
			this.ups = ups;
			using (List<UserDefineParam>.Enumerator enumerator = this.ups.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					ParserEnvironment.Class428 @class = new ParserEnvironment.Class428();
					@class.userDefineParam_0 = enumerator.Current;
					if (Array.Exists<MethodInfo>(ParserEnvironment.methodInfo_0, new Predicate<MethodInfo>(@class.method_0)))
					{
						throw new Exception(string.Format("自定义参数{0}和标准函数重复", @class.userDefineParam_0.Name));
					}
					if (Array.Exists<PropertyInfo>(this.propertyInfo_0, new Predicate<PropertyInfo>(@class.method_1)))
					{
						throw new Exception(string.Format("自定义参数{0}和属性重复", @class.userDefineParam_0.Name));
					}
				}
			}
		}

		// Token: 0x06002204 RID: 8708 RVA: 0x000E8E08 File Offset: 0x000E7008
		public static string smethod_0(string string_4)
		{
			string text = "";
			List<IndFuncAttri> indFuncAttriList = ParserEnvironment.IndFuncAttriList;
			for (int i = 0; i < indFuncAttriList.Count; i++)
			{
				if (string_4.ToUpper() == indFuncAttriList[i].Name)
				{
					string str = string_4 + indFuncAttriList[i].ParamStr + " " + indFuncAttriList[i].Script;
					text += str;
					text += "\n";
				}
			}
			if (text.Count<char>() > 0 && text.Last<char>() == '\n')
			{
				text = text.Remove(text.Length - 1);
			}
			return text;
		}

		// Token: 0x06002205 RID: 8709 RVA: 0x000E8EAC File Offset: 0x000E70AC
		public static double smethod_1(string[] string_4)
		{
			double num = 4.5;
			double result;
			if (string_4.Any<string>())
			{
				double num2 = string_4.Select(new Func<string, double>(ParserEnvironment.<>c.<>9.method_0)).Max();
				if (num <= num2)
				{
					result = num2;
				}
				else
				{
					result = num;
				}
			}
			else
			{
				result = num;
			}
			return result;
		}

		// Token: 0x06002206 RID: 8710 RVA: 0x000E8F08 File Offset: 0x000E7108
		public static double smethod_2(string string_4)
		{
			ParserEnvironment.Class429 @class = new ParserEnvironment.Class429();
			@class.string_0 = string_4;
			return ParserEnvironment.IndFuncAttriList.Where(new Func<IndFuncAttri, bool>(@class.method_0)).Max(new Func<IndFuncAttri, double>(ParserEnvironment.<>c.<>9.method_1));
		}

		// Token: 0x06002207 RID: 8711 RVA: 0x000E8F60 File Offset: 0x000E7160
		public static bool smethod_3(string string_4)
		{
			if (string_4.Length == "COLOR000000".Length && string_4.Substring(0, 5) == "COLOR")
			{
				bool flag = true;
				for (int i = 5; i < string_4.Length; i++)
				{
					if (!char.IsNumber(string_4, i) && (string_4[i] < 'A' || string_4[i] > 'F'))
					{
						return false;
					}
					flag = true;
				}
				if (flag)
				{
					return true;
				}
			}
			return false;
		}

		// Token: 0x06002208 RID: 8712 RVA: 0x000E8FD8 File Offset: 0x000E71D8
		public static bool smethod_4(string string_4)
		{
			ParserEnvironment.Class430 @class = new ParserEnvironment.Class430();
			@class.string_0 = string_4;
			bool flag = ParserEnvironment.string_3.Any(new Func<string, bool>(@class.method_0));
			bool result;
			if (@class.string_0 == "COLOR")
			{
				result = false;
			}
			else if (flag)
			{
				result = true;
			}
			else if (ParserEnvironment.smethod_3(@class.string_0))
			{
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06002209 RID: 8713 RVA: 0x000E903C File Offset: 0x000E723C
		public static bool smethod_5(string string_4)
		{
			for (int i = 0; i < string_4.Length; i++)
			{
				ParserEnvironment.Class431 @class = new ParserEnvironment.Class431();
				@class.char_0 = string_4[i];
				if (!char.IsLetterOrDigit(@class.char_0) && !ParserEnvironment.char_0.Any(new Func<char, bool>(@class.method_0)))
				{
					return false;
				}
			}
			return true;
		}

		// Token: 0x0600220A RID: 8714 RVA: 0x000E909C File Offset: 0x000E729C
		public bool method_0(string string_4, out DataArray dataArray_0)
		{
			Class437 @class = new Class437(this);
			bool result;
			try
			{
				if (!ParserEnvironment.smethod_5(string_4))
				{
					dataArray_0 = null;
					result = false;
				}
				else
				{
					Tokenes tokenes = @class.method_1(string_4);
					tokenes.method_4();
					string string_5 = "";
					dataArray_0 = null;
					if (tokenes.Current.Symbol.HSymbolType == Enum26.const_4)
					{
						string name = tokenes.Current.Symbol.Name;
						tokenes.method_1();
						if (tokenes.Current.Symbol.HSymbolType != Enum26.const_19)
						{
							result = false;
						}
						else
						{
							tokenes.method_1();
							if (tokenes.Current.Symbol.HSymbolType == Enum26.const_4 || tokenes.Current.Symbol.HSymbolType == Enum26.const_1)
							{
								string_5 = tokenes.Current.Symbol.Name;
							}
							tokenes.method_1();
							if (tokenes.Current.Symbol.HSymbolType == Enum26.const_30)
							{
								dataArray_0 = this.method_1(name, string_5, new List<double>());
								if (dataArray_0 != null)
								{
									result = true;
								}
								else
								{
									result = false;
								}
							}
							else if (tokenes.Current.Symbol.HSymbolType == Enum26.const_23)
							{
								HToken htoken = tokenes.Current;
								List<double> list = new List<double>();
								while (htoken.Symbol.HSymbolType != Enum26.const_30)
								{
									tokenes.method_1();
									htoken = tokenes.Current;
									if (htoken.Symbol.HSymbolType == Enum26.const_5)
									{
										list.Add(double.Parse(htoken.Symbol.Name));
									}
								}
								dataArray_0 = this.method_1(name, string_5, list);
								if (dataArray_0 != null)
								{
									result = true;
								}
								else
								{
									result = false;
								}
							}
							else
							{
								result = false;
							}
						}
					}
					else
					{
						result = false;
					}
				}
			}
			catch (Exception)
			{
				dataArray_0 = null;
				result = false;
			}
			return result;
		}

		// Token: 0x0600220B RID: 8715 RVA: 0x000E9264 File Offset: 0x000E7464
		private DataArray method_1(string string_4, string string_5, List<double> list_4)
		{
			ParserEnvironment.Class432 @class = new ParserEnvironment.Class432();
			@class.string_0 = string_4;
			UserDefineIndScript userDefineIndScript = UserDefineFileMgr.UDSListChecked.SingleOrDefault(new Func<UserDefineIndScript, bool>(@class.method_0));
			DataArray result;
			if (userDefineIndScript == null)
			{
				result = null;
			}
			else
			{
				UserDefineInd userDefineInd = new UserDefineInd(this.UserDefineIns.DataProvider, userDefineIndScript);
				if (list_4.Count == userDefineIndScript.UserDefineParams.Count)
				{
					for (int i = 0; i < userDefineIndScript.UserDefineParams.Count; i++)
					{
						userDefineIndScript.UserDefineParams[i].Value = list_4[i];
					}
				}
				foreach (DataArray dataArray in userDefineInd.method_9())
				{
					if (dataArray.Name == string_5)
					{
						return dataArray;
					}
				}
				result = null;
			}
			return result;
		}

		// Token: 0x0600220C RID: 8716 RVA: 0x000E9334 File Offset: 0x000E7534
		public static Color[] smethod_6()
		{
			return ParserEnvironment.string_3.Where(new Func<string, bool>(ParserEnvironment.<>c.<>9.method_2)).Select(new Func<string, Color>(ParserEnvironment.<>c.<>9.method_3)).ToArray<Color>();
		}

		// Token: 0x0600220D RID: 8717 RVA: 0x000E9398 File Offset: 0x000E7598
		public static Color? smethod_7(string string_4)
		{
			ParserEnvironment.Class433 @class = new ParserEnvironment.Class433();
			Color? result;
			if (string.IsNullOrEmpty(string_4))
			{
				Color? color = null;
				result = color;
			}
			else
			{
				@class.string_0 = string_4.ToUpper();
				Color? color;
				if (ParserEnvironment.smethod_3(string_4))
				{
					try
					{
						int blue = int.Parse(string_4.Substring(5, 2), NumberStyles.HexNumber);
						int green = int.Parse(string_4.Substring(7, 2), NumberStyles.HexNumber);
						color = new Color?(Color.FromArgb(int.Parse(string_4.Substring(9, 2), NumberStyles.HexNumber), green, blue));
						goto IL_138;
					}
					catch
					{
						Class182.smethod_0(new Exception("'" + string_4 + "'无法解析为颜色！"));
						color = new Color?(default(Color));
						goto IL_138;
					}
				}
				if (!ParserEnvironment.string_3.Any(new Func<string, bool>(@class.method_0)))
				{
					if (Utility.IsDigitChars(@class.string_0, false, false))
					{
						try
						{
							int num = Convert.ToInt32(@class.string_0);
							if (num < 0)
							{
								color = new Color?(Color.Red);
							}
							else
							{
								if (num <= ParserEnvironment.string_3.Count<string>())
								{
									@class.string_0 = ParserEnvironment.string_3[num];
									goto IL_156;
								}
								color = new Color?(Color.Yellow);
							}
						}
						catch
						{
							throw new Exception("'" + string_4 + "'无法解析为颜色！");
						}
						goto IL_138;
					}
					throw new Exception("'" + string_4 + "'无法解析为颜色！");
				}
				IL_156:
				string a = @class.string_0;
				uint num2 = Class508.smethod_0(a);
				if (num2 <= 1197706523U)
				{
					if (num2 <= 236932418U)
					{
						if (num2 != 139680548U)
						{
							if (num2 != 210056287U)
							{
								if (num2 == 236932418U)
								{
									if (a == "COLORLIGHTBULE")
									{
										return new Color?(Color.LightBlue);
									}
								}
							}
							else if (a == "COLORRED")
							{
								return new Color?(Color.Red);
							}
						}
						else if (a == "COLORBLUE")
						{
							return new Color?(Color.Blue);
						}
					}
					else if (num2 <= 623482133U)
					{
						if (num2 != 507969603U)
						{
							if (num2 == 623482133U)
							{
								if (a == "COLORWHITE")
								{
									return new Color?(Color.White);
								}
							}
						}
						else if (a == "COLORLIGHTGRAY")
						{
							return new Color?(Color.LightGray);
						}
					}
					else if (num2 != 858922475U)
					{
						if (num2 == 1197706523U)
						{
							if (a == "COLORBLACK")
							{
								return new Color?(Color.Black);
							}
						}
					}
					else if (a == "COLORLIGHTRED")
					{
						return new Color?(Color.Red);
					}
				}
				else if (num2 <= 2699790559U)
				{
					if (num2 != 1803559607U)
					{
						if (num2 != 2683819632U)
						{
							if (num2 == 2699790559U)
							{
								if (a == "COLORCYAN")
								{
									return new Color?(Color.Cyan);
								}
							}
						}
						else if (a == "COLORYELLOW")
						{
							return new Color?(Color.Yellow);
						}
					}
					else if (a == "COLORLIGHTGREEN")
					{
						return new Color?(Color.LightGreen);
					}
				}
				else if (num2 <= 3888318712U)
				{
					if (num2 != 3372332139U)
					{
						if (num2 == 3888318712U)
						{
							if (a == "COLOR")
							{
								return null;
							}
						}
					}
					else if (a == "COLORGREEN")
					{
						return new Color?(Color.Green);
					}
				}
				else if (num2 != 3962745255U)
				{
					if (num2 == 4102714959U)
					{
						if (a == "COLORMAGENTA")
						{
							return new Color?(Color.Magenta);
						}
					}
				}
				else if (a == "COLORGRAY")
				{
					return new Color?(Color.Gray);
				}
				throw new Exception("解析颜色错误");
				IL_138:
				result = color;
			}
			return result;
		}

		// Token: 0x0600220E RID: 8718 RVA: 0x000E97C8 File Offset: 0x000E79C8
		public static bool smethod_8(string string_4)
		{
			ParserEnvironment.Class434 @class = new ParserEnvironment.Class434();
			bool result;
			if (string_4 == null)
			{
				result = false;
			}
			else
			{
				@class.string_0 = string_4.ToUpper();
				if (ParserEnvironment.string_0.Any(new Func<string, bool>(@class.method_0)))
				{
					result = true;
				}
				else
				{
					result = false;
				}
			}
			return result;
		}

		// Token: 0x0600220F RID: 8719 RVA: 0x000E9810 File Offset: 0x000E7A10
		public static bool smethod_9(string string_4)
		{
			ParserEnvironment.Class435 @class = new ParserEnvironment.Class435();
			@class.string_0 = string_4;
			return ParserEnvironment.string_2.Any(new Func<string, bool>(@class.method_0));
		}

		// Token: 0x06002210 RID: 8720 RVA: 0x000E9844 File Offset: 0x000E7A44
		public static bool smethod_10(string string_4)
		{
			ParserEnvironment.Class436 @class = new ParserEnvironment.Class436();
			@class.string_0 = string_4;
			return ParserEnvironment.string_1.Any(new Func<string, bool>(@class.method_0));
		}

		// Token: 0x06002211 RID: 8721 RVA: 0x000E9878 File Offset: 0x000E7A78
		public static string[] smethod_11(List<UserDefineParam> list_4)
		{
			List<string> list = new List<string>();
			MethodInfo[] source = typeof(SIndicatorBase).GetMethods().Where(new Func<MethodInfo, bool>(ParserEnvironment.<>c.<>9.method_4)).ToArray<MethodInfo>();
			PropertyInfo[] properties = typeof(SIndicatorBase).GetProperties();
			list.AddRange(source.Select(new Func<MethodInfo, string>(ParserEnvironment.<>c.<>9.method_5)).Distinct<string>());
			list.AddRange(properties.Select(new Func<PropertyInfo, string>(ParserEnvironment.<>c.<>9.method_6)));
			list.AddRange(list_4.Select(new Func<UserDefineParam, string>(ParserEnvironment.<>c.<>9.method_7)));
			list.AddRange(ParserEnvironment.string_2);
			list.AddRange(ParserEnvironment.string_1);
			list.AddRange(ParserEnvironment.string_3);
			list.AddRange(ParserEnvironment.string_0);
			return list.ToArray();
		}

		// Token: 0x06002212 RID: 8722 RVA: 0x0000D907 File Offset: 0x0000BB07
		public void method_2(string string_4)
		{
			this.list_2.Add(string_4);
		}

		// Token: 0x170005D7 RID: 1495
		// (get) Token: 0x06002213 RID: 8723 RVA: 0x000E9990 File Offset: 0x000E7B90
		public static List<IndFuncAttri> IndFuncAttriList
		{
			get
			{
				return ParserEnvironment.list_1;
			}
		}

		// Token: 0x170005D8 RID: 1496
		// (get) Token: 0x06002214 RID: 8724 RVA: 0x000E99A8 File Offset: 0x000E7BA8
		public MethodInfo[] Functions
		{
			get
			{
				return ParserEnvironment.methodInfo_0;
			}
		}

		// Token: 0x170005D9 RID: 1497
		// (get) Token: 0x06002215 RID: 8725 RVA: 0x000E99C0 File Offset: 0x000E7BC0
		public PropertyInfo[] Properties
		{
			get
			{
				return this.propertyInfo_0;
			}
		}

		// Token: 0x170005DA RID: 1498
		// (get) Token: 0x06002216 RID: 8726 RVA: 0x000E99D8 File Offset: 0x000E7BD8
		// (set) Token: 0x06002217 RID: 8727 RVA: 0x0000D917 File Offset: 0x0000BB17
		public List<UserDefineParam> UserDefineParams
		{
			get
			{
				return this.ups;
			}
			private set
			{
				this.ups = value;
			}
		}

		// Token: 0x170005DB RID: 1499
		// (get) Token: 0x06002218 RID: 8728 RVA: 0x000E99F0 File Offset: 0x000E7BF0
		public List<string> NewVarList
		{
			get
			{
				return this.list_2;
			}
		}

		// Token: 0x170005DC RID: 1500
		// (get) Token: 0x06002219 RID: 8729 RVA: 0x000E9A08 File Offset: 0x000E7C08
		// (set) Token: 0x0600221A RID: 8730 RVA: 0x0000D922 File Offset: 0x0000BB22
		public List<DataArray> DataArrays { get; set; }

		// Token: 0x170005DD RID: 1501
		// (get) Token: 0x0600221B RID: 8731 RVA: 0x000E9A20 File Offset: 0x000E7C20
		// (set) Token: 0x0600221C RID: 8732 RVA: 0x0000D92D File Offset: 0x0000BB2D
		public UserDefineInd UserDefineIns { get; private set; }

		// Token: 0x0400106B RID: 4203
		public static readonly string[] string_0 = new string[]
		{
			"LINE",
			"STICK",
			"KLINE",
			"CIRCLEDOT",
			"COLORSTICK",
			"VOLSTICK"
		};

		// Token: 0x0400106C RID: 4204
		private static readonly string[] string_1 = new string[]
		{
			"LINETHICK0",
			"LINETHICK1",
			"LINETHICK2",
			"LINETHICK3",
			"LINETHICK4",
			"LINETHICK5",
			"LINETHICK6",
			"LINETHICK7",
			"LINETHICK8",
			"LINETHICK9",
			"LINETHICK10"
		};

		// Token: 0x0400106D RID: 4205
		private static readonly string[] string_2 = new string[]
		{
			"DASH",
			"DASHDOT",
			"DOT",
			"DOTLINE",
			"LINEDOT",
			"NODRAW",
			"POINTDOT"
		};

		// Token: 0x0400106E RID: 4206
		private static readonly string[] string_3 = new string[]
		{
			"COLORRED",
			"COLORBLUE",
			"COLORGREEN",
			"COLORMAGENTA",
			"COLORLIGHTGRAY",
			"COLORLIGHTRED",
			"COLORLIGHTGREEN",
			"COLORLIGHTBULE",
			"COLORBLACK",
			"COLORWHITE",
			"COLORCYAN",
			"COLORGRAY",
			"COLORYELLOW",
			"COLOR"
		};

		// Token: 0x0400106F RID: 4207
		private static char[] char_0 = new char[]
		{
			'<',
			'>',
			'!',
			'+',
			'-',
			'*',
			'/',
			':',
			'=',
			'(',
			')',
			'.',
			'\'',
			'\\',
			',',
			';',
			'_'
		};

		// Token: 0x04001070 RID: 4208
		public List<NameDoubleValue> list_0 = new List<NameDoubleValue>();

		// Token: 0x04001071 RID: 4209
		private static List<IndFuncAttri> list_1 = SIndicatorBase.GetAttris();

		// Token: 0x04001072 RID: 4210
		private static MethodInfo[] methodInfo_0 = SIndicatorBase.GetMethods();

		// Token: 0x04001073 RID: 4211
		private PropertyInfo[] propertyInfo_0 = SIndicatorBase.GetProperties();

		// Token: 0x04001074 RID: 4212
		private List<UserDefineParam> ups = new List<UserDefineParam>();

		// Token: 0x04001075 RID: 4213
		private List<string> list_2 = new List<string>();

		// Token: 0x04001076 RID: 4214
		[CompilerGenerated]
		private List<DataArray> list_3;

		// Token: 0x04001077 RID: 4215
		[CompilerGenerated]
		private UserDefineInd userDefineInd_0;

		// Token: 0x0200031A RID: 794
		[CompilerGenerated]
		private sealed class Class428
		{
			// Token: 0x0600221F RID: 8735 RVA: 0x000E9BD8 File Offset: 0x000E7DD8
			internal bool method_0(MethodInfo methodInfo_0)
			{
				return methodInfo_0.Name == this.userDefineParam_0.Name;
			}

			// Token: 0x06002220 RID: 8736 RVA: 0x000E9BD8 File Offset: 0x000E7DD8
			internal bool method_1(PropertyInfo propertyInfo_0)
			{
				return propertyInfo_0.Name == this.userDefineParam_0.Name;
			}

			// Token: 0x04001078 RID: 4216
			public UserDefineParam userDefineParam_0;
		}

		// Token: 0x0200031C RID: 796
		[CompilerGenerated]
		private sealed class Class429
		{
			// Token: 0x0600222C RID: 8748 RVA: 0x000E9C9C File Offset: 0x000E7E9C
			internal bool method_0(IndFuncAttri indFuncAttri_0)
			{
				return indFuncAttri_0.Name == this.string_0;
			}

			// Token: 0x04001082 RID: 4226
			public string string_0;
		}

		// Token: 0x0200031D RID: 797
		[CompilerGenerated]
		private sealed class Class430
		{
			// Token: 0x0600222E RID: 8750 RVA: 0x000E9CC0 File Offset: 0x000E7EC0
			internal bool method_0(string string_1)
			{
				return string_1 == this.string_0.ToUpper();
			}

			// Token: 0x04001083 RID: 4227
			public string string_0;
		}

		// Token: 0x0200031E RID: 798
		[CompilerGenerated]
		private sealed class Class431
		{
			// Token: 0x06002230 RID: 8752 RVA: 0x000E9CE4 File Offset: 0x000E7EE4
			internal bool method_0(char char_1)
			{
				return char_1 == this.char_0;
			}

			// Token: 0x04001084 RID: 4228
			public char char_0;
		}

		// Token: 0x0200031F RID: 799
		[CompilerGenerated]
		private sealed class Class432
		{
			// Token: 0x06002232 RID: 8754 RVA: 0x000E9D00 File Offset: 0x000E7F00
			internal bool method_0(UserDefineIndScript userDefineIndScript_0)
			{
				return userDefineIndScript_0.Name == this.string_0;
			}

			// Token: 0x04001085 RID: 4229
			public string string_0;
		}

		// Token: 0x02000320 RID: 800
		[CompilerGenerated]
		private sealed class Class433
		{
			// Token: 0x06002234 RID: 8756 RVA: 0x000E9D24 File Offset: 0x000E7F24
			internal bool method_0(string string_1)
			{
				return string_1.ToUpper() == this.string_0;
			}

			// Token: 0x04001086 RID: 4230
			public string string_0;
		}

		// Token: 0x02000321 RID: 801
		[CompilerGenerated]
		private sealed class Class434
		{
			// Token: 0x06002236 RID: 8758 RVA: 0x000E9D48 File Offset: 0x000E7F48
			internal bool method_0(string string_1)
			{
				return string_1.ToUpper() == this.string_0;
			}

			// Token: 0x04001087 RID: 4231
			public string string_0;
		}

		// Token: 0x02000322 RID: 802
		[CompilerGenerated]
		private sealed class Class435
		{
			// Token: 0x06002238 RID: 8760 RVA: 0x000E9D6C File Offset: 0x000E7F6C
			internal bool method_0(string string_1)
			{
				return string_1 == this.string_0.ToUpper();
			}

			// Token: 0x04001088 RID: 4232
			public string string_0;
		}

		// Token: 0x02000323 RID: 803
		[CompilerGenerated]
		private sealed class Class436
		{
			// Token: 0x0600223A RID: 8762 RVA: 0x000E9D90 File Offset: 0x000E7F90
			internal bool method_0(string string_1)
			{
				return string_1 == this.string_0.ToUpper();
			}

			// Token: 0x04001089 RID: 4233
			public string string_0;
		}
	}
}
