﻿using System;
using System.Drawing;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns14
{
	// Token: 0x020002F7 RID: 759
	internal sealed class Class392 : ShapeCurve
	{
		// Token: 0x0600212B RID: 8491 RVA: 0x000E345C File Offset: 0x000E165C
		public override void vmethod_6(string string_1, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			if (base.IndData.SingleData.Contains("Format"))
			{
				this.string_0 = (string)base.IndData.SingleData["Format"];
			}
			Ind_TextItem ind_TextItem = zedGraphControl_0.GraphPane.AddTextItem(base.IndData.Name, base.DataView, SymbolType.None, "Num");
			ind_TextItem.Symbol.Border.Color = color_0;
			this.curveItem_0 = ind_TextItem;
			ind_TextItem.Line.IsVisible = false;
			ind_TextItem.Tag = string_1 + "_" + base.IndData.Name;
			base.method_3(string_1, ind_TextItem);
		}

		// Token: 0x0600212C RID: 8492 RVA: 0x0000D5A5 File Offset: 0x0000B7A5
		public Class392(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}

		// Token: 0x0600212D RID: 8493 RVA: 0x000E3530 File Offset: 0x000E1730
		protected override PointPair vmethod_0(int int_0, DataArray dataArray_1)
		{
			DateTime dateTime = base.method_0(int_0);
			if (dataArray_1.Data.Length < int_0 + 1)
			{
				throw new Exception("数据长度溢出。");
			}
			double num = dataArray_1.Data[int_0];
			if (dataArray_1.OtherDataArrayList.Count != 2)
			{
				throw new Exception("文字字段包含数据不足，请检查。");
			}
			int num2 = (int)dataArray_1.OtherDataArrayList[0].Data[int_0];
			double d = dataArray_1.OtherDataArrayList[1].Data[int_0];
			PointPair result;
			if (num2 == 1 && !double.IsNaN(d) && !double.IsNaN(num))
			{
				result = new PointPair(new XDate(dateTime), num, 1.0, d.ToString(this.string_0));
			}
			else
			{
				result = new PointPair(new XDate(dateTime), double.NaN, 0.0);
			}
			return result;
		}

		// Token: 0x04001030 RID: 4144
		private string string_0 = "";
	}
}
