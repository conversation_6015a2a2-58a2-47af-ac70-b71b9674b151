﻿using System;
using System.Runtime.CompilerServices;

namespace ns32
{
	// Token: 0x0200013B RID: 315
	internal sealed class Class192
	{
		// Token: 0x06000CCA RID: 3274 RVA: 0x00005BCB File Offset: 0x00003DCB
		public Class192(int int_1, bool bool_2, int? nullable_4, int? nullable_5, bool bool_3, int? nullable_6, int? nullable_7)
		{
			this.stkId = int_1;
			this.getMin = bool_2;
			this.minBegYr = nullable_4;
			this.minEndYr = nullable_5;
			this.getHour = bool_3;
			this.hourBegYr = nullable_6;
			this.hourEndYr = nullable_7;
		}

		// Token: 0x17000208 RID: 520
		// (get) Token: 0x06000CCB RID: 3275 RVA: 0x0004AA40 File Offset: 0x00048C40
		// (set) Token: 0x06000CCC RID: 3276 RVA: 0x00005C0A File Offset: 0x00003E0A
		public int stkId { get; set; }

		// Token: 0x17000209 RID: 521
		// (get) Token: 0x06000CCD RID: 3277 RVA: 0x0004AA58 File Offset: 0x00048C58
		// (set) Token: 0x06000CCE RID: 3278 RVA: 0x00005C15 File Offset: 0x00003E15
		public bool getMin { get; set; }

		// Token: 0x1700020A RID: 522
		// (get) Token: 0x06000CCF RID: 3279 RVA: 0x0004AA70 File Offset: 0x00048C70
		// (set) Token: 0x06000CD0 RID: 3280 RVA: 0x00005C20 File Offset: 0x00003E20
		public int? minBegYr { get; set; }

		// Token: 0x1700020B RID: 523
		// (get) Token: 0x06000CD1 RID: 3281 RVA: 0x0004AA88 File Offset: 0x00048C88
		// (set) Token: 0x06000CD2 RID: 3282 RVA: 0x00005C2B File Offset: 0x00003E2B
		public int? minEndYr { get; set; }

		// Token: 0x1700020C RID: 524
		// (get) Token: 0x06000CD3 RID: 3283 RVA: 0x0004AAA0 File Offset: 0x00048CA0
		// (set) Token: 0x06000CD4 RID: 3284 RVA: 0x00005C36 File Offset: 0x00003E36
		public bool getHour { get; set; }

		// Token: 0x1700020D RID: 525
		// (get) Token: 0x06000CD5 RID: 3285 RVA: 0x0004AAB8 File Offset: 0x00048CB8
		// (set) Token: 0x06000CD6 RID: 3286 RVA: 0x00005C41 File Offset: 0x00003E41
		public int? hourBegYr { get; set; }

		// Token: 0x1700020E RID: 526
		// (get) Token: 0x06000CD7 RID: 3287 RVA: 0x0004AAD0 File Offset: 0x00048CD0
		// (set) Token: 0x06000CD8 RID: 3288 RVA: 0x00005C4C File Offset: 0x00003E4C
		public int? hourEndYr { get; set; }

		// Token: 0x04000545 RID: 1349
		[CompilerGenerated]
		private int int_0;

		// Token: 0x04000546 RID: 1350
		[CompilerGenerated]
		private bool bool_0;

		// Token: 0x04000547 RID: 1351
		[CompilerGenerated]
		private int? nullable_0;

		// Token: 0x04000548 RID: 1352
		[CompilerGenerated]
		private int? nullable_1;

		// Token: 0x04000549 RID: 1353
		[CompilerGenerated]
		private bool bool_1;

		// Token: 0x0400054A RID: 1354
		[CompilerGenerated]
		private int? nullable_2;

		// Token: 0x0400054B RID: 1355
		[CompilerGenerated]
		private int? nullable_3;
	}
}
