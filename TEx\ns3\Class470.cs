﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

namespace ns3
{
	// Token: 0x0200036A RID: 874
	internal sealed class Class470
	{
		// Token: 0x1700063A RID: 1594
		// (get) Token: 0x06002488 RID: 9352 RVA: 0x000F4D78 File Offset: 0x000F2F78
		// (set) Token: 0x06002489 RID: 9353 RVA: 0x0000E2F3 File Offset: 0x0000C4F3
		public List<DateTime> DayList { get; private set; }

		// Token: 0x1700063B RID: 1595
		// (get) Token: 0x0600248A RID: 9354 RVA: 0x000F4D90 File Offset: 0x000F2F90
		// (set) Token: 0x0600248B RID: 9355 RVA: 0x0000E2FE File Offset: 0x0000C4FE
		public int Idx { get; private set; }

		// Token: 0x1700063C RID: 1596
		// (get) Token: 0x0600248C RID: 9356 RVA: 0x000F4DA8 File Offset: 0x000F2FA8
		// (set) Token: 0x0600248D RID: 9357 RVA: 0x0000E309 File Offset: 0x0000C509
		public bool OK { get; private set; }

		// Token: 0x0600248E RID: 9358 RVA: 0x0000E314 File Offset: 0x0000C514
		public void method_0(bool bool_1)
		{
			this.OK = bool_1;
		}

		// Token: 0x0600248F RID: 9359 RVA: 0x0000E31F File Offset: 0x0000C51F
		public Class470(DateTime dateTime_1)
		{
			this.DayList = new List<DateTime>();
			this.dateTime_0 = dateTime_1;
			this.method_1();
		}

		// Token: 0x06002490 RID: 9360 RVA: 0x0000E341 File Offset: 0x0000C541
		private void method_1()
		{
			this.DayList.Clear();
			this.Idx = -1;
			this.OK = false;
		}

		// Token: 0x1700063D RID: 1597
		// (get) Token: 0x06002491 RID: 9361 RVA: 0x000F4DC0 File Offset: 0x000F2FC0
		public DateTime CurrDay
		{
			get
			{
				DateTime result;
				if (this.Idx < this.DayList.Count)
				{
					result = this.DayList[this.Idx];
				}
				else
				{
					result = this.DayList[this.DayList.Count - 1];
				}
				return result;
			}
		}

		// Token: 0x06002492 RID: 9362 RVA: 0x0000E35E File Offset: 0x0000C55E
		public void method_2(DateTime dateTime_1)
		{
			this.OK = false;
			this.Idx = this.DayList.IndexOf(dateTime_1);
			if (this.Idx == -1)
			{
				throw new Exception(string.Format("重置当前时间{0}失败,在序列中找不到此时间.", dateTime_1));
			}
		}

		// Token: 0x06002493 RID: 9363 RVA: 0x000F4E10 File Offset: 0x000F3010
		public bool method_3(DateTime dateTime_1)
		{
			this.method_1();
			DateTime dateTime = this.dateTime_0;
			bool flag = false;
			DateTime dateTime2 = dateTime_1.Date;
			while (dateTime2 <= dateTime.Date)
			{
				if (dateTime2.DayOfWeek != DayOfWeek.Saturday && dateTime2.DayOfWeek != DayOfWeek.Sunday)
				{
					this.DayList.Add(dateTime2);
					flag = true;
				}
				dateTime2 = dateTime2.AddDays(1.0);
			}
			if (flag)
			{
				this.Idx = 0;
			}
			return flag;
		}

		// Token: 0x06002494 RID: 9364 RVA: 0x000F4E88 File Offset: 0x000F3088
		public void method_4()
		{
			if (!this.OK)
			{
				if (this.Idx < this.DayList.Count)
				{
					int idx = this.Idx;
					this.Idx = idx + 1;
				}
				if (this.Idx >= this.DayList.Count)
				{
					this.OK = true;
				}
			}
		}

		// Token: 0x040011A8 RID: 4520
		[CompilerGenerated]
		private List<DateTime> list_0;

		// Token: 0x040011A9 RID: 4521
		[CompilerGenerated]
		private int int_0;

		// Token: 0x040011AA RID: 4522
		[CompilerGenerated]
		private bool bool_0;

		// Token: 0x040011AB RID: 4523
		private DateTime dateTime_0;
	}
}
