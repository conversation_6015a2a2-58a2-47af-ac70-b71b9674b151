﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Management;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using ns14;
using ns28;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x020001AF RID: 431
	internal static class TApp
	{
		// Token: 0x0600109F RID: 4255 RVA: 0x000728F8 File Offset: 0x00070AF8
		static TApp()
		{
			TApp.class16_0.HeartBeatResponsed += TApp.smethod_0;
		}

		// Token: 0x060010A0 RID: 4256 RVA: 0x000729FC File Offset: 0x00070BFC
		private static void smethod_0(object sender, MsgEventArgs e)
		{
			ApiResult apiResult = e.Data as ApiResult;
			if (apiResult.code != 0)
			{
				string text = string.Format("账户异常，程序即将关闭({0})。", apiResult.code);
				if (apiResult.code == 120)
				{
					text = "当前账号已在其他电脑上登录，程序即将关闭。";
				}
				else if (apiResult.code == 110 || apiResult.code == 100)
				{
					text = "当前登录已失效，程序即将关闭。";
				}
				MessageBox.Show(text, "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				Environment.Exit(0);
			}
		}

		// Token: 0x060010A1 RID: 4257 RVA: 0x00072A78 File Offset: 0x00070C78
		public static bool smethod_1(TExPackage? nullable_0)
		{
			bool result;
			if (nullable_0 != null)
			{
				result = Enum.GetName(typeof(TExPackage), nullable_0).Contains("TRL");
			}
			else
			{
				result = true;
			}
			return result;
		}

		// Token: 0x060010A2 RID: 4258 RVA: 0x00072AB8 File Offset: 0x00070CB8
		public static void smethod_2()
		{
			bool flag = false;
			if (TApp.string_2.Length != 0)
			{
				if (TApp.string_2.Length > 1)
				{
					for (int i = 0; i < TApp.string_2.Length - 1; i++)
					{
						if (ConnMgr.smethod_9(TApp.string_0 = TApp.string_2[i]))
						{
							flag = true;
							break;
						}
					}
				}
				else
				{
					TApp.string_0 = TApp.string_2[0];
				}
			}
			TApp.HOST = TApp.string_0;
			if (!string.IsNullOrEmpty(TApp.string_3) && !flag && !ConnMgr.smethod_9(TApp.string_0))
			{
				TApp.HOST = TApp.string_3;
			}
		}

		// Token: 0x060010A3 RID: 4259 RVA: 0x00007216 File Offset: 0x00005416
		public static void smethod_3(string string_19)
		{
			Utility.DeleteDir(TApp.string_7);
			if (string_19 != null)
			{
				Utility.DeleteDir(TApp.string_8 + string_19.Trim() + "\\HDPset\\");
			}
		}

		// Token: 0x060010A4 RID: 4260 RVA: 0x00072B44 File Offset: 0x00070D44
		public static float smethod_4(float float_1 = 9f, bool bool_5 = false)
		{
			float num = float_1;
			if (!TApp.IsHighDpiScreen)
			{
				try
				{
					num = (float)((double)float_1 * 1.25 / TApp.DpiScale);
					if (!bool_5)
					{
						num *= TApp.DpiScaleMulti;
					}
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
			return num;
		}

		// Token: 0x060010A5 RID: 4261 RVA: 0x00072B98 File Offset: 0x00070D98
		public static Size smethod_5()
		{
			Size result;
			try
			{
				ManagementScope scope = new ManagementScope();
				ObjectQuery query = new ObjectQuery("SELECT * FROM CIM_VideoControllerResolution");
				uint num = 0U;
				uint num2 = 0U;
				using (ManagementObjectSearcher managementObjectSearcher = new ManagementObjectSearcher(scope, query))
				{
					foreach (ManagementBaseObject managementBaseObject in managementObjectSearcher.Get())
					{
						if ((uint)managementBaseObject["HorizontalResolution"] > num)
						{
							num = (uint)managementBaseObject["HorizontalResolution"];
						}
						if ((uint)managementBaseObject["VerticalResolution"] > num2)
						{
							num2 = (uint)managementBaseObject["VerticalResolution"];
						}
					}
				}
				result = new Size(Convert.ToInt32(num), Convert.ToInt32(num2));
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
				result = Size.Empty;
			}
			return result;
		}

		// Token: 0x060010A6 RID: 4262 RVA: 0x00072C9C File Offset: 0x00070E9C
		public static List<KeyValuePair<string, DateTime>> smethod_6()
		{
			List<KeyValuePair<string, DateTime>> list = null;
			DirectoryInfo directoryInfo = new DirectoryInfo(TApp.string_8);
			if (directoryInfo.Exists)
			{
				DirectoryInfo[] directories = directoryInfo.GetDirectories();
				if (directories != null && directories.Any<DirectoryInfo>())
				{
					list = new List<KeyValuePair<string, DateTime>>();
					foreach (DirectoryInfo directoryInfo2 in directories)
					{
						FileInfo[] files = directoryInfo2.GetFiles();
						if (files.Count<FileInfo>() > 0)
						{
							DateTime value = files.Max(new Func<FileInfo, DateTime>(TApp.<>c.<>9.method_0));
							KeyValuePair<string, DateTime> item = new KeyValuePair<string, DateTime>(directoryInfo2.Name, value);
							list.Add(item);
						}
					}
				}
			}
			return list;
		}

		// Token: 0x060010A7 RID: 4263 RVA: 0x00072D50 File Offset: 0x00070F50
		public static Image smethod_7(Image image_0, Size size_0)
		{
			int width = image_0.Width;
			int height = image_0.Height;
			float val = (float)size_0.Width / (float)width;
			float val2 = (float)size_0.Height / (float)height;
			float num = Math.Min(val, val2);
			int width2 = (int)((float)width * num);
			int height2 = (int)((float)height * num);
			Bitmap bitmap = new Bitmap(width2, height2);
			Graphics graphics = Graphics.FromImage(bitmap);
			graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
			graphics.DrawImage(image_0, 0, 0, width2, height2);
			graphics.Dispose();
			return bitmap;
		}

		// Token: 0x060010A8 RID: 4264 RVA: 0x00072DD4 File Offset: 0x00070FD4
		public static bool smethod_8()
		{
			bool result;
			try
			{
				using (ManagementObjectSearcher managementObjectSearcher = new ManagementObjectSearcher("SELECT * from Win32_ComputerSystem \r\n                                    WHERE (Manufacturer LIKE '%microsoft corporation%' AND Model LIKE '%virtual%')\r\n                                    OR Manufacturer LIKE '%vmware%'\r\n                                    OR (Manufacturer LIKE '%Parallels%' OR Manufacturer LIKE '%parallels%')\r\n                                    OR Model LIKE '%VirtualBox%'"))
				{
					using (ManagementObjectCollection managementObjectCollection = managementObjectSearcher.Get())
					{
						if (managementObjectCollection.Count > 0)
						{
							result = true;
							goto IL_40;
						}
					}
				}
			}
			catch
			{
			}
			return false;
			IL_40:
			return result;
		}

		// Token: 0x1700026A RID: 618
		// (get) Token: 0x060010A9 RID: 4265 RVA: 0x00072E50 File Offset: 0x00071050
		// (set) Token: 0x060010AA RID: 4266 RVA: 0x00007243 File Offset: 0x00005443
		public static string HOST { get; private set; }

		// Token: 0x1700026B RID: 619
		// (get) Token: 0x060010AB RID: 4267 RVA: 0x00072E68 File Offset: 0x00071068
		public static string FULLHOST
		{
			get
			{
				return "http://" + TApp.HOST;
			}
		}

		// Token: 0x1700026C RID: 620
		// (get) Token: 0x060010AC RID: 4268 RVA: 0x00072E88 File Offset: 0x00071088
		// (set) Token: 0x060010AD RID: 4269 RVA: 0x0000724D File Offset: 0x0000544D
		public static SrvParams SrvParams
		{
			get
			{
				return TApp.srvParams_0;
			}
			set
			{
				TApp.srvParams_0 = value;
			}
		}

		// Token: 0x1700026D RID: 621
		// (get) Token: 0x060010AE RID: 4270 RVA: 0x00072EA0 File Offset: 0x000710A0
		public static string App
		{
			get
			{
				return "TEx";
			}
		}

		// Token: 0x1700026E RID: 622
		// (get) Token: 0x060010AF RID: 4271 RVA: 0x00072EB8 File Offset: 0x000710B8
		public static string AppName
		{
			get
			{
				return "交易练习者";
			}
		}

		// Token: 0x1700026F RID: 623
		// (get) Token: 0x060010B0 RID: 4272 RVA: 0x00072ED0 File Offset: 0x000710D0
		// (set) Token: 0x060010B1 RID: 4273 RVA: 0x00007257 File Offset: 0x00005457
		public static string UserName
		{
			get
			{
				return TApp.string_15;
			}
			set
			{
				TApp.string_15 = value;
			}
		}

		// Token: 0x17000270 RID: 624
		// (get) Token: 0x060010B2 RID: 4274 RVA: 0x00072EE8 File Offset: 0x000710E8
		public static string Ver
		{
			get
			{
				if (string.IsNullOrEmpty(TApp.string_16))
				{
					TApp.string_16 = TApp.TExVersion.ToString();
				}
				return TApp.string_16;
			}
		}

		// Token: 0x17000271 RID: 625
		// (get) Token: 0x060010B3 RID: 4275 RVA: 0x00072F1C File Offset: 0x0007111C
		public static Version TExVersion
		{
			get
			{
				if (TApp.version_0 == null)
				{
					TApp.version_0 = Assembly.GetExecutingAssembly().GetName().Version;
				}
				return TApp.version_0;
			}
		}

		// Token: 0x17000272 RID: 626
		// (get) Token: 0x060010B4 RID: 4276 RVA: 0x00072F54 File Offset: 0x00071154
		// (set) Token: 0x060010B5 RID: 4277 RVA: 0x00007261 File Offset: 0x00005461
		public static string LoginCode
		{
			get
			{
				return TApp.string_17;
			}
			set
			{
				TApp.string_17 = value;
			}
		}

		// Token: 0x17000273 RID: 627
		// (get) Token: 0x060010B6 RID: 4278 RVA: 0x00072F6C File Offset: 0x0007116C
		// (set) Token: 0x060010B7 RID: 4279 RVA: 0x0000726B File Offset: 0x0000546B
		public static string OS
		{
			get
			{
				return TApp.string_18;
			}
			set
			{
				TApp.string_18 = value;
			}
		}

		// Token: 0x17000274 RID: 628
		// (get) Token: 0x060010B8 RID: 4280 RVA: 0x00072F84 File Offset: 0x00071184
		// (set) Token: 0x060010B9 RID: 4281 RVA: 0x00007275 File Offset: 0x00005475
		public static bool StartedUp
		{
			get
			{
				return TApp.bool_0;
			}
			set
			{
				TApp.bool_0 = value;
			}
		}

		// Token: 0x17000275 RID: 629
		// (get) Token: 0x060010BA RID: 4282 RVA: 0x00072F9C File Offset: 0x0007119C
		// (set) Token: 0x060010BB RID: 4283 RVA: 0x0000727F File Offset: 0x0000547F
		public static bool EnteredMainForm
		{
			get
			{
				return TApp.bool_1;
			}
			set
			{
				TApp.bool_1 = value;
			}
		}

		// Token: 0x17000276 RID: 630
		// (get) Token: 0x060010BC RID: 4284 RVA: 0x00072FB4 File Offset: 0x000711B4
		// (set) Token: 0x060010BD RID: 4285 RVA: 0x00007289 File Offset: 0x00005489
		public static bool IsLoggedIn
		{
			get
			{
				return TApp.bool_2;
			}
			set
			{
				TApp.bool_2 = value;
			}
		}

		// Token: 0x17000277 RID: 631
		// (get) Token: 0x060010BE RID: 4286 RVA: 0x00072FCC File Offset: 0x000711CC
		public static bool IsTrialUser
		{
			get
			{
				return TApp.smethod_1(TApp.SrvParams.TExPkg);
			}
		}

		// Token: 0x17000278 RID: 632
		// (get) Token: 0x060010BF RID: 4287 RVA: 0x00072FEC File Offset: 0x000711EC
		public static bool IsStVer
		{
			get
			{
				bool result;
				if (TApp.SrvParams.TExPkg != null)
				{
					if (TApp.SrvParams.TExPkg.Value != TExPackage.SVIP_St && TApp.SrvParams.TExPkg.Value != TExPackage.SVIP_StFt)
					{
						result = (TApp.SrvParams.TExPkg.Value == TExPackage.LY_SVIP);
					}
					else
					{
						result = true;
					}
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x17000279 RID: 633
		// (get) Token: 0x060010C0 RID: 4288 RVA: 0x0007305C File Offset: 0x0007125C
		public static bool IsFtVer
		{
			get
			{
				bool result;
				if (!TApp.IsTrialUser && TApp.SrvParams.TExPkg != null && TApp.SrvParams.TExPkg.Value != TExPackage.SVIP_St)
				{
					result = (TApp.SrvParams.TExPkg.Value != TExPackage.LY_SVIP);
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x1700027A RID: 634
		// (get) Token: 0x060010C1 RID: 4289 RVA: 0x000730BC File Offset: 0x000712BC
		public static bool IsStIncluded
		{
			get
			{
				bool result;
				if (!TApp.IsTrialUser)
				{
					result = TApp.IsStVer;
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x1700027B RID: 635
		// (get) Token: 0x060010C2 RID: 4290 RVA: 0x000730E0 File Offset: 0x000712E0
		public static bool IsFtIncluded
		{
			get
			{
				bool result;
				if (!TApp.IsTrialUser)
				{
					result = TApp.IsFtVer;
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x1700027C RID: 636
		// (get) Token: 0x060010C3 RID: 4291 RVA: 0x00073104 File Offset: 0x00071304
		// (set) Token: 0x060010C4 RID: 4292 RVA: 0x00007293 File Offset: 0x00005493
		public static List<SrvParam> ReqSyncFileSpms
		{
			get
			{
				return TApp.list_0;
			}
			set
			{
				TApp.list_0 = value;
			}
		}

		// Token: 0x1700027D RID: 637
		// (get) Token: 0x060010C5 RID: 4293 RVA: 0x0007311C File Offset: 0x0007131C
		public static string UserAcctFolder
		{
			get
			{
				if (string.IsNullOrEmpty(TApp.UserName))
				{
					throw new Exception("UserName == null!");
				}
				return TApp.string_8 + TApp.UserName;
			}
		}

		// Token: 0x1700027E RID: 638
		// (get) Token: 0x060010C6 RID: 4294 RVA: 0x00073154 File Offset: 0x00071354
		// (set) Token: 0x060010C7 RID: 4295 RVA: 0x0000729D File Offset: 0x0000549D
		public static bool StaticSrvAvailable
		{
			get
			{
				bool result;
				if (TApp.SrvParams != null && !string.IsNullOrEmpty(TApp.SrvParams.StaticSrvAddr))
				{
					result = TApp.bool_3;
				}
				else
				{
					result = false;
				}
				return result;
			}
			set
			{
				TApp.bool_3 = value;
			}
		}

		// Token: 0x1700027F RID: 639
		// (get) Token: 0x060010C8 RID: 4296 RVA: 0x00073188 File Offset: 0x00071388
		public static double DpiScale
		{
			get
			{
				if (TApp.double_1 == 0.0)
				{
					try
					{
						TApp.double_1 = Utility.GetDpiScale((IntPtr)0);
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
						TApp.double_1 = 1.0;
					}
				}
				return TApp.double_1;
			}
		}

		// Token: 0x060010C9 RID: 4297 RVA: 0x000731E4 File Offset: 0x000713E4
		public static string smethod_9()
		{
			Size size = TApp.smethod_5();
			int num = 0;
			int num2 = 0;
			try
			{
				Rectangle bounds = Screen.PrimaryScreen.Bounds;
				num = bounds.Width;
				num2 = bounds.Height;
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
			return string.Concat(new string[]
			{
				string.Format("HighDpiScreen: {0}", TApp.IsHighDpiScreen),
				Environment.NewLine,
				string.Format("Screen Size: {0} x {1}", num, num2),
				Environment.NewLine,
				string.Format("Window Size: {0} x {1}", TApp.WindowWidth, TApp.WindowHeight),
				Environment.NewLine,
				string.Format("Max WinSize: {0} x {1}", size.Width, size.Height),
				Environment.NewLine,
				"DpiScale: ",
				TApp.DpiScale.ToString("P0", new NumberFormatInfo
				{
					PercentPositivePattern = 1,
					PercentNegativePattern = 1
				}),
				Environment.NewLine,
				string.Format("Dpi: {0}", TApp.smethod_10()),
				Environment.NewLine,
				string.Format("InVirtualMachine: {0}", TApp.smethod_8()),
				Environment.NewLine,
				"OS: ",
				Utility.GetOSDesc(true, false)
			});
		}

		// Token: 0x060010CA RID: 4298
		[DllImport("user32.dll", ExactSpelling = true, SetLastError = true)]
		private static extern IntPtr GetDC(IntPtr intptr_0);

		// Token: 0x060010CB RID: 4299
		[DllImport("user32.dll", ExactSpelling = true)]
		private static extern IntPtr ReleaseDC(IntPtr intptr_0, IntPtr intptr_1);

		// Token: 0x060010CC RID: 4300
		[DllImport("user32.dll")]
		private static extern IntPtr GetDesktopWindow();

		// Token: 0x060010CD RID: 4301 RVA: 0x00073368 File Offset: 0x00071568
		private static float smethod_10()
		{
			float result = 96f;
			try
			{
				IntPtr desktopWindow = TApp.GetDesktopWindow();
				IntPtr dc = TApp.GetDC(desktopWindow);
				using (Graphics graphics = Graphics.FromHdc(dc))
				{
					result = graphics.DpiX;
				}
				TApp.ReleaseDC(desktopWindow, dc);
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
			return result;
		}

		// Token: 0x060010CE RID: 4302 RVA: 0x000733D8 File Offset: 0x000715D8
		private static float smethod_11()
		{
			float num = 1f;
			if (!TApp.IsHighDpiScreen)
			{
				float num2 = 0.05f;
				if (TApp.ComputerType == ComputerType.Laptop)
				{
					num2 = 0.07f;
				}
				if (TApp.DpiScale > 1.25)
				{
					num += Convert.ToSingle((TApp.DpiScale - 1.0) / 0.25 * (double)num2);
				}
				int num3 = 1920;
				if (TApp.ComputerType == ComputerType.Laptop)
				{
					num3 = 1440;
				}
				if (TApp.WindowWidth > num3)
				{
					num += (float)((TApp.WindowWidth - num3) / 500) * num2;
				}
				if (num > 1.35f)
				{
					num = 1.35f;
				}
			}
			return num;
		}

		// Token: 0x17000280 RID: 640
		// (get) Token: 0x060010CF RID: 4303 RVA: 0x00073480 File Offset: 0x00071680
		public static float DpiScaleMulti
		{
			get
			{
				if (TApp.float_0 == 0f)
				{
					TApp.float_0 = TApp.smethod_11();
				}
				return TApp.float_0;
			}
		}

		// Token: 0x17000281 RID: 641
		// (get) Token: 0x060010D0 RID: 4304 RVA: 0x000734AC File Offset: 0x000716AC
		public static int WindowWidth
		{
			get
			{
				if (TApp.int_0 == 0)
				{
					try
					{
						if (TApp.smethod_8())
						{
							TApp.int_0 = Convert.ToInt32(Math.Round((double)TApp.CurrentScreen.Bounds.Width * TApp.DpiScale));
						}
						else
						{
							Size size = TApp.smethod_5();
							if (!size.IsEmpty)
							{
								TApp.int_0 = size.Width;
							}
							else
							{
								TApp.int_0 = Convert.ToInt32(Math.Round((double)TApp.CurrentScreen.Bounds.Width * TApp.DpiScale));
							}
						}
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
				return TApp.int_0;
			}
		}

		// Token: 0x17000282 RID: 642
		// (get) Token: 0x060010D1 RID: 4305 RVA: 0x00073558 File Offset: 0x00071758
		public static int WindowHeight
		{
			get
			{
				if (TApp.int_1 == 0)
				{
					try
					{
						if (TApp.smethod_8())
						{
							TApp.int_1 = Convert.ToInt32(Math.Round((double)TApp.CurrentScreen.Bounds.Height * TApp.DpiScale));
						}
						else
						{
							Size size = TApp.smethod_5();
							if (!size.IsEmpty)
							{
								TApp.int_1 = size.Height;
							}
							else
							{
								TApp.int_1 = Convert.ToInt32(Math.Round((double)TApp.CurrentScreen.Bounds.Height * TApp.DpiScale));
							}
						}
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
				return TApp.int_1;
			}
		}

		// Token: 0x17000283 RID: 643
		// (get) Token: 0x060010D2 RID: 4306 RVA: 0x00073604 File Offset: 0x00071804
		public static bool IsHighDpiScreen
		{
			get
			{
				bool result;
				if (TApp.DpiScale < 2.0 && TApp.WindowWidth <= 3440 && TApp.WindowHeight <= 1600 && (TApp.DpiScale != 1.0 || TApp.WindowWidth < 2560))
				{
					result = false;
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x17000284 RID: 644
		// (get) Token: 0x060010D3 RID: 4307 RVA: 0x00073660 File Offset: 0x00071860
		// (set) Token: 0x060010D4 RID: 4308 RVA: 0x000072A7 File Offset: 0x000054A7
		public static Screen CurrentScreen
		{
			get
			{
				if (TApp.screen_0 == null)
				{
					TApp.screen_0 = Screen.PrimaryScreen;
				}
				return TApp.screen_0;
			}
			set
			{
				TApp.screen_0 = value;
				TApp.int_1 = 0;
				TApp.int_0 = 0;
			}
		}

		// Token: 0x17000285 RID: 645
		// (get) Token: 0x060010D5 RID: 4309 RVA: 0x00073688 File Offset: 0x00071888
		public static float DefaultScaledFontSize
		{
			get
			{
				return TApp.smethod_4(9f, false);
			}
		}

		// Token: 0x17000286 RID: 646
		// (get) Token: 0x060010D6 RID: 4310 RVA: 0x000736A4 File Offset: 0x000718A4
		// (set) Token: 0x060010D7 RID: 4311 RVA: 0x000072BD File Offset: 0x000054BD
		public static bool IsFirstRun { get; set; }

		// Token: 0x17000287 RID: 647
		// (get) Token: 0x060010D8 RID: 4312 RVA: 0x000736BC File Offset: 0x000718BC
		public static ComputerType ComputerType
		{
			get
			{
				if (TApp.computerType_0 == ComputerType.Unknown)
				{
					string computerType = Utility.GetComputerType();
					if (computerType == "Laptop")
					{
						TApp.computerType_0 = ComputerType.Laptop;
					}
					else if (computerType == "Desktop")
					{
						TApp.computerType_0 = ComputerType.Desktop;
					}
					else
					{
						TApp.computerType_0 = ComputerType.Unknown;
					}
				}
				return TApp.computerType_0;
			}
		}

		// Token: 0x040008DD RID: 2269
		public static string string_0;

		// Token: 0x040008DE RID: 2270
		public static string string_1;

		// Token: 0x040008DF RID: 2271
		private static readonly string[] string_2 = new string[]
		{
			"srv.tradingexer.com:8088",
			"srv.zsinvt.com:8088"
		};

		// Token: 0x040008E0 RID: 2272
		public static readonly string string_3 = "www.tradingexer.com:80";

		// Token: 0x040008E1 RID: 2273
		public static readonly string string_4 = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);

		// Token: 0x040008E2 RID: 2274
		public static readonly string string_5 = "TradingExer";

		// Token: 0x040008E3 RID: 2275
		public static readonly string string_6 = TApp.string_4 + "\\" + TApp.string_5;

		// Token: 0x040008E4 RID: 2276
		public static readonly string string_7 = TApp.string_6 + "\\Data\\";

		// Token: 0x040008E5 RID: 2277
		public static readonly string string_8 = TApp.string_6 + "\\Acct\\";

		// Token: 0x040008E6 RID: 2278
		public static readonly string string_9 = TApp.string_8 + "Trading\\";

		// Token: 0x040008E7 RID: 2279
		public static readonly string string_10 = TApp.string_6 + "\\Temp";

		// Token: 0x040008E8 RID: 2280
		public static readonly string string_11 = "HD";

		// Token: 0x040008E9 RID: 2281
		public static readonly string string_12 = "tex^";

		// Token: 0x040008EA RID: 2282
		public static double double_0 = 4.6;

		// Token: 0x040008EB RID: 2283
		public static readonly string string_13 = "%HostAddr%";

		// Token: 0x040008EC RID: 2284
		private static Class16 class16_0 = new Class16(20000);

		// Token: 0x040008ED RID: 2285
		[CompilerGenerated]
		private static string string_14;

		// Token: 0x040008EE RID: 2286
		private static SrvParams srvParams_0;

		// Token: 0x040008EF RID: 2287
		private static string string_15;

		// Token: 0x040008F0 RID: 2288
		private static string string_16;

		// Token: 0x040008F1 RID: 2289
		private static Version version_0;

		// Token: 0x040008F2 RID: 2290
		private static string string_17;

		// Token: 0x040008F3 RID: 2291
		private static string string_18;

		// Token: 0x040008F4 RID: 2292
		private static bool bool_0;

		// Token: 0x040008F5 RID: 2293
		private static bool bool_1;

		// Token: 0x040008F6 RID: 2294
		private static bool bool_2;

		// Token: 0x040008F7 RID: 2295
		private static List<SrvParam> list_0;

		// Token: 0x040008F8 RID: 2296
		private static bool bool_3;

		// Token: 0x040008F9 RID: 2297
		private static double double_1;

		// Token: 0x040008FA RID: 2298
		private static float float_0;

		// Token: 0x040008FB RID: 2299
		private static int int_0;

		// Token: 0x040008FC RID: 2300
		private static int int_1;

		// Token: 0x040008FD RID: 2301
		private static Screen screen_0;

		// Token: 0x040008FE RID: 2302
		[CompilerGenerated]
		private static bool bool_4;

		// Token: 0x040008FF RID: 2303
		private static ComputerType computerType_0;
	}
}
