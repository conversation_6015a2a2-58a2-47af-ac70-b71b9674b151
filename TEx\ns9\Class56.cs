﻿using System;
using System.Drawing;
using System.Windows.Forms;
using ns11;
using ns12;
using ns23;
using ns28;
using TEx;

namespace ns9
{
	// Token: 0x0200029D RID: 669
	internal sealed class Class56 : Class48
	{
		// Token: 0x06001DA0 RID: 7584 RVA: 0x000C9350 File Offset: 0x000C7550
		public Class56(SplitterPanel splitterPanel_0, int int_4, int int_5, bool bool_6, bool bool_7) : base(splitterPanel_0, int_4, int_5, bool_7)
		{
			this.bool_3 = bool_6;
			ChtCtrl chtCtrl = (ChtCtrl)splitterPanel_0.Parent.Parent.Parent;
			if (Base.UI.Form.IsSwitchingChart)
			{
				chtCtrl = Base.UI.SelectedChtCtrl;
			}
			if (chtCtrl != null && chtCtrl.Parent != null && chtCtrl.Parent.Parent != null && chtCtrl.Parent.Parent is SplitContainer)
			{
				SplitContainer splitContainer = (SplitContainer)chtCtrl.Parent.Parent;
				SplitterPanel splitterPanel = (SplitterPanel)chtCtrl.Parent;
				if ((splitterPanel == splitContainer.Panel1 && splitContainer.Panel2Collapsed) || (splitterPanel == splitContainer.Panel2 && splitContainer.Panel1Collapsed))
				{
					this.bool_4 = true;
					base.method_0(Class372.window_restore_blue);
				}
				else
				{
					base.method_0(Class372.window_maximize_blue);
				}
			}
			else
			{
				this.bool_5 = true;
			}
		}

		// Token: 0x06001DA1 RID: 7585 RVA: 0x0000C6AD File Offset: 0x0000A8AD
		public Class56(SplitterPanel splitterPanel_0, int int_4, int int_5, bool bool_6) : this(splitterPanel_0, int_4, int_5, bool_6, true)
		{
		}

		// Token: 0x170004AC RID: 1196
		// (get) Token: 0x06001DA2 RID: 7586 RVA: 0x000C9430 File Offset: 0x000C7630
		public bool IsMaximized
		{
			get
			{
				return this.bool_4;
			}
		}

		// Token: 0x170004AD RID: 1197
		// (get) Token: 0x06001DA3 RID: 7587 RVA: 0x000C9448 File Offset: 0x000C7648
		public bool IsInSingleChartPanel
		{
			get
			{
				return this.bool_5;
			}
		}

		// Token: 0x06001DA4 RID: 7588 RVA: 0x000C9460 File Offset: 0x000C7660
		protected override void Class48_MouseEnter(object sender, EventArgs e)
		{
			base.Class48_MouseEnter(sender, e);
			if (!this.bool_5)
			{
				if (!this.bool_4)
				{
					base.Image = (Image)Class348.Resources.GetObject("window_maximize_red");
				}
				else
				{
					base.Image = (Image)Class348.Resources.GetObject("window_restore_red");
				}
			}
		}

		// Token: 0x06001DA5 RID: 7589 RVA: 0x0000C6BB File Offset: 0x0000A8BB
		protected override void Class48_MouseLeave(object sender, EventArgs e)
		{
			base.Class48_MouseLeave(sender, e);
			if (!this.bool_5)
			{
				if (!this.bool_4)
				{
					base.Image = Class372.window_maximize_blue;
				}
				else
				{
					base.Image = Class372.window_restore_blue;
				}
			}
		}

		// Token: 0x06001DA6 RID: 7590 RVA: 0x000C94C0 File Offset: 0x000C76C0
		protected override void Class48_Click(object sender, EventArgs e)
		{
			if (!this.bool_5)
			{
				SplitterPanel splitterPanel = (SplitterPanel)base.Parent;
				if (this.bool_3)
				{
					this.method_1(splitterPanel);
				}
				SplitContainer splitContainer = (SplitContainer)splitterPanel.Parent;
				while (splitContainer.Parent.GetType() == typeof(SplitterPanel))
				{
					splitterPanel = (SplitterPanel)splitContainer.Parent;
					this.method_1(splitterPanel);
					splitContainer = (SplitContainer)splitterPanel.Parent;
				}
				this.bool_4 = !this.bool_4;
				this.Class48_MouseLeave(this, new EventArgs());
			}
		}

		// Token: 0x06001DA7 RID: 7591 RVA: 0x000C9554 File Offset: 0x000C7754
		private void method_1(SplitterPanel splitterPanel_0)
		{
			SplitContainer splitContainer = (SplitContainer)splitterPanel_0.Parent;
			Base.UI.MainForm.SuspendLayout();
			Base.UI.MainForm.smethod_0();
			if (splitterPanel_0 == splitContainer.Panel1)
			{
				if (this.bool_4)
				{
					if (splitContainer.Tag == null || (splitContainer.Tag != null && splitContainer.Tag.ToString() != "ParentSpC"))
					{
						if (splitContainer is ChtCtrl)
						{
							if (((ChtCtrl)splitContainer).IfShowTickPanel)
							{
								splitContainer.Panel2Collapsed = !this.bool_4;
							}
						}
						else
						{
							splitContainer.Panel2Collapsed = !this.bool_4;
						}
					}
				}
				else
				{
					splitContainer.Panel2Collapsed = !this.bool_4;
				}
			}
			else
			{
				splitContainer.Panel1Collapsed = !this.bool_4;
			}
			Base.UI.MainForm.ResumeLayout();
			Base.UI.MainForm.smethod_1();
		}

		// Token: 0x04000E8F RID: 3727
		private bool bool_3;

		// Token: 0x04000E90 RID: 3728
		private bool bool_4;

		// Token: 0x04000E91 RID: 3729
		private bool bool_5;
	}
}
