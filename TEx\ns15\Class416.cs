﻿using System;
using ns14;
using ns16;
using ns30;
using ns9;
using TEx.Inds;
using TEx.SIndicator;

namespace ns15
{
	// Token: 0x02000316 RID: 790
	internal sealed class Class416 : Class412
	{
		// Token: 0x060021F9 RID: 8697 RVA: 0x0000D8A3 File Offset: 0x0000BAA3
		public Class416(HToken htoken_1, Class408 class408_2, Class408 class408_3) : base(htoken_1, class408_2, class408_3)
		{
		}

		// Token: 0x060021FA RID: 8698 RVA: 0x000E8878 File Offset: 0x000E6A78
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			if (this.Token.Symbol.HSymbolType != Enum26.const_12 && this.Token.Symbol.HSymbolType != Enum26.const_14 && this.Token.Symbol.HSymbolType != Enum26.const_15 && this.Token.Symbol.HSymbolType != Enum26.const_16 && this.Token.Symbol.HSymbolType != Enum26.const_20)
			{
				if (this.Token.Symbol.HSymbolType != Enum26.const_13)
				{
					throw new Exception(this.Token.method_0("不是逻辑符号。"));
				}
			}
			object object_ = this.Left.vmethod_1(parserEnvironment_0);
			object object_2 = this.Right.vmethod_1(parserEnvironment_0);
			return this.vmethod_2(object_, object_2);
		}

		// Token: 0x060021FB RID: 8699 RVA: 0x000E893C File Offset: 0x000E6B3C
		protected override double vmethod_3(double double_0, double double_1)
		{
			switch (this.Token.Symbol.HSymbolType)
			{
			case Enum26.const_12:
				if (double_0 > double_1)
				{
					return 1.0;
				}
				return 0.0;
			case Enum26.const_13:
				if (double_0 != double_1)
				{
					return 1.0;
				}
				return 0.0;
			case Enum26.const_14:
				if (double_0 >= double_1)
				{
					return 1.0;
				}
				return 0.0;
			case Enum26.const_15:
				if (double_0 < double_1)
				{
					return 1.0;
				}
				return 0.0;
			case Enum26.const_16:
				if (double_0 <= double_1)
				{
					return 1.0;
				}
				return 0.0;
			case Enum26.const_20:
				if (double_0 == double_1)
				{
					return 1.0;
				}
				return 0.0;
			}
			throw new Exception(this.Token.method_0("不是逻辑符号"));
		}

		// Token: 0x060021FC RID: 8700 RVA: 0x000E8A58 File Offset: 0x000E6C58
		protected override DataArray vmethod_4(DataArray dataArray_0, DataArray dataArray_1)
		{
			switch (this.Token.Symbol.HSymbolType)
			{
			case Enum26.const_12:
				return dataArray_0 > dataArray_1;
			case Enum26.const_13:
				return dataArray_0 != dataArray_1;
			case Enum26.const_14:
				return dataArray_0 >= dataArray_1;
			case Enum26.const_15:
				return dataArray_0 < dataArray_1;
			case Enum26.const_16:
				return dataArray_0 <= dataArray_1;
			case Enum26.const_20:
				return dataArray_0 == dataArray_1;
			}
			throw new Exception(this.Token.method_0("不是逻辑符号"));
		}

		// Token: 0x060021FD RID: 8701 RVA: 0x000E8AF8 File Offset: 0x000E6CF8
		public static Class408 smethod_0(Tokenes tokenes_0)
		{
			Class408 @class = Class414.smethod_0(tokenes_0);
			tokenes_0.method_1();
			HToken htoken = tokenes_0.Current;
			while (htoken.Symbol.HSymbolType == Enum26.const_12 || htoken.Symbol.HSymbolType == Enum26.const_14 || htoken.Symbol.HSymbolType == Enum26.const_15 || htoken.Symbol.HSymbolType == Enum26.const_16 || htoken.Symbol.HSymbolType == Enum26.const_20 || htoken.Symbol.HSymbolType == Enum26.const_13)
			{
				tokenes_0.method_1();
				Class408 class408_ = Class414.smethod_0(tokenes_0);
				@class = new Class416(htoken, @class, class408_);
				tokenes_0.method_1();
				htoken = tokenes_0.Current;
			}
			tokenes_0.method_2();
			return @class;
		}
	}
}
