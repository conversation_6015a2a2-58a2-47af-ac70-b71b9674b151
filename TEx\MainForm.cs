﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Media;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Timers;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using DevComponents.DotNetBar.Metro;
using DevComponents.DotNetBar.Metro.ColorTables;
using NAppUpdate.Framework;
using NAppUpdate.Framework.Common;
using NAppUpdate.Framework.Sources;
using ns11;
using ns12;
using ns13;
using ns14;
using ns15;
using ns16;
using ns17;
using ns18;
using ns19;
using ns20;
using ns21;
using ns22;
using ns23;
using ns24;
using ns25;
using ns26;
using ns27;
using ns28;
using ns29;
using ns3;
using ns31;
using ns32;
using ns33;
using ns4;
using ns5;
using ns6;
using ns7;
using ns9;
using TEx.Comn;
using TEx.ImportTrans;
using TEx.SIndicator;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x0200027E RID: 638
	internal sealed partial class MainForm : MetroForm
	{
		// Token: 0x06001BA0 RID: 7072 RVA: 0x000B9D7C File Offset: 0x000B7F7C
		public MainForm()
		{
			if (!TApp.IsHighDpiScreen)
			{
				base.AutoScaleMode = AutoScaleMode.Dpi;
			}
			this.method_9();
			this.InitializeComponent();
			this.dotNetBarManager_0.ShowCustomizeContextMenu = false;
			this.toolStrip_Trading.ColorScheme = new ColorScheme(eDotNetBarStyle.Metro);
			this.toolStrip_Ctrls.ColorScheme = new ColorScheme(eDotNetBarStyle.Metro);
			this.toolStrip_Periods.ColorScheme = new ColorScheme(eDotNetBarStyle.Metro);
			this.toolStrip_Setting.ColorScheme = new ColorScheme(eDotNetBarStyle.Metro);
			this.toolStrip_ExitCreateNewPg.ColorScheme = new ColorScheme(eDotNetBarStyle.Metro);
			this.method_10();
			this.method_3();
			base.Icon = Class372.TExIcoBlue;
			base.ShowIcon = false;
			if (TApp.IsFirstRun)
			{
				PageSelWnd pageSelWnd = new PageSelWnd();
				pageSelWnd.PageSelected += this.method_7;
				pageSelWnd.ShowDialog();
			}
			this.bar_AcctTrans.Enabled = false;
			this.bar_AcctTrans.Visible = false;
			this.numericUpDown_Price.InterceptMouseWheel = Class302.Enum21.const_1;
			this.numericUpDown_Units.InterceptMouseWheel = Class302.Enum21.const_1;
			base.KeyPreview = true;
			base.Activated += this.MainForm_Activated;
			base.Deactivate += this.MainForm_Deactivate;
			base.FormClosing += this.MainForm_FormClosing;
			base.Load += this.MainForm_Load;
			base.Shown += this.MainForm_Shown;
			base.KeyDown += this.MainForm_KeyDown;
			base.KeyPress += this.MainForm_KeyPress;
			base.ResizeBegin += this.MainForm_ResizeBegin;
			base.ResizeEnd += this.MainForm_ResizeEnd;
			base.Resize += this.MainForm_Resize;
			base.MouseWheel += this.MainForm_MouseWheel;
			this.toolStrip_Ctrls.ThemeAware = false;
			this.toolStripBtnItem_Start.PopupOpen += this.toolStripBtnItem_Start_PopupOpen;
			this.toolStripDropDownButton_Settings.PopupOpen += new DotNetBarManager.PopupOpenEventHandler(this.toolStripDropDownButton_Settings_PopupOpen);
			this.btnItem_交易账户.PopupOpen += new DotNetBarManager.PopupOpenEventHandler(this.btnItem_交易账户_PopupOpen);
			this.toolStripDropDownButton_Pages.PopupOpen += new DotNetBarManager.PopupOpenEventHandler(this.toolStripDropDownButton_Pages_PopupOpen);
			this.toolStripDropDownButton_Accts.PopupOpen += new DotNetBarManager.PopupOpenEventHandler(this.toolStripDropDownButton_Accts_PopupOpen);
			this.toolStripDropDownButton_Symbls.PopupOpen += new DotNetBarManager.PopupOpenEventHandler(this.toolStripDropDownButton_Symbls_PopupOpen);
			this.toolStripBttn_Long.Click += this.toolStripBttn_Long_Click;
			this.toolStripBttn_ClsLong.Click += this.toolStripBttn_ClsLong_Click;
			this.toolStripBttn_Short.Click += this.toolStripBttn_Short_Click;
			this.toolStripBttn_ClsShort.Click += this.toolStripBttn_ClsShort_Click;
			this.新建账户ToolStripMenuItem.Click += this.新建账户ToolStripMenuItem_Click;
			this.toolStripBtnItem_SelectDate.Click += this.toolStripBtnItem_SelectDate_Click;
			this.btnItem_页面设置.PopupOpen += new DotNetBarManager.PopupOpenEventHandler(this.btnItem_页面设置_PopupOpen);
			this.新建页面ToolStripMenuItem.Click += this.新建页面ToolStripMenuItem_Click;
			this.保存页面ToolStripMenuItem.Click += this.保存页面ToolStripMenuItem_Click;
			this.另存页面ToolStripMenuItem.Click += this.另存页面ToolStripMenuItem_Click;
			this.自动保存ToolStripMenuItem.Click += this.自动保存ToolStripMenuItem_Click;
			this.btnItem_界面风格.PopupOpen += new DotNetBarManager.PopupOpenEventHandler(this.btnItem_界面风格_PopupOpen);
			this.黑红经典ToolStripMenuItem.Click += this.黑红经典ToolStripMenuItem_Click;
			this.绿白现代ToolStripMenuItem.Click += this.绿白现代ToolStripMenuItem_Click;
			this.btnItem_画线工具.Click += this.btnItem_画线工具_Click;
			this.btnItem_数据管理.Click += this.btnItem_数据管理_Click;
			this.toolStripMenuItem_BuySoft.Click += this.toolStripMenuItem_BuySoft_Click;
			this.toolStripMenuItem_VerInfo.Click += this.toolStripMenuItem_VerInfo_Click;
			this.toolStripBtnItem_Start.Click += this.toolStripBtnItem_Start_Click;
			this.toolStripBtn_Tick.Click += this.toolStripBtn_Tick_Click;
			this.toolStripBttn_1m.Click += this.toolStripBttn_1m_Click;
			this.toolStripBttn_3m.Click += this.toolStripBttn_3m_Click;
			this.toolStripBttn_5m.Click += this.toolStripBttn_5m_Click;
			this.toolStripBttn_10m.Click += this.toolStripBttn_10m_Click;
			this.toolStripBttn_15m.Click += this.toolStripBttn_15m_Click;
			this.toolStripBttn_30m.Click += this.toolStripBttn_30m_Click;
			this.toolStripBttn_1h.Click += this.toolStripBttn_1h_Click;
			this.toolStripBttn_2h.Click += this.toolStripBttn_2h_Click;
			this.toolStripBttn_4h.Click += this.toolStripBttn_4h_Click;
			this.toolStripBttn_1d.Click += this.toolStripBttn_1d_Click;
			this.toolStripBttn_1w.Click += this.toolStripBttn_1w_Click;
			this.toolStripBttn_1Mth.Click += this.toolStripBttn_1Mth_Click;
			this.toolStripButton_ExitCrtNewPg.Click += this.toolStripButton_ExitCrtNewPg_Click;
			this.timer_1.Tick += this.timer_1_Tick;
			this.controlContainerItem_Units.MouseEnter += this.controlContainerItem_Units_MouseEnter;
			this.controlContainerItem_Units.MouseLeave += this.controlContainerItem_Units_MouseLeave;
			this.numericUpDown_Price.ValueChanged += this.numericUpDown_Price_ValueChanged;
			this.numericUpDown_Price.BeforeValueDecrement += this.method_156;
			this.numericUpDown_Price.Click += this.numericUpDown_Price_Click;
			this.btnItem_热键定义.Click += this.btnItem_热键定义_Click;
			this.btnItem_画线下单.Click += this.btnItem_画线下单_Click;
			this.btnItem_指标管理.Click += this.btnItem_指标管理_Click;
			this.toolStripBttn_Long.Tooltip = " ";
			this.toolStripBttn_Long.ToolTipVisibleChanged += this.toolStripBttn_Long_ToolTipVisibleChanged;
			this.toolStripBttn_Short.Tooltip = " ";
			this.toolStripBttn_Short.ToolTipVisibleChanged += this.toolStripBttn_Short_ToolTipVisibleChanged;
			this.toolStripBttn_ClsLong.Tooltip = " ";
			this.toolStripBttn_ClsLong.ToolTipVisibleChanged += this.toolStripBttn_ClsLong_ToolTipVisibleChanged;
			this.toolStripBttn_ClsShort.Tooltip = " ";
			this.toolStripBttn_ClsShort.ToolTipVisibleChanged += this.toolStripBttn_ClsShort_ToolTipVisibleChanged;
		}

		// Token: 0x06001BA1 RID: 7073 RVA: 0x0000B670 File Offset: 0x00009870
		private void MainForm_Load(object sender, EventArgs e)
		{
			this.bool_2 = false;
			this.method_0();
		}

		// Token: 0x06001BA2 RID: 7074 RVA: 0x000BA450 File Offset: 0x000B8650
		private void method_0()
		{
			this.method_1();
			base.Activate();
			Base.UI.smethod_178();
			this.timer_2 = new System.Timers.Timer();
			this.timer_2.Interval = 15000.0;
			this.timer_2.Elapsed += this.timer_2_Elapsed;
			this.timer_2.Start();
		}

		// Token: 0x06001BA3 RID: 7075 RVA: 0x000BA4B4 File Offset: 0x000B86B4
		private void method_1()
		{
			Base.Data.CurrSymblChanged += this.method_164;
			Base.Acct.AccountChanging += this.method_92;
			Base.Acct.AccountChanged += this.method_93;
			Base.Data.AppendingData += this.method_166;
			Base.Data.HisDataAppended += this.method_167;
			Base.Data.HDPS_1hChanged += this.method_168;
			Base.Data.GenHisDataSetStarted += this.method_169;
			Base.Data.GenHisDataSetCompleted += this.method_170;
			Base.Data.GenHisDataSetCanceled += this.method_171;
			Base.Data.NoAvailableDataDetected += this.method_172;
			Base.Data.DownloadDataError += this.method_173;
			Base.Data.RetrievingData += this.method_174;
			Base.Data.RetrieveDataCompleted += this.method_175;
			Base.Data.CurrHisDataSetUdpatedWithNewStartEndDate += this.method_179;
			Base.Data.DateSelectionChanged += this.method_161;
			Base.Acct.smethod_43();
			Base.UI.smethod_176("正在初始化窗体...");
			Base.UI.MainForm = this;
			Base.UI.ChartPageChanged += this.method_111;
			Base.UI.CurrPageSaved += this.method_112;
			Base.UI.SelectedChtCtrlChanged += this.method_113;
			Base.UI.NewSwitchChtCtrlAdded += this.method_39;
			Base.UI.NewSwitchAcctTabAdded += this.method_41;
			Base.UI.ViewSwitched += this.method_40;
			Base.UI.Form.StartedSetOn += this.method_114;
			Base.UI.Form.StartedSetOff += this.method_115;
			Base.UI.Form.StockShortSettingChanged += this.method_116;
			Base.UI.Form.AutoPlayUnitChanged += this.method_117;
			Base.UI.Form.IfShowAcctInfoOnTransTabHeaderChanged += this.method_118;
			Base.UI.Form.IfShowDayOfWeekChanged += this.method_119;
			Base.UI.Form.BlindTestModeOn += this.method_121;
			Base.UI.Form.BlindTestModeOff += this.method_120;
			Base.UI.DrawObjStartMoving += this.method_42;
			Base.UI.TransTabUpdated += this.method_43;
			Base.UI.SpanMovePrevReachedBegDT += this.method_38;
			Base.UI.CurrTradingSymbChanged += this.method_108;
			Base.UI.ZiXuanSymbAdded += this.method_109;
			Base.UI.ChartThemeChanged += this.method_8;
			this.method_2();
			Base.UI.smethod_176("正在初始化图表...");
			this.method_24(null);
			Base.Trading.TransCreated += this.method_136;
			Base.Trading.ShownHisTransAdded += new Delegate22(this.method_137);
			Base.Trading.OrderCreated += this.method_138;
			Base.Trading.OrderExcFailed += this.method_139;
			Base.Trading.OrderStatusUpdated += this.method_140;
			Base.Trading.CondOrderStatusUpdated += this.method_141;
			Base.Trading.CondOrderCreated += this.method_147;
			Base.Trading.AutoStopChanged += this.method_127;
			Base.Trading.AutoLimitChanged += this.method_131;
			Base.Trading.StSpltTransExecuting += this.method_148;
			Base.Trading.StSpltTransExecuted += this.method_149;
			Base.Trading.FreeMarginNotEnoughWhenChkPlaceMktOdr += this.method_150;
			Base.Trading.ExceedClosableTransWhenChkPlaceMktOdr += this.method_151;
			this.method_16();
			if (Base.Data.SymbDataSets != null)
			{
				foreach (SymbDataSet symbDataSet_ in Base.Data.SymbDataSets)
				{
					this.method_157(symbDataSet_);
				}
			}
			Base.Data.SymbDataSetAdded += this.method_159;
			Base.Data.SymbDataSetRemoved += this.method_160;
			this.method_199();
		}

		// Token: 0x06001BA4 RID: 7076 RVA: 0x000BA8CC File Offset: 0x000B8ACC
		private void MainForm_Shown(object sender, EventArgs e)
		{
			if (TApp.SrvParams.LogonNoticeInfo != null && !string.IsNullOrEmpty(TApp.SrvParams.LogonNoticeInfo.HTMLUrl))
			{
				if (Base.UI.Form.LastDisplayedLogonNoticeDT != null && !Base.UI.Form.IfShowSameLogonNoticeNextTime && !(Base.UI.Form.LastDisplayedLogonNoticeDT.Value != TApp.SrvParams.LogonNoticeInfo.DateTime))
				{
					this.method_6();
				}
				else
				{
					new MsgWindow(TApp.SrvParams.LogonNoticeInfo)
					{
						Owner = this,
						StartPosition = FormStartPosition.CenterParent
					}.ShowDialog();
				}
			}
			else
			{
				this.method_6();
			}
			TApp.EnteredMainForm = true;
			Base.UI.smethod_164();
			if (Base.UI.TransTabs != null)
			{
				Base.UI.TransTabs.method_48();
			}
			int id = Base.UI.CurrTradingSymbol.ID;
			SymbDataSet symbDataSet = Base.Data.smethod_49(Base.UI.CurrTradingSymbol.ID, false);
			if (symbDataSet.HasValidDataSet)
			{
				this.method_66(symbDataSet.CurrHisData);
			}
			else
			{
				Base.UI.CurrTradingSymbol = Base.UI.CurrSymbol;
				Class182.smethod_0(new Exception("No Valid SymblDataSet for Base.UI.CurrTradingSymbol(Id:" + id + ")"));
			}
			if (!TApp.IsTrialUser)
			{
				DateTime? endDate = TApp.SrvParams.UsrStkMetaList.First<UsrStkMeta>().EndDate;
				TimeSpan? timeSpan = DateTime.Now - endDate;
				TimeSpan t = new TimeSpan(1, 0, 0, 0);
				if (timeSpan > t && MessageBox.Show("数据更新服务已到期（截止时间：" + endDate.Value.ToShortDateString() + "），登陆交易练习者网站个人中心办理续费吗？", "请确认", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) == DialogResult.OK)
				{
					try
					{
						Process.Start(new ProcessStartInfo("https://www.tradingexer.com/account/center"));
					}
					catch
					{
					}
				}
			}
			InfoMineMgr.smethod_5(null);
			this.method_11(false);
			CfmmcRecImporter.StartDownloadRecs += this.method_99;
			CfmmcRecImporter.RecImportSuccess += this.method_98;
			CfmmcRecImporter.RecImportFailed += this.method_97;
			CfmmcRecImporter.NoRecNeedToDnldDetected += this.method_96;
			CfmmcRecImporter.NotifyDnRecIndex += this.method_95;
			CfmmcRecImporter.smethod_12();
			CfmmcRecImporter.IEMajor = Class480.smethod_2();
			if (Base.UI.Form.CfmmcAutoDnldConfig != null && Base.UI.Form.CfmmcAutoDnldConfig.AutoDownOnStartup)
			{
				CfmmcRecImporter.smethod_37();
			}
			Class46.smethod_3(Class22.AppStarted, string.Concat(new string[]
			{
				"CurrVer:",
				TApp.Ver,
				"  CurrPkg:",
				(TApp.SrvParams.TExPkg != null) ? TApp.SrvParams.TExPkg.Value.ToString() : "TRL",
				"  CurrAcct:",
				Base.Acct.CurrAccount.AcctName,
				"  ",
				Base.Data.smethod_127(),
				"  CurrPage:",
				Base.UI.Form.CurrentPageName,
				"  ",
				Base.UI.smethod_102()
			}));
		}

		// Token: 0x06001BA5 RID: 7077 RVA: 0x000BAC14 File Offset: 0x000B8E14
		private void method_2()
		{
			base.Invalidate();
			this.method_5();
			this.method_90();
			this.method_186(this.toolStripDropDownButton_Pages);
			this.method_187(this.toolStripDropDownButton_Accts, false);
			this.method_76(this.toolStripDropDownButton_Symbls);
			this.method_69();
			this.timer_0 = new System.Windows.Forms.Timer();
			this.timer_0.Interval = 30;
			this.timer_0.Tick += this.timer_0_Tick;
			this.toolStripDropDownButton_Pages.Text = Base.UI.Form.CurrentPageName;
			this.toolStripDropDownButton_Accts.Text = Base.Acct.CurrAccount.AcctName;
			this.toolStripDropDownButton_Accts.Tooltip = Base.Acct.CurrAccount.Notes;
			StkSymbol stkSymbol = Base.Data.CurrSelectedSymbol;
			if (stkSymbol == null)
			{
				stkSymbol = Base.UI.CurrSymbol;
			}
			this.toolStripDropDownButton_Symbls.Text = stkSymbol.Desc;
			this.toolStripDropDownButton_Symbls.Tooltip = stkSymbol.CNName;
			this.toolStripBtn_NPeriod.ExpandChange += this.toolStripBtn_NPeriod_ExpandChange;
			this.toolStripBtnItem_NMins.Click += this.toolStripBtnItem_NMins_Click;
			this.toolStripBtnItem_NHours.Click += this.toolStripBtnItem_NHours_Click;
			this.toolStripBtnItem_NDays.Click += this.toolStripBtnItem_NDays_Click;
			this.txtBoxItem_NMins.InputTextChanged += this.txtBoxItem_NMins_InputTextChanged;
			this.txtBoxItem_NHours.InputTextChanged += this.txtBoxItem_NHours_InputTextChanged;
			this.txtBoxItem_NDays.InputTextChanged += this.txtBoxItem_NDays_InputTextChanged;
			if (!TApp.IsTrialUser)
			{
				this.toolStripMenuItem_BuySoft.Visible = false;
			}
			Base.UI.smethod_46(this);
			if (Base.UI.Form.TimerSpeed >= 1)
			{
				this.slider_Speed.Value = Base.UI.Form.TimerSpeed;
			}
			else
			{
				this.slider_Speed.Value = 90;
			}
			int value = Base.UI.smethod_180();
			this.numericUpDown_Units.Value = value;
			this.toolStrip_ExitCreateNewPg.Visible = false;
			if (Base.UI.Form.DotNetBarLayoutString != null)
			{
				this.dotNetBarManager_0.LayoutDefinition = Base.UI.Form.DotNetBarLayoutString;
			}
			if (!this.toolStrip_Trading.Visible)
			{
				this.toolStrip_Trading.Visible = true;
			}
			if (!this.toolStrip_Ctrls.Visible)
			{
				this.toolStrip_Ctrls.Visible = true;
			}
			if (!this.toolStrip_Setting.Visible)
			{
				this.toolStrip_Setting.Visible = true;
			}
			if (!this.toolStrip_Periods.Visible)
			{
				this.toolStrip_Periods.Visible = true;
			}
			if (!this.statusBar.Visible)
			{
				this.statusBar.Visible = true;
			}
			if (Base.UI.Chart.IsSingleTransTabCtrl)
			{
				Base.UI.Form.IsInBlindTestMode = false;
			}
			if (Base.UI.Form.IsInBlindTestMode)
			{
				this.method_154();
				this.toolStripBtnItem_SelectDate.Enabled = false;
			}
			else
			{
				this.method_155();
			}
			this.method_89();
			Base.UI.Form.IsSpanMoveNext = false;
			Base.UI.Form.IsSpanMovePrev = false;
			if (Base.UI.Form.AutoPlayPeriodType == null)
			{
				Base.UI.Form.AutoPlayPeriodType = new PeriodType?(PeriodType.ByMins);
				Base.UI.Form.AutoPlayPeriodUnits = new int?(1);
			}
			this.method_71();
			this.method_68();
			this.method_67();
			this.toolStripBtn_Tick.Text = "分时";
			this.toolStripBtn_Tick.Tooltip = "分时图";
			this.toolStripBtnItem_SelectDate.Tooltip = "期间选择  " + Class208.smethod_5(Enum3.const_20).ShortCutKeyString;
			if (Base.UI.Form.IsInBlindTestMode)
			{
				this.toolStripBtnItem_SelectDate.Tooltip = "期间选择（双盲模式下禁用）";
			}
			this.toolStripBtn_NPeriod.Tooltip = "自定义K线周期";
			this.toolStripBtn_NPeriod.ToolTipVisibleChanged += this.toolStripBtn_NPeriod_ToolTipVisibleChanged;
			this.toolStripDropDownButton_Settings.Tooltip = "设置菜单";
			this.bar_AcctTrans.EnabledChanged += this.bar_AcctTrans_EnabledChanged;
			if (Base.UI.Form.IsInBlindTestMode)
			{
				this.toolStripStatusLabel_BlindTest.Text = "双盲模式：开";
			}
			if (!TApp.IsStIncluded)
			{
				this.btnItem_财报分析.Visible = false;
				this.btnItem_条件选股.Visible = false;
			}
			this.btnItem_视频讲座.Visible = false;
			if (Utility.GetMajorVerFromTExVer(TApp.Ver) >= 4.2 && !TApp.IsTrialUser && !Base.UI.Form.BackupSyncDisabled && Base.UI.Form.BackupSyncPeriodically)
			{
				BkupSyncMgr.smethod_2();
			}
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.numericUpDown_Units.BackColor = Class179.color_1;
				this.numericUpDown_Price.BackColor = Class179.color_1;
			}
			else
			{
				this.numericUpDown_Units.BackColor = Color.White;
				this.numericUpDown_Price.BackColor = Color.White;
			}
		}

		// Token: 0x06001BA6 RID: 7078 RVA: 0x000BB0CC File Offset: 0x000B92CC
		private void method_3()
		{
			float num = TApp.smethod_4(9f, false);
			if (this.Font.Size != num)
			{
				this.Font = new Font("Microsoft YaHei", num);
			}
			foreach (object obj in this.dockSite_ToolBar.Controls)
			{
				Bar bar = (Bar)obj;
				if (bar.Font.Size != num)
				{
					bar.Font = new Font("Microsoft YaHei", num);
				}
			}
			if (this.statusBar.Font.Size != num)
			{
				this.statusBar.Font = new Font("Microsoft YaHei", num);
			}
			num = TApp.smethod_4(8.4f, false);
			if (this.bar_AcctTrans.Font.Size != num)
			{
				this.bar_AcctTrans.Font = new Font("Microsoft Sans Serif", num);
			}
			float num2 = TApp.smethod_4(8f, false);
			if (this.numericUpDown_Units.Font.Size != num2)
			{
				this.numericUpDown_Units.Font = new Font("Microsoft YaHei", num2);
				this.numericUpDown_Price.Font = new Font("Microsoft YaHei", num2);
			}
		}

		// Token: 0x06001BA7 RID: 7079 RVA: 0x000BB21C File Offset: 0x000B941C
		private Point? method_4()
		{
			Point? result = null;
			if (base.Visible && base.WindowState != FormWindowState.Minimized)
			{
				result = new Point?(new Point(base.Location.X + base.Width / 2 + 10, base.Location.Y + base.Height / 2));
			}
			return result;
		}

		// Token: 0x06001BA8 RID: 7080 RVA: 0x000BB284 File Offset: 0x000B9484
		private void method_5()
		{
			string text = Base.UI.smethod_114() + " -  (" + Base.Acct.CurrAccount.AcctName.Trim() + ")";
			if (Base.UI.Form.IsInBlindTestMode)
			{
				text += " - 双盲测试模式";
			}
			this.Text = text;
		}

		// Token: 0x06001BA9 RID: 7081
		[DllImport("user32.dll")]
		private static extern bool ShowWindow(IntPtr intptr_0, int int_11);

		// Token: 0x06001BAA RID: 7082 RVA: 0x0000B681 File Offset: 0x00009881
		private void MainForm_ResizeBegin(object sender, EventArgs e)
		{
			base.SuspendLayout();
			this.method_191();
			this.method_192();
		}

		// Token: 0x06001BAB RID: 7083 RVA: 0x0000B697 File Offset: 0x00009897
		private void MainForm_ResizeEnd(object sender, EventArgs e)
		{
			this.method_193();
			this.method_194();
			base.ResumeLayout();
		}

		// Token: 0x06001BAC RID: 7084 RVA: 0x000BB2D8 File Offset: 0x000B94D8
		private void MainForm_Resize(object sender, EventArgs e)
		{
			base.SuspendLayout();
			this.smethod_0();
			this.method_194();
			base.ResumeLayout();
			this.smethod_1();
			if (this.bool_4 && base.WindowState == FormWindowState.Maximized)
			{
				this.bool_4 = false;
				this.method_28(true);
			}
			else if (this.bool_5 && (base.WindowState == FormWindowState.Normal || base.WindowState == FormWindowState.Maximized))
			{
				this.bool_5 = false;
				this.method_28(true);
			}
			else
			{
				this.method_28(true);
			}
		}

		// Token: 0x06001BAD RID: 7085 RVA: 0x0000B6AD File Offset: 0x000098AD
		private void MainForm_MouseWheel(object sender, MouseEventArgs e)
		{
			if (Base.UI.TransTabs == null || !Base.UI.TransTabs.IsScrollableCtrlFocused)
			{
				if (e.Delta > 0)
				{
					this.method_50();
				}
				else
				{
					this.method_51();
				}
			}
		}

		// Token: 0x06001BAE RID: 7086 RVA: 0x000BB358 File Offset: 0x000B9558
		protected void WndProc(ref Message m)
		{
			int num = 125;
			int num2 = m.WParam.ToInt32() & 65520;
			if (m.Msg == 274 && (num2 == 61488 || num2 == 61472 || num2 == 61728))
			{
				this.method_29();
				this.method_28(false);
				if (num2 == 61488 || num2 == 61728)
				{
					this.method_191();
					num = this.bar_AcctTrans.DockedSite.Height;
					if (num2 == 61488)
					{
						this.bool_4 = true;
					}
					else
					{
						this.bool_5 = true;
					}
				}
			}
			else
			{
				int num3 = m.WParam.ToInt32();
				if ((num3 == 513 || num3 == 516) && this.class41_0 != null)
				{
					this.class41_0.method_2();
				}
			}
			base.WndProc(ref m);
			if (m.Msg == 274 && (num2 == 61488 || num2 == 61728))
			{
				this.method_193();
				this.method_30();
				if (!this.method_198() && this.bar_AcctTrans.Docked && this.bar_AcctTrans.DockedSite.Height != num)
				{
					this.bar_AcctTrans.DockedSite.Height = num;
				}
			}
		}

		// Token: 0x06001BAF RID: 7087 RVA: 0x000BB490 File Offset: 0x000B9690
		protected bool ProcessCmdKey(ref Message msg, Keys keyData)
		{
			Keys keys = (Keys)msg.WParam.ToInt32();
			if (msg.Msg == 257 && (keys == Keys.Left || keys == Keys.Up))
			{
				this.int_0 = 0;
			}
			bool result;
			if ((this.numericUpDown_Price.Focused || this.numericUpDown_Units.Focused) && ((keyData >= Keys.D0 && keyData <= Keys.D9) || keyData == Keys.OemPeriod))
			{
				result = base.ProcessCmdKey(ref msg, keyData);
			}
			else
			{
				if (keys == Keys.Escape)
				{
					Base.UI.smethod_156();
					if (Base.UI.Chart.IsSingleFixedContent && !Base.UI.IsInCreateNewPageState && !Base.UI.IsInDrawObjMode && !Base.UI.IsInDrawOdrMode && Base.UI.SelectedChtCtrl != null)
					{
						if (Base.UI.SelectedChtCtrl is ChtCtrl_Tick)
						{
							Base.UI.smethod_153(Base.UI.SelectedChtCtrl);
						}
						else if (Base.UI.SelectedChtCtrl is ChtCtrl_KLine)
						{
							Base.UI.smethod_149(Base.UI.SelectedChtCtrl);
						}
					}
				}
				if (keys == Keys.Space && Base.UI.TransTabs != null && Base.UI.TransTabs.IsInVideoPage)
				{
					Base.UI.TransTabs.method_133();
					result = base.ProcessCmdKey(ref msg, keyData);
				}
				else if (keyData == Class208.smethod_3(Enum3.const_0))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						this.method_135(OrderType.Order_OpenLong, 0m, false);
					}
					result = true;
				}
				else if (keyData == Class208.smethod_3(Enum3.const_1))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						this.method_135(OrderType.Order_CloseLong, 0m, false);
					}
					result = true;
				}
				else if (keyData == Class208.smethod_3(Enum3.const_2))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						this.method_135(OrderType.Order_OpenShort, 0m, false);
					}
					result = true;
				}
				else if (keyData == Class208.smethod_3(Enum3.const_3))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						this.method_135(OrderType.Order_CloseShort, 0m, false);
					}
					result = true;
				}
				else if (keyData == Class208.smethod_3(Enum3.const_4))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						this.method_133(OrderType.Order_OpenLong);
					}
					result = true;
				}
				else if (keyData == Class208.smethod_3(Enum3.const_5))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						this.method_133(OrderType.Order_CloseLong);
					}
					result = true;
				}
				else if (keyData == Class208.smethod_3(Enum3.const_6))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						this.method_133(OrderType.Order_OpenShort);
					}
					result = true;
				}
				else if (keyData == Class208.smethod_3(Enum3.const_7))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						this.method_133(OrderType.Order_CloseShort);
					}
					result = true;
				}
				else if (keyData == Class208.smethod_3(Enum3.const_8))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						Base.Trading.smethod_55();
					}
					result = true;
				}
				else if (keyData == Class208.smethod_3(Enum3.const_9))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						Base.Trading.smethod_74();
					}
					result = true;
				}
				else if (keyData == Class208.smethod_3(Enum3.const_10))
				{
					if (!Base.UI.IsInCreateNewPageState)
					{
						this.method_201();
					}
					result = true;
				}
				else
				{
					if (keyData == Class208.smethod_3(Enum3.const_11))
					{
						if (this.slider_Speed.Value > 1)
						{
							SliderItem sliderItem = this.slider_Speed;
							int value = sliderItem.Value;
							sliderItem.Value = value - 1;
							return true;
						}
					}
					else if (keyData == Class208.smethod_3(Enum3.const_12))
					{
						if (this.slider_Speed.Value < this.slider_Speed.Maximum)
						{
							SliderItem sliderItem2 = this.slider_Speed;
							int value = sliderItem2.Value;
							sliderItem2.Value = value + 1;
							return true;
						}
					}
					else
					{
						if (keyData == Class208.smethod_3(Enum3.const_13))
						{
							if (!Base.UI.IsInCreateNewPageState)
							{
								this.method_54();
							}
							return true;
						}
						if (keyData == Class208.smethod_3(Enum3.const_14))
						{
							Base.UI.smethod_117();
							return true;
						}
						if (keyData == Class208.smethod_3(Enum3.const_15))
						{
							Base.UI.smethod_120();
							return true;
						}
						if (keyData == Class208.smethod_3(Enum3.const_16))
						{
							this.method_122(Enum7.const_0);
							return true;
						}
						if (keyData == Class208.smethod_3(Enum3.const_17))
						{
							this.method_122(Enum7.const_1);
							return true;
						}
						if (keyData == Class208.smethod_3(Enum3.const_18))
						{
							this.method_55();
							return true;
						}
						if (keyData == Class208.smethod_3(Enum3.const_19))
						{
							Base.UI.smethod_115();
							return true;
						}
						if (keyData == Class208.smethod_3(Enum3.const_20))
						{
							this.method_81();
							return true;
						}
						if (keyData == Class208.smethod_3(Enum3.const_21))
						{
							Base.UI.smethod_116();
							return true;
						}
						if (keyData == Class208.smethod_3(Enum3.const_22))
						{
							Base.UI.smethod_179(null);
							return true;
						}
						if (keyData == Class208.smethod_3(Enum3.const_23))
						{
							ChtCtrl chtCtrl = Base.UI.SelectedChtCtrl;
							if (chtCtrl == null)
							{
								chtCtrl = Base.UI.smethod_37();
							}
							if (chtCtrl != null)
							{
								BaoDianMgr.smethod_6(chtCtrl, null);
							}
							return true;
						}
						if (keyData == Class208.smethod_3(Enum3.const_24))
						{
							Base.UI.smethod_81();
							return true;
						}
						if (keyData <= Keys.Delete)
						{
							switch (keyData)
							{
							case Keys.Left:
								this.method_52();
								break;
							case Keys.Up:
								this.method_50();
								break;
							case Keys.Right:
								this.method_53();
								break;
							case Keys.Down:
								this.method_51();
								break;
							default:
								if (keyData == Keys.Delete)
								{
									this.method_56();
								}
								break;
							}
						}
						else if (keyData != (Keys.LButton | Keys.MButton | Keys.Space | Keys.Shift))
						{
							if (keyData == (Keys.LButton | Keys.RButton | Keys.MButton | Keys.Space | Keys.Shift))
							{
								this.method_49();
							}
						}
						else
						{
							this.method_48();
						}
					}
					result = base.ProcessCmdKey(ref msg, keyData);
				}
			}
			return result;
		}

		// Token: 0x06001BB0 RID: 7088 RVA: 0x0000B6DB File Offset: 0x000098DB
		private void MainForm_KeyDown(object sender, KeyEventArgs e)
		{
			if (this.class41_0 != null)
			{
				this.class41_0.method_2();
			}
		}

		// Token: 0x06001BB1 RID: 7089 RVA: 0x000BB988 File Offset: 0x000B9B88
		private void MainForm_KeyPress(object sender, KeyPressEventArgs e)
		{
			MainForm.Class328 @class = new MainForm.Class328();
			if (((!this.numericUpDown_Price.Focused && !this.numericUpDown_Units.Focused) || (!char.IsDigit(e.KeyChar) && e.KeyChar != '.')) && !Base.UI.IsInCreateNewPageState && (Base.UI.TransTabs == null || !Base.UI.TransTabs.IsDateTimePickersFocused) && Control.ModifierKeys != Keys.Shift && Control.ModifierKeys != Keys.Control && Control.ModifierKeys != Keys.Alt && (Base.UI.TransTabs == null || !Base.UI.TransTabs.IsInInputState))
			{
				List<QuickWndItem> quickWndItemList = Base.UI.QuickWndItemList;
				string text = e.KeyChar.ToString();
				@class.string_0 = text.ToLower();
				IEnumerable<QuickWndItem> enumerable = quickWndItemList.Where(new Func<QuickWndItem, bool>(@class.method_0));
				if (enumerable.Any<QuickWndItem>())
				{
					if (this.quickWnd_0 == null)
					{
						try
						{
							this.quickWnd_0 = new QuickWnd(quickWndItemList, enumerable, text);
						}
						catch (Exception exception_)
						{
							Class182.smethod_0(exception_);
							goto IL_197;
						}
						this.quickWnd_0.StartPosition = FormStartPosition.Manual;
						this.quickWnd_0.QuickWndItemSelected += this.method_207;
						this.quickWnd_0.LostFocus += this.quickWnd_0_LostFocus;
					}
					else
					{
						this.quickWnd_0.method_3(quickWndItemList, enumerable, text);
						if (this.quickWnd_0.Visible)
						{
							this.quickWnd_0.Visible = false;
						}
					}
					this.quickWnd_0.Location = this.method_206();
					this.quickWnd_0.Owner = this;
					try
					{
						this.quickWnd_0.ShowDialog();
					}
					catch (Exception exception_2)
					{
						Class182.smethod_0(exception_2);
					}
				}
				IL_197:;
			}
		}

		// Token: 0x06001BB2 RID: 7090 RVA: 0x0000B6F2 File Offset: 0x000098F2
		private void MainForm_Deactivate(object sender, EventArgs e)
		{
			if (this.timer_0 != null)
			{
				this.timer_0.Stop();
				this.timer_0.Enabled = false;
			}
			if (this.class41_0 != null)
			{
				this.class41_0.Enable = false;
			}
		}

		// Token: 0x06001BB3 RID: 7091 RVA: 0x000BBB4C File Offset: 0x000B9D4C
		private void MainForm_Activated(object sender, EventArgs e)
		{
			if (base.WindowState == FormWindowState.Minimized)
			{
				this.method_28(true);
			}
			if (this.timer_0 != null)
			{
				this.timer_0.Enabled = true;
				this.timer_0.Start();
			}
			if (this.class41_0 != null)
			{
				this.class41_0.Enable = true;
			}
		}

		// Token: 0x06001BB4 RID: 7092 RVA: 0x000BBBA0 File Offset: 0x000B9DA0
		private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
		{
			Base.UI.smethod_156();
			if (e.CloseReason == CloseReason.UserClosing)
			{
				if (Base.UI.Form.IfConfirmQuit && MessageBox.Show("退出程序吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
				{
					e.Cancel = true;
				}
				else
				{
					try
					{
						CfmmcRecImporter.smethod_43();
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
					this.timer_1.Stop();
					if (this.class41_0 != null)
					{
						this.class41_0.method_3();
					}
					Base.UI.Form.IsStarted = false;
					base.Hide();
					Base.UI.smethod_177("正在保存本地数据...", this.method_4());
					BaoDianMgr.smethod_8();
					if (Base.UI.TransTabs != null)
					{
						Base.UI.TransTabs.method_131();
					}
					Base.UI.smethod_177("正在保存用户参数...", this.method_4());
					Base.Data.smethod_61();
					Base.Acct.smethod_13();
					if (Base.UI.Form.IfSaveSpeedOnQuit)
					{
						try
						{
							Base.UI.Form.TimerSpeed = this.slider_Speed.Value;
						}
						catch
						{
							Base.UI.Form.TimerSpeed = 10;
						}
					}
					try
					{
						Base.UI.Form.TradingUnits = Convert.ToInt32(this.numericUpDown_Units.Value);
					}
					catch
					{
						Base.UI.Form.TradingUnits = 1;
					}
					if (base.WindowState != FormWindowState.Minimized)
					{
						if (Base.UI.Form.IfAutoSavePageOnExit)
						{
							if (this.bar_AcctTrans.Enabled)
							{
								if (!Base.UI.Form.IsTransTabBarMaximized)
								{
									Base.UI.smethod_81();
								}
							}
							else if (!Base.UI.Form.IsTransTabMaximized)
							{
								Base.UI.smethod_81();
							}
						}
						if (Base.UI.Form.IfSaveWindowOnQuit)
						{
							Base.UI.Form.DotNetBarLayoutString = this.dotNetBarManager_0.LayoutDefinition;
							Base.UI.smethod_45(this);
						}
					}
					Base.UI.smethod_154();
					Base.UI.smethod_47();
					if (Utility.GetMajorVerFromTExVer(TApp.Ver) >= 4.2 && !TApp.IsTrialUser && !Base.UI.Form.BackupSyncDisabled && !Base.UI.Form.BackupSyncNotOnExit)
					{
						BkupSyncMgr.smethod_4(Base.UI.SplashScreen);
					}
					UpdateManager instance = UpdateManager.Instance;
					if (instance.State == UpdateManager.UpdateProcessState.Prepared)
					{
						Base.UI.smethod_177("正在执行后台更新...", this.method_4());
						Class46.smethod_3(Class22.AppBackgroudUpdating, "CurrVer:" + TApp.Ver);
						Base.UI.Form.LastAppUpdDateTime = new DateTime?(TApp.SrvParams.AppUpdInfo.DateTime);
						Base.UI.smethod_47();
						try
						{
							instance.ApplyUpdates(false);
							Class46.smethod_2(Class22.AppBackgroudUpdated);
						}
						catch (Exception exception_2)
						{
							Class46.smethod_4(exception_2, true, null);
							try
							{
								instance.CleanUp();
							}
							catch
							{
							}
						}
					}
					Class46.smethod_5(80);
					Class46.smethod_2(Class22.AppExited);
					Base.UI.smethod_178();
				}
			}
		}

		// Token: 0x06001BB5 RID: 7093 RVA: 0x000BBE5C File Offset: 0x000BA05C
		private void method_6()
		{
			string infoHTML = TApp.SrvParams.InfoHTML;
			if (!string.IsNullOrEmpty(infoHTML))
			{
				new MsgWindow(infoHTML)
				{
					Owner = this,
					StartPosition = FormStartPosition.CenterParent
				}.ShowDialog();
			}
		}

		// Token: 0x06001BB6 RID: 7094 RVA: 0x0000B729 File Offset: 0x00009929
		private void method_7(object sender, MsgEventArgs e)
		{
			Base.UI.Form.CurrentPageName = e.Msg;
		}

		// Token: 0x06001BB7 RID: 7095 RVA: 0x0000B73D File Offset: 0x0000993D
		private void method_8(object sender, EventArgs e)
		{
			this.smethod_0();
			base.SuspendLayout();
			this.method_9();
			this.method_10();
			Base.UI.smethod_33();
			base.ResumeLayout();
			this.smethod_1();
			base.TitleText = base.TitleText;
		}

		// Token: 0x06001BB8 RID: 7096 RVA: 0x000BBE98 File Offset: 0x000BA098
		private void method_9()
		{
			StyleManager.Style = eStyle.Metro;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				StyleManager.MetroColorGeneratorParameters = new MetroColorGeneratorParameters(Class179.color_1, Class179.color_1);
				ColorScheme colorScheme = this.GetColorScheme();
				colorScheme.BarCaptionText = Class179.color_10;
				colorScheme.BarCaptionInactiveText = Class179.color_8;
				colorScheme.CustomizeText = Class179.color_10;
				colorScheme.ItemText = Class179.color_10;
				colorScheme.ItemCheckedBorder = Class179.color_7;
				colorScheme.BarCaptionBackground = Color.FromArgb(32, 32, 32);
			}
			else
			{
				if (Base.UI.Form.ChartTheme == ChartTheme.Yellow)
				{
					StyleManager.MetroColorGeneratorParameters = new MetroColorGeneratorParameters(Class179.color_9, Class179.color_9);
				}
				else
				{
					StyleManager.MetroColorGeneratorParameters = new MetroColorGeneratorParameters(Class179.color_15, Class179.color_15);
				}
				ColorScheme colorScheme2 = this.GetColorScheme();
				colorScheme2.BarCaptionText = Class179.color_1;
				colorScheme2.BarCaptionInactiveText = Class179.color_4;
				colorScheme2.CustomizeText = Class179.color_1;
				colorScheme2.ItemText = Class179.color_1;
				colorScheme2.ItemCheckedBorder = Class179.color_7;
				colorScheme2.BarCaptionBackground = Color.FromArgb(235, 235, 235);
			}
			base.TitleText = base.TitleText;
		}

		// Token: 0x06001BB9 RID: 7097 RVA: 0x000BBFBC File Offset: 0x000BA1BC
		protected Color GetTitleColor(ItemPaintArgs p)
		{
			Color result;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				if (base.NonClientActive)
				{
					result = Class179.color_8;
				}
				else
				{
					result = Class179.color_7;
				}
			}
			else if (base.NonClientActive)
			{
				result = Class179.color_1;
			}
			else
			{
				result = Class179.color_4;
			}
			return result;
		}

		// Token: 0x06001BBA RID: 7098 RVA: 0x000BC008 File Offset: 0x000BA208
		private void method_10()
		{
			this.method_88();
			this.method_44();
			if (Base.UI.TransTabCtrl != null)
			{
				Base.UI.TransTabCtrl.vmethod_0();
			}
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.panel_Below.BackColor = Class179.color_1;
				this.labelItem_Lots.ForeColor = Class179.color_8;
				this.labelItem_Price.ForeColor = Class179.color_8;
				this.labelItem_Speed.ForeColor = Class179.color_8;
				this.toolStripBtnItem_Start.ForeColor = Class179.color_10;
				this.toolStripBtnItem_Start.SubItemTriangleColor = Class179.color_9;
				this.toolStripBtnItem_SelectDate.ForeColor = Class179.color_10;
				this.slider_Speed.TextColor = Class179.color_8;
				this.toolStripButton_ExitCrtNewPg.ForeColor = Class179.color_10;
				this.toolStripDropDownButton_Settings.ForeColor = Class179.color_10;
				this.toolStripDropDownButton_Settings.SubItemTriangleColor = Class179.color_9;
				this.toolStripBtn_NPeriod.ForeColor = Class179.color_9;
				this.toolStripBtn_NPeriod.SubItemTriangleColor = Class179.color_8;
				this.txtBoxItem_NMins.TextBox.ForeColor = Class179.color_8;
				this.txtBoxItem_NHours.TextBox.ForeColor = Class179.color_8;
				this.txtBoxItem_NDays.TextBox.ForeColor = Class179.color_8;
				this.toolStripBttn_1m.ForeColor = Class179.color_9;
				this.toolStripBttn_3m.ForeColor = Class179.color_9;
				this.toolStripBttn_5m.ForeColor = Class179.color_9;
				this.toolStripBttn_10m.ForeColor = Class179.color_9;
				this.toolStripBttn_15m.ForeColor = Class179.color_9;
				this.toolStripBttn_30m.ForeColor = Class179.color_9;
				this.toolStripBttn_1h.ForeColor = Class179.color_9;
				this.toolStripBttn_2h.ForeColor = Class179.color_9;
				this.toolStripBttn_4h.ForeColor = Class179.color_9;
				this.toolStripBttn_1d.ForeColor = Class179.color_9;
				this.toolStripBttn_1w.ForeColor = Class179.color_9;
				this.toolStripBttn_1Mth.ForeColor = Class179.color_9;
				this.toolStripBttn_1m.HotForeColor = Class179.color_1;
				this.toolStripBttn_3m.HotForeColor = Class179.color_1;
				this.toolStripBttn_5m.HotForeColor = Class179.color_1;
				this.toolStripBttn_10m.HotForeColor = Class179.color_1;
				this.toolStripBttn_15m.HotForeColor = Class179.color_1;
				this.toolStripBttn_30m.HotForeColor = Class179.color_1;
				this.toolStripBttn_1h.HotForeColor = Class179.color_1;
				this.toolStripBttn_2h.HotForeColor = Class179.color_1;
				this.toolStripBttn_4h.HotForeColor = Class179.color_1;
				this.toolStripBttn_1d.HotForeColor = Class179.color_1;
				this.toolStripBttn_1w.HotForeColor = Class179.color_1;
				this.toolStripBttn_1Mth.HotForeColor = Class179.color_1;
				this.toolStripBttn_Long.HotForeColor = Color.OrangeRed;
				this.toolStripBttn_Long.HotFontBold = true;
				this.toolStripBttn_ClsLong.HotForeColor = Color.LightGreen;
				this.toolStripBttn_ClsLong.HotFontBold = true;
				this.toolStripBttn_Short.HotForeColor = Color.LightGreen;
				this.toolStripBttn_Short.HotFontBold = true;
				this.toolStripBttn_ClsShort.HotForeColor = Color.OrangeRed;
				this.toolStripBttn_ClsShort.HotFontBold = true;
				this.labelItem_Lots.TextAlignment = StringAlignment.Far;
				this.labelItem_Price.TextAlignment = StringAlignment.Far;
				this.numericUpDown_Units.BackColor = Class179.color_1;
				this.numericUpDown_Price.BackColor = Class179.color_1;
				this.labelItem_页面.ForeColor = Class179.color_8;
				this.labelItem_账户.ForeColor = Class179.color_8;
				this.labelItem_品种.ForeColor = Class179.color_8;
				this.labelItem_平仓盈亏.ForeColor = Class179.color_8;
				this.labelItem_浮动盈亏.ForeColor = Class179.color_8;
				this.toolStripDropDownButton_Pages.ForeColor = Class179.color_10;
				this.toolStripDropDownButton_Accts.ForeColor = Class179.color_10;
				this.toolStripDropDownButton_Symbls.ForeColor = Class179.color_10;
			}
			else
			{
				if (Base.UI.Form.ChartTheme == ChartTheme.Yellow)
				{
					this.panel_Below.BackColor = Class179.color_9;
				}
				else
				{
					this.panel_Below.BackColor = Class179.color_14;
				}
				this.labelItem_Lots.ForeColor = Class179.color_1;
				this.labelItem_Price.ForeColor = Class179.color_1;
				this.labelItem_Speed.ForeColor = Class179.color_1;
				this.toolStripBtnItem_Start.ForeColor = Class179.color_1;
				this.toolStripBtnItem_Start.SubItemTriangleColor = Class179.color_1;
				this.toolStripBtnItem_SelectDate.ForeColor = Class179.color_1;
				this.slider_Speed.TextColor = Class179.color_1;
				this.toolStripButton_ExitCrtNewPg.ForeColor = Class179.color_1;
				this.toolStripDropDownButton_Settings.ForeColor = Class179.color_1;
				this.toolStripDropDownButton_Settings.SubItemTriangleColor = Class179.color_1;
				this.toolStripBtn_NPeriod.ForeColor = Class179.color_1;
				this.toolStripBtn_NPeriod.SubItemTriangleColor = Class179.color_1;
				this.txtBoxItem_NMins.TextBox.ForeColor = Class179.color_1;
				this.txtBoxItem_NHours.TextBox.ForeColor = Class179.color_1;
				this.txtBoxItem_NDays.TextBox.ForeColor = Class179.color_1;
				this.toolStripBttn_1m.ForeColor = Class179.color_1;
				this.toolStripBttn_3m.ForeColor = Class179.color_1;
				this.toolStripBttn_5m.ForeColor = Class179.color_1;
				this.toolStripBttn_10m.ForeColor = Class179.color_1;
				this.toolStripBttn_15m.ForeColor = Class179.color_1;
				this.toolStripBttn_30m.ForeColor = Class179.color_1;
				this.toolStripBttn_1h.ForeColor = Class179.color_1;
				this.toolStripBttn_2h.ForeColor = Class179.color_1;
				this.toolStripBttn_4h.ForeColor = Class179.color_1;
				this.toolStripBttn_1d.ForeColor = Class179.color_1;
				this.toolStripBttn_1w.ForeColor = Class179.color_1;
				this.toolStripBttn_1Mth.ForeColor = Class179.color_1;
				this.toolStripBttn_1m.HotForeColor = Class179.color_1;
				this.toolStripBttn_3m.HotForeColor = Class179.color_1;
				this.toolStripBttn_5m.HotForeColor = Class179.color_1;
				this.toolStripBttn_10m.HotForeColor = Class179.color_1;
				this.toolStripBttn_15m.HotForeColor = Class179.color_1;
				this.toolStripBttn_30m.HotForeColor = Class179.color_1;
				this.toolStripBttn_1h.HotForeColor = Class179.color_1;
				this.toolStripBttn_2h.HotForeColor = Class179.color_1;
				this.toolStripBttn_4h.HotForeColor = Class179.color_1;
				this.toolStripBttn_1d.HotForeColor = Class179.color_1;
				this.toolStripBttn_1w.HotForeColor = Class179.color_1;
				this.toolStripBttn_1Mth.HotForeColor = Class179.color_1;
				this.toolStripBttn_Long.HotForeColor = Color.OrangeRed;
				this.toolStripBttn_Long.HotFontBold = true;
				this.toolStripBttn_ClsLong.HotForeColor = Color.LightGreen;
				this.toolStripBttn_ClsLong.HotFontBold = true;
				this.toolStripBttn_Short.HotForeColor = Color.LightGreen;
				this.toolStripBttn_Short.HotFontBold = true;
				this.toolStripBttn_ClsShort.HotForeColor = Color.OrangeRed;
				this.toolStripBttn_ClsShort.HotFontBold = true;
				this.labelItem_Lots.TextAlignment = StringAlignment.Far;
				this.labelItem_Price.TextAlignment = StringAlignment.Far;
				this.numericUpDown_Units.BackColor = Color.White;
				this.numericUpDown_Price.BackColor = Color.White;
				this.labelItem_页面.ForeColor = Class179.color_1;
				this.labelItem_账户.ForeColor = Class179.color_1;
				this.labelItem_品种.ForeColor = Class179.color_1;
				this.labelItem_平仓盈亏.ForeColor = Class179.color_1;
				this.labelItem_浮动盈亏.ForeColor = Class179.color_1;
				this.toolStripDropDownButton_Pages.ForeColor = Class179.color_1;
				this.toolStripDropDownButton_Accts.ForeColor = Class179.color_1;
				this.toolStripDropDownButton_Symbls.ForeColor = Class179.color_1;
			}
		}

		// Token: 0x06001BBB RID: 7099 RVA: 0x0000685B File Offset: 0x00004A5B
		private void 黑红经典ToolStripMenuItem_Click(object sender, EventArgs e)
		{
			Base.UI.smethod_32(ChartTheme.Classic);
		}

		// Token: 0x06001BBC RID: 7100 RVA: 0x0000686F File Offset: 0x00004A6F
		private void 绿白现代ToolStripMenuItem_Click(object sender, EventArgs e)
		{
			Base.UI.smethod_32(ChartTheme.Modern);
		}

		// Token: 0x06001BBD RID: 7101 RVA: 0x00006865 File Offset: 0x00004A65
		private void 绿白经典ToolStripMenuItem_Click(object sender, EventArgs e)
		{
			Base.UI.smethod_32(ChartTheme.Yellow);
		}

		// Token: 0x06001BBE RID: 7102 RVA: 0x000BC7D8 File Offset: 0x000BA9D8
		private void btnItem_界面风格_PopupOpen(object sender, EventArgs e)
		{
			foreach (object obj in this.btnItem_界面风格.SubItems)
			{
				((ButtonItem)obj).Checked = false;
			}
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.黑红经典ToolStripMenuItem.Checked = true;
			}
			else if (Base.UI.Form.ChartTheme == ChartTheme.Modern)
			{
				this.绿白现代ToolStripMenuItem.Checked = true;
			}
			else if (Base.UI.Form.ChartTheme == ChartTheme.Yellow)
			{
				this.绿白经典ToolStripMenuItem.Checked = true;
			}
		}

		// Token: 0x06001BBF RID: 7103 RVA: 0x000BC888 File Offset: 0x000BAA88
		private void method_11(bool bool_8)
		{
			AppUpdInfo appUpdInfo = TApp.SrvParams.AppUpdInfo;
			if (appUpdInfo != null && !string.IsNullOrEmpty(appUpdInfo.Feed))
			{
				if (bool_8)
				{
					Version texVersion = TApp.TExVersion;
					Version v = new Version(TApp.SrvParams.AppUpdInfo.Ver);
					if (texVersion <= v)
					{
						this.method_12(false, false);
					}
				}
				else
				{
					this.method_12(false, false);
				}
			}
		}

		// Token: 0x06001BC0 RID: 7104 RVA: 0x000BC8EC File Offset: 0x000BAAEC
		private int method_12(bool bool_8, bool bool_9)
		{
			if (TApp.SrvParams.AppUpdInfo != null && !string.IsNullOrEmpty(TApp.SrvParams.AppUpdInfo.Feed))
			{
				if (bool_9)
				{
					Base.UI.smethod_177("正在检查程序更新...", this.method_4());
				}
				string feed = TApp.SrvParams.AppUpdInfo.Feed;
				UpdateManager instance = UpdateManager.Instance;
				instance.UpdateSource = new SimpleWebSource();
				instance.Config.TempFolder = Path.Combine(TApp.string_10, "Updates");
				instance.ReinstateIfRestarted();
				IUpdateSource iupdateSource_ = new MemorySource(feed);
				int num = 0;
				int result;
				try
				{
					num = this.method_13(iupdateSource_);
					goto IL_D3;
				}
				catch (Exception ex)
				{
					Base.UI.smethod_178();
					if (ex is InvalidOperationException && ex.Message.Contains("Already checked for updates"))
					{
						MessageBox.Show("已检查过程序更新，无需重复检查。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					}
					result = 0;
				}
				return result;
				IL_D3:
				if (bool_9)
				{
					Base.UI.smethod_178();
				}
				if (num > 0)
				{
					bool flag = true;
					if (bool_8)
					{
						flag = false;
						if (MessageBox.Show(string.Concat(new string[]
						{
							"检测到有程序更新(v",
							TApp.SrvParams.AppUpdInfo.Ver,
							")，执行更新吗？",
							Environment.NewLine,
							Environment.NewLine,
							"请注意：更新后需要重新启动程序才能生效，如退出时系统或安全软件提示是否允许程序更改，请选择允许。"
						}), "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
						{
							flag = true;
						}
						else
						{
							instance.CleanUp();
						}
					}
					if (flag)
					{
						if (bool_9)
						{
							Base.UI.smethod_177("正在更新程序...", this.method_4());
						}
						if (feed.Contains(".exe") || feed.Contains(".dll") || feed.Contains(".config"))
						{
							this.bool_3 = true;
						}
						this.toolStripStatusLabel_Info.Text = "发现" + num.ToString() + "个程序文件更新，准备后台更新中...";
						instance.Config.UpdateProcessName = "TExUpd.exe";
						instance.Config.UpdateExecutableName = "TExUpd.exe";
						instance.BeginPrepareUpdates(new AsyncCallback(this.method_14), null);
						this.string_0 = instance.Config.TempFolder;
						if (bool_9)
						{
							Base.UI.smethod_178();
						}
						return num;
					}
				}
				else
				{
					if (bool_8)
					{
						MessageBox.Show("未检测到程序更新，当前程序已是最新版本。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					}
					this.toolStripStatusLabel_Info.Text = "程序更新检查完成，已是最新版本。";
				}
			}
			return 0;
		}

		// Token: 0x06001BC1 RID: 7105 RVA: 0x000BCB3C File Offset: 0x000BAD3C
		private int method_13(IUpdateSource iupdateSource_0)
		{
			UpdateManager instance = UpdateManager.Instance;
			int result = 0;
			try
			{
				instance.CheckForUpdates(iupdateSource_0);
				result = instance.UpdatesAvailable;
			}
			catch (Exception ex)
			{
				NAppUpdateException ex2 = ex as NAppUpdateException;
				throw;
			}
			return result;
		}

		// Token: 0x06001BC2 RID: 7106 RVA: 0x000BCB80 File Offset: 0x000BAD80
		private void method_14(IAsyncResult iasyncResult_0)
		{
			MainForm.Class329 @class = new MainForm.Class329();
			@class.mainForm_0 = this;
			try
			{
				((UpdateProcessAsyncResult)iasyncResult_0).EndInvoke();
			}
			catch (Exception ex)
			{
				MainForm.Class330 class2 = new MainForm.Class330();
				class2.class329_0 = @class;
				Exception exception_ = ex;
				class2.exception_0 = exception_;
				try
				{
					this.toolStripStatusLabel_Info.Invoke(new Action(class2.method_0));
					this.method_15();
					return;
				}
				catch
				{
					return;
				}
			}
			@class.string_0 = "后台程序文件更新完成";
			if (this.bool_3)
			{
				@class.string_0 += "，重新启动程序后生效。";
			}
			else
			{
				@class.string_0 += "。";
			}
			this.toolStripStatusLabel_Info.Invoke(new Action(@class.method_0));
			if (!string.IsNullOrEmpty(this.string_0) && Directory.Exists(this.string_0))
			{
				string text = this.string_0 + "\\TExUpd.exe.config";
				if (!Utility.FileExists(text))
				{
					string[] contents = new string[]
					{
						"<?xml version=\"1.0\"?>",
						"<configuration>",
						"<startup useLegacyV2RuntimeActivationPolicy=\"true\">",
						"<supportedRuntime version=\"v4.0\"/>",
						"<supportedRuntime version=\"v2.0.50727\"/>",
						"</startup>",
						"</configuration>"
					};
					File.WriteAllLines(text, contents);
				}
			}
		}

		// Token: 0x06001BC3 RID: 7107 RVA: 0x000BCCD8 File Offset: 0x000BAED8
		private void method_15()
		{
			try
			{
				UpdateManager.Instance.CleanUp();
			}
			catch
			{
			}
		}

		// Token: 0x06001BC4 RID: 7108 RVA: 0x000BCD08 File Offset: 0x000BAF08
		private void method_16()
		{
			if (Base.UI.TransTabs != null)
			{
				Base.UI.TransTabs.TUnitsInputChanged -= this.method_143;
				Base.UI.TransTabs.TPriceInputChanged -= this.method_144;
				Base.UI.TransTabs.ChangeToHisTransDTRequested -= this.method_145;
				Base.UI.TransTabs.MsgNotifyNeeded -= this.method_146;
				Base.UI.TransTabs.Dispose();
				Base.UI.TransTabs = null;
			}
			Base.UI.smethod_112();
			if (Base.UI.TransTabs != null)
			{
				if (Base.UI.TransTabCtrl != null)
				{
					Base.UI.TransTabCtrl.MaxButtonVisible = !Base.UI.Chart.IsSingleTransTabCtrl;
					this.panel_Below.ResumeLayout();
				}
				if (this.bar_AcctTrans.Visible)
				{
					this.bar_AcctTrans.Hide();
				}
				if (this.bar_AcctTrans.Enabled)
				{
					this.bar_AcctTrans.Enabled = false;
				}
			}
			else
			{
				if (!this.bar_AcctTrans.Enabled)
				{
					this.bar_AcctTrans.Enabled = true;
				}
				TransTabs transTabs = new TransTabs(null);
				Base.UI.TransTabs = transTabs;
				this.panelDockContainer_AcctTrans.Controls.Add(transTabs);
				if (this.bool_2 && this.toolStripDropDownButton_Pages.Text == "行情")
				{
					Base.UI.Form.IfShowTransTabsBar = true;
				}
				if (Base.UI.Form.IfShowTransTabsBar)
				{
					if (!this.bar_AcctTrans.Visible)
					{
						this.bar_AcctTrans.DockSide = Base.UI.Form.AcctTransBar_DockSide;
						if (!this.bar_AcctTrans.AutoHide)
						{
							this.bar_AcctTrans.Show();
							this.panelDockContainer_AcctTrans.Visible = true;
						}
						Base.UI.IsTransTabsVisible = true;
					}
					else if (!this.panelDockContainer_AcctTrans.Visible)
					{
						this.panelDockContainer_AcctTrans.Visible = true;
					}
				}
				else if (this.bar_AcctTrans.Visible)
				{
					this.bar_AcctTrans.Hide();
					Base.UI.IsTransTabsVisible = false;
				}
			}
			if (Base.UI.TransTabs != null)
			{
				Base.UI.TransTabs.IfAutoOpenClose = Base.UI.Form.IfAutoOpenCloseInTradingTab;
				Base.UI.TransTabs.IfFollowPrice = Base.UI.Form.IfFollowPrcInTradingTab;
				Base.UI.TransTabs.TUnitsInputChanged += this.method_143;
				Base.UI.TransTabs.TPriceInputChanged += this.method_144;
				Base.UI.TransTabs.ChangeToHisTransDTRequested += this.method_145;
				Base.UI.TransTabs.MsgNotifyNeeded += this.method_146;
			}
			Base.UI.smethod_134(true);
			if (Base.UI.TransTabCtrl != null)
			{
				Base.UI.TransTabCtrl.vmethod_0();
			}
			if (Base.UI.TransTabs != null && Base.UI.TransTabs.Visible)
			{
				Base.UI.TransTabs.method_63();
			}
			this.method_132();
			this.method_17();
			this.method_45();
		}

		// Token: 0x06001BC5 RID: 7109 RVA: 0x0000B776 File Offset: 0x00009976
		private void bar_AcctTrans_EnabledChanged(object sender, EventArgs e)
		{
			if (this.bar_AcctTrans.Enabled)
			{
				this.method_45();
			}
		}

		// Token: 0x06001BC6 RID: 7110 RVA: 0x000BCFC0 File Offset: 0x000BB1C0
		private void method_17()
		{
			this.btnItem_财报分析.Click += this.btnItem_财报分析_Click;
			this.btnItem_条件选股.Click += this.btnItem_条件选股_Click;
			this.btnItem_视频讲座.Click += this.btnItem_视频讲座_Click;
			this.method_19();
			this.method_22();
		}

		// Token: 0x06001BC7 RID: 7111 RVA: 0x0000B78D File Offset: 0x0000998D
		private void btnItem_条件选股_Click(object sender, EventArgs e)
		{
			this.method_18("条件");
		}

		// Token: 0x06001BC8 RID: 7112 RVA: 0x0000B79C File Offset: 0x0000999C
		private void btnItem_财报分析_Click(object sender, EventArgs e)
		{
			this.method_18("财报");
		}

		// Token: 0x06001BC9 RID: 7113 RVA: 0x0000B7AB File Offset: 0x000099AB
		private void btnItem_视频讲座_Click(object sender, EventArgs e)
		{
			this.method_18("视频");
		}

		// Token: 0x06001BCA RID: 7114 RVA: 0x0000B7BA File Offset: 0x000099BA
		private void method_18(string string_6)
		{
			if (Base.UI.TransTabs != null)
			{
				Base.UI.TransTabs.method_136(string_6);
				if (!Base.UI.Form.IfShowTransTabsBar)
				{
					this.method_74();
				}
			}
		}

		// Token: 0x06001BCB RID: 7115 RVA: 0x000BD020 File Offset: 0x000BB220
		private void method_19()
		{
			if (Base.UI.TransTabs != null)
			{
				this.btnItem_市场板块.SubItems.Clear();
				foreach (object obj in Base.UI.TransTabs.MarketTabsCollection)
				{
					TabItem tabItem = (TabItem)obj;
					ButtonItem buttonItem = new ButtonItem();
					buttonItem.Text = tabItem.Text;
					buttonItem.Click += this.method_20;
					this.btnItem_市场板块.SubItems.Add(buttonItem);
				}
			}
		}

		// Token: 0x06001BCC RID: 7116 RVA: 0x000BD0CC File Offset: 0x000BB2CC
		private void method_20(object sender, EventArgs e)
		{
			ButtonItem buttonItem = sender as ButtonItem;
			if (Base.UI.TransTabs != null)
			{
				Base.UI.TransTabs.SelectedTabIndex = 0;
				if (!Base.UI.Form.IfShowTransTabsBar)
				{
					this.method_74();
				}
				Base.UI.TransTabs.MktSelectedTab = Base.UI.TransTabs.MarketTabsCollection[buttonItem.Text];
			}
		}

		// Token: 0x06001BCD RID: 7117 RVA: 0x0000B7E2 File Offset: 0x000099E2
		private void method_21(string string_6 = null)
		{
			if (Base.UI.TransTabs != null)
			{
				Base.UI.TransTabs.method_136("模拟交易");
				if (!string.IsNullOrEmpty(string_6))
				{
					Base.UI.TransTabs.method_138(string_6);
				}
				if (!Base.UI.Form.IfShowTransTabsBar)
				{
					this.method_74();
				}
			}
		}

		// Token: 0x06001BCE RID: 7118 RVA: 0x000BD128 File Offset: 0x000BB328
		private void method_22()
		{
			foreach (object obj in this.btnItem_交易分析.SubItems)
			{
				((ButtonItem)obj).Click += this.method_23;
			}
		}

		// Token: 0x06001BCF RID: 7119 RVA: 0x000BD194 File Offset: 0x000BB394
		private void method_23(object sender, EventArgs e)
		{
			ButtonItem buttonItem = sender as ButtonItem;
			if (Base.UI.TransTabs != null)
			{
				Base.UI.TransTabs.method_136("交易分析");
				Base.UI.TransTabs.method_139(buttonItem.Text);
				if (!Base.UI.Form.IfShowTransTabsBar)
				{
					this.method_74();
				}
			}
		}

		// Token: 0x06001BD0 RID: 7120 RVA: 0x000BD1E4 File Offset: 0x000BB3E4
		private void method_24(DateTime? nullable_1)
		{
			if (base.WindowState == FormWindowState.Minimized)
			{
				MainForm.ShowWindow(base.Handle, 9);
			}
			this.panel_Below.SuspendLayout();
			this.panel_Below.Controls.Clear();
			Base.UI.smethod_106(this.panel_Below);
			Base.UI.smethod_107(nullable_1);
			Base.UI.ChtCtrlsRestored += this.method_25;
		}

		// Token: 0x06001BD1 RID: 7121 RVA: 0x000BD248 File Offset: 0x000BB448
		private void method_25(object sender, EventArgs e)
		{
			foreach (ChtCtrl chtCtrl_ in Base.UI.ChtCtrlList)
			{
				this.method_26(chtCtrl_);
			}
			this.panel_Below.ResumeLayout();
			if (!this.bool_2)
			{
				this.method_35();
			}
			else
			{
				Base.UI.smethod_21();
				Base.UI.smethod_133();
			}
			if (Base.UI.ChtCtrlList.Count == 1)
			{
				Base.UI.ChtCtrlList[0].IsSelected = true;
			}
		}

		// Token: 0x06001BD2 RID: 7122 RVA: 0x000BD2E0 File Offset: 0x000BB4E0
		private void method_26(ChtCtrl chtCtrl_0)
		{
			chtCtrl_0.Enter += this.method_36;
			chtCtrl_0.Leave += this.method_37;
			if (chtCtrl_0 is ChtCtrl_KLine)
			{
				ChtCtrl_KLine chtCtrl_KLine = (ChtCtrl_KLine)chtCtrl_0;
				chtCtrl_KLine.PeriodChanged += this.method_32;
				chtCtrl_KLine.AfterEnteringRetroMode += this.method_31;
			}
		}

		// Token: 0x06001BD3 RID: 7123 RVA: 0x000BD344 File Offset: 0x000BB544
		private void method_27(DateTime dateTime_0)
		{
			foreach (ChtCtrl chtCtrl in Base.UI.ChtCtrlList)
			{
				chtCtrl.SymbDataSet = Base.Data.smethod_49(chtCtrl.SymbDataSet.SymblID, false);
				BackgroundWorker backgroundWorker = new BackgroundWorker();
				backgroundWorker.DoWork += this.method_33;
				backgroundWorker.RunWorkerCompleted += this.method_34;
				backgroundWorker.RunWorkerAsync(new Class178
				{
					chtCtrl_0 = chtCtrl,
					dateTime_0 = dateTime_0
				});
			}
		}

		// Token: 0x06001BD4 RID: 7124 RVA: 0x000BD3EC File Offset: 0x000BB5EC
		private void method_28(bool bool_8)
		{
			if (Base.UI.ChtCtrlList != null)
			{
				foreach (ChtCtrl chtCtrl in Base.UI.ChtCtrlList)
				{
					chtCtrl.Visible = bool_8;
				}
			}
		}

		// Token: 0x06001BD5 RID: 7125 RVA: 0x000BD448 File Offset: 0x000BB648
		private void method_29()
		{
			if (Base.UI.ChtCtrlList != null)
			{
				foreach (ChtCtrl chtCtrl in Base.UI.ChtCtrlList)
				{
					if (chtCtrl.IsSelected)
					{
						chtCtrl.method_28();
					}
				}
			}
		}

		// Token: 0x06001BD6 RID: 7126 RVA: 0x000BD4AC File Offset: 0x000BB6AC
		private void method_30()
		{
			if (Base.UI.ChtCtrlList != null)
			{
				foreach (ChtCtrl chtCtrl in Base.UI.ChtCtrlList)
				{
					if (chtCtrl.IsSelected)
					{
						chtCtrl.method_27();
					}
				}
			}
		}

		// Token: 0x06001BD7 RID: 7127 RVA: 0x0000B821 File Offset: 0x00009A21
		private void method_31(EventArgs eventArgs_0)
		{
			this.IsInRetroMode = true;
		}

		// Token: 0x06001BD8 RID: 7128 RVA: 0x000BD510 File Offset: 0x000BB710
		private void method_32(object sender, EventArgs e)
		{
			ChtCtrl_KLine chtCtrl_KLine = sender as ChtCtrl_KLine;
			if (chtCtrl_KLine.IsSelected)
			{
				this.method_62(chtCtrl_KLine);
			}
			if (Base.UI.Form.AutoPlayPeriodType != null && Base.UI.Form.IsJustSpanMoved && Base.UI.Form.SpanMoveChtCtrl != null && Base.UI.Form.SpanMoveChtCtrl == chtCtrl_KLine)
			{
				Base.UI.Form.AutoPlayPeriodType = new PeriodType?(chtCtrl_KLine.PeriodType);
				Base.UI.Form.AutoPlayPeriodUnits = chtCtrl_KLine.PeriodUnits;
			}
			else if (!Base.UI.ChtCtrl_KLineList.Exists(new Predicate<ChtCtrl_KLine>(MainForm.<>c.<>9.method_0)))
			{
				Base.UI.Form.AutoPlayPeriodType = new PeriodType?(PeriodType.ByMins);
				Base.UI.Form.AutoPlayPeriodUnits = new int?(1);
			}
		}

		// Token: 0x06001BD9 RID: 7129 RVA: 0x000BD5E0 File Offset: 0x000BB7E0
		private void method_33(object sender, DoWorkEventArgs e)
		{
			Class178 @class = e.Argument as Class178;
			ChtCtrl chtCtrl_ = @class.chtCtrl_0;
			PeriodType periodType = chtCtrl_.HisDataPeriodSet.PeriodType;
			int? periodUnits = chtCtrl_.HisDataPeriodSet.PeriodUnits;
			int id;
			if (@class.symbDataSet_0 != null && @class.symbDataSet_0.CurrSymbol != null)
			{
				id = @class.symbDataSet_0.CurrSymbol.ID;
			}
			else
			{
				id = chtCtrl_.Symbol.ID;
			}
			SymbDataSet symbDataSet = Base.Data.smethod_49(id, true);
			HisDataPeriodSet hisDataPeriodSet_ = null;
			if (symbDataSet.HasValidDataSet)
			{
				hisDataPeriodSet_ = symbDataSet.method_58(periodType, periodUnits);
			}
			@class.chtCtrl_0 = chtCtrl_;
			@class.hisDataPeriodSet_0 = hisDataPeriodSet_;
			e.Result = @class;
		}

		// Token: 0x06001BDA RID: 7130 RVA: 0x000BD688 File Offset: 0x000BB888
		private void method_34(object sender, RunWorkerCompletedEventArgs e)
		{
			Class178 @class = e.Result as Class178;
			HisDataPeriodSet hisDataPeriodSet_ = @class.hisDataPeriodSet_0;
			if (hisDataPeriodSet_ != null)
			{
				MainForm.Class331 class2 = new MainForm.Class331();
				ChtCtrl chtCtrl_ = @class.chtCtrl_0;
				chtCtrl_.HisDataPeriodSet = hisDataPeriodSet_;
				if (chtCtrl_ is ChtCtrl_KLine && chtCtrl_.HisDataPeriodSet != null)
				{
					((ChtCtrl_KLine)chtCtrl_).method_115();
				}
				DateTime dateTime_ = @class.dateTime_0;
				chtCtrl_.method_18(dateTime_);
				class2.symbDataSet_0 = chtCtrl_.SymbDataSet;
				if ((class2.symbDataSet_0.CurrHisDataSet != null && !class2.symbDataSet_0.IsCurrDateNotListedYet) || Base.UI.CurrTradingSymbol.ID != class2.symbDataSet_0.SymblID)
				{
					return;
				}
				try
				{
					Base.UI.CurrTradingSymbol = Base.Data.SymbDataSets.Where(new Func<SymbDataSet, bool>(class2.method_0)).First<SymbDataSet>().CurrSymbol;
					return;
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
					return;
				}
			}
			Class182.smethod_0(new ArgumentNullException("HisDataPeriodSet is null!"));
		}

		// Token: 0x06001BDB RID: 7131 RVA: 0x000BD77C File Offset: 0x000BB97C
		private void method_35()
		{
			if (Base.Acct.CurrAccount.LastSymbIdAndDtList != null)
			{
				Base.UI.smethod_22(Base.Acct.CurrAccount.LastSymbDT);
				Base.UI.smethod_133();
			}
			else if (Base.Acct.CurrAccount.LastSymbDT != null)
			{
				this.method_46(Base.Acct.CurrAccount.LastSymbDT.Value);
			}
			else
			{
				Base.UI.smethod_20(Base.Data.SymbDataSets.Where(new Func<SymbDataSet, bool>(MainForm.<>c.<>9.method_1)).Max(new Func<SymbDataSet, DateTime>(MainForm.<>c.<>9.method_2)));
				Base.UI.smethod_133();
			}
		}

		// Token: 0x06001BDC RID: 7132 RVA: 0x000BD838 File Offset: 0x000BBA38
		private void method_36(object sender, EventArgs e)
		{
			ChtCtrl chtCtrl = sender as ChtCtrl;
			this.method_62(chtCtrl);
			Base.UI.SelectedChtCtrl = chtCtrl;
		}

		// Token: 0x06001BDD RID: 7133 RVA: 0x0000B82C File Offset: 0x00009A2C
		private void method_37(object sender, EventArgs e)
		{
			this.method_62(null);
			Base.UI.SelectedChtCtrl = null;
		}

		// Token: 0x06001BDE RID: 7134 RVA: 0x0000B83D File Offset: 0x00009A3D
		private void method_38(object sender, EventArgs e)
		{
			if (TApp.IsTrialUser)
			{
				Base.UI.smethod_178();
				if (MessageBox.Show("已至免费版最早可回溯复盘日期，重新选择期间吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
				{
					this.method_82();
				}
			}
		}

		// Token: 0x06001BDF RID: 7135 RVA: 0x0000B868 File Offset: 0x00009A68
		private void method_39(EventArgs18 eventArgs18_0)
		{
			this.method_26(eventArgs18_0.ChtCtrl);
		}

		// Token: 0x06001BE0 RID: 7136 RVA: 0x000BD85C File Offset: 0x000BBA5C
		private void method_40(EventArgs20 eventArgs20_0)
		{
			if (eventArgs20_0.Trigger is TransTabs)
			{
				this.bar_AcctTrans.Enabled = true;
				Base.UI.Form.IfShowTransTabsBar = false;
				Base.UI.IsTransTabsVisible = false;
				Base.UI.SwitchedBehindTransTabs = (eventArgs20_0.Trigger as TransTabs);
				Base.UI.TransTabs = null;
				eventArgs20_0.Trigger = null;
			}
			if (eventArgs20_0.InfrontCtrl is ChtCtrl)
			{
				((ChtCtrl)eventArgs20_0.InfrontCtrl).Focus();
			}
			else if (eventArgs20_0.InfrontCtrl is TransTabs)
			{
				((TransTabs)eventArgs20_0.InfrontCtrl).ParentTransTabCtrl.Focus();
			}
			else if (eventArgs20_0.InfrontCtrl is TransTabCtrl)
			{
				((TransTabCtrl)eventArgs20_0.InfrontCtrl).Focus();
			}
		}

		// Token: 0x06001BE1 RID: 7137 RVA: 0x000BD918 File Offset: 0x000BBB18
		private void method_41(object sender, EventArgs e)
		{
			if (this.panelDockContainer_AcctTrans.Controls.Count > 0)
			{
				this.panelDockContainer_AcctTrans.Controls.Clear();
			}
			this.bar_AcctTrans.Enabled = false;
			this.bar_AcctTrans.Hide();
			if (Base.UI.TransTabs != null && Base.UI.TransTabs.Visible)
			{
				Base.UI.TransTabs.method_63();
			}
			this.method_45();
		}

		// Token: 0x06001BE2 RID: 7138 RVA: 0x0000B878 File Offset: 0x00009A78
		private void method_42(object sender, EventArgs e)
		{
			this.method_204();
		}

		// Token: 0x06001BE3 RID: 7139 RVA: 0x0000B882 File Offset: 0x00009A82
		private void method_43(object sender, EventArgs e)
		{
			this.method_44();
		}

		// Token: 0x06001BE4 RID: 7140 RVA: 0x000BD988 File Offset: 0x000BBB88
		private void method_44()
		{
			decimal d = Base.Trading.CurrOpenTransList.Where(new Func<ShownOpenTrans, bool>(MainForm.<>c.<>9.method_3)).Sum(new Func<ShownOpenTrans, decimal>(MainForm.<>c.<>9.method_4));
			decimal d2 = Math.Round(d);
			Color foreColor;
			if (d2 < 0m)
			{
				foreColor = Class179.color_24;
			}
			else if (d2 > 0m)
			{
				foreColor = Color.Red;
			}
			else if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				foreColor = Class179.color_9;
			}
			else
			{
				foreColor = Class179.color_1;
			}
			this.toolStripStatusLabel_CurrProfits.ForeColor = foreColor;
			this.toolStripStatusLabel_CurrProfits.Text = d.ToString("N0");
		}

		// Token: 0x06001BE5 RID: 7141 RVA: 0x000BDA54 File Offset: 0x000BBC54
		private void method_45()
		{
			if (!Base.UI.Form.IfNotShowAcctInfoOnTransTabHeader && (Base.UI.TransTabCtrl != null || this.bar_AcctTrans.Enabled))
			{
				Base.Trading.smethod_151();
				string text = (Base.Acct.smethod_19() / 1.000000000000000000000m).ToString("N0");
				decimal? num = Base.Acct.smethod_23();
				string text2;
				if (num != null)
				{
					text2 = (Math.Round(num.Value, 0) / 1.00000000000000000000000m).ToString() + "%";
				}
				else
				{
					text2 = "N/A";
				}
				decimal d = Base.Acct.smethod_42();
				decimal d2 = Base.Trading.smethod_178();
				string str = string.Concat(new string[]
				{
					"          可用资金: ",
					text,
					"    仓位: ",
					text2,
					"    动态权益: ",
					(d + d2).ToString("N0")
				});
				if (Base.UI.TransTabCtrl != null)
				{
					Base.UI.TransTabCtrl.PanelHeaderText = " 功能栏" + str;
				}
				if (this.bar_AcctTrans.Enabled)
				{
					this.dockContainer_AcctTrans.Text = "功能栏" + str;
					this.dockContainer_AcctTrans.Visible = true;
				}
			}
			else if (Base.UI.Form.IfNotShowAcctInfoOnTransTabHeader)
			{
				if (Base.UI.TransTabCtrl != null && Base.UI.TransTabCtrl.PanelHeaderText != " 功能栏")
				{
					Base.UI.TransTabCtrl.PanelHeaderText = " 功能栏";
				}
				if (this.bar_AcctTrans.Enabled && this.dockContainer_AcctTrans.Text != "功能栏")
				{
					this.dockContainer_AcctTrans.Text = "功能栏";
				}
			}
		}

		// Token: 0x06001BE6 RID: 7142 RVA: 0x0000B88C File Offset: 0x00009A8C
		private void method_46(DateTime dateTime_0)
		{
			Base.UI.smethod_20(dateTime_0);
			Base.UI.smethod_133();
		}

		// Token: 0x06001BE7 RID: 7143 RVA: 0x0000B89B File Offset: 0x00009A9B
		private void method_47()
		{
			Base.UI.smethod_18();
			Base.UI.smethod_21();
			Base.UI.smethod_133();
		}

		// Token: 0x06001BE8 RID: 7144 RVA: 0x0000B8AE File Offset: 0x00009AAE
		private void method_48()
		{
			if (Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl is ChtCtrl_KLine)
			{
				ChtCtrl_KLine chtCtrl_KLine = Base.UI.SelectedChtCtrl as ChtCtrl_KLine;
				chtCtrl_KLine.method_120(chtCtrl_KLine.HalfNbOfSticks);
			}
		}

		// Token: 0x06001BE9 RID: 7145 RVA: 0x0000B8DA File Offset: 0x00009ADA
		private void method_49()
		{
			if (Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl is ChtCtrl_KLine)
			{
				ChtCtrl_KLine chtCtrl_KLine = Base.UI.SelectedChtCtrl as ChtCtrl_KLine;
				chtCtrl_KLine.method_122(chtCtrl_KLine.HalfNbOfSticks);
			}
		}

		// Token: 0x06001BEA RID: 7146 RVA: 0x0000B906 File Offset: 0x00009B06
		private void method_50()
		{
			if (Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl is ChtCtrl_KLine)
			{
				((ChtCtrl_KLine)Base.UI.SelectedChtCtrl).method_117(false);
			}
		}

		// Token: 0x06001BEB RID: 7147 RVA: 0x0000B92D File Offset: 0x00009B2D
		private void method_51()
		{
			if (Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl is ChtCtrl_KLine)
			{
				((ChtCtrl_KLine)Base.UI.SelectedChtCtrl).method_117(true);
			}
		}

		// Token: 0x06001BEC RID: 7148 RVA: 0x000BDC20 File Offset: 0x000BBE20
		private void method_52()
		{
			if (Base.UI.SelectedChtCtrl != null)
			{
				ChtCtrl selectedChtCtrl = Base.UI.SelectedChtCtrl;
				selectedChtCtrl.IsInCrossReviewMode = true;
				if (selectedChtCtrl.RevCrossXVal != null)
				{
					double num = Math.Round(selectedChtCtrl.RevCrossXVal.Value);
					if (num > 1.0)
					{
						if (selectedChtCtrl is ChtCtrl_KLine)
						{
							if (num > (double)(selectedChtCtrl.IndexOfLastItemShownInScr + 1))
							{
								selectedChtCtrl.RevCrossXVal = new double?((double)(selectedChtCtrl.IndexOfLastItemShownInScr + 1));
							}
							else
							{
								selectedChtCtrl.RevCrossXVal = new double?(num - 1.0);
							}
						}
						else
						{
							ChtCtrl_Tick chtCtrl_Tick = selectedChtCtrl as ChtCtrl_Tick;
							if (num > (double)chtCtrl_Tick.ChtHDList.Count)
							{
								selectedChtCtrl.RevCrossXVal = new double?((double)chtCtrl_Tick.ChtHDList.Count);
							}
							else
							{
								selectedChtCtrl.RevCrossXVal = new double?(num - 1.0);
							}
						}
					}
					else if (selectedChtCtrl is ChtCtrl_KLine)
					{
						(selectedChtCtrl as ChtCtrl_KLine).method_120(1);
					}
					else
					{
						selectedChtCtrl.RevCrossXVal = new double?((double)1f);
					}
				}
				else if (selectedChtCtrl is ChtCtrl_KLine)
				{
					selectedChtCtrl.RevCrossXVal = new double?((double)(selectedChtCtrl.IndexOfLastItemShownInScr + 1));
				}
				else
				{
					selectedChtCtrl.RevCrossXVal = new double?((double)((ChtCtrl_Tick)selectedChtCtrl).ChtHDList.Count);
				}
				selectedChtCtrl.method_25();
				selectedChtCtrl.method_15();
			}
		}

		// Token: 0x06001BED RID: 7149 RVA: 0x000BDD7C File Offset: 0x000BBF7C
		private void method_53()
		{
			if (Base.UI.SelectedChtCtrl != null)
			{
				ChtCtrl selectedChtCtrl = Base.UI.SelectedChtCtrl;
				selectedChtCtrl.IsInCrossReviewMode = true;
				if (selectedChtCtrl.RevCrossXVal != null)
				{
					double num = Math.Round(selectedChtCtrl.RevCrossXVal.Value);
					if (selectedChtCtrl is ChtCtrl_KLine)
					{
						if (num < (double)(selectedChtCtrl.IndexOfLastItemShownInScr + 1))
						{
							selectedChtCtrl.RevCrossXVal = new double?(Math.Floor(selectedChtCtrl.RevCrossXVal.Value) + 1.0);
						}
						else
						{
							((ChtCtrl_KLine)selectedChtCtrl).method_123();
						}
					}
					else if (num < (double)((ChtCtrl_Tick)selectedChtCtrl).ChtHDList.Count)
					{
						selectedChtCtrl.RevCrossXVal = new double?(Math.Floor(selectedChtCtrl.RevCrossXVal.Value) + 1.0);
					}
				}
				else if (selectedChtCtrl is ChtCtrl_KLine)
				{
					((ChtCtrl_KLine)selectedChtCtrl).method_123();
				}
				else
				{
					selectedChtCtrl.RevCrossXVal = new double?((double)((ChtCtrl_Tick)selectedChtCtrl).ChtHDList.Count);
				}
				selectedChtCtrl.method_25();
				selectedChtCtrl.method_15();
			}
		}

		// Token: 0x06001BEE RID: 7150 RVA: 0x0000B954 File Offset: 0x00009B54
		private void method_54()
		{
			if (Base.UI.SelectedChtCtrl != null)
			{
				if (Base.UI.SelectedChtCtrl is ChtCtrl_KLine)
				{
					Base.UI.smethod_149(Base.UI.SelectedChtCtrl);
				}
				else
				{
					Base.UI.smethod_151(Base.UI.SelectedChtCtrl);
				}
			}
		}

		// Token: 0x06001BEF RID: 7151 RVA: 0x000BDE94 File Offset: 0x000BC094
		private void method_55()
		{
			ChtCtrl selectedChtCtrl = Base.UI.SelectedChtCtrl;
			if (selectedChtCtrl != null)
			{
				selectedChtCtrl.IsReverse = !selectedChtCtrl.IsReverse;
			}
		}

		// Token: 0x06001BF0 RID: 7152 RVA: 0x000BDEBC File Offset: 0x000BC0BC
		private void method_56()
		{
			ChtCtrl selectedChtCtrl = Base.UI.SelectedChtCtrl;
			if (selectedChtCtrl != null && selectedChtCtrl is ChtCtrl_KLine)
			{
				ChtCtrl_KLine chtCtrl_KLine = selectedChtCtrl as ChtCtrl_KLine;
				if (chtCtrl_KLine.SelectedInd != null && chtCtrl_KLine.SelectedInd.IsMainChartInd)
				{
					Indicator selectedInd = chtCtrl_KLine.SelectedInd;
					selectedInd.IsSelected = false;
					selectedInd.RemoveFromChart();
				}
				else if (chtCtrl_KLine.SelectedDrawObj != null)
				{
					chtCtrl_KLine.SelectedDrawObj.method_30();
				}
			}
		}

		// Token: 0x06001BF1 RID: 7153 RVA: 0x0000B983 File Offset: 0x00009B83
		private void toolStripBttn_1m_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(1));
		}

		// Token: 0x06001BF2 RID: 7154 RVA: 0x0000B994 File Offset: 0x00009B94
		private void method_57(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(2));
		}

		// Token: 0x06001BF3 RID: 7155 RVA: 0x0000B9A5 File Offset: 0x00009BA5
		private void toolStripBttn_3m_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(3));
		}

		// Token: 0x06001BF4 RID: 7156 RVA: 0x0000B9B6 File Offset: 0x00009BB6
		private void toolStripBttn_5m_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(5));
		}

		// Token: 0x06001BF5 RID: 7157 RVA: 0x0000B9C7 File Offset: 0x00009BC7
		private void toolStripBttn_10m_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(10));
		}

		// Token: 0x06001BF6 RID: 7158 RVA: 0x0000B9D9 File Offset: 0x00009BD9
		private void toolStripBttn_15m_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(15));
		}

		// Token: 0x06001BF7 RID: 7159 RVA: 0x0000B9EB File Offset: 0x00009BEB
		private void toolStripBttn_30m_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(30));
		}

		// Token: 0x06001BF8 RID: 7160 RVA: 0x0000B9FD File Offset: 0x00009BFD
		private void toolStripBttn_1h_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(60));
		}

		// Token: 0x06001BF9 RID: 7161 RVA: 0x0000BA0F File Offset: 0x00009C0F
		private void toolStripBttn_2h_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(120));
		}

		// Token: 0x06001BFA RID: 7162 RVA: 0x0000BA21 File Offset: 0x00009C21
		private void toolStripBttn_4h_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMins, new int?(240));
		}

		// Token: 0x06001BFB RID: 7163 RVA: 0x000BDF20 File Offset: 0x000BC120
		private void toolStripBttn_1d_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByDay, null);
		}

		// Token: 0x06001BFC RID: 7164 RVA: 0x000BDF40 File Offset: 0x000BC140
		private void toolStripBttn_1w_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByWeek, null);
		}

		// Token: 0x06001BFD RID: 7165 RVA: 0x000BDF60 File Offset: 0x000BC160
		private void toolStripBttn_1Mth_Click(object sender, EventArgs e)
		{
			this.method_58(PeriodType.ByMonth, null);
		}

		// Token: 0x06001BFE RID: 7166 RVA: 0x000BDF80 File Offset: 0x000BC180
		private void method_58(PeriodType periodType_0, int? nullable_1)
		{
			MainForm.Class332 @class = new MainForm.Class332();
			@class.periodType_0 = periodType_0;
			@class.nullable_0 = nullable_1;
			if (Base.UI.SelectedChtCtrl != null)
			{
				if (Base.UI.SelectedChtCtrl is ChtCtrl_Tick)
				{
					Base.UI.SelectedChtCtrl = Base.UI.smethod_151(Base.UI.SelectedChtCtrl);
				}
				this.method_59(@class.periodType_0, @class.nullable_0);
				Base.UI.SelectedChtCtrl.Focus();
			}
			else
			{
				List<ChtCtrl> visibleChtCtrlList = Base.UI.VisibleChtCtrlList;
				if (visibleChtCtrlList != null && visibleChtCtrlList.Count > 0)
				{
					List<ChtCtrl> list = visibleChtCtrlList.Where(new Func<ChtCtrl, bool>(MainForm.<>c.<>9.method_5)).ToList<ChtCtrl>();
					if (list.Count > 0)
					{
						ChtCtrl chtCtrl = list.FirstOrDefault(new Func<ChtCtrl, bool>(@class.method_0));
						if (chtCtrl == null)
						{
							chtCtrl = list[0];
						}
						Base.UI.SelectedChtCtrl = chtCtrl;
					}
					else
					{
						ChtCtrl chtCtrl = visibleChtCtrlList[0];
						Base.UI.SelectedChtCtrl = Base.UI.smethod_151(chtCtrl);
					}
					this.method_59(@class.periodType_0, @class.nullable_0);
					Base.UI.SelectedChtCtrl.Focus();
				}
				else if (Base.UI.Chart.IsSingleFixedContent && Base.UI.TransTabs != null)
				{
					Base.UI.SelectedChtCtrl = Base.UI.TransTabs.method_91();
					if (Base.UI.SelectedChtCtrl != null)
					{
						this.method_59(@class.periodType_0, @class.nullable_0);
						Base.UI.SelectedChtCtrl.Focus();
					}
					else
					{
						Class182.smethod_0(new Exception("SwitchToKLineCht failed!"));
					}
				}
			}
		}

		// Token: 0x06001BFF RID: 7167 RVA: 0x000BE0E8 File Offset: 0x000BC2E8
		private void method_59(PeriodType periodType_0, int? nullable_1)
		{
			if (Base.UI.SelectedChtCtrl is ChtCtrl_KLine)
			{
				ChtCtrl_KLine chtCtrl_KLine = Base.UI.SelectedChtCtrl as ChtCtrl_KLine;
				if (chtCtrl_KLine.SymbDataSet.CurrHisDataSet != null && !chtCtrl_KLine.SymbDataSet.IsCurrDateNotListedYet)
				{
					string text = "Type:" + periodType_0.ToString() + "  Unit:" + ((nullable_1 != null) ? nullable_1.Value.ToString() : "null");
					Class46.smethod_3(Class22.PeriodResetting, text);
					if (periodType_0 == PeriodType.ByMins && nullable_1 != null && nullable_1.Value < 60)
					{
						chtCtrl_KLine.SymbDataSet.method_42();
					}
					chtCtrl_KLine.method_12(periodType_0, nullable_1);
					Class46.smethod_3(Class22.PeriodReset, text);
				}
			}
		}

		// Token: 0x06001C00 RID: 7168 RVA: 0x0000BA36 File Offset: 0x00009C36
		private void toolStripBtn_Tick_Click(object sender, EventArgs e)
		{
			this.method_60();
		}

		// Token: 0x06001C01 RID: 7169 RVA: 0x000BE1AC File Offset: 0x000BC3AC
		private void method_60()
		{
			if (Base.UI.SelectedChtCtrl != null)
			{
				if (Base.UI.SelectedChtCtrl is ChtCtrl_KLine)
				{
					Base.UI.SelectedChtCtrl = Base.UI.smethod_149(Base.UI.SelectedChtCtrl);
				}
				Base.UI.SelectedChtCtrl.Focus();
			}
			else
			{
				List<ChtCtrl> visibleChtCtrlList = Base.UI.VisibleChtCtrlList;
				if (visibleChtCtrlList != null && visibleChtCtrlList.Count > 0)
				{
					ChtCtrl chtCtrl = visibleChtCtrlList.FirstOrDefault(new Func<ChtCtrl, bool>(MainForm.<>c.<>9.method_6));
					if (chtCtrl == null)
					{
						chtCtrl = visibleChtCtrlList[0];
						Base.UI.SelectedChtCtrl = Base.UI.smethod_149(chtCtrl);
					}
					else
					{
						Base.UI.SelectedChtCtrl = chtCtrl;
					}
					Base.UI.SelectedChtCtrl.Focus();
				}
				else if (Base.UI.Chart.IsSingleFixedContent && Base.UI.TransTabs != null)
				{
					ChtCtrl_Tick chtCtrl_Tick = Base.UI.TransTabs.method_88();
					if (chtCtrl_Tick != null)
					{
						Base.UI.SelectedChtCtrl = chtCtrl_Tick;
						Base.UI.SelectedChtCtrl.Focus();
					}
				}
			}
		}

		// Token: 0x06001C02 RID: 7170 RVA: 0x000BE288 File Offset: 0x000BC488
		private void method_61(object sender, EventArgs e)
		{
			ButtonItem buttonItem = sender as ButtonItem;
			if (!buttonItem.Checked)
			{
				if (buttonItem.Tag != null && !(buttonItem.Tag.ToString() == string.Empty))
				{
					HisDataPeriodSet hisDataPeriodSet = buttonItem.Tag as HisDataPeriodSet;
					if (hisDataPeriodSet != null)
					{
						Base.UI.Form.AutoPlayPeriodType = new PeriodType?(hisDataPeriodSet.PeriodType);
						Base.UI.Form.AutoPlayPeriodUnits = hisDataPeriodSet.PeriodUnits;
					}
				}
				else
				{
					Base.UI.Form.AutoPlayPeriodType = new PeriodType?(PeriodType.ByMins);
					Base.UI.Form.AutoPlayPeriodUnits = new int?(1);
				}
			}
		}

		// Token: 0x06001C03 RID: 7171 RVA: 0x000BE320 File Offset: 0x000BC520
		private void method_62(ChtCtrl chtCtrl_0 = null)
		{
			Bar bar_ = this.toolStrip_Periods;
			if (chtCtrl_0 != null)
			{
				if (chtCtrl_0 is ChtCtrl_KLine)
				{
					if (chtCtrl_0.HisDataPeriodSet.PeriodType == PeriodType.ByMins)
					{
						int? periodUnits = chtCtrl_0.HisDataPeriodSet.PeriodUnits;
						if (periodUnits.GetValueOrDefault() == 1 & periodUnits != null)
						{
							this.method_63(bar_, 2);
						}
						else
						{
							periodUnits = chtCtrl_0.HisDataPeriodSet.PeriodUnits;
							if (periodUnits.GetValueOrDefault() == 3 & periodUnits != null)
							{
								this.method_63(bar_, 3);
							}
							else
							{
								periodUnits = chtCtrl_0.HisDataPeriodSet.PeriodUnits;
								if (periodUnits.GetValueOrDefault() == 5 & periodUnits != null)
								{
									this.method_63(bar_, 4);
								}
								else
								{
									periodUnits = chtCtrl_0.HisDataPeriodSet.PeriodUnits;
									if (periodUnits.GetValueOrDefault() == 10 & periodUnits != null)
									{
										this.method_63(bar_, 5);
									}
									else
									{
										periodUnits = chtCtrl_0.HisDataPeriodSet.PeriodUnits;
										if (periodUnits.GetValueOrDefault() == 15 & periodUnits != null)
										{
											this.method_63(bar_, 6);
										}
										else
										{
											periodUnits = chtCtrl_0.HisDataPeriodSet.PeriodUnits;
											if (periodUnits.GetValueOrDefault() == 30 & periodUnits != null)
											{
												this.method_63(bar_, 7);
											}
											else
											{
												periodUnits = chtCtrl_0.HisDataPeriodSet.PeriodUnits;
												if (periodUnits.GetValueOrDefault() == 60 & periodUnits != null)
												{
													this.method_63(bar_, 8);
												}
												else
												{
													periodUnits = chtCtrl_0.HisDataPeriodSet.PeriodUnits;
													if (periodUnits.GetValueOrDefault() == 120 & periodUnits != null)
													{
														this.method_63(bar_, 9);
													}
													else
													{
														periodUnits = chtCtrl_0.HisDataPeriodSet.PeriodUnits;
														if (periodUnits.GetValueOrDefault() == 240 & periodUnits != null)
														{
															this.method_63(bar_, 10);
														}
														else
														{
															this.method_63(bar_, 1);
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
					else if (chtCtrl_0.HisDataPeriodSet.PeriodType == PeriodType.ByDay)
					{
						if (chtCtrl_0.HisDataPeriodSet.PeriodUnits != null)
						{
							if (chtCtrl_0.HisDataPeriodSet.PeriodUnits.Value != 1)
							{
								this.method_63(bar_, 1);
								return;
							}
						}
						this.method_63(bar_, 11);
					}
					else if (chtCtrl_0.HisDataPeriodSet.PeriodType == PeriodType.ByWeek)
					{
						this.method_63(bar_, 12);
					}
					else if (chtCtrl_0.HisDataPeriodSet.PeriodType == PeriodType.ByMonth)
					{
						this.method_63(bar_, 13);
					}
					else
					{
						this.method_63(bar_, 1);
					}
				}
				else
				{
					this.method_63(bar_, 0);
				}
			}
			else
			{
				Base.UI.smethod_158(bar_);
			}
		}

		// Token: 0x06001C04 RID: 7172 RVA: 0x0000BA40 File Offset: 0x00009C40
		private void method_63(Bar bar_0, int int_11)
		{
			Base.UI.smethod_158(bar_0);
			((ButtonItem)bar_0.Items[int_11]).Checked = true;
		}

		// Token: 0x06001C05 RID: 7173 RVA: 0x000BE5BC File Offset: 0x000BC7BC
		private void method_64(Enum4 enum4_0)
		{
			int num = 10;
			if (enum4_0 == Enum4.const_0)
			{
				if (Base.UI.Form.NMinsPeriodUnits != null)
				{
					num = Base.UI.Form.NMinsPeriodUnits.Value;
				}
			}
			else if (enum4_0 == Enum4.const_1)
			{
				if (Base.UI.Form.NHoursPeriodUnits != null)
				{
					num = Base.UI.Form.NHoursPeriodUnits.Value;
				}
			}
			else if (Base.UI.Form.NDaysPeriodUnits != null)
			{
				num = Base.UI.Form.NDaysPeriodUnits.Value;
			}
			this.method_204();
			if (this.quickWnd_0 != null)
			{
				this.quickWnd_0.Hide();
			}
			base.BringToFront();
			SetNPeriodWnd setNPeriodWnd = new SetNPeriodWnd(enum4_0, num);
			setNPeriodWnd.Owner = this;
			setNPeriodWnd.NPeriodSet += this.method_65;
			setNPeriodWnd.ShowDialog();
		}

		// Token: 0x06001C06 RID: 7174 RVA: 0x000BE694 File Offset: 0x000BC894
		private void method_65(EventArgs4 eventArgs4_0)
		{
			ChtCtrl selectedChtCtrl = Base.UI.SelectedChtCtrl;
			switch (eventArgs4_0.NPeriodType)
			{
			case Enum4.const_0:
				this.method_58(PeriodType.ByMins, new int?(eventArgs4_0.Period));
				Base.UI.Form.NMinsPeriodUnits = new int?(eventArgs4_0.Period);
				break;
			case Enum4.const_1:
				this.method_58(PeriodType.ByMins, new int?(eventArgs4_0.Period * 60));
				Base.UI.Form.NHoursPeriodUnits = new int?(eventArgs4_0.Period);
				break;
			case Enum4.const_2:
				this.method_58(PeriodType.ByDay, new int?(eventArgs4_0.Period));
				Base.UI.Form.NDaysPeriodUnits = new int?(eventArgs4_0.Period);
				break;
			}
		}

		// Token: 0x06001C07 RID: 7175 RVA: 0x000BE740 File Offset: 0x000BC940
		private void toolStripBtn_NPeriod_ToolTipVisibleChanged(object sender, EventArgs e)
		{
			if (this.toolStripBtn_NPeriod.ToolTipControl.Visible)
			{
				if (this.toolStripBtn_NPeriod.Checked)
				{
					this.toolStripBtn_NPeriod.ToolTipControl.Text = "自定义周期(" + Base.UI.SelectedChtCtrl.HisDataPeriodSet.PeriodDesc + ")";
				}
				else
				{
					this.toolStripBtn_NPeriod.ToolTipControl.Text = "自定义周期";
				}
			}
		}

		// Token: 0x06001C08 RID: 7176 RVA: 0x000BE7B4 File Offset: 0x000BC9B4
		private void toolStripBtn_NPeriod_ExpandChange(object sender, EventArgs e)
		{
			if (this.toolStripBtn_NPeriod.Expanded)
			{
				if (Base.UI.Form.NMinsPeriodUnits != null)
				{
					this.txtBoxItem_NMins.Text = Base.UI.Form.NMinsPeriodUnits.Value.ToString();
				}
				if (Base.UI.Form.NHoursPeriodUnits != null)
				{
					this.txtBoxItem_NHours.Text = Base.UI.Form.NHoursPeriodUnits.Value.ToString();
				}
				if (Base.UI.Form.NDaysPeriodUnits != null)
				{
					this.txtBoxItem_NDays.Text = Base.UI.Form.NDaysPeriodUnits.Value.ToString();
				}
				this.txtBoxItem_NMins.TextBox.BorderStyle = BorderStyle.FixedSingle;
				this.txtBoxItem_NHours.TextBox.BorderStyle = BorderStyle.FixedSingle;
				this.txtBoxItem_NDays.TextBox.BorderStyle = BorderStyle.FixedSingle;
				this.txtBoxItem_NMins.TextAlign = HorizontalAlignment.Right;
				this.txtBoxItem_NHours.TextAlign = HorizontalAlignment.Right;
				this.txtBoxItem_NDays.TextAlign = HorizontalAlignment.Right;
				this.txtBoxItem_NMins.KeyDown += this.txtBoxItem_NMins_KeyDown;
				this.txtBoxItem_NHours.KeyDown += this.txtBoxItem_NHours_KeyDown;
				this.txtBoxItem_NDays.KeyDown += this.txtBoxItem_NDays_KeyDown;
				ChtCtrl selectedChtCtrl = Base.UI.SelectedChtCtrl;
				if (selectedChtCtrl is ChtCtrl_KLine)
				{
					if (selectedChtCtrl.PeriodType == PeriodType.ByMins && selectedChtCtrl.PeriodUnits != null)
					{
						try
						{
							int num = Convert.ToInt32(this.txtBoxItem_NMins.Text);
							int num2 = Convert.ToInt32(this.txtBoxItem_NHours.Text);
							if (Utility.CanExactDiv(selectedChtCtrl.PeriodUnits.Value, 60) && num2 == selectedChtCtrl.PeriodUnits.Value / 60)
							{
								this.toolStripBtnItem_NHours.Checked = true;
							}
							else if (selectedChtCtrl.PeriodUnits.Value == num)
							{
								this.toolStripBtnItem_NMins.Checked = true;
							}
							return;
						}
						catch (Exception exception_)
						{
							Class182.smethod_0(exception_);
							return;
						}
					}
					if (selectedChtCtrl.PeriodType == PeriodType.ByDay && selectedChtCtrl.PeriodUnits != null)
					{
						try
						{
							int num3 = Convert.ToInt32(this.txtBoxItem_NDays.Text);
							if (selectedChtCtrl.PeriodUnits.Value == num3)
							{
								this.toolStripBtnItem_NDays.Checked = true;
							}
						}
						catch (Exception exception_2)
						{
							Class182.smethod_0(exception_2);
						}
					}
				}
			}
		}

		// Token: 0x06001C09 RID: 7177 RVA: 0x000BEA3C File Offset: 0x000BCC3C
		private void toolStripBtnItem_NMins_Click(object sender, EventArgs e)
		{
			string text = this.txtBoxItem_NMins.Text;
			if (text.Length > 0)
			{
				try
				{
					int value = Convert.ToInt32(text);
					this.method_58(PeriodType.ByMins, new int?(value));
					Base.UI.Form.NMinsPeriodUnits = new int?(value);
					return;
				}
				catch
				{
				}
			}
			MessageBox.Show("请输入有效的自定义分钟数！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
		}

		// Token: 0x06001C0A RID: 7178 RVA: 0x000BEAAC File Offset: 0x000BCCAC
		private void toolStripBtnItem_NHours_Click(object sender, EventArgs e)
		{
			string text = this.txtBoxItem_NHours.Text;
			if (text.Length > 0)
			{
				try
				{
					int num = Convert.ToInt32(text);
					this.method_58(PeriodType.ByMins, new int?(num * 60));
					Base.UI.Form.NHoursPeriodUnits = new int?(num);
					return;
				}
				catch
				{
				}
			}
			MessageBox.Show("请输入有效的自定义小时数！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
		}

		// Token: 0x06001C0B RID: 7179 RVA: 0x000BEB20 File Offset: 0x000BCD20
		private void toolStripBtnItem_NDays_Click(object sender, EventArgs e)
		{
			string text = this.txtBoxItem_NDays.Text;
			if (text.Length > 0)
			{
				try
				{
					int value = Convert.ToInt32(text);
					this.method_58(PeriodType.ByDay, new int?(value));
					Base.UI.Form.NDaysPeriodUnits = new int?(value);
					return;
				}
				catch
				{
				}
			}
			MessageBox.Show("请输入有效的自定义天数！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
		}

		// Token: 0x06001C0C RID: 7180 RVA: 0x0000BA61 File Offset: 0x00009C61
		private void txtBoxItem_NMins_InputTextChanged(object object_0)
		{
			this.toolStripBtnItem_NMins.Checked = false;
		}

		// Token: 0x06001C0D RID: 7181 RVA: 0x0000BA71 File Offset: 0x00009C71
		private void txtBoxItem_NHours_InputTextChanged(object object_0)
		{
			this.toolStripBtnItem_NHours.Checked = false;
		}

		// Token: 0x06001C0E RID: 7182 RVA: 0x0000BA81 File Offset: 0x00009C81
		private void txtBoxItem_NDays_InputTextChanged(object object_0)
		{
			this.toolStripBtnItem_NDays.Checked = false;
		}

		// Token: 0x06001C0F RID: 7183 RVA: 0x0000BA91 File Offset: 0x00009C91
		private void txtBoxItem_NMins_KeyDown(object sender, KeyEventArgs e)
		{
			if (e.KeyCode == Keys.Return && !this.toolStripBtnItem_NMins.Checked)
			{
				this.toolStripBtnItem_NMins_Click(this.toolStripBtnItem_NMins, null);
				this.toolStripBtn_NPeriod.Expanded = false;
			}
		}

		// Token: 0x06001C10 RID: 7184 RVA: 0x0000BAC5 File Offset: 0x00009CC5
		private void txtBoxItem_NHours_KeyDown(object sender, KeyEventArgs e)
		{
			if (e.KeyCode == Keys.Return && !this.toolStripBtnItem_NHours.Checked)
			{
				this.toolStripBtnItem_NHours_Click(this.toolStripBtnItem_NHours, null);
				this.toolStripBtn_NPeriod.Expanded = false;
			}
		}

		// Token: 0x06001C11 RID: 7185 RVA: 0x0000BAF9 File Offset: 0x00009CF9
		private void txtBoxItem_NDays_KeyDown(object sender, KeyEventArgs e)
		{
			if (e.KeyCode == Keys.Return && !this.toolStripBtnItem_NDays.Checked)
			{
				this.toolStripBtnItem_NDays_Click(this.toolStripBtnItem_NDays, null);
				this.toolStripBtn_NPeriod.Expanded = false;
			}
		}

		// Token: 0x06001C12 RID: 7186 RVA: 0x000BEB90 File Offset: 0x000BCD90
		private bool method_66(HisData hisData_0)
		{
			bool result;
			try
			{
				this.numericUpDown_Price.Value = Convert.ToDecimal(hisData_0.Close) / 1.000000000000000000000000000m;
				result = true;
				goto IL_41;
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
			return false;
			IL_41:
			return result;
		}

		// Token: 0x06001C13 RID: 7187 RVA: 0x000BEBF4 File Offset: 0x000BCDF4
		private void method_67()
		{
			if (Base.Data.smethod_124(Base.UI.CurrTradingSymbol))
			{
				this.toolStripBttn_Short.Enabled = true;
				this.toolStripBttn_ClsShort.Enabled = true;
			}
			else
			{
				this.toolStripBttn_Short.Enabled = false;
				this.toolStripBttn_ClsShort.Enabled = false;
			}
		}

		// Token: 0x06001C14 RID: 7188 RVA: 0x000BEC44 File Offset: 0x000BCE44
		private void method_68()
		{
			if (Base.UI.CurrTradingSymbol != null && Base.UI.CurrTradingSymbol.IsStock)
			{
				this.toolStripBttn_Long.Text = "买入";
				this.toolStripBttn_ClsLong.Text = "卖出";
				this.toolStripBttn_Short.Text = "卖出";
				this.toolStripBttn_ClsShort.Text = "买入";
			}
			else
			{
				this.toolStripBttn_Long.Text = "买开";
				this.toolStripBttn_ClsLong.Text = "卖平";
				this.toolStripBttn_Short.Text = "卖开";
				this.toolStripBttn_ClsShort.Text = "买平";
			}
		}

		// Token: 0x06001C15 RID: 7189 RVA: 0x000BECE8 File Offset: 0x000BCEE8
		private void method_69()
		{
			if (Base.UI.CurrTradingSymbol != null)
			{
				if (this.numericUpDown_Price.Increment != Base.UI.CurrTradingSymbol.LeastPriceVar.Value)
				{
					this.numericUpDown_Price.Increment = Base.UI.CurrTradingSymbol.LeastPriceVar.Value;
				}
				if (this.numericUpDown_Price.Value == 0m)
				{
					this.numericUpDown_Price.DecimalPlaces = 0;
				}
				else
				{
					this.numericUpDown_Price.DecimalPlaces = Base.UI.CurrTradingSymbol.DigitNb;
				}
			}
		}

		// Token: 0x06001C16 RID: 7190 RVA: 0x000BED7C File Offset: 0x000BCF7C
		private void toolStripBttn_Long_Click(object sender, EventArgs e)
		{
			decimal value = this.numericUpDown_Price.Value;
			this.method_135(OrderType.Order_OpenLong, value, true);
		}

		// Token: 0x06001C17 RID: 7191 RVA: 0x000BEDA0 File Offset: 0x000BCFA0
		private void toolStripBttn_ClsLong_Click(object sender, EventArgs e)
		{
			decimal value = this.numericUpDown_Price.Value;
			this.method_135(OrderType.Order_CloseLong, value, true);
		}

		// Token: 0x06001C18 RID: 7192 RVA: 0x000BEDC4 File Offset: 0x000BCFC4
		private void toolStripBttn_Short_Click(object sender, EventArgs e)
		{
			decimal value = this.numericUpDown_Price.Value;
			this.method_135(OrderType.Order_OpenShort, value, true);
		}

		// Token: 0x06001C19 RID: 7193 RVA: 0x000BEDE8 File Offset: 0x000BCFE8
		private void toolStripBttn_ClsShort_Click(object sender, EventArgs e)
		{
			decimal value = this.numericUpDown_Price.Value;
			this.method_135(OrderType.Order_CloseShort, value, true);
		}

		// Token: 0x06001C1A RID: 7194 RVA: 0x0000BB2D File Offset: 0x00009D2D
		private void toolStripBttn_Long_ToolTipVisibleChanged(object sender, EventArgs e)
		{
			this.method_70(sender as ButtonItem);
		}

		// Token: 0x06001C1B RID: 7195 RVA: 0x0000BB2D File Offset: 0x00009D2D
		private void toolStripBttn_Short_ToolTipVisibleChanged(object sender, EventArgs e)
		{
			this.method_70(sender as ButtonItem);
		}

		// Token: 0x06001C1C RID: 7196 RVA: 0x0000BB2D File Offset: 0x00009D2D
		private void toolStripBttn_ClsLong_ToolTipVisibleChanged(object sender, EventArgs e)
		{
			this.method_70(sender as ButtonItem);
		}

		// Token: 0x06001C1D RID: 7197 RVA: 0x0000BB2D File Offset: 0x00009D2D
		private void toolStripBttn_ClsShort_ToolTipVisibleChanged(object sender, EventArgs e)
		{
			this.method_70(sender as ButtonItem);
		}

		// Token: 0x06001C1E RID: 7198 RVA: 0x000BEE0C File Offset: 0x000BD00C
		private void method_70(ButtonItem buttonItem_0)
		{
			if (buttonItem_0.Enabled && (!Base.UI.Form.IsInBlindTestMode || Base.UI.Form.IsSingleBlindTest))
			{
				buttonItem_0.Tooltip = string.Concat(new string[]
				{
					buttonItem_0.Text,
					Base.UI.CurrTradingSymbol.CNName,
					"(",
					Base.UI.CurrTradingSymbol.Code,
					")"
				});
			}
			else
			{
				buttonItem_0.Tooltip = buttonItem_0.Text;
			}
		}

		// Token: 0x06001C1F RID: 7199 RVA: 0x000BEE90 File Offset: 0x000BD090
		private void method_71()
		{
			string str = Base.UI.smethod_174(Base.UI.Form.AutoPlayPeriodType.Value, Base.UI.Form.AutoPlayPeriodUnits);
			this.toolStripBtnItem_Start.Tooltip = "自动回放(步进单位:" + str + ")";
		}

		// Token: 0x06001C20 RID: 7200 RVA: 0x000BEEDC File Offset: 0x000BD0DC
		private void toolStripBtnItem_Start_PopupOpen(object sender, PopupOpenEventArgs e)
		{
			if (Base.UI.ChtCtrlList != null)
			{
				this.toolStripBtnItem_Start.SubItems.Clear();
				ButtonItem buttonItem = new ButtonItem();
				buttonItem.Text = "步进单位：1分钟";
				buttonItem.Click += this.method_61;
				PeriodType? autoPlayPeriodType = Base.UI.Form.AutoPlayPeriodType;
				if (autoPlayPeriodType.GetValueOrDefault() == PeriodType.ByMins & autoPlayPeriodType != null)
				{
					int? num = Base.UI.Form.AutoPlayPeriodUnits;
					if (num.GetValueOrDefault() == 1 & num != null)
					{
						buttonItem.Checked = true;
					}
				}
				this.toolStripBtnItem_Start.SubItems.Add(buttonItem);
				List<HisDataPeriodSet> list = new List<HisDataPeriodSet>();
				using (IEnumerator<ChtCtrl> enumerator = Base.UI.ChtCtrlList.Where(new Func<ChtCtrl, bool>(MainForm.<>c.<>9.method_7)).OrderBy(new Func<ChtCtrl, PeriodType>(MainForm.<>c.<>9.method_8)).ThenBy(new Func<ChtCtrl, int?>(MainForm.<>c.<>9.method_9)).GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						MainForm.Class333 @class = new MainForm.Class333();
						@class.chtCtrl_0 = enumerator.Current;
						if (!@class.chtCtrl_0.HisDataPeriodSet.IsPeriod1m && !list.Exists(new Predicate<HisDataPeriodSet>(@class.method_0)))
						{
							ButtonItem buttonItem2 = new ButtonItem();
							buttonItem2.Text = "步进单位：" + @class.chtCtrl_0.HisDataPeriodSet.PeriodDesc;
							buttonItem2.Tag = @class.chtCtrl_0.HisDataPeriodSet;
							buttonItem2.Click += this.method_61;
							PeriodType periodType = @class.chtCtrl_0.PeriodType;
							autoPlayPeriodType = Base.UI.Form.AutoPlayPeriodType;
							if (periodType == autoPlayPeriodType.GetValueOrDefault() & autoPlayPeriodType != null)
							{
								int? num = @class.chtCtrl_0.PeriodUnits;
								int? autoPlayPeriodUnits = Base.UI.Form.AutoPlayPeriodUnits;
								if (num.GetValueOrDefault() == autoPlayPeriodUnits.GetValueOrDefault() & num != null == (autoPlayPeriodUnits != null))
								{
									buttonItem2.Checked = true;
								}
							}
							list.Add(@class.chtCtrl_0.HisDataPeriodSet);
							this.toolStripBtnItem_Start.SubItems.Add(buttonItem2);
						}
					}
				}
			}
		}

		// Token: 0x06001C21 RID: 7201 RVA: 0x000BF168 File Offset: 0x000BD368
		private void timer_0_Tick(object sender, EventArgs e)
		{
			this.int_0++;
			if (this.int_0 > 25)
			{
				if (Utility.GetKeyDown(Keys.Left) && Control.ModifierKeys != Keys.Shift && Control.ModifierKeys != Keys.Control)
				{
					this.method_52();
				}
				else if (Utility.GetKeyDown(Keys.Right) && Control.ModifierKeys != Keys.Shift && Control.ModifierKeys != Keys.Control)
				{
					this.method_53();
				}
				else
				{
					this.int_0 = 0;
				}
			}
		}

		// Token: 0x06001C22 RID: 7202 RVA: 0x000BF1E8 File Offset: 0x000BD3E8
		private void slider_Speed_ValueChanged(object sender, EventArgs e)
		{
			this.slider_Speed.Text = this.slider_Speed.Value.ToString();
			this.method_205();
		}

		// Token: 0x06001C23 RID: 7203 RVA: 0x000BF21C File Offset: 0x000BD41C
		private void controlContainerItem_Units_MouseEnter(object sender, EventArgs e)
		{
			long num = Base.Trading.smethod_207(Base.UI.CurrTradingSymbol);
			if (num < 0L)
			{
				num = 0L;
			}
			this.toolTip_0.SetToolTip(this.numericUpDown_Units, "可开: " + num.ToString());
		}

		// Token: 0x06001C24 RID: 7204 RVA: 0x0000BB3D File Offset: 0x00009D3D
		private void controlContainerItem_Units_MouseLeave(object sender, EventArgs e)
		{
			this.toolTip_0.SetToolTip(this.numericUpDown_Units, null);
		}

		// Token: 0x06001C25 RID: 7205 RVA: 0x0000BB53 File Offset: 0x00009D53
		private void btnItem_热键定义_Click(object sender, EventArgs e)
		{
			new HotKeyCfgForm
			{
				Owner = this,
				StartPosition = FormStartPosition.CenterParent
			}.ShowDialog();
		}

		// Token: 0x06001C26 RID: 7206 RVA: 0x00006687 File Offset: 0x00004887
		private void btnItem_画线下单_Click(object sender, EventArgs e)
		{
			Base.UI.smethod_116();
		}

		// Token: 0x06001C27 RID: 7207 RVA: 0x0000BB70 File Offset: 0x00009D70
		private void btnItem_指标管理_Click(object sender, EventArgs e)
		{
			new FormIndMgr().ShowDialog();
		}

		// Token: 0x06001C28 RID: 7208 RVA: 0x0000BB7F File Offset: 0x00009D7F
		private void toolStripDropDownButton_Symbls_PopupOpen(object sender, EventArgs e)
		{
			this.method_76(this.toolStripDropDownButton_Symbls);
		}

		// Token: 0x06001C29 RID: 7209 RVA: 0x0000BB8F File Offset: 0x00009D8F
		private void 关于ToolStripMenuItem_Click(object sender, EventArgs e)
		{
			new AboutForm
			{
				Owner = this
			}.ShowDialog();
		}

		// Token: 0x06001C2A RID: 7210 RVA: 0x0000BBA5 File Offset: 0x00009DA5
		private void btnItem_品种设置_Click(object sender, EventArgs e)
		{
			new SetSymbParamForm
			{
				Owner = this,
				StartPosition = FormStartPosition.CenterParent
			}.ShowDialog();
		}

		// Token: 0x06001C2B RID: 7211 RVA: 0x000BF270 File Offset: 0x000BD470
		private void btnItem_Help_Click(object sender, EventArgs e)
		{
			try
			{
				Process.Start(".\\TExHelp.chm");
			}
			catch
			{
				MessageBox.Show("无法找到帮助文件<TExHelp.chm>。", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
		}

		// Token: 0x06001C2C RID: 7212 RVA: 0x000BF2B4 File Offset: 0x000BD4B4
		private void btnItem_VideoHelp_Click(object sender, EventArgs e)
		{
			try
			{
				string str = "tex";
				Process.Start(new ProcessStartInfo("https://www.tradingexer.com/help/video/" + str));
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x06001C2D RID: 7213 RVA: 0x000BF2F8 File Offset: 0x000BD4F8
		private void btnItem_TExWeb_Click(object sender, EventArgs e)
		{
			try
			{
				string str = "";
				Process.Start(new ProcessStartInfo("https://www.tradingexer.com" + str));
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x06001C2E RID: 7214 RVA: 0x0000BBC2 File Offset: 0x00009DC2
		private void btnItem_AppUpg_Click(object sender, EventArgs e)
		{
			this.method_12(true, true);
		}

		// Token: 0x06001C2F RID: 7215 RVA: 0x0000BBCF File Offset: 0x00009DCF
		private void btnItem_系统参数_Click(object sender, EventArgs e)
		{
			this.method_204();
			SettingsForm settingsForm = new SettingsForm();
			settingsForm.UISettingsConfirmed += this.method_110;
			settingsForm.Owner = this;
			settingsForm.ShowInTaskbar = false;
			settingsForm.StartPosition = FormStartPosition.CenterParent;
			settingsForm.ShowDialog();
		}

		// Token: 0x06001C30 RID: 7216 RVA: 0x0000BC0B File Offset: 0x00009E0B
		private void 工具栏ToolStripMenuItem_Click(object sender, EventArgs e)
		{
			this.method_72();
		}

		// Token: 0x06001C31 RID: 7217 RVA: 0x0000BC15 File Offset: 0x00009E15
		private void method_72()
		{
			if (this.dockSite_ToolBar.Visible)
			{
				this.dockSite_ToolBar.Visible = false;
			}
			else
			{
				this.dockSite_ToolBar.Visible = true;
			}
		}

		// Token: 0x06001C32 RID: 7218 RVA: 0x0000BC40 File Offset: 0x00009E40
		private void 功能栏ToolStripMenuItem_Click(object sender, EventArgs e)
		{
			if (Base.UI.Form.IfShowTransTabsBar)
			{
				this.method_73();
			}
			else
			{
				this.method_74();
			}
		}

		// Token: 0x06001C33 RID: 7219 RVA: 0x0000BC5E File Offset: 0x00009E5E
		private void method_73()
		{
			this.功能栏ToolStripMenuItem.Checked = false;
			this.bar_AcctTrans.Hide();
			Base.UI.IsTransTabsVisible = false;
			Base.UI.Form.IfShowTransTabsBar = false;
		}

		// Token: 0x06001C34 RID: 7220 RVA: 0x000BF33C File Offset: 0x000BD53C
		private void method_74()
		{
			if (this.bar_AcctTrans.Enabled)
			{
				this.功能栏ToolStripMenuItem.Checked = true;
				if (this.bar_AcctTrans.DockSide != Base.UI.Form.AcctTransBar_DockSide)
				{
					this.bar_AcctTrans.DockSide = Base.UI.Form.AcctTransBar_DockSide;
				}
				if (this.panelDockContainer_AcctTrans.Controls.Count < 1)
				{
					Base.UI.TransTabs = new TransTabs(null);
					this.panelDockContainer_AcctTrans.Controls.Add(Base.UI.TransTabs);
				}
				this.bar_AcctTrans.Show();
				this.panelDockContainer_AcctTrans.Visible = true;
				Base.UI.IsTransTabsVisible = true;
				Base.UI.Form.IfShowTransTabsBar = true;
			}
		}

		// Token: 0x06001C35 RID: 7221 RVA: 0x000BF3F0 File Offset: 0x000BD5F0
		private void btnItem_交易账户_PopupOpen(object sender, EventArgs e)
		{
			ButtonItem buttonItem = this.btnItem_交易账户;
			buttonItem.SubItems.Clear();
			buttonItem.SubItems.Add(this.新建账户ToolStripMenuItem);
			buttonItem.SubItems.Add(this.切换账户ToolStripMenuItem);
			buttonItem.SubItems.Add(this.删除账户ToolStripMenuItem);
			this.method_187(this.切换账户ToolStripMenuItem, true);
			this.删除账户ToolStripMenuItem.SubItems.Clear();
			foreach (Account account in Base.Acct.CurrAccounts)
			{
				ButtonItem buttonItem2 = new ButtonItem();
				buttonItem2.Text = account.AcctName;
				buttonItem2.Tag = account.ID;
				buttonItem2.Tooltip = account.Notes;
				buttonItem2.Click += this.method_75;
				if (account.ID == Base.Acct.CurrAccount.ID)
				{
					ButtonItem buttonItem3 = buttonItem2;
					buttonItem3.Text += "(当前账户)";
					buttonItem2.Enabled = false;
				}
				this.删除账户ToolStripMenuItem.SubItems.Add(buttonItem2);
			}
		}

		// Token: 0x06001C36 RID: 7222 RVA: 0x000BF52C File Offset: 0x000BD72C
		private void method_75(object sender, EventArgs e)
		{
			ButtonItem buttonItem = (ButtonItem)sender;
			if (MessageBox.Show(string.Concat(new string[]
			{
				"确认删除账户'",
				buttonItem.Text,
				"'吗？",
				Environment.NewLine,
				"（请注意：账户一旦删除，所有在该账户下生成的交易记录、画线等都会被清除。）"
			}), "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes && Base.Acct.smethod_8(Convert.ToInt32(buttonItem.Tag)))
			{
				MessageBox.Show("账户'" + buttonItem.Text + "'已被成功删除。", "请确认", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
			}
		}

		// Token: 0x06001C37 RID: 7223 RVA: 0x000BF5BC File Offset: 0x000BD7BC
		private void toolStripDropDownButton_Settings_PopupOpen(object sender, EventArgs e)
		{
			if (this.dockSite_ToolBar.Visible)
			{
				this.工具栏ToolStripMenuItem.Checked = true;
			}
			else
			{
				this.工具栏ToolStripMenuItem.Checked = false;
			}
			if (this.bar_AcctTrans.Enabled)
			{
				this.功能栏ToolStripMenuItem.Enabled = true;
				if (this.bar_AcctTrans.Visible)
				{
					this.功能栏ToolStripMenuItem.Checked = true;
				}
				else
				{
					this.功能栏ToolStripMenuItem.Checked = false;
				}
			}
			else
			{
				this.功能栏ToolStripMenuItem.Checked = true;
				this.功能栏ToolStripMenuItem.Enabled = false;
			}
			if (Base.UI.DrawToolWnd != null)
			{
				this.btnItem_画线工具.Checked = true;
			}
			else
			{
				this.btnItem_画线工具.Checked = false;
			}
			if (Base.UI.DrawOdrWnd != null)
			{
				this.btnItem_画线下单.Checked = true;
			}
			else
			{
				this.btnItem_画线下单.Checked = false;
			}
		}

		// Token: 0x06001C38 RID: 7224 RVA: 0x000BF690 File Offset: 0x000BD890
		private void method_76(ButtonItem buttonItem_0)
		{
			buttonItem_0.SubItems.Clear();
			if (Base.Data.CurrExchangeList.Count > 1)
			{
				if (TApp.IsStIncluded)
				{
					List<StkSymbol> source = Base.Data.smethod_98();
					List<StkSymbol> source2 = Base.Data.smethod_100(false);
					List<StkSymbol> source3 = Base.Data.smethod_101(false);
					ExchgHouse exchgHouse = Base.Data.CurrExchangeList.SingleOrDefault(new Func<ExchgHouse, bool>(MainForm.<>c.<>9.method_10));
					if (exchgHouse != null)
					{
						ButtonItem buttonItem = new ButtonItem();
						buttonItem.Text = exchgHouse.AbbrName_CN;
						IEnumerable<StkSymbol> enumerable = source3.Where(new Func<StkSymbol, bool>(MainForm.<>c.<>9.method_11));
						if (enumerable.Any<StkSymbol>())
						{
							ButtonItem buttonItem2 = new ButtonItem();
							buttonItem2.Text = "主板";
							this.method_79(buttonItem2, enumerable);
							buttonItem.SubItems.Add(buttonItem2);
						}
						IEnumerable<StkSymbol> enumerable2 = source.Where(new Func<StkSymbol, bool>(MainForm.<>c.<>9.method_12));
						if (enumerable2.Any<StkSymbol>())
						{
							ButtonItem buttonItem3 = new ButtonItem();
							buttonItem3.Text = "指数";
							this.method_79(buttonItem3, enumerable2);
							buttonItem.SubItems.Add(buttonItem3);
						}
						IEnumerable<StkSymbol> enumerable3 = source2.Where(new Func<StkSymbol, bool>(MainForm.<>c.<>9.method_13));
						if (enumerable3.Any<StkSymbol>())
						{
							ButtonItem buttonItem4 = new ButtonItem();
							buttonItem4.Text = "基金";
							this.method_79(buttonItem4, enumerable3);
							buttonItem.SubItems.Add(buttonItem4);
						}
						List<StkSymbol> list = Base.Data.smethod_106(false);
						if (list.Any<StkSymbol>())
						{
							ButtonItem buttonItem5 = new ButtonItem();
							buttonItem5.Text = "科创版";
							this.method_79(buttonItem5, list);
							buttonItem.SubItems.Add(buttonItem5);
						}
						List<StkSymbol> list2 = Base.Data.smethod_107(new int?(5), false);
						if (list2.Any<StkSymbol>())
						{
							ButtonItem buttonItem6 = new ButtonItem();
							buttonItem6.Text = "可转债";
							this.method_79(buttonItem6, list2);
							buttonItem.SubItems.Add(buttonItem6);
						}
						buttonItem_0.SubItems.Add(buttonItem);
					}
					exchgHouse = Base.Data.CurrExchangeList.SingleOrDefault(new Func<ExchgHouse, bool>(MainForm.<>c.<>9.method_14));
					if (exchgHouse != null)
					{
						ButtonItem buttonItem7 = new ButtonItem();
						buttonItem7.Text = exchgHouse.AbbrName_CN;
						IEnumerable<StkSymbol> enumerable4 = source3.Where(new Func<StkSymbol, bool>(MainForm.<>c.<>9.method_15));
						if (enumerable4.Any<StkSymbol>())
						{
							ButtonItem buttonItem8 = new ButtonItem();
							buttonItem8.Text = "主板";
							this.method_79(buttonItem8, enumerable4);
							buttonItem7.SubItems.Add(buttonItem8);
						}
						List<StkSymbol> list3 = Base.Data.smethod_104(false);
						if (list3.Any<StkSymbol>())
						{
							ButtonItem buttonItem9 = new ButtonItem();
							buttonItem9.Text = "中小板";
							this.method_79(buttonItem9, list3);
							buttonItem7.SubItems.Add(buttonItem9);
						}
						List<StkSymbol> list4 = Base.Data.smethod_105(false);
						if (list4.Any<StkSymbol>())
						{
							ButtonItem buttonItem10 = new ButtonItem();
							buttonItem10.Text = "创业板";
							this.method_79(buttonItem10, list4);
							buttonItem7.SubItems.Add(buttonItem10);
						}
						IEnumerable<StkSymbol> enumerable5 = source.Where(new Func<StkSymbol, bool>(MainForm.<>c.<>9.method_16));
						if (enumerable5.Any<StkSymbol>())
						{
							ButtonItem buttonItem11 = new ButtonItem();
							buttonItem11.Text = "指数";
							this.method_79(buttonItem11, enumerable5);
							buttonItem7.SubItems.Add(buttonItem11);
						}
						IEnumerable<StkSymbol> enumerable6 = source2.Where(new Func<StkSymbol, bool>(MainForm.<>c.<>9.method_17));
						if (enumerable6.Any<StkSymbol>())
						{
							ButtonItem buttonItem12 = new ButtonItem();
							buttonItem12.Text = "基金";
							this.method_79(buttonItem12, enumerable6);
							buttonItem7.SubItems.Add(buttonItem12);
						}
						List<StkSymbol> list5 = Base.Data.smethod_107(new int?(6), false);
						if (list5.Any<StkSymbol>())
						{
							ButtonItem buttonItem13 = new ButtonItem();
							buttonItem13.Text = "可转债";
							this.method_79(buttonItem13, list5);
							buttonItem7.SubItems.Add(buttonItem13);
						}
						buttonItem_0.SubItems.Add(buttonItem7);
					}
				}
				if (TApp.IsFtIncluded)
				{
					bool bool_ = buttonItem_0.SubItems.Count > 0;
					this.method_77(1, buttonItem_0, bool_);
					this.method_77(2, buttonItem_0, false);
					this.method_77(4, buttonItem_0, false);
					this.method_77(3, buttonItem_0, false);
					this.method_77(0, buttonItem_0, false);
					this.method_77(-1, buttonItem_0, false);
				}
			}
			else
			{
				this.method_79(buttonItem_0, Base.Data.UsrStkSymbols.Values);
			}
			if (Base.UI.TransTabs != null)
			{
				List<StkSymbol> zixuanStkSymbList = Base.UI.TransTabs.ZixuanStkSymbList;
				if (zixuanStkSymbList != null)
				{
					ButtonItem buttonItem14 = new ButtonItem();
					buttonItem14.Text = "自选板块";
					buttonItem14.BeginGroup = true;
					this.method_79(buttonItem14, zixuanStkSymbList);
					buttonItem_0.SubItems.Add(buttonItem14);
				}
			}
		}

		// Token: 0x06001C39 RID: 7225 RVA: 0x000BFB8C File Offset: 0x000BDD8C
		private void method_77(int int_11, ButtonItem buttonItem_0, bool bool_8 = false)
		{
			MainForm.Class334 @class = new MainForm.Class334();
			@class.int_0 = int_11;
			@class.exchgHouse_0 = Base.Data.CurrExchangeList.SingleOrDefault(new Func<ExchgHouse, bool>(@class.method_0));
			if (@class.exchgHouse_0 != null)
			{
				ButtonItem buttonItem = new ButtonItem();
				buttonItem.Text = @class.exchgHouse_0.AbbrName_CN;
				IEnumerable<StkSymbol> enumerable = Base.Data.UsrStkSymbols.Values.Where(new Func<StkSymbol, bool>(@class.method_1));
				if (enumerable != null && enumerable.Any<StkSymbol>())
				{
					this.method_78(buttonItem, enumerable);
					buttonItem_0.SubItems.Add(buttonItem);
					if (bool_8)
					{
						buttonItem.BeginGroup = true;
					}
				}
			}
		}

		// Token: 0x06001C3A RID: 7226 RVA: 0x000BFC2C File Offset: 0x000BDE2C
		private void method_78(ButtonItem buttonItem_0, IEnumerable<StkSymbol> ienumerable_0)
		{
			foreach (string text in ienumerable_0.Select(new Func<StkSymbol, string>(MainForm.<>c.<>9.method_18)).Distinct<string>())
			{
				MainForm.Class335 @class = new MainForm.Class335();
				ButtonItem buttonItem = new ButtonItem();
				buttonItem.Text = text;
				@class.string_0 = text.Substring(0, text.IndexOf("("));
				IEnumerable<StkSymbol> enumerable = ienumerable_0.Where(new Func<StkSymbol, bool>(@class.method_0));
				if (enumerable.Any<StkSymbol>())
				{
					this.method_79(buttonItem, enumerable);
					buttonItem_0.SubItems.Add(buttonItem);
				}
			}
		}

		// Token: 0x06001C3B RID: 7227 RVA: 0x000BFCF8 File Offset: 0x000BDEF8
		private void method_79(ButtonItem buttonItem_0, IEnumerable<StkSymbol> ienumerable_0)
		{
			buttonItem_0.SubItems.Clear();
			foreach (StkSymbol stkSymbol in ienumerable_0)
			{
				ButtonItem buttonItem = new ButtonItem();
				buttonItem.Text = stkSymbol.CNName + "(" + stkSymbol.Code + ")";
				buttonItem.Click += this.method_80;
				buttonItem.Tag = stkSymbol.ID;
				buttonItem.Tooltip = stkSymbol.CNName;
				if (stkSymbol.Code == Base.Data.CurrSelectedSymbol.Code)
				{
					buttonItem.Checked = true;
				}
				buttonItem_0.SubItems.Add(buttonItem);
			}
		}

		// Token: 0x06001C3C RID: 7228 RVA: 0x000BFDD0 File Offset: 0x000BDFD0
		private void method_80(object sender, EventArgs e)
		{
			ButtonItem buttonItem = (ButtonItem)sender;
			if (!buttonItem.Checked)
			{
				StkSymbol stkSymbol_ = SymbMgr.smethod_3(Convert.ToInt32(buttonItem.Tag));
				Base.UI.smethod_177("正在提取数据...", this.method_4());
				Base.UI.smethod_175(stkSymbol_);
				Base.UI.smethod_178();
			}
		}

		// Token: 0x06001C3D RID: 7229 RVA: 0x0000BC8A File Offset: 0x00009E8A
		private void toolStripBtnItem_SelectDate_Click(object sender, EventArgs e)
		{
			this.method_81();
		}

		// Token: 0x06001C3E RID: 7230 RVA: 0x000BFE18 File Offset: 0x000BE018
		public void method_81()
		{
			if (!Base.UI.Form.IsInBlindTestMode && !Base.UI.IsInCreateNewPageState)
			{
				if (this.timer_1.Enabled)
				{
					this.timer_1.Enabled = false;
					Base.UI.Form.IsStarted = false;
				}
				this.method_82();
			}
		}

		// Token: 0x06001C3F RID: 7231 RVA: 0x000BFE64 File Offset: 0x000BE064
		public void method_82()
		{
			try
			{
				if (this.toolStripBtnItem_SelectDate != null)
				{
					this.toolStripBtnItem_SelectDate.Checked = true;
				}
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
			DateSelectForm dateSelectForm = new DateSelectForm();
			dateSelectForm.Owner = this;
			dateSelectForm.FormClosed += this.method_163;
			dateSelectForm.ShowInTaskbar = false;
			dateSelectForm.ShowDialog();
		}

		// Token: 0x06001C40 RID: 7232 RVA: 0x000BFECC File Offset: 0x000BE0CC
		private void btnItem_双盲测试_Click(object sender, EventArgs e)
		{
			if (Base.UI.Form.IsInBlindTestMode)
			{
				this.method_85();
			}
			else
			{
				BlindTestForm blindTestForm = new BlindTestForm();
				blindTestForm.Owner = this;
				blindTestForm.SelectionChanged += this.method_83;
				blindTestForm.ShowInTaskbar = false;
				blindTestForm.ShowDialog();
			}
		}

		// Token: 0x06001C41 RID: 7233 RVA: 0x000BFF1C File Offset: 0x000BE11C
		private void method_83(EventArgs1 eventArgs1_0)
		{
			this.list_0 = eventArgs1_0.SelectionSymbList;
			this.method_84(eventArgs1_0.NewSymbID, eventArgs1_0.NewSymbLastDT.Value, true);
			if (Base.UI.Chart.IsSingleFixedContent && Base.UI.TransTabCtrl != null && !Base.UI.TransTabCtrl.IsSwitchedBehind && Base.UI.TransTabs != null && Base.UI.TransTabs.Visible)
			{
				Base.UI.SelectedChtCtrl = Base.UI.TransTabs.method_88();
				if (Base.UI.SelectedChtCtrl != null)
				{
					Base.UI.SelectedChtCtrl.Focus();
					this.method_62(Base.UI.SelectedChtCtrl);
				}
			}
		}

		// Token: 0x06001C42 RID: 7234 RVA: 0x000BFFB4 File Offset: 0x000BE1B4
		private bool method_84(int int_11, DateTime dateTime_0, bool bool_8)
		{
			if (int_11 != Base.Data.CurrSelectedSymbol.ID)
			{
				if (!Base.Data.smethod_66(int_11, dateTime_0, false, false))
				{
					if (bool_8)
					{
						Base.UI.Form.IsInBlindTestMode = false;
					}
					return false;
				}
				Base.UI.Form.IsSpanMoveNext = false;
				Base.UI.Form.IsSpanMovePrev = false;
				Base.UI.Form.LastSpanMoveDT = null;
			}
			else
			{
				SymbDataSet symbDataSet = Base.Data.CurrSymbDataSet;
				if (symbDataSet == null)
				{
					symbDataSet = Base.Data.smethod_49(int_11, true);
				}
				else if (!symbDataSet.HasValidDataSet)
				{
					symbDataSet.method_19(symbDataSet.SymblID, new DateTime?(dateTime_0));
				}
				if (symbDataSet.CurrHisDataSet == null || !symbDataSet.CurrHisDataSet.method_7(dateTime_0.Date, new DateTime?(symbDataSet.CurrStkMeta.EndDate.Value)))
				{
					if (bool_8)
					{
						Base.UI.Form.IsInBlindTestMode = false;
					}
					return false;
				}
				DateTime dateTime = dateTime_0;
				if (bool_8)
				{
					dateTime = symbDataSet.method_70(dateTime_0);
					if (dateTime != dateTime_0)
					{
						symbDataSet.CurrHisDataSet.method_15(dateTime);
					}
				}
				this.method_27(dateTime);
				Base.UI.smethod_133();
				Base.Acct.CurrAccount.LastSymbDT = new DateTime?(dateTime);
			}
			if (bool_8)
			{
				this.method_154();
				this.toolStripBtnItem_SelectDate.Enabled = false;
			}
			this.method_89();
			if (Base.UI.TransTabs != null)
			{
				Base.UI.TransTabs.method_82();
				Base.UI.TransTabs.method_69();
				Base.UI.TransTabs.Refresh();
			}
			if (Base.UI.Form.IsInBlindTestMode)
			{
				Class46.smethod_3(Class22.BlindTestEntered, "CurrSymb:" + Base.Data.CurrSelectedSymbol.Code + "  NewDT:" + dateTime_0.ToString());
			}
			return true;
		}

		// Token: 0x06001C43 RID: 7235 RVA: 0x0000BC94 File Offset: 0x00009E94
		private void toolStripStatusLabel_BlindTest_Click(object sender, EventArgs e)
		{
			if (Base.UI.Form.IsInBlindTestMode)
			{
				this.method_85();
			}
			else
			{
				this.btnItem_双盲测试_Click(null, new EventArgs());
			}
		}

		// Token: 0x06001C44 RID: 7236 RVA: 0x000C0150 File Offset: 0x000BE350
		private void method_85()
		{
			if (MessageBox.Show("退出双盲测试模式吗？", "请确认", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) == DialogResult.OK)
			{
				Class46.smethod_3(Class22.BlindTestExiting, "CurrSymb:" + Base.Data.CurrSelectedSymbol.Code);
				Base.UI.Form.IsInBlindTestMode = false;
				if (MessageBox.Show("返回双盲测试前的交易品种/行情时间请选『是』，仍停留在现有交易品种并显示品种名称和行情时间请选『否』。", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
				{
					int value = Base.UI.Form.LastSymbIDPriorToBlindTest.Value;
					DateTime value2 = Base.UI.Form.LastSymbDTPriorToBlindTest.Value;
					if (Base.Data.UsrStkSymbols.ContainsKey(value))
					{
						SymbMgr.smethod_3(value);
						UsrStkMeta usrStkMeta = Base.Data.smethod_90(value);
						if (usrStkMeta == null)
						{
							throw new Exception("Could not get UsrStkMeta for symbl(id:" + value.ToString() + ")!");
						}
						if (usrStkMeta.BeginDate <= value2 && value2 <= usrStkMeta.EndDate)
						{
							if (value != Base.Data.CurrSelectedSymbol.ID)
							{
								Base.Data.smethod_75(Base.Data.CurrSymbDataSet);
								Base.Data.smethod_66(Base.UI.Form.LastSymbIDPriorToBlindTest.Value, Base.UI.Form.LastSymbDTPriorToBlindTest.Value, false, false);
							}
							else
							{
								SymbDataSet symbDataSet = Base.Data.smethod_49(value, false);
								if (symbDataSet.CurrHisDataSet.method_7(value2, new DateTime?(symbDataSet.CurrStkMeta.EndDate.Value)))
								{
									this.method_27(symbDataSet.CurrHisDataSet.CurrHisData.Date);
									Base.UI.smethod_133();
									Base.Acct.CurrAccount.LastSymbDT = new DateTime?(symbDataSet.CurrHisDataSet.CurrHisData.Date);
								}
							}
						}
						else
						{
							this.method_86(value);
						}
					}
					else
					{
						this.method_86(value);
					}
				}
				else
				{
					foreach (ChtCtrl chtCtrl in Base.UI.ChtCtrlList)
					{
						if (chtCtrl.IfNoSync)
						{
							chtCtrl.IfNoSync = false;
							chtCtrl.LinkedSymblId = null;
						}
					}
					if (Base.Data.CurrSymbDataSet != null && Base.Data.CurrSymbDataSet.HasValidDataSet)
					{
						this.method_46(Base.Data.CurrSymbDataSet.CurrHisData.Date);
					}
				}
				this.method_155();
				this.method_89();
				this.toolStripBtnItem_SelectDate.Enabled = true;
				if (Base.UI.TransTabs != null)
				{
					Base.UI.TransTabs.method_69();
					Base.UI.TransTabs.method_82();
					Base.UI.TransTabs.Refresh();
				}
				Class46.smethod_3(Class22.BlindTestExited, "CurrSymb:" + Base.Data.CurrSelectedSymbol.Code);
			}
		}

		// Token: 0x06001C45 RID: 7237 RVA: 0x000C042C File Offset: 0x000BE62C
		private void method_86(int int_11)
		{
			SymbDataSet symbDataSet = Base.Data.smethod_49(int_11, false);
			if (symbDataSet != null)
			{
				DateTime dateTime_;
				if (symbDataSet != null && symbDataSet.HasValidDataSet)
				{
					dateTime_ = symbDataSet.CurrHisDataSet.CurrHisData.Date;
				}
				else
				{
					dateTime_ = symbDataSet.method_108();
				}
				this.method_46(dateTime_);
			}
			else
			{
				Class182.smethod_0(new Exception("sds == null!"));
			}
		}

		// Token: 0x06001C46 RID: 7238 RVA: 0x0000BCB8 File Offset: 0x00009EB8
		private void 新建账户ToolStripMenuItem_Click(object sender, EventArgs e)
		{
			this.method_204();
			CreateAcctForm createAcctForm = new CreateAcctForm();
			createAcctForm.Owner = this;
			createAcctForm.ShowInTaskbar = false;
			createAcctForm.NewAcctCreated += this.method_87;
			createAcctForm.ShowDialog();
		}

		// Token: 0x06001C47 RID: 7239 RVA: 0x000C0484 File Offset: 0x000BE684
		private void method_87(object sender, EventArgs13 e)
		{
			if (e.IfSwitchToNewAcct)
			{
				Base.UI.smethod_177("正在切换账户...", this.method_4());
				try
				{
					Base.Acct.smethod_47(e.NewAcctID);
				}
				catch
				{
					Base.UI.smethod_178();
					throw;
				}
				Base.UI.smethod_178();
			}
		}

		// Token: 0x06001C48 RID: 7240 RVA: 0x00006740 File Offset: 0x00004940
		private void btnItem_画线工具_Click(object sender, EventArgs e)
		{
			Base.UI.smethod_115();
		}

		// Token: 0x06001C49 RID: 7241 RVA: 0x0000BCED File Offset: 0x00009EED
		private void btnItem_数据管理_Click(object sender, EventArgs e)
		{
			this.method_204();
			new DataMgmtForm
			{
				Owner = this,
				StartPosition = FormStartPosition.CenterParent
			}.ShowDialog();
		}

		// Token: 0x06001C4A RID: 7242 RVA: 0x000C04D8 File Offset: 0x000BE6D8
		private void toolStripMenuItem_BuySoft_Click(object sender, EventArgs e)
		{
			try
			{
				string str = "";
				Process.Start(new ProcessStartInfo("https://www.tradingexer.com/" + str + "buy"));
			}
			catch
			{
			}
		}

		// Token: 0x06001C4B RID: 7243 RVA: 0x000C0520 File Offset: 0x000BE720
		private void toolStripMenuItem_VerInfo_Click(object sender, EventArgs e)
		{
			try
			{
				string str = "";
				Process.Start(new ProcessStartInfo("https://www.tradingexer.com/" + str + "versions"));
			}
			catch
			{
			}
		}

		// Token: 0x06001C4C RID: 7244 RVA: 0x000C0568 File Offset: 0x000BE768
		private void method_88()
		{
			decimal d = Base.Trading.smethod_176();
			this.toolStripStatusLabel_Profits.Text = d.ToString("N0");
			decimal d2 = Math.Round(d);
			if (d2 > 0m)
			{
				this.toolStripStatusLabel_Profits.ForeColor = Color.Red;
			}
			else if (d2 < 0m)
			{
				this.toolStripStatusLabel_Profits.ForeColor = Class179.color_24;
			}
			else if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.toolStripStatusLabel_Profits.ForeColor = Class179.color_9;
			}
			else
			{
				this.toolStripStatusLabel_Profits.ForeColor = Class179.color_1;
			}
		}

		// Token: 0x06001C4D RID: 7245 RVA: 0x000C0608 File Offset: 0x000BE808
		private void method_89()
		{
			if (Base.UI.Form.IsInBlindTestMode)
			{
				if (!Base.UI.Form.IsSingleBlindTest)
				{
					this.toolStripDropDownButton_Symbls.Text = "●●";
					this.toolStripDropDownButton_Symbls.Tooltip = "双盲模式中品种名隐藏";
					this.toolStripDropDownButton_Symbls.Enabled = false;
				}
				else
				{
					if (Base.UI.CurrTradingSymbol != null)
					{
						this.toolStripDropDownButton_Symbls.Text = Base.UI.CurrTradingSymbol.Desc;
						this.toolStripDropDownButton_Symbls.Tooltip = Base.UI.CurrTradingSymbol.CNName;
					}
					this.toolStripDropDownButton_Symbls.Enabled = false;
				}
			}
			else
			{
				if (Base.UI.CurrTradingSymbol != null)
				{
					this.toolStripDropDownButton_Symbls.Text = Base.UI.CurrTradingSymbol.Desc;
					this.toolStripDropDownButton_Symbls.Tooltip = Base.UI.CurrTradingSymbol.CNName;
				}
				this.toolStripDropDownButton_Symbls.Enabled = true;
			}
		}

		// Token: 0x06001C4E RID: 7246 RVA: 0x0000BD10 File Offset: 0x00009F10
		private void method_90()
		{
			this.toolStripStatusLabel_Info.Text = "";
			this.toolStripStatusLabel_Profits.Text = "0";
		}

		// Token: 0x06001C4F RID: 7247 RVA: 0x000C06DC File Offset: 0x000BE8DC
		private void method_91(object sender, EventArgs e)
		{
			ButtonItem buttonItem = (ButtonItem)sender;
			if (!buttonItem.Checked)
			{
				int num = Convert.ToInt32(buttonItem.Tag);
				Base.UI.smethod_177("正在切换账户...", this.method_4());
				try
				{
					Base.Acct.smethod_47(num);
				}
				catch
				{
					Base.UI.smethod_178();
					throw;
				}
				Base.UI.smethod_178();
			}
		}

		// Token: 0x06001C50 RID: 7248 RVA: 0x0000BD34 File Offset: 0x00009F34
		private void method_92(object sender, EventArgs e)
		{
			this.method_100(Enum13.const_0, "正在切换账户...");
		}

		// Token: 0x06001C51 RID: 7249 RVA: 0x000C073C File Offset: 0x000BE93C
		private void method_93(object sender, EventArgs e)
		{
			this.toolStripDropDownButton_Accts.Text = Base.Acct.CurrAccount.AcctName;
			this.toolStripDropDownButton_Accts.Tooltip = Base.Acct.CurrAccount.Notes;
			this.toolStripStatusLabel_Profits.Text = "0";
			if (this.toolStripStatusLabel_Profits.ForeColor != Color.Black)
			{
				this.toolStripStatusLabel_Profits.ForeColor = Color.Black;
			}
			this.method_45();
			this.method_100(Enum13.const_0, "账户切换成功。");
		}

		// Token: 0x06001C52 RID: 7250 RVA: 0x000C07C0 File Offset: 0x000BE9C0
		private void method_94()
		{
			if (Base.Trading.smethod_107(Base.UI.CurrTradingSymbol.ID))
			{
				this.method_128();
			}
			else
			{
				this.method_129();
			}
			if (Base.Trading.smethod_108(Base.UI.CurrTradingSymbol.ID))
			{
				this.method_152();
			}
			else
			{
				this.method_153();
			}
		}

		// Token: 0x06001C53 RID: 7251 RVA: 0x000C0810 File Offset: 0x000BEA10
		private void timer_2_Elapsed(object sender, ElapsedEventArgs e)
		{
			string text = this.toolStripStatusLabel_Info.Text;
			if (!string.IsNullOrEmpty(text) && !text.EndsWith("..."))
			{
				this.method_101("");
			}
		}

		// Token: 0x06001C54 RID: 7252 RVA: 0x000041AE File Offset: 0x000023AE
		private void method_95(object sender, EventArgs25 e)
		{
		}

		// Token: 0x06001C55 RID: 7253 RVA: 0x0000BD44 File Offset: 0x00009F44
		private void method_96(object sender, EventArgs e)
		{
			this.method_101("期货监控中心成交记录检查完成，无新记录需下载。");
		}

		// Token: 0x06001C56 RID: 7254 RVA: 0x0000BD53 File Offset: 0x00009F53
		private void method_97(object sender, EventArgs25 e)
		{
			this.method_101("下载期货监控中心成交记录发生错误。");
		}

		// Token: 0x06001C57 RID: 7255 RVA: 0x0000BD62 File Offset: 0x00009F62
		private void method_98(object sender, EventArgs25 e)
		{
			this.method_101("已完成下载期货监控中心成交记录。");
		}

		// Token: 0x06001C58 RID: 7256 RVA: 0x0000BD71 File Offset: 0x00009F71
		private void method_99(object sender, EventArgs25 e)
		{
			this.method_101("正在检查下载期货监控中心成交记录...");
		}

		// Token: 0x06001C59 RID: 7257 RVA: 0x000C084C File Offset: 0x000BEA4C
		private void method_100(Enum13 enum13_0, string string_6)
		{
			string str = "";
			if (enum13_0 != Enum13.const_0)
			{
				if (enum13_0 == Enum13.const_1)
				{
					str = "警告：";
				}
			}
			else
			{
				str = "提示：";
			}
			this.method_101(str + string_6);
		}

		// Token: 0x06001C5A RID: 7258 RVA: 0x0000BD80 File Offset: 0x00009F80
		private void method_101(string string_6)
		{
			this.toolStripStatusLabel_Info.Text = string_6;
			if (this.timer_2 != null)
			{
				this.timer_2.Stop();
				this.timer_2.Start();
			}
		}

		// Token: 0x06001C5B RID: 7259 RVA: 0x000C0888 File Offset: 0x000BEA88
		private void method_102()
		{
			Image image_;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				image_ = Class372.line_chart_lightgray;
			}
			else
			{
				image_ = Class372.line_chart_darkblack;
			}
			Image image = TApp.smethod_7(image_, new Size(20, 20));
			this.toolStripBtn_Tick.Image = image;
		}

		// Token: 0x06001C5C RID: 7260 RVA: 0x000C08D0 File Offset: 0x000BEAD0
		private int? method_103()
		{
			return new int?(this.slider_Speed.Value);
		}

		// Token: 0x06001C5D RID: 7261 RVA: 0x000C08F4 File Offset: 0x000BEAF4
		private int? method_104()
		{
			int? result = null;
			try
			{
				result = new int?(Convert.ToInt32(this.numericUpDown_Units.Value));
			}
			catch
			{
				MessageBox.Show("请输入有效的" + (Base.UI.CurrTradingSymbol.IsFutures ? "手数" : "股数") + "(1~9999999)！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
			return result;
		}

		// Token: 0x06001C5E RID: 7262 RVA: 0x000C0970 File Offset: 0x000BEB70
		private decimal method_105()
		{
			return this.numericUpDown_Price.Value;
		}

		// Token: 0x06001C5F RID: 7263 RVA: 0x000C098C File Offset: 0x000BEB8C
		private int? method_106()
		{
			return new int?(Base.UI.Form.TradingUnits);
		}

		// Token: 0x06001C60 RID: 7264 RVA: 0x000C09AC File Offset: 0x000BEBAC
		private decimal? method_107()
		{
			return new decimal?(Base.UI.Form.TradingPrice);
		}

		// Token: 0x06001C61 RID: 7265 RVA: 0x0000BDAE File Offset: 0x00009FAE
		private void method_108(object sender, EventArgs e)
		{
			this.method_89();
			this.method_68();
			this.method_67();
		}

		// Token: 0x06001C62 RID: 7266 RVA: 0x000C09CC File Offset: 0x000BEBCC
		private void method_109(object sender, EventArgs e)
		{
			string string_ = "品种'" + (sender as StkSymbol).CNName + "'已加入自选板块。";
			if (Base.UI.Form.IsInBlindTestMode)
			{
				string_ = "品种已加入自选板块。";
			}
			this.method_100(Enum13.const_0, string_);
		}

		// Token: 0x06001C63 RID: 7267 RVA: 0x0000BDC4 File Offset: 0x00009FC4
		private void method_110(object sender, EventArgs e)
		{
			SettingsForm settingsForm = sender as SettingsForm;
			if (settingsForm.ChartThemeChanged)
			{
				Base.UI.smethod_32(Base.UI.Form.ChartTheme);
			}
			if (settingsForm.KLineTypeChanged)
			{
				Base.UI.smethod_19();
			}
			else
			{
				Base.UI.smethod_31();
			}
		}

		// Token: 0x06001C64 RID: 7268 RVA: 0x000C0A10 File Offset: 0x000BEC10
		private void method_111(object sender, EventArgs e)
		{
			this.bool_2 = true;
			if (Base.Data.CurrDate == default(DateTime) && Base.Data.CurrSymbDataSet.CurrStkMeta.EndDate != null)
			{
				Base.Data.CurrDate = Base.Data.CurrSymbDataSet.CurrStkMeta.EndDate.Value;
			}
			Base.Acct.CurrAccount.LastSymbDT = new DateTime?(Base.Data.CurrDate);
			this.method_24(Base.Acct.CurrAccount.LastSymbDT);
			this.method_16();
			InfoMineMgr.smethod_5(null);
			this.toolStripDropDownButton_Pages.Text = Base.UI.Form.CurrentPageName;
			Class46.smethod_3(Class22.PageChanged, "CurrPage:" + Base.UI.Form.CurrentPageName + "  " + Base.UI.smethod_102());
		}

		// Token: 0x06001C65 RID: 7269 RVA: 0x0000BDF8 File Offset: 0x00009FF8
		private void method_112(object sender, EventArgs e)
		{
			if (!this.bool_6)
			{
				this.method_100(Enum13.const_0, "当前页面已保存。");
			}
		}

		// Token: 0x06001C66 RID: 7270 RVA: 0x000041AE File Offset: 0x000023AE
		private void method_113(object sender, EventArgs e)
		{
		}

		// Token: 0x06001C67 RID: 7271 RVA: 0x0000BE10 File Offset: 0x0000A010
		private void method_114(object sender, EventArgs e)
		{
			if (!this.timer_1.Enabled)
			{
				this.method_203();
				this.timer_1.Enabled = true;
			}
			this.IsInRetroMode = false;
		}

		// Token: 0x06001C68 RID: 7272 RVA: 0x0000BE3A File Offset: 0x0000A03A
		private void method_115(object sender, EventArgs e)
		{
			if (this.timer_1.Enabled)
			{
				this.timer_1.Enabled = false;
			}
		}

		// Token: 0x06001C69 RID: 7273 RVA: 0x0000BE57 File Offset: 0x0000A057
		private void method_116(object sender, EventArgs e)
		{
			this.method_67();
		}

		// Token: 0x06001C6A RID: 7274 RVA: 0x0000BE61 File Offset: 0x0000A061
		private void method_117(object sender, EventArgs e)
		{
			this.method_71();
		}

		// Token: 0x06001C6B RID: 7275 RVA: 0x0000BE6B File Offset: 0x0000A06B
		private void method_118(object sender, EventArgs e)
		{
			this.method_45();
		}

		// Token: 0x06001C6C RID: 7276 RVA: 0x0000BE75 File Offset: 0x0000A075
		private void method_119(object sender, EventArgs e)
		{
			Base.UI.smethod_31();
		}

		// Token: 0x06001C6D RID: 7277 RVA: 0x000C0AE0 File Offset: 0x000BECE0
		private void method_120(object sender, EventArgs e)
		{
			if (this.Text.EndsWith(" - 双盲测试模式"))
			{
				this.Text = this.Text.Replace(" - 双盲测试模式", string.Empty);
			}
			this.toolStripStatusLabel_BlindTest.Text = "双盲模式：关";
		}

		// Token: 0x06001C6E RID: 7278 RVA: 0x0000BE7E File Offset: 0x0000A07E
		private void method_121(object sender, EventArgs e)
		{
			if (!this.Text.EndsWith(" - 双盲测试模式"))
			{
				this.Text += " - 双盲测试模式";
			}
			this.toolStripStatusLabel_BlindTest.Text = "双盲模式：开";
		}

		// Token: 0x06001C6F RID: 7279 RVA: 0x000C0B2C File Offset: 0x000BED2C
		private void method_122(Enum7 enum7_0)
		{
			if (Base.Data.UsrStandAloneStkSymbList.Count > 1)
			{
				if (Base.UI.ChtCtrlList.Exists(new Predicate<ChtCtrl>(MainForm.<>c.<>9.method_19)))
				{
					if (Base.UI.Form.IsInBlindTestMode)
					{
						if (this.list_0 == null)
						{
							this.list_0 = Base.Data.smethod_84();
						}
						if (this.list_0.Count > 1)
						{
							StkSymbol stkSymbol = this.method_123();
							Base.UI.smethod_177("正在提取数据...", this.method_4());
							int i = 0;
							while (i < this.list_0.Count)
							{
								i++;
								UsrStkMeta usrStkMeta = Base.Data.smethod_90(stkSymbol.ID);
								if (stkSymbol != Base.Data.CurrSelectedSymbol && usrStkMeta != null && (usrStkMeta.EndDate.Value.Date - usrStkMeta.BeginDate.Value.Date).TotalDays >= (double)SymbMgr.MinBlindTestDays)
								{
									break;
								}
								stkSymbol = this.method_123();
							}
							if (stkSymbol != Base.Data.CurrSelectedSymbol)
							{
								if (Base.UI.Form.IsSingleBlindTest)
								{
									Base.UI.Form.IsSingleBlindTest = false;
								}
								Base.UI.smethod_177("正在提取数据...", this.method_4());
								Base.Data.smethod_68(stkSymbol, false, false);
								Base.UI.smethod_178();
							}
						}
					}
					else
					{
						Base.UI.smethod_177("正在提取数据...", this.method_4());
						Base.Data.smethod_83(enum7_0, null);
						Base.UI.smethod_178();
					}
				}
			}
		}

		// Token: 0x06001C70 RID: 7280 RVA: 0x000C0C98 File Offset: 0x000BEE98
		private StkSymbol method_123()
		{
			StkSymbol stkSymbol = Base.Data.CurrSelectedSymbol;
			if (this.list_0 != null && this.list_0.Count > 1)
			{
				while (stkSymbol == Base.Data.CurrSelectedSymbol)
				{
					int num = new Random(Utility.GetRandomSeed()).Next(0, this.list_0.Count);
					if (num == this.list_0.Count)
					{
						num = this.list_0.Count - 1;
					}
					stkSymbol = this.list_0[num];
				}
			}
			return stkSymbol;
		}

		// Token: 0x06001C71 RID: 7281 RVA: 0x0000BEBA File Offset: 0x0000A0BA
		private void method_124(bool bool_8, bool bool_9)
		{
			SetSymbParamForm setSymbParamForm = new SetSymbParamForm(new List<StkSymbol>
			{
				Base.UI.CurrTradingSymbol
			}, bool_8, bool_9, true);
			setSymbParamForm.SymbParamsUpdated += this.method_125;
			setSymbParamForm.Owner = this;
			setSymbParamForm.ShowDialog();
		}

		// Token: 0x06001C72 RID: 7282 RVA: 0x000C0D18 File Offset: 0x000BEF18
		private void method_125(object sender, EventArgs21 e)
		{
			if (e.IfTurnOnAutoStop && Base.Trading.smethod_107(Base.UI.CurrTradingSymbol.ID))
			{
				this.method_126(true);
			}
			else if (e.IfTurnOnAutoLimit && Base.Trading.smethod_108(Base.UI.CurrTradingSymbol.ID))
			{
				this.method_130(true);
			}
		}

		// Token: 0x06001C73 RID: 7283 RVA: 0x000C0D6C File Offset: 0x000BEF6C
		private void method_126(bool bool_8)
		{
			if (Base.UI.CurrTradingSymbol.AutoStopLossPoints == null)
			{
				if (MessageBox.Show("当前交易品种的自动止损点数尚未设置，自动止损无法生效。现在设置吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
				{
					this.method_124(true, false);
				}
			}
			else
			{
				Base.Trading.smethod_109(Base.UI.CurrTradingSymbol.ID, true);
				if (bool_8)
				{
					this.method_128();
				}
				else
				{
					this.method_129();
				}
			}
		}

		// Token: 0x06001C74 RID: 7284 RVA: 0x0000BEF5 File Offset: 0x0000A0F5
		private void method_127(object sender, EventArgs e)
		{
			if (Base.Trading.smethod_107(Base.UI.CurrTradingSymbol.ID))
			{
				this.method_128();
			}
			else
			{
				this.method_129();
			}
		}

		// Token: 0x06001C75 RID: 7285 RVA: 0x000C0DD0 File Offset: 0x000BEFD0
		private void method_128()
		{
			if (this.toolStripStatusLabel_AutoStopStatus.Image.Tag == null || this.toolStripStatusLabel_AutoStopStatus.Image.Tag.ToString() != "Enabled")
			{
				this.toolStripStatusLabel_AutoStopStatus.Image = Class372.autostop;
				this.toolStripStatusLabel_AutoStopStatus.Image.Tag = "Enabled";
				this.toolStripStatusLabel_AutoStopStatus.Tooltip = "自动止损已开启（点击可关闭）";
			}
		}

		// Token: 0x06001C76 RID: 7286 RVA: 0x000C0E48 File Offset: 0x000BF048
		private void method_129()
		{
			if (this.toolStripStatusLabel_AutoStopStatus.Image.Tag == null || this.toolStripStatusLabel_AutoStopStatus.Image.Tag.ToString() != "Disabled")
			{
				this.toolStripStatusLabel_AutoStopStatus.Image = Class372.autostop_gray;
				this.toolStripStatusLabel_AutoStopStatus.Image.Tag = "Disabled";
				this.toolStripStatusLabel_AutoStopStatus.Tooltip = "自动止损已关闭（点击可开启）";
			}
		}

		// Token: 0x06001C77 RID: 7287 RVA: 0x000C0EC0 File Offset: 0x000BF0C0
		private void method_130(bool bool_8)
		{
			if (Base.UI.CurrTradingSymbol.AutoLimitTakePoints == null)
			{
				if (MessageBox.Show("当前页面品种的自动止盈点数尚未设置，自动止盈无法生效。现在设置吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
				{
					this.method_124(false, true);
				}
			}
			else
			{
				Base.Trading.smethod_110(Base.UI.CurrTradingSymbol.ID, bool_8);
				if (bool_8)
				{
					this.method_152();
				}
				else
				{
					this.method_153();
				}
			}
		}

		// Token: 0x06001C78 RID: 7288 RVA: 0x0000BF18 File Offset: 0x0000A118
		private void method_131(object sender, EventArgs e)
		{
			if (Base.Trading.smethod_108(Base.UI.CurrTradingSymbol.ID))
			{
				this.method_152();
			}
			else
			{
				this.method_153();
			}
		}

		// Token: 0x06001C79 RID: 7289 RVA: 0x000C0F24 File Offset: 0x000BF124
		private void toolStripStatusLabel_AutoStopStatus_Click(object sender, EventArgs e)
		{
			if (this.toolStripStatusLabel_AutoStopStatus.Image.Tag != null && this.toolStripStatusLabel_AutoStopStatus.Image.Tag.ToString() == "Enabled")
			{
				if (MessageBox.Show("取消自动止损吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
				{
					this.method_126(false);
				}
			}
			else if (MessageBox.Show("打开自动止损吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
			{
				this.method_126(true);
			}
		}

		// Token: 0x06001C7A RID: 7290 RVA: 0x000C0FA0 File Offset: 0x000BF1A0
		private void toolStripStatusLabel_AutoLimitStatus_Click(object sender, EventArgs e)
		{
			if (this.toolStripStatusLabel_AutoLimitStatus.Image.Tag.ToString() == "Enabled")
			{
				if (MessageBox.Show("取消自动止盈吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
				{
					this.method_130(false);
				}
			}
			else if (MessageBox.Show("打开自动止盈吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
			{
				this.method_130(true);
			}
		}

		// Token: 0x06001C7B RID: 7291 RVA: 0x000C100C File Offset: 0x000BF20C
		private void method_132()
		{
			if (Base.UI.CurrTradingSymbol.AutoStopLossPoints != null)
			{
				if (Base.Trading.smethod_107(Base.UI.CurrTradingSymbol.ID))
				{
					this.method_128();
				}
				else
				{
					this.method_129();
				}
			}
			else
			{
				Base.Trading.smethod_109(Base.UI.CurrTradingSymbol.ID, false);
				this.method_129();
			}
			if (Base.UI.CurrTradingSymbol.AutoLimitTakePoints != null)
			{
				if (Base.Trading.smethod_108(Base.UI.CurrTradingSymbol.ID))
				{
					this.method_152();
				}
				else
				{
					this.method_153();
				}
			}
			else
			{
				Base.Trading.smethod_110(Base.UI.CurrTradingSymbol.ID, false);
				this.method_153();
			}
		}

		// Token: 0x06001C7C RID: 7292 RVA: 0x0000BF3B File Offset: 0x0000A13B
		private void method_133(OrderType orderType_0)
		{
			this.method_134(orderType_0, false, false);
		}

		// Token: 0x06001C7D RID: 7293 RVA: 0x000C10B4 File Offset: 0x000BF2B4
		private void method_134(OrderType orderType_0, bool bool_8, bool bool_9)
		{
			decimal decimal_;
			if (bool_8)
			{
				decimal_ = this.method_105();
			}
			else
			{
				decimal_ = this.method_107().Value;
			}
			this.method_135(orderType_0, decimal_, bool_9);
		}

		// Token: 0x06001C7E RID: 7294 RVA: 0x000C10E8 File Offset: 0x000BF2E8
		private void method_135(OrderType orderType_0, decimal decimal_1, bool bool_8)
		{
			int num;
			if (bool_8)
			{
				num = this.method_104().Value;
			}
			else
			{
				num = Base.UI.smethod_180();
			}
			Base.Trading.smethod_197(Base.UI.CurrTradingSymbol.ID, orderType_0, (long)num, decimal_1);
			Class46.smethod_3(Class22.OrderPlaced, string.Concat(new string[]
			{
				"Via MainForm UI. OrderType:",
				orderType_0.ToString(),
				"  Units:",
				num.ToString(),
				"  Price:",
				decimal_1.ToString()
			}));
		}

		// Token: 0x06001C7F RID: 7295 RVA: 0x0000BE6B File Offset: 0x0000A06B
		private void method_136(EventArgs17 eventArgs17_0)
		{
			this.method_45();
		}

		// Token: 0x06001C80 RID: 7296 RVA: 0x0000BF48 File Offset: 0x0000A148
		private void method_137(EventArgs eventArgs_0)
		{
			this.method_88();
		}

		// Token: 0x06001C81 RID: 7297 RVA: 0x000C1178 File Offset: 0x000BF378
		private void method_138(EventArgs14 eventArgs14_0)
		{
			ShownOrder shownOrder = eventArgs14_0.ShownOrder;
			if (shownOrder.OrderStatus == 2)
			{
				this.method_100(Enum13.const_0, eventArgs14_0.OrderDesc + "执行成功！");
				this.method_142(shownOrder);
			}
			else if (shownOrder.OrderStatus == 0)
			{
				this.method_100(Enum13.const_0, eventArgs14_0.OrderDesc + "已生效！");
			}
			this.method_44();
			this.method_45();
		}

		// Token: 0x06001C82 RID: 7298 RVA: 0x0000BF52 File Offset: 0x0000A152
		private void method_139(EventArgs14 eventArgs14_0)
		{
			this.method_100(Enum13.const_1, eventArgs14_0.OrderDesc + "执行失败！" + eventArgs14_0.ExcResultNotes);
		}

		// Token: 0x06001C83 RID: 7299 RVA: 0x000C11E4 File Offset: 0x000BF3E4
		private void method_140(EventArgs14 eventArgs14_0)
		{
			ShownOrder shownOrder = eventArgs14_0.ShownOrder;
			string str = "";
			if (shownOrder.OrderStatus == 2)
			{
				this.method_142(shownOrder);
				str = "执行成功！";
			}
			else if (shownOrder.OrderStatus == 1)
			{
				str = "已撤销！";
			}
			this.method_100(Enum13.const_0, eventArgs14_0.OrderDesc + str);
			this.method_44();
			this.method_45();
		}

		// Token: 0x06001C84 RID: 7300 RVA: 0x000C1248 File Offset: 0x000BF448
		private void method_141(EventArgs15 eventArgs15_0)
		{
			CondOrder shownCondOrder = eventArgs15_0.ShownCondOrder;
			string string_ = "";
			if (shownCondOrder.OrderStatus == OrderStatus.Executed)
			{
				string_ = "条件单已执行！";
			}
			else if (shownCondOrder.OrderStatus == OrderStatus.Canceled)
			{
				string_ = "条件单已取消！";
			}
			this.method_100(Enum13.const_0, string_);
		}

		// Token: 0x06001C85 RID: 7301 RVA: 0x000C128C File Offset: 0x000BF48C
		private void method_142(ShownOrder shownOrder_0)
		{
			if (!Base.UI.Form.IfDisableOpenCloseSound)
			{
				string text = "close.wav";
				if (shownOrder_0.OrderType == 0 || shownOrder_0.OrderType == 2)
				{
					text = "open.wav";
				}
				text = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, text);
				try
				{
					new SoundPlayer(text).Play();
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
		}

		// Token: 0x06001C86 RID: 7302 RVA: 0x000C12FC File Offset: 0x000BF4FC
		private void method_143(object sender, EventArgs e)
		{
			TransTabs transTabs = sender as TransTabs;
			if (!Base.UI.Form.IfNoSyncToolBarAndTradingTabPriceUnits && transTabs != null && this.numericUpDown_Units.Value != transTabs.InputTradingUnits)
			{
				this.numericUpDown_Units.Value = transTabs.InputTradingUnits;
			}
		}

		// Token: 0x06001C87 RID: 7303 RVA: 0x000C134C File Offset: 0x000BF54C
		private void method_144(object sender, EventArgs e)
		{
			TransTabs transTabs = sender as TransTabs;
			if (!Base.UI.Form.IfNoSyncToolBarAndTradingTabPriceUnits && this.numericUpDown_Price.Value != transTabs.InputTradingPrice)
			{
				this.numericUpDown_Price.Value = transTabs.InputTradingPrice;
			}
		}

		// Token: 0x06001C88 RID: 7304 RVA: 0x000C1398 File Offset: 0x000BF598
		private void method_145(EventArgs16 eventArgs16_0)
		{
			ShownHisTrans shownHisTrans = eventArgs16_0.ShownHisTrans;
			if (!Base.UI.smethod_122(shownHisTrans.CreateTime, "存在开仓时间晚于该记录成交日期的未平仓交易，需先平仓才能跳转至该日期。"))
			{
				if (Base.UI.smethod_123())
				{
					this.method_84(shownHisTrans.SymbolID, shownHisTrans.CreateTime, false);
				}
			}
		}

		// Token: 0x06001C89 RID: 7305 RVA: 0x0000BF73 File Offset: 0x0000A173
		private void method_146(object sender, MsgEventArgs e)
		{
			this.method_100(Enum13.const_0, e.Msg);
		}

		// Token: 0x06001C8A RID: 7306 RVA: 0x0000BF84 File Offset: 0x0000A184
		private void method_147(EventArgs15 eventArgs15_0)
		{
			this.method_100(Enum13.const_0, "新建条件单完成。");
		}

		// Token: 0x06001C8B RID: 7307 RVA: 0x000C13E0 File Offset: 0x000BF5E0
		private void method_148(EventArgs22 eventArgs22_0)
		{
			if (eventArgs22_0.TransType == Enum17.const_7)
			{
				if (Base.UI.Form.RationedShareTreatmt == null)
				{
					Base.UI.Form.RationedShareTreatmt = new RationedShareTreatmt?(RationedShareTreatmt.Prompt);
				}
				if (Base.UI.Form.RationedShareTreatmt.Value == RationedShareTreatmt.Prompt)
				{
					decimal? rationedSharePrice = eventArgs22_0.StSplit.RationedSharePrice;
					decimal d = 0m;
					if (rationedSharePrice.GetValueOrDefault() > d & rationedSharePrice != null)
					{
						bool enabled;
						if (enabled = this.timer_1.Enabled)
						{
							this.method_204();
						}
						if (MessageBox.Show(string.Concat(new object[]
						{
							"当前股票可配股（10配",
							eventArgs22_0.StSplit.RationedShares.Value * 10m / 1.00000000000000000m,
							"，配股价",
							eventArgs22_0.StSplit.RationedSharePrice.Value,
							"），执行配股吗？"
						}), "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
						{
							new BuyRationedShareForm(eventArgs22_0)
							{
								Owner = this
							}.ShowDialog();
						}
						else
						{
							eventArgs22_0.Cancel = true;
						}
						if (enabled)
						{
							this.method_202();
						}
					}
				}
			}
		}

		// Token: 0x06001C8C RID: 7308 RVA: 0x000C152C File Offset: 0x000BF72C
		private void method_149(EventArgs22 eventArgs22_0)
		{
			string text = string.Empty;
			if (eventArgs22_0.TransType == Enum17.const_0)
			{
				if (eventArgs22_0.TranStock.Units > 0L)
				{
					object[] array = new object[4];
					array[0] = "当前股票已送股";
					array[1] = eventArgs22_0.TranStock.Units;
					array[2] = "股";
					int num = 3;
					decimal? profit = eventArgs22_0.TranStock.Profit;
					decimal d = 0m;
					array[num] = ((profit.GetValueOrDefault() > d & profit != null) ? ("，派息" + eventArgs22_0.TranStock.Profit + "元。") : "。");
					text = string.Concat(array);
				}
				else
				{
					decimal? profit = eventArgs22_0.TranStock.Profit;
					decimal d = 0m;
					if (profit.GetValueOrDefault() > d & profit != null)
					{
						text = "当前股票已派息" + eventArgs22_0.TranStock.Profit + "元。";
					}
				}
			}
			if (!string.IsNullOrEmpty(text))
			{
				this.method_100(Enum13.const_0, text);
			}
		}

		// Token: 0x06001C8D RID: 7309 RVA: 0x0000BF94 File Offset: 0x0000A194
		private void method_150(object sender, EventArgs e)
		{
			this.method_100(Enum13.const_1, "余额不足！条件/委托单未执行成功。");
		}

		// Token: 0x06001C8E RID: 7310 RVA: 0x0000BFA4 File Offset: 0x0000A1A4
		private void method_151(object sender, EventArgs e)
		{
			this.method_100(Enum13.const_1, "超出可平头寸！条件/委托单未执行成功。");
		}

		// Token: 0x06001C8F RID: 7311 RVA: 0x000C1648 File Offset: 0x000BF848
		private void method_152()
		{
			if (this.toolStripStatusLabel_AutoLimitStatus.Image.Tag == null || this.toolStripStatusLabel_AutoLimitStatus.Image.Tag.ToString() != "Enabled")
			{
				this.toolStripStatusLabel_AutoLimitStatus.Image = Class372.autolimit;
				this.toolStripStatusLabel_AutoLimitStatus.Image.Tag = "Enabled";
				this.toolStripStatusLabel_AutoLimitStatus.Tooltip = "自动止盈已开启（点击可关闭）";
			}
		}

		// Token: 0x06001C90 RID: 7312 RVA: 0x000C16C0 File Offset: 0x000BF8C0
		private void method_153()
		{
			if (this.toolStripStatusLabel_AutoLimitStatus.Image.Tag == null || this.toolStripStatusLabel_AutoLimitStatus.Image.Tag.ToString() != "Disabled")
			{
				this.toolStripStatusLabel_AutoLimitStatus.Image = Class372.autolimit_gray;
				this.toolStripStatusLabel_AutoLimitStatus.Image.Tag = "Disabled";
				this.toolStripStatusLabel_AutoLimitStatus.Tooltip = "自动止盈已关闭（点击可开启）";
			}
		}

		// Token: 0x06001C91 RID: 7313 RVA: 0x000C1738 File Offset: 0x000BF938
		private void method_154()
		{
			if (this.toolStripStatusLabel_BlindTest.Image.Tag == null || this.toolStripStatusLabel_BlindTest.Image.Tag.ToString() != "Enabled")
			{
				this.toolStripStatusLabel_BlindTest.Image = Class372.GlassFace_18;
				this.toolStripStatusLabel_BlindTest.Image.Tag = "Enabled";
				this.toolStripBtnItem_SelectDate.Tooltip = "期间选择（双盲模式下禁用）";
				this.toolStripStatusLabel_BlindTest.Tooltip = "双盲测试模式已开启（点击可关闭）";
			}
		}

		// Token: 0x06001C92 RID: 7314 RVA: 0x000C17C0 File Offset: 0x000BF9C0
		private void method_155()
		{
			if (this.toolStripStatusLabel_BlindTest.Image.Tag == null || this.toolStripStatusLabel_BlindTest.Image.Tag.ToString() != "Disabled")
			{
				this.toolStripStatusLabel_BlindTest.Image = Class372.GlassFace_18_BW;
				this.toolStripStatusLabel_BlindTest.Image.Tag = "Disabled";
				this.toolStripBtnItem_SelectDate.Tooltip = "期间选择  " + Class208.smethod_5(Enum3.const_20).ShortCutKeyString;
				this.toolStripStatusLabel_BlindTest.Tooltip = "双盲测试模式已关闭（点击可开启）";
			}
		}

		// Token: 0x06001C93 RID: 7315 RVA: 0x000C185C File Offset: 0x000BFA5C
		private void numericUpDown_Units_ValueChanged(object sender, EventArgs e)
		{
			if (Base.UI.Form.TradingUnits != this.numericUpDown_Units.Value)
			{
				Base.UI.Form.TradingUnits = Convert.ToInt32(this.numericUpDown_Units.Value);
			}
			if (!Base.UI.Form.IfNoSyncToolBarAndTradingTabPriceUnits && Base.UI.TransTabs != null && Base.UI.TransTabs.InputTradingUnits != this.numericUpDown_Units.Value)
			{
				Base.UI.TransTabs.InputTradingUnits = this.numericUpDown_Units.Value;
			}
		}

		// Token: 0x06001C94 RID: 7316 RVA: 0x000C18EC File Offset: 0x000BFAEC
		private void numericUpDown_Price_ValueChanged(object sender, EventArgs e)
		{
			this.method_69();
			decimal num = 0m;
			SymbDataSet symbDataSet = Base.Data.smethod_49(Base.UI.CurrTradingSymbol.ID, false);
			if (symbDataSet != null && symbDataSet.LastHisData != null)
			{
				num = Convert.ToDecimal(symbDataSet.LastHisData.Close);
			}
			if (this.decimal_0 == 0m && this.numericUpDown_Price.Value != num && this.numericUpDown_Price.Value == this.numericUpDown_Price.Increment)
			{
				this.numericUpDown_Price.Value = num;
			}
			this.decimal_0 = this.numericUpDown_Price.Value;
			Base.UI.Form.TradingPrice = this.decimal_0;
			if (!Base.UI.Form.IfNoSyncToolBarAndTradingTabPriceUnits && Base.UI.TransTabs != null && Base.UI.TransTabs.InputTradingPrice != this.numericUpDown_Price.Value)
			{
				Base.UI.TransTabs.InputTradingPrice = this.numericUpDown_Price.Value;
			}
		}

		// Token: 0x06001C95 RID: 7317 RVA: 0x000C19EC File Offset: 0x000BFBEC
		private void method_156(object sender, CancelEventArgs e)
		{
			if (this.numericUpDown_Price.Value == 0m)
			{
				decimal d = Convert.ToDecimal(Base.Data.smethod_49(Base.UI.CurrTradingSymbol.ID, false).LastHisData.Close);
				this.numericUpDown_Price.Value = d - this.numericUpDown_Price.Increment;
			}
		}

		// Token: 0x06001C96 RID: 7318 RVA: 0x0000BFB4 File Offset: 0x0000A1B4
		private void numericUpDown_Price_Click(object sender, EventArgs e)
		{
			if (Base.UI.Form.IfFollowPrcInTradingTab)
			{
				this.timer_1.Stop();
			}
		}

		// Token: 0x06001C97 RID: 7319 RVA: 0x000C1A50 File Offset: 0x000BFC50
		private void method_157(SymbDataSet symbDataSet_0)
		{
			symbDataSet_0.CurrHisDataChanged += this.method_165;
			symbDataSet_0.SpltDayEnter += this.method_176;
			symbDataSet_0.SpltDayLeave += this.method_177;
			symbDataSet_0.SpltDaySpan += this.method_178;
		}

		// Token: 0x06001C98 RID: 7320 RVA: 0x000C1AA8 File Offset: 0x000BFCA8
		private void method_158(SymbDataSet symbDataSet_0)
		{
			symbDataSet_0.CurrHisDataChanged -= this.method_165;
			symbDataSet_0.SpltDayEnter -= this.method_176;
			symbDataSet_0.SpltDayLeave -= this.method_177;
			symbDataSet_0.SpltDaySpan -= this.method_178;
		}

		// Token: 0x06001C99 RID: 7321 RVA: 0x000C1B00 File Offset: 0x000BFD00
		private void method_159(object sender, EventArgs e)
		{
			SymbDataSet symbDataSet_ = sender as SymbDataSet;
			this.method_157(symbDataSet_);
		}

		// Token: 0x06001C9A RID: 7322 RVA: 0x000C1B20 File Offset: 0x000BFD20
		private void method_160(object sender, EventArgs e)
		{
			SymbDataSet symbDataSet_ = sender as SymbDataSet;
			this.method_158(symbDataSet_);
		}

		// Token: 0x06001C9B RID: 7323 RVA: 0x0000BFCF File Offset: 0x0000A1CF
		private void method_161(object sender, EventArgs e)
		{
			this.method_162();
		}

		// Token: 0x06001C9C RID: 7324 RVA: 0x000C1B40 File Offset: 0x000BFD40
		private void method_162()
		{
			this.timer_1.Enabled = false;
			Base.UI.Form.IsStarted = false;
			Base.UI.Form.IsSpanMoveNext = false;
			Base.UI.Form.IsSpanMovePrev = false;
			if (this.toolStripBtnItem_Start.Checked)
			{
				this.toolStripBtnItem_Start.Checked = false;
			}
			this.method_27(Base.Data.CurrDate);
			Base.UI.smethod_133();
			Base.Acct.CurrAccount.LastSymbDT = new DateTime?(Base.Data.CurrDate);
			List<ChtCtrl_KLine> chtCtrl_KLineList = Base.UI.ChtCtrl_KLineList;
			if (chtCtrl_KLineList != null)
			{
				foreach (ChtCtrl_KLine chtCtrl_KLine in chtCtrl_KLineList)
				{
					if (chtCtrl_KLine.Symbol.IsStock && chtCtrl_KLine.SymbDataSet.CurrSymbStSpltList != null && chtCtrl_KLine.SymbDataSet.CurrSymbStSpltList.Any<StSplit>())
					{
						chtCtrl_KLine.Chart_CS.method_217();
					}
				}
			}
		}

		// Token: 0x06001C9D RID: 7325 RVA: 0x0000BFD9 File Offset: 0x0000A1D9
		private void method_163(object sender, FormClosedEventArgs e)
		{
			this.toolStripBtnItem_SelectDate.Checked = false;
		}

		// Token: 0x06001C9E RID: 7326 RVA: 0x000C1C34 File Offset: 0x000BFE34
		private void method_164(EventArgs1 eventArgs1_0)
		{
			DateTime? dateTime = eventArgs1_0.NewSymbLastDT;
			if (dateTime == null)
			{
				dateTime = Base.Acct.CurrAccount.LastSymbDT;
			}
			if (dateTime != null)
			{
				if (eventArgs1_0.IfOnlyNonSyncSelCht)
				{
					ChtCtrl selectedChtCtrl = Base.UI.SelectedChtCtrl;
					if (selectedChtCtrl.SymbDataSet.CurrHisDataSet != null && selectedChtCtrl.SymbDataSet.method_107(dateTime.Value))
					{
						selectedChtCtrl.method_13(dateTime.Value);
					}
					else
					{
						selectedChtCtrl.HisDataPeriodSet = HisDataPeriodSet.smethod_3(selectedChtCtrl.SymbDataSet.SymblID, dateTime.Value, selectedChtCtrl.PeriodType, selectedChtCtrl.PeriodUnits);
						selectedChtCtrl.method_18(dateTime.Value);
					}
				}
				else
				{
					foreach (ChtCtrl chtCtrl in Base.UI.ChtCtrlList)
					{
						if (chtCtrl.SymbDataSet.CurrHisDataSet != null && !chtCtrl.SymbDataSet.IsCurrDateNotListedYet)
						{
							chtCtrl.vmethod_10(chtCtrl.PeriodType, chtCtrl.PeriodUnits, dateTime.Value);
						}
						else
						{
							chtCtrl.HisDataPeriodSet = HisDataPeriodSet.smethod_3(chtCtrl.SymbDataSet.SymblID, dateTime.Value, chtCtrl.PeriodType, chtCtrl.PeriodUnits);
							chtCtrl.method_18(dateTime.Value);
						}
					}
				}
				Base.Data.CurrDate = dateTime.Value;
				Base.Acct.smethod_36();
				Base.Acct.CurrAccount.LastSymbID = new int?(Base.Data.CurrSelectedSymbol.ID);
				if (!eventArgs1_0.IfOnlyNonSyncSelCht || eventArgs1_0.NewSymbID == Base.Data.CurrSelectedSymbol.ID)
				{
					Base.Acct.CurrAccount.LastSymbDT = new DateTime?(dateTime.Value);
				}
				this.method_5();
				this.method_94();
				Class46.smethod_3(Class22.SymblChanged, "NewSymb:" + SymbMgr.smethod_3(eventArgs1_0.NewSymbID).Code + " CurrDate:" + string.Format("{0:yyyy-MM-dd HH:mm}", Base.Data.CurrDate));
			}
			this.method_89();
			if (Base.UI.CurrTradingSymbol.DefaultUnits != null)
			{
				this.numericUpDown_Units.Value = Base.UI.CurrTradingSymbol.DefaultUnits.Value;
			}
			SymbDataSet symbDataSet = Base.Data.smethod_49(Base.UI.CurrTradingSymbol.ID, false);
			if (symbDataSet != null && symbDataSet.HasValidDataSet && this.method_66(symbDataSet.CurrHisData))
			{
				this.method_69();
			}
			this.method_68();
			this.method_67();
		}

		// Token: 0x06001C9F RID: 7327 RVA: 0x000C1EBC File Offset: 0x000C00BC
		private void method_165(object sender, EventArgs e)
		{
			if (Base.UI.Form.IfNoSyncToolBarAndTradingTabPriceUnits && Base.UI.Form.IfFollowPrcInTradingTab)
			{
				SymbDataSet symbDataSet = sender as SymbDataSet;
				if (symbDataSet.CurrSymbol == Base.UI.CurrTradingSymbol)
				{
					this.method_66(symbDataSet.CurrHisDataSet.CurrHisData);
				}
			}
			this.method_45();
		}

		// Token: 0x06001CA0 RID: 7328 RVA: 0x0000BFE9 File Offset: 0x0000A1E9
		private void method_166(object sender, EventArgs e)
		{
			this.method_100(Enum13.const_0, "正在提取数据...");
		}

		// Token: 0x06001CA1 RID: 7329 RVA: 0x0000BFF9 File Offset: 0x0000A1F9
		private void method_167(object sender, EventArgs e)
		{
			this.method_101(string.Empty);
		}

		// Token: 0x06001CA2 RID: 7330 RVA: 0x0000C008 File Offset: 0x0000A208
		private void method_168(object sender, EventArgs e)
		{
			Base.Data.smethod_60();
		}

		// Token: 0x06001CA3 RID: 7331 RVA: 0x0000BFE9 File Offset: 0x0000A1E9
		private void method_169(object sender, EventArgs e)
		{
			this.method_100(Enum13.const_0, "正在提取数据...");
		}

		// Token: 0x06001CA4 RID: 7332 RVA: 0x0000BFF9 File Offset: 0x0000A1F9
		private void method_170(object sender, EventArgs e)
		{
			this.method_101(string.Empty);
		}

		// Token: 0x06001CA5 RID: 7333 RVA: 0x0000C011 File Offset: 0x0000A211
		private void method_171(object sender, EventArgs e)
		{
			if (!this.toolStripStatusLabel_Info.Text.Contains("无法下载数据"))
			{
				this.method_101(string.Empty);
			}
		}

		// Token: 0x06001CA6 RID: 7334 RVA: 0x0000C037 File Offset: 0x0000A237
		private void method_172(object sender, EventArgs e)
		{
			Base.UI.smethod_178();
			MessageBox.Show("该品种在可选期间内无有效数据。", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
		}

		// Token: 0x06001CA7 RID: 7335 RVA: 0x0000C053 File Offset: 0x0000A253
		private void method_173(object sender, EventArgs e)
		{
			this.method_101("提示：无法下载数据，请检查网络后重试。");
		}

		// Token: 0x06001CA8 RID: 7336 RVA: 0x000C1F10 File Offset: 0x000C0110
		private void method_174(EventArgs5 eventArgs5_0)
		{
			if (!string.IsNullOrEmpty(eventArgs5_0.Msg))
			{
				try
				{
					Base.UI.smethod_177(eventArgs5_0.Msg, this.method_4());
					base.Activate();
				}
				catch
				{
				}
			}
		}

		// Token: 0x06001CA9 RID: 7337 RVA: 0x000C1F58 File Offset: 0x000C0158
		private void method_175(object sender, EventArgs e)
		{
			try
			{
				Base.UI.smethod_178();
			}
			catch
			{
			}
		}

		// Token: 0x06001CAA RID: 7338 RVA: 0x0000C062 File Offset: 0x0000A262
		private void method_176(object sender, EventArgs e)
		{
			if (!Base.UI.Form.StockRstMethodIsNone)
			{
				Base.UI.smethod_170((sender as SymbDataSet).SymblID);
			}
		}

		// Token: 0x06001CAB RID: 7339 RVA: 0x000C1F84 File Offset: 0x000C0184
		private void method_177(object sender, EventArgs e)
		{
			if (!Base.UI.Form.StockRstMethodIsNone && Base.UI.Form.IsJustSpanMoved && Base.UI.Form.LastSpanMoveDT != null)
			{
				Base.UI.smethod_170((sender as SymbDataSet).SymblID);
			}
		}

		// Token: 0x06001CAC RID: 7340 RVA: 0x0000C062 File Offset: 0x0000A262
		private void method_178(object sender, EventArgs e)
		{
			if (!Base.UI.Form.StockRstMethodIsNone)
			{
				Base.UI.smethod_170((sender as SymbDataSet).SymblID);
			}
		}

		// Token: 0x06001CAD RID: 7341 RVA: 0x000041AE File Offset: 0x000023AE
		private void method_179(object sender, EventArgs e)
		{
		}

		// Token: 0x06001CAE RID: 7342 RVA: 0x000C1FD0 File Offset: 0x000C01D0
		private void btnItem_页面设置_PopupOpen(object sender, EventArgs e)
		{
			ButtonItem buttonItem = this.btnItem_页面设置;
			buttonItem.SubItems.Clear();
			buttonItem.SubItems.Add(this.新建页面ToolStripMenuItem);
			buttonItem.SubItems.Add(this.保存页面ToolStripMenuItem);
			buttonItem.SubItems.Add(this.另存页面ToolStripMenuItem);
			buttonItem.SubItems.Add(this.选择页面ToolStripMenuItem);
			buttonItem.SubItems.Add(this.删除页面ToolStripMenuItem);
			buttonItem.SubItems.Add(this.自动保存ToolStripMenuItem);
			this.选择页面ToolStripMenuItem.SubItems.Clear();
			foreach (ChartPage chartPage in Base.UI.ChartPageList)
			{
				ButtonItem buttonItem2 = new ButtonItem();
				buttonItem2.Text = chartPage.Name;
				buttonItem2.Click += this.method_182;
				this.选择页面ToolStripMenuItem.SubItems.Add(buttonItem2);
				if (chartPage.Name == Base.UI.Form.CurrentPageName)
				{
					buttonItem2.Checked = true;
				}
			}
			this.删除页面ToolStripMenuItem.SubItems.Clear();
			foreach (ChartPage chartPage2 in Base.UI.ChartPageList)
			{
				ButtonItem buttonItem3 = new ButtonItem();
				buttonItem3.Text = chartPage2.Name;
				buttonItem3.Click += this.method_184;
				this.删除页面ToolStripMenuItem.SubItems.Add(buttonItem3);
				if (chartPage2.Name == Base.UI.Form.CurrentPageName)
				{
					ButtonItem buttonItem4 = buttonItem3;
					buttonItem4.Text += "(当前页面)";
					buttonItem3.Enabled = false;
				}
			}
			this.method_180();
		}

		// Token: 0x06001CAF RID: 7343 RVA: 0x0000C082 File Offset: 0x0000A282
		private void method_180()
		{
			if (Base.UI.Form.IfAutoSavePageOnExit)
			{
				this.自动保存ToolStripMenuItem.Checked = true;
			}
			else
			{
				this.自动保存ToolStripMenuItem.Checked = false;
			}
		}

		// Token: 0x06001CB0 RID: 7344 RVA: 0x0000693A File Offset: 0x00004B3A
		private void 保存页面ToolStripMenuItem_Click(object sender, EventArgs e)
		{
			Base.UI.smethod_81();
		}

		// Token: 0x06001CB1 RID: 7345 RVA: 0x0000C0AC File Offset: 0x0000A2AC
		private void 另存页面ToolStripMenuItem_Click(object sender, EventArgs e)
		{
			PageSaveAsForm pageSaveAsForm = new PageSaveAsForm();
			pageSaveAsForm.FormClosed += new FormClosedEventHandler(this.method_181);
			pageSaveAsForm.Show();
		}

		// Token: 0x06001CB2 RID: 7346 RVA: 0x0000C0CC File Offset: 0x0000A2CC
		private void 自动保存ToolStripMenuItem_Click(object sender, EventArgs e)
		{
			if (this.自动保存ToolStripMenuItem.Checked)
			{
				Base.UI.Form.IfAutoSavePageOnExit = false;
			}
			else
			{
				Base.UI.Form.IfAutoSavePageOnExit = true;
			}
		}

		// Token: 0x06001CB3 RID: 7347 RVA: 0x0000C0F5 File Offset: 0x0000A2F5
		private void method_181(object sender, EventArgs e)
		{
			this.toolStripDropDownButton_Pages.Text = Base.UI.Form.CurrentPageName;
		}

		// Token: 0x06001CB4 RID: 7348 RVA: 0x000C21C4 File Offset: 0x000C03C4
		private void method_182(object sender, EventArgs e)
		{
			ButtonItem buttonItem = (ButtonItem)sender;
			if (buttonItem.Text != Base.UI.Form.CurrentPageName)
			{
				this.method_183(buttonItem.Text);
			}
		}

		// Token: 0x06001CB5 RID: 7349 RVA: 0x000C2200 File Offset: 0x000C0400
		private void method_183(string string_6)
		{
			Class46.smethod_3(Class22.PageChanging, "PageName:" + string_6);
			Base.UI.smethod_177("正在切换页面...", this.method_4());
			this.method_100(Enum13.const_0, "正在切换页面...");
			this.bool_6 = true;
			if (this.bar_AcctTrans.Enabled)
			{
				if (this.bar_AcctTrans.Visible && Base.UI.Form.IsTransTabBarMaximized)
				{
					this.method_197(!Base.UI.Form.IsTransTabBarMaximized, true);
				}
				else if (Base.UI.Form.IfAutoSavePageOnExit)
				{
					Base.UI.smethod_81();
				}
			}
			else if (Base.UI.Form.IsTransTabMaximized)
			{
				Base.UI.TransTabCtrl.SetTransTabsMaximization();
			}
			else if (Base.UI.Form.IfAutoSavePageOnExit)
			{
				Base.UI.smethod_81();
			}
			Base.Acct.smethod_36();
			this.bar_AcctTrans.Enabled = false;
			this.bar_AcctTrans.Hide();
			if (Base.UI.TransTabs != null)
			{
				Base.UI.smethod_154();
				Base.UI.TransTabs.Dispose();
				Base.UI.TransTabs = null;
			}
			Base.UI.smethod_92(string_6);
			if (Base.UI.TransTabCtrl != null)
			{
				Base.UI.TransTabCtrl.vmethod_0();
			}
			this.bool_6 = false;
			Base.UI.smethod_178();
			this.method_100(Enum13.const_0, "页面切换成功！");
		}

		// Token: 0x06001CB6 RID: 7350 RVA: 0x000C2328 File Offset: 0x000C0528
		private void method_184(object sender, EventArgs e)
		{
			this.toolStripDropDownButton_Settings.ShowSubItems = false;
			ButtonItem buttonItem = (ButtonItem)sender;
			if (buttonItem.Text == Base.UI.Form.CurrentPageName)
			{
				MessageBox.Show("该页面当前正在使用中，无法删除！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
			else if (MessageBox.Show("确实要删除页面 '" + buttonItem.Text + "' 吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
			{
				Base.UI.smethod_93(buttonItem.Text);
			}
			this.toolStripDropDownButton_Settings.ShowSubItems = true;
		}

		// Token: 0x06001CB7 RID: 7351 RVA: 0x000C23B4 File Offset: 0x000C05B4
		private void method_185(object sender, EventArgs e)
		{
			ButtonItem buttonItem = (ButtonItem)sender;
			if (buttonItem.Text != Base.UI.Form.CurrentPageName)
			{
				this.method_183(buttonItem.Text);
				this.toolStripDropDownButton_Pages.Text = buttonItem.Text;
			}
		}

		// Token: 0x06001CB8 RID: 7352 RVA: 0x0000C10E File Offset: 0x0000A30E
		private void toolStripDropDownButton_Pages_PopupOpen(object sender, EventArgs e)
		{
			this.method_186(this.toolStripDropDownButton_Pages);
		}

		// Token: 0x06001CB9 RID: 7353 RVA: 0x000C2400 File Offset: 0x000C0600
		private void method_186(ButtonItem buttonItem_0)
		{
			buttonItem_0.SubItems.Clear();
			buttonItem_0.SubItems.Add(this.新建页面ToolStripMenuItem);
			buttonItem_0.SubItems.Add(this.保存页面ToolStripMenuItem);
			buttonItem_0.SubItems.Add(this.另存页面ToolStripMenuItem);
			buttonItem_0.SubItems.Add(this.自动保存ToolStripMenuItem);
			this.method_180();
			for (int i = 0; i < Base.UI.ChartPageList.Count; i++)
			{
				ButtonItem buttonItem = new ButtonItem();
				buttonItem.Text = Base.UI.ChartPageList[i].Name;
				buttonItem.Click += this.method_185;
				if (buttonItem.Text == Base.UI.Form.CurrentPageName)
				{
					buttonItem.Checked = true;
				}
				else
				{
					buttonItem.Checked = false;
				}
				if (i == 0)
				{
					buttonItem.BeginGroup = true;
				}
				buttonItem_0.SubItems.Add(buttonItem);
			}
		}

		// Token: 0x06001CBA RID: 7354 RVA: 0x0000C11E File Offset: 0x0000A31E
		private void toolStripDropDownButton_Accts_PopupOpen(object sender, EventArgs e)
		{
			this.method_187(this.toolStripDropDownButton_Accts, false);
		}

		// Token: 0x06001CBB RID: 7355 RVA: 0x000C24EC File Offset: 0x000C06EC
		private void method_187(ButtonItem buttonItem_0, bool bool_8)
		{
			buttonItem_0.SubItems.Clear();
			if (!bool_8)
			{
				buttonItem_0.SubItems.Add(this.新建账户ToolStripMenuItem);
			}
			for (int i = 0; i < Base.Acct.CurrAccounts.Count; i++)
			{
				Account account = Base.Acct.CurrAccounts[i];
				ButtonItem buttonItem = new ButtonItem();
				buttonItem.Text = account.AcctName;
				buttonItem.Click += this.method_91;
				buttonItem.Tag = account.ID;
				buttonItem.Tooltip = account.Notes;
				if (account.ID == Base.Acct.CurrAccount.ID)
				{
					buttonItem.Checked = true;
				}
				if (!bool_8 && i == 0)
				{
					buttonItem.BeginGroup = true;
				}
				buttonItem_0.SubItems.Add(buttonItem);
			}
		}

		// Token: 0x06001CBC RID: 7356 RVA: 0x000C25B8 File Offset: 0x000C07B8
		private void 新建页面ToolStripMenuItem_Click(object sender, EventArgs e)
		{
			this.toolStrip_Trading.Visible = false;
			this.toolStrip_Ctrls.Visible = false;
			this.toolStrip_Periods.Visible = false;
			this.toolStrip_Setting.Visible = false;
			this.toolStripDropDownButton_Pages.Enabled = false;
			this.toolStrip_ExitCreateNewPg.Location = new Point(base.Width - 60, 0);
			this.toolStrip_ExitCreateNewPg.Dock = DockStyle.Right;
			this.toolStrip_ExitCreateNewPg.Visible = true;
			if (this.bar_AcctTrans.Visible)
			{
				this.bar_AcctTrans.Hide();
			}
			Class308 @class = new Class308();
			this.panel_Below.Controls.Add(@class);
			@class.BringToFront();
			@class.Disposed += this.method_188;
			Base.UI.IsInCreateNewPageState = true;
		}

		// Token: 0x06001CBD RID: 7357 RVA: 0x000C2684 File Offset: 0x000C0884
		private void method_188(object sender, EventArgs e)
		{
			this.toolStrip_Trading.Visible = true;
			this.toolStrip_Ctrls.Visible = true;
			this.toolStrip_Setting.Visible = true;
			this.toolStrip_Periods.Visible = true;
			this.toolStripDropDownButton_Pages.Enabled = true;
			this.toolStrip_ExitCreateNewPg.Visible = false;
			if (Base.UI.Form.IfShowTransTabsBar && this.bar_AcctTrans.Enabled)
			{
				this.bar_AcctTrans.Visible = true;
			}
		}

		// Token: 0x06001CBE RID: 7358 RVA: 0x000C2700 File Offset: 0x000C0900
		private void toolStripButton_ExitCrtNewPg_Click(object sender, EventArgs e)
		{
			foreach (object obj in this.panel_Below.Controls)
			{
				Control control = (Control)obj;
				if (control.GetType() == typeof(Class308))
				{
					this.class308_0 = (Class308)control;
					if (this.class308_0.IfChanged)
					{
						DialogResult dialogResult = MessageBox.Show("是否保存新建页面？", "请确认", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
						if (dialogResult == DialogResult.Yes)
						{
							if (this.class308_0.IfBlankPanelExist)
							{
								MessageBox.Show("页面设置未完成，请完成后继续。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
							}
							else
							{
								PageSaveAsForm pageSaveAsForm = new PageSaveAsForm(new ChartUISettings?(this.class308_0.ChartUI), false);
								pageSaveAsForm.PageSaved += this.method_189;
								pageSaveAsForm.ShowDialog();
							}
						}
						else if (dialogResult == DialogResult.No)
						{
							this.method_190();
						}
					}
					else
					{
						this.method_190();
					}
				}
			}
		}

		// Token: 0x06001CBF RID: 7359 RVA: 0x0000C12F File Offset: 0x0000A32F
		private void method_189(object sender, EventArgs e)
		{
			this.method_190();
		}

		// Token: 0x06001CC0 RID: 7360 RVA: 0x0000C139 File Offset: 0x0000A339
		private void method_190()
		{
			this.class308_0.Dispose();
			this.panel_Below.Controls.Remove(this.class308_0);
			this.class308_0 = null;
			Base.UI.IsInCreateNewPageState = false;
		}

		// Token: 0x06001CC1 RID: 7361 RVA: 0x000C280C File Offset: 0x000C0A0C
		private void method_191()
		{
			if (this.toolStrip_Trading.BarState == eBarState.Docked)
			{
				this.struct6_0.point_0 = this.toolStrip_Trading.Location;
			}
			if (this.toolStrip_Ctrls.BarState == eBarState.Docked)
			{
				this.struct6_0.point_1 = this.toolStrip_Ctrls.Location;
			}
			if (this.toolStrip_Periods.BarState == eBarState.Docked)
			{
				this.struct6_0.point_3 = this.toolStrip_Periods.Location;
			}
			if (this.toolStrip_Setting.BarState == eBarState.Docked)
			{
				this.struct6_0.point_2 = this.toolStrip_Setting.Location;
			}
		}

		// Token: 0x06001CC2 RID: 7362 RVA: 0x0000C16B File Offset: 0x0000A36B
		private void method_192()
		{
			if (this.bar_AcctTrans.Enabled)
			{
				this.nullable_0 = new int?(this.bar_AcctTrans.DockedSite.Height);
			}
		}

		// Token: 0x06001CC3 RID: 7363 RVA: 0x000C28AC File Offset: 0x000C0AAC
		private void method_193()
		{
			if (this.toolStrip_Trading.BarState == eBarState.Docked)
			{
				this.toolStrip_Trading.Location = this.struct6_0.point_0;
			}
			if (this.toolStrip_Ctrls.BarState == eBarState.Docked)
			{
				this.toolStrip_Ctrls.Location = this.struct6_0.point_1;
			}
			if (this.toolStrip_Periods.BarState == eBarState.Docked)
			{
				this.toolStrip_Periods.Location = this.struct6_0.point_3;
			}
			if (this.toolStrip_Setting.BarState == eBarState.Docked)
			{
				this.toolStrip_Setting.Location = this.struct6_0.point_2;
			}
		}

		// Token: 0x06001CC4 RID: 7364 RVA: 0x000C294C File Offset: 0x000C0B4C
		private void method_194()
		{
			if (this.bar_AcctTrans.Enabled && !this.bar_AcctTrans.AutoHide && this.nullable_0 != null)
			{
				this.bar_AcctTrans.DockedSite.Height = this.nullable_0.Value;
			}
		}

		// Token: 0x06001CC5 RID: 7365 RVA: 0x0000C197 File Offset: 0x0000A397
		private void bar_AcctTrans_BarDock(object sender, EventArgs e)
		{
			Base.UI.Form.AcctTransBar_DockSide = this.bar_AcctTrans.DockSide;
		}

		// Token: 0x06001CC6 RID: 7366 RVA: 0x0000C1B0 File Offset: 0x0000A3B0
		private void bar_AcctTrans_AutoHideDisplay(object sender, AutoHideDisplayEventArgs e)
		{
			if (this.bar_AcctTrans.Items.Count > 0)
			{
				this.bar_AcctTrans.Items[0].Visible = true;
			}
		}

		// Token: 0x06001CC7 RID: 7367 RVA: 0x000C29A0 File Offset: 0x000C0BA0
		private void bar_AcctTrans_SizeChanged(object sender, EventArgs e)
		{
			DockSite dockSite;
			int num;
			if (this.bar_AcctTrans.DockSide == eDockSide.Bottom)
			{
				dockSite = this.dockSite_Below;
				num = Base.UI.Form.OriDockSiteBelowHeight;
			}
			else
			{
				dockSite = this.dockSite_Top;
				num = Base.UI.Form.OriDockSiteTopHeight;
			}
			if (dockSite.Height > base.Height - 70)
			{
				if (num < base.Height - 70)
				{
					dockSite.Height = num;
				}
				else
				{
					dockSite.Height = base.Height - 70;
				}
			}
		}

		// Token: 0x06001CC8 RID: 7368 RVA: 0x0000C1DE File Offset: 0x0000A3DE
		private void method_195(object sender, EventArgs e)
		{
			if (Base.UI.TransTabs != null)
			{
				Base.UI.TransTabs.Focus();
			}
			this.method_196();
		}

		// Token: 0x06001CC9 RID: 7369 RVA: 0x0000C1FA File Offset: 0x0000A3FA
		private void method_196()
		{
			this.method_197(!Base.UI.Form.IsTransTabBarMaximized, false);
		}

		// Token: 0x06001CCA RID: 7370 RVA: 0x000C2A18 File Offset: 0x000C0C18
		private void method_197(bool bool_8, bool bool_9 = false)
		{
			if (bool_8)
			{
				if (!Base.UI.Form.IsTransTabBarMaximized)
				{
					if (this.bar_AcctTrans.DockSide == eDockSide.Bottom)
					{
						Base.UI.Form.OriDockSiteBelowHeight = this.dockSite_Below.Height;
					}
					else
					{
						Base.UI.Form.OriDockSiteTopHeight = this.dockSite_Top.Height;
					}
				}
				if (bool_9 && Base.UI.Form.IfAutoSavePageOnExit)
				{
					Base.UI.smethod_81();
				}
			}
			else if (this.bar_AcctTrans.DockSide == eDockSide.Bottom)
			{
				this.dockSite_Below.Height = Base.UI.Form.OriDockSiteBelowHeight;
			}
			else
			{
				this.dockSite_Top.Height = Base.UI.Form.OriDockSiteTopHeight;
			}
			Base.UI.Form.IsTransTabBarMaximized = bool_8;
			this.method_198();
		}

		// Token: 0x06001CCB RID: 7371 RVA: 0x000C2AD4 File Offset: 0x000C0CD4
		private bool method_198()
		{
			bool result;
			if (Base.UI.Form.IsTransTabBarMaximized)
			{
				if (this.bar_AcctTrans.DockSide == eDockSide.Bottom)
				{
					this.dockSite_Below.Height = Base.UI.Form.OriDockSiteBelowHeight + this.panel_Below.Height;
				}
				else if (this.bar_AcctTrans.DockSide == eDockSide.Top)
				{
					this.dockSite_Top.Height = Base.UI.Form.OriDockSiteTopHeight + this.panel_Below.Height;
				}
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06001CCC RID: 7372 RVA: 0x000C2B58 File Offset: 0x000C0D58
		private void toolStrip_Ctrls_BarDock(object sender, EventArgs e)
		{
			if (this.toolStrip_Ctrls.DockSide != eDockSide.Left)
			{
				if (this.toolStrip_Ctrls.DockSide != eDockSide.Right)
				{
					if (this.slider_Speed.SliderOrientation != eOrientation.Horizontal)
					{
						this.slider_Speed.SliderOrientation = eOrientation.Horizontal;
						return;
					}
					return;
				}
			}
			if (this.slider_Speed.SliderOrientation != eOrientation.Vertical)
			{
				this.slider_Speed.SliderOrientation = eOrientation.Vertical;
			}
		}

		// Token: 0x06001CCD RID: 7373 RVA: 0x0000C212 File Offset: 0x0000A412
		private void toolStrip_Ctrls_BarUndock(object sender, EventArgs e)
		{
			if (this.slider_Speed.SliderOrientation != eOrientation.Horizontal)
			{
				this.slider_Speed.SliderOrientation = eOrientation.Horizontal;
			}
		}

		// Token: 0x06001CCE RID: 7374 RVA: 0x0000C22F File Offset: 0x0000A42F
		private void bar_AcctTrans_Closing(object sender, BarClosingEventArgs e)
		{
			if (Base.UI.Form.IfShowTransTabsBar)
			{
				Base.UI.Form.IfShowTransTabsBar = false;
			}
		}

		// Token: 0x06001CCF RID: 7375 RVA: 0x000C2BBC File Offset: 0x000C0DBC
		private void method_199()
		{
			if (TApp.IsTrialUser && TApp.smethod_6().Count <= 1)
			{
				this.class41_0 = new Class41(20000, 10000);
				this.class41_0.BeforeTooltipDisplay += this.method_200;
				eTooltipColor color = eTooltipColor.Gray;
				if (TApp.IsTrialUser)
				{
					string str = "versions";
					this.class41_0.method_1("SymbTab", Base.UI.TransTabs, Enum0.const_2, new SuperTooltipInfo("品种不全？", "<a name=\"noshow\" href=\"SymbTab\">不再显示</a>", "免费版品种有限，具体请参考<a name=\"web\" href=\"https://www.tradingexer.com/" + str + "\">「版本说明」</a>", null, null, color));
				}
				this.class41_0.method_1("StartBtn", this.toolStripBtnItem_Start, Enum0.const_1, new SuperTooltipInfo("点击开始按钮启动行情自动回放", "<a name=\"noshow\" href=\"StartBtn\">不再显示</a>", "软件同时支持手动回放\r\n具体请参考帮助<a name=\"help\" href=\"行情控制.html\">「行情回放」</a>", Class372.play, null, color));
				this.class41_0.method_1("DateBtn", this.toolStripBtnItem_SelectDate, Enum0.const_1, new SuperTooltipInfo("点击期间按钮选择当前行情时间", "<a name=\"noshow\" href=\"DateBtn\">不再显示</a>", "可跳转到任意历史时点\r\n具体请参考帮助<a name=\"help\" href=\"行情控制-起止时间设定.html\">「时间设定」</a>", Class372.calendar_date, null, color));
				this.class41_0.method_1("ChgSymbLbl", this.labelItem_品种, Enum0.const_0, new SuperTooltipInfo("点击下方弹出按钮切换品种", "<a name=\"noshow\" href=\"ChgSymbLbl\">不再显示</a>", "还可以在软件界面直接输入代码切换品种\r\n具体请参考帮助<a name=\"help\" href=\"行情控制-品种切换.html\">「品种切换」</a>", null, null, color));
				this.class41_0.method_1("ChgPageLbl", this.labelItem_页面, Enum0.const_0, new SuperTooltipInfo("点击下方弹出按钮切换页面", "<a name=\"noshow\" href=\"ChgPageLbl\">不再显示</a>", "软件支持自定义页面\r\n具体请参考帮助<a name=\"help\" href=\"页面设置.html\">「页面设置」</a>", null, null, color));
				this.class41_0.method_1("BlindTestIco", this.toolStripStatusLabel_BlindTest, Enum0.const_0, new SuperTooltipInfo("点击下方图标开启双盲模式", "<a name=\"noshow\" href=\"BlindTestIco\">不再显示</a>", "双盲模式隐藏品种和时间\r\n具体请参考帮助<a name=\"help\" href=\"双盲测试.html\">「双盲测试」</a>", Class372.BlindTest_48x48, null, color));
			}
		}

		// Token: 0x06001CD0 RID: 7376 RVA: 0x000C2D50 File Offset: 0x000C0F50
		private void method_200(object sender, SuperTooltipEventArgs e)
		{
			if (Form.ActiveForm == this && !Base.UI.IsInCreateNewPageState)
			{
				Class44 @class = sender as Class44;
				if (@class.Name == "SymbTab")
				{
					if (Base.UI.TransTabs == null || Base.UI.TransTabs.SelectedTabIndex != 0)
					{
						e.Cancel = true;
					}
				}
				else if (@class.Name == "StartBtn")
				{
					if (this.toolStripBtnItem_Start.Text != "开始")
					{
						e.Cancel = true;
					}
				}
				else if (@class.Name == "BlindTestBtn")
				{
					if (this.toolStripStatusLabel_BlindTest.Image.Tag != null && !(this.toolStripStatusLabel_BlindTest.Image.Tag.ToString() != "Enabled"))
					{
						e.TooltipInfo.HeaderText = "点击下方图标关闭双盲模式";
					}
					else
					{
						e.TooltipInfo.HeaderText = "点击下方图标开启双盲模式";
					}
				}
			}
			else
			{
				e.Cancel = true;
			}
		}

		// Token: 0x06001CD1 RID: 7377 RVA: 0x0000C24A File Offset: 0x0000A44A
		private void toolStripBtnItem_Start_Click(object sender, EventArgs e)
		{
			this.method_201();
		}

		// Token: 0x06001CD2 RID: 7378 RVA: 0x0000C254 File Offset: 0x0000A454
		private void method_201()
		{
			if (!this.timer_1.Enabled)
			{
				this.method_202();
			}
			else
			{
				this.method_204();
			}
		}

		// Token: 0x06001CD3 RID: 7379 RVA: 0x000C2E54 File Offset: 0x000C1054
		public void method_202()
		{
			if (!this.timer_1.Enabled)
			{
				this.IsInRetroMode = false;
				this.method_203();
				this.timer_1.Start();
				Class46.smethod_3(Class22.AutoPlayStarted, "Type:" + ((Base.UI.Form.AutoPlayPeriodType != null) ? Base.UI.Form.AutoPlayPeriodType.Value.ToString() : "null") + "    Unit:" + ((Base.UI.Form.AutoPlayPeriodUnits != null) ? Base.UI.Form.AutoPlayPeriodUnits.Value.ToString() : "1"));
			}
			this.toolStripBtnItem_Start.Text = "暂停";
			this.toolStripBtnItem_Start.Image = Class372.pause;
			Base.UI.IsStartJustClicked = true;
			Base.UI.Form.IsStarted = true;
		}

		// Token: 0x06001CD4 RID: 7380 RVA: 0x000C2F48 File Offset: 0x000C1148
		private void method_203()
		{
			int? num = this.method_103();
			if (num != null)
			{
				this.timer_1.Interval = (101 - num.Value) * 100;
			}
			else
			{
				this.timer_1.Interval = 1100;
			}
		}

		// Token: 0x06001CD5 RID: 7381 RVA: 0x000C2F94 File Offset: 0x000C1194
		public void method_204()
		{
			if (this.timer_1.Enabled)
			{
				this.timer_1.Stop();
				Class46.smethod_3(Class22.AutoPlayStopped, "Type:" + ((Base.UI.Form.AutoPlayPeriodType != null) ? Base.UI.Form.AutoPlayPeriodType.Value.ToString() : "null") + "  Unit:" + ((Base.UI.Form.AutoPlayPeriodUnits != null) ? Base.UI.Form.AutoPlayPeriodUnits.Value.ToString() : "1"));
			}
			if (this.toolStripBtnItem_Start.Text != "开始")
			{
				this.toolStripBtnItem_Start.Text = "开始";
			}
			if (this.toolStripBtnItem_Start.Image != Class372.play)
			{
				this.toolStripBtnItem_Start.Image = Class372.play;
			}
			if (Base.UI.Form.IsStarted)
			{
				Base.UI.Form.IsStarted = false;
			}
		}

		// Token: 0x06001CD6 RID: 7382 RVA: 0x0000C273 File Offset: 0x0000A473
		public void method_205()
		{
			if (Base.UI.Form.IsStarted)
			{
				this.method_204();
				this.method_202();
			}
		}

		// Token: 0x06001CD7 RID: 7383 RVA: 0x000C30A8 File Offset: 0x000C12A8
		private void timer_1_Tick(object sender, EventArgs e)
		{
			if (Base.UI.ChtCtrlList == null)
			{
				this.method_204();
			}
			else
			{
				PeriodType? autoPlayPeriodType = Base.UI.Form.AutoPlayPeriodType;
				if (autoPlayPeriodType.GetValueOrDefault() == PeriodType.ByMins & autoPlayPeriodType != null)
				{
					int? autoPlayPeriodUnits = Base.UI.Form.AutoPlayPeriodUnits;
					if (autoPlayPeriodUnits.GetValueOrDefault() == 1 & autoPlayPeriodUnits != null)
					{
						if (Base.UI.smethod_126())
						{
							this.method_44();
							return;
						}
						goto IL_C1;
					}
				}
				ChtCtrl chtCtrl = Base.UI.ChtCtrlList.FirstOrDefault(new Func<ChtCtrl, bool>(MainForm.<>c.<>9.method_20));
				if (chtCtrl != null)
				{
					Base.UI.smethod_118(chtCtrl);
				}
				else
				{
					this.method_204();
					Base.UI.Form.AutoPlayPeriodType = new PeriodType?(PeriodType.ByMins);
					Base.UI.Form.AutoPlayPeriodUnits = new int?(1);
				}
				IL_C1:;
			}
		}

		// Token: 0x06001CD8 RID: 7384 RVA: 0x000C3178 File Offset: 0x000C1378
		private Point method_206()
		{
			return new Point(base.Location.X + (base.Width - this.quickWnd_0.Width) - 10, base.Location.Y + (base.Height - this.quickWnd_0.Height) - this.statusBar.Height - 10);
		}

		// Token: 0x06001CD9 RID: 7385 RVA: 0x000C31E4 File Offset: 0x000C13E4
		private void method_207(object sender, EventArgs10 e)
		{
			base.BringToFront();
			QuickWndItem quickWndItem = e.QuickWndItem;
			if (quickWndItem != null && quickWndItem.LinkObj != null)
			{
				if (quickWndItem.LinkObj is Class264)
				{
					switch ((quickWndItem.LinkObj as Class264).Id)
					{
					case 250:
						this.method_58(PeriodType.ByMins, new int?(1));
						break;
					case 251:
						this.method_58(PeriodType.ByMins, new int?(3));
						break;
					case 252:
						this.method_58(PeriodType.ByMins, new int?(5));
						break;
					case 253:
						this.method_58(PeriodType.ByMins, new int?(15));
						break;
					case 254:
						this.method_58(PeriodType.ByMins, new int?(30));
						break;
					case 255:
						this.method_58(PeriodType.ByMins, new int?(60));
						break;
					case 256:
						this.method_58(PeriodType.ByMins, new int?(120));
						break;
					case 257:
						this.method_58(PeriodType.ByMins, new int?(240));
						break;
					case 258:
						this.method_58(PeriodType.ByDay, null);
						break;
					case 259:
						this.method_58(PeriodType.ByWeek, null);
						break;
					case 260:
						this.method_58(PeriodType.ByMonth, null);
						break;
					case 261:
						this.method_58(PeriodType.ByMins, new int?(10));
						break;
					case 270:
						this.method_64(Enum4.const_0);
						break;
					case 271:
						this.method_64(Enum4.const_1);
						break;
					case 272:
						this.method_64(Enum4.const_2);
						break;
					}
				}
				else if (quickWndItem.LinkObj.GetType() == typeof(UserDefineIndScript))
				{
					if (Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl is ChtCtrl_KLine)
					{
						object linkObj = quickWndItem.LinkObj;
						ChtCtrl_KLine chtCtrl_KLine = Base.UI.SelectedChtCtrl as ChtCtrl_KLine;
						UserDefineIndScript userDefineIndScript = quickWndItem.LinkObj as UserDefineIndScript;
						if (userDefineIndScript != null)
						{
							chtCtrl_KLine.method_94((chtCtrl_KLine.MainChart as ChartKLine).DP, userDefineIndScript);
							chtCtrl_KLine.method_114(userDefineIndScript);
						}
					}
				}
				else if (quickWndItem.LinkObj is StkSymbol)
				{
					StkSymbol stkSymbol_ = quickWndItem.LinkObj as StkSymbol;
					Base.UI.smethod_177("正在提取数据...", this.method_4());
					Base.UI.smethod_175(stkSymbol_);
					Base.UI.smethod_178();
				}
			}
		}

		// Token: 0x06001CDA RID: 7386 RVA: 0x0000C28F File Offset: 0x0000A48F
		private void quickWnd_0_LostFocus(object sender, EventArgs e)
		{
			base.BringToFront();
		}

		// Token: 0x1700048F RID: 1167
		// (get) Token: 0x06001CDB RID: 7387 RVA: 0x000C3454 File Offset: 0x000C1654
		// (set) Token: 0x06001CDC RID: 7388 RVA: 0x0000C299 File Offset: 0x0000A499
		public bool IsInRetroMode
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
				if (this.bool_0)
				{
					this.method_204();
					if (this.toolStripBtnItem_Start.Checked)
					{
						this.toolStripBtnItem_Start.Checked = false;
					}
				}
			}
		}

		// Token: 0x17000490 RID: 1168
		// (get) Token: 0x06001CDD RID: 7389 RVA: 0x000C346C File Offset: 0x000C166C
		protected CreateParams CreateParams
		{
			get
			{
				CreateParams createParams = base.CreateParams;
				if (this.bool_7)
				{
					createParams.ExStyle |= 33554432;
				}
				return createParams;
			}
		}

		// Token: 0x17000491 RID: 1169
		// (get) Token: 0x06001CDE RID: 7390 RVA: 0x000C34A0 File Offset: 0x000C16A0
		// (set) Token: 0x06001CDF RID: 7391 RVA: 0x0000C2CB File Offset: 0x0000A4CB
		public bool EnableFormLevelDoubleBuffering
		{
			get
			{
				return this.bool_7;
			}
			set
			{
				this.bool_7 = value;
			}
		}

		// Token: 0x17000492 RID: 1170
		// (get) Token: 0x06001CE0 RID: 7392 RVA: 0x000C34B8 File Offset: 0x000C16B8
		public bool TransTabBarEnabled
		{
			get
			{
				return this.bar_AcctTrans.Enabled;
			}
		}

		// Token: 0x06001CE1 RID: 7393 RVA: 0x0000C2D6 File Offset: 0x0000A4D6
		private void method_208()
		{
			new MainForm.Delegate31(Base.Trading.smethod_59).BeginInvoke(null, null);
		}

		// Token: 0x06001CE2 RID: 7394 RVA: 0x0000C2EE File Offset: 0x0000A4EE
		private void method_209(OrderType orderType_0)
		{
			new MainForm.Delegate32(this.method_133).BeginInvoke(orderType_0, null, null);
		}

		// Token: 0x06001CE3 RID: 7395 RVA: 0x0000C307 File Offset: 0x0000A507
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000DAB RID: 3499
		private List<StkSymbol> list_0;

		// Token: 0x04000DAC RID: 3500
		private MainForm.Struct6 struct6_0;

		// Token: 0x04000DAD RID: 3501
		private int? nullable_0;

		// Token: 0x04000DAE RID: 3502
		private bool bool_0;

		// Token: 0x04000DAF RID: 3503
		private System.Windows.Forms.Timer timer_0;

		// Token: 0x04000DB0 RID: 3504
		private System.Windows.Forms.Timer timer_1 = new System.Windows.Forms.Timer();

		// Token: 0x04000DB1 RID: 3505
		private System.Timers.Timer timer_2;

		// Token: 0x04000DB2 RID: 3506
		private bool bool_1;

		// Token: 0x04000DB3 RID: 3507
		private int int_0;

		// Token: 0x04000DB4 RID: 3508
		private decimal decimal_0;

		// Token: 0x04000DB5 RID: 3509
		private bool bool_2;

		// Token: 0x04000DB6 RID: 3510
		private bool bool_3;

		// Token: 0x04000DB7 RID: 3511
		private bool bool_4;

		// Token: 0x04000DB8 RID: 3512
		private bool bool_5;

		// Token: 0x04000DB9 RID: 3513
		private string string_0;

		// Token: 0x04000DBA RID: 3514
		private Class41 class41_0;

		// Token: 0x04000DBB RID: 3515
		private QuickWnd quickWnd_0;

		// Token: 0x04000DBC RID: 3516
		private Class308 class308_0;

		// Token: 0x04000DBD RID: 3517
		private const string string_1 = "正在提取数据...";

		// Token: 0x04000DBE RID: 3518
		private const string string_2 = "正在切换账户...";

		// Token: 0x04000DBF RID: 3519
		private const string string_3 = "交易练习者更新程序";

		// Token: 0x04000DC0 RID: 3520
		private const string string_4 = "TExUpd.exe";

		// Token: 0x04000DC1 RID: 3521
		private const string string_5 = " - 双盲测试模式";

		// Token: 0x04000DC2 RID: 3522
		private const int int_1 = 274;

		// Token: 0x04000DC3 RID: 3523
		private const int int_2 = 257;

		// Token: 0x04000DC4 RID: 3524
		private const int int_3 = 514;

		// Token: 0x04000DC5 RID: 3525
		private const int int_4 = 517;

		// Token: 0x04000DC6 RID: 3526
		private const int int_5 = 513;

		// Token: 0x04000DC7 RID: 3527
		private const int int_6 = 516;

		// Token: 0x04000DC8 RID: 3528
		private const int int_7 = 61536;

		// Token: 0x04000DC9 RID: 3529
		private const int int_8 = 61472;

		// Token: 0x04000DCA RID: 3530
		private const int int_9 = 61488;

		// Token: 0x04000DCB RID: 3531
		private const int int_10 = 61728;

		// Token: 0x04000DCC RID: 3532
		private bool bool_6;

		// Token: 0x04000DCD RID: 3533
		private bool bool_7;

		// Token: 0x0200027F RID: 639
		private struct Struct6
		{
			// Token: 0x04000E44 RID: 3652
			public Point point_0;

			// Token: 0x04000E45 RID: 3653
			public Point point_1;

			// Token: 0x04000E46 RID: 3654
			public Point point_2;

			// Token: 0x04000E47 RID: 3655
			public Point point_3;
		}

		// Token: 0x02000280 RID: 640
		// (Invoke) Token: 0x06001CE6 RID: 7398
		private delegate bool Delegate31();

		// Token: 0x02000281 RID: 641
		// (Invoke) Token: 0x06001CEA RID: 7402
		public delegate void Delegate32(OrderType orderType);

		// Token: 0x02000282 RID: 642
		[CompilerGenerated]
		private sealed class Class328
		{
			// Token: 0x06001CEE RID: 7406 RVA: 0x000C6DF8 File Offset: 0x000C4FF8
			internal bool method_0(QuickWndItem quickWndItem_0)
			{
				bool result;
				if (!quickWndItem_0.Code.ToLower().StartsWith(this.string_0))
				{
					if (!string.IsNullOrEmpty(quickWndItem_0.HidenCode))
					{
						result = quickWndItem_0.HidenCode.StartsWith(this.string_0);
					}
					else
					{
						result = false;
					}
				}
				else
				{
					result = true;
				}
				return result;
			}

			// Token: 0x04000E48 RID: 3656
			public string string_0;
		}

		// Token: 0x02000283 RID: 643
		[CompilerGenerated]
		private sealed class Class329
		{
			// Token: 0x06001CF0 RID: 7408 RVA: 0x0000C328 File Offset: 0x0000A528
			internal void method_0()
			{
				this.mainForm_0.toolStripStatusLabel_Info.Text = this.string_0;
			}

			// Token: 0x04000E49 RID: 3657
			public MainForm mainForm_0;

			// Token: 0x04000E4A RID: 3658
			public string string_0;
		}

		// Token: 0x02000284 RID: 644
		[CompilerGenerated]
		private sealed class Class330
		{
			// Token: 0x06001CF2 RID: 7410 RVA: 0x0000C342 File Offset: 0x0000A542
			internal void method_0()
			{
				this.class329_0.mainForm_0.toolStripStatusLabel_Info.Text = "程序更新过程中遇到问题（" + this.exception_0.InnerException.Message + "），已取消。";
			}

			// Token: 0x04000E4B RID: 3659
			public Exception exception_0;

			// Token: 0x04000E4C RID: 3660
			public MainForm.Class329 class329_0;
		}

		// Token: 0x02000286 RID: 646
		[CompilerGenerated]
		private sealed class Class331
		{
			// Token: 0x06001D0B RID: 7435 RVA: 0x000C6F18 File Offset: 0x000C5118
			internal bool method_0(SymbDataSet symbDataSet_1)
			{
				return symbDataSet_1.SymblID != this.symbDataSet_0.SymblID;
			}

			// Token: 0x04000E63 RID: 3683
			public SymbDataSet symbDataSet_0;
		}

		// Token: 0x02000287 RID: 647
		[CompilerGenerated]
		private sealed class Class332
		{
			// Token: 0x06001D0D RID: 7437 RVA: 0x000C6F40 File Offset: 0x000C5140
			internal bool method_0(ChtCtrl chtCtrl_0)
			{
				return chtCtrl_0.method_34(new PeriodType?(this.periodType_0), this.nullable_0);
			}

			// Token: 0x04000E64 RID: 3684
			public PeriodType periodType_0;

			// Token: 0x04000E65 RID: 3685
			public int? nullable_0;
		}

		// Token: 0x02000288 RID: 648
		[CompilerGenerated]
		private sealed class Class333
		{
			// Token: 0x06001D0F RID: 7439 RVA: 0x000C6F68 File Offset: 0x000C5168
			internal bool method_0(HisDataPeriodSet hisDataPeriodSet_0)
			{
				return hisDataPeriodSet_0.method_45(this.chtCtrl_0.HisDataPeriodSet);
			}

			// Token: 0x04000E66 RID: 3686
			public ChtCtrl chtCtrl_0;
		}

		// Token: 0x02000289 RID: 649
		[CompilerGenerated]
		private sealed class Class334
		{
			// Token: 0x06001D11 RID: 7441 RVA: 0x000C6F8C File Offset: 0x000C518C
			internal bool method_0(ExchgHouse exchgHouse_1)
			{
				return exchgHouse_1.ID == this.int_0;
			}

			// Token: 0x06001D12 RID: 7442 RVA: 0x000C6FAC File Offset: 0x000C51AC
			internal bool method_1(StkSymbol stkSymbol_0)
			{
				return stkSymbol_0.ExchangeID == this.exchgHouse_0.ID;
			}

			// Token: 0x04000E67 RID: 3687
			public int int_0;

			// Token: 0x04000E68 RID: 3688
			public ExchgHouse exchgHouse_0;
		}

		// Token: 0x0200028A RID: 650
		[CompilerGenerated]
		private sealed class Class335
		{
			// Token: 0x06001D14 RID: 7444 RVA: 0x000C6FD0 File Offset: 0x000C51D0
			internal bool method_0(StkSymbol stkSymbol_0)
			{
				return stkSymbol_0.MstSymbol.CNName == this.string_0;
			}

			// Token: 0x04000E69 RID: 3689
			public string string_0;
		}
	}
}
