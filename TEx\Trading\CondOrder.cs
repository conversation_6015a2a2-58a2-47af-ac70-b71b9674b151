﻿using System;
using TEx.Comn;

namespace TEx.Trading
{
	// Token: 0x020003AD RID: 941
	[Serializable]
	internal class CondOrder
	{
		// Token: 0x1700065C RID: 1628
		// (get) Token: 0x060025F7 RID: 9719 RVA: 0x000FA5E8 File Offset: 0x000F87E8
		// (set) Token: 0x060025F8 RID: 9720 RVA: 0x0000E75F File Offset: 0x0000C95F
		public int ID
		{
			get
			{
				return this._ID;
			}
			set
			{
				if (this._ID != value)
				{
					this._ID = value;
				}
			}
		}

		// Token: 0x1700065D RID: 1629
		// (get) Token: 0x060025F9 RID: 9721 RVA: 0x000FA600 File Offset: 0x000F8800
		// (set) Token: 0x060025FA RID: 9722 RVA: 0x0000E773 File Offset: 0x0000C973
		public int AcctID
		{
			get
			{
				return this._AcctID;
			}
			set
			{
				if (this._AcctID != value)
				{
					this._AcctID = value;
				}
			}
		}

		// Token: 0x1700065E RID: 1630
		// (get) Token: 0x060025FB RID: 9723 RVA: 0x000FA618 File Offset: 0x000F8818
		// (set) Token: 0x060025FC RID: 9724 RVA: 0x0000E787 File Offset: 0x0000C987
		public int SymbID
		{
			get
			{
				return this._SymbID;
			}
			set
			{
				if (this._SymbID != value)
				{
					this._SymbID = value;
				}
			}
		}

		// Token: 0x1700065F RID: 1631
		// (get) Token: 0x060025FD RID: 9725 RVA: 0x000FA630 File Offset: 0x000F8830
		// (set) Token: 0x060025FE RID: 9726 RVA: 0x000FA648 File Offset: 0x000F8848
		public int? TransID
		{
			get
			{
				return this._TransID;
			}
			set
			{
				int? transID = this._TransID;
				int? num = value;
				if (!(transID.GetValueOrDefault() == num.GetValueOrDefault() & transID != null == (num != null)))
				{
					this._TransID = value;
				}
			}
		}

		// Token: 0x17000660 RID: 1632
		// (get) Token: 0x060025FF RID: 9727 RVA: 0x000FA68C File Offset: 0x000F888C
		// (set) Token: 0x06002600 RID: 9728 RVA: 0x0000E79B File Offset: 0x0000C99B
		public decimal Units
		{
			get
			{
				return this._Units;
			}
			set
			{
				if (this._Units != value)
				{
					this._Units = value;
				}
			}
		}

		// Token: 0x17000661 RID: 1633
		// (get) Token: 0x06002601 RID: 9729 RVA: 0x000FA6A4 File Offset: 0x000F88A4
		// (set) Token: 0x06002602 RID: 9730 RVA: 0x0000E7B4 File Offset: 0x0000C9B4
		public OrderType OrderType
		{
			get
			{
				return this._OrderType;
			}
			set
			{
				if (this._OrderType != value)
				{
					this._OrderType = value;
				}
			}
		}

		// Token: 0x17000662 RID: 1634
		// (get) Token: 0x06002603 RID: 9731 RVA: 0x000FA6BC File Offset: 0x000F88BC
		// (set) Token: 0x06002604 RID: 9732 RVA: 0x0000E7C8 File Offset: 0x0000C9C8
		public ComparisonOpt ComparisonOpt
		{
			get
			{
				return this._ComparisonOpt;
			}
			set
			{
				if (this._ComparisonOpt != value)
				{
					this._ComparisonOpt = value;
				}
			}
		}

		// Token: 0x17000663 RID: 1635
		// (get) Token: 0x06002605 RID: 9733 RVA: 0x000FA6D4 File Offset: 0x000F88D4
		// (set) Token: 0x06002606 RID: 9734 RVA: 0x0000E7DC File Offset: 0x0000C9DC
		public decimal CondPrice
		{
			get
			{
				return this._CondPrice;
			}
			set
			{
				if (this._CondPrice != value)
				{
					this._CondPrice = value;
				}
			}
		}

		// Token: 0x17000664 RID: 1636
		// (get) Token: 0x06002607 RID: 9735 RVA: 0x000FA6EC File Offset: 0x000F88EC
		// (set) Token: 0x06002608 RID: 9736 RVA: 0x0000E7F5 File Offset: 0x0000C9F5
		public decimal ExePrice
		{
			get
			{
				return this._ExePrice;
			}
			set
			{
				if (this._ExePrice != value)
				{
					this._ExePrice = value;
				}
			}
		}

		// Token: 0x17000665 RID: 1637
		// (get) Token: 0x06002609 RID: 9737 RVA: 0x000FA704 File Offset: 0x000F8904
		// (set) Token: 0x0600260A RID: 9738 RVA: 0x0000E80E File Offset: 0x0000CA0E
		public OrderStatus OrderStatus
		{
			get
			{
				return this._OrderStatus;
			}
			set
			{
				if (this._OrderStatus != value)
				{
					this._OrderStatus = value;
				}
			}
		}

		// Token: 0x17000666 RID: 1638
		// (get) Token: 0x0600260B RID: 9739 RVA: 0x000FA71C File Offset: 0x000F891C
		// (set) Token: 0x0600260C RID: 9740 RVA: 0x0000E822 File Offset: 0x0000CA22
		public DateTime CreateTime
		{
			get
			{
				return this._CreateTime;
			}
			set
			{
				if (this._CreateTime != value)
				{
					this._CreateTime = value;
				}
			}
		}

		// Token: 0x17000667 RID: 1639
		// (get) Token: 0x0600260D RID: 9741 RVA: 0x000FA734 File Offset: 0x000F8934
		// (set) Token: 0x0600260E RID: 9742 RVA: 0x000FA74C File Offset: 0x000F894C
		public DateTime? UpdateTime
		{
			get
			{
				return this._UpdateTime;
			}
			set
			{
				if (this._UpdateTime != value)
				{
					this._UpdateTime = value;
				}
			}
		}

		// Token: 0x17000668 RID: 1640
		// (get) Token: 0x0600260F RID: 9743 RVA: 0x000FA7A0 File Offset: 0x000F89A0
		// (set) Token: 0x06002610 RID: 9744 RVA: 0x0000E83B File Offset: 0x0000CA3B
		public string Notes
		{
			get
			{
				return this._Notes;
			}
			set
			{
				if (this._Notes != value)
				{
					this._Notes = value;
				}
			}
		}

		// Token: 0x17000669 RID: 1641
		// (get) Token: 0x06002611 RID: 9745 RVA: 0x000FA7B8 File Offset: 0x000F89B8
		public StkSymbol StkSymbol
		{
			get
			{
				return SymbMgr.smethod_3(this.SymbID);
			}
		}

		// Token: 0x1700066A RID: 1642
		// (get) Token: 0x06002612 RID: 9746 RVA: 0x000FA7D4 File Offset: 0x000F89D4
		// (set) Token: 0x06002613 RID: 9747 RVA: 0x0000E854 File Offset: 0x0000CA54
		public bool IsROpen
		{
			get
			{
				return this._IsROpen;
			}
			set
			{
				if (this._IsROpen != value)
				{
					this._IsROpen = value;
				}
			}
		}

		// Token: 0x1700066B RID: 1643
		// (get) Token: 0x06002614 RID: 9748 RVA: 0x000FA7EC File Offset: 0x000F89EC
		// (set) Token: 0x06002615 RID: 9749 RVA: 0x000FA804 File Offset: 0x000F8A04
		public int? ROpenCondOdrID
		{
			get
			{
				return this._ROpenCondOdrID;
			}
			set
			{
				int? ropenCondOdrID = this._ROpenCondOdrID;
				int? num = value;
				if (!(ropenCondOdrID.GetValueOrDefault() == num.GetValueOrDefault() & ropenCondOdrID != null == (num != null)))
				{
					this._ROpenCondOdrID = value;
				}
			}
		}

		// Token: 0x1700066C RID: 1644
		// (get) Token: 0x06002616 RID: 9750 RVA: 0x000FA848 File Offset: 0x000F8A48
		// (set) Token: 0x06002617 RID: 9751 RVA: 0x000FA860 File Offset: 0x000F8A60
		public decimal? TrailingStopPts
		{
			get
			{
				return this._TrailingStopPts;
			}
			set
			{
				decimal? trailingStopPts = this._TrailingStopPts;
				decimal? num = value;
				if (!(trailingStopPts.GetValueOrDefault() == num.GetValueOrDefault() & trailingStopPts != null == (num != null)))
				{
					this._TrailingStopPts = value;
				}
			}
		}

		// Token: 0x06002618 RID: 9752 RVA: 0x000FA8A8 File Offset: 0x000F8AA8
		public void method_0(CondOrder condOrder_0)
		{
			this.ID = condOrder_0.ID;
			this.AcctID = condOrder_0.AcctID;
			this.SymbID = condOrder_0.SymbID;
			this.OrderStatus = condOrder_0.OrderStatus;
			this.OrderType = condOrder_0.OrderType;
			this.Units = condOrder_0.Units;
			this.ComparisonOpt = condOrder_0.ComparisonOpt;
			this.CondPrice = condOrder_0.CondPrice;
			this.ExePrice = condOrder_0.ExePrice;
			this.TransID = condOrder_0.TransID;
			this.IsROpen = condOrder_0.IsROpen;
			this.ROpenCondOdrID = condOrder_0.ROpenCondOdrID;
			this.TrailingStopPts = condOrder_0.TrailingStopPts;
			this.CreateTime = condOrder_0.CreateTime;
			this.UpdateTime = condOrder_0.UpdateTime;
		}

		// Token: 0x06002619 RID: 9753 RVA: 0x000FA96C File Offset: 0x000F8B6C
		public decimal? method_1(HisData hisData_0)
		{
			decimal? result;
			if (this.CreateTime < hisData_0.Date && this.TrailingStopPts != null)
			{
				decimal num = Convert.ToDecimal(hisData_0.High);
				decimal num2 = Convert.ToDecimal(hisData_0.Low);
				decimal value = this.TrailingStopPts.Value;
				decimal condPrice = this.CondPrice;
				if (this.OrderType == OrderType.Order_CloseLong)
				{
					if (condPrice > 0m)
					{
						if (num2 <= condPrice)
						{
							result = null;
						}
						else if (condPrice + value < num)
						{
							this.CondPrice = num - value;
							result = new decimal?(this.CondPrice - condPrice);
						}
						else
						{
							result = new decimal?(0m);
						}
					}
					else
					{
						Transaction transaction = Base.Trading.smethod_130(this.TransID.Value);
						if (num - transaction.Price >= value)
						{
							this.CondPrice = num - value;
						}
						result = new decimal?(0m);
					}
				}
				else if (condPrice > 0m)
				{
					if (num >= condPrice)
					{
						result = null;
					}
					else if (condPrice - value > num2)
					{
						this.CondPrice = num2 + value;
						result = new decimal?(this.CondPrice - condPrice);
					}
					else
					{
						result = new decimal?(0m);
					}
				}
				else
				{
					if (Base.Trading.smethod_130(this.TransID.Value).Price - num2 >= value)
					{
						this.CondPrice = num2 + value;
					}
					result = new decimal?(0m);
				}
			}
			else
			{
				result = new decimal?(0m);
			}
			return result;
		}

		// Token: 0x0400124F RID: 4687
		private int _ID;

		// Token: 0x04001250 RID: 4688
		private int _AcctID;

		// Token: 0x04001251 RID: 4689
		private int _SymbID;

		// Token: 0x04001252 RID: 4690
		private int? _TransID;

		// Token: 0x04001253 RID: 4691
		private decimal _Units;

		// Token: 0x04001254 RID: 4692
		private OrderType _OrderType;

		// Token: 0x04001255 RID: 4693
		private ComparisonOpt _ComparisonOpt;

		// Token: 0x04001256 RID: 4694
		private decimal _CondPrice;

		// Token: 0x04001257 RID: 4695
		private decimal _ExePrice;

		// Token: 0x04001258 RID: 4696
		private OrderStatus _OrderStatus;

		// Token: 0x04001259 RID: 4697
		private bool _IsROpen;

		// Token: 0x0400125A RID: 4698
		private int? _ROpenCondOdrID;

		// Token: 0x0400125B RID: 4699
		private decimal? _TrailingStopPts;

		// Token: 0x0400125C RID: 4700
		private DateTime _CreateTime;

		// Token: 0x0400125D RID: 4701
		private DateTime? _UpdateTime;

		// Token: 0x0400125E RID: 4702
		private string _Notes;
	}
}
