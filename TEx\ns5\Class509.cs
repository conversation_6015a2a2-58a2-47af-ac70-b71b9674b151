﻿using System;

namespace ns5
{
	// Token: 0x020003C3 RID: 963
	internal sealed class Class509
	{
		// Token: 0x170006B8 RID: 1720
		// (get) Token: 0x060026D1 RID: 9937 RVA: 0x0000EDBB File Offset: 0x0000CFBB
		public static Version Version
		{
			get
			{
				return Class509.version_0;
			}
		}

		// Token: 0x170006B9 RID: 1721
		// (get) Token: 0x060026D2 RID: 9938 RVA: 0x0000EDC2 File Offset: 0x0000CFC2
		public static string AppName
		{
			get
			{
				return Class509.AppNameMinusVersion + " " + Class509.MajorVersion;
			}
		}

		// Token: 0x170006BA RID: 1722
		// (get) Token: 0x060026D3 RID: 9939 RVA: 0x0000EDDD File Offset: 0x0000CFDD
		public static string TitleVersion
		{
			get
			{
				string result;
				if ((result = Class509.string_0) == null)
				{
					result = (Class509.string_0 = string.Format("{0}.{1}", Class509.version_0.Major, Class509.version_0.Minor));
				}
				return result;
			}
		}

		// Token: 0x170006BB RID: 1723
		// (get) Token: 0x060026D4 RID: 9940 RVA: 0x0000EE16 File Offset: 0x0000D016
		public static int MajorVersion
		{
			get
			{
				return Class509.version_0.Major;
			}
		}

		// Token: 0x170006BC RID: 1724
		// (get) Token: 0x060026D5 RID: 9941 RVA: 0x0000EE22 File Offset: 0x0000D022
		public static string AppNameMinusVersion
		{
			get
			{
				return "SmartAssembly";
			}
		}

		// Token: 0x060026D6 RID: 9942 RVA: 0x00002D25 File Offset: 0x00000F25
		private Class509()
		{
		}

		// Token: 0x040012BE RID: 4798
		private static Version version_0 = new Version("7.0.9.2591");

		// Token: 0x040012BF RID: 4799
		private static string string_0 = null;
	}
}
