﻿using System;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns6;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000221 RID: 545
	internal sealed class GraphCtrlStat : ZedGraphControl
	{
		// Token: 0x06001695 RID: 5781 RVA: 0x0000934A File Offset: 0x0000754A
		public GraphCtrlStat(bool fillChart = false)
		{
			this.FillChart = fillChart;
			this.vmethod_0();
			Base.UI.ChartThemeChanged += this.method_0;
		}

		// Token: 0x06001696 RID: 5782 RVA: 0x00009372 File Offset: 0x00007572
		private void method_0(object sender, EventArgs e)
		{
			this.vmethod_1();
		}

		// Token: 0x06001697 RID: 5783 RVA: 0x000974A0 File Offset: 0x000956A0
		protected void vmethod_0()
		{
			this.Dock = DockStyle.Fill;
			base.IsEnableZoom = false;
			base.PointValueFormat = "F0";
			base.IsShowPointValues = true;
			base.BorderStyle = BorderStyle.None;
			base.IsShowContextMenu = false;
			GraphPane graphPane = base.GraphPane;
			graphPane.Border.Width = 0f;
			graphPane.Title.FontSpec.Family = "Microsoft Sans Serif";
			graphPane.XAxis.MajorGrid.IsVisible = false;
			graphPane.XAxis.MinorGrid.IsVisible = false;
			graphPane.XAxis.MinorTic.Size = 0f;
			graphPane.XAxis.MajorTic.Size = 0f;
			graphPane.YAxis.MajorGrid.IsVisible = false;
			graphPane.YAxis.MinorGrid.IsVisible = false;
			graphPane.YAxis.MinorTic.Size = 0f;
			graphPane.YAxis.MajorTic.Size = 0f;
			graphPane.YAxis.Scale.Format = "0";
			graphPane.YAxis.Title.IsOmitMag = true;
			graphPane.Title.FontSpec.Size = 22f * TApp.DpiScaleMulti;
			graphPane.Legend.FontSpec.Size = 16f * TApp.DpiScaleMulti;
			graphPane.XAxis.Scale.FontSpec.Size = 17f * TApp.DpiScaleMulti;
			graphPane.YAxis.Scale.FontSpec.Size = 17f * TApp.DpiScaleMulti;
			float size = 18f * TApp.DpiScaleMulti;
			graphPane.XAxis.Title.FontSpec.Size = size;
			graphPane.YAxis.Title.FontSpec.Size = size;
			graphPane.Chart.Fill = new Fill(Color.FromArgb(91, 91, 91), Color.FromArgb(66, 66, 66), 45f);
			this.vmethod_1();
		}

		// Token: 0x06001698 RID: 5784 RVA: 0x000976A4 File Offset: 0x000958A4
		public void vmethod_1()
		{
			if (base.GraphPane != null)
			{
				bool chartTheme = Base.UI.Form.ChartTheme != ChartTheme.Classic;
				Fill fill = new Fill(Color.Transparent);
				Color fontColor;
				Color color;
				Color color2;
				Color color3;
				if (!chartTheme)
				{
					fontColor = Class179.color_9;
					color = Class179.color_6;
					color2 = Class179.color_6;
					color3 = Class179.color_3;
					base.GraphPane.Title.FontSpec.IsDropShadow = true;
					base.GraphPane.XAxis.Title.FontSpec.IsDropShadow = true;
					base.GraphPane.XAxis.Scale.FontSpec.IsDropShadow = true;
					base.GraphPane.YAxis.Title.FontSpec.IsDropShadow = true;
					base.GraphPane.YAxis.Color = Class179.color_7;
					if (this.FillChart)
					{
						fill = new Fill(Color.White, Class179.color_7, 90f);
					}
				}
				else
				{
					fontColor = Color.FromArgb(50, 50, 50);
					color = Color.DarkGray;
					color2 = Color.White;
					color3 = Class179.color_8;
					base.GraphPane.Title.FontSpec.IsDropShadow = false;
					base.GraphPane.XAxis.Title.FontSpec.IsDropShadow = false;
					base.GraphPane.XAxis.Scale.FontSpec.IsDropShadow = false;
					base.GraphPane.YAxis.Title.FontSpec.IsDropShadow = false;
					base.GraphPane.YAxis.Color = Class179.color_4;
					if (this.FillChart)
					{
						fill = new Fill(Color.White, Color.FromArgb(255, 255, 236), 90f);
					}
				}
				base.GraphPane.Fill = new Fill(color2, color3, 90f);
				base.GraphPane.Border.Color = color3;
				base.GraphPane.Chart.Fill = fill;
				base.GraphPane.Chart.Border.Color = color;
				base.GraphPane.Title.FontSpec.FontColor = fontColor;
				base.GraphPane.XAxis.Title.FontSpec.FontColor = fontColor;
				base.GraphPane.XAxis.Scale.FontSpec.FontColor = fontColor;
				base.GraphPane.YAxis.Title.FontSpec.FontColor = fontColor;
				base.GraphPane.YAxis.Scale.FontSpec.FontColor = fontColor;
				base.GraphPane.YAxis.MajorGrid.Color = color;
				this.Refresh();
			}
		}

		// Token: 0x06001699 RID: 5785 RVA: 0x0009794C File Offset: 0x00095B4C
		public void method_1(double[] double_0)
		{
			int num = this.method_2(double_0);
			if (num > 0)
			{
				string text = "";
				if (num == 4)
				{
					text = "(万)";
				}
				else if (num == 8)
				{
					text = "(亿)";
				}
				if (!string.IsNullOrEmpty(text) && !base.GraphPane.YAxis.Title.Text.EndsWith(text))
				{
					AxisLabel title = base.GraphPane.YAxis.Title;
					title.Text += text;
				}
			}
			base.GraphPane.YAxis.Scale.Mag = num;
		}

		// Token: 0x0600169A RID: 5786 RVA: 0x000979E0 File Offset: 0x00095BE0
		public int method_2(double[] double_0)
		{
			int result = 0;
			double[] array = double_0.Where(new Func<double, bool>(GraphCtrlStat.<>c.<>9.method_0)).ToArray<double>();
			if (array.Length != 0)
			{
				double num = array.Select(new Func<double, double>(GraphCtrlStat.<>c.<>9.method_1)).Max();
				if (num > 100000000.0)
				{
					result = 8;
				}
				else if (num > 10000.0)
				{
					result = 4;
				}
			}
			return result;
		}

		// Token: 0x0600169B RID: 5787 RVA: 0x0000937C File Offset: 0x0000757C
		public void method_3()
		{
			Base.UI.ChartThemeChanged -= this.method_0;
			base.Dispose();
		}

		// Token: 0x170003AD RID: 941
		// (get) Token: 0x0600169C RID: 5788 RVA: 0x00097A6C File Offset: 0x00095C6C
		// (set) Token: 0x0600169D RID: 5789 RVA: 0x00009397 File Offset: 0x00007597
		public bool FillChart { get; set; }

		// Token: 0x04000B82 RID: 2946
		[CompilerGenerated]
		private bool bool_0;
	}
}
