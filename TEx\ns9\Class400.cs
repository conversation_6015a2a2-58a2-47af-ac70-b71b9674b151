﻿using System;
using System.Drawing;
using System.Linq;
using ns24;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns9
{
	// Token: 0x020002E7 RID: 743
	[Obsolete]
	internal sealed class Class400 : Class399
	{
		// Token: 0x060020FA RID: 8442 RVA: 0x0000D4E1 File Offset: 0x0000B6E1
		public Class400(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
			throw new Exception("此类不再使用。由shapeCurveColorDot类代替。");
		}

		// Token: 0x060020FB RID: 8443 RVA: 0x0000D4F6 File Offset: 0x0000B6F6
		public override void vmethod_2(int int_0)
		{
			if (base.KCount != base.IndData.Data.Count<double>())
			{
				throw new Exception("数据长度错误");
			}
			this.vmethod_3(int_0, base.IndData);
		}

		// Token: 0x060020FC RID: 8444 RVA: 0x000E23D8 File Offset: 0x000E05D8
		private PointPair method_1(int int_0, DataArray dataArray_1)
		{
			double x = new XDate(base.method_0(int_0));
			if (dataArray_1.OtherDataArrayList.Count != 1)
			{
				throw new Exception("SAR指标没有装载颜色信息。");
			}
			double num = dataArray_1.Data[int_0];
			PointPair result;
			if (double.IsNaN(num))
			{
				result = null;
			}
			else
			{
				result = new PointPair(x, num, dataArray_1.OtherDataArrayList[0].Data[int_0]);
			}
			return result;
		}

		// Token: 0x060020FD RID: 8445 RVA: 0x000E2444 File Offset: 0x000E0644
		private double method_2()
		{
			GraphPane graphPane = this.zedGraphControl_0.GraphPane;
			return (graphPane.YAxis.Scale.Max - graphPane.YAxis.Scale.Min) * 0.6000000238418579 / (double)(graphPane.Rect.Height / graphPane.Rect.Width * (float)this.rollingPointPairList_0.Capacity);
		}

		// Token: 0x060020FE RID: 8446 RVA: 0x000E24B8 File Offset: 0x000E06B8
		public override void vmethod_3(int int_0, DataArray dataArray_1)
		{
			PointPair pointPair = this.method_1(int_0, dataArray_1);
			if (pointPair != null)
			{
				double double_ = this.method_2();
				((IPointListEdit)this.rollingPointPairList_0).Add(pointPair);
				if (int_0 == this.indEx_0.StartIdx)
				{
					this.vmethod_1(this.zedGraphControl_0);
				}
				if (int_0 == this.indEx_0.EndIdx)
				{
					for (int i = 0; i < this.rollingPointPairList_0.Count; i++)
					{
						GraphObj item = this.method_5(i, double_);
						this.list_0.Add(item);
						this.zedGraphControl_0.GraphPane.GraphObjList.Add(item);
					}
				}
			}
		}

		// Token: 0x060020FF RID: 8447 RVA: 0x000E2554 File Offset: 0x000E0754
		protected PointPair method_3(int int_0)
		{
			return this.method_1(int_0, base.IndData);
		}

		// Token: 0x06002100 RID: 8448 RVA: 0x000E2574 File Offset: 0x000E0774
		protected GraphObj method_4(int int_0, PointPair pointPair_0, double double_0)
		{
			double y = pointPair_0.Y;
			EllipseObj ellipseObj;
			if (pointPair_0.Z > 0.0)
			{
				ellipseObj = new EllipseObj((double)int_0 - 0.3, y + double_0 / 2.0, 0.6, double_0, Color.Blue, Color.Transparent);
			}
			else
			{
				ellipseObj = new EllipseObj((double)int_0 - 0.3, y + double_0 / 2.0, 0.6, double_0, Color.Red, Color.Transparent);
			}
			ellipseObj.Tag = "";
			ellipseObj.ZOrder = ZOrder.E_BehindCurves;
			ellipseObj.Location.CoordinateFrame = CoordType.AxisXYScale;
			return ellipseObj;
		}

		// Token: 0x06002101 RID: 8449 RVA: 0x000E2628 File Offset: 0x000E0828
		protected GraphObj method_5(int int_0, double double_0)
		{
			PointPair pointPair_ = this.rollingPointPairList_0[int_0];
			int int_ = int_0 + 1;
			return this.method_4(int_, pointPair_, double_0);
		}

		// Token: 0x06002102 RID: 8450 RVA: 0x000E2654 File Offset: 0x000E0854
		public override void vmethod_4(int int_0, DataArray dataArray_1)
		{
			PointPair pointPair = this.method_1(int_0, dataArray_1);
			if (pointPair != null)
			{
				if (this.list_0.Count > 0)
				{
					double double_ = this.method_2();
					this.list_0[this.list_0.Count - 1] = this.method_4(this.list_0.Count - 1, pointPair, double_);
				}
			}
		}
	}
}
