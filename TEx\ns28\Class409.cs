﻿using System;
using System.Linq;
using System.Runtime.CompilerServices;
using ns14;
using ns9;
using TEx.Inds;
using TEx.SIndicator;

namespace ns28
{
	// Token: 0x0200030C RID: 780
	internal class Class409 : Class408
	{
		// Token: 0x170005CB RID: 1483
		// (get) Token: 0x060021B0 RID: 8624 RVA: 0x000E741C File Offset: 0x000E561C
		// (set) Token: 0x060021B1 RID: 8625 RVA: 0x0000D80C File Offset: 0x0000BA0C
		public override HToken Token
		{
			get
			{
				return this.htoken_0;
			}
			protected set
			{
				this.htoken_0 = value;
			}
		}

		// Token: 0x060021B2 RID: 8626 RVA: 0x0000D817 File Offset: 0x0000BA17
		public Class409(HToken htoken_1)
		{
			this.Token = htoken_1;
		}

		// Token: 0x170005CC RID: 1484
		// (get) Token: 0x060021B3 RID: 8627 RVA: 0x0000D828 File Offset: 0x0000BA28
		// (set) Token: 0x060021B4 RID: 8628 RVA: 0x0000D828 File Offset: 0x0000BA28
		public override Class408 Left
		{
			get
			{
				throw new Exception("没有Left");
			}
			protected set
			{
				throw new Exception("没有Left");
			}
		}

		// Token: 0x170005CD RID: 1485
		// (get) Token: 0x060021B5 RID: 8629 RVA: 0x0000D834 File Offset: 0x0000BA34
		// (set) Token: 0x060021B6 RID: 8630 RVA: 0x0000D834 File Offset: 0x0000BA34
		public override Class408 Right
		{
			get
			{
				throw new Exception("没有Right");
			}
			protected set
			{
				throw new Exception("没有Right");
			}
		}

		// Token: 0x060021B7 RID: 8631 RVA: 0x000E7434 File Offset: 0x000E5634
		public override string vmethod_0()
		{
			return this.Token.Symbol.Name;
		}

		// Token: 0x060021B8 RID: 8632 RVA: 0x000E7458 File Offset: 0x000E5658
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			object result;
			if (this.Token.Symbol.HSymbolType == Enum26.const_5)
			{
				result = double.Parse(this.Token.Symbol.Name);
			}
			else if (this.Token.Symbol.HSymbolType == Enum26.const_38)
			{
				DataArray dataArray = null;
				if (parserEnvironment_0.method_0(this.Token.Symbol.Name, out dataArray))
				{
					result = dataArray;
				}
				else
				{
					result = this.Token.Symbol.Name;
				}
			}
			else
			{
				if (this.Token.Symbol.HSymbolType != Enum26.const_3 && this.Token.Symbol.HSymbolType != Enum26.const_1)
				{
					if (this.Token.Symbol.HSymbolType != Enum26.const_34)
					{
						if (this.Token.Symbol.HSymbolType == Enum26.const_4)
						{
							Class409.Class426 @class = new Class409.Class426();
							@class.string_0 = this.Token.Symbol.Name;
							DataArray dataArray2 = parserEnvironment_0.DataArrays.SingleOrDefault(new Func<DataArray, bool>(@class.method_0));
							if (dataArray2 != null)
							{
								return dataArray2.Clone();
							}
							NameDoubleValue nameDoubleValue = parserEnvironment_0.list_0.SingleOrDefault(new Func<NameDoubleValue, bool>(@class.method_1));
							if (nameDoubleValue != null)
							{
								return nameDoubleValue.Value;
							}
						}
						else
						{
							if (this.Token.Symbol.HSymbolType == Enum26.const_2)
							{
								return parserEnvironment_0.UserDefineParams.Single(new Func<UserDefineParam, bool>(this.method_0)).Value;
							}
							if (this.Token.Symbol.HSymbolType == Enum26.const_22)
							{
								return null;
							}
							if (this.Token.Symbol.HSymbolType == Enum26.const_10)
							{
								return null;
							}
							if (this.Token.Symbol.HSymbolType == Enum26.const_11)
							{
								return null;
							}
						}
						throw new Exception(this.Token.method_0("无法识别"));
					}
				}
				result = this.Token.Symbol.Name;
			}
			return result;
		}

		// Token: 0x060021B9 RID: 8633 RVA: 0x000E7664 File Offset: 0x000E5864
		[CompilerGenerated]
		private bool method_0(UserDefineParam userDefineParam_0)
		{
			return userDefineParam_0.Name == this.Token.Symbol.Name;
		}

		// Token: 0x04001062 RID: 4194
		private HToken htoken_0;

		// Token: 0x0200030D RID: 781
		[CompilerGenerated]
		private sealed class Class426
		{
			// Token: 0x060021BB RID: 8635 RVA: 0x000E7690 File Offset: 0x000E5890
			internal bool method_0(DataArray dataArray_0)
			{
				return dataArray_0.Name == this.string_0;
			}

			// Token: 0x060021BC RID: 8636 RVA: 0x000E76B4 File Offset: 0x000E58B4
			internal bool method_1(NameDoubleValue nameDoubleValue_0)
			{
				return nameDoubleValue_0.Name == this.string_0;
			}

			// Token: 0x04001063 RID: 4195
			public string string_0;
		}
	}
}
