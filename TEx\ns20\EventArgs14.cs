﻿using System;
using TEx;
using TEx.Trading;

namespace ns20
{
	// Token: 0x02000271 RID: 625
	internal sealed class EventArgs14 : EventArgs
	{
		// Token: 0x06001B6D RID: 7021 RVA: 0x0000B4C9 File Offset: 0x000096C9
		public EventArgs14(ShownOrder shownOrder_1, string string_2)
		{
			this.shownOrder_0 = shownOrder_1;
			this.string_0 = Base.Trading.smethod_34(shownOrder_1);
			this.string_1 = string_2;
		}

		// Token: 0x17000472 RID: 1138
		// (get) Token: 0x06001B6E RID: 7022 RVA: 0x000B9AC4 File Offset: 0x000B7CC4
		public ShownOrder ShownOrder
		{
			get
			{
				return this.shownOrder_0;
			}
		}

		// Token: 0x17000473 RID: 1139
		// (get) Token: 0x06001B6F RID: 7023 RVA: 0x000B9ADC File Offset: 0x000B7CDC
		public string OrderDesc
		{
			get
			{
				return this.string_0;
			}
		}

		// Token: 0x17000474 RID: 1140
		// (get) Token: 0x06001B70 RID: 7024 RVA: 0x000B9AF4 File Offset: 0x000B7CF4
		public string ExcResultNotes
		{
			get
			{
				return this.string_1;
			}
		}

		// Token: 0x04000D8D RID: 3469
		private readonly ShownOrder shownOrder_0;

		// Token: 0x04000D8E RID: 3470
		private readonly string string_0;

		// Token: 0x04000D8F RID: 3471
		private readonly string string_1;
	}
}
