﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns11;
using ns22;
using ns26;
using ns29;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000165 RID: 357
	internal sealed partial class DrawParamWnd : Form
	{
		// Token: 0x14000071 RID: 113
		// (add) Token: 0x06000D94 RID: 3476 RVA: 0x000551A8 File Offset: 0x000533A8
		// (remove) Token: 0x06000D95 RID: 3477 RVA: 0x000551E0 File Offset: 0x000533E0
		public event Delegate8 DrawObjParamSet
		{
			[CompilerGenerated]
			add
			{
				Delegate8 @delegate = this.delegate8_0;
				Delegate8 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate8 value2 = (Delegate8)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate8>(ref this.delegate8_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate8 @delegate = this.delegate8_0;
				Delegate8 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate8 value2 = (Delegate8)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate8>(ref this.delegate8_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06000D96 RID: 3478 RVA: 0x00006099 File Offset: 0x00004299
		protected void method_0(EventArgs7 eventArgs7_0)
		{
			Delegate8 @delegate = this.delegate8_0;
			if (@delegate != null)
			{
				@delegate(this, eventArgs7_0);
			}
		}

		// Token: 0x06000D97 RID: 3479 RVA: 0x000060B0 File Offset: 0x000042B0
		public DrawParamWnd()
		{
			this.InitializeComponent();
		}

		// Token: 0x06000D98 RID: 3480 RVA: 0x00055218 File Offset: 0x00053418
		public DrawParamWnd(DrawObj drawObj) : this()
		{
			this.drawObj = drawObj;
			DTValLocation dtvalLocation = drawObj.DTValLocation;
			this.drawObjDTVal_0 = new DrawObjDTVal(drawObj, dtvalLocation.X1DateTime, dtvalLocation.Y1Value);
			this.drawObjDTVal_0.Location = new Point(15, 15);
			this.nullable_0 = drawObj.LineColor;
			this.tabPage_Coord.Controls.Add(this.drawObjDTVal_0);
			this.tabPage_Coord.BackColor = Color.FromKnownColor(KnownColor.Control);
			int num = 115;
			if (!drawObj.IsOneClickLoc)
			{
				this.drawObjDTVal_1 = new DrawObjDTVal(drawObj, dtvalLocation.X2DateTime, dtvalLocation.Y2Value, 2);
				this.drawObjDTVal_1.Location = new Point(15, num + 32);
				this.tabPage_Coord.Controls.Add(this.drawObjDTVal_1);
			}
			this.tabControl.Height += num;
			base.Height += num;
			this.btn_color.Location = new Point(this.btn_color.Location.X, this.btn_color.Location.Y + num + 1);
			this.btn_OK.Location = new Point(this.btn_OK.Location.X, this.btn_OK.Location.Y + num + 1);
			this.btn_Cancel.Location = new Point(this.btn_Cancel.Location.X, this.btn_Cancel.Location.Y + num + 1);
			DrawLineStyle lineStyle = drawObj.LineStyle;
			if (lineStyle != null)
			{
				this.tabPage_LineStyle = new TabPage();
				this.tabPage_LineStyle.Location = new Point(4, 25);
				this.tabPage_LineStyle.Name = "tabPage_LineStyle";
				this.tabPage_LineStyle.Padding = new Padding(3);
				this.tabPage_LineStyle.Size = new Size(385, 170);
				this.tabPage_LineStyle.Text = "线形";
				this.tabPage_LineStyle.BackColor = Color.FromKnownColor(KnownColor.Control);
				this.tabControl.SuspendLayout();
				base.SuspendLayout();
				this.tabControl.Controls.Add(this.tabPage_LineStyle);
				this.label_0 = new Label();
				this.label_0.Text = "线形：";
				this.label_0.TextAlign = ContentAlignment.MiddleLeft;
				this.label_1 = new Label();
				this.label_1.Text = "粗细：";
				this.label_1.TextAlign = ContentAlignment.MiddleLeft;
				int width = this.tabPage_LineStyle.Width / 2;
				int height = 36;
				this.class64_0 = new Class64(null, null);
				this.class64_0.Width = width;
				this.class64_0.Height = 36;
				for (int i = 0; i < Enum.GetNames(typeof(DrawLineType)).Length; i++)
				{
					this.class64_0.Items.Add(i);
				}
				this.class64_0.SelectedIndex = (int)lineStyle.LineType;
				this.class65_0 = new Class65(null, null);
				this.class65_0.Width = width;
				this.class65_0.Height = height;
				for (int j = 0; j < Enum.GetNames(typeof(DrawLineWidth)).Length; j++)
				{
					this.class65_0.Items.Add(j);
				}
				this.class65_0.SelectedIndex = (int)lineStyle.LineWidth;
				this.tabPage_LineStyle.Controls.Add(this.label_0);
				this.tabPage_LineStyle.Controls.Add(this.label_1);
				this.tabPage_LineStyle.Controls.Add(this.class64_0);
				this.tabPage_LineStyle.Controls.Add(this.class65_0);
				this.tabControl.ResumeLayout(false);
				base.ResumeLayout(false);
			}
			this.list_0 = drawObj.SublineParamList;
			if (this.list_0 != null)
			{
				this.tabPage_Params = new TabPage();
				this.tabPage_Params.Location = new Point(4, 25);
				this.tabPage_Params.Name = "tabPage_Params";
				this.tabPage_Params.Padding = new Padding(3);
				this.tabPage_Params.Size = new Size(385, 170);
				this.tabPage_Params.Text = "参数";
				this.tabPage_Params.BackColor = Color.FromKnownColor(KnownColor.Control);
				this.tabControl.SuspendLayout();
				base.SuspendLayout();
				this.tabControl.Controls.Add(this.tabPage_Params);
				this.dataGridView_0 = this.method_1(this.list_0);
				this.tabPage_Params.Controls.Add(this.dataGridView_0);
				this.checkBox_0 = new CheckBox();
				this.checkBox_0.Checked = false;
				this.checkBox_0.Text = "设为此画线默认参数";
				this.tabPage_Params.Controls.Add(this.checkBox_0);
				this.tabControl.ResumeLayout(false);
				base.ResumeLayout(false);
			}
			if (!drawObj.CanChgColor)
			{
				this.btn_color.Visible = false;
			}
			if (!string.IsNullOrEmpty(drawObj.Name))
			{
				this.Text = this.Text + " - " + drawObj.Name;
			}
			base.Shown += this.DrawParamWnd_Shown;
		}

		// Token: 0x06000D99 RID: 3481 RVA: 0x00055794 File Offset: 0x00053994
		private void DrawParamWnd_Shown(object sender, EventArgs e)
		{
			if (this.tabPage_LineStyle != null)
			{
				int num = this.tabPage_LineStyle.Height / 3;
				int num2 = num + this.tabPage_LineStyle.Height / 5;
				int width = base.Width / 8;
				int num3 = base.Width / 7;
				this.label_0.Location = new Point(num3, num);
				this.label_1.Location = new Point(num3, num2);
				this.label_0.Width = width;
				this.label_1.Width = width;
				int width2 = Convert.ToInt32(Math.Round((double)base.Width / 2.4));
				int x = num3 + this.label_0.Width;
				this.class64_0.Location = new Point(x, num - 1);
				this.class65_0.Location = new Point(x, num2 - 1);
				this.class64_0.Width = width2;
				this.class65_0.Width = width2;
			}
			if (this.tabPage_Params != null && this.dataGridView_0 != null)
			{
				DataGridView dataGridView = this.dataGridView_0;
				dataGridView.Columns[Utility.GetPropertyName<DrawSublineParam>((DrawSublineParam p) => (object)p.DigitNb)].Visible = false;
				dataGridView.Columns[Utility.GetPropertyName<DrawSublineParam>((DrawSublineParam p) => (object)p.MaxValue)].Visible = false;
				dataGridView.Columns[Utility.GetPropertyName<DrawSublineParam>((DrawSublineParam p) => (object)p.MinValue)].Visible = false;
				dataGridView.Columns[Utility.GetPropertyName<DrawSublineParam>((DrawSublineParam f) => f.Name)].FillWeight = 30f;
				dataGridView.Columns[Utility.GetPropertyName<DrawSublineParam>((DrawSublineParam f) => f.Name)].ReadOnly = true;
				dataGridView.Columns[Utility.GetPropertyName<DrawSublineParam>((DrawSublineParam f) => (object)f.Enabled)].FillWeight = 50f;
				dataGridView.Columns[Utility.GetPropertyName<DrawSublineParam>((DrawSublineParam f) => (object)f.Value)].HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleRight;
				dataGridView.Columns[Utility.GetPropertyName<DrawSublineParam>((DrawSublineParam f) => (object)f.Value)].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
				dataGridView.Width = Convert.ToInt32(Math.Round((double)this.tabPage_Params.Width / 2.2));
				dataGridView.Height = this.tabPage_Params.Height - 24;
				this.checkBox_0.Location = new Point(Convert.ToInt32(this.tabPage_Params.Width / 2 + 16), Convert.ToInt32(this.tabPage_Params.Height - 30));
				this.checkBox_0.Width = this.tabPage_Params.Width / 2 - 16;
			}
		}

		// Token: 0x06000D9A RID: 3482 RVA: 0x00055C44 File Offset: 0x00053E44
		private DataGridView method_1(List<DrawSublineParam> list_1)
		{
			DataGridView dataGridView = new DataGridView();
			dataGridView.MultiSelect = false;
			dataGridView.BorderStyle = BorderStyle.None;
			dataGridView.CellBorderStyle = DataGridViewCellBorderStyle.Single;
			dataGridView.BackgroundColor = Color.White;
			dataGridView.GridColor = Color.FromArgb(217, 217, 217);
			dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
			dataGridView.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
			dataGridView.RowHeadersVisible = false;
			dataGridView.DefaultCellStyle.Font = new Font("Microsoft Sans Serif", 8.5f);
			dataGridView.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 120, 215);
			dataGridView.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
			dataGridView.AllowUserToAddRows = false;
			dataGridView.AllowUserToDeleteRows = false;
			dataGridView.Location = new Point(10, 14);
			dataGridView.CellToolTipTextNeeded += this.method_2;
			dataGridView.CellValidating += this.method_3;
			dataGridView.DataSource = new BindingSource
			{
				DataSource = list_1
			};
			return dataGridView;
		}

		// Token: 0x06000D9B RID: 3483 RVA: 0x000060C0 File Offset: 0x000042C0
		private void method_2(object sender, DataGridViewCellToolTipTextNeededEventArgs e)
		{
			if (e.ColumnIndex == 2)
			{
				e.ToolTipText = "双击鼠标编辑";
			}
		}

		// Token: 0x06000D9C RID: 3484 RVA: 0x00055D38 File Offset: 0x00053F38
		private void method_3(object sender, DataGridViewCellValidatingEventArgs e)
		{
			object formattedValue = e.FormattedValue;
			if (e.ColumnIndex == 2)
			{
				DrawSublineParam drawSublineParam = this.list_0[e.RowIndex];
				try
				{
					double num = Convert.ToDouble(e.FormattedValue);
					if (!double.IsNaN(num))
					{
						if (num < drawSublineParam.MinValue)
						{
							MessageBox.Show("数值应大于或等于" + drawSublineParam.MinValue.ToString() + "！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
							e.Cancel = true;
						}
						else
						{
							double num2 = num;
							double? maxValue = drawSublineParam.MaxValue;
							if (num2 > maxValue.GetValueOrDefault() & maxValue != null)
							{
								MessageBox.Show("数值应小于或等于" + drawSublineParam.MaxValue.ToString() + "！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
								e.Cancel = true;
							}
						}
					}
					else
					{
						MessageBox.Show("请输入有效的数字！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
						e.Cancel = true;
					}
				}
				catch
				{
					MessageBox.Show("请输入有效的数字！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					e.Cancel = true;
				}
			}
		}

		// Token: 0x06000D9D RID: 3485 RVA: 0x00055E60 File Offset: 0x00054060
		private void btn_OK_Click(object sender, EventArgs e)
		{
			DTValLocation dtvalLocation_;
			if (this.drawObjDTVal_1 == null)
			{
				dtvalLocation_ = new DTValLocation(this.drawObjDTVal_0.XDateTime, this.drawObjDTVal_0.YValue, this.drawObjDTVal_0.XDateTime, this.drawObjDTVal_0.YValue);
			}
			else
			{
				dtvalLocation_ = new DTValLocation(this.drawObjDTVal_0.XDateTime, this.drawObjDTVal_0.YValue, this.drawObjDTVal_1.XDateTime, this.drawObjDTVal_1.YValue);
			}
			DrawLineStyle drawLineStyle = null;
			if (this.tabPage_LineStyle != null)
			{
				drawLineStyle = this.drawObj.LineStyle;
				if (drawLineStyle.LineType != (DrawLineType)this.class64_0.SelectedIndex)
				{
					drawLineStyle.LineType = (DrawLineType)this.class64_0.SelectedIndex;
				}
				if (drawLineStyle.LineWidth != (DrawLineWidth)this.class65_0.SelectedIndex)
				{
					drawLineStyle.LineWidth = (DrawLineWidth)this.class65_0.SelectedIndex;
				}
			}
			if (this.checkBox_0 != null && this.checkBox_0.Checked && this.list_0 != null)
			{
				if (Base.UI.Form.UserDrawObjParamsDict == null)
				{
					Base.UI.Form.UserDrawObjParamsDict = new Dictionary<string, Dictionary<string, object>>();
				}
				string key = this.drawObj.GetType().ToString();
				if (!Base.UI.Form.UserDrawObjParamsDict.ContainsKey(key))
				{
					Base.UI.Form.UserDrawObjParamsDict[key] = new Dictionary<string, object>();
				}
				Base.UI.Form.UserDrawObjParamsDict[key][DrawObjParamType.SublineParam.ToString()] = this.list_0;
				Base.UI.smethod_47();
			}
			this.method_0(new EventArgs7(dtvalLocation_, this.nullable_0, drawLineStyle, this.list_0));
			base.Close();
		}

		// Token: 0x06000D9E RID: 3486 RVA: 0x00056004 File Offset: 0x00054204
		private void btn_color_Click(object sender, EventArgs e)
		{
			if (this.nullable_0 != null)
			{
				this.colorDialog_0.Color = this.nullable_0.Value;
			}
			else
			{
				this.colorDialog_0.Color = ((Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.White : Color.Black);
			}
			if (this.colorDialog_0.ShowDialog() == DialogResult.OK)
			{
				this.nullable_0 = new Color?(this.colorDialog_0.Color);
				Base.UI.Form.LastSelectedLineColor = this.nullable_0;
			}
		}

		// Token: 0x06000D9F RID: 3487 RVA: 0x000060D8 File Offset: 0x000042D8
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x040006E4 RID: 1764
		[CompilerGenerated]
		private Delegate8 delegate8_0;

		// Token: 0x040006E5 RID: 1765
		private DrawObj drawObj;

		// Token: 0x040006E6 RID: 1766
		private Color? nullable_0;

		// Token: 0x040006E7 RID: 1767
		private DrawObjDTVal drawObjDTVal_0;

		// Token: 0x040006E8 RID: 1768
		private DrawObjDTVal drawObjDTVal_1;

		// Token: 0x040006E9 RID: 1769
		private List<DrawSublineParam> list_0;

		// Token: 0x040006EA RID: 1770
		private TabPage tabPage_LineStyle;

		// Token: 0x040006EB RID: 1771
		private Label label_0;

		// Token: 0x040006EC RID: 1772
		private Label label_1;

		// Token: 0x040006ED RID: 1773
		private Class64 class64_0;

		// Token: 0x040006EE RID: 1774
		private Class65 class65_0;

		// Token: 0x040006EF RID: 1775
		private TabPage tabPage_Params;

		// Token: 0x040006F0 RID: 1776
		private DataGridView dataGridView_0;

		// Token: 0x040006F1 RID: 1777
		private CheckBox checkBox_0;

		// Token: 0x040006F2 RID: 1778
		private IContainer icontainer_0;
	}
}
