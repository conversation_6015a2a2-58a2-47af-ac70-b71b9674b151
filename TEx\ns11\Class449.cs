﻿using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;

namespace ns11
{
	// Token: 0x02000343 RID: 835
	internal static class Class449
	{
		// Token: 0x140000A6 RID: 166
		// (add) Token: 0x06002320 RID: 8992 RVA: 0x000EE14C File Offset: 0x000EC34C
		// (remove) Token: 0x06002321 RID: 8993 RVA: 0x000EE184 File Offset: 0x000EC384
		public static event EventHandler ScriptError
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = Class449.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref Class449.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = Class449.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref Class449.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06002322 RID: 8994 RVA: 0x0000DD30 File Offset: 0x0000BF30
		public static void smethod_0()
		{
			Class449.bool_0 = false;
			Class449.bool_1 = false;
		}

		// Token: 0x06002323 RID: 8995 RVA: 0x0000DD40 File Offset: 0x0000BF40
		public static void smethod_1()
		{
			BackgroundWorker backgroundWorker = new BackgroundWorker();
			backgroundWorker.DoWork += Class449.smethod_3;
			backgroundWorker.RunWorkerCompleted += Class449.smethod_2;
			backgroundWorker.RunWorkerAsync();
		}

		// Token: 0x06002324 RID: 8996 RVA: 0x0000DD72 File Offset: 0x0000BF72
		private static void smethod_2(object sender, RunWorkerCompletedEventArgs e)
		{
			if (Class449.eventHandler_0 != null && Class449.bool_1)
			{
				Class449.eventHandler_0(null, new EventArgs());
			}
		}

		// Token: 0x06002325 RID: 8997 RVA: 0x000EE1BC File Offset: 0x000EC3BC
		private static void smethod_3(object sender, DoWorkEventArgs e)
		{
			if (!Class449.bool_0)
			{
				IntPtr intPtr = IntPtr.Zero;
				Class449.bool_0 = true;
				Class449.bool_1 = false;
				while (Class449.bool_0)
				{
					intPtr = Class449.smethod_4("", Class449.string_0);
					if (intPtr != IntPtr.Zero)
					{
						Class449.SetForegroundWindow(intPtr);
						SendKeys.SendWait("%n");
						Class449.bool_1 = true;
						break;
					}
					Thread.Sleep(10);
				}
			}
		}

		// Token: 0x06002326 RID: 8998
		[DllImport("user32.dll")]
		public static extern bool SetForegroundWindow(IntPtr intptr_0);

		// Token: 0x06002327 RID: 8999
		[DllImport("user32.dll")]
		private static extern IntPtr FindWindow(string string_1, string string_2);

		// Token: 0x06002328 RID: 9000 RVA: 0x000EE22C File Offset: 0x000EC42C
		public static IntPtr smethod_4(string string_1, string string_2)
		{
			IntPtr result;
			if (string_1 == "")
			{
				result = Class449.FindWindow(null, string_2);
			}
			else if (string_2 == "")
			{
				result = Class449.FindWindow(string_1, null);
			}
			else
			{
				result = Class449.FindWindow(string_1, string_2);
			}
			return result;
		}

		// Token: 0x040010F7 RID: 4343
		[CompilerGenerated]
		private static EventHandler eventHandler_0;

		// Token: 0x040010F8 RID: 4344
		private static string string_0 = "脚本错误";

		// Token: 0x040010F9 RID: 4345
		private static bool bool_0;

		// Token: 0x040010FA RID: 4346
		private static bool bool_1;
	}
}
