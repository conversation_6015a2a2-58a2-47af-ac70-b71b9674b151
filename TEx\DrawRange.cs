﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Runtime.Serialization;
using TEx.Chart;
using TEx.Comn;

namespace TEx
{
	// Token: 0x0200007A RID: 122
	[Serializable]
	internal sealed class DrawRange : DrawObj, ISerializable
	{
		// Token: 0x06000458 RID: 1112 RVA: 0x000037AA File Offset: 0x000019AA
		public DrawRange()
		{
		}

		// Token: 0x06000459 RID: 1113 RVA: 0x00003E15 File Offset: 0x00002015
		public DrawRange(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = "测距";
			base.CanChgColor = true;
			base.IsOneClickLoc = false;
		}

		// Token: 0x0600045A RID: 1114 RVA: 0x000037DC File Offset: 0x000019DC
		protected DrawRange(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x0600045B RID: 1115 RVA: 0x000037ED File Offset: 0x000019ED
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x0600045C RID: 1116 RVA: 0x00003E3F File Offset: 0x0000203F
		protected override void vmethod_1()
		{
			this.method_40();
			base.vmethod_1();
		}

		// Token: 0x0600045D RID: 1117 RVA: 0x00003E4F File Offset: 0x0000204F
		protected override void vmethod_2()
		{
			this.method_42();
			base.vmethod_2();
		}

		// Token: 0x0600045E RID: 1118 RVA: 0x00023300 File Offset: 0x00021500
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			int num = Convert.ToInt32(Math.Round(Math.Abs(double_3 - double_1))) + 1;
			string text = "f2";
			if (base.Chart != null && base.Chart.Symbol != null)
			{
				text = "f" + base.Chart.Symbol.DigitNb.ToString();
			}
			if ((Base.UI.DrawingObj == this || base.MouseHovering || base.SelectBoxVisible) && num > 0)
			{
				TextObj item = this.method_41(chartCS_1, double_1, double_2, double_3, double_4, string_5, text);
				list.Add(item);
			}
			LineObj lineObj = base.method_23(double_1, double_2, double_3, double_4, string_5);
			lineObj.ZOrder = ZOrder.B_BehindLegend;
			list.Add(lineObj);
			double num2 = double_1;
			double double_5 = double_3;
			double double_6 = double_2;
			double num3 = double_4;
			if (double_3 < double_1)
			{
				num2 = double_3;
				double_5 = double_1;
				double_6 = double_4;
				num3 = double_2;
			}
			this.VLine = base.method_23(num2, double_6, num2, num3, string_5);
			list.Add(this.VLine);
			this.HLine = base.method_23(num2, num3, double_5, num3, string_5);
			list.Add(this.HLine);
			double num4 = Math.Abs(double_4 - double_2);
			double num5 = Math.Min(double_1, double_3);
			double num6 = Math.Min(double_2, double_4);
			if (num4 > 0.0)
			{
				this.VLabel = base.method_27(chartCS_1, num2, num6 + num4 / 2.0, num4.ToString(text) + Environment.NewLine + string.Format("{0:0.00}", num4 * 100.0 / double_2) + "%", null, string_5);
				this.VLabel.Location.AlignV = AlignV.Center;
				this.VLabel.ZOrder = ZOrder.B_BehindLegend;
				if (num2 > num5)
				{
					this.VLabel.Location.AlignH = AlignH.Left;
				}
				else
				{
					this.VLabel.Location.AlignH = AlignH.Right;
				}
				list.Add(this.VLabel);
			}
			if (num > 0)
			{
				this.HLabel = base.method_27(chartCS_1, num5 + Math.Abs(double_3 - double_1) / 2.0, num3, num.ToString() + "根", null, string_5);
				this.HLabel.Location.AlignH = AlignH.Center;
				this.HLabel.ZOrder = ZOrder.B_BehindLegend;
				if (num3 > num6)
				{
					this.HLabel.Location.AlignV = AlignV.Bottom;
				}
				else
				{
					this.HLabel.Location.AlignV = AlignV.Top;
				}
				list.Add(this.HLabel);
			}
			chartCS_1.ZedGraphControl.Resize -= this.method_39;
			chartCS_1.ZedGraphControl.Resize += this.method_39;
			return list;
		}

		// Token: 0x0600045F RID: 1119 RVA: 0x00003E5F File Offset: 0x0000205F
		private void method_39(object sender, EventArgs e)
		{
			base.Chart.method_60(this.VLabel);
			base.Chart.method_60(this.HLabel);
		}

		// Token: 0x06000460 RID: 1120 RVA: 0x000235C0 File Offset: 0x000217C0
		private void method_40()
		{
			if (base.GraphObjLst != null)
			{
				string string_ = "f" + base.Chart.Symbol.DigitNb.ToString();
				TextObj item = this.method_41(base.Chart, base.Location.X1, base.Location.Y1, base.Location.X2, base.Location.Y2, base.Tag, string_);
				this.method_42();
				base.Chart.GraphPane.GraphObjList.Add(item);
				base.GraphObjLst.Add(item);
			}
		}

		// Token: 0x06000461 RID: 1121 RVA: 0x00023668 File Offset: 0x00021868
		private TextObj method_41(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5, string string_6)
		{
			double a = double_1;
			double a2 = double_3;
			if (double_3 < double_1)
			{
				a = double_3;
				a2 = double_1;
			}
			int num = Convert.ToInt32(Math.Round(Math.Abs(double_3 - double_1))) + 1;
			int lastItemIndex = base.Chart.LastItemIndex;
			int num2 = Convert.ToInt32(Math.Round(a)) - 1 + base.Chart.FirstItemIndex;
			string text = " 跨度：" + num.ToString() + "个周期";
			if (lastItemIndex >= num2 && num2 >= 0 && base.Chart.PeriodHisDataList.Any<KeyValuePair<DateTime, HisData>>())
			{
				DrawRange.Class47 @class = new DrawRange.Class47();
				@class.hisData_0 = base.Chart.PeriodHisDataList.Values[num2];
				int num3 = Convert.ToInt32(Math.Round(a2)) - 1 + base.Chart.FirstItemIndex;
				if (num3 >= base.Chart.PeriodHisDataList.Count)
				{
					num3 = base.Chart.PeriodHisDataList.Count - 1;
				}
				@class.hisData_1 = base.Chart.PeriodHisDataList.Values[num3];
				IEnumerable<HisData> source = base.Chart.PeriodHisDataList.Values.Where(new Func<HisData, bool>(@class.method_0));
				if (source.Any<HisData>())
				{
					text = text + Environment.NewLine + " 收-开：" + (@class.hisData_0.Open - @class.hisData_1.Close).ToString(string_6);
					text = text + Environment.NewLine + " 高-低：" + (source.Max(new Func<HisData, double>(DrawRange.<>c.<>9.method_0)) - source.Min(new Func<HisData, double>(DrawRange.<>c.<>9.method_1))).ToString(string_6);
					text = string.Concat(new string[]
					{
						text,
						Environment.NewLine,
						" 连线幅度：",
						(double_4 - double_2).ToString(string_6),
						"（",
						string.Format("{0:0.00}", Math.Abs((double_4 - double_2) * 100.0 / double_2)),
						"%）"
					});
					double? num4 = null;
					double? num5 = null;
					double? num6 = null;
					try
					{
						num4 = source.Sum(new Func<HisData, double?>(DrawRange.<>c.<>9.method_2));
					}
					catch
					{
					}
					try
					{
						num5 = new double?(source.Sum(new Func<HisData, double>(DrawRange.<>c.<>9.method_3)));
					}
					catch
					{
					}
					try
					{
						num6 = source.Sum(new Func<HisData, double?>(DrawRange.<>c.<>9.method_4));
					}
					catch
					{
					}
					if (num4 != null)
					{
						string str = Convert.ToInt64(Math.Round(num4.Value)).ToString();
						text = text + Environment.NewLine + " 成交量：" + str;
						text = text + Environment.NewLine + " 均量：" + Convert.ToInt64(Math.Round(num4.Value / (double)num)).ToString();
					}
					if (num5 != null)
					{
						text = text + Environment.NewLine + " 均价：" + (num5.Value / (double)num).ToString(string_6);
					}
					if (num4 != null && num6 != null)
					{
						text = text + Environment.NewLine + " 加权均价：" + (num6.Value / num4.Value).ToString(string_6);
					}
					if (base.Chart.Symbol.IsFutures && @class.hisData_1.Amount != null && @class.hisData_0.Amount != null)
					{
						text = text + Environment.NewLine + " 增仓：" + Convert.ToInt64(Math.Round(@class.hisData_1.Amount.Value - @class.hisData_0.Amount.Value)).ToString();
					}
				}
			}
			double max = chartCS_1.GraphPane.XAxis.Scale.Max;
			double max2 = chartCS_1.GraphPane.YAxis.Scale.Max;
			double min = chartCS_1.GraphPane.YAxis.Scale.Min;
			double num7 = min + (max2 - min) / 2.0;
			double num8 = Math.Min(double_1, double_3);
			double num9 = Math.Max(double_1, double_3);
			double num10;
			if (num8 - 0.0 > max - num9)
			{
				num10 = num8;
			}
			else
			{
				num10 = num9;
			}
			double y = this.HLine.Location.Y;
			TextObj textObj = base.method_27(chartCS_1, num10, y, text, null, string_5);
			textObj.FontSpec.Border.IsVisible = true;
			textObj.FontSpec.Fill.IsVisible = true;
			textObj.FontSpec.FontColor = Color.Black;
			textObj.ZOrder = ZOrder.A_InFront;
			double num11 = Math.Abs(double_3 - double_1) / (double)chartCS_1.MaxSticksPerChart;
			if (num10 == num8)
			{
				if (num11 > 0.6)
				{
					textObj.Location.AlignH = AlignH.Left;
				}
				else
				{
					textObj.Location.AlignH = AlignH.Right;
					if (this.VLabel != null)
					{
						textObj.Location.X -= Math.Round((double)this.VLabel.FontSpec.RectF.Width * 1.2 / (double)chartCS_1.ZedGraphControl.Width * (double)chartCS_1.MaxSticksPerChart);
					}
				}
			}
			else if (num11 > 0.6)
			{
				textObj.Location.AlignH = AlignH.Right;
			}
			else
			{
				textObj.Location.AlignH = AlignH.Left;
			}
			if (y > num7)
			{
				textObj.Location.AlignV = AlignV.Top;
			}
			else
			{
				textObj.Location.AlignV = AlignV.Bottom;
			}
			return textObj;
		}

		// Token: 0x06000462 RID: 1122 RVA: 0x00023CD8 File Offset: 0x00021ED8
		private void method_42()
		{
			if (base.Chart != null)
			{
				if (base.GraphObjLst != null)
				{
					IEnumerable<GraphObj> source = base.GraphObjLst.Where(new Func<GraphObj, bool>(DrawRange.<>c.<>9.method_5));
					if (source.Any<GraphObj>())
					{
						GraphObj item = source.First<GraphObj>();
						base.Chart.GraphPane.GraphObjList.Remove(item);
						base.GraphObjLst.Remove(item);
					}
				}
			}
		}

		// Token: 0x170000DE RID: 222
		// (get) Token: 0x06000463 RID: 1123 RVA: 0x00023D58 File Offset: 0x00021F58
		// (set) Token: 0x06000464 RID: 1124 RVA: 0x00003E85 File Offset: 0x00002085
		public TextObj VLabel
		{
			get
			{
				return this.textObj_0;
			}
			set
			{
				this.textObj_0 = value;
			}
		}

		// Token: 0x170000DF RID: 223
		// (get) Token: 0x06000465 RID: 1125 RVA: 0x00023D70 File Offset: 0x00021F70
		// (set) Token: 0x06000466 RID: 1126 RVA: 0x00003E90 File Offset: 0x00002090
		public TextObj HLabel
		{
			get
			{
				return this.textObj_1;
			}
			set
			{
				this.textObj_1 = value;
			}
		}

		// Token: 0x170000E0 RID: 224
		// (get) Token: 0x06000467 RID: 1127 RVA: 0x00023D88 File Offset: 0x00021F88
		// (set) Token: 0x06000468 RID: 1128 RVA: 0x00003E9B File Offset: 0x0000209B
		public LineObj VLine
		{
			get
			{
				return this.lineObj_0;
			}
			set
			{
				this.lineObj_0 = value;
			}
		}

		// Token: 0x170000E1 RID: 225
		// (get) Token: 0x06000469 RID: 1129 RVA: 0x00023DA0 File Offset: 0x00021FA0
		// (set) Token: 0x0600046A RID: 1130 RVA: 0x00003EA6 File Offset: 0x000020A6
		public LineObj HLine
		{
			get
			{
				return this.lineObj_1;
			}
			set
			{
				this.lineObj_1 = value;
			}
		}

		// Token: 0x04000154 RID: 340
		private TextObj textObj_0;

		// Token: 0x04000155 RID: 341
		private TextObj textObj_1;

		// Token: 0x04000156 RID: 342
		private LineObj lineObj_0;

		// Token: 0x04000157 RID: 343
		private LineObj lineObj_1;

		// Token: 0x0200007B RID: 123
		[CompilerGenerated]
		private sealed class Class47
		{
			// Token: 0x0600046C RID: 1132 RVA: 0x00023DB8 File Offset: 0x00021FB8
			internal bool method_0(HisData hisData_2)
			{
				bool result;
				if (hisData_2.Date >= this.hisData_0.Date)
				{
					result = (hisData_2.Date <= this.hisData_1.Date);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000158 RID: 344
			public HisData hisData_0;

			// Token: 0x04000159 RID: 345
			public HisData hisData_1;
		}
	}
}
