﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns20;
using TEx;
using TEx.Chart;
using TEx.Comn;

namespace ns3
{
	// Token: 0x020001C7 RID: 455
	internal abstract class Class224 : ChartBase
	{
		// Token: 0x060011CF RID: 4559 RVA: 0x00007707 File Offset: 0x00005907
		public Class224(ChtCtrl_Tick chtCtrl_Tick_0, SplitterPanel splitterPanel_1) : base(chtCtrl_Tick_0, splitterPanel_1)
		{
		}

		// Token: 0x060011D0 RID: 4560 RVA: 0x0007ADBC File Offset: 0x00078FBC
		protected override void vmethod_0()
		{
			base.vmethod_0();
			GraphPane graphPane = base.GraphPane;
			graphPane.XAxis.MinorTic.Size = 0f;
			graphPane.XAxis.MajorTic.Size = 0f;
			graphPane.XAxis.Scale.IsVisible = false;
			graphPane.Y2Axis.IsVisible = true;
			graphPane.Y2Axis.Scale.IsSkipFirstLabel = true;
			graphPane.Y2Axis.Scale.IsSkipLastLabel = true;
			graphPane.Y2Axis.Scale.IsVisible = true;
			graphPane.Y2Axis.Scale.Align = AlignP.Inside;
			graphPane.Y2Axis.Scale.MagAuto = false;
			graphPane.Y2Axis.Scale.LabelGap = 0.25f;
			graphPane.Y2Axis.Scale.FontSpec.Size = 11f;
			graphPane.Y2Axis.MinorTic.Size = 0f;
			graphPane.Y2Axis.MajorTic.Size = 0f;
			graphPane.Y2Axis.MajorGrid.IsVisible = false;
			graphPane.Y2Axis.Title.IsVisible = false;
			base.ZedGraphControl.ContextMenuBuilder += this.vmethod_24;
		}

		// Token: 0x060011D1 RID: 4561 RVA: 0x0007AF08 File Offset: 0x00079108
		public override void vmethod_2()
		{
			base.vmethod_2();
			GraphPane graphPane = base.GraphPane;
			graphPane.XAxis.Scale.Min = 0.0;
			graphPane.XAxis.Scale.Max = Convert.ToDouble(base.SymbDataSet.HasValidDataSet ? base.SymbDataSet.CurrHisDataSet.TotalMinsPerTradingDay : 240) + 1.0;
			this.method_73();
			this.TimeLineXs = this.ChtCtrl_Tick.TimeLineXs;
			this.method_74(this.TimeLineXs);
		}

		// Token: 0x060011D2 RID: 4562 RVA: 0x00007742 File Offset: 0x00005942
		public override void vmethod_3()
		{
			base.vmethod_3();
		}

		// Token: 0x060011D3 RID: 4563 RVA: 0x0007AFA4 File Offset: 0x000791A4
		public override void vmethod_5(HisData hisData_0)
		{
			base.vmethod_5(hisData_0);
			if (base.Symbol.Type == TradingSymbolType.Forex && hisData_0.Date.Hour % 2 == 0 && hisData_0.Date.Minute == 0 && hisData_0.Date.Second == 0 && base.GraphPane.CurveList[0].NPts > 0)
			{
				this.method_75(base.GraphPane.CurveList[0].NPts - 1, false);
			}
		}

		// Token: 0x060011D4 RID: 4564 RVA: 0x0007B034 File Offset: 0x00079234
		public override void vmethod_4(int int_0)
		{
			foreach (HisData hisData_ in this.ChtHDList)
			{
				this.vmethod_5(hisData_);
			}
			base.vmethod_4(int_0);
		}

		// Token: 0x060011D5 RID: 4565 RVA: 0x0000774C File Offset: 0x0000594C
		public override void vmethod_11(HisData hisData_0)
		{
			this.method_76();
			base.vmethod_11(hisData_0);
		}

		// Token: 0x060011D6 RID: 4566 RVA: 0x0000775D File Offset: 0x0000595D
		public override void ApplyTheme(ChartTheme theme)
		{
			this.method_73();
			this.method_74(this.TimeLineXs);
			base.ApplyTheme(theme);
		}

		// Token: 0x060011D7 RID: 4567 RVA: 0x0007B094 File Offset: 0x00079294
		private void method_73()
		{
			GraphPane graphPane = base.GraphPane;
			List<LineObj> timeLineList = this.TimeLineList;
			if (timeLineList != null && timeLineList.Count > 0)
			{
				for (int i = 0; i < timeLineList.Count; i++)
				{
					graphPane.GraphObjList.Remove(timeLineList[i]);
					timeLineList[i] = null;
				}
			}
		}

		// Token: 0x060011D8 RID: 4568 RVA: 0x0007B0EC File Offset: 0x000792EC
		protected void method_74(List<Struct3> list_1)
		{
			if (list_1 != null && list_1.Count > 0)
			{
				foreach (Struct3 @struct in list_1)
				{
					if (@struct.bool_1)
					{
						this.method_75(@struct.int_0, @struct.bool_2);
					}
				}
			}
		}

		// Token: 0x060011D9 RID: 4569 RVA: 0x0007B15C File Offset: 0x0007935C
		private void method_75(int int_0, bool bool_3)
		{
			GraphPane graphPane = base.GraphPane;
			LineObj lineObj = new LineObj((Base.UI.Form.ChartTheme == ChartTheme.Classic) ? Color.DarkRed : Color.Silver, (double)(int_0 + 1), graphPane.YAxis.Scale.Min, (double)(int_0 + 1), graphPane.YAxis.Scale.Max);
			lineObj.IsClippedToChartRect = false;
			lineObj.Line.Style = (bool_3 ? DashStyle.Solid : DashStyle.Dot);
			lineObj.Tag = this.string_9;
			lineObj.ZOrder = ZOrder.E_BehindCurves;
			graphPane.GraphObjList.Add(lineObj);
		}

		// Token: 0x060011DA RID: 4570 RVA: 0x0007B1F0 File Offset: 0x000793F0
		protected void method_76()
		{
			GraphPane graphPane = base.GraphPane;
			List<LineObj> timeLineList = this.TimeLineList;
			if (timeLineList != null && timeLineList.Count > 0)
			{
				foreach (LineObj lineObj in timeLineList)
				{
					lineObj.Location.Y1 = graphPane.YAxis.Scale.Min;
					lineObj.Location.Height = graphPane.YAxis.Scale.Max - graphPane.YAxis.Scale.Min;
				}
			}
		}

		// Token: 0x060011DB RID: 4571 RVA: 0x0007B29C File Offset: 0x0007949C
		protected virtual void vmethod_24(ZedGraphControl zedGraphControl_1, ContextMenuStrip contextMenuStrip_0, Point point_0, ZedGraphControl.ContextMenuObjectState contextMenuObjectState_0)
		{
			contextMenuStrip_0.Items.Clear();
			if (!Base.UI.IsInCreateNewPageState)
			{
				base.method_10(contextMenuStrip_0);
				base.method_38(contextMenuStrip_0);
			}
			base.method_13(contextMenuStrip_0);
			base.method_14(contextMenuStrip_0);
			base.method_15(contextMenuStrip_0);
			base.method_26(contextMenuStrip_0);
			if (!Base.UI.IsInCreateNewPageState)
			{
				base.method_11(contextMenuStrip_0);
				base.method_31(contextMenuStrip_0);
				base.method_38(contextMenuStrip_0);
				base.method_32(contextMenuStrip_0);
				if (base.ChtCtrl.SymbDataSet != null && base.ChtCtrl.SymbDataSet.CurrHisData != null)
				{
					base.method_33(contextMenuStrip_0);
				}
				base.method_12(contextMenuStrip_0);
			}
			Base.UI.smethod_73(contextMenuStrip_0);
		}

		// Token: 0x17000298 RID: 664
		// (get) Token: 0x060011DC RID: 4572 RVA: 0x0007B340 File Offset: 0x00079540
		public ChtCtrl_Tick ChtCtrl_Tick
		{
			get
			{
				return (ChtCtrl_Tick)base.ChtCtrl;
			}
		}

		// Token: 0x17000299 RID: 665
		// (get) Token: 0x060011DD RID: 4573 RVA: 0x0007B35C File Offset: 0x0007955C
		public int StartHDIdx
		{
			get
			{
				return this.ChtCtrl_Tick.StartHDIdx;
			}
		}

		// Token: 0x1700029A RID: 666
		// (get) Token: 0x060011DE RID: 4574 RVA: 0x0007B378 File Offset: 0x00079578
		public DateTime StartHDDateTime
		{
			get
			{
				return this.ChtCtrl_Tick.StartHDDateTime;
			}
		}

		// Token: 0x1700029B RID: 667
		// (get) Token: 0x060011DF RID: 4575 RVA: 0x0007B394 File Offset: 0x00079594
		// (set) Token: 0x060011E0 RID: 4576 RVA: 0x0000777A File Offset: 0x0000597A
		public List<Struct3> TimeLineXs
		{
			get
			{
				return this.list_0;
			}
			set
			{
				this.list_0 = value;
			}
		}

		// Token: 0x1700029C RID: 668
		// (get) Token: 0x060011E1 RID: 4577 RVA: 0x0007B3AC File Offset: 0x000795AC
		public List<LineObj> TimeLineList
		{
			get
			{
				List<LineObj> result;
				if (base.GraphPane != null)
				{
					result = base.GraphPane.GraphObjList.Where(new Func<GraphObj, bool>(this.method_77)).Cast<LineObj>().ToList<LineObj>();
				}
				else
				{
					result = null;
				}
				return result;
			}
		}

		// Token: 0x1700029D RID: 669
		// (get) Token: 0x060011E2 RID: 4578 RVA: 0x0007B3F0 File Offset: 0x000795F0
		public List<HisData> ChtHDList
		{
			get
			{
				return this.ChtCtrl_Tick.ChtHDList;
			}
		}

		// Token: 0x060011E3 RID: 4579 RVA: 0x0007B40C File Offset: 0x0007960C
		[CompilerGenerated]
		private bool method_77(GraphObj graphObj_0)
		{
			bool result;
			if (graphObj_0 is LineObj && graphObj_0.Tag != null)
			{
				result = (graphObj_0.Tag.ToString() == this.string_9);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x04000947 RID: 2375
		protected Color color_0 = Color.FromArgb(0, 44, 87);

		// Token: 0x04000948 RID: 2376
		protected Color color_1 = Color.FromArgb(255, 128, 0);

		// Token: 0x04000949 RID: 2377
		protected string string_9 = "TimeLine";

		// Token: 0x0400094A RID: 2378
		private List<Struct3> list_0;
	}
}
