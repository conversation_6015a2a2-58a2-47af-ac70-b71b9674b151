﻿using System;
using TEx;
using TEx.Comn;

namespace ns32
{
	// Token: 0x020001E0 RID: 480
	internal struct Struct4
	{
		// Token: 0x040009BF RID: 2495
		public StkSymbol stkSymbol_0;

		// Token: 0x040009C0 RID: 2496
		public DateTime dateTime_0;

		// Token: 0x040009C1 RID: 2497
		public DateTime dateTime_1;

		// Token: 0x040009C2 RID: 2498
		public bool bool_0;

		// Token: 0x040009C3 RID: 2499
		public bool bool_1;

		// Token: 0x040009C4 RID: 2500
		public string string_0;

		// Token: 0x040009C5 RID: 2501
		public string string_1;

		// Token: 0x040009C6 RID: 2502
		public PeriodType periodType_0;

		// Token: 0x040009C7 RID: 2503
		public int? nullable_0;
	}
}
