﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using SmartAssembly.SmartExceptionsCore;

namespace ns8
{
	// Token: 0x02000403 RID: 1027
	internal sealed class Class538
	{
		// Token: 0x060027D9 RID: 10201 RVA: 0x0000F516 File Offset: 0x0000D716
		public static void smethod_0(Exception exception_0)
		{
			Class538.smethod_11(exception_0, new object[0]);
		}

		// Token: 0x060027DA RID: 10202 RVA: 0x0000F524 File Offset: 0x0000D724
		public static void smethod_1(Exception exception_0, object object_0)
		{
			Class538.smethod_11(exception_0, new object[]
			{
				object_0
			});
		}

		// Token: 0x060027DB RID: 10203 RVA: 0x0000F536 File Offset: 0x0000D736
		public static void smethod_2(Exception exception_0, object object_0, object object_1)
		{
			Class538.smethod_11(exception_0, new object[]
			{
				object_0,
				object_1
			});
		}

		// Token: 0x060027DC RID: 10204 RVA: 0x0000F54C File Offset: 0x0000D74C
		public static void smethod_3(Exception exception_0, object object_0, object object_1, object object_2)
		{
			Class538.smethod_11(exception_0, new object[]
			{
				object_0,
				object_1,
				object_2
			});
		}

		// Token: 0x060027DD RID: 10205 RVA: 0x0000F566 File Offset: 0x0000D766
		public static void smethod_4(Exception exception_0, object object_0, object object_1, object object_2, object object_3)
		{
			Class538.smethod_11(exception_0, new object[]
			{
				object_0,
				object_1,
				object_2,
				object_3
			});
		}

		// Token: 0x060027DE RID: 10206 RVA: 0x0000F585 File Offset: 0x0000D785
		public static void smethod_5(Exception exception_0, object object_0, object object_1, object object_2, object object_3, object object_4)
		{
			Class538.smethod_11(exception_0, new object[]
			{
				object_0,
				object_1,
				object_2,
				object_3,
				object_4
			});
		}

		// Token: 0x060027DF RID: 10207 RVA: 0x0000F5A9 File Offset: 0x0000D7A9
		public static void smethod_6(Exception exception_0, object object_0, object object_1, object object_2, object object_3, object object_4, object object_5)
		{
			Class538.smethod_11(exception_0, new object[]
			{
				object_0,
				object_1,
				object_2,
				object_3,
				object_4,
				object_5
			});
		}

		// Token: 0x060027E0 RID: 10208 RVA: 0x0000F5D2 File Offset: 0x0000D7D2
		public static void smethod_7(Exception exception_0, object object_0, object object_1, object object_2, object object_3, object object_4, object object_5, object object_6)
		{
			Class538.smethod_11(exception_0, new object[]
			{
				object_0,
				object_1,
				object_2,
				object_3,
				object_4,
				object_5,
				object_6
			});
		}

		// Token: 0x060027E1 RID: 10209 RVA: 0x0000F600 File Offset: 0x0000D800
		public static void smethod_8(Exception exception_0, object object_0, object object_1, object object_2, object object_3, object object_4, object object_5, object object_6, object object_7)
		{
			Class538.smethod_11(exception_0, new object[]
			{
				object_0,
				object_1,
				object_2,
				object_3,
				object_4,
				object_5,
				object_6,
				object_7
			});
		}

		// Token: 0x060027E2 RID: 10210 RVA: 0x0000F633 File Offset: 0x0000D833
		public static void smethod_9(Exception exception_0, object object_0, object object_1, object object_2, object object_3, object object_4, object object_5, object object_6, object object_7, object object_8)
		{
			Class538.smethod_11(exception_0, new object[]
			{
				object_0,
				object_1,
				object_2,
				object_3,
				object_4,
				object_5,
				object_6,
				object_7,
				object_8
			});
		}

		// Token: 0x060027E3 RID: 10211 RVA: 0x0000F66C File Offset: 0x0000D86C
		public static void smethod_10(Exception exception_0, object object_0, object object_1, object object_2, object object_3, object object_4, object object_5, object object_6, object object_7, object object_8, object object_9)
		{
			Class538.smethod_11(exception_0, new object[]
			{
				object_0,
				object_1,
				object_2,
				object_3,
				object_4,
				object_5,
				object_6,
				object_7,
				object_8,
				object_9
			});
		}

		// Token: 0x060027E4 RID: 10212 RVA: 0x00102BF4 File Offset: 0x00100DF4
		public static void smethod_11(Exception exception_0, object[] object_0)
		{
			int methodID = -1;
			int ilOffset = -1;
			int num = 0;
			StackTrace stackTrace = new StackTrace(exception_0);
			try
			{
				if (exception_0.StackTrace != null)
				{
					string[] array = exception_0.StackTrace.Split(new char[]
					{
						'\r',
						'\n'
					});
					for (int i = 0; i < array.Length; i++)
					{
						if (array[i].Length > 0)
						{
							num++;
						}
					}
				}
			}
			catch
			{
				num = -1;
			}
			try
			{
				if (stackTrace.FrameCount > 0)
				{
					StackFrame frame = stackTrace.GetFrame(stackTrace.FrameCount - 1);
					methodID = (frame.GetMethod().MetadataToken & 16777215) - 1;
					ilOffset = frame.GetILOffset();
				}
			}
			catch
			{
			}
			try
			{
				SmartStackFrame value = new SmartStackFrame(methodID, object_0, ilOffset, num);
				LinkedList<object> linkedList;
				if (!exception_0.Data.Contains("SmartStackFrames"))
				{
					linkedList = new LinkedList<object>();
					exception_0.Data["SmartStackFrames"] = linkedList;
				}
				else
				{
					linkedList = (LinkedList<object>)exception_0.Data["SmartStackFrames"];
				}
				linkedList.AddLast(value);
			}
			catch
			{
			}
		}

		// Token: 0x040013CB RID: 5067
		public const string string_0 = "SmartStackFrames";
	}
}
