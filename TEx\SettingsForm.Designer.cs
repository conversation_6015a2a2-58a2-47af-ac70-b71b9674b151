﻿namespace TEx
{
	// Token: 0x020001A9 RID: 425
	public sealed partial class SettingsForm : global::System.Windows.Forms.Form
	{
		// Token: 0x06001085 RID: 4229 RVA: 0x0006C93C File Offset: 0x0006AB3C
		private void InitializeComponent()
		{
			this.button_Cancel = new global::System.Windows.Forms.Button();
			this.button_OK = new global::System.Windows.Forms.Button();
			this.chkBx_saveSpeed = new global::System.Windows.Forms.CheckBox();
			this.chkBx_saveWindow = new global::System.Windows.Forms.CheckBox();
			this.chkBx_savePage = new global::System.Windows.Forms.CheckBox();
			this.chkBox_pauseAtDayEnd = new global::System.Windows.Forms.CheckBox();
			this.chkBx_confQuit = new global::System.Windows.Forms.CheckBox();
			this.groupBox1 = new global::System.Windows.Forms.GroupBox();
			this.chkBox_IfFollowPrcInTradingInput = new global::System.Windows.Forms.CheckBox();
			this.chkBox_SyncToolbarTradingTabPriceUnits = new global::System.Windows.Forms.CheckBox();
			this.checkBox_AutoShowCurrTransTab = new global::System.Windows.Forms.CheckBox();
			this.groupBox5 = new global::System.Windows.Forms.GroupBox();
			this.tabControl_Settings = new global::System.Windows.Forms.TabControl();
			this.tabPage_ChtSetting = new global::System.Windows.Forms.TabPage();
			this.groupBox10 = new global::System.Windows.Forms.GroupBox();
			this.picBox_theme = new global::System.Windows.Forms.PictureBox();
			this.comboBox_BarTypes = new global::System.Windows.Forms.ComboBox();
			this.comboBox_ChartThemes = new global::System.Windows.Forms.ComboBox();
			this.label9 = new global::System.Windows.Forms.Label();
			this.label10 = new global::System.Windows.Forms.Label();
			this.groupBox9 = new global::System.Windows.Forms.GroupBox();
			this.chkBox_IfShowDayOfWeek = new global::System.Windows.Forms.CheckBox();
			this.label12 = new global::System.Windows.Forms.Label();
			this.lbl_DispDayDivLine2 = new global::System.Windows.Forms.Label();
			this.cmbBx_DDlineChtMins = new global::System.Windows.Forms.ComboBox();
			this.chkBox_ifDispDayDivLine = new global::System.Windows.Forms.CheckBox();
			this.groupBox3 = new global::System.Windows.Forms.GroupBox();
			this.checkBox_ShowHiLowMarks = new global::System.Windows.Forms.CheckBox();
			this.groupBox2 = new global::System.Windows.Forms.GroupBox();
			this.picBox_ArrwType = new global::System.Windows.Forms.PictureBox();
			this.comboBox_arrwType = new global::System.Windows.Forms.ComboBox();
			this.groupBox_TransNote = new global::System.Windows.Forms.GroupBox();
			this.label7 = new global::System.Windows.Forms.Label();
			this.checkBox_TransNotesFillTransparant = new global::System.Windows.Forms.CheckBox();
			this.label_dispPeriodOfTransArw = new global::System.Windows.Forms.Label();
			this.checkBox_OnlyShowUserNoteBox = new global::System.Windows.Forms.CheckBox();
			this.cmbBx_PeriodForTransArrow = new global::System.Windows.Forms.ComboBox();
			this.checkBox_ShowTOdrLine = new global::System.Windows.Forms.CheckBox();
			this.checkBox_ShowTransNotesBorder = new global::System.Windows.Forms.CheckBox();
			this.panel1 = new global::System.Windows.Forms.Panel();
			this.radioBtn_AllTransArrow = new global::System.Windows.Forms.RadioButton();
			this.radioBtn_CurrTransArrow = new global::System.Windows.Forms.RadioButton();
			this.checkBox_AlwaysShowTransNoteBox = new global::System.Windows.Forms.CheckBox();
			this.checkBox_ShowTransArrow = new global::System.Windows.Forms.CheckBox();
			this.tabPage_UISettings = new global::System.Windows.Forms.TabPage();
			this.groupBox14 = new global::System.Windows.Forms.GroupBox();
			this.panel_SymbSwitchTime = new global::System.Windows.Forms.Panel();
			this.radioBtn_SymbSwitchCurrDt = new global::System.Windows.Forms.RadioButton();
			this.radioBtn_SymbSwitchLastDt = new global::System.Windows.Forms.RadioButton();
			this.label8 = new global::System.Windows.Forms.Label();
			this.groupBox12 = new global::System.Windows.Forms.GroupBox();
			this.chkBox_IfShowAcctInfoOnTransTabHeader = new global::System.Windows.Forms.CheckBox();
			this.groupBox13 = new global::System.Windows.Forms.GroupBox();
			this.panel2 = new global::System.Windows.Forms.Panel();
			this.radioBtn_ShowSymbCode = new global::System.Windows.Forms.RadioButton();
			this.radioBtn_ShowSymbCNName = new global::System.Windows.Forms.RadioButton();
			this.label11 = new global::System.Windows.Forms.Label();
			this.groupBox6 = new global::System.Windows.Forms.GroupBox();
			this.chkBox_ConfDblClickClsTrans = new global::System.Windows.Forms.CheckBox();
			this.chkBox_ConfOrdr = new global::System.Windows.Forms.CheckBox();
			this.checkBox_PlayOCSound = new global::System.Windows.Forms.CheckBox();
			this.tabPage_TradingSettings = new global::System.Windows.Forms.TabPage();
			this.groupBox4 = new global::System.Windows.Forms.GroupBox();
			this.panel_ClsOrdr = new global::System.Windows.Forms.Panel();
			this.radioBtn_ClsOrd_NewFirst = new global::System.Windows.Forms.RadioButton();
			this.radioBtn_ClsOrd_OldFirst = new global::System.Windows.Forms.RadioButton();
			this.groupBox8 = new global::System.Windows.Forms.GroupBox();
			this.label6 = new global::System.Windows.Forms.Label();
			this.txtBox_ROpenRAmt = new global::System.Windows.Forms.TextBox();
			this.label_ROpenPers = new global::System.Windows.Forms.Label();
			this.numUpDown_ROpenRatio = new global::System.Windows.Forms.NumericUpDown();
			this.chkBox_ROpenShowCnfmDlg = new global::System.Windows.Forms.CheckBox();
			this.radioBtn_ROpenFixAmt = new global::System.Windows.Forms.RadioButton();
			this.radioBtn_ROpenRatio = new global::System.Windows.Forms.RadioButton();
			this.groupBox_ClsOdr = new global::System.Windows.Forms.GroupBox();
			this.chkBox_IfAutoCancelCloseTransOrder = new global::System.Windows.Forms.CheckBox();
			this.chkBox_IfAutoCancelOpenTransOrder = new global::System.Windows.Forms.CheckBox();
			this.groupBox7 = new global::System.Windows.Forms.GroupBox();
			this.groupBox11 = new global::System.Windows.Forms.GroupBox();
			this.comboBox_RationedShareTreatmt = new global::System.Windows.Forms.ComboBox();
			this.comboBox_DividentTreatment = new global::System.Windows.Forms.ComboBox();
			this.label4 = new global::System.Windows.Forms.Label();
			this.label3 = new global::System.Windows.Forms.Label();
			this.comboBox_BonusShareTreatment = new global::System.Windows.Forms.ComboBox();
			this.label2 = new global::System.Windows.Forms.Label();
			this.comboBox_StkRstMethod = new global::System.Windows.Forms.ComboBox();
			this.label1 = new global::System.Windows.Forms.Label();
			this.chkBox_T0ForStock = new global::System.Windows.Forms.CheckBox();
			this.chkBox_ShortForStock = new global::System.Windows.Forms.CheckBox();
			this.groupBox_自动盈损 = new global::System.Windows.Forms.GroupBox();
			this.linkLabel_AutoPrftPt = new global::System.Windows.Forms.LinkLabel();
			this.linkLabel_AutoStopPt = new global::System.Windows.Forms.LinkLabel();
			this.chkBox_AutoPrft = new global::System.Windows.Forms.CheckBox();
			this.chkBox_AutoStop = new global::System.Windows.Forms.CheckBox();
			this.tabPage_BackupSettings = new global::System.Windows.Forms.TabPage();
			this.label_bkSyncNote = new global::System.Windows.Forms.Label();
			this.btn_BkupToSrv = new global::System.Windows.Forms.Button();
			this.btn_RestoreBkup = new global::System.Windows.Forms.Button();
			this.picBox_Bulb = new global::System.Windows.Forms.PictureBox();
			this.groupBox_SyncDirection = new global::System.Windows.Forms.GroupBox();
			this.comboBox_SyncConflictTreatmt = new global::System.Windows.Forms.ComboBox();
			this.label5 = new global::System.Windows.Forms.Label();
			this.comboBox_OverwritingLocalBkupFiles = new global::System.Windows.Forms.ComboBox();
			this.radioBtn_BackupDirectionOnlyToSrv = new global::System.Windows.Forms.RadioButton();
			this.label_同步本地提示 = new global::System.Windows.Forms.Label();
			this.radioBtn_BackupDualDirection = new global::System.Windows.Forms.RadioButton();
			this.groupBox_SyncTime = new global::System.Windows.Forms.GroupBox();
			this.comboBox_BackupPeriod = new global::System.Windows.Forms.ComboBox();
			this.chkBox_BackupTimePeriodically = new global::System.Windows.Forms.CheckBox();
			this.chkBox_BackupTimeExit = new global::System.Windows.Forms.CheckBox();
			this.chkBox_BackupTimeStartup = new global::System.Windows.Forms.CheckBox();
			this.groupBox_SyncItems = new global::System.Windows.Forms.GroupBox();
			this.chkBox_BackupSymbParams = new global::System.Windows.Forms.CheckBox();
			this.chkBox_BackupDrwObj = new global::System.Windows.Forms.CheckBox();
			this.chkBox_BackupZixuan = new global::System.Windows.Forms.CheckBox();
			this.chkBox_BackupPages = new global::System.Windows.Forms.CheckBox();
			this.chkBox_BackupUISettings = new global::System.Windows.Forms.CheckBox();
			this.chkBox_BackupTrans = new global::System.Windows.Forms.CheckBox();
			this.groupBox1.SuspendLayout();
			this.groupBox5.SuspendLayout();
			this.tabControl_Settings.SuspendLayout();
			this.tabPage_ChtSetting.SuspendLayout();
			this.groupBox10.SuspendLayout();
			((global::System.ComponentModel.ISupportInitialize)this.picBox_theme).BeginInit();
			this.groupBox9.SuspendLayout();
			this.groupBox3.SuspendLayout();
			((global::System.ComponentModel.ISupportInitialize)this.picBox_ArrwType).BeginInit();
			this.panel1.SuspendLayout();
			this.tabPage_UISettings.SuspendLayout();
			this.groupBox14.SuspendLayout();
			this.panel_SymbSwitchTime.SuspendLayout();
			this.groupBox12.SuspendLayout();
			this.panel2.SuspendLayout();
			this.groupBox6.SuspendLayout();
			this.tabPage_TradingSettings.SuspendLayout();
			this.groupBox4.SuspendLayout();
			this.panel_ClsOrdr.SuspendLayout();
			this.groupBox8.SuspendLayout();
			((global::System.ComponentModel.ISupportInitialize)this.numUpDown_ROpenRatio).BeginInit();
			this.groupBox_ClsOdr.SuspendLayout();
			this.groupBox7.SuspendLayout();
			this.groupBox_自动盈损.SuspendLayout();
			this.tabPage_BackupSettings.SuspendLayout();
			((global::System.ComponentModel.ISupportInitialize)this.picBox_Bulb).BeginInit();
			this.groupBox_SyncDirection.SuspendLayout();
			this.groupBox_SyncTime.SuspendLayout();
			this.groupBox_SyncItems.SuspendLayout();
			base.SuspendLayout();
			this.button_Cancel.DialogResult = global::System.Windows.Forms.DialogResult.Cancel;
			this.button_Cancel.Location = new global::System.Drawing.Point(645, 452);
			this.button_Cancel.Name = "button_Cancel";
			this.button_Cancel.Size = new global::System.Drawing.Size(120, 32);
			this.button_Cancel.TabIndex = 1;
			this.button_Cancel.Text = "取消";
			this.button_Cancel.UseVisualStyleBackColor = true;
			this.button_Cancel.Click += new global::System.EventHandler(this.button_Cancel_Click);
			this.button_OK.Location = new global::System.Drawing.Point(513, 452);
			this.button_OK.Name = "button_OK";
			this.button_OK.Size = new global::System.Drawing.Size(120, 32);
			this.button_OK.TabIndex = 0;
			this.button_OK.Text = "确定";
			this.button_OK.UseVisualStyleBackColor = true;
			this.button_OK.Click += new global::System.EventHandler(this.button_OK_Click);
			this.chkBx_saveSpeed.AutoSize = true;
			this.chkBx_saveSpeed.Location = new global::System.Drawing.Point(22, 55);
			this.chkBx_saveSpeed.Name = "chkBx_saveSpeed";
			this.chkBx_saveSpeed.Size = new global::System.Drawing.Size(194, 19);
			this.chkBx_saveSpeed.TabIndex = 1;
			this.chkBx_saveSpeed.Text = "退出时保存行情回放速度";
			this.chkBx_saveSpeed.UseVisualStyleBackColor = true;
			this.chkBx_saveWindow.AutoSize = true;
			this.chkBx_saveWindow.Location = new global::System.Drawing.Point(22, 111);
			this.chkBx_saveWindow.Name = "chkBx_saveWindow";
			this.chkBx_saveWindow.Size = new global::System.Drawing.Size(224, 19);
			this.chkBx_saveWindow.TabIndex = 3;
			this.chkBx_saveWindow.Text = "退出时保存窗口及工具栏位置";
			this.chkBx_saveWindow.UseVisualStyleBackColor = true;
			this.chkBx_savePage.AutoSize = true;
			this.chkBx_savePage.Location = new global::System.Drawing.Point(22, 83);
			this.chkBx_savePage.Name = "chkBx_savePage";
			this.chkBx_savePage.Size = new global::System.Drawing.Size(194, 19);
			this.chkBx_savePage.TabIndex = 2;
			this.chkBx_savePage.Text = "退出时保存当前页面设置";
			this.chkBx_savePage.UseVisualStyleBackColor = true;
			this.chkBox_pauseAtDayEnd.AutoSize = true;
			this.chkBox_pauseAtDayEnd.Location = new global::System.Drawing.Point(22, 30);
			this.chkBox_pauseAtDayEnd.Name = "chkBox_pauseAtDayEnd";
			this.chkBox_pauseAtDayEnd.Size = new global::System.Drawing.Size(194, 19);
			this.chkBox_pauseAtDayEnd.TabIndex = 0;
			this.chkBox_pauseAtDayEnd.Text = "日行情结束自动暂停回放";
			this.chkBox_pauseAtDayEnd.UseVisualStyleBackColor = true;
			this.chkBx_confQuit.AutoSize = true;
			this.chkBx_confQuit.Location = new global::System.Drawing.Point(22, 27);
			this.chkBx_confQuit.Name = "chkBx_confQuit";
			this.chkBx_confQuit.Size = new global::System.Drawing.Size(164, 19);
			this.chkBx_confQuit.TabIndex = 0;
			this.chkBx_confQuit.Text = "退出程序时提示确认";
			this.chkBx_confQuit.UseVisualStyleBackColor = true;
			this.groupBox1.Controls.Add(this.chkBx_saveWindow);
			this.groupBox1.Controls.Add(this.chkBx_saveSpeed);
			this.groupBox1.Controls.Add(this.chkBx_savePage);
			this.groupBox1.Controls.Add(this.chkBx_confQuit);
			this.groupBox1.Location = new global::System.Drawing.Point(22, 16);
			this.groupBox1.Name = "groupBox1";
			this.groupBox1.Size = new global::System.Drawing.Size(340, 148);
			this.groupBox1.TabIndex = 0;
			this.groupBox1.TabStop = false;
			this.groupBox1.Text = "退出设置";
			this.chkBox_IfFollowPrcInTradingInput.AutoSize = true;
			this.chkBox_IfFollowPrcInTradingInput.Location = new global::System.Drawing.Point(21, 83);
			this.chkBox_IfFollowPrcInTradingInput.Name = "chkBox_IfFollowPrcInTradingInput";
			this.chkBox_IfFollowPrcInTradingInput.Size = new global::System.Drawing.Size(224, 19);
			this.chkBox_IfFollowPrcInTradingInput.TabIndex = 2;
			this.chkBox_IfFollowPrcInTradingInput.Text = "价格输入框自动跟随行情更新";
			this.chkBox_IfFollowPrcInTradingInput.UseVisualStyleBackColor = true;
			this.chkBox_SyncToolbarTradingTabPriceUnits.AutoSize = true;
			this.chkBox_SyncToolbarTradingTabPriceUnits.Location = new global::System.Drawing.Point(21, 55);
			this.chkBox_SyncToolbarTradingTabPriceUnits.Name = "chkBox_SyncToolbarTradingTabPriceUnits";
			this.chkBox_SyncToolbarTradingTabPriceUnits.Size = new global::System.Drawing.Size(232, 19);
			this.chkBox_SyncToolbarTradingTabPriceUnits.TabIndex = 1;
			this.chkBox_SyncToolbarTradingTabPriceUnits.Text = "同步工具栏/交易栏价格及手数";
			this.chkBox_SyncToolbarTradingTabPriceUnits.UseVisualStyleBackColor = true;
			this.checkBox_AutoShowCurrTransTab.AutoSize = true;
			this.checkBox_AutoShowCurrTransTab.Location = new global::System.Drawing.Point(21, 27);
			this.checkBox_AutoShowCurrTransTab.Name = "checkBox_AutoShowCurrTransTab";
			this.checkBox_AutoShowCurrTransTab.Size = new global::System.Drawing.Size(179, 19);
			this.checkBox_AutoShowCurrTransTab.TabIndex = 0;
			this.checkBox_AutoShowCurrTransTab.Text = "开仓后自动显示持仓栏";
			this.checkBox_AutoShowCurrTransTab.UseVisualStyleBackColor = true;
			this.groupBox5.Controls.Add(this.chkBox_pauseAtDayEnd);
			this.groupBox5.Location = new global::System.Drawing.Point(22, 304);
			this.groupBox5.Name = "groupBox5";
			this.groupBox5.Size = new global::System.Drawing.Size(340, 70);
			this.groupBox5.TabIndex = 2;
			this.groupBox5.TabStop = false;
			this.groupBox5.Text = "其他设置";
			this.tabControl_Settings.Controls.Add(this.tabPage_ChtSetting);
			this.tabControl_Settings.Controls.Add(this.tabPage_UISettings);
			this.tabControl_Settings.Controls.Add(this.tabPage_TradingSettings);
			this.tabControl_Settings.Controls.Add(this.tabPage_BackupSettings);
			this.tabControl_Settings.Location = new global::System.Drawing.Point(12, 12);
			this.tabControl_Settings.Name = "tabControl_Settings";
			this.tabControl_Settings.SelectedIndex = 0;
			this.tabControl_Settings.Size = new global::System.Drawing.Size(755, 426);
			this.tabControl_Settings.TabIndex = 16;
			this.tabPage_ChtSetting.BackColor = global::System.Drawing.SystemColors.Control;
			this.tabPage_ChtSetting.Controls.Add(this.groupBox10);
			this.tabPage_ChtSetting.Controls.Add(this.groupBox9);
			this.tabPage_ChtSetting.Controls.Add(this.groupBox3);
			this.tabPage_ChtSetting.Location = new global::System.Drawing.Point(4, 25);
			this.tabPage_ChtSetting.Name = "tabPage_ChtSetting";
			this.tabPage_ChtSetting.Padding = new global::System.Windows.Forms.Padding(3);
			this.tabPage_ChtSetting.Size = new global::System.Drawing.Size(747, 397);
			this.tabPage_ChtSetting.TabIndex = 3;
			this.tabPage_ChtSetting.Text = "图表显示";
			this.groupBox10.Controls.Add(this.picBox_theme);
			this.groupBox10.Controls.Add(this.comboBox_BarTypes);
			this.groupBox10.Controls.Add(this.comboBox_ChartThemes);
			this.groupBox10.Controls.Add(this.label9);
			this.groupBox10.Controls.Add(this.label10);
			this.groupBox10.Location = new global::System.Drawing.Point(384, 16);
			this.groupBox10.Name = "groupBox10";
			this.groupBox10.Size = new global::System.Drawing.Size(340, 228);
			this.groupBox10.TabIndex = 19;
			this.groupBox10.TabStop = false;
			this.groupBox10.Text = "显示方案";
			this.picBox_theme.Image = global::ns28.Class372.theme_1_3;
			this.picBox_theme.Location = new global::System.Drawing.Point(48, 93);
			this.picBox_theme.Name = "picBox_theme";
			this.picBox_theme.Size = new global::System.Drawing.Size(261, 120);
			this.picBox_theme.SizeMode = global::System.Windows.Forms.PictureBoxSizeMode.StretchImage;
			this.picBox_theme.TabIndex = 19;
			this.picBox_theme.TabStop = false;
			this.comboBox_BarTypes.DropDownStyle = global::System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_BarTypes.FormattingEnabled = true;
			this.comboBox_BarTypes.Items.AddRange(new object[]
			{
				"蜡烛线(空心阳线)",
				"蜡烛线(实心阳线)",
				"竹节线(OHLC)",
				"竹节线(HLC)"
			});
			this.comboBox_BarTypes.Location = new global::System.Drawing.Point(111, 26);
			this.comboBox_BarTypes.Name = "comboBox_BarTypes";
			this.comboBox_BarTypes.Size = new global::System.Drawing.Size(198, 23);
			this.comboBox_BarTypes.TabIndex = 16;
			this.comboBox_ChartThemes.DropDownStyle = global::System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_ChartThemes.FormattingEnabled = true;
			this.comboBox_ChartThemes.Items.AddRange(new object[]
			{
				"黑红经典",
				"绿白经典",
				"绿白现代"
			});
			this.comboBox_ChartThemes.Location = new global::System.Drawing.Point(111, 59);
			this.comboBox_ChartThemes.Name = "comboBox_ChartThemes";
			this.comboBox_ChartThemes.Size = new global::System.Drawing.Size(198, 23);
			this.comboBox_ChartThemes.TabIndex = 18;
			this.label9.AutoSize = true;
			this.label9.Location = new global::System.Drawing.Point(23, 30);
			this.label9.Name = "label9";
			this.label9.Size = new global::System.Drawing.Size(82, 15);
			this.label9.TabIndex = 3;
			this.label9.Text = "Ｋ线类型：";
			this.label10.AutoSize = true;
			this.label10.Location = new global::System.Drawing.Point(23, 63);
			this.label10.Name = "label10";
			this.label10.Size = new global::System.Drawing.Size(82, 15);
			this.label10.TabIndex = 17;
			this.label10.Text = "界面风格：";
			this.groupBox9.Controls.Add(this.chkBox_IfShowDayOfWeek);
			this.groupBox9.Controls.Add(this.label12);
			this.groupBox9.Controls.Add(this.lbl_DispDayDivLine2);
			this.groupBox9.Controls.Add(this.cmbBx_DDlineChtMins);
			this.groupBox9.Controls.Add(this.chkBox_ifDispDayDivLine);
			this.groupBox9.Location = new global::System.Drawing.Point(385, 255);
			this.groupBox9.Name = "groupBox9";
			this.groupBox9.Size = new global::System.Drawing.Size(340, 121);
			this.groupBox9.TabIndex = 2;
			this.groupBox9.TabStop = false;
			this.groupBox9.Text = "图表其他";
			this.chkBox_IfShowDayOfWeek.AutoSize = true;
			this.chkBox_IfShowDayOfWeek.Location = new global::System.Drawing.Point(23, 84);
			this.chkBox_IfShowDayOfWeek.Name = "chkBox_IfShowDayOfWeek";
			this.chkBox_IfShowDayOfWeek.Size = new global::System.Drawing.Size(119, 19);
			this.chkBox_IfShowDayOfWeek.TabIndex = 17;
			this.chkBox_IfShowDayOfWeek.Text = "显示星期信息";
			this.chkBox_IfShowDayOfWeek.UseVisualStyleBackColor = true;
			this.label12.AutoSize = true;
			this.label12.Location = new global::System.Drawing.Point(43, 56);
			this.label12.Name = "label12";
			this.label12.Size = new global::System.Drawing.Size(52, 15);
			this.label12.TabIndex = 16;
			this.label12.Text = "应用于";
			this.lbl_DispDayDivLine2.AutoSize = true;
			this.lbl_DispDayDivLine2.Location = new global::System.Drawing.Point(187, 56);
			this.lbl_DispDayDivLine2.Name = "lbl_DispDayDivLine2";
			this.lbl_DispDayDivLine2.Size = new global::System.Drawing.Size(112, 15);
			this.lbl_DispDayDivLine2.TabIndex = 13;
			this.lbl_DispDayDivLine2.Text = "及以下级别图表";
			this.lbl_DispDayDivLine2.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.cmbBx_DDlineChtMins.DropDownStyle = global::System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.cmbBx_DDlineChtMins.FormattingEnabled = true;
			this.cmbBx_DDlineChtMins.Location = new global::System.Drawing.Point(103, 52);
			this.cmbBx_DDlineChtMins.Name = "cmbBx_DDlineChtMins";
			this.cmbBx_DDlineChtMins.Size = new global::System.Drawing.Size(76, 23);
			this.cmbBx_DDlineChtMins.TabIndex = 15;
			this.chkBox_ifDispDayDivLine.AutoSize = true;
			this.chkBox_ifDispDayDivLine.Location = new global::System.Drawing.Point(23, 30);
			this.chkBox_ifDispDayDivLine.Name = "chkBox_ifDispDayDivLine";
			this.chkBox_ifDispDayDivLine.Size = new global::System.Drawing.Size(164, 19);
			this.chkBox_ifDispDayDivLine.TabIndex = 14;
			this.chkBox_ifDispDayDivLine.Text = "显示日收盘分隔虚线";
			this.chkBox_ifDispDayDivLine.UseVisualStyleBackColor = true;
			this.groupBox3.Controls.Add(this.checkBox_ShowHiLowMarks);
			this.groupBox3.Controls.Add(this.groupBox2);
			this.groupBox3.Controls.Add(this.picBox_ArrwType);
			this.groupBox3.Controls.Add(this.comboBox_arrwType);
			this.groupBox3.Controls.Add(this.groupBox_TransNote);
			this.groupBox3.Controls.Add(this.label7);
			this.groupBox3.Controls.Add(this.checkBox_TransNotesFillTransparant);
			this.groupBox3.Controls.Add(this.label_dispPeriodOfTransArw);
			this.groupBox3.Controls.Add(this.checkBox_OnlyShowUserNoteBox);
			this.groupBox3.Controls.Add(this.cmbBx_PeriodForTransArrow);
			this.groupBox3.Controls.Add(this.checkBox_ShowTOdrLine);
			this.groupBox3.Controls.Add(this.checkBox_ShowTransNotesBorder);
			this.groupBox3.Controls.Add(this.panel1);
			this.groupBox3.Controls.Add(this.checkBox_AlwaysShowTransNoteBox);
			this.groupBox3.Controls.Add(this.checkBox_ShowTransArrow);
			this.groupBox3.Location = new global::System.Drawing.Point(22, 16);
			this.groupBox3.Name = "groupBox3";
			this.groupBox3.Size = new global::System.Drawing.Size(340, 360);
			this.groupBox3.TabIndex = 1;
			this.groupBox3.TabStop = false;
			this.groupBox3.Text = "交易标示";
			this.checkBox_ShowHiLowMarks.AutoSize = true;
			this.checkBox_ShowHiLowMarks.Location = new global::System.Drawing.Point(22, 327);
			this.checkBox_ShowHiLowMarks.Name = "checkBox_ShowHiLowMarks";
			this.checkBox_ShowHiLowMarks.Size = new global::System.Drawing.Size(164, 19);
			this.checkBox_ShowHiLowMarks.TabIndex = 18;
			this.checkBox_ShowHiLowMarks.Text = "显示主图高低点数值";
			this.checkBox_ShowHiLowMarks.UseVisualStyleBackColor = true;
			this.groupBox2.Location = new global::System.Drawing.Point(44, 106);
			this.groupBox2.Name = "groupBox2";
			this.groupBox2.Size = new global::System.Drawing.Size(215, 2);
			this.groupBox2.TabIndex = 17;
			this.groupBox2.TabStop = false;
			this.picBox_ArrwType.Image = global::ns28.Class372.BigArrwSamples;
			this.picBox_ArrwType.Location = new global::System.Drawing.Point(47, 143);
			this.picBox_ArrwType.Name = "picBox_ArrwType";
			this.picBox_ArrwType.Size = new global::System.Drawing.Size(174, 39);
			this.picBox_ArrwType.SizeMode = global::System.Windows.Forms.PictureBoxSizeMode.AutoSize;
			this.picBox_ArrwType.TabIndex = 16;
			this.picBox_ArrwType.TabStop = false;
			this.comboBox_arrwType.DropDownStyle = global::System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_arrwType.FormattingEnabled = true;
			this.comboBox_arrwType.Items.AddRange(new object[]
			{
				"醒目大箭头",
				"传统小箭头"
			});
			this.comboBox_arrwType.Location = new global::System.Drawing.Point(132, 113);
			this.comboBox_arrwType.Name = "comboBox_arrwType";
			this.comboBox_arrwType.Size = new global::System.Drawing.Size(110, 23);
			this.comboBox_arrwType.TabIndex = 15;
			this.groupBox_TransNote.Location = new global::System.Drawing.Point(44, 188);
			this.groupBox_TransNote.Name = "groupBox_TransNote";
			this.groupBox_TransNote.Size = new global::System.Drawing.Size(215, 2);
			this.groupBox_TransNote.TabIndex = 13;
			this.groupBox_TransNote.TabStop = false;
			this.label7.AutoSize = true;
			this.label7.Location = new global::System.Drawing.Point(44, 117);
			this.label7.Name = "label7";
			this.label7.Size = new global::System.Drawing.Size(82, 15);
			this.label7.TabIndex = 14;
			this.label7.Text = "箭头风格：";
			this.checkBox_TransNotesFillTransparant.AutoSize = true;
			this.checkBox_TransNotesFillTransparant.Location = new global::System.Drawing.Point(44, 270);
			this.checkBox_TransNotesFillTransparant.Name = "checkBox_TransNotesFillTransparant";
			this.checkBox_TransNotesFillTransparant.Size = new global::System.Drawing.Size(179, 19);
			this.checkBox_TransNotesFillTransparant.TabIndex = 7;
			this.checkBox_TransNotesFillTransparant.Text = "交易注释文字背景透明";
			this.checkBox_TransNotesFillTransparant.TextAlign = global::System.Drawing.ContentAlignment.MiddleCenter;
			this.checkBox_TransNotesFillTransparant.UseVisualStyleBackColor = true;
			this.label_dispPeriodOfTransArw.AutoSize = true;
			this.label_dispPeriodOfTransArw.Location = new global::System.Drawing.Point(123, 81);
			this.label_dispPeriodOfTransArw.Name = "label_dispPeriodOfTransArw";
			this.label_dispPeriodOfTransArw.Size = new global::System.Drawing.Size(112, 15);
			this.label_dispPeriodOfTransArw.TabIndex = 12;
			this.label_dispPeriodOfTransArw.Text = "及以下级别图表";
			this.label_dispPeriodOfTransArw.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.checkBox_OnlyShowUserNoteBox.AutoSize = true;
			this.checkBox_OnlyShowUserNoteBox.Location = new global::System.Drawing.Point(66, 220);
			this.checkBox_OnlyShowUserNoteBox.Name = "checkBox_OnlyShowUserNoteBox";
			this.checkBox_OnlyShowUserNoteBox.Size = new global::System.Drawing.Size(134, 19);
			this.checkBox_OnlyShowUserNoteBox.TabIndex = 5;
			this.checkBox_OnlyShowUserNoteBox.Text = "仅显示用户注释";
			this.checkBox_OnlyShowUserNoteBox.UseVisualStyleBackColor = true;
			this.cmbBx_PeriodForTransArrow.DropDownStyle = global::System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.cmbBx_PeriodForTransArrow.FormattingEnabled = true;
			this.cmbBx_PeriodForTransArrow.Location = new global::System.Drawing.Point(44, 78);
			this.cmbBx_PeriodForTransArrow.Name = "cmbBx_PeriodForTransArrow";
			this.cmbBx_PeriodForTransArrow.Size = new global::System.Drawing.Size(76, 23);
			this.cmbBx_PeriodForTransArrow.TabIndex = 3;
			this.checkBox_ShowTOdrLine.AutoSize = true;
			this.checkBox_ShowTOdrLine.Location = new global::System.Drawing.Point(22, 300);
			this.checkBox_ShowTOdrLine.Name = "checkBox_ShowTOdrLine";
			this.checkBox_ShowTOdrLine.Size = new global::System.Drawing.Size(239, 19);
			this.checkBox_ShowTOdrLine.TabIndex = 0;
			this.checkBox_ShowTOdrLine.Text = "显示持仓、委托及条件单提示线";
			this.checkBox_ShowTOdrLine.UseVisualStyleBackColor = true;
			this.checkBox_ShowTransNotesBorder.AutoSize = true;
			this.checkBox_ShowTransNotesBorder.Location = new global::System.Drawing.Point(44, 245);
			this.checkBox_ShowTransNotesBorder.Name = "checkBox_ShowTransNotesBorder";
			this.checkBox_ShowTransNotesBorder.Size = new global::System.Drawing.Size(179, 19);
			this.checkBox_ShowTransNotesBorder.TabIndex = 6;
			this.checkBox_ShowTransNotesBorder.Text = "显示交易注释文字边框";
			this.checkBox_ShowTransNotesBorder.UseVisualStyleBackColor = true;
			this.panel1.Controls.Add(this.radioBtn_AllTransArrow);
			this.panel1.Controls.Add(this.radioBtn_CurrTransArrow);
			this.panel1.Location = new global::System.Drawing.Point(42, 49);
			this.panel1.Name = "panel1";
			this.panel1.Size = new global::System.Drawing.Size(200, 27);
			this.panel1.TabIndex = 7;
			this.radioBtn_AllTransArrow.AutoSize = true;
			this.radioBtn_AllTransArrow.Location = new global::System.Drawing.Point(98, 4);
			this.radioBtn_AllTransArrow.Name = "radioBtn_AllTransArrow";
			this.radioBtn_AllTransArrow.Size = new global::System.Drawing.Size(88, 19);
			this.radioBtn_AllTransArrow.TabIndex = 1;
			this.radioBtn_AllTransArrow.TabStop = true;
			this.radioBtn_AllTransArrow.Text = "所有交易";
			this.radioBtn_AllTransArrow.UseVisualStyleBackColor = true;
			this.radioBtn_CurrTransArrow.AutoSize = true;
			this.radioBtn_CurrTransArrow.Location = new global::System.Drawing.Point(2, 4);
			this.radioBtn_CurrTransArrow.Name = "radioBtn_CurrTransArrow";
			this.radioBtn_CurrTransArrow.Size = new global::System.Drawing.Size(88, 19);
			this.radioBtn_CurrTransArrow.TabIndex = 0;
			this.radioBtn_CurrTransArrow.TabStop = true;
			this.radioBtn_CurrTransArrow.Text = "当前交易";
			this.radioBtn_CurrTransArrow.UseVisualStyleBackColor = true;
			this.checkBox_AlwaysShowTransNoteBox.AutoSize = true;
			this.checkBox_AlwaysShowTransNoteBox.Location = new global::System.Drawing.Point(44, 197);
			this.checkBox_AlwaysShowTransNoteBox.Name = "checkBox_AlwaysShowTransNoteBox";
			this.checkBox_AlwaysShowTransNoteBox.Size = new global::System.Drawing.Size(164, 19);
			this.checkBox_AlwaysShowTransNoteBox.TabIndex = 4;
			this.checkBox_AlwaysShowTransNoteBox.Text = "总是显示交易注释框";
			this.checkBox_AlwaysShowTransNoteBox.UseVisualStyleBackColor = true;
			this.checkBox_ShowTransArrow.AutoSize = true;
			this.checkBox_ShowTransArrow.Location = new global::System.Drawing.Point(22, 27);
			this.checkBox_ShowTransArrow.Name = "checkBox_ShowTransArrow";
			this.checkBox_ShowTransArrow.Size = new global::System.Drawing.Size(164, 19);
			this.checkBox_ShowTransArrow.TabIndex = 0;
			this.checkBox_ShowTransArrow.Text = "显示开平仓标示箭头";
			this.checkBox_ShowTransArrow.UseVisualStyleBackColor = true;
			this.tabPage_UISettings.BackColor = global::System.Drawing.SystemColors.Control;
			this.tabPage_UISettings.Controls.Add(this.groupBox14);
			this.tabPage_UISettings.Controls.Add(this.groupBox12);
			this.tabPage_UISettings.Controls.Add(this.groupBox6);
			this.tabPage_UISettings.Controls.Add(this.groupBox1);
			this.tabPage_UISettings.Controls.Add(this.groupBox5);
			this.tabPage_UISettings.Location = new global::System.Drawing.Point(4, 25);
			this.tabPage_UISettings.Name = "tabPage_UISettings";
			this.tabPage_UISettings.Padding = new global::System.Windows.Forms.Padding(3);
			this.tabPage_UISettings.Size = new global::System.Drawing.Size(747, 397);
			this.tabPage_UISettings.TabIndex = 0;
			this.tabPage_UISettings.Text = "界面参数";
			this.groupBox14.Controls.Add(this.panel_SymbSwitchTime);
			this.groupBox14.Controls.Add(this.label8);
			this.groupBox14.Location = new global::System.Drawing.Point(384, 238);
			this.groupBox14.Name = "groupBox14";
			this.groupBox14.Size = new global::System.Drawing.Size(340, 136);
			this.groupBox14.TabIndex = 0;
			this.groupBox14.TabStop = false;
			this.groupBox14.Text = "品种切换设置";
			this.panel_SymbSwitchTime.Controls.Add(this.radioBtn_SymbSwitchCurrDt);
			this.panel_SymbSwitchTime.Controls.Add(this.radioBtn_SymbSwitchLastDt);
			this.panel_SymbSwitchTime.Location = new global::System.Drawing.Point(21, 55);
			this.panel_SymbSwitchTime.Name = "panel_SymbSwitchTime";
			this.panel_SymbSwitchTime.Size = new global::System.Drawing.Size(287, 59);
			this.panel_SymbSwitchTime.TabIndex = 22;
			this.radioBtn_SymbSwitchCurrDt.AutoSize = true;
			this.radioBtn_SymbSwitchCurrDt.Location = new global::System.Drawing.Point(4, 34);
			this.radioBtn_SymbSwitchCurrDt.Name = "radioBtn_SymbSwitchCurrDt";
			this.radioBtn_SymbSwitchCurrDt.Size = new global::System.Drawing.Size(118, 19);
			this.radioBtn_SymbSwitchCurrDt.TabIndex = 1;
			this.radioBtn_SymbSwitchCurrDt.TabStop = true;
			this.radioBtn_SymbSwitchCurrDt.Text = "当前品种时间";
			this.radioBtn_SymbSwitchCurrDt.UseVisualStyleBackColor = true;
			this.radioBtn_SymbSwitchLastDt.AutoSize = true;
			this.radioBtn_SymbSwitchLastDt.Location = new global::System.Drawing.Point(4, 7);
			this.radioBtn_SymbSwitchLastDt.Name = "radioBtn_SymbSwitchLastDt";
			this.radioBtn_SymbSwitchLastDt.Size = new global::System.Drawing.Size(163, 19);
			this.radioBtn_SymbSwitchLastDt.TabIndex = 0;
			this.radioBtn_SymbSwitchLastDt.TabStop = true;
			this.radioBtn_SymbSwitchLastDt.Text = "新品种上次复盘时间";
			this.radioBtn_SymbSwitchLastDt.UseVisualStyleBackColor = true;
			this.label8.AutoSize = true;
			this.label8.Location = new global::System.Drawing.Point(18, 32);
			this.label8.Name = "label8";
			this.label8.Size = new global::System.Drawing.Size(232, 15);
			this.label8.TabIndex = 0;
			this.label8.Text = "切换到新品种后的默认行情时间：";
			this.groupBox12.Controls.Add(this.chkBox_IfShowAcctInfoOnTransTabHeader);
			this.groupBox12.Controls.Add(this.groupBox13);
			this.groupBox12.Controls.Add(this.panel2);
			this.groupBox12.Controls.Add(this.label11);
			this.groupBox12.Controls.Add(this.chkBox_IfFollowPrcInTradingInput);
			this.groupBox12.Controls.Add(this.checkBox_AutoShowCurrTransTab);
			this.groupBox12.Controls.Add(this.chkBox_SyncToolbarTradingTabPriceUnits);
			this.groupBox12.Location = new global::System.Drawing.Point(384, 16);
			this.groupBox12.Name = "groupBox12";
			this.groupBox12.Size = new global::System.Drawing.Size(340, 210);
			this.groupBox12.TabIndex = 4;
			this.groupBox12.TabStop = false;
			this.groupBox12.Text = "交易栏设置";
			this.chkBox_IfShowAcctInfoOnTransTabHeader.AutoSize = true;
			this.chkBox_IfShowAcctInfoOnTransTabHeader.Location = new global::System.Drawing.Point(21, 112);
			this.chkBox_IfShowAcctInfoOnTransTabHeader.Name = "chkBox_IfShowAcctInfoOnTransTabHeader";
			this.chkBox_IfShowAcctInfoOnTransTabHeader.Size = new global::System.Drawing.Size(239, 19);
			this.chkBox_IfShowAcctInfoOnTransTabHeader.TabIndex = 3;
			this.chkBox_IfShowAcctInfoOnTransTabHeader.Text = "功能栏标题显示可用金额等信息";
			this.chkBox_IfShowAcctInfoOnTransTabHeader.UseVisualStyleBackColor = true;
			this.groupBox13.Location = new global::System.Drawing.Point(20, 147);
			this.groupBox13.Name = "groupBox13";
			this.groupBox13.Size = new global::System.Drawing.Size(285, 2);
			this.groupBox13.TabIndex = 4;
			this.groupBox13.TabStop = false;
			this.panel2.Controls.Add(this.radioBtn_ShowSymbCode);
			this.panel2.Controls.Add(this.radioBtn_ShowSymbCNName);
			this.panel2.Location = new global::System.Drawing.Point(110, 158);
			this.panel2.Name = "panel2";
			this.panel2.Size = new global::System.Drawing.Size(193, 32);
			this.panel2.TabIndex = 6;
			this.radioBtn_ShowSymbCode.AutoSize = true;
			this.radioBtn_ShowSymbCode.Location = new global::System.Drawing.Point(96, 7);
			this.radioBtn_ShowSymbCode.Name = "radioBtn_ShowSymbCode";
			this.radioBtn_ShowSymbCode.Size = new global::System.Drawing.Size(88, 19);
			this.radioBtn_ShowSymbCode.TabIndex = 1;
			this.radioBtn_ShowSymbCode.TabStop = true;
			this.radioBtn_ShowSymbCode.Text = "品种代码";
			this.radioBtn_ShowSymbCode.UseVisualStyleBackColor = true;
			this.radioBtn_ShowSymbCNName.AutoSize = true;
			this.radioBtn_ShowSymbCNName.Location = new global::System.Drawing.Point(4, 7);
			this.radioBtn_ShowSymbCNName.Name = "radioBtn_ShowSymbCNName";
			this.radioBtn_ShowSymbCNName.Size = new global::System.Drawing.Size(88, 19);
			this.radioBtn_ShowSymbCNName.TabIndex = 0;
			this.radioBtn_ShowSymbCNName.TabStop = true;
			this.radioBtn_ShowSymbCNName.Text = "品种名称";
			this.radioBtn_ShowSymbCNName.UseVisualStyleBackColor = true;
			this.label11.AutoSize = true;
			this.label11.Location = new global::System.Drawing.Point(18, 167);
			this.label11.Name = "label11";
			this.label11.Size = new global::System.Drawing.Size(97, 15);
			this.label11.TabIndex = 5;
			this.label11.Text = "持仓栏显示：";
			this.groupBox6.Controls.Add(this.chkBox_ConfDblClickClsTrans);
			this.groupBox6.Controls.Add(this.chkBox_ConfOrdr);
			this.groupBox6.Controls.Add(this.checkBox_PlayOCSound);
			this.groupBox6.Location = new global::System.Drawing.Point(22, 175);
			this.groupBox6.Name = "groupBox6";
			this.groupBox6.Size = new global::System.Drawing.Size(340, 118);
			this.groupBox6.TabIndex = 1;
			this.groupBox6.TabStop = false;
			this.groupBox6.Text = "交易提示";
			this.chkBox_ConfDblClickClsTrans.AutoSize = true;
			this.chkBox_ConfDblClickClsTrans.Location = new global::System.Drawing.Point(23, 81);
			this.chkBox_ConfDblClickClsTrans.Name = "chkBox_ConfDblClickClsTrans";
			this.chkBox_ConfDblClickClsTrans.Size = new global::System.Drawing.Size(209, 19);
			this.chkBox_ConfDblClickClsTrans.TabIndex = 2;
			this.chkBox_ConfDblClickClsTrans.Text = "双击持仓无需确认自动平仓";
			this.chkBox_ConfDblClickClsTrans.UseVisualStyleBackColor = true;
			this.chkBox_ConfOrdr.AutoSize = true;
			this.chkBox_ConfOrdr.Location = new global::System.Drawing.Point(23, 27);
			this.chkBox_ConfOrdr.Name = "chkBox_ConfOrdr";
			this.chkBox_ConfOrdr.Size = new global::System.Drawing.Size(134, 19);
			this.chkBox_ConfOrdr.TabIndex = 0;
			this.chkBox_ConfOrdr.Text = "下单时提示确认";
			this.chkBox_ConfOrdr.UseVisualStyleBackColor = true;
			this.checkBox_PlayOCSound.AutoSize = true;
			this.checkBox_PlayOCSound.Location = new global::System.Drawing.Point(23, 54);
			this.checkBox_PlayOCSound.Name = "checkBox_PlayOCSound";
			this.checkBox_PlayOCSound.Size = new global::System.Drawing.Size(164, 19);
			this.checkBox_PlayOCSound.TabIndex = 1;
			this.checkBox_PlayOCSound.Text = "播放开平仓声音提示";
			this.checkBox_PlayOCSound.UseVisualStyleBackColor = true;
			this.tabPage_TradingSettings.BackColor = global::System.Drawing.SystemColors.Control;
			this.tabPage_TradingSettings.Controls.Add(this.groupBox4);
			this.tabPage_TradingSettings.Controls.Add(this.groupBox8);
			this.tabPage_TradingSettings.Controls.Add(this.groupBox_ClsOdr);
			this.tabPage_TradingSettings.Controls.Add(this.groupBox7);
			this.tabPage_TradingSettings.Controls.Add(this.groupBox_自动盈损);
			this.tabPage_TradingSettings.Location = new global::System.Drawing.Point(4, 25);
			this.tabPage_TradingSettings.Name = "tabPage_TradingSettings";
			this.tabPage_TradingSettings.Padding = new global::System.Windows.Forms.Padding(3);
			this.tabPage_TradingSettings.Size = new global::System.Drawing.Size(747, 397);
			this.tabPage_TradingSettings.TabIndex = 1;
			this.tabPage_TradingSettings.Text = "交易参数";
			this.groupBox4.Controls.Add(this.panel_ClsOrdr);
			this.groupBox4.Location = new global::System.Drawing.Point(384, 271);
			this.groupBox4.Name = "groupBox4";
			this.groupBox4.Size = new global::System.Drawing.Size(340, 99);
			this.groupBox4.TabIndex = 5;
			this.groupBox4.TabStop = false;
			this.groupBox4.Text = "平仓次序";
			this.panel_ClsOrdr.Controls.Add(this.radioBtn_ClsOrd_NewFirst);
			this.panel_ClsOrdr.Controls.Add(this.radioBtn_ClsOrd_OldFirst);
			this.panel_ClsOrdr.Location = new global::System.Drawing.Point(17, 21);
			this.panel_ClsOrdr.Name = "panel_ClsOrdr";
			this.panel_ClsOrdr.Size = new global::System.Drawing.Size(228, 60);
			this.panel_ClsOrdr.TabIndex = 2;
			this.radioBtn_ClsOrd_NewFirst.AutoSize = true;
			this.radioBtn_ClsOrd_NewFirst.Location = new global::System.Drawing.Point(5, 10);
			this.radioBtn_ClsOrd_NewFirst.Name = "radioBtn_ClsOrd_NewFirst";
			this.radioBtn_ClsOrd_NewFirst.Size = new global::System.Drawing.Size(88, 19);
			this.radioBtn_ClsOrd_NewFirst.TabIndex = 0;
			this.radioBtn_ClsOrd_NewFirst.TabStop = true;
			this.radioBtn_ClsOrd_NewFirst.Text = "先平新仓";
			this.radioBtn_ClsOrd_NewFirst.UseVisualStyleBackColor = true;
			this.radioBtn_ClsOrd_OldFirst.AutoSize = true;
			this.radioBtn_ClsOrd_OldFirst.Location = new global::System.Drawing.Point(5, 36);
			this.radioBtn_ClsOrd_OldFirst.Name = "radioBtn_ClsOrd_OldFirst";
			this.radioBtn_ClsOrd_OldFirst.Size = new global::System.Drawing.Size(88, 19);
			this.radioBtn_ClsOrd_OldFirst.TabIndex = 1;
			this.radioBtn_ClsOrd_OldFirst.TabStop = true;
			this.radioBtn_ClsOrd_OldFirst.Text = "先平老仓";
			this.radioBtn_ClsOrd_OldFirst.UseVisualStyleBackColor = true;
			this.groupBox8.Controls.Add(this.label6);
			this.groupBox8.Controls.Add(this.txtBox_ROpenRAmt);
			this.groupBox8.Controls.Add(this.label_ROpenPers);
			this.groupBox8.Controls.Add(this.numUpDown_ROpenRatio);
			this.groupBox8.Controls.Add(this.chkBox_ROpenShowCnfmDlg);
			this.groupBox8.Controls.Add(this.radioBtn_ROpenFixAmt);
			this.groupBox8.Controls.Add(this.radioBtn_ROpenRatio);
			this.groupBox8.Location = new global::System.Drawing.Point(384, 128);
			this.groupBox8.Name = "groupBox8";
			this.groupBox8.Size = new global::System.Drawing.Size(340, 132);
			this.groupBox8.TabIndex = 3;
			this.groupBox8.TabStop = false;
			this.groupBox8.Text = "以损定量";
			this.label6.AutoSize = true;
			this.label6.Location = new global::System.Drawing.Point(253, 63);
			this.label6.Name = "label6";
			this.label6.Size = new global::System.Drawing.Size(22, 15);
			this.label6.TabIndex = 20;
			this.label6.Text = "元";
			this.txtBox_ROpenRAmt.Location = new global::System.Drawing.Point(172, 58);
			this.txtBox_ROpenRAmt.Name = "txtBox_ROpenRAmt";
			this.txtBox_ROpenRAmt.Size = new global::System.Drawing.Size(78, 25);
			this.txtBox_ROpenRAmt.TabIndex = 3;
			this.txtBox_ROpenRAmt.Text = "5000";
			this.label_ROpenPers.AutoSize = true;
			this.label_ROpenPers.Location = new global::System.Drawing.Point(256, 33);
			this.label_ROpenPers.Name = "label_ROpenPers";
			this.label_ROpenPers.Size = new global::System.Drawing.Size(15, 15);
			this.label_ROpenPers.TabIndex = 18;
			this.label_ROpenPers.Text = "%";
			this.numUpDown_ROpenRatio.DecimalPlaces = 1;
			this.numUpDown_ROpenRatio.Increment = new decimal(new int[]
			{
				5,
				0,
				0,
				65536
			});
			this.numUpDown_ROpenRatio.Location = new global::System.Drawing.Point(172, 29);
			this.numUpDown_ROpenRatio.Minimum = new decimal(new int[]
			{
				5,
				0,
				0,
				65536
			});
			this.numUpDown_ROpenRatio.Name = "numUpDown_ROpenRatio";
			this.numUpDown_ROpenRatio.Size = new global::System.Drawing.Size(78, 25);
			this.numUpDown_ROpenRatio.TabIndex = 1;
			this.numUpDown_ROpenRatio.Value = new decimal(new int[]
			{
				5,
				0,
				0,
				65536
			});
			this.chkBox_ROpenShowCnfmDlg.AutoSize = true;
			this.chkBox_ROpenShowCnfmDlg.Location = new global::System.Drawing.Point(22, 92);
			this.chkBox_ROpenShowCnfmDlg.Name = "chkBox_ROpenShowCnfmDlg";
			this.chkBox_ROpenShowCnfmDlg.Size = new global::System.Drawing.Size(179, 19);
			this.chkBox_ROpenShowCnfmDlg.TabIndex = 4;
			this.chkBox_ROpenShowCnfmDlg.Text = "开仓时提示确认止损额";
			this.chkBox_ROpenShowCnfmDlg.UseVisualStyleBackColor = true;
			this.radioBtn_ROpenFixAmt.AutoSize = true;
			this.radioBtn_ROpenFixAmt.Location = new global::System.Drawing.Point(22, 60);
			this.radioBtn_ROpenFixAmt.Name = "radioBtn_ROpenFixAmt";
			this.radioBtn_ROpenFixAmt.Size = new global::System.Drawing.Size(148, 19);
			this.radioBtn_ROpenFixAmt.TabIndex = 2;
			this.radioBtn_ROpenFixAmt.TabStop = true;
			this.radioBtn_ROpenFixAmt.Text = "按固定金额止损：";
			this.radioBtn_ROpenFixAmt.UseVisualStyleBackColor = true;
			this.radioBtn_ROpenRatio.AutoSize = true;
			this.radioBtn_ROpenRatio.Location = new global::System.Drawing.Point(22, 31);
			this.radioBtn_ROpenRatio.Name = "radioBtn_ROpenRatio";
			this.radioBtn_ROpenRatio.Size = new global::System.Drawing.Size(148, 19);
			this.radioBtn_ROpenRatio.TabIndex = 0;
			this.radioBtn_ROpenRatio.TabStop = true;
			this.radioBtn_ROpenRatio.Text = "按权益比例止损：";
			this.radioBtn_ROpenRatio.UseVisualStyleBackColor = true;
			this.groupBox_ClsOdr.Controls.Add(this.chkBox_IfAutoCancelCloseTransOrder);
			this.groupBox_ClsOdr.Controls.Add(this.chkBox_IfAutoCancelOpenTransOrder);
			this.groupBox_ClsOdr.Location = new global::System.Drawing.Point(22, 271);
			this.groupBox_ClsOdr.Name = "groupBox_ClsOdr";
			this.groupBox_ClsOdr.Size = new global::System.Drawing.Size(340, 99);
			this.groupBox_ClsOdr.TabIndex = 4;
			this.groupBox_ClsOdr.TabStop = false;
			this.groupBox_ClsOdr.Text = "自动撤单";
			this.chkBox_IfAutoCancelCloseTransOrder.AutoSize = true;
			this.chkBox_IfAutoCancelCloseTransOrder.Location = new global::System.Drawing.Point(21, 58);
			this.chkBox_IfAutoCancelCloseTransOrder.Name = "chkBox_IfAutoCancelCloseTransOrder";
			this.chkBox_IfAutoCancelCloseTransOrder.Size = new global::System.Drawing.Size(254, 19);
			this.chkBox_IfAutoCancelCloseTransOrder.TabIndex = 4;
			this.chkBox_IfAutoCancelCloseTransOrder.Text = "平仓自动根据需要撤除同向委托单";
			this.chkBox_IfAutoCancelCloseTransOrder.UseVisualStyleBackColor = true;
			this.chkBox_IfAutoCancelOpenTransOrder.AutoSize = true;
			this.chkBox_IfAutoCancelOpenTransOrder.Location = new global::System.Drawing.Point(21, 30);
			this.chkBox_IfAutoCancelOpenTransOrder.Name = "chkBox_IfAutoCancelOpenTransOrder";
			this.chkBox_IfAutoCancelOpenTransOrder.Size = new global::System.Drawing.Size(254, 19);
			this.chkBox_IfAutoCancelOpenTransOrder.TabIndex = 0;
			this.chkBox_IfAutoCancelOpenTransOrder.Text = "开仓余额不足自动撤除同向委托单";
			this.chkBox_IfAutoCancelOpenTransOrder.UseVisualStyleBackColor = true;
			this.groupBox7.Controls.Add(this.groupBox11);
			this.groupBox7.Controls.Add(this.comboBox_RationedShareTreatmt);
			this.groupBox7.Controls.Add(this.comboBox_DividentTreatment);
			this.groupBox7.Controls.Add(this.label4);
			this.groupBox7.Controls.Add(this.label3);
			this.groupBox7.Controls.Add(this.comboBox_BonusShareTreatment);
			this.groupBox7.Controls.Add(this.label2);
			this.groupBox7.Controls.Add(this.comboBox_StkRstMethod);
			this.groupBox7.Controls.Add(this.label1);
			this.groupBox7.Controls.Add(this.chkBox_T0ForStock);
			this.groupBox7.Controls.Add(this.chkBox_ShortForStock);
			this.groupBox7.Location = new global::System.Drawing.Point(22, 16);
			this.groupBox7.Name = "groupBox7";
			this.groupBox7.Size = new global::System.Drawing.Size(340, 244);
			this.groupBox7.TabIndex = 1;
			this.groupBox7.TabStop = false;
			this.groupBox7.Text = "股票设置";
			this.groupBox11.Location = new global::System.Drawing.Point(24, 157);
			this.groupBox11.Name = "groupBox11";
			this.groupBox11.Size = new global::System.Drawing.Size(215, 2);
			this.groupBox11.TabIndex = 18;
			this.groupBox11.TabStop = false;
			this.comboBox_RationedShareTreatmt.DropDownStyle = global::System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_RationedShareTreatmt.FormattingEnabled = true;
			this.comboBox_RationedShareTreatmt.Items.AddRange(new object[]
			{
				"自动配股",
				"提示配股",
				"不配股"
			});
			this.comboBox_RationedShareTreatmt.Location = new global::System.Drawing.Point(135, 88);
			this.comboBox_RationedShareTreatmt.Name = "comboBox_RationedShareTreatmt";
			this.comboBox_RationedShareTreatmt.Size = new global::System.Drawing.Size(104, 23);
			this.comboBox_RationedShareTreatmt.TabIndex = 5;
			this.comboBox_DividentTreatment.DropDownStyle = global::System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_DividentTreatment.FormattingEnabled = true;
			this.comboBox_DividentTreatment.Items.AddRange(new object[]
			{
				"自动派息",
				"不派息"
			});
			this.comboBox_DividentTreatment.Location = new global::System.Drawing.Point(135, 119);
			this.comboBox_DividentTreatment.Name = "comboBox_DividentTreatment";
			this.comboBox_DividentTreatment.Size = new global::System.Drawing.Size(104, 23);
			this.comboBox_DividentTreatment.TabIndex = 7;
			this.label4.AutoSize = true;
			this.label4.Location = new global::System.Drawing.Point(20, 92);
			this.label4.Name = "label4";
			this.label4.Size = new global::System.Drawing.Size(112, 15);
			this.label4.TabIndex = 4;
			this.label4.Text = "配股处理方式：";
			this.label3.AutoSize = true;
			this.label3.Location = new global::System.Drawing.Point(20, 123);
			this.label3.Name = "label3";
			this.label3.Size = new global::System.Drawing.Size(112, 15);
			this.label3.TabIndex = 6;
			this.label3.Text = "派息处理方式：";
			this.comboBox_BonusShareTreatment.DropDownStyle = global::System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_BonusShareTreatment.FormattingEnabled = true;
			this.comboBox_BonusShareTreatment.Items.AddRange(new object[]
			{
				"自动送股",
				"不送股"
			});
			this.comboBox_BonusShareTreatment.Location = new global::System.Drawing.Point(135, 57);
			this.comboBox_BonusShareTreatment.Name = "comboBox_BonusShareTreatment";
			this.comboBox_BonusShareTreatment.Size = new global::System.Drawing.Size(104, 23);
			this.comboBox_BonusShareTreatment.TabIndex = 3;
			this.label2.AutoSize = true;
			this.label2.Location = new global::System.Drawing.Point(20, 61);
			this.label2.Name = "label2";
			this.label2.Size = new global::System.Drawing.Size(112, 15);
			this.label2.TabIndex = 2;
			this.label2.Text = "送股处理方式：";
			this.comboBox_StkRstMethod.DropDownStyle = global::System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_StkRstMethod.FormattingEnabled = true;
			this.comboBox_StkRstMethod.Items.AddRange(new object[]
			{
				"前复权",
				"后复权",
				"不复权"
			});
			this.comboBox_StkRstMethod.Location = new global::System.Drawing.Point(135, 26);
			this.comboBox_StkRstMethod.Name = "comboBox_StkRstMethod";
			this.comboBox_StkRstMethod.Size = new global::System.Drawing.Size(104, 23);
			this.comboBox_StkRstMethod.TabIndex = 1;
			this.label1.AutoSize = true;
			this.label1.Location = new global::System.Drawing.Point(20, 30);
			this.label1.Name = "label1";
			this.label1.Size = new global::System.Drawing.Size(112, 15);
			this.label1.TabIndex = 0;
			this.label1.Text = "复权处理方式：";
			this.chkBox_T0ForStock.AutoSize = true;
			this.chkBox_T0ForStock.Location = new global::System.Drawing.Point(24, 202);
			this.chkBox_T0ForStock.Name = "chkBox_T0ForStock";
			this.chkBox_T0ForStock.Size = new global::System.Drawing.Size(203, 19);
			this.chkBox_T0ForStock.TabIndex = 9;
			this.chkBox_T0ForStock.Text = "股票允许T+0（日内平仓）";
			this.chkBox_T0ForStock.UseVisualStyleBackColor = true;
			this.chkBox_ShortForStock.AutoSize = true;
			this.chkBox_ShortForStock.Location = new global::System.Drawing.Point(24, 174);
			this.chkBox_ShortForStock.Name = "chkBox_ShortForStock";
			this.chkBox_ShortForStock.Size = new global::System.Drawing.Size(179, 19);
			this.chkBox_ShortForStock.TabIndex = 8;
			this.chkBox_ShortForStock.Text = "股票允许卖开（融券）";
			this.chkBox_ShortForStock.UseVisualStyleBackColor = true;
			this.groupBox_自动盈损.Controls.Add(this.linkLabel_AutoPrftPt);
			this.groupBox_自动盈损.Controls.Add(this.linkLabel_AutoStopPt);
			this.groupBox_自动盈损.Controls.Add(this.chkBox_AutoPrft);
			this.groupBox_自动盈损.Controls.Add(this.chkBox_AutoStop);
			this.groupBox_自动盈损.Location = new global::System.Drawing.Point(384, 16);
			this.groupBox_自动盈损.Name = "groupBox_自动盈损";
			this.groupBox_自动盈损.Size = new global::System.Drawing.Size(340, 100);
			this.groupBox_自动盈损.TabIndex = 2;
			this.groupBox_自动盈损.TabStop = false;
			this.groupBox_自动盈损.Text = "自动盈损";
			this.linkLabel_AutoPrftPt.LinkArea = new global::System.Windows.Forms.LinkArea(4, 3);
			this.linkLabel_AutoPrftPt.Location = new global::System.Drawing.Point(116, 61);
			this.linkLabel_AutoPrftPt.Name = "linkLabel_AutoPrftPt";
			this.linkLabel_AutoPrftPt.Size = new global::System.Drawing.Size(152, 22);
			this.linkLabel_AutoPrftPt.TabIndex = 3;
			this.linkLabel_AutoPrftPt.TabStop = true;
			this.linkLabel_AutoPrftPt.Text = "（点数：未设置）";
			this.linkLabel_AutoPrftPt.UseCompatibleTextRendering = true;
			this.linkLabel_AutoStopPt.LinkArea = new global::System.Windows.Forms.LinkArea(4, 3);
			this.linkLabel_AutoStopPt.Location = new global::System.Drawing.Point(116, 35);
			this.linkLabel_AutoStopPt.Name = "linkLabel_AutoStopPt";
			this.linkLabel_AutoStopPt.Size = new global::System.Drawing.Size(152, 23);
			this.linkLabel_AutoStopPt.TabIndex = 2;
			this.linkLabel_AutoStopPt.TabStop = true;
			this.linkLabel_AutoStopPt.Text = "（点数：未设置）";
			this.linkLabel_AutoStopPt.UseCompatibleTextRendering = true;
			this.chkBox_AutoPrft.AutoSize = true;
			this.chkBox_AutoPrft.Location = new global::System.Drawing.Point(22, 60);
			this.chkBox_AutoPrft.Name = "chkBox_AutoPrft";
			this.chkBox_AutoPrft.Size = new global::System.Drawing.Size(89, 19);
			this.chkBox_AutoPrft.TabIndex = 1;
			this.chkBox_AutoPrft.Text = "自动止盈";
			this.chkBox_AutoPrft.UseVisualStyleBackColor = true;
			this.chkBox_AutoStop.AutoSize = true;
			this.chkBox_AutoStop.Location = new global::System.Drawing.Point(22, 33);
			this.chkBox_AutoStop.Name = "chkBox_AutoStop";
			this.chkBox_AutoStop.Size = new global::System.Drawing.Size(89, 19);
			this.chkBox_AutoStop.TabIndex = 0;
			this.chkBox_AutoStop.Text = "自动止损";
			this.chkBox_AutoStop.UseVisualStyleBackColor = true;
			this.tabPage_BackupSettings.BackColor = global::System.Drawing.SystemColors.Control;
			this.tabPage_BackupSettings.Controls.Add(this.label_bkSyncNote);
			this.tabPage_BackupSettings.Controls.Add(this.btn_BkupToSrv);
			this.tabPage_BackupSettings.Controls.Add(this.btn_RestoreBkup);
			this.tabPage_BackupSettings.Controls.Add(this.picBox_Bulb);
			this.tabPage_BackupSettings.Controls.Add(this.groupBox_SyncDirection);
			this.tabPage_BackupSettings.Controls.Add(this.groupBox_SyncTime);
			this.tabPage_BackupSettings.Controls.Add(this.groupBox_SyncItems);
			this.tabPage_BackupSettings.Location = new global::System.Drawing.Point(4, 25);
			this.tabPage_BackupSettings.Name = "tabPage_BackupSettings";
			this.tabPage_BackupSettings.Padding = new global::System.Windows.Forms.Padding(3);
			this.tabPage_BackupSettings.Size = new global::System.Drawing.Size(747, 397);
			this.tabPage_BackupSettings.TabIndex = 2;
			this.tabPage_BackupSettings.Text = "备份同步";
			this.label_bkSyncNote.Location = new global::System.Drawing.Point(61, 24);
			this.label_bkSyncNote.Name = "label_bkSyncNote";
			this.label_bkSyncNote.Size = new global::System.Drawing.Size(522, 44);
			this.label_bkSyncNote.TabIndex = 25;
			this.label_bkSyncNote.Text = "云备份同步可将本地数据储存至云端，并可从云端恢复。重装系统时交易数据和参数等不会丢失，且可实现在两台机器上自动同步。";
			this.btn_BkupToSrv.Location = new global::System.Drawing.Point(164, 302);
			this.btn_BkupToSrv.Name = "btn_BkupToSrv";
			this.btn_BkupToSrv.Size = new global::System.Drawing.Size(127, 32);
			this.btn_BkupToSrv.TabIndex = 24;
			this.btn_BkupToSrv.Text = "备份至云端";
			this.btn_BkupToSrv.UseVisualStyleBackColor = true;
			this.btn_RestoreBkup.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.btn_RestoreBkup.Location = new global::System.Drawing.Point(26, 302);
			this.btn_RestoreBkup.Name = "btn_RestoreBkup";
			this.btn_RestoreBkup.Size = new global::System.Drawing.Size(127, 32);
			this.btn_RestoreBkup.TabIndex = 23;
			this.btn_RestoreBkup.Text = "从云端恢复";
			this.btn_RestoreBkup.UseVisualStyleBackColor = true;
			this.picBox_Bulb.BackgroundImageLayout = global::System.Windows.Forms.ImageLayout.None;
			this.picBox_Bulb.Image = global::ns28.Class372._1683_Lightbulb_32x32;
			this.picBox_Bulb.Location = new global::System.Drawing.Point(39, 22);
			this.picBox_Bulb.Name = "picBox_Bulb";
			this.picBox_Bulb.Size = new global::System.Drawing.Size(20, 20);
			this.picBox_Bulb.SizeMode = global::System.Windows.Forms.PictureBoxSizeMode.StretchImage;
			this.picBox_Bulb.TabIndex = 22;
			this.picBox_Bulb.TabStop = false;
			this.groupBox_SyncDirection.Controls.Add(this.comboBox_SyncConflictTreatmt);
			this.groupBox_SyncDirection.Controls.Add(this.label5);
			this.groupBox_SyncDirection.Controls.Add(this.comboBox_OverwritingLocalBkupFiles);
			this.groupBox_SyncDirection.Controls.Add(this.radioBtn_BackupDirectionOnlyToSrv);
			this.groupBox_SyncDirection.Controls.Add(this.label_同步本地提示);
			this.groupBox_SyncDirection.Controls.Add(this.radioBtn_BackupDualDirection);
			this.groupBox_SyncDirection.Location = new global::System.Drawing.Point(314, 206);
			this.groupBox_SyncDirection.Name = "groupBox_SyncDirection";
			this.groupBox_SyncDirection.Size = new global::System.Drawing.Size(274, 142);
			this.groupBox_SyncDirection.TabIndex = 16;
			this.groupBox_SyncDirection.TabStop = false;
			this.groupBox_SyncDirection.Text = "自动同步方向";
			this.comboBox_SyncConflictTreatmt.DropDownStyle = global::System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_SyncConflictTreatmt.FormattingEnabled = true;
			this.comboBox_SyncConflictTreatmt.Items.AddRange(new object[]
			{
				"提示确认",
				"不提示",
				"取消备份"
			});
			this.comboBox_SyncConflictTreatmt.Location = new global::System.Drawing.Point(156, 105);
			this.comboBox_SyncConflictTreatmt.Name = "comboBox_SyncConflictTreatmt";
			this.comboBox_SyncConflictTreatmt.Size = new global::System.Drawing.Size(96, 23);
			this.comboBox_SyncConflictTreatmt.TabIndex = 21;
			this.label5.AutoSize = true;
			this.label5.Location = new global::System.Drawing.Point(35, 109);
			this.label5.Name = "label5";
			this.label5.Size = new global::System.Drawing.Size(112, 15);
			this.label5.TabIndex = 22;
			this.label5.Text = "云端数据较新时";
			this.comboBox_OverwritingLocalBkupFiles.DropDownStyle = global::System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_OverwritingLocalBkupFiles.FormattingEnabled = true;
			this.comboBox_OverwritingLocalBkupFiles.Items.AddRange(new object[]
			{
				"提示确认",
				"不提示"
			});
			this.comboBox_OverwritingLocalBkupFiles.Location = new global::System.Drawing.Point(156, 48);
			this.comboBox_OverwritingLocalBkupFiles.Name = "comboBox_OverwritingLocalBkupFiles";
			this.comboBox_OverwritingLocalBkupFiles.Size = new global::System.Drawing.Size(96, 23);
			this.comboBox_OverwritingLocalBkupFiles.TabIndex = 19;
			this.radioBtn_BackupDirectionOnlyToSrv.AutoSize = true;
			this.radioBtn_BackupDirectionOnlyToSrv.Location = new global::System.Drawing.Point(16, 83);
			this.radioBtn_BackupDirectionOnlyToSrv.Name = "radioBtn_BackupDirectionOnlyToSrv";
			this.radioBtn_BackupDirectionOnlyToSrv.Size = new global::System.Drawing.Size(163, 19);
			this.radioBtn_BackupDirectionOnlyToSrv.TabIndex = 1;
			this.radioBtn_BackupDirectionOnlyToSrv.TabStop = true;
			this.radioBtn_BackupDirectionOnlyToSrv.Text = "仅从本地备份至云端";
			this.radioBtn_BackupDirectionOnlyToSrv.UseVisualStyleBackColor = true;
			this.label_同步本地提示.AutoSize = true;
			this.label_同步本地提示.Location = new global::System.Drawing.Point(35, 52);
			this.label_同步本地提示.Name = "label_同步本地提示";
			this.label_同步本地提示.Size = new global::System.Drawing.Size(112, 15);
			this.label_同步本地提示.TabIndex = 20;
			this.label_同步本地提示.Text = "覆盖本地文件前";
			this.radioBtn_BackupDualDirection.AutoSize = true;
			this.radioBtn_BackupDualDirection.Location = new global::System.Drawing.Point(16, 26);
			this.radioBtn_BackupDualDirection.Name = "radioBtn_BackupDualDirection";
			this.radioBtn_BackupDualDirection.Size = new global::System.Drawing.Size(163, 19);
			this.radioBtn_BackupDualDirection.TabIndex = 0;
			this.radioBtn_BackupDualDirection.TabStop = true;
			this.radioBtn_BackupDualDirection.Text = "本地与云端双向同步";
			this.radioBtn_BackupDualDirection.UseVisualStyleBackColor = true;
			this.groupBox_SyncTime.Controls.Add(this.comboBox_BackupPeriod);
			this.groupBox_SyncTime.Controls.Add(this.chkBox_BackupTimePeriodically);
			this.groupBox_SyncTime.Controls.Add(this.chkBox_BackupTimeExit);
			this.groupBox_SyncTime.Controls.Add(this.chkBox_BackupTimeStartup);
			this.groupBox_SyncTime.Location = new global::System.Drawing.Point(314, 78);
			this.groupBox_SyncTime.Name = "groupBox_SyncTime";
			this.groupBox_SyncTime.Size = new global::System.Drawing.Size(274, 115);
			this.groupBox_SyncTime.TabIndex = 3;
			this.groupBox_SyncTime.TabStop = false;
			this.groupBox_SyncTime.Text = "自动同步时间";
			this.comboBox_BackupPeriod.DropDownStyle = global::System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_BackupPeriod.FormattingEnabled = true;
			this.comboBox_BackupPeriod.Items.AddRange(new object[]
			{
				"30分钟",
				"1小时",
				"2小时"
			});
			this.comboBox_BackupPeriod.Location = new global::System.Drawing.Point(156, 79);
			this.comboBox_BackupPeriod.Name = "comboBox_BackupPeriod";
			this.comboBox_BackupPeriod.Size = new global::System.Drawing.Size(96, 23);
			this.comboBox_BackupPeriod.TabIndex = 19;
			this.chkBox_BackupTimePeriodically.AutoSize = true;
			this.chkBox_BackupTimePeriodically.Location = new global::System.Drawing.Point(16, 81);
			this.chkBox_BackupTimePeriodically.Name = "chkBox_BackupTimePeriodically";
			this.chkBox_BackupTimePeriodically.Size = new global::System.Drawing.Size(134, 19);
			this.chkBox_BackupTimePeriodically.TabIndex = 17;
			this.chkBox_BackupTimePeriodically.Text = "程序运行时每隔";
			this.chkBox_BackupTimePeriodically.UseVisualStyleBackColor = true;
			this.chkBox_BackupTimeExit.AutoSize = true;
			this.chkBox_BackupTimeExit.Location = new global::System.Drawing.Point(16, 54);
			this.chkBox_BackupTimeExit.Name = "chkBox_BackupTimeExit";
			this.chkBox_BackupTimeExit.Size = new global::System.Drawing.Size(104, 19);
			this.chkBox_BackupTimeExit.TabIndex = 16;
			this.chkBox_BackupTimeExit.Text = "程序退出时";
			this.chkBox_BackupTimeExit.UseVisualStyleBackColor = true;
			this.chkBox_BackupTimeStartup.AutoSize = true;
			this.chkBox_BackupTimeStartup.Location = new global::System.Drawing.Point(16, 27);
			this.chkBox_BackupTimeStartup.Name = "chkBox_BackupTimeStartup";
			this.chkBox_BackupTimeStartup.Size = new global::System.Drawing.Size(104, 19);
			this.chkBox_BackupTimeStartup.TabIndex = 15;
			this.chkBox_BackupTimeStartup.Text = "程序启动时";
			this.chkBox_BackupTimeStartup.UseVisualStyleBackColor = true;
			this.groupBox_SyncItems.Controls.Add(this.chkBox_BackupSymbParams);
			this.groupBox_SyncItems.Controls.Add(this.chkBox_BackupDrwObj);
			this.groupBox_SyncItems.Controls.Add(this.chkBox_BackupZixuan);
			this.groupBox_SyncItems.Controls.Add(this.chkBox_BackupPages);
			this.groupBox_SyncItems.Controls.Add(this.chkBox_BackupUISettings);
			this.groupBox_SyncItems.Controls.Add(this.chkBox_BackupTrans);
			this.groupBox_SyncItems.Location = new global::System.Drawing.Point(21, 78);
			this.groupBox_SyncItems.Name = "groupBox_SyncItems";
			this.groupBox_SyncItems.Size = new global::System.Drawing.Size(275, 206);
			this.groupBox_SyncItems.TabIndex = 2;
			this.groupBox_SyncItems.TabStop = false;
			this.groupBox_SyncItems.Text = "备份内容";
			this.chkBox_BackupSymbParams.AutoSize = true;
			this.chkBox_BackupSymbParams.Location = new global::System.Drawing.Point(16, 84);
			this.chkBox_BackupSymbParams.Name = "chkBox_BackupSymbParams";
			this.chkBox_BackupSymbParams.Size = new global::System.Drawing.Size(89, 19);
			this.chkBox_BackupSymbParams.TabIndex = 20;
			this.chkBox_BackupSymbParams.Text = "品种设置";
			this.chkBox_BackupSymbParams.UseVisualStyleBackColor = true;
			this.chkBox_BackupDrwObj.AutoSize = true;
			this.chkBox_BackupDrwObj.Location = new global::System.Drawing.Point(16, 165);
			this.chkBox_BackupDrwObj.Name = "chkBox_BackupDrwObj";
			this.chkBox_BackupDrwObj.Size = new global::System.Drawing.Size(59, 19);
			this.chkBox_BackupDrwObj.TabIndex = 19;
			this.chkBox_BackupDrwObj.Text = "画线";
			this.chkBox_BackupDrwObj.UseVisualStyleBackColor = true;
			this.chkBox_BackupZixuan.AutoSize = true;
			this.chkBox_BackupZixuan.Location = new global::System.Drawing.Point(16, 138);
			this.chkBox_BackupZixuan.Name = "chkBox_BackupZixuan";
			this.chkBox_BackupZixuan.Size = new global::System.Drawing.Size(119, 19);
			this.chkBox_BackupZixuan.TabIndex = 18;
			this.chkBox_BackupZixuan.Text = "自选品种列表";
			this.chkBox_BackupZixuan.UseVisualStyleBackColor = true;
			this.chkBox_BackupPages.AutoSize = true;
			this.chkBox_BackupPages.Location = new global::System.Drawing.Point(16, 111);
			this.chkBox_BackupPages.Name = "chkBox_BackupPages";
			this.chkBox_BackupPages.Size = new global::System.Drawing.Size(89, 19);
			this.chkBox_BackupPages.TabIndex = 16;
			this.chkBox_BackupPages.Text = "页面设置";
			this.chkBox_BackupPages.UseVisualStyleBackColor = true;
			this.chkBox_BackupUISettings.AutoSize = true;
			this.chkBox_BackupUISettings.Location = new global::System.Drawing.Point(16, 57);
			this.chkBox_BackupUISettings.Name = "chkBox_BackupUISettings";
			this.chkBox_BackupUISettings.Size = new global::System.Drawing.Size(134, 19);
			this.chkBox_BackupUISettings.TabIndex = 15;
			this.chkBox_BackupUISettings.Text = "界面及交易参数";
			this.chkBox_BackupUISettings.UseVisualStyleBackColor = true;
			this.chkBox_BackupTrans.AutoSize = true;
			this.chkBox_BackupTrans.Location = new global::System.Drawing.Point(16, 30);
			this.chkBox_BackupTrans.Name = "chkBox_BackupTrans";
			this.chkBox_BackupTrans.Size = new global::System.Drawing.Size(134, 19);
			this.chkBox_BackupTrans.TabIndex = 14;
			this.chkBox_BackupTrans.Text = "账户及交易记录";
			this.chkBox_BackupTrans.UseVisualStyleBackColor = true;
			base.AcceptButton = this.button_OK;
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(8f, 15f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			this.BackColor = global::System.Drawing.SystemColors.Control;
			base.CancelButton = this.button_Cancel;
			base.ClientSize = new global::System.Drawing.Size(779, 497);
			base.Controls.Add(this.tabControl_Settings);
			base.Controls.Add(this.button_Cancel);
			base.Controls.Add(this.button_OK);
			this.DoubleBuffered = true;
			base.FormBorderStyle = global::System.Windows.Forms.FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = "SettingsForm";
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.StartPosition = global::System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "系统参数";
			base.Load += new global::System.EventHandler(this.SettingsForm_Load);
			this.groupBox1.ResumeLayout(false);
			this.groupBox1.PerformLayout();
			this.groupBox5.ResumeLayout(false);
			this.groupBox5.PerformLayout();
			this.tabControl_Settings.ResumeLayout(false);
			this.tabPage_ChtSetting.ResumeLayout(false);
			this.groupBox10.ResumeLayout(false);
			this.groupBox10.PerformLayout();
			((global::System.ComponentModel.ISupportInitialize)this.picBox_theme).EndInit();
			this.groupBox9.ResumeLayout(false);
			this.groupBox9.PerformLayout();
			this.groupBox3.ResumeLayout(false);
			this.groupBox3.PerformLayout();
			((global::System.ComponentModel.ISupportInitialize)this.picBox_ArrwType).EndInit();
			this.panel1.ResumeLayout(false);
			this.panel1.PerformLayout();
			this.tabPage_UISettings.ResumeLayout(false);
			this.groupBox14.ResumeLayout(false);
			this.groupBox14.PerformLayout();
			this.panel_SymbSwitchTime.ResumeLayout(false);
			this.panel_SymbSwitchTime.PerformLayout();
			this.groupBox12.ResumeLayout(false);
			this.groupBox12.PerformLayout();
			this.panel2.ResumeLayout(false);
			this.panel2.PerformLayout();
			this.groupBox6.ResumeLayout(false);
			this.groupBox6.PerformLayout();
			this.tabPage_TradingSettings.ResumeLayout(false);
			this.groupBox4.ResumeLayout(false);
			this.panel_ClsOrdr.ResumeLayout(false);
			this.panel_ClsOrdr.PerformLayout();
			this.groupBox8.ResumeLayout(false);
			this.groupBox8.PerformLayout();
			((global::System.ComponentModel.ISupportInitialize)this.numUpDown_ROpenRatio).EndInit();
			this.groupBox_ClsOdr.ResumeLayout(false);
			this.groupBox_ClsOdr.PerformLayout();
			this.groupBox7.ResumeLayout(false);
			this.groupBox7.PerformLayout();
			this.groupBox_自动盈损.ResumeLayout(false);
			this.groupBox_自动盈损.PerformLayout();
			this.tabPage_BackupSettings.ResumeLayout(false);
			((global::System.ComponentModel.ISupportInitialize)this.picBox_Bulb).EndInit();
			this.groupBox_SyncDirection.ResumeLayout(false);
			this.groupBox_SyncDirection.PerformLayout();
			this.groupBox_SyncTime.ResumeLayout(false);
			this.groupBox_SyncTime.PerformLayout();
			this.groupBox_SyncItems.ResumeLayout(false);
			this.groupBox_SyncItems.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x04000839 RID: 2105
		private global::System.Windows.Forms.Button button_Cancel;

		// Token: 0x0400083A RID: 2106
		private global::System.Windows.Forms.Button button_OK;

		// Token: 0x0400083B RID: 2107
		private global::System.Windows.Forms.CheckBox chkBx_saveSpeed;

		// Token: 0x0400083C RID: 2108
		private global::System.Windows.Forms.CheckBox chkBx_saveWindow;

		// Token: 0x0400083D RID: 2109
		private global::System.Windows.Forms.CheckBox chkBx_savePage;

		// Token: 0x0400083E RID: 2110
		private global::System.Windows.Forms.CheckBox chkBox_pauseAtDayEnd;

		// Token: 0x0400083F RID: 2111
		private global::System.Windows.Forms.CheckBox chkBx_confQuit;

		// Token: 0x04000840 RID: 2112
		private global::System.Windows.Forms.GroupBox groupBox1;

		// Token: 0x04000841 RID: 2113
		private global::System.Windows.Forms.GroupBox groupBox5;

		// Token: 0x04000842 RID: 2114
		private global::System.Windows.Forms.CheckBox checkBox_AutoShowCurrTransTab;

		// Token: 0x04000843 RID: 2115
		private global::System.Windows.Forms.TabControl tabControl_Settings;

		// Token: 0x04000844 RID: 2116
		private global::System.Windows.Forms.TabPage tabPage_UISettings;

		// Token: 0x04000845 RID: 2117
		private global::System.Windows.Forms.TabPage tabPage_TradingSettings;

		// Token: 0x04000846 RID: 2118
		private global::System.Windows.Forms.GroupBox groupBox_自动盈损;

		// Token: 0x04000847 RID: 2119
		private global::System.Windows.Forms.CheckBox chkBox_AutoPrft;

		// Token: 0x04000848 RID: 2120
		private global::System.Windows.Forms.CheckBox chkBox_AutoStop;

		// Token: 0x04000849 RID: 2121
		private global::System.Windows.Forms.GroupBox groupBox7;

		// Token: 0x0400084A RID: 2122
		private global::System.Windows.Forms.CheckBox chkBox_ShortForStock;

		// Token: 0x0400084B RID: 2123
		private global::System.Windows.Forms.CheckBox chkBox_T0ForStock;

		// Token: 0x0400084C RID: 2124
		private global::System.Windows.Forms.GroupBox groupBox_ClsOdr;

		// Token: 0x0400084D RID: 2125
		private global::System.Windows.Forms.RadioButton radioBtn_ClsOrd_OldFirst;

		// Token: 0x0400084E RID: 2126
		private global::System.Windows.Forms.RadioButton radioBtn_ClsOrd_NewFirst;

		// Token: 0x0400084F RID: 2127
		private global::System.Windows.Forms.Label label1;

		// Token: 0x04000850 RID: 2128
		private global::System.Windows.Forms.ComboBox comboBox_StkRstMethod;

		// Token: 0x04000851 RID: 2129
		private global::System.Windows.Forms.ComboBox comboBox_BonusShareTreatment;

		// Token: 0x04000852 RID: 2130
		private global::System.Windows.Forms.Label label2;

		// Token: 0x04000853 RID: 2131
		private global::System.Windows.Forms.ComboBox comboBox_DividentTreatment;

		// Token: 0x04000854 RID: 2132
		private global::System.Windows.Forms.Label label3;

		// Token: 0x04000855 RID: 2133
		private global::System.Windows.Forms.ComboBox comboBox_RationedShareTreatmt;

		// Token: 0x04000856 RID: 2134
		private global::System.Windows.Forms.Label label4;

		// Token: 0x04000857 RID: 2135
		private global::System.Windows.Forms.LinkLabel linkLabel_AutoStopPt;

		// Token: 0x04000858 RID: 2136
		private global::System.Windows.Forms.LinkLabel linkLabel_AutoPrftPt;

		// Token: 0x04000859 RID: 2137
		private global::System.Windows.Forms.GroupBox groupBox8;

		// Token: 0x0400085A RID: 2138
		private global::System.Windows.Forms.TabPage tabPage_BackupSettings;

		// Token: 0x0400085B RID: 2139
		private global::System.Windows.Forms.GroupBox groupBox_SyncItems;

		// Token: 0x0400085C RID: 2140
		private global::System.Windows.Forms.CheckBox chkBox_BackupDrwObj;

		// Token: 0x0400085D RID: 2141
		private global::System.Windows.Forms.CheckBox chkBox_BackupZixuan;

		// Token: 0x0400085E RID: 2142
		private global::System.Windows.Forms.CheckBox chkBox_BackupPages;

		// Token: 0x0400085F RID: 2143
		private global::System.Windows.Forms.CheckBox chkBox_BackupUISettings;

		// Token: 0x04000860 RID: 2144
		private global::System.Windows.Forms.CheckBox chkBox_BackupTrans;

		// Token: 0x04000861 RID: 2145
		private global::System.Windows.Forms.GroupBox groupBox_SyncTime;

		// Token: 0x04000862 RID: 2146
		private global::System.Windows.Forms.CheckBox chkBox_BackupTimePeriodically;

		// Token: 0x04000863 RID: 2147
		private global::System.Windows.Forms.CheckBox chkBox_BackupTimeExit;

		// Token: 0x04000864 RID: 2148
		private global::System.Windows.Forms.CheckBox chkBox_BackupTimeStartup;

		// Token: 0x04000865 RID: 2149
		private global::System.Windows.Forms.ComboBox comboBox_BackupPeriod;

		// Token: 0x04000866 RID: 2150
		private global::System.Windows.Forms.ComboBox comboBox_OverwritingLocalBkupFiles;

		// Token: 0x04000867 RID: 2151
		private global::System.Windows.Forms.GroupBox groupBox_SyncDirection;

		// Token: 0x04000868 RID: 2152
		private global::System.Windows.Forms.RadioButton radioBtn_BackupDualDirection;

		// Token: 0x04000869 RID: 2153
		private global::System.Windows.Forms.RadioButton radioBtn_BackupDirectionOnlyToSrv;

		// Token: 0x0400086A RID: 2154
		private global::System.Windows.Forms.Label label_同步本地提示;

		// Token: 0x0400086B RID: 2155
		private global::System.Windows.Forms.PictureBox picBox_Bulb;

		// Token: 0x0400086C RID: 2156
		private global::System.Windows.Forms.CheckBox chkBox_BackupSymbParams;

		// Token: 0x0400086D RID: 2157
		private global::System.Windows.Forms.Button btn_RestoreBkup;

		// Token: 0x0400086E RID: 2158
		private global::System.Windows.Forms.Button btn_BkupToSrv;

		// Token: 0x0400086F RID: 2159
		private global::System.Windows.Forms.ComboBox comboBox_SyncConflictTreatmt;

		// Token: 0x04000870 RID: 2160
		private global::System.Windows.Forms.Label label5;

		// Token: 0x04000871 RID: 2161
		private global::System.Windows.Forms.Label label_bkSyncNote;

		// Token: 0x04000872 RID: 2162
		private global::System.Windows.Forms.CheckBox chkBox_IfFollowPrcInTradingInput;

		// Token: 0x04000873 RID: 2163
		private global::System.Windows.Forms.CheckBox chkBox_SyncToolbarTradingTabPriceUnits;

		// Token: 0x04000874 RID: 2164
		private global::System.Windows.Forms.RadioButton radioBtn_ROpenFixAmt;

		// Token: 0x04000875 RID: 2165
		private global::System.Windows.Forms.RadioButton radioBtn_ROpenRatio;

		// Token: 0x04000876 RID: 2166
		private global::System.Windows.Forms.CheckBox chkBox_ROpenShowCnfmDlg;

		// Token: 0x04000877 RID: 2167
		private global::System.Windows.Forms.Label label_ROpenPers;

		// Token: 0x04000878 RID: 2168
		private global::System.Windows.Forms.NumericUpDown numUpDown_ROpenRatio;

		// Token: 0x04000879 RID: 2169
		private global::System.Windows.Forms.TextBox txtBox_ROpenRAmt;

		// Token: 0x0400087A RID: 2170
		private global::System.Windows.Forms.Label label6;

		// Token: 0x0400087B RID: 2171
		private global::System.Windows.Forms.CheckBox chkBox_IfAutoCancelOpenTransOrder;

		// Token: 0x0400087C RID: 2172
		private global::System.Windows.Forms.Panel panel_ClsOrdr;

		// Token: 0x0400087D RID: 2173
		private global::System.Windows.Forms.TabPage tabPage_ChtSetting;

		// Token: 0x0400087E RID: 2174
		private global::System.Windows.Forms.GroupBox groupBox3;

		// Token: 0x0400087F RID: 2175
		private global::System.Windows.Forms.GroupBox groupBox2;

		// Token: 0x04000880 RID: 2176
		private global::System.Windows.Forms.PictureBox picBox_ArrwType;

		// Token: 0x04000881 RID: 2177
		private global::System.Windows.Forms.ComboBox comboBox_arrwType;

		// Token: 0x04000882 RID: 2178
		private global::System.Windows.Forms.GroupBox groupBox_TransNote;

		// Token: 0x04000883 RID: 2179
		private global::System.Windows.Forms.Label label7;

		// Token: 0x04000884 RID: 2180
		private global::System.Windows.Forms.CheckBox checkBox_TransNotesFillTransparant;

		// Token: 0x04000885 RID: 2181
		private global::System.Windows.Forms.Label label_dispPeriodOfTransArw;

		// Token: 0x04000886 RID: 2182
		private global::System.Windows.Forms.CheckBox checkBox_OnlyShowUserNoteBox;

		// Token: 0x04000887 RID: 2183
		private global::System.Windows.Forms.ComboBox cmbBx_PeriodForTransArrow;

		// Token: 0x04000888 RID: 2184
		private global::System.Windows.Forms.CheckBox checkBox_ShowTOdrLine;

		// Token: 0x04000889 RID: 2185
		private global::System.Windows.Forms.CheckBox checkBox_ShowTransNotesBorder;

		// Token: 0x0400088A RID: 2186
		private global::System.Windows.Forms.Panel panel1;

		// Token: 0x0400088B RID: 2187
		private global::System.Windows.Forms.RadioButton radioBtn_AllTransArrow;

		// Token: 0x0400088C RID: 2188
		private global::System.Windows.Forms.RadioButton radioBtn_CurrTransArrow;

		// Token: 0x0400088D RID: 2189
		private global::System.Windows.Forms.CheckBox checkBox_AlwaysShowTransNoteBox;

		// Token: 0x0400088E RID: 2190
		private global::System.Windows.Forms.CheckBox checkBox_ShowTransArrow;

		// Token: 0x0400088F RID: 2191
		private global::System.Windows.Forms.GroupBox groupBox6;

		// Token: 0x04000890 RID: 2192
		private global::System.Windows.Forms.CheckBox chkBox_ConfDblClickClsTrans;

		// Token: 0x04000891 RID: 2193
		private global::System.Windows.Forms.CheckBox chkBox_ConfOrdr;

		// Token: 0x04000892 RID: 2194
		private global::System.Windows.Forms.CheckBox checkBox_PlayOCSound;

		// Token: 0x04000893 RID: 2195
		private global::System.Windows.Forms.GroupBox groupBox9;

		// Token: 0x04000894 RID: 2196
		private global::System.Windows.Forms.Label lbl_DispDayDivLine2;

		// Token: 0x04000895 RID: 2197
		private global::System.Windows.Forms.ComboBox cmbBx_DDlineChtMins;

		// Token: 0x04000896 RID: 2198
		private global::System.Windows.Forms.CheckBox chkBox_ifDispDayDivLine;

		// Token: 0x04000897 RID: 2199
		private global::System.Windows.Forms.ComboBox comboBox_BarTypes;

		// Token: 0x04000898 RID: 2200
		private global::System.Windows.Forms.Label label9;

		// Token: 0x04000899 RID: 2201
		private global::System.Windows.Forms.ComboBox comboBox_ChartThemes;

		// Token: 0x0400089A RID: 2202
		private global::System.Windows.Forms.Label label10;

		// Token: 0x0400089B RID: 2203
		private global::System.Windows.Forms.GroupBox groupBox10;

		// Token: 0x0400089C RID: 2204
		private global::System.Windows.Forms.GroupBox groupBox11;

		// Token: 0x0400089D RID: 2205
		private global::System.Windows.Forms.PictureBox picBox_theme;

		// Token: 0x0400089E RID: 2206
		private global::System.Windows.Forms.GroupBox groupBox12;

		// Token: 0x0400089F RID: 2207
		private global::System.Windows.Forms.GroupBox groupBox13;

		// Token: 0x040008A0 RID: 2208
		private global::System.Windows.Forms.Panel panel2;

		// Token: 0x040008A1 RID: 2209
		private global::System.Windows.Forms.RadioButton radioBtn_ShowSymbCode;

		// Token: 0x040008A2 RID: 2210
		private global::System.Windows.Forms.RadioButton radioBtn_ShowSymbCNName;

		// Token: 0x040008A3 RID: 2211
		private global::System.Windows.Forms.Label label11;

		// Token: 0x040008A4 RID: 2212
		private global::System.Windows.Forms.CheckBox chkBox_IfShowAcctInfoOnTransTabHeader;

		// Token: 0x040008A5 RID: 2213
		private global::System.Windows.Forms.CheckBox chkBox_IfShowDayOfWeek;

		// Token: 0x040008A6 RID: 2214
		private global::System.Windows.Forms.Label label12;

		// Token: 0x040008A7 RID: 2215
		private global::System.Windows.Forms.GroupBox groupBox4;

		// Token: 0x040008A8 RID: 2216
		private global::System.Windows.Forms.CheckBox chkBox_IfAutoCancelCloseTransOrder;

		// Token: 0x040008A9 RID: 2217
		private global::System.Windows.Forms.GroupBox groupBox14;

		// Token: 0x040008AA RID: 2218
		private global::System.Windows.Forms.Label label8;

		// Token: 0x040008AB RID: 2219
		private global::System.Windows.Forms.Panel panel_SymbSwitchTime;

		// Token: 0x040008AC RID: 2220
		private global::System.Windows.Forms.RadioButton radioBtn_SymbSwitchCurrDt;

		// Token: 0x040008AD RID: 2221
		private global::System.Windows.Forms.RadioButton radioBtn_SymbSwitchLastDt;

		// Token: 0x040008AE RID: 2222
		private global::System.Windows.Forms.CheckBox checkBox_ShowHiLowMarks;
	}
}
