﻿using System;
using TEx.Trading;

namespace ns15
{
	// Token: 0x02000274 RID: 628
	internal sealed class EventArgs17 : EventArgs
	{
		// Token: 0x06001B76 RID: 7030 RVA: 0x0000B516 File Offset: 0x00009716
		public EventArgs17(Transaction transaction_1)
		{
			this.transaction_0 = transaction_1;
		}

		// Token: 0x17000478 RID: 1144
		// (get) Token: 0x06001B77 RID: 7031 RVA: 0x000B9B54 File Offset: 0x000B7D54
		public Transaction Transaction
		{
			get
			{
				return this.transaction_0;
			}
		}

		// Token: 0x04000D94 RID: 3476
		private readonly Transaction transaction_0;
	}
}
