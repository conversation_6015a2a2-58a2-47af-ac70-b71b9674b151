﻿using System;
using System.Collections.Generic;
using ns4;

namespace ns15
{
	// Token: 0x02000061 RID: 97
	internal sealed class EventArgs0 : EventArgs
	{
		// Token: 0x0600034C RID: 844 RVA: 0x000036C1 File Offset: 0x000018C1
		public EventArgs0(List<Class18> list_1)
		{
			this.InfoMineList = list_1;
		}

		// Token: 0x170000C4 RID: 196
		// (get) Token: 0x0600034D RID: 845 RVA: 0x0001F7EC File Offset: 0x0001D9EC
		// (set) Token: 0x0600034E RID: 846 RVA: 0x000036D2 File Offset: 0x000018D2
		public List<Class18> InfoMineList
		{
			get
			{
				return this.list_0;
			}
			private set
			{
				this.list_0 = value;
			}
		}

		// Token: 0x0400012B RID: 299
		private List<Class18> list_0;
	}
}
