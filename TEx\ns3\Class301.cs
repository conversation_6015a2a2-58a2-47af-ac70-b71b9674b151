﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using TEx;

namespace ns3
{
	// Token: 0x02000229 RID: 553
	internal sealed class Class301
	{
		// Token: 0x170003BD RID: 957
		// (get) Token: 0x060016F8 RID: 5880 RVA: 0x0009ADFC File Offset: 0x00098FFC
		// (set) Token: 0x060016F9 RID: 5881 RVA: 0x000095D8 File Offset: 0x000077D8
		public string Name { get; set; }

		// Token: 0x170003BE RID: 958
		// (get) Token: 0x060016FA RID: 5882 RVA: 0x0009AE14 File Offset: 0x00099014
		// (set) Token: 0x060016FB RID: 5883 RVA: 0x000095E3 File Offset: 0x000077E3
		public List<FilterCond> FilterConds { get; set; }

		// Token: 0x04000BB5 RID: 2997
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04000BB6 RID: 2998
		[CompilerGenerated]
		private List<FilterCond> list_0;
	}
}
