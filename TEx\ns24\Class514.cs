﻿using System;
using ns17;
using ns34;

namespace ns24
{
	// Token: 0x020003CF RID: 975
	internal sealed class Class514 : Class513
	{
		// Token: 0x060026F9 RID: 9977 RVA: 0x0000EED5 File Offset: 0x0000D0D5
		public Class514() : base(UIntPtr.Zero)
		{
		}

		// Token: 0x060026FA RID: 9978 RVA: 0x0000EEE2 File Offset: 0x0000D0E2
		public Class514(UIntPtr uintptr_1) : base(uintptr_1)
		{
		}

		// Token: 0x060026FB RID: 9979 RVA: 0x0000EEEB File Offset: 0x0000D0EB
		public override void Dispose()
		{
			Class516.smethod_0(base.HKey);
		}

		// Token: 0x060026FC RID: 9980 RVA: 0x0000EEF9 File Offset: 0x0000D0F9
		public override object vmethod_0(string string_0)
		{
			return Class516.GetValue(base.HKey, string_0);
		}

		// Token: 0x060026FD RID: 9981 RVA: 0x0000EF07 File Offset: 0x0000D107
		public override bool vmethod_1(string string_0, out object object_0)
		{
			object_0 = Class516.GetValue(base.HKey, string_0);
			return object_0 != null;
		}
	}
}
