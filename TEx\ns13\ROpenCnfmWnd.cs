﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using TEx;

namespace ns13
{
	// Token: 0x020001FF RID: 511
	internal sealed partial class ROpenCnfmWnd : Form
	{
		// Token: 0x0600149B RID: 5275 RVA: 0x00008352 File Offset: 0x00006552
		public ROpenCnfmWnd()
		{
			this.InitializeComponent();
			base.Load += this.ROpenCnfmWnd_Load;
		}

		// Token: 0x0600149C RID: 5276 RVA: 0x00086710 File Offset: 0x00084910
		private void ROpenCnfmWnd_Load(object sender, EventArgs e)
		{
			if (Base.UI.Form.IfROpenFixedAmt)
			{
				this.radioBtn_ROpenFixAmt.Checked = true;
				this.numUpDown_ROpenRatio.Enabled = false;
			}
			else
			{
				this.radioBtn_ROpenRatio.Checked = true;
				this.txtBox_ROpenRAmt.Enabled = false;
			}
			if (Base.UI.Form.ROpenRatio != null)
			{
				this.numUpDown_ROpenRatio.Value = Base.UI.Form.ROpenRatio.Value;
			}
			else
			{
				this.numUpDown_ROpenRatio.Value = 3m;
			}
			if (Base.UI.Form.ROpenFixedAmt != null)
			{
				this.txtBox_ROpenRAmt.Text = Base.UI.Form.ROpenFixedAmt.Value.ToString();
			}
			else
			{
				this.txtBox_ROpenRAmt.Text = Base.Trading.smethod_213().ToString();
			}
			if (Base.UI.Form.IfROpenNoShowCnfmDlg)
			{
				this.chkBox_ROpenShowCnfmDlg.Checked = true;
			}
			this.txtBox_ROpenRAmt.KeyPress += this.txtBox_ROpenRAmt_KeyPress;
			this.radioBtn_ROpenRatio.CheckedChanged += this.radioBtn_ROpenRatio_CheckedChanged;
			this.button_OK.Click += this.button_OK_Click;
		}

		// Token: 0x0600149D RID: 5277 RVA: 0x00086854 File Offset: 0x00084A54
		private void button_OK_Click(object sender, EventArgs e)
		{
			if (this.radioBtn_ROpenFixAmt.Checked)
			{
				Base.UI.Form.IfROpenFixedAmt = true;
				bool flag = false;
				string value = this.txtBox_ROpenRAmt.Text.Trim();
				try
				{
					if (string.IsNullOrEmpty(value))
					{
						flag = true;
					}
					else if (Convert.ToInt32(value) < 1)
					{
						flag = true;
					}
				}
				catch
				{
					flag = true;
				}
				if (flag)
				{
					MessageBox.Show("以损定量固定金额输入值无效，请重新输入！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					this.txtBox_ROpenRAmt.Focus();
					return;
				}
				Base.UI.Form.ROpenFixedAmt = new int?(Convert.ToInt32(value));
			}
			else
			{
				Base.UI.Form.IfROpenFixedAmt = false;
				Base.UI.Form.ROpenRatio = new decimal?(this.numUpDown_ROpenRatio.Value);
			}
			Base.UI.Form.IfROpenNoShowCnfmDlg = this.chkBox_ROpenShowCnfmDlg.Checked;
			base.DialogResult = DialogResult.OK;
			base.Close();
		}

		// Token: 0x0600149E RID: 5278 RVA: 0x000070D2 File Offset: 0x000052D2
		private void txtBox_ROpenRAmt_KeyPress(object sender, KeyPressEventArgs e)
		{
			if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar))
			{
				e.Handled = true;
			}
		}

		// Token: 0x0600149F RID: 5279 RVA: 0x00086940 File Offset: 0x00084B40
		private void radioBtn_ROpenRatio_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioBtn_ROpenRatio.Checked)
			{
				this.numUpDown_ROpenRatio.Enabled = true;
				this.txtBox_ROpenRAmt.Enabled = false;
			}
			else
			{
				this.numUpDown_ROpenRatio.Enabled = false;
				this.txtBox_ROpenRAmt.Enabled = true;
			}
		}

		// Token: 0x060014A0 RID: 5280 RVA: 0x00008374 File Offset: 0x00006574
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000AA7 RID: 2727
		private IContainer icontainer_0;
	}
}
