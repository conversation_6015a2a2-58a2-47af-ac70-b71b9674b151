﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns11;
using TEx;

namespace ns15
{
	// Token: 0x020002D8 RID: 728
	internal sealed partial class FormPickColor : Form
	{
		// Token: 0x0600207E RID: 8318 RVA: 0x0000D234 File Offset: 0x0000B434
		public FormPickColor()
		{
			this.InitializeComponent();
			this.buttonOK.Click += this.buttonOK_Click;
			Base.UI.smethod_54(this);
		}

		// Token: 0x140000A1 RID: 161
		// (add) Token: 0x0600207F RID: 8319 RVA: 0x000DF7F0 File Offset: 0x000DD9F0
		// (remove) Token: 0x06002080 RID: 8320 RVA: 0x000DF828 File Offset: 0x000DDA28
		public event EventHandler OnColor
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06002081 RID: 8321 RVA: 0x000DF860 File Offset: 0x000DDA60
		private void buttonOK_Click(object sender, EventArgs e)
		{
			ColorDialog colorDialog = new ColorDialog();
			colorDialog.AllowFullOpen = true;
			colorDialog.ShowHelp = true;
			colorDialog.Color = Color.Red;
			colorDialog.AnyColor = true;
			if (colorDialog.ShowDialog() == DialogResult.OK && this.eventHandler_0 != null)
			{
				EventArgs31 e2 = new EventArgs31(colorDialog.Color);
				this.eventHandler_0(this, e2);
			}
		}

		// Token: 0x06002082 RID: 8322 RVA: 0x0000D261 File Offset: 0x0000B461
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000FF9 RID: 4089
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000FFA RID: 4090
		private IContainer icontainer_0;
	}
}
