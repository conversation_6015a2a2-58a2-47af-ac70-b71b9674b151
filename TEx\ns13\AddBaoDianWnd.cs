﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using DevComponents.AdvTree;
using ns28;
using TEx;
using TEx.Util;

namespace ns13
{
	// Token: 0x0200008C RID: 140
	internal sealed partial class AddBaoDianWnd : Form
	{
		// Token: 0x06000492 RID: 1170 RVA: 0x00003F64 File Offset: 0x00002164
		public AddBaoDianWnd()
		{
			this.InitializeComponent();
			base.Icon = Class372.BookIcon;
			base.Shown += this.AddBaoDianWnd_Shown;
		}

		// Token: 0x06000493 RID: 1171 RVA: 0x00003F91 File Offset: 0x00002191
		private void AddBaoDianWnd_Shown(object sender, EventArgs e)
		{
			this.txtBox_Name.SelectAll();
			this.txtBox_Name.Focus();
		}

		// Token: 0x06000494 RID: 1172 RVA: 0x00024024 File Offset: 0x00022224
		internal void method_0(ChtCtrl chtCtrl_1, List<string> list_1, Bitmap bitmap_0, string string_1 = null)
		{
			this.ChtCtrl = chtCtrl_1;
			this.Groups = list_1;
			this.CurrGroupPath = string_1;
			this.cmb_GroupName.DataSource = list_1;
			this.cmb_GroupName.SelectedIndex = 0;
			if (!string.IsNullOrEmpty(string_1))
			{
				this.cmb_GroupName.SelectedIndex = list_1.IndexOf(string_1);
			}
			this.pictureBox1.Image = bitmap_0;
			DateTime date = this.ChtCtrl.SymbDataSet.CurrHisData.Date;
			this.label_Symbl.Text = this.ChtCtrl.Symbol.CNName;
			this.label_Date.Text = date.ToString("yyyy-MM-dd hh:mm");
			this.txtBox_Name.Text = this.ChtCtrl.Symbol.CNName + date.ToString("(yy-MM-dd)");
		}

		// Token: 0x06000495 RID: 1173 RVA: 0x00024100 File Offset: 0x00022300
		internal void method_1(Node node_1, List<string> list_1)
		{
			this.Text = "修改宝典";
			this.BaoDianNodeToEdit = node_1;
			this.Groups = list_1;
			this.CurrGroupPath = node_1.Parent.FullPath;
			this.cmb_GroupName.DataSource = list_1;
			this.cmb_GroupName.SelectedIndex = 0;
			if (!string.IsNullOrEmpty(this.CurrGroupPath))
			{
				this.cmb_GroupName.SelectedIndex = list_1.IndexOf(this.CurrGroupPath);
			}
			BaoDian baoDian = node_1.Tag as BaoDian;
			this.pictureBox1.Image = baoDian.ScreenShot;
			this.txtBox_Name.Text = baoDian.Name;
			DateTime symbolTime = baoDian.SymbolTime;
			this.label_Date.Text = symbolTime.ToString("yyyy-MM-dd hh:mm");
			StkSymbol stkSymbol = SymbMgr.smethod_3(baoDian.SymbolID);
			if (stkSymbol != null)
			{
				this.label_Symbl.Text = stkSymbol.CNName;
			}
			this.txtBox_Note.Text = baoDian.Note;
		}

		// Token: 0x06000496 RID: 1174 RVA: 0x000241F4 File Offset: 0x000223F4
		private void Btn_OK_Click(object sender, EventArgs e)
		{
			string group = this.cmb_GroupName.Text.Trim();
			string text = this.txtBox_Name.Text.Trim();
			if (string.IsNullOrEmpty(text))
			{
				MessageBox.Show("请输入宝典名称");
				this.txtBox_Name.Focus();
			}
			else
			{
				string note = this.txtBox_Note.Text.Trim();
				if (this.BaoDianNodeToEdit == null)
				{
					BaoDian baoDian = new BaoDian();
					baoDian.Name = text;
					baoDian.UID = Utility.GetUniqueString(20);
					baoDian.ScreenShot = (this.pictureBox1.Image as Bitmap);
					baoDian.Group = this.cmb_GroupName.Text.Trim();
					baoDian.Note = this.txtBox_Note.Text.Trim();
					baoDian.SymbolID = this.ChtCtrl.Symbol.ID;
					baoDian.PeriodType = this.ChtCtrl.PeriodType;
					baoDian.PeriodUnit = this.ChtCtrl.PeriodUnits;
					baoDian.SymbolTime = this.ChtCtrl.SymbDataSet.CurrHisData.Date;
					baoDian.CreateTime = DateTime.Now;
					Action<BaoDian> action_ = BaoDianMgr.action_0;
					if (action_ != null)
					{
						action_(baoDian);
					}
				}
				else
				{
					this.BaoDianNodeToEdit.Text = text;
					BaoDian baoDian2 = this.BaoDianNodeToEdit.Tag as BaoDian;
					baoDian2.Name = text;
					baoDian2.Group = group;
					baoDian2.Note = note;
					Action<Node> action_2 = BaoDianMgr.action_1;
					if (action_2 != null)
					{
						action_2(this.BaoDianNodeToEdit);
					}
				}
				BaoDianMgr.smethod_8();
				base.Close();
			}
		}

		// Token: 0x170000F2 RID: 242
		// (get) Token: 0x06000497 RID: 1175 RVA: 0x00024384 File Offset: 0x00022584
		// (set) Token: 0x06000498 RID: 1176 RVA: 0x00003FAC File Offset: 0x000021AC
		internal ChtCtrl ChtCtrl { get; set; }

		// Token: 0x170000F3 RID: 243
		// (get) Token: 0x06000499 RID: 1177 RVA: 0x0002439C File Offset: 0x0002259C
		// (set) Token: 0x0600049A RID: 1178 RVA: 0x00003FB7 File Offset: 0x000021B7
		public List<string> Groups { get; set; }

		// Token: 0x170000F4 RID: 244
		// (get) Token: 0x0600049B RID: 1179 RVA: 0x000243B4 File Offset: 0x000225B4
		// (set) Token: 0x0600049C RID: 1180 RVA: 0x00003FC2 File Offset: 0x000021C2
		public string CurrGroupPath { get; set; }

		// Token: 0x170000F5 RID: 245
		// (get) Token: 0x0600049D RID: 1181 RVA: 0x000243CC File Offset: 0x000225CC
		// (set) Token: 0x0600049E RID: 1182 RVA: 0x00003FCD File Offset: 0x000021CD
		public Node BaoDianNodeToEdit { get; private set; }

		// Token: 0x0600049F RID: 1183 RVA: 0x00003FD8 File Offset: 0x000021D8
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x040001C0 RID: 448
		[CompilerGenerated]
		private ChtCtrl chtCtrl_0;

		// Token: 0x040001C1 RID: 449
		[CompilerGenerated]
		private List<string> list_0;

		// Token: 0x040001C2 RID: 450
		[CompilerGenerated]
		private string string_0;

		// Token: 0x040001C3 RID: 451
		[CompilerGenerated]
		private Node node_0;

		// Token: 0x040001C4 RID: 452
		private IContainer icontainer_0;
	}
}
