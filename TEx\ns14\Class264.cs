﻿using System;

namespace ns14
{
	// Token: 0x020001DF RID: 479
	internal sealed class Class264
	{
		// Token: 0x060012B8 RID: 4792 RVA: 0x00002D25 File Offset: 0x00000F25
		public Class264()
		{
		}

		// Token: 0x060012B9 RID: 4793 RVA: 0x00007C76 File Offset: 0x00005E76
		public Class264(int int_1, string string_3, string string_4, string string_5)
		{
			this.Id = int_1;
			this.KeyStr = string_3;
			this.EnName = string_4;
			this.CnName = string_5;
		}

		// Token: 0x170002D0 RID: 720
		// (get) Token: 0x060012BA RID: 4794 RVA: 0x0007F99C File Offset: 0x0007DB9C
		// (set) Token: 0x060012BB RID: 4795 RVA: 0x00007C9D File Offset: 0x00005E9D
		public int Id
		{
			get
			{
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
			}
		}

		// Token: 0x170002D1 RID: 721
		// (get) Token: 0x060012BC RID: 4796 RVA: 0x0007F9B4 File Offset: 0x0007DBB4
		// (set) Token: 0x060012BD RID: 4797 RVA: 0x00007CA8 File Offset: 0x00005EA8
		public string KeyStr
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x170002D2 RID: 722
		// (get) Token: 0x060012BE RID: 4798 RVA: 0x0007F9CC File Offset: 0x0007DBCC
		// (set) Token: 0x060012BF RID: 4799 RVA: 0x00007CB3 File Offset: 0x00005EB3
		public string EnName
		{
			get
			{
				return this.string_1;
			}
			set
			{
				this.string_1 = value;
			}
		}

		// Token: 0x170002D3 RID: 723
		// (get) Token: 0x060012C0 RID: 4800 RVA: 0x0007F9E4 File Offset: 0x0007DBE4
		// (set) Token: 0x060012C1 RID: 4801 RVA: 0x00007CBE File Offset: 0x00005EBE
		public string CnName
		{
			get
			{
				return this.string_2;
			}
			set
			{
				this.string_2 = value;
			}
		}

		// Token: 0x040009BB RID: 2491
		private int int_0;

		// Token: 0x040009BC RID: 2492
		private string string_0;

		// Token: 0x040009BD RID: 2493
		private string string_1;

		// Token: 0x040009BE RID: 2494
		private string string_2;
	}
}
