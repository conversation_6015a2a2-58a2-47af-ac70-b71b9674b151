﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using System.Xml;
using Newtonsoft.Json;
using ns12;
using ns28;
using ns32;
using ns6;
using ns9;
using TEx.Comn;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000135 RID: 309
	internal static class HDFileMgr
	{
		// Token: 0x06000CA0 RID: 3232 RVA: 0x0004994C File Offset: 0x00047B4C
		public static string smethod_0(List<UsrStkMeta> list_0, List<HDFileInfo> list_1, string string_0)
		{
			return Utility.ConvertXMLDocToString(HDFileMgr.smethod_3(list_0, list_1, string_0));
		}

		// Token: 0x06000CA1 RID: 3233 RVA: 0x0004996C File Offset: 0x00047B6C
		public static string smethod_1(List<UsrStkMeta> list_0, List<HDFileInfo> list_1, Dictionary<int, StkSymbol> dictionary_1, string string_0)
		{
			return Utility.ConvertXMLDocToString(HDFileMgr.smethod_2(list_0, list_1, dictionary_1, string_0));
		}

		// Token: 0x06000CA2 RID: 3234 RVA: 0x0004998C File Offset: 0x00047B8C
		private static XmlDocument smethod_2(List<UsrStkMeta> list_0, List<HDFileInfo> list_1, Dictionary<int, StkSymbol> dictionary_1, string string_0)
		{
			IEnumerable<HDFileInfo> ienumerable_ = list_1.Join(dictionary_1.Values, new Func<HDFileInfo, int>(HDFileMgr.<>c.<>9.method_0), new Func<StkSymbol, int>(HDFileMgr.<>c.<>9.method_1), new Func<HDFileInfo, StkSymbol, <>f__AnonymousType14<HDFileInfo, StkSymbol>>(HDFileMgr.<>c.<>9.method_2)).Where(new Func<<>f__AnonymousType14<HDFileInfo, StkSymbol>, bool>(HDFileMgr.<>c.<>9.method_3)).Select(new Func<<>f__AnonymousType14<HDFileInfo, StkSymbol>, HDFileInfo>(HDFileMgr.<>c.<>9.method_4));
			return HDFileMgr.smethod_3(list_0, ienumerable_, string_0);
		}

		// Token: 0x06000CA3 RID: 3235 RVA: 0x00049A58 File Offset: 0x00047C58
		public static XmlDocument smethod_3(List<UsrStkMeta> list_0, IEnumerable<HDFileInfo> ienumerable_0, string string_0)
		{
			XmlDocument xmlDocument = new XmlDocument();
			XmlDeclaration newChild = xmlDocument.CreateXmlDeclaration("1.0", "utf-8", null);
			xmlDocument.AppendChild(newChild);
			XmlElement xmlElement = xmlDocument.CreateElement("Feed");
			xmlElement.SetAttribute("BaseUrl", string_0);
			xmlDocument.AppendChild(xmlElement);
			XmlElement xmlElement2 = xmlDocument.CreateElement("Tasks");
			using (List<UsrStkMeta>.Enumerator enumerator = list_0.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					HDFileMgr.Class188 @class = new HDFileMgr.Class188();
					@class.usrStkMeta_0 = enumerator.Current;
					try
					{
						if (@class.usrStkMeta_0.DatInfoList != null)
						{
							using (IEnumerator<DatInfo> enumerator2 = @class.usrStkMeta_0.DatInfoList.Where(new Func<DatInfo, bool>(HDFileMgr.<>c.<>9.method_5)).GetEnumerator())
							{
								while (enumerator2.MoveNext())
								{
									HDFileMgr.Class189 class2 = new HDFileMgr.Class189();
									class2.class188_0 = @class;
									class2.datInfo_0 = enumerator2.Current;
									IEnumerable<HDFileInfo> ienumerable_ = ienumerable_0.Where(new Func<HDFileInfo, bool>(class2.method_0));
									HDFileMgr.smethod_13(xmlDocument, xmlElement2, ienumerable_, string_0);
								}
							}
						}
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
			}
			xmlElement.AppendChild(xmlElement2);
			return xmlDocument;
		}

		// Token: 0x06000CA4 RID: 3236 RVA: 0x00049BD0 File Offset: 0x00047DD0
		public static string smethod_4(List<UsrStkMeta> list_0, string string_0)
		{
			return Utility.ConvertXMLDocToString(HDFileMgr.smethod_5(list_0, string_0));
		}

		// Token: 0x06000CA5 RID: 3237 RVA: 0x00049BF0 File Offset: 0x00047DF0
		public static XmlDocument smethod_5(List<UsrStkMeta> list_0, string string_0)
		{
			XmlDocument xmlDocument = new XmlDocument();
			XmlDeclaration newChild = xmlDocument.CreateXmlDeclaration("1.0", "utf-8", null);
			xmlDocument.AppendChild(newChild);
			XmlElement xmlElement = xmlDocument.CreateElement("Feed");
			xmlElement.SetAttribute("BaseUrl", string_0);
			xmlDocument.AppendChild(xmlElement);
			XmlElement xmlElement2 = xmlDocument.CreateElement("Tasks");
			List<Class192> list = new List<Class192>();
			foreach (UsrStkMeta usrStkMeta in list_0)
			{
				try
				{
					if (usrStkMeta.DatInfoList != null)
					{
						DatInfo datInfo = usrStkMeta.DatInfoList.FirstOrDefault(new Func<DatInfo, bool>(HDFileMgr.<>c.<>9.method_6));
						if (datInfo != null)
						{
							list.Add(HDFileMgr.smethod_9(usrStkMeta, new int?(datInfo.BeginDate.Value.Year), new int?(datInfo.EndDate.Value.Year)));
						}
					}
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
			if (list.Count > 0)
			{
				IEnumerable<HDFileInfo> ienumerable_ = HDFileMgr.smethod_8(list);
				HDFileMgr.smethod_13(xmlDocument, xmlElement2, ienumerable_, string_0);
			}
			xmlElement.AppendChild(xmlElement2);
			return xmlDocument;
		}

		// Token: 0x06000CA6 RID: 3238 RVA: 0x00049D50 File Offset: 0x00047F50
		public static IEnumerable<HDFileInfo> smethod_6(int int_0, int? nullable_0, int? nullable_1, bool bool_0 = true)
		{
			HDFileMgr.Class190 @class = new HDFileMgr.Class190();
			@class.int_0 = int_0;
			SymbDataSet symbDataSet = Base.Data.SymbDataSets.SingleOrDefault(new Func<SymbDataSet, bool>(@class.method_0));
			UsrStkMeta usrStkMeta_;
			if (symbDataSet != null)
			{
				usrStkMeta_ = symbDataSet.CurrStkMeta;
			}
			else
			{
				usrStkMeta_ = Base.Data.smethod_90(@class.int_0);
			}
			IEnumerable<HDFileInfo> enumerable = null;
			@class.class192_0 = HDFileMgr.smethod_9(usrStkMeta_, nullable_0, nullable_1);
			if (bool_0 && HDFileMgr.StkHDFileInfoDict.ContainsKey(@class.int_0))
			{
				Class192 apiParam = HDFileMgr.StkHDFileInfoDict[@class.int_0].ApiParam;
				DateTime fetchTime = HDFileMgr.StkHDFileInfoDict[@class.int_0].FetchTime;
				bool flag = fetchTime.DayOfWeek == DayOfWeek.Saturday || fetchTime.DayOfWeek == DayOfWeek.Sunday;
				bool flag2;
				if ((flag2 = ((DateTime.Now - fetchTime).TotalHours < 4.0 && (!flag && fetchTime.Hour < 18) && DateTime.Now.Hour >= 16)) && @class.class192_0.getMin)
				{
					bool flag3;
					if (apiParam.getMin)
					{
						int? num = apiParam.minBegYr;
						int? num2 = @class.class192_0.minBegYr;
						if (!(num.GetValueOrDefault() == num2.GetValueOrDefault() & num != null == (num2 != null)) && (apiParam.minBegYr == null || @class.class192_0.minBegYr == null || apiParam.minBegYr.Value > @class.class192_0.minBegYr.Value))
						{
							flag3 = false;
						}
						else
						{
							num2 = apiParam.minEndYr;
							num = @class.class192_0.minEndYr;
							if (!(num2.GetValueOrDefault() == num.GetValueOrDefault() & num2 != null == (num != null)))
							{
								if (apiParam.minEndYr != null && @class.class192_0.minEndYr != null)
								{
									num = apiParam.minEndYr;
									int value = @class.class192_0.minEndYr.Value;
									flag3 = (num.GetValueOrDefault() >= value & num != null);
								}
								else
								{
									flag3 = false;
								}
							}
							else
							{
								flag3 = true;
							}
						}
					}
					else
					{
						flag3 = false;
					}
					flag2 = flag3;
				}
				if (flag2 && @class.class192_0.getHour)
				{
					bool flag4;
					if (apiParam.getHour)
					{
						int? num = apiParam.hourBegYr;
						int? num2 = @class.class192_0.hourBegYr;
						if (!(num.GetValueOrDefault() == num2.GetValueOrDefault() & num != null == (num2 != null)) && (apiParam.hourBegYr == null || @class.class192_0.hourBegYr == null || apiParam.hourBegYr.Value > @class.class192_0.hourBegYr.Value))
						{
							flag4 = false;
						}
						else
						{
							num2 = apiParam.hourEndYr;
							num = @class.class192_0.hourEndYr;
							if (!(num2.GetValueOrDefault() == num.GetValueOrDefault() & num2 != null == (num != null)))
							{
								if (apiParam.hourEndYr != null && @class.class192_0.hourEndYr != null)
								{
									num = apiParam.hourEndYr;
									int value = @class.class192_0.hourEndYr.Value;
									flag4 = (num.GetValueOrDefault() >= value & num != null);
								}
								else
								{
									flag4 = false;
								}
							}
							else
							{
								flag4 = true;
							}
						}
					}
					else
					{
						flag4 = false;
					}
					flag2 = flag4;
				}
				if (flag2)
				{
					IEnumerable<HDFileInfo> hdfileInfos = HDFileMgr.StkHDFileInfoDict[@class.int_0].HDFileInfos;
					if (@class.class192_0.getMin)
					{
						enumerable = hdfileInfos.Where(new Func<HDFileInfo, bool>(@class.method_1));
					}
					if (@class.class192_0.getHour)
					{
						enumerable.Union(hdfileInfos.Where(new Func<HDFileInfo, bool>(@class.method_2)));
					}
				}
			}
			if (enumerable == null)
			{
				enumerable = HDFileMgr.smethod_8(new List<Class192>
				{
					@class.class192_0
				});
				if (enumerable != null)
				{
					Class191 value2 = new Class191(@class.int_0, enumerable, @class.class192_0);
					HDFileMgr.StkHDFileInfoDict[@class.int_0] = value2;
				}
			}
			return enumerable;
		}

		// Token: 0x06000CA7 RID: 3239 RVA: 0x0004A1A8 File Offset: 0x000483A8
		public static IEnumerable<HDFileInfo> smethod_7(int int_0, bool bool_0, int? nullable_0, int? nullable_1, bool bool_1, int? nullable_2, int? nullable_3)
		{
			Class192 item = new Class192(int_0, bool_0, nullable_0, nullable_1, bool_1, nullable_2, nullable_3);
			return HDFileMgr.smethod_8(new List<Class192>
			{
				item
			});
		}

		// Token: 0x06000CA8 RID: 3240 RVA: 0x0004A1DC File Offset: 0x000483DC
		public static IEnumerable<HDFileInfo> smethod_8(List<Class192> list_0)
		{
			IEnumerable<HDFileInfo> result = null;
			Class8<string, string, List<Class192>> postDataObj = new Class8<string, string, List<Class192>>("GetUsrHFIs", TApp.LoginCode, list_0);
			ApiResult apiResultByPost = new Class555().GetApiResultByPost(postDataObj);
			if (apiResultByPost != null)
			{
				if (apiResultByPost.code == 0)
				{
					if (apiResultByPost.data != null)
					{
						result = JsonConvert.DeserializeObject<IEnumerable<HDFileInfo>>(Convert.ToString(apiResultByPost.data));
					}
				}
				else if (apiResultByPost.code == 120 || apiResultByPost.code == 110 || apiResultByPost.code == 100)
				{
					string text = "程序即将关闭。";
					if (apiResultByPost.code == 120)
					{
						text = "当前账号已在其他电脑上登录，" + text;
					}
					else
					{
						text = "当前登录已失效，" + text;
					}
					MessageBox.Show(text, "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					Environment.Exit(0);
				}
			}
			return result;
		}

		// Token: 0x06000CA9 RID: 3241 RVA: 0x0004A298 File Offset: 0x00048498
		private static Class192 smethod_9(UsrStkMeta usrStkMeta_0, int? nullable_0, int? nullable_1)
		{
			int stkId = usrStkMeta_0.StkId;
			bool bool_ = false;
			int? nullable_2 = null;
			int? nullable_3 = null;
			bool bool_2 = false;
			int? nullable_4 = null;
			int? nullable_5 = null;
			if (TApp.IsTrialUser)
			{
				if (usrStkMeta_0.DatInfo_1m != null)
				{
					bool_ = true;
					nullable_2 = new int?(HDFileMgr.smethod_10(nullable_0, nullable_1, usrStkMeta_0.DatInfo_1m.BeginDate.Value.Year));
					nullable_3 = new int?(HDFileMgr.smethod_11(nullable_1, usrStkMeta_0.DatInfo_1m.EndDate.Value.Year));
				}
				if (usrStkMeta_0.DatInfo_1h != null)
				{
					bool_2 = true;
					nullable_4 = new int?(usrStkMeta_0.DatInfo_1h.BeginDate.Value.Year);
					nullable_5 = new int?(usrStkMeta_0.DatInfo_1h.EndDate.Value.Year);
				}
			}
			else
			{
				bool_ = true;
				bool_2 = true;
				int year = usrStkMeta_0.BeginDate.Value.Year;
				int year2 = usrStkMeta_0.EndDate.Value.Year;
				nullable_2 = new int?(HDFileMgr.smethod_10(nullable_0, nullable_1, year));
				nullable_3 = new int?(HDFileMgr.smethod_11(nullable_1, year2));
				nullable_4 = new int?((nullable_0 != null) ? ((nullable_0.Value > year) ? nullable_0.Value : year) : year);
				nullable_5 = new int?((nullable_1 != null) ? ((nullable_1.Value < year2) ? nullable_1.Value : year2) : year2);
			}
			return new Class192(stkId, bool_, nullable_2, nullable_3, bool_2, nullable_4, nullable_5);
		}

		// Token: 0x06000CAA RID: 3242 RVA: 0x0004A440 File Offset: 0x00048640
		private static int smethod_10(int? nullable_0, int? nullable_1, int int_0)
		{
			if (nullable_0 != null)
			{
				if (nullable_0.Value > int_0)
				{
					int_0 = nullable_0.Value;
				}
			}
			else if (nullable_1 != null && nullable_1.Value > int_0)
			{
				int_0 = nullable_1.Value;
			}
			return int_0;
		}

		// Token: 0x06000CAB RID: 3243 RVA: 0x0004A48C File Offset: 0x0004868C
		private static int smethod_11(int? nullable_0, int int_0)
		{
			if (nullable_0 != null && nullable_0.Value < int_0)
			{
				int_0 = nullable_0.Value;
			}
			return int_0;
		}

		// Token: 0x06000CAC RID: 3244 RVA: 0x00005B42 File Offset: 0x00003D42
		public static void smethod_12(XmlDocument xmlDocument_0, XmlElement xmlElement_0, IEnumerable<HDFileInfo> ienumerable_0)
		{
			HDFileMgr.smethod_13(xmlDocument_0, xmlElement_0, ienumerable_0, TApp.SrvParams.HdDatFileBaseUrl);
		}

		// Token: 0x06000CAD RID: 3245 RVA: 0x0004A4BC File Offset: 0x000486BC
		public static void smethod_13(XmlDocument xmlDocument_0, XmlElement xmlElement_0, IEnumerable<HDFileInfo> ienumerable_0, string string_0)
		{
			foreach (HDFileInfo hdfileInfo in ienumerable_0)
			{
				XmlElement xmlElement = xmlDocument_0.CreateElement("FileUpdateTask");
				xmlElement.SetAttribute("hotswap", "true");
				xmlElement.SetAttribute("updateTo", string_0 + "/Data/" + hdfileInfo.FileName);
				xmlElement.SetAttribute("localPath", ".\\Data\\" + hdfileInfo.FileName);
				xmlElement.SetAttribute("fileSize", hdfileInfo.FileSize.ToString());
				XmlElement xmlElement2 = xmlDocument_0.CreateElement("Conditions");
				XmlElement xmlElement3 = xmlDocument_0.CreateElement("FileExistsCondition");
				xmlElement3.SetAttribute("type", "or-not");
				xmlElement2.AppendChild(xmlElement3);
				xmlElement3 = xmlDocument_0.CreateElement("FileSizeCondition");
				xmlElement3.SetAttribute("type", "or-not");
				xmlElement3.SetAttribute("what", "is");
				xmlElement3.SetAttribute("size", hdfileInfo.FileSize.ToString());
				xmlElement2.AppendChild(xmlElement3);
				xmlElement3 = xmlDocument_0.CreateElement("FileChecksumCondition");
				xmlElement3.SetAttribute("type", "or-not");
				xmlElement3.SetAttribute("checksumType", "sha256");
				xmlElement3.SetAttribute("checksum", hdfileInfo.SHA);
				xmlElement2.AppendChild(xmlElement3);
				xmlElement.AppendChild(xmlElement2);
				xmlElement_0.AppendChild(xmlElement);
			}
		}

		// Token: 0x06000CAE RID: 3246 RVA: 0x0004A66C File Offset: 0x0004886C
		public static void smethod_14(string string_0, List<HDFileInfo> list_0)
		{
			if (string_0 != null)
			{
				string[] array = string_0.Split(new string[]
				{
					Environment.NewLine
				}, StringSplitOptions.None);
				for (int i = 0; i < array.Length; i++)
				{
					string[] array2 = array[i].Split(new char[]
					{
						','
					});
					HDFileInfo hdfileInfo = new HDFileInfo();
					hdfileInfo.StkID = Convert.ToInt32(array2[0].Trim());
					hdfileInfo.BeginDate = Convert.ToDateTime(array2[1]);
					hdfileInfo.EndDate = Convert.ToDateTime(array2[2]);
					hdfileInfo.TotalRecords = Convert.ToInt32(array2[3].Trim());
					hdfileInfo.FileName = array2[4];
					hdfileInfo.SHA = array2[5];
					hdfileInfo.PeriodType = Convert.ToInt32(array2[6].Trim());
					hdfileInfo.PeriodUnits = Convert.ToInt32(array2[7].Trim());
					hdfileInfo.DataType = Convert.ToInt32(array2[8].Trim());
					hdfileInfo.FileSize = new int?(Convert.ToInt32(array2[9].Trim()));
					string text = array2[10];
					if (!string.IsNullOrEmpty(text))
					{
						hdfileInfo.FirstDayClose = new double?(Convert.ToDouble(text.Trim()));
					}
					list_0.Add(hdfileInfo);
				}
			}
		}

		// Token: 0x17000203 RID: 515
		// (get) Token: 0x06000CAF RID: 3247 RVA: 0x0004A798 File Offset: 0x00048998
		// (set) Token: 0x06000CB0 RID: 3248 RVA: 0x00005B58 File Offset: 0x00003D58
		public static Dictionary<int, Class191> StkHDFileInfoDict
		{
			get
			{
				if (HDFileMgr.dictionary_0 == null)
				{
					HDFileMgr.dictionary_0 = new Dictionary<int, Class191>();
				}
				return HDFileMgr.dictionary_0;
			}
			private set
			{
				HDFileMgr.dictionary_0 = value;
			}
		}

		// Token: 0x04000533 RID: 1331
		private static Dictionary<int, Class191> dictionary_0;

		// Token: 0x02000137 RID: 311
		[CompilerGenerated]
		private sealed class Class188
		{
			// Token: 0x0400053C RID: 1340
			public UsrStkMeta usrStkMeta_0;
		}

		// Token: 0x02000138 RID: 312
		[CompilerGenerated]
		private sealed class Class189
		{
			// Token: 0x06000CBC RID: 3260 RVA: 0x0004A85C File Offset: 0x00048A5C
			internal bool method_0(HDFileInfo hdfileInfo_0)
			{
				bool result;
				if (hdfileInfo_0.StkID == this.class188_0.usrStkMeta_0.StkId && hdfileInfo_0.EndDate.Year >= this.datInfo_0.BeginDate.Value.Year && hdfileInfo_0.EndDate.Year <= this.datInfo_0.EndDate.Value.Year)
				{
					if (hdfileInfo_0.PeriodUnits != 1)
					{
						result = (hdfileInfo_0.PeriodUnits == 60);
					}
					else
					{
						result = true;
					}
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400053D RID: 1341
			public DatInfo datInfo_0;

			// Token: 0x0400053E RID: 1342
			public HDFileMgr.Class188 class188_0;
		}

		// Token: 0x02000139 RID: 313
		[CompilerGenerated]
		private sealed class Class190
		{
			// Token: 0x06000CBE RID: 3262 RVA: 0x0004A8F0 File Offset: 0x00048AF0
			internal bool method_0(SymbDataSet symbDataSet_0)
			{
				return symbDataSet_0.SymblID == this.int_0;
			}

			// Token: 0x06000CBF RID: 3263 RVA: 0x0004A910 File Offset: 0x00048B10
			internal bool method_1(HDFileInfo hdfileInfo_0)
			{
				bool result;
				if (hdfileInfo_0.IsPeriod_1m() && hdfileInfo_0.BeginDate.Year >= this.class192_0.minBegYr.Value)
				{
					result = (hdfileInfo_0.EndDate.Year <= this.class192_0.minEndYr.Value);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x06000CC0 RID: 3264 RVA: 0x0004A978 File Offset: 0x00048B78
			internal bool method_2(HDFileInfo hdfileInfo_0)
			{
				bool result;
				if (hdfileInfo_0.IsPeriod_1h() && hdfileInfo_0.BeginDate.Year >= this.class192_0.hourBegYr.Value)
				{
					result = (hdfileInfo_0.EndDate.Year <= this.class192_0.hourEndYr.Value);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x0400053F RID: 1343
			public int int_0;

			// Token: 0x04000540 RID: 1344
			public Class192 class192_0;
		}
	}
}
