﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns28;
using TEx;

namespace ns6
{
	// Token: 0x0200019A RID: 410
	internal sealed partial class DrawWnd : Form
	{
		// Token: 0x06000FC7 RID: 4039 RVA: 0x00006C53 File Offset: 0x00004E53
		public DrawWnd()
		{
			this.InitializeComponent();
		}

		// Token: 0x06000FC8 RID: 4040 RVA: 0x00062E1C File Offset: 0x0006101C
		private void DrawWnd_Load(object sender, EventArgs e)
		{
			foreach (object obj in base.Controls)
			{
				if (obj is ButtonX)
				{
					(obj as ButtonX).AutoCheckOnClick = true;
				}
			}
			this.btnX_line.Click += this.btnX_line_Click;
			this.btnX_lineD.Click += this.btnX_lineD_Click;
			this.btnX_lineV.Click += this.btnX_lineV_Click;
			this.btnX_lineH.Click += this.btnX_lineH_Click;
			this.btnX_paralLine.Click += this.btnX_paralLine_Click;
			this.btnX_square.Click += this.btnX_square_Click;
			this.btnX_ellipse.Click += this.btnX_ellipse_Click;
			this.btnX_45DegreeUp.Click += this.btnX_45DegreeUp_Click;
			this.btnX_45DegreeDn.Click += this.btnX_45DegreeDn_Click;
			this.btnX_gannFan.Click += this.btnX_gannFan_Click;
			this.btnX_fbLines.Click += this.btnX_fbLines_Click;
			this.btnX_fbExtLines.Click += this.btnX_fbExtLines_Click;
			this.btnX_gannLines.Click += this.btnX_gannLines_Click;
			this.btnX_measureObj.Click += this.btnX_measureObj_Click;
			this.btnX_periodLines.Click += this.btnX_periodLines_Click;
			this.btnX_trendSpeed.Click += this.btnX_trendSpeed_Click;
			this.btnX_goldenRatio.Click += this.btnX_goldenRatio_Click;
			this.btnX_ratio.Click += this.btnX_ratio_Click;
			this.btnX_range.Click += this.btnX_range_Click;
			this.btnX_lineDExt.Click += this.btnX_lineDExt_Click;
			this.btnX_lineDH.Click += this.btnX_lineDH_Click;
			this.btnX_lineHExt.Click += this.btnX_lineHExt_Click;
			this.btnX_redArwLUp.Click += this.btnX_redArwLUp_Click;
			this.btnX_redArwRUp.Click += this.btnX_redArwRUp_Click;
			this.btnX_redArwUp.Click += this.btnX_redArwUp_Click;
			this.btnX_grnArwDn.Click += this.btnX_grnArwDn_Click;
			this.btnX_grnArwLDn.Click += this.btnX_grnArwLDn_Click;
			this.btnX_grnArwRDn.Click += this.btnX_grnArwRDn_Click;
			this.btnX_text.Click += this.btnX_text_Click;
			this.btnX_DelDraw.Click += this.btnX_DelDraw_Click;
			Base.UI.DrawModeSetOff += this.method_3;
			Base.UI.DrawToolWnd = this;
			base.FormClosed += this.DrawWnd_FormClosed;
			base.Disposed += this.DrawWnd_Disposed;
			base.Focus();
		}

		// Token: 0x06000FC9 RID: 4041 RVA: 0x000549D4 File Offset: 0x00052BD4
		protected bool ProcessCmdKey(ref Message msg, Keys keyData)
		{
			if (msg.WParam.ToInt32() == 27)
			{
				Base.UI.smethod_156();
			}
			return base.ProcessCmdKey(ref msg, keyData);
		}

		// Token: 0x06000FCA RID: 4042 RVA: 0x00063174 File Offset: 0x00061374
		private void btnX_MousePt_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.Off);
		}

		// Token: 0x06000FCB RID: 4043 RVA: 0x00063194 File Offset: 0x00061394
		private void btnX_ArrowLine_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.ArwLine);
		}

		// Token: 0x06000FCC RID: 4044 RVA: 0x000631B4 File Offset: 0x000613B4
		private void btnX_line_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.Line);
		}

		// Token: 0x06000FCD RID: 4045 RVA: 0x000631D4 File Offset: 0x000613D4
		private void btnX_lineD_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.LineD);
		}

		// Token: 0x06000FCE RID: 4046 RVA: 0x000631F4 File Offset: 0x000613F4
		private void btnX_lineV_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.LineV);
		}

		// Token: 0x06000FCF RID: 4047 RVA: 0x00063214 File Offset: 0x00061414
		private void btnX_lineH_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.LineH);
		}

		// Token: 0x06000FD0 RID: 4048 RVA: 0x00063234 File Offset: 0x00061434
		private void btnX_paralLine_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.LineP);
		}

		// Token: 0x06000FD1 RID: 4049 RVA: 0x00063254 File Offset: 0x00061454
		private void btnX_square_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.Square);
		}

		// Token: 0x06000FD2 RID: 4050 RVA: 0x00063274 File Offset: 0x00061474
		private void btnX_periodLines_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.PeriodLines);
		}

		// Token: 0x06000FD3 RID: 4051 RVA: 0x00063294 File Offset: 0x00061494
		private void btnX_fbLines_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.FibonacciLines);
		}

		// Token: 0x06000FD4 RID: 4052 RVA: 0x000632B4 File Offset: 0x000614B4
		private void btnX_fbExtLines_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.FibonacciExtLines);
		}

		// Token: 0x06000FD5 RID: 4053 RVA: 0x000632D4 File Offset: 0x000614D4
		private void btnX_gannLines_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.GannLines);
		}

		// Token: 0x06000FD6 RID: 4054 RVA: 0x000632F4 File Offset: 0x000614F4
		private void btnX_measureObj_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.MeasureObj);
		}

		// Token: 0x06000FD7 RID: 4055 RVA: 0x00063314 File Offset: 0x00061514
		private void btnX_lineHExt_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.LineHExt);
		}

		// Token: 0x06000FD8 RID: 4056 RVA: 0x00063334 File Offset: 0x00061534
		private void btnX_lineDH_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.LineDH);
		}

		// Token: 0x06000FD9 RID: 4057 RVA: 0x00063354 File Offset: 0x00061554
		private void btnX_lineDExt_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.LineDExt);
		}

		// Token: 0x06000FDA RID: 4058 RVA: 0x00063374 File Offset: 0x00061574
		private void btnX_gannFan_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.GannFan);
		}

		// Token: 0x06000FDB RID: 4059 RVA: 0x00063394 File Offset: 0x00061594
		private void btnX_45DegreeDn_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.Degree45Dn);
		}

		// Token: 0x06000FDC RID: 4060 RVA: 0x000633B4 File Offset: 0x000615B4
		private void btnX_45DegreeUp_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.Degree45Up);
		}

		// Token: 0x06000FDD RID: 4061 RVA: 0x000633D4 File Offset: 0x000615D4
		private void btnX_ellipse_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.Ellipse);
		}

		// Token: 0x06000FDE RID: 4062 RVA: 0x000633F4 File Offset: 0x000615F4
		private void btnX_range_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.Range);
		}

		// Token: 0x06000FDF RID: 4063 RVA: 0x00063414 File Offset: 0x00061614
		private void btnX_ratio_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.Ratio);
		}

		// Token: 0x06000FE0 RID: 4064 RVA: 0x00063434 File Offset: 0x00061634
		private void btnX_goldenRatio_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.GoldenRatio);
		}

		// Token: 0x06000FE1 RID: 4065 RVA: 0x00063454 File Offset: 0x00061654
		private void btnX_trendSpeed_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.TrendSpeed);
		}

		// Token: 0x06000FE2 RID: 4066 RVA: 0x00063474 File Offset: 0x00061674
		private void btnX_redArwUp_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.RedArwUp);
		}

		// Token: 0x06000FE3 RID: 4067 RVA: 0x00063494 File Offset: 0x00061694
		private void btnX_redArwLUp_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.RedArwLUp);
		}

		// Token: 0x06000FE4 RID: 4068 RVA: 0x000634B4 File Offset: 0x000616B4
		private void btnX_redArwRUp_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.RedArwRUp);
		}

		// Token: 0x06000FE5 RID: 4069 RVA: 0x000634D4 File Offset: 0x000616D4
		private void btnX_grnArwDn_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.GrnArwDn);
		}

		// Token: 0x06000FE6 RID: 4070 RVA: 0x000634F4 File Offset: 0x000616F4
		private void btnX_grnArwLDn_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.GrnArwLDn);
		}

		// Token: 0x06000FE7 RID: 4071 RVA: 0x00063514 File Offset: 0x00061714
		private void btnX_grnArwRDn_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.GrnArwRDn);
		}

		// Token: 0x06000FE8 RID: 4072 RVA: 0x00063534 File Offset: 0x00061734
		private void btnX_text_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.Text);
		}

		// Token: 0x06000FE9 RID: 4073 RVA: 0x00063554 File Offset: 0x00061754
		private void btnX_DelDraw_Click(object sender, EventArgs e)
		{
			ButtonX buttonX_ = (ButtonX)sender;
			this.method_0(buttonX_, TEx.DrawMode.EraseAllDrawObj);
		}

		// Token: 0x06000FEA RID: 4074 RVA: 0x00063574 File Offset: 0x00061774
		private void method_0(ButtonX buttonX_0, TEx.DrawMode drawMode_0)
		{
			Base.UI.DrawingObj1stPt = null;
			if (buttonX_0.Checked)
			{
				this.method_1(buttonX_0);
				Base.UI.DrawMode = drawMode_0;
			}
			else
			{
				Base.UI.DrawMode = TEx.DrawMode.Off;
			}
		}

		// Token: 0x06000FEB RID: 4075 RVA: 0x00054C48 File Offset: 0x00052E48
		private void method_1(ButtonX buttonX_0)
		{
			foreach (object obj in base.Controls)
			{
				if (obj is ButtonX && obj != buttonX_0)
				{
					ButtonX buttonX = (ButtonX)obj;
					if (buttonX.Checked)
					{
						buttonX.Checked = false;
					}
				}
			}
		}

		// Token: 0x06000FEC RID: 4076 RVA: 0x00054CBC File Offset: 0x00052EBC
		private void method_2()
		{
			foreach (object obj in base.Controls)
			{
				if (obj is ButtonX)
				{
					ButtonX buttonX = (ButtonX)obj;
					if (buttonX.Checked)
					{
						buttonX.Checked = false;
					}
				}
			}
		}

		// Token: 0x06000FED RID: 4077 RVA: 0x00006C63 File Offset: 0x00004E63
		private void DrawWnd_FormClosed(object sender, FormClosedEventArgs e)
		{
			Base.UI.DrawMode = TEx.DrawMode.Off;
			Base.UI.DrawToolWnd = null;
		}

		// Token: 0x06000FEE RID: 4078 RVA: 0x00006C74 File Offset: 0x00004E74
		private void DrawWnd_Disposed(object sender, EventArgs e)
		{
			Base.UI.DrawModeSetOff -= this.method_3;
		}

		// Token: 0x06000FEF RID: 4079 RVA: 0x00006C89 File Offset: 0x00004E89
		private void method_3(object sender, EventArgs e)
		{
			this.method_2();
		}

		// Token: 0x06000FF0 RID: 4080 RVA: 0x00006C93 File Offset: 0x00004E93
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x040007D5 RID: 2005
		private IContainer icontainer_0;
	}
}
