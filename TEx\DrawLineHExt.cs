﻿using System;
using System.Runtime.Serialization;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000076 RID: 118
	[Serializable]
	internal sealed class DrawLineHExt : DrawLineH, ISerializable
	{
		// Token: 0x0600043C RID: 1084 RVA: 0x00003D34 File Offset: 0x00001F34
		public DrawLineHExt()
		{
		}

		// Token: 0x0600043D RID: 1085 RVA: 0x00003D3C File Offset: 0x00001F3C
		public DrawLineHExt(ChartCS chart, double x1, double y1, double x2, double y2) : this(chart, x1, y1, x2, y1, false)
		{
		}

		// Token: 0x0600043E RID: 1086 RVA: 0x00003D4B File Offset: 0x00001F4B
		public DrawLineHExt(ChartCS chart, double x1, double y1, double x2, double y2, bool disableRstPrc) : base(chart, x1, y1, x2, y1, disableRstPrc)
		{
			base.Name = "水平射线";
		}

		// Token: 0x0600043F RID: 1087 RVA: 0x00003D68 File Offset: 0x00001F68
		protected DrawLineHExt(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x06000440 RID: 1088 RVA: 0x00003D79 File Offset: 0x00001F79
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x06000441 RID: 1089 RVA: 0x00022FA0 File Offset: 0x000211A0
		protected override LineObj vmethod_24(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			double max = chartCS_1.GraphPane.XAxis.Scale.Max;
			return base.method_23(double_1, double_4, max, double_4, base.Tag);
		}
	}
}
