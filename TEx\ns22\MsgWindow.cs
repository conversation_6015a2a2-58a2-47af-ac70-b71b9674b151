﻿using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Windows.Forms;
using TEx;
using TEx.Comn;

namespace ns22
{
	// Token: 0x020001A7 RID: 423
	internal sealed partial class MsgWindow : Form
	{
		// Token: 0x06001048 RID: 4168 RVA: 0x00006E72 File Offset: 0x00005072
		public MsgWindow()
		{
			base.AutoScaleMode = AutoScaleMode.None;
			this.InitializeComponent();
		}

		// Token: 0x06001049 RID: 4169 RVA: 0x00006E89 File Offset: 0x00005089
		public MsgWindow(string string_1) : this()
		{
			this.string_0 = string_1;
		}

		// Token: 0x0600104A RID: 4170 RVA: 0x00006E9A File Offset: 0x0000509A
		public MsgWindow(LogonNoticeInfo logonNoticeInfo_1) : this()
		{
			this.logonNoticeInfo_0 = logonNoticeInfo_1;
		}

		// Token: 0x0600104B RID: 4171 RVA: 0x0006A448 File Offset: 0x00068648
		private void MsgWindow_Load(object sender, EventArgs e)
		{
			float defaultScaledFontSize = TApp.DefaultScaledFontSize;
			if (this.chkBox_noticeAgain.Font.Size != defaultScaledFontSize)
			{
				this.chkBox_noticeAgain.Font = new Font("SimSun", defaultScaledFontSize);
			}
			if (this.btn_Close.Font.Size != defaultScaledFontSize)
			{
				this.btn_Close.Font = new Font("SimSun", defaultScaledFontSize);
			}
			if (!string.IsNullOrEmpty(this.string_0))
			{
				this.chkBox_noticeAgain.Hide();
				this.webBrowser1.DocumentText = this.string_0;
			}
			else if (this.logonNoticeInfo_0 != null)
			{
				string text = this.logonNoticeInfo_0.HTMLUrl;
				if (string.IsNullOrEmpty(text))
				{
					return;
				}
				if (text.Equals("about:blank"))
				{
					return;
				}
				if (!text.StartsWith("http://") && !text.StartsWith("https://"))
				{
					text = "http://" + text;
				}
				try
				{
					this.webBrowser1.Navigate(new Uri(text));
				}
				catch
				{
					base.Close();
					return;
				}
			}
			this.chkBox_noticeAgain.Checked = true;
			this.webBrowser1.Navigating += this.webBrowser1_Navigating;
		}

		// Token: 0x0600104C RID: 4172 RVA: 0x0006A580 File Offset: 0x00068780
		private void btn_Close_Click(object sender, EventArgs e)
		{
			if (Base.UI.Form.IfShowSameLogonNoticeNextTime == this.chkBox_noticeAgain.Checked)
			{
				Base.UI.Form.IfShowSameLogonNoticeNextTime = !this.chkBox_noticeAgain.Checked;
			}
			if (this.logonNoticeInfo_0 != null)
			{
				Base.UI.Form.LastDisplayedLogonNoticeDT = new DateTime?(this.logonNoticeInfo_0.DateTime);
			}
			Base.UI.smethod_47();
			base.Close();
		}

		// Token: 0x0600104D RID: 4173 RVA: 0x00006EAB File Offset: 0x000050AB
		private void webBrowser1_Navigating(object sender, WebBrowserNavigatingEventArgs e)
		{
			e.Cancel = true;
			Process.Start(new ProcessStartInfo
			{
				FileName = e.Url.ToString()
			});
			base.Close();
		}

		// Token: 0x17000266 RID: 614
		// (get) Token: 0x0600104E RID: 4174 RVA: 0x0006A5EC File Offset: 0x000687EC
		// (set) Token: 0x0600104F RID: 4175 RVA: 0x00006ED8 File Offset: 0x000050D8
		public bool NoticeAgainChkBoxEnabled
		{
			get
			{
				return this.chkBox_noticeAgain.Enabled;
			}
			set
			{
				this.chkBox_noticeAgain.Enabled = value;
			}
		}

		// Token: 0x06001050 RID: 4176 RVA: 0x00006EE8 File Offset: 0x000050E8
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000824 RID: 2084
		private string string_0;

		// Token: 0x04000825 RID: 2085
		private LogonNoticeInfo logonNoticeInfo_0;

		// Token: 0x04000826 RID: 2086
		private IContainer icontainer_0;
	}
}
