﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using System.Xml.Linq;
using ns22;
using ns23;
using ns32;
using TEx;

namespace ns24
{
	// Token: 0x02000174 RID: 372
	internal sealed class Class208
	{
		// Token: 0x06000E19 RID: 3609 RVA: 0x00059E58 File Offset: 0x00058058
		public static List<Class278> smethod_0()
		{
			return new List<Class278>
			{
				new Class278(200, "Open Long Market Order", "市价买开", KeyModifiers.None, Keys.F1),
				new Class278(201, "Close Long Market Order", "市价卖平", KeyModifiers.None, Keys.F2),
				new Class278(202, "Open Short Market Order", "市价卖开", KeyModifiers.None, Keys.F3),
				new Class278(203, "Close Short Market Order", "市价买平", KeyModifiers.None, Keys.F4),
				new Class278(204, "Open Long Order", "买开", KeyModifiers.Ctrl, Keys.D1),
				new Class278(205, "Close Long Order", "卖平", KeyModifiers.Ctrl, Keys.D2),
				new Class278(206, "Open Short Order", "卖开", KeyModifiers.Ctrl, Keys.D3),
				new Class278(207, "Close Short Order", "买平", KeyModifiers.Ctrl, Keys.D4),
				new Class278(208, "Close All", "全平持仓", KeyModifiers.Alt, Keys.A),
				new Class278(209, "Cancel All Orders", "全撤委托", KeyModifiers.Alt, Keys.W),
				new Class278(300, "Start/Pause", "开始/暂停行情回放", KeyModifiers.None, Keys.Space),
				new Class278(301, "Reduce Speed", "降低回放速度", KeyModifiers.None, Keys.OemOpenBrackets),
				new Class278(302, "Increse Speed", "增加回放速度", KeyModifiers.None, Keys.OemCloseBrackets),
				new Class278(303, "Switch Tick/KLine Chart", "切换分时/K线图", KeyModifiers.None, Keys.F5),
				new Class278(306, "Show Next KLine", "步进显示下一K线", KeyModifiers.Ctrl, Keys.Right),
				new Class278(307, "Show Previous KLine", "步进显示上一K线", KeyModifiers.Ctrl, Keys.Left),
				new Class278(308, "Show Next Symbol", "下一交易品种", KeyModifiers.None, Keys.Next),
				new Class278(309, "Show Previous Symbol", "上一交易品种", KeyModifiers.None, Keys.Prior),
				new Class278(310, "Reverse Coords", "坐标反转切换", KeyModifiers.Ctrl, Keys.R),
				new Class278(311, "Show Draw Toolbar", "显示画线工具", KeyModifiers.Ctrl, Keys.D),
				new Class278(312, "Show Date Selection Window", "显示日期选择窗口", KeyModifiers.Ctrl, Keys.X),
				new Class278(313, "Show Draw Order Window", "显示画线下单窗口", KeyModifiers.None, Keys.F9),
				new Class278(330, "Add To ZiXuan", "加入自选", KeyModifiers.Ctrl, Keys.Z),
				new Class278(340, "Add To BaoDian", "加入宝典", KeyModifiers.Ctrl, Keys.B),
				new Class278(350, "Save Page", "保存页面", KeyModifiers.Ctrl, Keys.S)
			};
		}

		// Token: 0x06000E1A RID: 3610 RVA: 0x0005A14C File Offset: 0x0005834C
		public static List<Class278> smethod_1()
		{
			List<Class278> list = Class208.smethod_2(Base.UI.smethod_53());
			if (list == null)
			{
				list = Class208.smethod_0();
			}
			else
			{
				using (List<Class278>.Enumerator enumerator = Class208.DefaultUsrHotKeyList.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						Class208.Class209 @class = new Class208.Class209();
						@class.class278_0 = enumerator.Current;
						if (!list.Exists(new Predicate<Class278>(@class.method_0)))
						{
							list.Add(@class.class278_0);
						}
					}
				}
			}
			return list;
		}

		// Token: 0x06000E1B RID: 3611 RVA: 0x0005A1E0 File Offset: 0x000583E0
		public static List<Class278> smethod_2(XDocument xdocument_0)
		{
			List<Class278> list = null;
			if (xdocument_0 != null)
			{
				try
				{
					list = new List<Class278>();
					foreach (XElement xelement in xdocument_0.Element("ShortCutKeys").Element("HotKeys").Elements("HotKey"))
					{
						Class278 @class = new Class278();
						@class.Id = Convert.ToInt32(xelement.Attribute("Id").Value);
						@class.KeyModifier = (KeyModifiers)Convert.ToInt32(xelement.Attribute("Modifier").Value);
						@class.Key = (Keys)Convert.ToInt32(xelement.Attribute("Key").Value);
						Class278 class2 = Class208.smethod_7(@class.Id);
						@class.EnName = class2.EnName;
						@class.CnName = class2.CnName;
						@class.IfDispInQuickWnd = class2.IfDispInQuickWnd;
						list.Add(@class);
					}
				}
				catch
				{
					throw;
				}
			}
			return list;
		}

		// Token: 0x06000E1C RID: 3612 RVA: 0x0005A334 File Offset: 0x00058534
		public static Keys smethod_3(Enum3 enum3_0)
		{
			return Class208.smethod_4((int)enum3_0);
		}

		// Token: 0x06000E1D RID: 3613 RVA: 0x0005A34C File Offset: 0x0005854C
		public static Keys smethod_4(int int_0)
		{
			Keys keyCode;
			try
			{
				Class278 @class = Class208.smethod_6(int_0);
				if (@class != null)
				{
					keyCode = @class.KeyCode;
					goto IL_3E;
				}
			}
			catch
			{
				Class46.smethod_4(new Exception("The hot key for id:" + int_0.ToString() + " cound not be found!"), true, null);
			}
			return Keys.None;
			IL_3E:
			return keyCode;
		}

		// Token: 0x06000E1E RID: 3614 RVA: 0x0005A3AC File Offset: 0x000585AC
		public static Class278 smethod_5(Enum3 enum3_0)
		{
			return Class208.smethod_6((int)enum3_0);
		}

		// Token: 0x06000E1F RID: 3615 RVA: 0x0005A3C4 File Offset: 0x000585C4
		public static Class278 smethod_6(int int_0)
		{
			Class208.Class210 @class = new Class208.Class210();
			@class.int_0 = int_0;
			return Class208.UsrHotKeyList.Find(new Predicate<Class278>(@class.method_0));
		}

		// Token: 0x06000E20 RID: 3616 RVA: 0x0005A3F8 File Offset: 0x000585F8
		private static Class278 smethod_7(int int_0)
		{
			Class208.Class211 @class = new Class208.Class211();
			@class.int_0 = int_0;
			return Class208.DefaultUsrHotKeyList.Find(new Predicate<Class278>(@class.method_0));
		}

		// Token: 0x17000234 RID: 564
		// (get) Token: 0x06000E21 RID: 3617 RVA: 0x0005A42C File Offset: 0x0005862C
		public static List<Class278> DefaultUsrHotKeyList
		{
			get
			{
				if (Class208.list_0 == null || Class208.list_0.Count == 0)
				{
					Class208.list_0 = Class208.smethod_0();
				}
				return Class208.list_0;
			}
		}

		// Token: 0x17000235 RID: 565
		// (get) Token: 0x06000E22 RID: 3618 RVA: 0x0005A460 File Offset: 0x00058660
		// (set) Token: 0x06000E23 RID: 3619 RVA: 0x000064C4 File Offset: 0x000046C4
		public static List<Class278> UsrHotKeyList
		{
			get
			{
				if (Class208.list_1 == null || Class208.list_1.Count == 0)
				{
					Class208.list_1 = Class208.smethod_1();
				}
				return Class208.list_1;
			}
			set
			{
				Class208.list_1 = value;
			}
		}

		// Token: 0x04000752 RID: 1874
		private static List<Class278> list_0;

		// Token: 0x04000753 RID: 1875
		private static List<Class278> list_1;

		// Token: 0x02000175 RID: 373
		[CompilerGenerated]
		private sealed class Class209
		{
			// Token: 0x06000E26 RID: 3622 RVA: 0x0005A494 File Offset: 0x00058694
			internal bool method_0(Class278 class278_1)
			{
				return class278_1.Id == this.class278_0.Id;
			}

			// Token: 0x04000754 RID: 1876
			public Class278 class278_0;
		}

		// Token: 0x02000176 RID: 374
		[CompilerGenerated]
		private sealed class Class210
		{
			// Token: 0x06000E28 RID: 3624 RVA: 0x0005A4B8 File Offset: 0x000586B8
			internal bool method_0(Class278 class278_0)
			{
				return class278_0.Id == this.int_0;
			}

			// Token: 0x04000755 RID: 1877
			public int int_0;
		}

		// Token: 0x02000177 RID: 375
		[CompilerGenerated]
		private sealed class Class211
		{
			// Token: 0x06000E2A RID: 3626 RVA: 0x0005A4D8 File Offset: 0x000586D8
			internal bool method_0(Class278 class278_0)
			{
				return class278_0.Id == this.int_0;
			}

			// Token: 0x04000756 RID: 1878
			public int int_0;
		}
	}
}
