﻿using System;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns27;
using ns28;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000213 RID: 531
	internal sealed class DataGridViewOpenTrans : Class290
	{
		// Token: 0x060015BE RID: 5566 RVA: 0x000900E8 File Offset: 0x0008E2E8
		public DataGridViewOpenTrans()
		{
			base.MouseClick += this.DataGridViewOpenTrans_MouseClick;
			base.MouseDoubleClick += this.DataGridViewOpenTrans_MouseDoubleClick;
			base.RowContextMenuStripNeeded += this.DataGridViewOpenTrans_RowContextMenuStripNeeded;
			base.Resize += this.DataGridViewOpenTrans_Resize;
		}

		// Token: 0x060015BF RID: 5567 RVA: 0x00008B81 File Offset: 0x00006D81
		protected override void vmethod_1()
		{
			base.SuspendLayout();
			base.DataSource = Base.Trading.CurrOpenTransList;
			this.method_11();
			base.ResumeLayout();
		}

		// Token: 0x060015C0 RID: 5568 RVA: 0x00008A1A File Offset: 0x00006C1A
		public void method_5()
		{
			base.DataSource = null;
			this.vmethod_1();
		}

		// Token: 0x060015C1 RID: 5569 RVA: 0x00090148 File Offset: 0x0008E348
		protected override void vmethod_0()
		{
			ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
			contextMenuStrip.Items.Add(this.method_9());
			Base.UI.smethod_73(contextMenuStrip);
			this.ContextMenuStrip = contextMenuStrip;
		}

		// Token: 0x060015C2 RID: 5570 RVA: 0x0009017C File Offset: 0x0008E37C
		private ToolStripMenuItem method_6()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = "otItemCloseCurr";
			toolStripMenuItem.Text = "平当前仓";
			toolStripMenuItem.Click += this.method_13;
			return toolStripMenuItem;
		}

		// Token: 0x060015C3 RID: 5571 RVA: 0x000901BC File Offset: 0x0008E3BC
		private ToolStripMenuItem method_7()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = "otItemCloseall2";
			toolStripMenuItem.Text = "全部平仓";
			toolStripMenuItem.Click += this.method_15;
			return toolStripMenuItem;
		}

		// Token: 0x060015C4 RID: 5572 RVA: 0x000901FC File Offset: 0x0008E3FC
		private ToolStripMenuItem method_8()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = "otItemStopLimit";
			toolStripMenuItem.Text = "止损止盈...";
			toolStripMenuItem.Click += this.method_16;
			return toolStripMenuItem;
		}

		// Token: 0x060015C5 RID: 5573 RVA: 0x0009023C File Offset: 0x0008E43C
		private ToolStripMenuItem method_9()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = "otItemCloseall";
			toolStripMenuItem.Text = "全部平仓";
			toolStripMenuItem.Click += this.method_15;
			toolStripMenuItem.Paint += this.method_17;
			return toolStripMenuItem;
		}

		// Token: 0x060015C6 RID: 5574 RVA: 0x0009028C File Offset: 0x0008E48C
		private ContextMenuStrip method_10()
		{
			ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
			contextMenuStrip.Items.Add(this.method_6());
			contextMenuStrip.Items.Add(this.method_7());
			contextMenuStrip.Items.Add(this.method_8());
			Base.UI.smethod_73(contextMenuStrip);
			this.ContextMenuStripOfRow = contextMenuStrip;
			return contextMenuStrip;
		}

		// Token: 0x060015C7 RID: 5575 RVA: 0x000902E8 File Offset: 0x0008E4E8
		protected override void vmethod_3(DataGridViewCellFormattingEventArgs dataGridViewCellFormattingEventArgs_0)
		{
			base.vmethod_3(dataGridViewCellFormattingEventArgs_0);
			int columnIndex = dataGridViewCellFormattingEventArgs_0.ColumnIndex;
			DataGridViewRow dataGridViewRow = base.Rows[dataGridViewCellFormattingEventArgs_0.RowIndex];
			DataGridViewCell dataGridViewCell_ = dataGridViewRow.Cells[dataGridViewCellFormattingEventArgs_0.ColumnIndex];
			string dataPropertyName = base.Columns[columnIndex].DataPropertyName;
			if (!(dataPropertyName == Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.CurrPrice)) && !(dataPropertyName == Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.Price)))
			{
				if (dataPropertyName == Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.ProfitRatio))
				{
					decimal d = (decimal)dataGridViewCellFormattingEventArgs_0.Value;
					if (d != 0m)
					{
						string text = d.ToString("P") + "%";
						if (!text.EndsWith("%"))
						{
							text += "%";
						}
						dataGridViewCellFormattingEventArgs_0.Value = text;
					}
					base.method_3(dataGridViewCell_);
				}
				else
				{
					if (dataPropertyName == Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.Profit))
					{
						if (dataGridViewCellFormattingEventArgs_0.Value == null)
						{
							return;
						}
						try
						{
							dataGridViewCellFormattingEventArgs_0.CellStyle.Format = "F" + base.method_4((decimal)dataGridViewCellFormattingEventArgs_0.Value).ToString();
							base.method_3(dataGridViewCell_);
							return;
						}
						catch (Exception exception_)
						{
							Class182.smethod_0(exception_);
							return;
						}
					}
					if (dataPropertyName == Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => t.LongOrShort))
					{
						base.method_2(dataGridViewCell_);
					}
				}
			}
			else
			{
				try
				{
					StkSymbol stkSymbol = SymbMgr.smethod_3((dataGridViewRow.DataBoundItem as ShownOpenTrans).SymbolID);
					dataGridViewCellFormattingEventArgs_0.CellStyle.Format = "F" + stkSymbol.DigitNb.ToString();
				}
				catch (Exception exception_2)
				{
					Class182.smethod_0(exception_2);
				}
			}
		}

		// Token: 0x060015C8 RID: 5576 RVA: 0x00090608 File Offset: 0x0008E808
		private void DataGridViewOpenTrans_MouseDoubleClick(object sender, MouseEventArgs e)
		{
			int columnIndex = base.HitTest(e.X, e.Y).ColumnIndex;
			int rowIndex = base.HitTest(e.X, e.Y).RowIndex;
			if (columnIndex >= 0 && rowIndex >= 0)
			{
				if (base.Columns[columnIndex].DataPropertyName != Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.CurrPrice) && rowIndex >= 0)
				{
					ShownOpenTrans shownOpenTrans = base.Rows[rowIndex].DataBoundItem as ShownOpenTrans;
					if (shownOpenTrans.SymbolID != Base.Data.CurrSelectedSymbol.ID)
					{
						Base.Data.smethod_62(shownOpenTrans.SymbolID, false);
					}
					else if ((Base.UI.Form.IfNoConfClsTransWhenDblClick || MessageBox.Show("以当前价格平仓" + (Base.UI.Form.IsInBlindTestMode ? string.Empty : ("（" + shownOpenTrans.SymblCode + "）")) + "？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes) && Base.Trading.smethod_57(shownOpenTrans))
					{
						this.method_5();
					}
				}
			}
		}

		// Token: 0x060015C9 RID: 5577 RVA: 0x00090760 File Offset: 0x0008E960
		private void DataGridViewOpenTrans_RowContextMenuStripNeeded(object sender, DataGridViewRowContextMenuStripNeededEventArgs e)
		{
			DataGridViewRow dataGridViewRow = base.Rows[e.RowIndex];
			dataGridViewRow.Selected = true;
			e.ContextMenuStrip = this.method_10();
			ShownOpenTrans shownOpenTrans = dataGridViewRow.DataBoundItem as ShownOpenTrans;
			e.ContextMenuStrip.Items[0].Tag = shownOpenTrans;
			ToolStripItem toolStripItem = e.ContextMenuStrip.Items[2];
			SymbDataSet symbDataSet = Base.Data.smethod_49(shownOpenTrans.SymbolID, false);
			if (symbDataSet != null && symbDataSet.HasValidDataSet && symbDataSet.CurrHisData.Close > 0.0)
			{
				toolStripItem.Tag = shownOpenTrans;
				toolStripItem.Enabled = true;
			}
			else
			{
				toolStripItem.Enabled = false;
			}
		}

		// Token: 0x060015CA RID: 5578 RVA: 0x0009080C File Offset: 0x0008EA0C
		private void method_11()
		{
			if (base.Columns.Count > 0)
			{
				base.AutoGenerateColumns = false;
				try
				{
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.OpenUnits)].DisplayIndex = 2;
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.TodayUnits)].DisplayIndex = 3;
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.UsableUnits)].DisplayIndex = 4;
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.Profit)].HeaderText = "浮动盈亏";
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.Profit)].DisplayIndex = 5;
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.ProfitRatio)].DisplayIndex = 6;
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.Price)].HeaderText = "开仓均价";
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.Price)].DisplayIndex = 7;
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.CurrPrice)].DisplayIndex = 8;
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.Units)].Visible = false;
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.Fee)].Visible = false;
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => t.Notes)].Visible = false;
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
				try
				{
					if (Base.UI.Form.IsInBlindTestMode)
					{
						if (!Base.UI.Form.IsSingleBlindTest)
						{
							base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => t.SymblCode)].Visible = false;
						}
						base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.CreateTime)].Visible = false;
						base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.UpdateTime)].Visible = false;
					}
					else
					{
						base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => t.SymblCode)].Visible = true;
						if (Base.UI.Form.IfShowIndividualShownOpenTrans)
						{
							base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.CreateTime)].Visible = true;
							base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.UpdateTime)].Visible = false;
						}
						else
						{
							base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.CreateTime)].Visible = false;
							base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.UpdateTime)].Visible = true;
							base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.UpdateTime)].HeaderText = "最新成交时间";
						}
					}
				}
				catch (Exception exception_2)
				{
					Class182.smethod_0(exception_2);
				}
				this.Refresh();
			}
		}

		// Token: 0x060015CB RID: 5579 RVA: 0x0009104C File Offset: 0x0008F24C
		private void DataGridViewOpenTrans_MouseClick(object sender, MouseEventArgs e)
		{
			int columnIndex = base.HitTest(e.X, e.Y).ColumnIndex;
			int rowIndex = base.HitTest(e.X, e.Y).RowIndex;
			if (columnIndex == 4 && rowIndex >= 0)
			{
				ShownOpenTrans shownOpenTrans_ = base.Rows[rowIndex].DataBoundItem as ShownOpenTrans;
				this.method_12(shownOpenTrans_);
			}
		}

		// Token: 0x060015CC RID: 5580 RVA: 0x00008BA2 File Offset: 0x00006DA2
		public void method_12(ShownOpenTrans shownOpenTrans_0)
		{
			if (shownOpenTrans_0 != null && Base.Data.smethod_52(shownOpenTrans_0.SymbolID) != null)
			{
				new SetStopLimitForm
				{
					Tag = shownOpenTrans_0,
					ShowInTaskbar = false
				}.ShowDialog();
			}
		}

		// Token: 0x060015CD RID: 5581 RVA: 0x000041AE File Offset: 0x000023AE
		private void DataGridViewOpenTrans_Resize(object sender, EventArgs e)
		{
		}

		// Token: 0x060015CE RID: 5582 RVA: 0x00008BCF File Offset: 0x00006DCF
		private void method_13(object sender, EventArgs e)
		{
			this.method_14();
		}

		// Token: 0x060015CF RID: 5583 RVA: 0x000910B0 File Offset: 0x0008F2B0
		public void method_14()
		{
			if (Base.Acct.CurrAccount.IsReadOnly)
			{
				Base.Acct.smethod_50();
			}
			else if (base.SelectedRows.Count > 0 && Base.Trading.smethod_57(base.SelectedRows[0].DataBoundItem as ShownOpenTrans))
			{
				this.method_5();
			}
		}

		// Token: 0x060015D0 RID: 5584 RVA: 0x00008BD9 File Offset: 0x00006DD9
		private void method_15(object sender, EventArgs e)
		{
			if (Base.Acct.CurrAccount.IsReadOnly)
			{
				Base.Acct.smethod_50();
			}
			else if (Base.Trading.smethod_217() && Base.Trading.smethod_55())
			{
				this.method_5();
			}
		}

		// Token: 0x060015D1 RID: 5585 RVA: 0x00091104 File Offset: 0x0008F304
		private void method_16(object sender, EventArgs e)
		{
			ShownOpenTrans shownOpenTrans_ = (sender as ToolStripMenuItem).Tag as ShownOpenTrans;
			this.method_12(shownOpenTrans_);
		}

		// Token: 0x060015D2 RID: 5586 RVA: 0x00008C04 File Offset: 0x00006E04
		private void method_17(object sender, PaintEventArgs e)
		{
			if (Base.Trading.CurrOpenTransList.Count < 1)
			{
				(sender as ToolStripMenuItem).Enabled = false;
			}
			else
			{
				(sender as ToolStripMenuItem).Enabled = true;
			}
		}

		// Token: 0x060015D3 RID: 5587 RVA: 0x0009112C File Offset: 0x0008F32C
		private void method_18()
		{
			int num = 0;
			VScrollBar vscrollBar = base.Controls.OfType<VScrollBar>().First<VScrollBar>();
			if (vscrollBar.Visible)
			{
				num = vscrollBar.Width;
			}
			int num2 = base.Parent.Width - num;
			int num3 = 0;
			decimal d;
			bool flag;
			if (!Base.UI.Form.IsInBlindTestMode)
			{
				d = 586m;
				flag = (num2 - num3 > d);
				num2 -= num3;
				try
				{
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => t.SymblCode)].Width = (flag ? Convert.ToInt32(Math.Floor(66 * num2 / d)) : 66);
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => t.LongOrShort)].Width = (flag ? Convert.ToInt32(Math.Floor(43 * num2 / d)) : 43);
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.TodayUnits)].Width = (flag ? Convert.ToInt32(Math.Floor(43 * num2 / d)) : 43);
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.UsableUnits)].Width = (flag ? Convert.ToInt32(Math.Floor(43 * num2 / d)) : 43);
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.ProfitRatio)].Width = (flag ? Convert.ToInt32(Math.Floor(74 * num2 / d)) : 74);
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.Price)].Width = (flag ? Convert.ToInt32(Math.Floor(70 * num2 / d)) : 70);
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.OpenUnits)].Width = (flag ? Convert.ToInt32(Math.Floor(43 * num2 / d)) : 43);
					base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.Profit)].Width = (flag ? Convert.ToInt32(Math.Floor(84 * num2 / d)) : 84);
					DataGridViewColumn dataGridViewColumn = base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.CreateTime)];
					if (!dataGridViewColumn.Visible)
					{
						dataGridViewColumn = base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.UpdateTime)];
					}
					dataGridViewColumn.Width = (flag ? Convert.ToInt32(Math.Floor(120 * num2 / d)) : 120);
					return;
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
					return;
				}
			}
			if (!Base.UI.Form.IsSingleBlindTest)
			{
				d = 406m;
				flag = (num2 - num3 > d);
				num2 -= num3;
			}
			else
			{
				d = 466m;
				flag = (num2 - num3 > d);
				num2 -= num3;
				base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => t.SymblCode)].Width = (flag ? Convert.ToInt32(Math.Floor(66 * num2 / d)) : 66);
			}
			try
			{
				base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => t.LongOrShort)].Width = (flag ? Convert.ToInt32(Math.Floor(43 * num2 / d)) : 43);
				base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.TodayUnits)].Width = (flag ? Convert.ToInt32(Math.Floor(43 * num2 / d)) : 43);
				base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.UsableUnits)].Width = (flag ? Convert.ToInt32(Math.Floor(43 * num2 / d)) : 43);
				base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.ProfitRatio)].Width = (flag ? Convert.ToInt32(Math.Floor(73 * num2 / d)) : 73);
				base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.OpenUnits)].Width = (flag ? Convert.ToInt32(Math.Floor(43 * num2 / d)) : 43);
				base.Columns[Utility.GetPropertyName<ShownOpenTrans>((ShownOpenTrans t) => (object)t.Profit)].Width = (flag ? Convert.ToInt32(Math.Floor(85 * num2 / d)) : 85);
			}
			catch (Exception exception_2)
			{
				Class182.smethod_0(exception_2);
			}
		}

		// Token: 0x1700038B RID: 907
		// (get) Token: 0x060015D4 RID: 5588 RVA: 0x00091A8C File Offset: 0x0008FC8C
		// (set) Token: 0x060015D5 RID: 5589 RVA: 0x00008C2F File Offset: 0x00006E2F
		public ContextMenuStrip ContextMenuStripOfRow { get; set; }

		// Token: 0x04000B12 RID: 2834
		[CompilerGenerated]
		private ContextMenuStrip contextMenuStrip_0;
	}
}
