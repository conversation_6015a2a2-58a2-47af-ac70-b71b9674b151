﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using TEx.Chart;

namespace TEx
{
	// Token: 0x0200006C RID: 108
	[Serializable]
	internal sealed class DrawGannFan : DrawTrendSpeed, ISerializable
	{
		// Token: 0x060003F2 RID: 1010 RVA: 0x00003AD9 File Offset: 0x00001CD9
		public DrawGannFan()
		{
		}

		// Token: 0x060003F3 RID: 1011 RVA: 0x00003AE1 File Offset: 0x00001CE1
		public DrawGannFan(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = "江恩角度线";
		}

		// Token: 0x060003F4 RID: 1012 RVA: 0x00003AFD File Offset: 0x00001CFD
		protected DrawGannFan(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x060003F5 RID: 1013 RVA: 0x00003B0E File Offset: 0x00001D0E
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x060003F6 RID: 1014 RVA: 0x00021EEC File Offset: 0x000200EC
		protected override List<LineObj> vmethod_24(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<LineObj> list = new List<LineObj>();
			double num = Math.Min(double_1, double_3);
			double num2 = Math.Max(double_1, double_3);
			double num3 = Math.Min(double_2, double_4);
			double num4 = Math.Max(double_2, double_4);
			double num5 = Math.Abs(double_4 - double_2);
			double num6 = Math.Abs(double_3 - double_1);
			double[] array = new double[5];
			double[] array2 = new double[4];
			List<DrawSublineParam> sublineParamList = base.SublineParamList;
			for (int i = 0; i < array2.Length; i++)
			{
				DrawSublineParam drawSublineParam = sublineParamList[i];
				if (drawSublineParam.Enabled)
				{
					array2[i] = num + num6 * drawSublineParam.Value;
				}
			}
			double double_5 = num;
			double double_6 = num2;
			double double_7;
			double double_8;
			if ((double_1 < double_3 && double_4 > double_2) || (double_3 < double_1 && double_2 > double_4))
			{
				double_7 = num3;
				double_8 = num4;
				array[0] = num4;
				for (int j = 1; j < array.Length; j++)
				{
					DrawSublineParam drawSublineParam2 = sublineParamList[4 - j];
					if (drawSublineParam2.Enabled)
					{
						array[j] = num3 + num5 * drawSublineParam2.Value;
					}
				}
			}
			else
			{
				double_7 = num4;
				double_8 = num3;
				for (int k = 0; k < array.Length - 1; k++)
				{
					DrawSublineParam drawSublineParam3 = sublineParamList[k];
					if (drawSublineParam3.Enabled)
					{
						array[k] = num4 - num5 * drawSublineParam3.Value;
					}
				}
				array[4] = num3;
			}
			foreach (double num7 in array)
			{
				if (num7 != 0.0)
				{
					base.method_40(list, chartCS_1, double_5, double_7, double_6, num7, string_5);
				}
			}
			foreach (double num8 in array2)
			{
				if (num8 != 0.0)
				{
					base.method_40(list, chartCS_1, double_5, double_7, num8, double_8, string_5);
				}
			}
			return list;
		}

		// Token: 0x060003F7 RID: 1015 RVA: 0x000220B8 File Offset: 0x000202B8
		protected override List<DrawSublineParam> vmethod_22()
		{
			List<double> list_ = new List<double>(new double[]
			{
				0.125,
				0.25,
				0.33,
				0.5
			});
			return base.method_28(list_, 0.001, 0.999, 3);
		}
	}
}
