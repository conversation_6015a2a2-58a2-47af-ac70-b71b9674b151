﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Xml.Linq;
using TEx.Inds;
using TEx.SIndicator;

namespace ns11
{
	// Token: 0x02000306 RID: 774
	internal static class Class422
	{
		// Token: 0x06002166 RID: 8550 RVA: 0x000E450C File Offset: 0x000E270C
		public static string smethod_0(XDocument xdocument_0)
		{
			string value = xdocument_0.Element("Namespace").Attribute("Name").Value;
			string text = Class422.smethod_1(xdocument_0.Element("Namespace").Element("Programs"));
			return string.Format("using system;\r\n  using TEx.Inds; \r\n namespace {0}\r\n {1} \r\n {2} \r\n{3}", new object[]
			{
				value,
				"{",
				text,
				"}"
			});
		}

		// Token: 0x06002167 RID: 8551 RVA: 0x000E4590 File Offset: 0x000E2790
		private static string smethod_1(XElement xelement_0)
		{
			string text = "";
			foreach (XElement xelement_ in xelement_0.Elements("Program"))
			{
				text += Class422.smethod_2(xelement_);
			}
			return text;
		}

		// Token: 0x06002168 RID: 8552 RVA: 0x000E45FC File Offset: 0x000E27FC
		private static string smethod_2(XElement xelement_0)
		{
			string text = string.Format(" public class {0}:SIndicatorBase\r\n", xelement_0.Attribute("Name").Value);
			text += "{\r\n";
			text += string.Format(" public string Descript = {0};\r\n", xelement_0.Element("Descript").Value);
			text += string.Format("public bool MainK = {0};\r\n", xelement_0.Attribute("MainK").Value);
			XElement xelement = xelement_0.Element("Params");
			if (xelement != null)
			{
				foreach (XElement xelement2 in xelement.Elements("Param"))
				{
					text += string.Format("public double {0}={1}\r\n", xelement2.Attribute("Name").Value, xelement2.Attribute("Value").Value);
				}
			}
			text += " public Maes(DataProvider dp):base(dp){}\r\n";
			text += "public void Run(DataProvider dp){ if (!CheckIFRun(dp)) return; GetIndDatas(Code()); }\r\n";
			text += "private SIndData[] Code()\r\n";
			text += "{\r\n";
			text += Class422.smethod_3(xelement_0.Element("Code").Value);
			text += "}\r\n";
			return text;
		}

		// Token: 0x06002169 RID: 8553 RVA: 0x000E4778 File Offset: 0x000E2978
		private static string smethod_3(string string_0)
		{
			List<string> list = new List<string>();
			string[] array = string_0.Split(new char[]
			{
				';'
			});
			string text = "";
			for (int i = 0; i < array.Length; i++)
			{
				string[] array2 = array[i].Trim().Split(new char[]
				{
					','
				});
				text += Class422.smethod_4(array2[0].Trim(), list);
			}
			string text2 = "";
			for (int j = 0; j < list.Count; j++)
			{
				text2 = text2 + list[j].Trim() + ",";
			}
			if (text2 != "")
			{
				text2.Remove(text2.Count<char>() - 1);
				string text3 = "return new SIndData[] {";
				text3 += text2;
				text3 += " };";
				text += text3;
			}
			return text;
		}

		// Token: 0x0600216A RID: 8554 RVA: 0x000E4868 File Offset: 0x000E2A68
		private static string smethod_4(string string_0, List<string> list_0)
		{
			int num = string_0.Count<char>();
			int num2 = string_0.IndexOf(":=", 0, num);
			string text;
			string arg;
			if (num2 == -1)
			{
				int num3 = string_0.IndexOf(":", 0, num);
				if (num3 == -1)
				{
					throw new IndexOutOfRangeException();
				}
				text = string_0.Substring(0, num3);
				arg = string_0.Substring(num3 + 1, num - num3 - 1);
				list_0.Add(text);
			}
			else
			{
				text = string_0.Substring(0, num2);
				arg = string_0.Substring(num2 + 2, num - num2 - 1);
			}
			return string.Format("SIndData {0} = {1}; {0}.Name=\"{0}\"; \r\n", text, arg);
		}

		// Token: 0x0600216B RID: 8555 RVA: 0x000E4904 File Offset: 0x000E2B04
		public static void smethod_5(string string_0, string string_1, UserDefineIndScript userDefineIndScript_0)
		{
			XDocument xdocument = new XDocument();
			if (File.Exists(string_0))
			{
				xdocument = XDocument.Load(string_0);
			}
			XElement xelement = xdocument.Element("Namespace");
			if (xelement == null)
			{
				xelement = new XElement("Namespace", new XAttribute("Name", "自定义指标"));
				xdocument.Add(xelement);
			}
			XElement xelement2 = xelement.Element("Programs");
			if (xelement2 == null)
			{
				xelement2 = new XElement("Programs");
				xelement.Add(xelement2);
			}
			XElement content = Class422.smethod_6(userDefineIndScript_0);
			foreach (XElement xelement3 in xelement2.Elements("Program"))
			{
				if (xelement3.Attribute("Name").Value == userDefineIndScript_0.Name)
				{
					xelement3.Remove();
					break;
				}
			}
			xelement2.Add(content);
			xdocument.Save(string_0);
			string contents = Class422.smethod_0(xdocument);
			File.WriteAllText(string_1, contents);
		}

		// Token: 0x0600216C RID: 8556 RVA: 0x000E4A30 File Offset: 0x000E2C30
		private static XElement smethod_6(UserDefineIndScript userDefineIndScript_0)
		{
			XElement xelement = new XElement("Program", new object[]
			{
				new XAttribute("Name", userDefineIndScript_0.Name),
				new XAttribute("MainK", userDefineIndScript_0.MainK)
			});
			XElement content = new XElement("Descript", userDefineIndScript_0.Script);
			xelement.Add(content);
			XElement xelement2 = new XElement("Params");
			xelement.Add(xelement2);
			for (int i = 0; i < userDefineIndScript_0.UserDefineParams.Count; i++)
			{
				UserDefineParam userDefineParam = userDefineIndScript_0.UserDefineParams[i];
				XElement content2 = new XElement("Param", new object[]
				{
					new XAttribute("Name", userDefineParam.Name),
					new XAttribute("Max", userDefineParam.Max),
					new XAttribute("Min", userDefineParam.Min),
					new XAttribute("Value", userDefineParam.Value),
					new XAttribute("Step", userDefineParam.Step)
				});
				xelement2.Add(content2);
			}
			XElement content3 = new XElement("Code", userDefineIndScript_0.Code);
			xelement.Add(content3);
			return xelement;
		}
	}
}
