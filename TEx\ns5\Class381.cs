﻿using System;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using ns28;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns5
{
	// Token: 0x020002E4 RID: 740
	internal class Class381 : ShapeCurve
	{
		// Token: 0x060020E6 RID: 8422 RVA: 0x000E1CE0 File Offset: 0x000DFEE0
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			Ind_AngleLine lineItem_ = zedGraphControl_0.GraphPane.AddAngleLine(base.IndData.Name, base.DataView, color_0, SymbolType.None, this.double_0);
			base.method_3(string_0, lineItem_);
			this.int_0 = 1;
		}

		// Token: 0x060020E7 RID: 8423 RVA: 0x000E1D44 File Offset: 0x000DFF44
		protected override PointPair vmethod_0(int int_1, DataArray dataArray_1)
		{
			Class381.Class402 @class = new Class381.Class402();
			@class.dataArray_0 = dataArray_1;
			DateTime dateTime = base.method_0(int_1);
			if (@class.dataArray_0.Data.Length < int_1 + 1)
			{
				throw new Exception("数据长度溢出。");
			}
			double num = @class.dataArray_0.Data[int_1];
			if (@class.dataArray_0.OtherDataArrayList.Count != 3)
			{
				throw new Exception("TrenderLines数据不足，请检查。");
			}
			if (@class.dataArray_0.OtherDataArrayList.Any(new Func<DataArray, bool>(@class.method_0)))
			{
				throw new Exception("TrenderLines长度不相等。请检查。");
			}
			double y = @class.dataArray_0.Data[int_1];
			int num2 = (int)@class.dataArray_0.OtherDataArrayList[0].Data[int_1];
			int num3 = (int)@class.dataArray_0.OtherDataArrayList[1].Data[int_1];
			double y2 = @class.dataArray_0.OtherDataArrayList[2].Data[int_1];
			PointPair result;
			if (num2 == 1 && this.int_0 == 1)
			{
				this.int_0 = 2;
				result = new PointPair(new XDate(dateTime), y, 1.0);
			}
			else if (num3 == 1 && this.int_0 == 2)
			{
				this.int_0 = 1;
				result = new PointPair(new XDate(dateTime), y2, 2.0);
			}
			else
			{
				result = new PointPair(new XDate(dateTime), double.NaN, double.NaN);
			}
			return result;
		}

		// Token: 0x060020E8 RID: 8424 RVA: 0x000E1EC0 File Offset: 0x000E00C0
		public Class381(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
			try
			{
				this.double_0 = (double)dataArray_1.SingleData["RATIO"];
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x0400101E RID: 4126
		protected double double_0;

		// Token: 0x0400101F RID: 4127
		protected int int_0 = -1;

		// Token: 0x020002E5 RID: 741
		[CompilerGenerated]
		private sealed class Class402
		{
			// Token: 0x060020EA RID: 8426 RVA: 0x000E1F14 File Offset: 0x000E0114
			internal bool method_0(DataArray dataArray_1)
			{
				return dataArray_1.Data.Length != this.dataArray_0.Data.Length;
			}

			// Token: 0x04001020 RID: 4128
			public DataArray dataArray_0;
		}
	}
}
