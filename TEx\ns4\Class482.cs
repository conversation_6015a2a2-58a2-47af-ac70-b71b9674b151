﻿using System;
using System.Collections.Generic;
using System.Drawing;
using TEx.ImportTrans.Captcha;

namespace ns4
{
	// Token: 0x0200037E RID: 894
	internal abstract class Class482
	{
		// Token: 0x060024F7 RID: 9463
		public abstract void vmethod_0(string string_0);

		// Token: 0x060024F8 RID: 9464
		public abstract void vmethod_1(string string_0);

		// Token: 0x060024F9 RID: 9465
		public abstract void vmethod_2(string string_0);

		// Token: 0x060024FA RID: 9466
		public abstract Reg[] vmethod_3(Bitmap bitmap_0, List<Rectangle> list_0);

		// Token: 0x060024FB RID: 9467
		public abstract Reg[] vmethod_4(List<List<Point>> list_0);

		// Token: 0x060024FC RID: 9468
		public abstract string vmethod_5(Bitmap bitmap_0);
	}
}
