using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using ns11;
using ns17;
using ns19;
using ns22;
using ns23;
using ns25;
using ns28;
using ns30;
using TEx.Comn;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000042 RID: 66
	internal sealed class SymbDataSet
	{
		// Token: 0x14000006 RID: 6
		// (add) Token: 0x0600020D RID: 525 RVA: 0x00018870 File Offset: 0x00016A70
		// (remove) Token: 0x0600020E RID: 526 RVA: 0x000188A8 File Offset: 0x00016AA8
		public event EventHandler GenHisDataSetStarted
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600020F RID: 527 RVA: 0x000032D3 File Offset: 0x000014D3
		private void method_0()
		{
			this.method_16(this.eventHandler_0);
		}

		// Token: 0x14000007 RID: 7
		// (add) Token: 0x06000210 RID: 528 RVA: 0x000188E0 File Offset: 0x00016AE0
		// (remove) Token: 0x06000211 RID: 529 RVA: 0x00018918 File Offset: 0x00016B18
		public event EventHandler GenHisDataSetCanceled
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000212 RID: 530 RVA: 0x000032E3 File Offset: 0x000014E3
		private void method_1()
		{
			this.method_16(this.eventHandler_1);
		}

		// Token: 0x14000008 RID: 8
		// (add) Token: 0x06000213 RID: 531 RVA: 0x00018950 File Offset: 0x00016B50
		// (remove) Token: 0x06000214 RID: 532 RVA: 0x00018988 File Offset: 0x00016B88
		public event EventHandler HDPS_1hChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_2;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_2, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_2;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_2, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000215 RID: 533 RVA: 0x000032F3 File Offset: 0x000014F3
		private void method_2()
		{
			this.method_16(this.eventHandler_2);
		}

		// Token: 0x14000009 RID: 9
		// (add) Token: 0x06000216 RID: 534 RVA: 0x000189C0 File Offset: 0x00016BC0
		// (remove) Token: 0x06000217 RID: 535 RVA: 0x000189F8 File Offset: 0x00016BF8
		public event EventHandler GenHisDataSetCompleted
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_3;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_3, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_3;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_3, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000218 RID: 536 RVA: 0x00003303 File Offset: 0x00001503
		private void method_3()
		{
			this.method_16(this.eventHandler_3);
		}

		// Token: 0x1400000A RID: 10
		// (add) Token: 0x06000219 RID: 537 RVA: 0x00018A30 File Offset: 0x00016C30
		// (remove) Token: 0x0600021A RID: 538 RVA: 0x00018A68 File Offset: 0x00016C68
		public event EventHandler HisDataSetUpdatedWithNewStartEndDate
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_4;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_4, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_4;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_4, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600021B RID: 539 RVA: 0x00003313 File Offset: 0x00001513
		private void method_4()
		{
			this.method_16(this.eventHandler_4);
		}

		// Token: 0x1400000B RID: 11
		// (add) Token: 0x0600021C RID: 540 RVA: 0x00018AA0 File Offset: 0x00016CA0
		// (remove) Token: 0x0600021D RID: 541 RVA: 0x00018AD8 File Offset: 0x00016CD8
		public event EventHandler GettingLocalHisDataStarted
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_5;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_5, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_5;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_5, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600021E RID: 542 RVA: 0x00003323 File Offset: 0x00001523
		private void method_5()
		{
			this.method_16(this.eventHandler_5);
		}

		// Token: 0x1400000C RID: 12
		// (add) Token: 0x0600021F RID: 543 RVA: 0x00018B10 File Offset: 0x00016D10
		// (remove) Token: 0x06000220 RID: 544 RVA: 0x00018B48 File Offset: 0x00016D48
		public event EventHandler DownloadDataError
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_6;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_6, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_6;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_6, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000221 RID: 545 RVA: 0x00003333 File Offset: 0x00001533
		private void method_6()
		{
			this.method_16(this.eventHandler_6);
		}

		// Token: 0x1400000D RID: 13
		// (add) Token: 0x06000222 RID: 546 RVA: 0x00018B80 File Offset: 0x00016D80
		// (remove) Token: 0x06000223 RID: 547 RVA: 0x00018BB8 File Offset: 0x00016DB8
		public event EventHandler AppendingData
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_7;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_7, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_7;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_7, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000224 RID: 548 RVA: 0x00003343 File Offset: 0x00001543
		public void method_7()
		{
			this.method_16(this.eventHandler_7);
		}

		// Token: 0x1400000E RID: 14
		// (add) Token: 0x06000225 RID: 549 RVA: 0x00018BF0 File Offset: 0x00016DF0
		// (remove) Token: 0x06000226 RID: 550 RVA: 0x00018C28 File Offset: 0x00016E28
		public event EventHandler HisDataAppended
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_8;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_8, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_8;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_8, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000227 RID: 551 RVA: 0x00003353 File Offset: 0x00001553
		private void method_8()
		{
			this.method_16(this.eventHandler_8);
		}

		// Token: 0x1400000F RID: 15
		// (add) Token: 0x06000228 RID: 552 RVA: 0x00018C60 File Offset: 0x00016E60
		// (remove) Token: 0x06000229 RID: 553 RVA: 0x00018C98 File Offset: 0x00016E98
		public event EventHandler CurrHisDataChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_9;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_9, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_9;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_9, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600022A RID: 554 RVA: 0x00003363 File Offset: 0x00001563
		private void method_9()
		{
			this.method_16(this.eventHandler_9);
		}

		// Token: 0x14000010 RID: 16
		// (add) Token: 0x0600022B RID: 555 RVA: 0x00018CD0 File Offset: 0x00016ED0
		// (remove) Token: 0x0600022C RID: 556 RVA: 0x00018D08 File Offset: 0x00016F08
		public event EventHandler CurrDateChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_10;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_10, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_10;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_10, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600022D RID: 557 RVA: 0x00003373 File Offset: 0x00001573
		private void method_10()
		{
			this.method_16(this.eventHandler_10);
		}

		// Token: 0x14000011 RID: 17
		// (add) Token: 0x0600022E RID: 558 RVA: 0x00018D40 File Offset: 0x00016F40
		// (remove) Token: 0x0600022F RID: 559 RVA: 0x00018D78 File Offset: 0x00016F78
		public event EventHandler SpltDayEnter
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_11;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_11, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_11;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_11, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000230 RID: 560 RVA: 0x00003383 File Offset: 0x00001583
		private void method_11()
		{
			this.method_16(this.eventHandler_11);
		}

		// Token: 0x14000012 RID: 18
		// (add) Token: 0x06000231 RID: 561 RVA: 0x00018DB0 File Offset: 0x00016FB0
		// (remove) Token: 0x06000232 RID: 562 RVA: 0x00018DE8 File Offset: 0x00016FE8
		public event EventHandler SpltDayLeave
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_12;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_12, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_12;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_12, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000233 RID: 563 RVA: 0x00003393 File Offset: 0x00001593
		private void method_12()
		{
			this.method_16(this.eventHandler_12);
		}

		// Token: 0x14000013 RID: 19
		// (add) Token: 0x06000234 RID: 564 RVA: 0x00018E20 File Offset: 0x00017020
		// (remove) Token: 0x06000235 RID: 565 RVA: 0x00018E58 File Offset: 0x00017058
		public event EventHandler SpltDaySpan
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_13;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_13, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_13;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_13, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000236 RID: 566 RVA: 0x000033A3 File Offset: 0x000015A3
		private void method_13()
		{
			this.method_16(this.eventHandler_13);
		}

		// Token: 0x14000014 RID: 20
		// (add) Token: 0x06000237 RID: 567 RVA: 0x00018E90 File Offset: 0x00017090
		// (remove) Token: 0x06000238 RID: 568 RVA: 0x00018EC8 File Offset: 0x000170C8
		public event Delegate5 RetrievingData
		{
			[CompilerGenerated]
			add
			{
				Delegate5 @delegate = this.delegate5_0;
				Delegate5 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate5 value2 = (Delegate5)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate5>(ref this.delegate5_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate5 @delegate = this.delegate5_0;
				Delegate5 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate5 value2 = (Delegate5)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate5>(ref this.delegate5_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06000239 RID: 569 RVA: 0x00018F00 File Offset: 0x00017100
		private void method_14(string string_1, bool bool_1)
		{
			EventArgs5 e = new EventArgs5(string_1, bool_1);
			Delegate5 @delegate = this.delegate5_0;
			if (@delegate != null)
			{
				@delegate(e);
			}
		}

		// Token: 0x14000015 RID: 21
		// (add) Token: 0x0600023A RID: 570 RVA: 0x00018F2C File Offset: 0x0001712C
		// (remove) Token: 0x0600023B RID: 571 RVA: 0x00018F64 File Offset: 0x00017164
		public event EventHandler RetrieveDataCompleted
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_14;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_14, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_14;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_14, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600023C RID: 572 RVA: 0x000033B3 File Offset: 0x000015B3
		private void method_15()
		{
			this.method_16(this.eventHandler_14);
		}

		// Token: 0x0600023D RID: 573 RVA: 0x00018F9C File Offset: 0x0001719C
		private void method_16(EventHandler eventHandler_15)
		{
			EventArgs e = new EventArgs();
			if (eventHandler_15 != null)
			{
				eventHandler_15(this, e);
			}
		}

		// Token: 0x0600023E RID: 574 RVA: 0x000033C3 File Offset: 0x000015C3
		public SymbDataSet(StkSymbol symbl, bool ifGetHdsFromLocal, DateTime? lastSymbDT) : this(symbl.ID, ifGetHdsFromLocal, lastSymbDT)
		{
		}

		// Token: 0x0600023F RID: 575 RVA: 0x00018FBC File Offset: 0x000171BC
		public SymbDataSet(int symbId, bool ifGetHdsFromLocal, DateTime? lastSymbDT)
		{
			this.CurrSymbol = this.method_21(symbId);
			if (ifGetHdsFromLocal && Base.Acct.CurrAccount.LastSymbDT != null)
			{
				HisDataSet hisDataSet = this.method_48();
				if (hisDataSet == null)
				{
					this.method_19(symbId, lastSymbDT);
				}
				else
				{
					if (lastSymbDT == null)
					{
						if (Base.Acct.CurrAccount.LastSymbIdAndDtList != null)
						{
							try
							{
								DateTime value = Base.Acct.CurrAccount.LastSymbIdAndDtList.SingleOrDefault(new Func<KeyValuePair<int, DateTime>, bool>(this.method_111)).Value;
								if (value != default(DateTime))
								{
									lastSymbDT = new DateTime?(value);
								}
								goto IL_FC;
							}
							catch
							{
								if (Base.Acct.CurrAccount.LastSymbDT != null)
								{
									lastSymbDT = new DateTime?(Base.Acct.CurrAccount.LastSymbDT.Value);
								}
								goto IL_FC;
							}
						}
						if (Base.Acct.CurrAccount.LastSymbDT != null)
						{
							lastSymbDT = new DateTime?(Base.Acct.CurrAccount.LastSymbDT.Value);
						}
					}
					IL_FC:
					if (lastSymbDT != null && hisDataSet.method_29() && lastSymbDT >= hisDataSet.FetchedHDDataStartDate && lastSymbDT <= hisDataSet.FetchedHDDataEndDate)
					{
						this.CurrHisDataSet = hisDataSet;
						this.CurrHisDataSet.SymbDataSet = this;
						this.CurrHisDataSet.method_15(lastSymbDT.Value);
						this.CurrHisDataSet.method_4(lastSymbDT.Value);
						this.DateTimeOfLastRec = this.CurrHisDataSet.CurrHisData.Date;
					}
					else
					{
						this.method_19(symbId, lastSymbDT);
					}
				}
			}
			else
			{
				this.method_19(symbId, lastSymbDT);
			}
			if (this.CurrSymbol.IsStock)
			{
				this.CurrSymbStSpltList = this.method_71(this.CurrSymbol.ID);
			}
		}

		// Token: 0x06000240 RID: 576 RVA: 0x000033D3 File Offset: 0x000015D3
		public void method_17(StkSymbol stkSymbol_1, DateTime? nullable_0)
		{
			this.method_18(stkSymbol_1, nullable_0, true);
		}

		// Token: 0x06000241 RID: 577 RVA: 0x000033E0 File Offset: 0x000015E0
		public void method_18(StkSymbol stkSymbol_1, DateTime? nullable_0, bool bool_1)
		{
			this.method_20(stkSymbol_1.ID, nullable_0, bool_1);
		}

		// Token: 0x06000242 RID: 578 RVA: 0x000033F2 File Offset: 0x000015F2
		public void method_19(int int_1, DateTime? nullable_0)
		{
			this.method_20(int_1, nullable_0, true);
		}

		// Token: 0x06000243 RID: 579 RVA: 0x000191C0 File Offset: 0x000173C0
		public void method_20(int int_1, DateTime? nullable_0, bool bool_1)
		{
			if (nullable_0 == null)
			{
				nullable_0 = Base.Acct.CurrAccount.LastSymbDT;
			}
			if (nullable_0 != null)
			{
				DateTime? dateTime = nullable_0;
				DateTime d = default(DateTime);
				if (dateTime == null || (dateTime != null && dateTime.GetValueOrDefault() != d))
				{
					if (this.method_25(this.stkSymbol_0.ID, nullable_0) == null && this.stkSymbol_0.IsFutures && !this.stkSymbol_0.IsFuturesMI)
					{
						StkSymbol stkSymbol = SymbMgr.smethod_13(this.stkSymbol_0.MIStkCode);
						if (stkSymbol != null)
						{
							this.CurrSymbol = this.method_21(stkSymbol.ID);
							this.method_25(this.CurrSymbol.ID, nullable_0);
						}
					}
					this.DateTimeOfLastRec = nullable_0.Value;
					return;
				}
			}
			DateTime? dateTime2 = this.method_24(this.CurrSymbol.ID);
			if (TApp.IsTrialUser)
			{
				if (dateTime2 == null || !(dateTime2.Value >= this.CurrStkMeta.BeginDate.Value))
				{
					try
					{
						dateTime2 = new DateTime?(this.sortedList_0.First(new Func<KeyValuePair<DateTime, HisData>, bool>(this.method_112)).Key);
					}
					catch
					{
						Class182.smethod_0(new Exception("Exception: firstDT = _HisDataList.First(h => h.Key >= CurrStkMeta.BeginDate.Value).Key"));
					}
				}
				if (bool_1 && dateTime2 != null && dateTime2.Value >= Base.Data.MaxSymbDT)
				{
					Base.Acct.CurrAccount.LastSymbDT = new DateTime?(dateTime2.Value);
				}
			}
			else if (bool_1 && dateTime2 != null && dateTime2.Value >= Base.Data.MaxSymbDT)
			{
				Base.Acct.CurrAccount.LastSymbDT = new DateTime?(this.method_26());
			}
		}

		// Token: 0x06000244 RID: 580 RVA: 0x000193A4 File Offset: 0x000175A4
		private StkSymbol method_21(int int_1)
		{
			return Base.Acct.smethod_48(int_1);
		}

		// Token: 0x06000245 RID: 581 RVA: 0x000193BC File Offset: 0x000175BC
		private StkSymbol method_22()
		{
			StkSymbol result;
			if (this.stkSymbol_0 == null)
			{
				result = this.method_21(SymbMgr.smethod_27(Base.Data.UsrStkSymbols, Base.Acct.CurrAccount).ID);
			}
			else
			{
				result = this.method_21(this.stkSymbol_0.ID);
			}
			return result;
		}

		// Token: 0x06000246 RID: 582 RVA: 0x00019404 File Offset: 0x00017604
		public DateTime? method_23(DateTime? nullable_0)
		{
			return this.method_25(this.CurrSymbol.ID, nullable_0);
		}

		// Token: 0x06000247 RID: 583 RVA: 0x00019428 File Offset: 0x00017628
		public DateTime? method_24(int int_1)
		{
			return this.method_25(int_1, null);
		}

		// Token: 0x06000248 RID: 584 RVA: 0x0001944C File Offset: 0x0001764C
		public DateTime? method_25(int int_1, DateTime? nullable_0)
		{
			this.method_0();
			StkSymbol stkSymbol = SymbMgr.smethod_3(int_1);
			DateTime dateTime_;
			if (nullable_0 != null)
			{
				dateTime_ = nullable_0.Value;
			}
			else
			{
				DateTime value = this.CurrStkMeta.BeginDate.Value;
				DateTime value2 = this.CurrStkMeta.EndDate.Value;
				if (value < value2)
				{
					dateTime_ = Utility.GetRandomDate(value, value2);
				}
				else
				{
					dateTime_ = this.CurrStkMeta.EndDate.Value;
				}
			}
			if (this.stkSymbol_0 == null || this.stkSymbol_0.ID != stkSymbol.ID)
			{
				this.CurrSymbol = this.method_21(stkSymbol.ID);
				if (!this.method_27(dateTime_))
				{
					this.method_1();
					return null;
				}
			}
			DateTime? result;
			if ((this.sortedList_0 == null || !this.sortedList_0.Any<KeyValuePair<DateTime, HisData>>()) && !this.method_27(dateTime_))
			{
				this.method_1();
				result = null;
			}
			else
			{
				DateTime dateTime;
				if (nullable_0 != null)
				{
					dateTime = nullable_0.Value;
				}
				else
				{
					dateTime = this.method_26();
				}
				if (this.sortedList_0 != null && this.sortedList_0.Keys.First<DateTime>().Year <= dateTime_.Year && this.sortedList_0.Keys.Last<DateTime>().Year >= dateTime_.Year)
				{
					this.CurrHisDataSet = new HisDataSet(this, dateTime);
				}
				else
				{
					if (!this.method_27(dateTime_))
					{
						this.method_1();
						return null;
					}
					if (this.sortedList_0 != null)
					{
						if (this.sortedList_0.Keys.Last<DateTime>() < dateTime)
						{
							dateTime = this.sortedList_0.Keys.Last<DateTime>();
						}
						else if (this.sortedList_0.Keys.First<DateTime>() > dateTime)
						{
							dateTime = this.sortedList_0.Keys.First<DateTime>();
						}
					}
					this.CurrHisDataSet = new HisDataSet(this, dateTime);
				}
				if (this.CurrDate != null)
				{
					this.DateTimeOfLastRec = this.CurrDate.Value;
				}
				this.method_31(dateTime.Year);
				this.method_3();
				result = new DateTime?(dateTime);
			}
			return result;
		}

		// Token: 0x06000249 RID: 585 RVA: 0x00019698 File Offset: 0x00017898
		private DateTime method_26()
		{
			DateTime dateTime = this.sortedList_0.Keys.First<DateTime>();
			if (dateTime < this.CurrStkMeta.BeginDate.Value.Date)
			{
				dateTime = this.CurrStkMeta.BeginDate.Value.Date;
			}
			DateTime dateTime2 = this.sortedList_0.Keys.Last<DateTime>();
			if (dateTime2 > this.CurrStkMeta.EndDate.Value.Date)
			{
				dateTime2 = this.CurrStkMeta.EndDate.Value.Date;
			}
			DateTime result = dateTime2;
			try
			{
				SymbDataSet.Class23 @class = new SymbDataSet.Class23();
				if (dateTime < dateTime2)
				{
					@class.dateTime_0 = Utility.GetRandomDate(dateTime, dateTime2);
					result = this.sortedList_0.FirstOrDefault(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_0)).Key;
				}
			}
			catch
			{
			}
			return result;
		}

		// Token: 0x0600024A RID: 586 RVA: 0x000197A8 File Offset: 0x000179A8
		public bool method_27(DateTime dateTime_1)
		{
			SortedList<DateTime, HisData> sortedList = this.method_28(this.CurrSymbol, dateTime_1.Year);
			bool result;
			if (sortedList != null && sortedList.Any<KeyValuePair<DateTime, HisData>>())
			{
				this.HisDataList = sortedList;
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600024B RID: 587 RVA: 0x000197E4 File Offset: 0x000179E4
		private SortedList<DateTime, HisData> method_28(StkSymbol stkSymbol_1, int int_1)
		{
			List<HisData> list = this.method_29(stkSymbol_1, int_1, true);
			SortedList<DateTime, HisData> result;
			if (list != null)
			{
				result = this.method_86(list);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x0600024C RID: 588 RVA: 0x00019810 File Offset: 0x00017A10
		public List<HisData> method_29(StkSymbol stkSymbol_1, int int_1, bool bool_1)
		{
			List<DatFileInfo> list = this.method_55(stkSymbol_1, int_1, bool_1);
			if (list != null)
			{
				DatFileInfo? datFileInfo = this.method_30(list);
				if (datFileInfo != null)
				{
					List<HisData> list2 = Base.Data.smethod_45(datFileInfo.Value.FileName);
					IEnumerable<DatFileInfo> source = list.Where(new Func<DatFileInfo, bool>(SymbDataSet.<>c.<>9.method_0));
					if (source.Any<DatFileInfo>())
					{
						List<HisData> list3 = Base.Data.smethod_45(source.First<DatFileInfo>().FileName);
						if (list2 != null && list3 != null && list3.Any<HisData>())
						{
							if (list2.Any<HisData>())
							{
								SymbDataSet.Class24 @class = new SymbDataSet.Class24();
								@class.dateTime_0 = list2.Last<HisData>().Date;
								if (list3.First<HisData>().Date > @class.dateTime_0)
								{
									list2.AddRange(list3);
								}
								else
								{
									list2.AddRange(list3.Where(new Func<HisData, bool>(@class.method_0)));
								}
							}
							else
							{
								list2.AddRange(list3);
							}
						}
						else if (list2 == null)
						{
							list2 = list3;
						}
					}
					return list2;
				}
			}
			return null;
		}

		// Token: 0x0600024D RID: 589 RVA: 0x00019928 File Offset: 0x00017B28
		private DatFileInfo? method_30(List<DatFileInfo> list_1)
		{
			DatFileInfo? result;
			try
			{
				result = new DatFileInfo?(list_1.Single(new Func<DatFileInfo, bool>(SymbDataSet.<>c.<>9.method_1)));
			}
			catch
			{
				result = null;
			}
			return result;
		}

		// Token: 0x0600024E RID: 590 RVA: 0x00019984 File Offset: 0x00017B84
		public bool method_31(int int_1)
		{
			bool result;
			if (this.Curr1hPeriodHisData != null && this.Curr1hPeriodHisData.SymbId == this.CurrSymbol.ID)
			{
				if (this.Curr1hPeriodHisData.PeriodHisDataList.Keys.Last<DateTime>().Year < int_1)
				{
					int year = this.Curr1hPeriodHisData.PeriodHisDataList.Keys.Last<DateTime>().Year;
					result = this.method_32(year + 1, int_1);
				}
				else
				{
					result = false;
				}
			}
			else
			{
				HisDataPeriodSet curr1hPeriodHisData = this.method_53(this.CurrStkMeta, null, new int?(int_1));
				this.Curr1hPeriodHisData = curr1hPeriodHisData;
				result = true;
			}
			return result;
		}

		// Token: 0x0600024F RID: 591 RVA: 0x00019A2C File Offset: 0x00017C2C
		public bool method_32(int int_1, int int_2)
		{
			HisDataPeriodSet hisDataPeriodSet = this.method_53(this.CurrStkMeta, new int?(int_1), new int?(int_2));
			bool result;
			if (hisDataPeriodSet != null)
			{
				result = this.method_33(hisDataPeriodSet);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06000250 RID: 592 RVA: 0x00019A64 File Offset: 0x00017C64
		public bool method_33(HisDataPeriodSet hisDataPeriodSet_1)
		{
			this.Curr1hPeriodHisData.method_41(hisDataPeriodSet_1);
			this.method_2();
			return true;
		}

		// Token: 0x06000251 RID: 593 RVA: 0x00019A88 File Offset: 0x00017C88
		public bool method_34(HisData hisData_1)
		{
			return hisData_1.Date.TimeOfDay == this.CurrHisDataSet.CurrExchgOBT.DayOpenTime.Value.AddMinutes(1.0).TimeOfDay;
		}

		// Token: 0x06000252 RID: 594 RVA: 0x00019AE0 File Offset: 0x00017CE0
		public bool method_35(HisData hisData_1)
		{
			return this.method_36(hisData_1, false);
		}

		// Token: 0x06000253 RID: 595 RVA: 0x00019AFC File Offset: 0x00017CFC
		public bool method_36(HisData hisData_1, bool bool_1)
		{
			ExchgOBT exchgOBT;
			if (bool_1)
			{
				exchgOBT = this.method_67(hisData_1.Date);
			}
			else
			{
				exchgOBT = this.CurrHisDataSet.CurrExchgOBT;
			}
			return exchgOBT.IsDayEndDT(hisData_1.Date);
		}

		// Token: 0x06000254 RID: 596 RVA: 0x00019B38 File Offset: 0x00017D38
		public HisData method_37(HisData hisData_1)
		{
			return this.method_38(this.CurrHisDataSet.FetchedHisDataList, hisData_1);
		}

		// Token: 0x06000255 RID: 597 RVA: 0x00019B5C File Offset: 0x00017D5C
		public HisData method_38(SortedList<DateTime, HisData> sortedList_1, HisData hisData_1)
		{
			if (hisData_1 != null)
			{
				DateTime? dateTime = this.method_39(hisData_1.Date);
				if (dateTime != null)
				{
					return Class336.smethod_2(sortedList_1, dateTime.Value);
				}
			}
			return null;
		}

		// Token: 0x06000256 RID: 598 RVA: 0x00019B98 File Offset: 0x00017D98
		public DateTime? method_39(DateTime dateTime_1)
		{
			return this.method_40(dateTime_1, Enum18.const_1);
		}

		// Token: 0x06000257 RID: 599 RVA: 0x00019BB4 File Offset: 0x00017DB4
		public DateTime? method_40(DateTime dateTime_1, Enum18 enum18_0)
		{
			SymbDataSet.Class25 @class = new SymbDataSet.Class25();
			DateTime? result;
			if (this.CurrHisDataSet == null)
			{
				result = null;
			}
			else
			{
				if (dateTime_1.Second > 0)
				{
					dateTime_1 = dateTime_1.AddSeconds((double)(-(double)dateTime_1.Second));
				}
				int num = Convert.ToInt32(enum18_0);
				DateTime? dateTime = new DateTime?(dateTime_1.AddMinutes((double)num));
				ExchgOBT exchgOBT = this.method_67(dateTime_1);
				SymbNtTrDate symbNtTrDate = this.method_68(dateTime_1);
				bool flag = exchgOBT.IsDayEndDT(dateTime_1);
				@class.nullable_0 = exchgOBT.GetNightTradingCloseDT(dateTime_1);
				if (flag)
				{
					dateTime = this.method_41(dateTime_1, exchgOBT, symbNtTrDate, num);
				}
				else if (@class.nullable_0 != null)
				{
					if (symbNtTrDate == null)
					{
						return null;
					}
					if (dateTime_1 < @class.nullable_0.Value)
					{
						dateTime = new DateTime?(dateTime_1.AddMinutes((double)num));
					}
					else
					{
						if (!(dateTime_1.TimeOfDay == exchgOBT.NightCloseTime.Value.TimeOfDay))
						{
							return null;
						}
						if (this.CurrHisDataSet.FetchedHisDataList.Count <= 0)
						{
							return null;
						}
						DateTime? dateTime2 = null;
						int num2 = this.CurrHisDataSet.FetchedHisDataList.IndexOfKey(@class.nullable_0.Value);
						if (num2 >= 0 && this.CurrHisDataSet.FetchedHisDataList.Count > num2 + 1)
						{
							dateTime2 = new DateTime?(this.CurrHisDataSet.FetchedHisDataList.Keys[num2 + 1]);
						}
						else if (this.CurrHisDataSet.FetchedHisDataList.Keys.FirstOrDefault(new Func<DateTime, bool>(@class.method_0)) != default(DateTime))
						{
							dateTime2 = new DateTime?(this.CurrHisDataSet.FetchedHisDataList.First(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_1)).Key);
						}
						if (dateTime2 != null)
						{
							return new DateTime?(dateTime2.Value.Date.Add(exchgOBT.DayOpenTime.Value.TimeOfDay).AddMinutes((double)num));
						}
						return null;
					}
				}
				else if (exchgOBT.AMRestStartTime != null && dateTime.Value.TimeOfDay > exchgOBT.AMRestStartTime.Value.TimeOfDay && dateTime.Value.TimeOfDay <= exchgOBT.AMRestEndTime.Value.TimeOfDay)
				{
					dateTime = new DateTime?(dateTime_1.Date.Add(exchgOBT.AMRestEndTime.Value.AddMinutes((double)num).TimeOfDay));
				}
				else if (exchgOBT.NoonBreakStartTime != null && dateTime.Value.TimeOfDay > exchgOBT.NoonBreakStartTime.Value.TimeOfDay && dateTime.Value.TimeOfDay <= exchgOBT.NoonBreakEndTime.Value.TimeOfDay)
				{
					dateTime = new DateTime?(dateTime_1.Date.Add(exchgOBT.NoonBreakEndTime.Value.AddMinutes((double)num).TimeOfDay));
				}
				else if (exchgOBT.PMRestStartTime != null && dateTime.Value.TimeOfDay > exchgOBT.PMRestStartTime.Value.TimeOfDay && dateTime.Value.TimeOfDay <= exchgOBT.PMRestEndTime.Value.TimeOfDay)
				{
					dateTime = new DateTime?(dateTime_1.Date.Add(exchgOBT.PMRestEndTime.Value.AddMinutes((double)num).TimeOfDay));
				}
				else if (dateTime_1.TimeOfDay >= exchgOBT.DayCloseTime.Value.TimeOfDay && (symbNtTrDate == null || (exchgOBT.NightOpenTime != null && dateTime_1.TimeOfDay < exchgOBT.NightOpenTime.Value.TimeOfDay)))
				{
					dateTime = this.method_41(dateTime_1, exchgOBT, symbNtTrDate, num);
				}
				result = dateTime;
			}
			return result;
		}

		// Token: 0x06000258 RID: 600 RVA: 0x0001A0A8 File Offset: 0x000182A8
		private DateTime? method_41(DateTime dateTime_1, ExchgOBT exchgOBT_0, SymbNtTrDate symbNtTrDate_0, int int_1)
		{
			SymbDataSet.Class26 @class = new SymbDataSet.Class26();
			@class.dateTime_0 = dateTime_1;
			DateTime? dateTime;
			try
			{
				this.method_43(@class.dateTime_0);
				int num = this.CurrHisDataSet.FetchedHisDataList.IndexOfKey(@class.dateTime_0);
				DateTime dateTime2;
				if (num >= 0)
				{
					if (num >= this.CurrHisDataSet.FetchedHisDataList.Count - 1)
					{
						dateTime = null;
						dateTime = dateTime;
						goto IL_16F;
					}
					dateTime2 = this.CurrHisDataSet.FetchedHisDataList.Keys[num + 1];
				}
				else
				{
					dateTime2 = this.CurrHisDataSet.FetchedHisDataList.Keys.First(new Func<DateTime, bool>(@class.method_0));
				}
				if (symbNtTrDate_0 == null)
				{
					this.CurrHisDataSet.method_15(dateTime2);
					exchgOBT_0 = this.CurrHisDataSet.CurrExchgOBT;
					dateTime = new DateTime?(dateTime2.Date.Add(exchgOBT_0.DayOpenTime.Value.TimeOfDay).AddMinutes((double)int_1));
					goto IL_16F;
				}
				DateTime? nightTradingStartDT = exchgOBT_0.GetNightTradingStartDT(dateTime2);
				if (nightTradingStartDT != null)
				{
					dateTime = new DateTime?(nightTradingStartDT.Value.AddMinutes((double)int_1));
					goto IL_16F;
				}
				dateTime = new DateTime?(dateTime2.Date.Add(exchgOBT_0.DayOpenTime.Value.TimeOfDay).AddMinutes((double)int_1));
				goto IL_16F;
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
			return null;
			IL_16F:
			return dateTime;
		}

		// Token: 0x06000259 RID: 601 RVA: 0x0001A248 File Offset: 0x00018448
		public bool method_42()
		{
			DateTime dateTime_ = default(DateTime);
			if (this.CurrHisDataSet != null && this.CurrHisDataSet.CurrHisData != null)
			{
				dateTime_ = this.CurrHisDataSet.CurrHisData.Date;
			}
			else if (Base.Acct.CurrAccount.LastSymbDT != null)
			{
				dateTime_ = Base.Acct.CurrAccount.LastSymbDT.Value;
			}
			return this.method_43(dateTime_);
		}

		// Token: 0x0600025A RID: 602 RVA: 0x0001A2B8 File Offset: 0x000184B8
		public bool method_43(DateTime dateTime_1)
		{
			bool result;
			try
			{
				if (this.CurrHisDataSet != null && dateTime_1 >= this.CurrStkMeta.BeginDate.Value && (dateTime_1 < this.CurrHisDataSet.FetchedHisDataList.Keys.First<DateTime>() || dateTime_1 > this.CurrHisDataSet.FetchedHisDataList.Keys.Last<DateTime>()))
				{
					result = this.method_44(dateTime_1, this.CurrStkMeta.EndDate.Value);
					goto IL_87;
				}
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
			return false;
			IL_87:
			return result;
		}

		// Token: 0x0600025B RID: 603 RVA: 0x0001A364 File Offset: 0x00018564
		public bool method_44(DateTime dateTime_1, DateTime dateTime_2)
		{
			if (this.CurrHisDataSet.method_7(dateTime_1, new DateTime?(dateTime_2)))
			{
				this.method_4();
				if (this.CurrDate != null)
				{
					this.DateTimeOfLastRec = this.CurrDate.Value;
					Base.Acct.smethod_14(this.SymblID, this.CurrDate.Value);
					return true;
				}
			}
			return false;
		}

		// Token: 0x0600025C RID: 604 RVA: 0x0001A3D4 File Offset: 0x000185D4
		public DateTime? method_45(HisData hisData_1, bool bool_1, bool bool_2)
		{
			if (hisData_1 == null)
			{
				hisData_1 = this.CurrHisDataSet.CurrHisData;
			}
			return this.method_46(new DateTime?(hisData_1.Date), bool_1, bool_2);
		}

		// Token: 0x0600025D RID: 605 RVA: 0x0001A408 File Offset: 0x00018608
		public DateTime? method_46(DateTime? nullable_0, bool bool_1, bool bool_2)
		{
			SymbDataSet.Class27 @class = new SymbDataSet.Class27();
			@class.nullable_0 = nullable_0;
			if (@class.nullable_0 == null)
			{
				if (!this.HasValidDataSet)
				{
					return null;
				}
				@class.nullable_0 = new DateTime?(this.CurrHisDataSet.CurrHisData.Date);
			}
			DateTime? result = null;
			if (!this.CurrHisDataSet.IsCurrHisDataEndRec && this.CurrHisDataSet.IsCurrHisDataDayEndRec)
			{
				if (bool_1)
				{
					try
					{
						IEnumerable<KeyValuePair<DateTime, HisData>> source = this.Curr1hPeriodHisData.PeriodHisDataList.Where(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_0));
						if (@class.nullable_0.Value < this.Curr1hPeriodHisData.PeriodHisDataList.Keys.Last<DateTime>() && source.Any<KeyValuePair<DateTime, HisData>>())
						{
							result = new DateTime?(source.First<KeyValuePair<DateTime, HisData>>().Key);
						}
						else if (this.method_31(@class.nullable_0.Value.Year + 1))
						{
							result = new DateTime?(this.Curr1hPeriodHisData.PeriodHisDataList.Keys.First(new Func<DateTime, bool>(@class.method_1)));
						}
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
				if (bool_2 && @class.nullable_0.Value >= this.CurrHisDataSet.FetchedHisDataList.Keys.Last<DateTime>() && this.method_88())
				{
					try
					{
						IEnumerable<KeyValuePair<DateTime, HisData>> source2 = this.CurrHisDataSet.FetchedHisDataList.Where(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_2));
						if (source2.Any<KeyValuePair<DateTime, HisData>>())
						{
							result = new DateTime?(source2.First<KeyValuePair<DateTime, HisData>>().Key);
						}
					}
					catch (Exception exception_2)
					{
						Class182.smethod_0(exception_2);
					}
				}
				try
				{
					if (result != null)
					{
						this.CurrHisDataSet.method_15(result.Value);
					}
				}
				catch (Exception exception_3)
				{
					Class182.smethod_0(exception_3);
				}
			}
			return result;
		}

		// Token: 0x0600025E RID: 606 RVA: 0x0001A610 File Offset: 0x00018810
		public void method_47()
		{
			string filePath = this.method_49();
			try
			{
				if (this.CurrHisDataSet != null)
				{
					Directory.CreateDirectory(Base.Acct.string_0);
					Utility.GenSerializedFile(this.CurrHisDataSet, filePath);
				}
			}
			catch (Exception exception_)
			{
				Utility.DeleteFile(filePath);
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x0600025F RID: 607 RVA: 0x0001A668 File Offset: 0x00018868
		public HisDataSet method_48()
		{
			this.method_5();
			string filePath = this.method_49();
			object obj = null;
			if (Utility.FileExists(filePath))
			{
				obj = Utility.DeserializeFile(filePath);
			}
			HisDataSet result;
			if (obj != null)
			{
				result = (HisDataSet)obj;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06000260 RID: 608 RVA: 0x0001A6A4 File Offset: 0x000188A4
		private string method_49()
		{
			string tableNamePrefix = SymbMgr.smethod_1(this.CurrSymbol.ExchangeID).TableNamePrefix;
			return string.Concat(new string[]
			{
				Base.Acct.string_0,
				"HDS",
				tableNamePrefix,
				this.CurrSymbol.Code,
				".DAT"
			});
		}

		// Token: 0x06000261 RID: 609 RVA: 0x0001A700 File Offset: 0x00018900
		public HisDataPeriodSet method_50(int int_1)
		{
			return this.method_51(int_1, null, null);
		}

		// Token: 0x06000262 RID: 610 RVA: 0x0001A72C File Offset: 0x0001892C
		public HisDataPeriodSet method_51(int int_1, int? nullable_0, int? nullable_1)
		{
			UsrStkMeta usrStkMeta_ = Base.Data.smethod_90(int_1);
			return this.method_53(usrStkMeta_, nullable_0, nullable_1);
		}

		// Token: 0x06000263 RID: 611 RVA: 0x0001A750 File Offset: 0x00018950
		public HisDataPeriodSet method_52(UsrStkMeta usrStkMeta_1)
		{
			return this.method_53(usrStkMeta_1, null, null);
		}

		// Token: 0x06000264 RID: 612 RVA: 0x0001A77C File Offset: 0x0001897C
		public HisDataPeriodSet method_53(UsrStkMeta usrStkMeta_1, int? nullable_0, int? nullable_1)
		{
			HisDataPeriodSet result;
			if (usrStkMeta_1 != null)
			{
				DatInfo datInfo_1h = usrStkMeta_1.DatInfo_1h;
				int int_ = (nullable_0 != null) ? nullable_0.Value : ((datInfo_1h == null || datInfo_1h.BeginDate == null) ? usrStkMeta_1.BeginDate.Value.Year : datInfo_1h.BeginDate.Value.Year);
				int int_2 = (nullable_1 != null) ? nullable_1.Value : ((datInfo_1h == null || datInfo_1h.EndDate == null) ? usrStkMeta_1.EndDate.Value.Year : datInfo_1h.EndDate.Value.Year);
				result = this.method_54(usrStkMeta_1.StkId, int_, int_2, false);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06000265 RID: 613 RVA: 0x0001A850 File Offset: 0x00018A50
		public HisDataPeriodSet method_54(int int_1, int int_2, int int_3, bool bool_1 = false)
		{
			SymbDataSet.Class28 @class = new SymbDataSet.Class28();
			@class.int_0 = int_1;
			@class.int_1 = int_2;
			@class.int_2 = int_3;
			List<HisData> list = new List<HisData>();
			IEnumerable<HDFileInfo> enumerable;
			if (TApp.SrvParams.GetHDFileInfoFromSrvApi)
			{
				enumerable = HDFileMgr.smethod_7(@class.int_0, false, null, null, true, new int?(@class.int_1), new int?(@class.int_2));
			}
			else
			{
				enumerable = Base.Data.HFileInfoList.Where(new Func<HDFileInfo, bool>(@class.method_0));
			}
			if (enumerable.Any<HDFileInfo>())
			{
				enumerable = enumerable.OrderBy(new Func<HDFileInfo, DateTime>(SymbDataSet.<>c.<>9.method_2));
				if (bool_1)
				{
					Base.UI.smethod_176(Base.Data.string_3);
				}
				foreach (HDFileInfo hdfileInfo in enumerable)
				{
					string text = TApp.string_7 + hdfileInfo.FileName;
					if (!Utility.FileExists(text))
					{
						this.method_56(@class.int_0, hdfileInfo.EndDate.Year, false);
					}
					else
					{
						long length = new FileInfo(text).Length;
						int? fileSize = hdfileInfo.FileSize;
						long? num = (fileSize != null) ? new long?((long)fileSize.GetValueOrDefault()) : null;
						if (!(length == num.GetValueOrDefault() & num != null))
						{
							this.method_56(@class.int_0, hdfileInfo.EndDate.Year, false);
						}
					}
					if (Utility.FileExists(text))
					{
						object obj = Utility.DeserializeCompressedFile(text, CompressAlgm.LZMA, SerializationType.ProtoBuf, typeof(HisData));
						if (obj != null)
						{
							List<HisData> collection = (List<HisData>)obj;
							list.AddRange(collection);
						}
					}
				}
				if (bool_1)
				{
					Base.UI.smethod_178();
				}
				if (list.Count > 0)
				{
					return new HisDataPeriodSet(@class.int_0, list, PeriodType.ByMins, new int?(60));
				}
			}
			return null;
		}

		// Token: 0x06000266 RID: 614 RVA: 0x0001AA68 File Offset: 0x00018C68
		private List<DatFileInfo> method_55(StkSymbol stkSymbol_1, int int_1, bool bool_1)
		{
			return this.method_56(stkSymbol_1.ID, int_1, bool_1);
		}

		// Token: 0x06000267 RID: 615 RVA: 0x0001AA88 File Offset: 0x00018C88
		private List<DatFileInfo> method_56(int int_1, int int_2, bool bool_1)
		{
			List<DatFileInfo> list = Base.Data.smethod_41(int_1, int_2);
			List<DatFileInfo> result;
			if (list == null)
			{
				result = null;
			}
			else
			{
				DatFileInfo datFileInfo;
				List<DatFileInfo> result2;
				try
				{
					datFileInfo = list.Single(new Func<DatFileInfo, bool>(SymbDataSet.<>c.<>9.method_3));
					goto IL_43;
				}
				catch
				{
					result2 = null;
				}
				return result2;
				IL_43:
				string filePath = TApp.string_7 + datFileInfo.FileName;
				this.method_57(datFileInfo, int_2, bool_1);
				if (!Utility.FileExists(filePath))
				{
					this.method_57(datFileInfo, int_2, false);
					if (!Utility.FileExists(filePath))
					{
						this.method_6();
						return null;
					}
				}
				result = list;
			}
			return result;
		}

		// Token: 0x06000268 RID: 616 RVA: 0x0001AB30 File Offset: 0x00018D30
		private void method_57(DatFileInfo datFileInfo_0, int int_1, bool bool_1 = false)
		{
			try
			{
				new Class180(datFileInfo_0.StkId).method_2(datFileInfo_0.StkId, int_1, bool_1);
			}
			catch
			{
			}
		}

		// Token: 0x06000269 RID: 617 RVA: 0x0001AB6C File Offset: 0x00018D6C
		public HisDataPeriodSet method_58(PeriodType periodType_0, int? nullable_0)
		{
			return this.method_59(periodType_0, nullable_0, null);
		}

		// Token: 0x0600026A RID: 618 RVA: 0x0001AB90 File Offset: 0x00018D90
		public HisDataPeriodSet method_59(PeriodType periodType_0, int? nullable_0, DateTime? nullable_1)
		{
			if (periodType_0 == PeriodType.ByMins && nullable_0 != null)
			{
				int? num = nullable_0;
				if ((num.GetValueOrDefault() < 60 & num != null) || !Utility.CanExactDiv(nullable_0.Value, 60))
				{
					if (this.CurrHisDataSet != null)
					{
						return new HisDataPeriodSet(this, periodType_0, nullable_0);
					}
					if (nullable_1 != null && !(nullable_1.Value < this.CurrStkMeta.BeginDate.Value) && !(nullable_1.Value > this.CurrStkMeta.EndDate.Value))
					{
						throw new NotImplementedException();
					}
					return HisDataPeriodSet.smethod_3(this.SymblID, this.DateTimeOfLastRec, periodType_0, nullable_0);
				}
			}
			new HisDataPeriodSet();
			return this.method_61(this.CurrSymbol, periodType_0, nullable_0, null, nullable_1);
		}

		// Token: 0x0600026B RID: 619 RVA: 0x0001AC78 File Offset: 0x00018E78
		public HisDataPeriodSet method_60(StkSymbol stkSymbol_1, PeriodType periodType_0, int? nullable_0)
		{
			return this.method_61(stkSymbol_1, periodType_0, nullable_0, null, null);
		}

		// Token: 0x0600026C RID: 620 RVA: 0x0001ACA4 File Offset: 0x00018EA4
		public HisDataPeriodSet method_61(StkSymbol stkSymbol_1, PeriodType periodType_0, int? nullable_0, DateTime? nullable_1, DateTime? nullable_2)
		{
			HisDataPeriodSet hisDataPeriodSet = null;
			if (this.CurrSymbol.Code == stkSymbol_1.Code && this.Curr1hPeriodHisData != null && this.Curr1hPeriodHisData.Count > 0 && this.Curr1hPeriodHisData.SymbId == stkSymbol_1.ID && (nullable_2 == null || this.Curr1hPeriodHisData.LastYear >= nullable_2.Value.Year))
			{
				hisDataPeriodSet = (HisDataPeriodSet)this.Curr1hPeriodHisData.System.ICloneable.Clone();
			}
			else
			{
				int? nullable_3 = null;
				if (nullable_2 != null)
				{
					nullable_3 = new int?(nullable_2.Value.Year);
				}
				this.hisDataPeriodSet_0 = this.method_51(stkSymbol_1.ID, null, nullable_3);
				if (this.Curr1hPeriodHisData != null)
				{
					hisDataPeriodSet = (HisDataPeriodSet)this.Curr1hPeriodHisData.System.ICloneable.Clone();
				}
			}
			HisDataPeriodSet result;
			if (hisDataPeriodSet != null && hisDataPeriodSet.Count > 0 && hisDataPeriodSet.SymbId == stkSymbol_1.ID)
			{
				result = this.method_62(hisDataPeriodSet, periodType_0, nullable_0, nullable_1, nullable_2);
			}
			else
			{
				hisDataPeriodSet = this.method_50(stkSymbol_1.ID);
				if (hisDataPeriodSet == null || hisDataPeriodSet.Count <= 0)
				{
					throw new Exception("1H Hdps data can't be generated!");
				}
				result = this.method_62(hisDataPeriodSet, periodType_0, nullable_0, nullable_1, nullable_2);
			}
			return result;
		}

		// Token: 0x0600026D RID: 621 RVA: 0x0001ADF4 File Offset: 0x00018FF4
		private HisDataPeriodSet method_62(HisDataPeriodSet hisDataPeriodSet_1, PeriodType periodType_0, int? nullable_0, DateTime? nullable_1, DateTime? nullable_2)
		{
			HisDataPeriodSet result;
			if (hisDataPeriodSet_1 != null && hisDataPeriodSet_1.Count > 0)
			{
				if (periodType_0 == PeriodType.ByMins && nullable_0 != null && nullable_0.Value == 60)
				{
					hisDataPeriodSet_1.method_3();
					result = hisDataPeriodSet_1;
				}
				else
				{
					if (periodType_0 != PeriodType.ByDay && periodType_0 != PeriodType.ByWeek)
					{
						if (periodType_0 != PeriodType.ByMonth)
						{
							return hisDataPeriodSet_1.method_40(hisDataPeriodSet_1.SymbId, periodType_0, nullable_0, nullable_1, nullable_2);
						}
					}
					result = hisDataPeriodSet_1.method_39(hisDataPeriodSet_1.SymbId, periodType_0, nullable_0);
				}
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x0600026E RID: 622 RVA: 0x0001AE68 File Offset: 0x00019068
		public SortedList<DateTime, HisData> method_63(DateTime dateTime_1, DateTime dateTime_2, bool bool_1 = false)
		{
			HisDataPeriodSet hisDataPeriodSet = this.method_54(this.CurrSymbol.ID, dateTime_1.Year, dateTime_2.Year, bool_1);
			SortedList<DateTime, HisData> result;
			if (hisDataPeriodSet != null)
			{
				result = Base.Data.smethod_46(hisDataPeriodSet.PeriodHisDataList, this.CurrSymbol, dateTime_1, dateTime_2, 60, null);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x0600026F RID: 623 RVA: 0x0001AEB8 File Offset: 0x000190B8
		public HisData method_64(DateTime dateTime_1)
		{
			SymbDataSet.Class29 @class = new SymbDataSet.Class29();
			@class.dateTime_0 = dateTime_1;
			HisData result = null;
			if (this.CurrHisDataSet != null && this.CurrHisDataSet.FetchedHisDataList != null)
			{
				HisData result2;
				try
				{
					DateTime dateTime = this.CurrHisDataSet.FetchedHisDataList.Keys.LastOrDefault(new Func<DateTime, bool>(@class.method_0));
					if (dateTime != default(DateTime))
					{
						this.CurrHisDataSet.FetchedHisDataList.TryGetValue(dateTime, out result);
					}
					goto IL_79;
				}
				catch
				{
					result2 = null;
				}
				return result2;
			}
			IL_79:
			return result;
		}

		// Token: 0x06000270 RID: 624 RVA: 0x0001AF58 File Offset: 0x00019158
		public HisData method_65(DateTime dateTime_1)
		{
			SymbDataSet.Class30 @class = new SymbDataSet.Class30();
			@class.dateTime_0 = dateTime_1;
			HisData result;
			if (this.CurrHisDataSet == null)
			{
				result = null;
			}
			else
			{
				HisData hisData = null;
				@class.dateTime_1 = this.CurrHisDataSet.CurrDayBeginDT;
				SortedList<DateTime, HisData> fetchedHisDataList = this.CurrHisDataSet.FetchedHisDataList;
				HisData result2;
				try
				{
					DateTime dateTime = @class.dateTime_0.AddMinutes(-1.0);
					if (dateTime > @class.dateTime_1)
					{
						fetchedHisDataList.TryGetValue(dateTime, out hisData);
					}
					if (hisData == null && (@class.dateTime_0 - @class.dateTime_1).TotalMinutes > 1.0)
					{
						int num = fetchedHisDataList.IndexOfKey(@class.dateTime_0);
						if (num > 0 && fetchedHisDataList.Keys[num - 1] > @class.dateTime_1)
						{
							hisData = fetchedHisDataList.Values[num - 1];
						}
						else
						{
							IEnumerable<KeyValuePair<DateTime, HisData>> source = fetchedHisDataList.Where(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_0));
							if (source.Any<KeyValuePair<DateTime, HisData>>())
							{
								hisData = source.Last<KeyValuePair<DateTime, HisData>>().Value;
							}
						}
					}
					goto IL_111;
				}
				catch
				{
					result2 = null;
				}
				return result2;
				IL_111:
				result = hisData;
			}
			return result;
		}

		// Token: 0x06000271 RID: 625 RVA: 0x0001B090 File Offset: 0x00019290
		public DateTime? method_66(DateTime dateTime_1)
		{
			HisData hisData = this.method_65(dateTime_1);
			DateTime? result;
			if (hisData != null)
			{
				result = new DateTime?(hisData.Date);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06000272 RID: 626 RVA: 0x0001B0C4 File Offset: 0x000192C4
		public ExchgOBT method_67(DateTime dateTime_1)
		{
			return Base.Data.smethod_110(this.CurrSymbol, dateTime_1);
		}

		// Token: 0x06000273 RID: 627 RVA: 0x0001B0E4 File Offset: 0x000192E4
		public SymbNtTrDate method_68(DateTime dateTime_1)
		{
			return Base.Data.smethod_112(this.CurrSymbol, dateTime_1);
		}

		// Token: 0x06000274 RID: 628 RVA: 0x0001B104 File Offset: 0x00019304
		public decimal method_69(double double_0)
		{
			decimal result;
			try
			{
				decimal num = Convert.ToDecimal(double_0);
				if (this.CurrSymbol.LeastPriceVar != null && this.CurrSymbol.LeastPriceVar.Value != 0m)
				{
					decimal value = this.CurrSymbol.LeastPriceVar.Value;
					num = Math.Round(num / value) * value / 1.000000000000000m;
				}
				result = num;
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
				result = 0m;
			}
			return result;
		}

		// Token: 0x06000275 RID: 629 RVA: 0x0001B1B4 File Offset: 0x000193B4
		public DateTime method_70(DateTime dateTime_1)
		{
			SymbDataSet.Class31 @class = new SymbDataSet.Class31();
			@class.dateTime_0 = dateTime_1;
			HisData hisData = new HisData();
			DateTime dateTime = DateTime.MinValue;
			DateTime result;
			if (this.CurrHisDataSet.FetchedHisDataList.TryGetValue(@class.dateTime_0, out hisData))
			{
				result = @class.dateTime_0;
			}
			else
			{
				try
				{
					dateTime = this.CurrHisDataSet.FetchedHisDataList.Keys.First(new Func<DateTime, bool>(@class.method_0));
				}
				catch
				{
					Random random = new Random();
					dateTime = this.CurrHisDataSet.FetchedHisDataList.Keys[random.Next(0, this.CurrHisDataSet.FetchedHisDataList.Count - 1)];
				}
				result = dateTime;
			}
			return result;
		}

		// Token: 0x06000276 RID: 630 RVA: 0x0001B274 File Offset: 0x00019474
		private List<StSplit> method_71(int int_1)
		{
			return this.method_72(TApp.SrvParams.StSplitList, int_1);
		}

		// Token: 0x06000277 RID: 631 RVA: 0x0001B298 File Offset: 0x00019498
		private List<StSplit> method_72(List<StSplit> list_1, int int_1)
		{
			SymbDataSet.Class32 @class = new SymbDataSet.Class32();
			@class.int_0 = int_1;
			if (list_1 != null)
			{
				IEnumerable<StSplit> source = list_1.Where(new Func<StSplit, bool>(@class.method_0));
				if (source.Any<StSplit>())
				{
					return this.method_73(source.ToList<StSplit>());
				}
			}
			return null;
		}

		// Token: 0x06000278 RID: 632 RVA: 0x0001B2E4 File Offset: 0x000194E4
		public List<StSplit> method_73(List<StSplit> list_1)
		{
			List<StSplit> result;
			if (list_1 != null && list_1.Any<StSplit>())
			{
				List<StSplit> list = new List<StSplit>();
				foreach (StSplit stSplit in list_1)
				{
					decimal value = (stSplit.BonusShares != null) ? (stSplit.BonusShares.Value / 10m) : 0m;
					decimal value2 = (stSplit.RationedShares != null) ? (stSplit.RationedShares.Value / 10m) : 0m;
					decimal value3 = (stSplit.RationedSharePrice != null) ? stSplit.RationedSharePrice.Value : 0m;
					decimal value4 = (stSplit.Divident != null) ? (stSplit.Divident.Value / 10m) : 0m;
					list.Add(new StSplit
					{
						StockId = stSplit.StockId,
						Date = stSplit.Date,
						BonusShares = new decimal?(value),
						RationedShares = new decimal?(value2),
						RationedSharePrice = new decimal?(value3),
						Divident = new decimal?(value4)
					});
				}
				result = list;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06000279 RID: 633 RVA: 0x0001B488 File Offset: 0x00019688
		public void method_74()
		{
			TradingSymbol mstSymbol = Base.Acct.smethod_49(this.stkSymbol_0.MstSymbol.ID);
			foreach (StkSymbol stkSymbol in Base.Data.UsrStkSymbols.Values.Where(new Func<StkSymbol, bool>(this.method_113)))
			{
				stkSymbol.MstSymbol = mstSymbol;
			}
			this.stkSymbol_0.MstSymbol = mstSymbol;
		}

		// Token: 0x0600027A RID: 634 RVA: 0x000033FF File Offset: 0x000015FF
		private void method_75(object sender, EventArgs e)
		{
			this.method_9();
			Base.Trading.smethod_19(this.CurrSymbol.ID, this.CurrHisData);
		}

		// Token: 0x0600027B RID: 635 RVA: 0x0001B510 File Offset: 0x00019710
		private void method_76(object sender, EventArgs24 e)
		{
			this.method_10();
			this.method_78();
			Enum6 @enum = this.method_77(e.OldDate, e.NewDate);
			if (@enum == Enum6.const_1)
			{
				this.method_11();
			}
			else if (@enum == Enum6.const_2)
			{
				this.method_12();
			}
			else if (@enum == Enum6.const_3)
			{
				this.method_13();
			}
		}

		// Token: 0x0600027C RID: 636 RVA: 0x0001B564 File Offset: 0x00019764
		public Enum6 method_77(DateTime dateTime_1, DateTime dateTime_2)
		{
			SymbDataSet.Class33 @class = new SymbDataSet.Class33();
			@class.dateTime_0 = dateTime_2;
			@class.dateTime_1 = dateTime_1;
			if (this.CurrSymbol.IsStock && this.CurrSymbStSpltList != null)
			{
				if (this.CurrSymbStSpltList.Exists(new Predicate<StSplit>(@class.method_0)))
				{
					return Enum6.const_1;
				}
				if (this.CurrSymbStSpltList.Exists(new Predicate<StSplit>(@class.method_1)))
				{
					return Enum6.const_2;
				}
				if (this.CurrSymbStSpltList.Exists(new Predicate<StSplit>(@class.method_2)))
				{
					return Enum6.const_3;
				}
			}
			return Enum6.const_0;
		}

		// Token: 0x0600027D RID: 637 RVA: 0x0001B5F8 File Offset: 0x000197F8
		private bool method_78()
		{
			return this.method_79(true);
		}

		// Token: 0x0600027E RID: 638 RVA: 0x0001B610 File Offset: 0x00019810
		private bool method_79(bool bool_1)
		{
			bool flag = false;
			if (this.CurrSymbol.IsStock && this.CurrSymbStSpltList != null)
			{
				bool flag2 = Base.UI.Form.IfNoBonusShare && Base.UI.Form.IfNoDivident;
				bool flag3 = Base.UI.Form.RationedShareTreatmt != null && Base.UI.Form.RationedShareTreatmt.Value == RationedShareTreatmt.None;
				if (flag2 && flag3)
				{
					return false;
				}
				List<Transaction> list = Base.Trading.smethod_143(Base.Acct.CurrAccount.ID, this.SymblID).ToList<Transaction>();
				if (list != null && list.Any<Transaction>())
				{
					List<StSplit> list2 = this.method_101(this.CurrDate.Value);
					if (list2 != null)
					{
						SymbDataSet.Class34 @class = new SymbDataSet.Class34();
						@class.stSplit_0 = list2.Last<StSplit>();
						bool flag4;
						if (bool_1)
						{
							decimal? num = @class.stSplit_0.RationedShares;
							decimal d = 0m;
							flag4 = (num.GetValueOrDefault() > d & num != null);
						}
						else
						{
							flag4 = false;
						}
						bool flag5 = flag4;
						if (!flag2 && (list2.Count > 1 || !flag5))
						{
							foreach (Transaction transaction in list)
							{
								TranStock tranStock = transaction as TranStock;
								List<StSplit> list3 = this.method_102(transaction.CreateTime, this.CurrDate.Value);
								if (list3 != null)
								{
									if (flag5)
									{
										list3.Remove(@class.stSplit_0);
									}
									if (list3.Any<StSplit>())
									{
										flag = Base.Trading.smethod_224(tranStock, list3, false);
									}
								}
								else
								{
									if (flag5)
									{
										decimal? num = @class.stSplit_0.RationedShares;
										decimal d = 0m;
										if (!(num.GetValueOrDefault() == d & num != null))
										{
											continue;
										}
										num = @class.stSplit_0.Divident;
										d = 0m;
										if (!(num.GetValueOrDefault() == d & num != null))
										{
											continue;
										}
									}
									if (tranStock != null && tranStock.ProcessedStSplts != null)
									{
										if (tranStock.ProcessedStSplts.Exists(new Predicate<StSplit>(SymbDataSet.<>c.<>9.method_4)))
										{
											tranStock.method_0();
											flag = true;
										}
									}
								}
							}
							if (flag)
							{
								Base.Trading.CurrOpenTransList = Base.Trading.smethod_138();
							}
						}
						if (flag5)
						{
							DateTime? dateTime = null;
							try
							{
								dateTime = new DateTime?(this.CurrHisDataSet.FetchedHisDataList.FirstOrDefault(new Func<KeyValuePair<DateTime, HisData>, bool>(@class.method_0)).Key.Date);
							}
							catch
							{
							}
							if (dateTime != null && Base.Data.CurrDate.Date == dateTime.Value)
							{
								List<TranStock> list4 = new List<TranStock>();
								foreach (Transaction transaction2 in list)
								{
									Transaction transaction3 = Base.Trading.smethod_130(transaction2.ID);
									if (transaction3 != null)
									{
										TranStock item = transaction3 as TranStock;
										list4.Add(item);
									}
								}
								if (!list4.Exists(new Predicate<TranStock>(@class.method_1)))
								{
									long num2 = list.Sum(new Func<Transaction, long>(SymbDataSet.<>c.<>9.method_5));
									int num3 = Base.Trading.smethod_229(this.CurrHisDataSet, (int)num2, @class.stSplit_0);
									if (!flag)
									{
										flag = (num3 >= 0);
									}
								}
							}
							if (!flag2)
							{
								bool flag6 = false;
								foreach (Transaction transaction4 in list)
								{
									if (transaction4.CreateTime.Date < @class.stSplit_0.Date.Date)
									{
										TranStock tranStock2 = Base.Trading.smethod_130(transaction4.ID) as TranStock;
										if (this.method_102(tranStock2.CreateTime, this.CurrDate.Value).Count == 1)
										{
											tranStock2.method_0();
										}
										flag6 = Base.Trading.smethod_223(tranStock2, @class.stSplit_0, false);
										if (!flag)
										{
											flag = flag6;
										}
									}
								}
								if (flag6)
								{
									Base.Trading.CurrOpenTransList = Base.Trading.smethod_138();
								}
							}
						}
						Base.Trading.smethod_151();
						if (flag)
						{
							Base.Trading.smethod_126();
							try
							{
								this.method_47();
								Base.UI.smethod_47();
							}
							catch (Exception exception_)
							{
								Class182.smethod_0(exception_);
							}
						}
					}
				}
			}
			return flag;
		}

		// Token: 0x0600027F RID: 639 RVA: 0x0001BB18 File Offset: 0x00019D18
		public HisDataPeriodSet method_80(PeriodType periodType_0, int? nullable_0, DateTime? nullable_1)
		{
			return this.method_81(this.CurrHisDataSet.FetchedHisDataList, periodType_0, nullable_0, nullable_1, 0);
		}

		// Token: 0x06000280 RID: 640 RVA: 0x0001BB40 File Offset: 0x00019D40
		public HisDataPeriodSet method_81(SortedList<DateTime, HisData> sortedList_1, PeriodType periodType_0, int? nullable_0, DateTime? nullable_1, int int_1)
		{
			return this.method_82(this.CurrHisDataSet.FetchedHisDataList, periodType_0, nullable_0, nullable_1, null, 1, int_1);
		}

		// Token: 0x06000281 RID: 641 RVA: 0x0001BB74 File Offset: 0x00019D74
		public HisDataPeriodSet method_82(SortedList<DateTime, HisData> sortedList_1, PeriodType periodType_0, int? nullable_0, DateTime? nullable_1, DateTime? nullable_2, int int_1, int int_2)
		{
			SymbDataSet.Class35 @class = new SymbDataSet.Class35();
			@class.nullable_0 = nullable_1;
			int num = 1800 + int_2;
			int num2;
			if (nullable_0 != null)
			{
				num2 = nullable_0.Value / int_1 * num;
			}
			else
			{
				if (periodType_0 != PeriodType.ByDay)
				{
					throw new Exception("PeriodType is not supported.");
				}
				SymbDataSet.Class36 class2 = new SymbDataSet.Class36();
				class2.dateTime_0 = sortedList_1.Keys.First<DateTime>();
				num2 = sortedList_1.Count(new Func<KeyValuePair<DateTime, HisData>, bool>(class2.method_0)) * num;
			}
			int count = sortedList_1.Count;
			int num3 = 0;
			if (count > num2)
			{
				if (@class.nullable_0 != null)
				{
					if (int_1 == 1)
					{
						SymbDataSet.Class37 class3 = new SymbDataSet.Class37();
						class3.dateTime_0 = @class.nullable_0.Value.AddSeconds((double)(-(double)@class.nullable_0.Value.Second)).AddMilliseconds((double)(-(double)@class.nullable_0.Value.Millisecond));
						num3 = sortedList_1.IndexOfKey(class3.dateTime_0);
						if (num3 < 0)
						{
							num3 = sortedList_1.IndexOfKey(class3.dateTime_0);
							if (num3 < 0)
							{
								num3 = sortedList_1.Keys.ToList<DateTime>().FindLastIndex(new Predicate<DateTime>(class3.method_0));
							}
							if (num3 < 0)
							{
								num3 = 0;
							}
						}
					}
					else
					{
						num3 = sortedList_1.IndexOfKey(@class.nullable_0.Value);
						if (num3 < 0)
						{
							num3 = sortedList_1.Keys.ToList<DateTime>().FindLastIndex(new Predicate<DateTime>(@class.method_0));
						}
						if (num3 < 0)
						{
							if (!(@class.nullable_0.Value >= this.CurrStkMeta.BeginDate))
							{
								throw new Exception("CurrDT < Data.CurrHDTableMeta.BeginDate!");
							}
							num3 = 0;
						}
					}
				}
				int num4 = num3 - num2;
				if (num4 > 0)
				{
					DateTime dateTime_ = sortedList_1.Keys[num4];
					if (nullable_2 == null)
					{
						nullable_2 = new DateTime?(sortedList_1.Keys.Last<DateTime>());
					}
					SortedList<DateTime, HisData> sortedList = this.method_83(sortedList_1, dateTime_, nullable_2.Value);
					if (sortedList != null)
					{
						sortedList_1 = sortedList;
					}
				}
			}
			return new HisDataPeriodSet(this, sortedList_1, periodType_0, nullable_0);
		}

		// Token: 0x06000282 RID: 642 RVA: 0x0001BD98 File Offset: 0x00019F98
		public SortedList<DateTime, HisData> method_83(SortedList<DateTime, HisData> sortedList_1, DateTime dateTime_1, DateTime dateTime_2)
		{
			SortedList<DateTime, HisData> result;
			if (sortedList_1 != null && sortedList_1.Any<KeyValuePair<DateTime, HisData>>())
			{
				result = Base.Data.smethod_46(sortedList_1, this.CurrSymbol, dateTime_1, dateTime_2, 1, null);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06000283 RID: 643 RVA: 0x0001BDC8 File Offset: 0x00019FC8
		private bool method_84(int int_1, int int_2)
		{
			if (int_1 < this.CurrStkMeta.BeginDate.Value.Year)
			{
				int_1 = this.CurrStkMeta.BeginDate.Value.Year;
			}
			if (int_2 > this.CurrStkMeta.EndDate.Value.Year)
			{
				int_2 = this.CurrStkMeta.EndDate.Value.Year;
			}
			if (this.sortedList_0 != null && this.sortedList_0.Count > 0)
			{
				int year = this.sortedList_0.Keys.First<DateTime>().Year;
				int year2 = this.sortedList_0.Keys.Last<DateTime>().Year;
				if (year - int_2 <= 1 && int_1 - year2 <= 1)
				{
					if (year <= int_1 && year2 >= int_2)
					{
						if (year != int_1 || year2 != int_2)
						{
							SortedList<DateTime, HisData> sortedList = new SortedList<DateTime, HisData>();
							foreach (KeyValuePair<DateTime, HisData> keyValuePair in this.HisDataList)
							{
								if (keyValuePair.Key.Year >= int_1 && keyValuePair.Key.Year <= int_2)
								{
									sortedList.Add(keyValuePair.Key, keyValuePair.Value);
								}
							}
							this.HisDataList = sortedList;
							this.method_31(int_2);
						}
					}
					else
					{
						if (year > int_1)
						{
							for (int i = 1; i <= year - int_1; i++)
							{
								List<HisData> list = this.method_29(this.CurrSymbol, year - i, true);
								if (list != null)
								{
									list.AddRange(this.HisDataList.Values.ToList<HisData>());
									this.HisDataList = this.method_86(list);
								}
							}
						}
						if (year2 < int_2)
						{
							this.method_14(SymbDataSet.string_0, true);
							List<HisData> list2 = this.HisDataList.Values.ToList<HisData>();
							List<HisData> list3 = null;
							for (int j = 1; j <= int_2 - year2; j++)
							{
								list3 = this.method_29(this.CurrSymbol, year2 + j, false);
								if (list3 != null)
								{
									list2.AddRange(list3);
								}
							}
							if (list3 == null)
							{
								this.method_15();
								return false;
							}
							this.HisDataList = this.method_86(list2);
						}
						this.method_31(int_2);
						this.method_15();
					}
				}
				else
				{
					this.method_85(int_1, int_2);
				}
			}
			else
			{
				this.method_85(int_1, int_2);
			}
			return true;
		}

		// Token: 0x06000284 RID: 644 RVA: 0x0001C04C File Offset: 0x0001A24C
		private bool method_85(int int_1, int int_2)
		{
			List<HisData> list = new List<HisData>();
			for (int i = int_1; i <= int_2; i++)
			{
				List<HisData> list2 = this.method_29(this.CurrSymbol, i, true);
				if (list2 != null)
				{
					list.AddRange(list2);
				}
			}
			bool result;
			if (list.Any<HisData>())
			{
				this.HisDataList = this.method_86(list);
				this.method_31(int_2);
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06000285 RID: 645 RVA: 0x0001C0AC File Offset: 0x0001A2AC
		private SortedList<DateTime, HisData> method_86(List<HisData> list_1)
		{
			SortedList<DateTime, HisData> result;
			try
			{
				result = new SortedList<DateTime, HisData>(list_1.ToDictionary(new Func<HisData, DateTime>(SymbDataSet.<>c.<>9.method_6)));
			}
			catch (Exception exception_)
			{
				IEnumerable<HisData> source = list_1.GroupBy(new Func<HisData, DateTime>(SymbDataSet.<>c.<>9.method_7)).Select(new Func<IGrouping<DateTime, HisData>, HisData>(SymbDataSet.<>c.<>9.method_8));
				int num = list_1.GroupBy(new Func<HisData, DateTime>(SymbDataSet.<>c.<>9.method_9)).SelectMany(new Func<IGrouping<DateTime, HisData>, IEnumerable<HisData>>(SymbDataSet.<>c.<>9.method_10)).Count<HisData>();
				string string_ = string.Concat(new object[]
				{
					num,
					" duplicate HD recs found! SymbCode:",
					this.CurrSymbol.Code,
					" Year:",
					list_1.Last<HisData>().Date.Year
				});
				Class46.smethod_4(exception_, true, string_);
				result = new SortedList<DateTime, HisData>(source.ToDictionary(new Func<HisData, DateTime>(SymbDataSet.<>c.<>9.method_11)));
			}
			return result;
		}

		// Token: 0x06000286 RID: 646 RVA: 0x0001C22C File Offset: 0x0001A42C
		public SortedList<DateTime, HisData> method_87(DateTime dateTime_1, DateTime dateTime_2)
		{
			SortedList<DateTime, HisData> result;
			if (this.method_84(dateTime_1.Year, dateTime_2.Year) && this.sortedList_0 != null)
			{
				result = this.method_83(this.sortedList_0, dateTime_1, dateTime_2);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06000287 RID: 647 RVA: 0x0001C270 File Offset: 0x0001A470
		public bool method_88()
		{
			return this.method_89(7);
		}

		// Token: 0x06000288 RID: 648 RVA: 0x0001C288 File Offset: 0x0001A488
		public bool method_89(int int_1)
		{
			return this.method_90(int_1, null);
		}

		// Token: 0x06000289 RID: 649 RVA: 0x0001C2AC File Offset: 0x0001A4AC
		public bool method_90(int int_1, DateTime? nullable_0)
		{
			DateTime fetchedHDDataEndDate = this.CurrHisDataSet.FetchedHDDataEndDate;
			DateTime value;
			if (nullable_0 != null)
			{
				value = nullable_0.Value;
			}
			else
			{
				if (this.CurrDate == null)
				{
					return false;
				}
				value = this.CurrDate.Value;
			}
			DateTime dateTime_ = fetchedHDDataEndDate.AddDays(1.0);
			int num = int_1;
			int num2 = Convert.ToInt32(Math.Ceiling((this.CurrStkMeta.EndDate.Value - fetchedHDDataEndDate.Date).TotalDays));
			if (num > num2)
			{
				num = num2;
			}
			if (num > 0)
			{
				SortedList<DateTime, HisData> sortedList = null;
				for (;;)
				{
					if (sortedList != null && sortedList.Any<KeyValuePair<DateTime, HisData>>() && !(value >= sortedList.Keys.Last<DateTime>()))
					{
						bool? flag = this.CurrHisDataSet.CurrExchgOBT.IsNightTradingDT(sortedList.Keys.Last<DateTime>());
						if (!(flag.GetValueOrDefault() & flag != null))
						{
							break;
						}
					}
					if (fetchedHDDataEndDate.AddDays((double)num) > this.CurrStkMeta.EndDate)
					{
						num = num2;
					}
					sortedList = this.method_87(dateTime_, fetchedHDDataEndDate.AddDays((double)num));
					if (num == num2)
					{
						break;
					}
					num += int_1;
				}
				if (sortedList != null && sortedList.Any<KeyValuePair<DateTime, HisData>>())
				{
					this.method_7();
					foreach (KeyValuePair<DateTime, HisData> keyValuePair in sortedList)
					{
						try
						{
							this.CurrHisDataSet.FetchedHisDataList.Add(keyValuePair.Key, keyValuePair.Value);
						}
						catch (Exception ex)
						{
							Class182.smethod_0(new Exception(string.Concat(new string[]
							{
								ex.Message,
								"品种:",
								this.CurrSymbol.Code,
								"，日期:",
								keyValuePair.Key.ToString()
							})));
						}
					}
					int count = this.CurrHisDataSet.FetchedHisDataList.Count;
					if (this.CurrHisDataSet.FetchedHisDataList.Count > 6000)
					{
						DateTime date = this.CurrHisDataSet.FetchedHisDataList.Keys[count - 6000].Date;
						List<DateTime> list = new List<DateTime>();
						foreach (ChtCtrl chtCtrl in Base.UI.ChtCtrlList)
						{
							if (chtCtrl.IsNMinsPeriod)
							{
								try
								{
									if (!chtCtrl.SymbDataSet.IsCurrDateNotListedYet)
									{
										list.Add(chtCtrl.FirstItemShown.Date);
									}
								}
								catch (Exception exception_)
								{
									Class182.smethod_0(exception_);
								}
							}
						}
						if (list.Any<DateTime>())
						{
							DateTime dateTime = list.Min(new Func<DateTime, DateTime>(SymbDataSet.<>c.<>9.method_12));
							if (date > dateTime.Date)
							{
								date = dateTime.Date;
							}
						}
						this.CurrHisDataSet.FetchedHisDataList = this.method_83(this.CurrHisDataSet.FetchedHisDataList, date, this.CurrHisDataSet.FetchedHisDataList.Last<KeyValuePair<DateTime, HisData>>().Value.Date);
					}
					foreach (ChtCtrl chtCtrl2 in Base.UI.ChtCtrlList)
					{
						if (chtCtrl2.SymbDataSet == this)
						{
							chtCtrl2.vmethod_12(sortedList);
						}
					}
					this.method_8();
					return true;
				}
			}
			return false;
		}

		// Token: 0x0600028A RID: 650 RVA: 0x0001C6B8 File Offset: 0x0001A8B8
		public HisData method_91(HisData hisData_1)
		{
			HisData result;
			if (this.CurrSymbol.IsStock)
			{
				result = this.method_92(this.CurrSymbol, this.CurrStSplitList, hisData_1, Base.UI.Form.StockRestorationMethod);
			}
			else
			{
				result = hisData_1;
			}
			return result;
		}

		// Token: 0x0600028B RID: 651 RVA: 0x0001C6F8 File Offset: 0x0001A8F8
		public HisData method_92(StkSymbol stkSymbol_1, List<StSplit> list_1, HisData hisData_1, StockRestorationMethod? nullable_0)
		{
			IEnumerable<StSplit> enumerable = this.method_100(stkSymbol_1, list_1, hisData_1.Date, nullable_0);
			HisData result;
			if (enumerable != null)
			{
				HisData hisData = hisData_1.Clone();
				if (nullable_0 == null)
				{
					nullable_0 = new StockRestorationMethod?(StockRestorationMethod.Prior);
				}
				foreach (StSplit stSplit in enumerable)
				{
					this.method_93(hisData, stSplit.BonusShares.Value, stSplit.RationedShares.Value, stSplit.RationedSharePrice.Value, stSplit.Divident.Value, nullable_0.Value);
				}
				result = hisData;
			}
			else
			{
				result = hisData_1;
			}
			return result;
		}

		// Token: 0x0600028C RID: 652 RVA: 0x0001C7C0 File Offset: 0x0001A9C0
		private HisData method_93(HisData hisData_1, decimal decimal_0, decimal decimal_1, decimal decimal_2, decimal decimal_3, StockRestorationMethod stockRestorationMethod_0)
		{
			hisData_1.Close = this.method_94(hisData_1.Close, decimal_0, decimal_1, decimal_2, decimal_3, stockRestorationMethod_0);
			hisData_1.Open = this.method_94(hisData_1.Open, decimal_0, decimal_1, decimal_2, decimal_3, stockRestorationMethod_0);
			hisData_1.High = this.method_94(hisData_1.High, decimal_0, decimal_1, decimal_2, decimal_3, stockRestorationMethod_0);
			hisData_1.Low = this.method_94(hisData_1.Low, decimal_0, decimal_1, decimal_2, decimal_3, stockRestorationMethod_0);
			return hisData_1;
		}

		// Token: 0x0600028D RID: 653 RVA: 0x0001C83C File Offset: 0x0001AA3C
		private double method_94(double double_0, decimal decimal_0, decimal decimal_1, decimal decimal_2, decimal decimal_3, StockRestorationMethod stockRestorationMethod_0)
		{
			double num;
			try
			{
				num = double_0;
				if (stockRestorationMethod_0 == StockRestorationMethod.Prior)
				{
					num = (num + (double)decimal_1 * (double)decimal_2 - (double)decimal_3) / (1.0 + (double)decimal_0 + (double)decimal_1);
				}
				else if (stockRestorationMethod_0 == StockRestorationMethod.Later)
				{
					num = num * (1.0 + (double)decimal_0 + (double)decimal_1) - ((double)decimal_1 * (double)decimal_2 - (double)decimal_3);
				}
			}
			catch
			{
				throw;
			}
			return num;
		}

		// Token: 0x0600028E RID: 654 RVA: 0x0001C8DC File Offset: 0x0001AADC
		public double method_95(double double_0, DateTime dateTime_1, StockRestorationMethod stockRestorationMethod_0)
		{
			StkSymbol currSymbol = this.CurrSymbol;
			double result;
			if (this.CurrStSplitList != null)
			{
				IEnumerable<StSplit> ienumerable_ = this.method_100(currSymbol, this.CurrStSplitList, dateTime_1, new StockRestorationMethod?(stockRestorationMethod_0));
				result = this.method_96(currSymbol, double_0, dateTime_1, ienumerable_, new StockRestorationMethod?(stockRestorationMethod_0));
			}
			else
			{
				result = double_0;
			}
			return result;
		}

		// Token: 0x0600028F RID: 655 RVA: 0x0001C928 File Offset: 0x0001AB28
		public double method_96(StkSymbol stkSymbol_1, double double_0, DateTime dateTime_1, IEnumerable<StSplit> ienumerable_0, StockRestorationMethod? nullable_0)
		{
			double num = double_0;
			double result;
			if (ienumerable_0 != null)
			{
				foreach (StSplit stSplit in ienumerable_0)
				{
					num = this.method_97(num, stSplit.BonusShares.Value, stSplit.RationedShares.Value, stSplit.RationedSharePrice.Value, stSplit.Divident.Value, nullable_0.Value);
				}
				result = num;
			}
			else
			{
				result = num;
			}
			return result;
		}

		// Token: 0x06000290 RID: 656 RVA: 0x0001C9C4 File Offset: 0x0001ABC4
		private double method_97(double double_0, decimal decimal_0, decimal decimal_1, decimal decimal_2, decimal decimal_3, StockRestorationMethod stockRestorationMethod_0)
		{
			decimal num = Convert.ToDecimal(double_0);
			decimal value = num;
			if (stockRestorationMethod_0 == StockRestorationMethod.Prior)
			{
				value = num * (1m + decimal_0 + decimal_1) - decimal_1 * decimal_2 + decimal_3;
			}
			else if (stockRestorationMethod_0 == StockRestorationMethod.Later)
			{
				value = (num + (decimal_1 * decimal_2 - decimal_3)) / (1m + decimal_0 + decimal_1);
			}
			return Convert.ToDouble(value);
		}

		// Token: 0x06000291 RID: 657 RVA: 0x0001CA48 File Offset: 0x0001AC48
		public double method_98(double double_0, DateTime dateTime_1)
		{
			StkSymbol currSymbol = this.CurrSymbol;
			StockRestorationMethod value = Base.UI.smethod_171(currSymbol);
			double result;
			if (this.CurrStSplitList != null)
			{
				IEnumerable<StSplit> ienumerable_ = this.method_100(currSymbol, this.CurrStSplitList, dateTime_1, new StockRestorationMethod?(value));
				result = this.method_99(currSymbol, double_0, dateTime_1, ienumerable_, new StockRestorationMethod?(value));
			}
			else
			{
				result = double_0;
			}
			return result;
		}

		// Token: 0x06000292 RID: 658 RVA: 0x0001CA98 File Offset: 0x0001AC98
		public double method_99(StkSymbol stkSymbol_1, double double_0, DateTime dateTime_1, IEnumerable<StSplit> ienumerable_0, StockRestorationMethod? nullable_0)
		{
			IEnumerable<StSplit> enumerable = this.method_100(stkSymbol_1, ienumerable_0, dateTime_1, nullable_0);
			double result;
			if (enumerable != null)
			{
				foreach (StSplit stSplit in enumerable)
				{
					double_0 = this.method_94(double_0, stSplit.BonusShares.Value, stSplit.RationedShares.Value, stSplit.RationedSharePrice.Value, stSplit.Divident.Value, nullable_0.Value);
				}
				result = double_0;
			}
			else
			{
				result = double_0;
			}
			return result;
		}

		// Token: 0x06000293 RID: 659 RVA: 0x0001CB40 File Offset: 0x0001AD40
		private IEnumerable<StSplit> method_100(StkSymbol stkSymbol_1, IEnumerable<StSplit> ienumerable_0, DateTime dateTime_1, StockRestorationMethod? nullable_0)
		{
			SymbDataSet.Class38 @class = new SymbDataSet.Class38();
			@class.dateTime_0 = dateTime_1;
			IEnumerable<StSplit> result = null;
			if (nullable_0 == null)
			{
				nullable_0 = new StockRestorationMethod?(StockRestorationMethod.Prior);
			}
			StockRestorationMethod? stockRestorationMethod = nullable_0;
			if (!(stockRestorationMethod.GetValueOrDefault() == StockRestorationMethod.None & stockRestorationMethod != null) && ienumerable_0 != null && stkSymbol_1.IsStock)
			{
				stockRestorationMethod = nullable_0;
				if (stockRestorationMethod.GetValueOrDefault() == StockRestorationMethod.Prior & stockRestorationMethod != null)
				{
					result = ienumerable_0.Where(new Func<StSplit, bool>(@class.method_0));
				}
				else
				{
					stockRestorationMethod = nullable_0;
					if (stockRestorationMethod.GetValueOrDefault() == StockRestorationMethod.Later & stockRestorationMethod != null)
					{
						result = ienumerable_0.Where(new Func<StSplit, bool>(@class.method_1));
					}
				}
			}
			return result;
		}

		// Token: 0x06000294 RID: 660 RVA: 0x0001CBF4 File Offset: 0x0001ADF4
		public List<StSplit> method_101(DateTime dateTime_1)
		{
			List<StSplit> result;
			if (this.CurrSymbStSpltList != null)
			{
				result = this.method_102(this.CurrSymbStSpltList.Min(new Func<StSplit, DateTime>(SymbDataSet.<>c.<>9.method_13)), dateTime_1);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06000295 RID: 661 RVA: 0x0001CC44 File Offset: 0x0001AE44
		public List<StSplit> method_102(DateTime dateTime_1, DateTime dateTime_2)
		{
			return this.method_103(this.CurrSymbStSpltList, dateTime_1, dateTime_2);
		}

		// Token: 0x06000296 RID: 662 RVA: 0x0001CC64 File Offset: 0x0001AE64
		public List<StSplit> method_103(List<StSplit> list_1, DateTime dateTime_1, DateTime dateTime_2)
		{
			SymbDataSet.Class39 @class = new SymbDataSet.Class39();
			@class.dateTime_0 = dateTime_1;
			@class.dateTime_1 = dateTime_2;
			if (list_1 != null)
			{
				IEnumerable<StSplit> source = list_1.Where(new Func<StSplit, bool>(@class.method_0));
				if (source.Any<StSplit>())
				{
					return source.OrderBy(new Func<StSplit, DateTime>(SymbDataSet.<>c.<>9.method_14)).ToList<StSplit>();
				}
			}
			return null;
		}

		// Token: 0x06000297 RID: 663 RVA: 0x0001CCD8 File Offset: 0x0001AED8
		public bool method_104(HisData hisData_1)
		{
			DateTime dateTime = this.CurrHisDataSet.method_17(hisData_1.Date);
			return hisData_1.Date.TimeOfDay == dateTime.AddMinutes(1.0).TimeOfDay;
		}

		// Token: 0x06000298 RID: 664 RVA: 0x0001CD28 File Offset: 0x0001AF28
		public List<HisData> method_105(SortedList<DateTime, HisData> sortedList_1, DateTime dateTime_1, DateTime dateTime_2, int? nullable_0)
		{
			return this.method_106(sortedList_1, dateTime_1, dateTime_2, false, true, nullable_0);
		}

		// Token: 0x06000299 RID: 665 RVA: 0x0001CD48 File Offset: 0x0001AF48
		public List<HisData> method_106(SortedList<DateTime, HisData> sortedList_1, DateTime dateTime_1, DateTime dateTime_2, bool bool_1, bool bool_2, int? nullable_0)
		{
			SymbDataSet.Class40 @class = new SymbDataSet.Class40();
			@class.dateTime_0 = dateTime_1;
			@class.dateTime_1 = dateTime_2;
			List<HisData> result;
			if (sortedList_1 == null)
			{
				result = null;
			}
			else
			{
				if (nullable_0 == null)
				{
					nullable_0 = new int?(60);
				}
				List<HisData> list = null;
				int num = -1;
				DateTime dateTime;
				if (bool_1)
				{
					dateTime = @class.dateTime_0;
				}
				else
				{
					dateTime = @class.dateTime_0.AddMinutes((double)nullable_0.Value);
				}
				if (dateTime >= sortedList_1.Keys.First<DateTime>())
				{
					num = sortedList_1.IndexOfKey(dateTime);
					if (num < 0)
					{
						bool flag = false;
						ExchgOBT exchgOBT = this.method_67(@class.dateTime_0);
						if (exchgOBT.GetDayCloseDT(@class.dateTime_0) == @class.dateTime_0)
						{
							flag = true;
						}
						else
						{
							DateTime? nightTradingCloseDT = exchgOBT.GetNightTradingCloseDT(@class.dateTime_0);
							if (nightTradingCloseDT != null && nightTradingCloseDT.Value == @class.dateTime_0)
							{
								flag = true;
							}
						}
						if (flag)
						{
							num = sortedList_1.IndexOfKey(@class.dateTime_0);
							if (num >= 0 && !bool_1)
							{
								num++;
							}
						}
					}
				}
				if (num > -1)
				{
					DateTime dateTime2;
					if (bool_2)
					{
						dateTime2 = @class.dateTime_1;
					}
					else
					{
						dateTime2 = @class.dateTime_1.AddMinutes((double)(-(double)nullable_0.Value));
					}
					if (dateTime2 <= sortedList_1.Keys.Last<DateTime>())
					{
						int num2 = sortedList_1.IndexOfKey(dateTime2);
						if (num2 > -1)
						{
							list = new List<HisData>();
							for (int i = num; i <= num2; i++)
							{
								list.Add(sortedList_1.Values[i]);
							}
						}
					}
				}
				if (list == null && @class.dateTime_0 <= sortedList_1.Keys.Last<DateTime>() && @class.dateTime_1 >= sortedList_1.Keys.First<DateTime>())
				{
					if (bool_1)
					{
						if (bool_2)
						{
							list = sortedList_1.Values.Where(new Func<HisData, bool>(@class.method_0)).ToList<HisData>();
						}
						else
						{
							list = sortedList_1.Values.Where(new Func<HisData, bool>(@class.method_1)).ToList<HisData>();
						}
					}
					else if (bool_2)
					{
						list = sortedList_1.Values.Where(new Func<HisData, bool>(@class.method_2)).ToList<HisData>();
					}
					else
					{
						list = sortedList_1.Values.Where(new Func<HisData, bool>(@class.method_3)).ToList<HisData>();
					}
				}
				result = list;
			}
			return result;
		}

		// Token: 0x0600029A RID: 666 RVA: 0x0001CF88 File Offset: 0x0001B188
		public bool method_107(DateTime dateTime_1)
		{
			bool result;
			if (dateTime_1 >= this.CurrStkMeta.BeginDate.Value)
			{
				result = (dateTime_1 <= this.CurrStkMeta.EndDate.Value);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600029B RID: 667 RVA: 0x0001CFD4 File Offset: 0x0001B1D4
		public DateTime method_108()
		{
			DateTime dateTime = this.CurrStkMeta.BeginDate.Value;
			if (!TApp.IsTrialUser)
			{
				dateTime = dateTime.AddMonths(1);
			}
			if (dateTime > this.CurrStkMeta.EndDate.Value)
			{
				dateTime = this.CurrStkMeta.EndDate.Value;
			}
			return dateTime;
		}

		// Token: 0x0600029C RID: 668 RVA: 0x0001D03C File Offset: 0x0001B23C
		public DateTime method_109()
		{
			return this.CurrStkMeta.EndDate.Value;
		}

		// Token: 0x170000A4 RID: 164
		// (get) Token: 0x0600029D RID: 669 RVA: 0x0001D060 File Offset: 0x0001B260
		// (set) Token: 0x0600029E RID: 670 RVA: 0x0000341F File Offset: 0x0000161F
		public StkSymbol CurrSymbol
		{
			get
			{
				return this.stkSymbol_0;
			}
			set
			{
				if ((this.stkSymbol_0 == null && value != null) || (this.stkSymbol_0 != null && value == null) || (this.stkSymbol_0 != null && value != null && value.ID != this.stkSymbol_0.ID))
				{
					this.stkSymbol_0 = value;
				}
			}
		}

		// Token: 0x170000A5 RID: 165
		// (get) Token: 0x0600029F RID: 671 RVA: 0x0001D078 File Offset: 0x0001B278
		public int SymblID
		{
			get
			{
				return this.CurrSymbol.ID;
			}
		}

		// Token: 0x170000A6 RID: 166
		// (get) Token: 0x060002A0 RID: 672 RVA: 0x0001D094 File Offset: 0x0001B294
		// (set) Token: 0x060002A1 RID: 673 RVA: 0x0000345E File Offset: 0x0000165E
		public SortedList<DateTime, HisData> HisDataList
		{
			get
			{
				return this.sortedList_0;
			}
			set
			{
				this.sortedList_0 = value;
			}
		}

		// Token: 0x170000A7 RID: 167
		// (get) Token: 0x060002A2 RID: 674 RVA: 0x0001D0AC File Offset: 0x0001B2AC
		// (set) Token: 0x060002A3 RID: 675 RVA: 0x00003469 File Offset: 0x00001669
		public HisDataPeriodSet Curr1hPeriodHisData
		{
			get
			{
				return this.hisDataPeriodSet_0;
			}
			set
			{
				if (this.hisDataPeriodSet_0 != value)
				{
					this.hisDataPeriodSet_0 = value;
					this.method_2();
				}
			}
		}

		// Token: 0x170000A8 RID: 168
		// (get) Token: 0x060002A4 RID: 676 RVA: 0x0001D0C4 File Offset: 0x0001B2C4
		// (set) Token: 0x060002A5 RID: 677 RVA: 0x0001D0DC File Offset: 0x0001B2DC
		public HisDataSet CurrHisDataSet
		{
			get
			{
				return this.hisDataSet_0;
			}
			set
			{
				this.hisDataSet_0 = value;
				if (this.hisDataSet_0 != null)
				{
					this.hisDataSet_0.CurrHisDataChanged += this.method_75;
					this.hisDataSet_0.CurrDateChanged += this.method_76;
				}
			}
		}

		// Token: 0x170000A9 RID: 169
		// (get) Token: 0x060002A6 RID: 678 RVA: 0x0001D128 File Offset: 0x0001B328
		public HisData CurrHisData
		{
			get
			{
				HisData result = null;
				if (this.CurrHisDataSet != null)
				{
					result = this.CurrHisDataSet.CurrHisData;
				}
				return result;
			}
		}

		// Token: 0x170000AA RID: 170
		// (get) Token: 0x060002A7 RID: 679 RVA: 0x0001D150 File Offset: 0x0001B350
		public bool HasValidDataSet
		{
			get
			{
				bool result;
				if (this.CurrHisDataSet != null)
				{
					result = (this.CurrHisData != null);
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x170000AB RID: 171
		// (get) Token: 0x060002A8 RID: 680 RVA: 0x0001D178 File Offset: 0x0001B378
		public DateTime? CurrDate
		{
			get
			{
				DateTime? result;
				if (this.CurrHisData != null)
				{
					result = new DateTime?(this.CurrHisData.Date);
				}
				else
				{
					result = null;
				}
				return result;
			}
		}

		// Token: 0x170000AC RID: 172
		// (get) Token: 0x060002A9 RID: 681 RVA: 0x0001D1B0 File Offset: 0x0001B3B0
		public UsrStkMeta CurrStkMeta
		{
			get
			{
				if (this.usrStkMeta_0 == null || this.usrStkMeta_0.StkId != this.CurrSymbol.ID)
				{
					this.usrStkMeta_0 = Base.Data.smethod_90(this.CurrSymbol.ID);
				}
				return this.usrStkMeta_0;
			}
		}

		// Token: 0x170000AD RID: 173
		// (get) Token: 0x060002AA RID: 682 RVA: 0x0001D200 File Offset: 0x0001B400
		// (set) Token: 0x060002AB RID: 683 RVA: 0x00003483 File Offset: 0x00001683
		public List<StSplit> CurrSymbStSpltList
		{
			get
			{
				return this.list_0;
			}
			set
			{
				this.list_0 = value;
			}
		}

		// Token: 0x170000AE RID: 174
		// (get) Token: 0x060002AC RID: 684 RVA: 0x0001D218 File Offset: 0x0001B418
		public List<StSplit> CurrStSplitList
		{
			get
			{
				return this.method_110();
			}
		}

		// Token: 0x170000AF RID: 175
		// (get) Token: 0x060002AD RID: 685 RVA: 0x0001D230 File Offset: 0x0001B430
		public bool CurrSymblSupportsShort
		{
			get
			{
				return Base.Data.smethod_124(this.CurrSymbol);
			}
		}

		// Token: 0x170000B0 RID: 176
		// (get) Token: 0x060002AE RID: 686 RVA: 0x0001D24C File Offset: 0x0001B44C
		// (set) Token: 0x060002AF RID: 687 RVA: 0x0000348E File Offset: 0x0000168E
		public bool IsNextHdDayBegin
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x170000B1 RID: 177
		// (get) Token: 0x060002B0 RID: 688 RVA: 0x0001D264 File Offset: 0x0001B464
		public bool ShouldChkRationedPrice
		{
			get
			{
				bool result;
				if (this.CurrSymbol.IsStock)
				{
					if (Base.UI.Form.StockRestorationMethod != null)
					{
						result = (Base.UI.Form.StockRestorationMethod.Value != StockRestorationMethod.None);
					}
					else
					{
						result = true;
					}
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x170000B2 RID: 178
		// (get) Token: 0x060002B1 RID: 689 RVA: 0x0001D2B8 File Offset: 0x0001B4B8
		// (set) Token: 0x060002B2 RID: 690 RVA: 0x00003499 File Offset: 0x00001699
		public HisData LastHisData
		{
			get
			{
				return this.hisData_0;
			}
			set
			{
				this.hisData_0 = value;
			}
		}

		// Token: 0x170000B3 RID: 179
		// (get) Token: 0x060002B3 RID: 691 RVA: 0x0001D2D0 File Offset: 0x0001B4D0
		// (set) Token: 0x060002B4 RID: 692 RVA: 0x0001D2E8 File Offset: 0x0001B4E8
		public DateTime DateTimeOfLastRec
		{
			get
			{
				return this.dateTime_0;
			}
			set
			{
				if (value == default(DateTime))
				{
					throw new Exception("_DateTimeOfLastRec error!");
				}
				if (this.dateTime_0 != value)
				{
					this.dateTime_0 = value;
				}
			}
		}

		// Token: 0x170000B4 RID: 180
		// (get) Token: 0x060002B5 RID: 693 RVA: 0x0001D328 File Offset: 0x0001B528
		public bool IsCurrDateNotListedYet
		{
			get
			{
				return this.DateTimeOfLastRec < this.CurrStkMeta.BeginDate.Value;
			}
		}

		// Token: 0x170000B5 RID: 181
		// (get) Token: 0x060002B6 RID: 694 RVA: 0x0001D358 File Offset: 0x0001B558
		public List<HisData> LastMovedHisDataList
		{
			get
			{
				List<HisData> list = new List<HisData>();
				if (Base.UI.Form.IsSpanMoveNext && Base.UI.Form.LastSpanMoveDT != null)
				{
					int num = Base.UI.Form.SpanMoveChtCtrl.PeriodHisDataList.IndexOfKey(Base.UI.Form.LastSpanMoveDT.Value);
					if (num > 0)
					{
						DateTime dateTime_ = Base.UI.Form.SpanMoveChtCtrl.PeriodHisDataList.Keys[num - 1];
						if (this.CurrHisDataSet != null && this.CurrHisDataSet.FetchedHisDataList != null)
						{
							list = Base.Data.smethod_58(this.CurrHisDataSet.FetchedHisDataList, dateTime_, Base.UI.Form.LastSpanMoveDT.Value, false, true);
						}
						else if (Base.UI.Form.SpanMoveChtCtrl.IsPeriodLong && this.Curr1hPeriodHisData != null)
						{
							list = Base.Data.smethod_58(this.Curr1hPeriodHisData.PeriodHisDataList, dateTime_, Base.UI.Form.LastSpanMoveDT.Value, false, true);
						}
					}
				}
				if (list == null || !list.Any<HisData>())
				{
					if (list == null)
					{
						list = new List<HisData>();
					}
					list.Add(this.CurrHisDataSet.CurrHisData);
				}
				return list;
			}
		}

		// Token: 0x060002B7 RID: 695 RVA: 0x0001D484 File Offset: 0x0001B684
		private List<StSplit> method_110()
		{
			List<StSplit> result;
			if (this.HasValidDataSet)
			{
				DateTime value = this.CurrStkMeta.DatInfo_1h.BeginDate.Value;
				DateTime dateTime_;
				if ((Base.UI.Form.IsSpanMoveNext || Base.UI.Form.IsSpanMovePrev) && Base.UI.Form.LastSpanMoveDT != null)
				{
					dateTime_ = Base.UI.Form.LastSpanMoveDT.Value;
				}
				else
				{
					dateTime_ = this.CurrHisDataSet.CurrHisData.Date;
				}
				result = this.method_102(value, dateTime_);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060002B9 RID: 697 RVA: 0x0001D514 File Offset: 0x0001B714
		[CompilerGenerated]
		private bool method_111(KeyValuePair<int, DateTime> keyValuePair_0)
		{
			return keyValuePair_0.Key == this.SymblID;
		}

		// Token: 0x060002BA RID: 698 RVA: 0x0001D534 File Offset: 0x0001B734
		[CompilerGenerated]
		private bool method_112(KeyValuePair<DateTime, HisData> keyValuePair_0)
		{
			return keyValuePair_0.Key >= this.CurrStkMeta.BeginDate.Value;
		}

		// Token: 0x060002BB RID: 699 RVA: 0x0001D564 File Offset: 0x0001B764
		[CompilerGenerated]
		private bool method_113(StkSymbol stkSymbol_1)
		{
			return stkSymbol_1.MstSymbol.ID == this.stkSymbol_0.ID;
		}

		// Token: 0x040000C6 RID: 198
		private static readonly string string_0 = "正在提取数据...";

		// Token: 0x040000C7 RID: 199
		private const int int_0 = 6000;

		// Token: 0x040000C8 RID: 200
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x040000C9 RID: 201
		[CompilerGenerated]
		private EventHandler eventHandler_1;

		// Token: 0x040000CA RID: 202
		[CompilerGenerated]
		private EventHandler eventHandler_2;

		// Token: 0x040000CB RID: 203
		[CompilerGenerated]
		private EventHandler eventHandler_3;

		// Token: 0x040000CC RID: 204
		[CompilerGenerated]
		private EventHandler eventHandler_4;

		// Token: 0x040000CD RID: 205
		[CompilerGenerated]
		private EventHandler eventHandler_5;

		// Token: 0x040000CE RID: 206
		[CompilerGenerated]
		private EventHandler eventHandler_6;

		// Token: 0x040000CF RID: 207
		[CompilerGenerated]
		private EventHandler eventHandler_7;

		// Token: 0x040000D0 RID: 208
		[CompilerGenerated]
		private EventHandler eventHandler_8;

		// Token: 0x040000D1 RID: 209
		[CompilerGenerated]
		private EventHandler eventHandler_9;

		// Token: 0x040000D2 RID: 210
		[CompilerGenerated]
		private EventHandler eventHandler_10;

		// Token: 0x040000D3 RID: 211
		[CompilerGenerated]
		private EventHandler eventHandler_11;

		// Token: 0x040000D4 RID: 212
		[CompilerGenerated]
		private EventHandler eventHandler_12;

		// Token: 0x040000D5 RID: 213
		[CompilerGenerated]
		private EventHandler eventHandler_13;

		// Token: 0x040000D6 RID: 214
		[CompilerGenerated]
		private Delegate5 delegate5_0;

		// Token: 0x040000D7 RID: 215
		[CompilerGenerated]
		private EventHandler eventHandler_14;

		// Token: 0x040000D8 RID: 216
		private StkSymbol stkSymbol_0;

		// Token: 0x040000D9 RID: 217
		private SortedList<DateTime, HisData> sortedList_0;

		// Token: 0x040000DA RID: 218
		private HisDataPeriodSet hisDataPeriodSet_0;

		// Token: 0x040000DB RID: 219
		private HisDataSet hisDataSet_0;

		// Token: 0x040000DC RID: 220
		private UsrStkMeta usrStkMeta_0;

		// Token: 0x040000DD RID: 221
		private List<StSplit> list_0;

		// Token: 0x040000DE RID: 222
		private bool bool_0;

		// Token: 0x040000DF RID: 223
		private HisData hisData_0;

		// Token: 0x040000E0 RID: 224
		private DateTime dateTime_0;

		// Token: 0x02000043 RID: 67
		[CompilerGenerated]
		private sealed class Class23
		{
			// Token: 0x060002BD RID: 701 RVA: 0x0001D590 File Offset: 0x0001B790
			internal bool method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				return keyValuePair_0.Key.Date >= this.dateTime_0.Date.AddHours(8.0);
			}

			// Token: 0x040000E1 RID: 225
			public DateTime dateTime_0;
		}

		// Token: 0x02000044 RID: 68
		[CompilerGenerated]
		private sealed class Class24
		{
			// Token: 0x060002BF RID: 703 RVA: 0x0001D5D4 File Offset: 0x0001B7D4
			internal bool method_0(HisData hisData_0)
			{
				return hisData_0.Date > this.dateTime_0;
			}

			// Token: 0x040000E2 RID: 226
			public DateTime dateTime_0;
		}

		// Token: 0x02000046 RID: 70
		[CompilerGenerated]
		private sealed class Class25
		{
			// Token: 0x060002D2 RID: 722 RVA: 0x0001D734 File Offset: 0x0001B934
			internal bool method_0(DateTime dateTime_0)
			{
				return dateTime_0 > this.nullable_0.Value;
			}

			// Token: 0x060002D3 RID: 723 RVA: 0x0001D758 File Offset: 0x0001B958
			internal bool method_1(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				return keyValuePair_0.Key > this.nullable_0.Value;
			}

			// Token: 0x040000F3 RID: 243
			public DateTime? nullable_0;
		}

		// Token: 0x02000047 RID: 71
		[CompilerGenerated]
		private sealed class Class26
		{
			// Token: 0x060002D5 RID: 725 RVA: 0x0001D780 File Offset: 0x0001B980
			internal bool method_0(DateTime dateTime_1)
			{
				return dateTime_1 > this.dateTime_0;
			}

			// Token: 0x040000F4 RID: 244
			public DateTime dateTime_0;
		}

		// Token: 0x02000048 RID: 72
		[CompilerGenerated]
		private sealed class Class27
		{
			// Token: 0x060002D7 RID: 727 RVA: 0x0001D7A0 File Offset: 0x0001B9A0
			internal bool method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				return keyValuePair_0.Key.Date > this.nullable_0.Value;
			}

			// Token: 0x060002D8 RID: 728 RVA: 0x0001D7D0 File Offset: 0x0001B9D0
			internal bool method_1(DateTime dateTime_0)
			{
				return dateTime_0 > this.nullable_0.Value;
			}

			// Token: 0x060002D9 RID: 729 RVA: 0x0001D7A0 File Offset: 0x0001B9A0
			internal bool method_2(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				return keyValuePair_0.Key.Date > this.nullable_0.Value;
			}

			// Token: 0x040000F5 RID: 245
			public DateTime? nullable_0;
		}

		// Token: 0x02000049 RID: 73
		[CompilerGenerated]
		private sealed class Class28
		{
			// Token: 0x060002DB RID: 731 RVA: 0x0001D7F4 File Offset: 0x0001B9F4
			internal bool method_0(HDFileInfo hdfileInfo_0)
			{
				bool result;
				if (hdfileInfo_0.StkID == this.int_0 && hdfileInfo_0.IsPeriod_1h() && hdfileInfo_0.BeginDate.Year >= this.int_1)
				{
					result = (hdfileInfo_0.EndDate.Year <= this.int_2);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x040000F6 RID: 246
			public int int_0;

			// Token: 0x040000F7 RID: 247
			public int int_1;

			// Token: 0x040000F8 RID: 248
			public int int_2;
		}

		// Token: 0x0200004A RID: 74
		[CompilerGenerated]
		private sealed class Class29
		{
			// Token: 0x060002DD RID: 733 RVA: 0x0001D850 File Offset: 0x0001BA50
			internal bool method_0(DateTime dateTime_1)
			{
				return dateTime_1 < this.dateTime_0;
			}

			// Token: 0x040000F9 RID: 249
			public DateTime dateTime_0;
		}

		// Token: 0x0200004B RID: 75
		[CompilerGenerated]
		private sealed class Class30
		{
			// Token: 0x060002DF RID: 735 RVA: 0x0001D870 File Offset: 0x0001BA70
			internal bool method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				bool result;
				if (keyValuePair_0.Key < this.dateTime_0)
				{
					result = (keyValuePair_0.Key > this.dateTime_1);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x040000FA RID: 250
			public DateTime dateTime_0;

			// Token: 0x040000FB RID: 251
			public DateTime dateTime_1;
		}

		// Token: 0x0200004C RID: 76
		[CompilerGenerated]
		private sealed class Class31
		{
			// Token: 0x060002E1 RID: 737 RVA: 0x0001D8AC File Offset: 0x0001BAAC
			internal bool method_0(DateTime dateTime_1)
			{
				return dateTime_1 >= this.dateTime_0;
			}

			// Token: 0x040000FC RID: 252
			public DateTime dateTime_0;
		}

		// Token: 0x0200004D RID: 77
		[CompilerGenerated]
		private sealed class Class32
		{
			// Token: 0x060002E3 RID: 739 RVA: 0x0001D8CC File Offset: 0x0001BACC
			internal bool method_0(StSplit stSplit_0)
			{
				return stSplit_0.StockId == this.int_0;
			}

			// Token: 0x040000FD RID: 253
			public int int_0;
		}

		// Token: 0x0200004E RID: 78
		[CompilerGenerated]
		private sealed class Class33
		{
			// Token: 0x060002E5 RID: 741 RVA: 0x0001D8EC File Offset: 0x0001BAEC
			internal bool method_0(StSplit stSplit_0)
			{
				return stSplit_0.Date.Date == this.dateTime_0.Date;
			}

			// Token: 0x060002E6 RID: 742 RVA: 0x0001D91C File Offset: 0x0001BB1C
			internal bool method_1(StSplit stSplit_0)
			{
				return stSplit_0.Date.Date == this.dateTime_1.Date;
			}

			// Token: 0x060002E7 RID: 743 RVA: 0x0001D94C File Offset: 0x0001BB4C
			internal bool method_2(StSplit stSplit_0)
			{
				bool result;
				if (stSplit_0.Date.Date > this.dateTime_1.Date && stSplit_0.Date.Date < this.dateTime_0.Date)
				{
					result = true;
				}
				else if (stSplit_0.Date.Date < this.dateTime_1.Date)
				{
					result = (stSplit_0.Date.Date > this.dateTime_0.Date);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x040000FE RID: 254
			public DateTime dateTime_0;

			// Token: 0x040000FF RID: 255
			public DateTime dateTime_1;
		}

		// Token: 0x0200004F RID: 79
		[CompilerGenerated]
		private sealed class Class34
		{
			// Token: 0x060002E9 RID: 745 RVA: 0x0001D9E4 File Offset: 0x0001BBE4
			internal bool method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				return keyValuePair_0.Key > this.stSplit_0.Date;
			}

			// Token: 0x060002EA RID: 746 RVA: 0x0001DA0C File Offset: 0x0001BC0C
			internal bool method_1(TranStock tranStock_0)
			{
				bool result;
				if (tranStock_0.IsRational)
				{
					decimal oriPrice = tranStock_0.OriPrice;
					decimal? rationedSharePrice = this.stSplit_0.RationedSharePrice;
					result = (oriPrice == rationedSharePrice.GetValueOrDefault() & rationedSharePrice != null);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000100 RID: 256
			public StSplit stSplit_0;
		}

		// Token: 0x02000050 RID: 80
		[CompilerGenerated]
		private sealed class Class35
		{
			// Token: 0x060002EC RID: 748 RVA: 0x0001DA50 File Offset: 0x0001BC50
			internal bool method_0(DateTime dateTime_0)
			{
				return dateTime_0 <= this.nullable_0.Value;
			}

			// Token: 0x04000101 RID: 257
			public DateTime? nullable_0;
		}

		// Token: 0x02000051 RID: 81
		[CompilerGenerated]
		private sealed class Class36
		{
			// Token: 0x060002EE RID: 750 RVA: 0x0001DA74 File Offset: 0x0001BC74
			internal bool method_0(KeyValuePair<DateTime, HisData> keyValuePair_0)
			{
				return keyValuePair_0.Key < this.dateTime_0.AddDays(1.0);
			}

			// Token: 0x04000102 RID: 258
			public DateTime dateTime_0;
		}

		// Token: 0x02000052 RID: 82
		[CompilerGenerated]
		private sealed class Class37
		{
			// Token: 0x060002F0 RID: 752 RVA: 0x0001DAA8 File Offset: 0x0001BCA8
			internal bool method_0(DateTime dateTime_1)
			{
				return dateTime_1 <= this.dateTime_0;
			}

			// Token: 0x04000103 RID: 259
			public DateTime dateTime_0;
		}

		// Token: 0x02000053 RID: 83
		[CompilerGenerated]
		private sealed class Class38
		{
			// Token: 0x060002F2 RID: 754 RVA: 0x0001DAC8 File Offset: 0x0001BCC8
			internal bool method_0(StSplit stSplit_0)
			{
				return stSplit_0.Date.Date > this.dateTime_0;
			}

			// Token: 0x060002F3 RID: 755 RVA: 0x0001DAF4 File Offset: 0x0001BCF4
			internal bool method_1(StSplit stSplit_0)
			{
				return stSplit_0.Date.Date < this.dateTime_0;
			}

			// Token: 0x04000104 RID: 260
			public DateTime dateTime_0;
		}

		// Token: 0x02000054 RID: 84
		[CompilerGenerated]
		private sealed class Class39
		{
			// Token: 0x060002F5 RID: 757 RVA: 0x0001DB20 File Offset: 0x0001BD20
			internal bool method_0(StSplit stSplit_0)
			{
				bool result;
				if (stSplit_0.Date >= this.dateTime_0.Date)
				{
					result = (stSplit_0.Date <= this.dateTime_1.Date);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000105 RID: 261
			public DateTime dateTime_0;

			// Token: 0x04000106 RID: 262
			public DateTime dateTime_1;
		}

		// Token: 0x02000055 RID: 85
		[CompilerGenerated]
		private sealed class Class40
		{
			// Token: 0x060002F7 RID: 759 RVA: 0x0001DB64 File Offset: 0x0001BD64
			internal bool method_0(HisData hisData_0)
			{
				bool result;
				if (hisData_0.Date >= this.dateTime_0)
				{
					result = (hisData_0.Date <= this.dateTime_1);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x060002F8 RID: 760 RVA: 0x0001DBA0 File Offset: 0x0001BDA0
			internal bool method_1(HisData hisData_0)
			{
				bool result;
				if (hisData_0.Date >= this.dateTime_0)
				{
					result = (hisData_0.Date < this.dateTime_1);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x060002F9 RID: 761 RVA: 0x0001DBDC File Offset: 0x0001BDDC
			internal bool method_2(HisData hisData_0)
			{
				bool result;
				if (hisData_0.Date > this.dateTime_0)
				{
					result = (hisData_0.Date <= this.dateTime_1);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x060002FA RID: 762 RVA: 0x0001DC18 File Offset: 0x0001BE18
			internal bool method_3(HisData hisData_0)
			{
				bool result;
				if (hisData_0.Date > this.dateTime_0)
				{
					result = (hisData_0.Date < this.dateTime_1);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000107 RID: 263
			public DateTime dateTime_0;

			// Token: 0x04000108 RID: 264
			public DateTime dateTime_1;
		}
	}
}
