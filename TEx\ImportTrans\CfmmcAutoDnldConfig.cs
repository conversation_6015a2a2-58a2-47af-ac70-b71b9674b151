﻿using System;

namespace TEx.ImportTrans
{
	// Token: 0x02000355 RID: 853
	[Serializable]
	public sealed class CfmmcAutoDnldConfig
	{
		// Token: 0x1700061B RID: 1563
		// (get) Token: 0x060023A3 RID: 9123 RVA: 0x000F0A48 File Offset: 0x000EEC48
		// (set) Token: 0x060023A4 RID: 9124 RVA: 0x0000DEC1 File Offset: 0x0000C0C1
		public AutoDownCfmmcFrequencyEnum Frequency
		{
			get
			{
				return this._Frequency;
			}
			set
			{
				this._Frequency = value;
			}
		}

		// Token: 0x1700061C RID: 1564
		// (get) Token: 0x060023A5 RID: 9125 RVA: 0x000F0A60 File Offset: 0x000EEC60
		// (set) Token: 0x060023A6 RID: 9126 RVA: 0x0000DECC File Offset: 0x0000C0CC
		public bool AutoDownOnStartup
		{
			get
			{
				return this._AutoDownOnStartup;
			}
			set
			{
				this._AutoDownOnStartup = value;
			}
		}

		// Token: 0x1700061D RID: 1565
		// (get) Token: 0x060023A7 RID: 9127 RVA: 0x000F0A78 File Offset: 0x000EEC78
		// (set) Token: 0x060023A8 RID: 9128 RVA: 0x0000DED7 File Offset: 0x0000C0D7
		public bool AutoDownPeriodly
		{
			get
			{
				return this._AutoDownPeriodly;
			}
			set
			{
				this._AutoDownPeriodly = value;
			}
		}

		// Token: 0x1700061E RID: 1566
		// (get) Token: 0x060023A9 RID: 9129 RVA: 0x000F0A90 File Offset: 0x000EEC90
		// (set) Token: 0x060023AA RID: 9130 RVA: 0x0000DEE2 File Offset: 0x0000C0E2
		public DateTime BeginTime
		{
			get
			{
				return this._BeginTime;
			}
			set
			{
				this._BeginTime = value;
			}
		}

		// Token: 0x1700061F RID: 1567
		// (get) Token: 0x060023AB RID: 9131 RVA: 0x000F0AA8 File Offset: 0x000EECA8
		// (set) Token: 0x060023AC RID: 9132 RVA: 0x0000DEED File Offset: 0x0000C0ED
		public DayOfWeek WklyDnldDayOfWeek
		{
			get
			{
				return this._WklyDnldDayOfWeek;
			}
			set
			{
				this._WklyDnldDayOfWeek = value;
			}
		}

		// Token: 0x04001135 RID: 4405
		private AutoDownCfmmcFrequencyEnum _Frequency;

		// Token: 0x04001136 RID: 4406
		private bool _AutoDownOnStartup;

		// Token: 0x04001137 RID: 4407
		private bool _AutoDownPeriodly;

		// Token: 0x04001138 RID: 4408
		private DateTime _BeginTime;

		// Token: 0x04001139 RID: 4409
		private DayOfWeek _WklyDnldDayOfWeek;
	}
}
