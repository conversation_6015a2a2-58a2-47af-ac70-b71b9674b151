﻿using System;
using System.Runtime.CompilerServices;
using ns14;
using ns28;
using TEx.Inds;
using TEx.SIndicator;

namespace ns30
{
	// Token: 0x0200030E RID: 782
	internal class Class412 : Class408
	{
		// Token: 0x170005CE RID: 1486
		// (get) Token: 0x060021BD RID: 8637 RVA: 0x000E76D8 File Offset: 0x000E58D8
		// (set) Token: 0x060021BE RID: 8638 RVA: 0x0000D840 File Offset: 0x0000BA40
		public override Class408 Left { get; protected set; }

		// Token: 0x170005CF RID: 1487
		// (get) Token: 0x060021BF RID: 8639 RVA: 0x000E76F0 File Offset: 0x000E58F0
		// (set) Token: 0x060021C0 RID: 8640 RVA: 0x0000D84B File Offset: 0x0000BA4B
		public override Class408 Right { get; protected set; }

		// Token: 0x170005D0 RID: 1488
		// (get) Token: 0x060021C1 RID: 8641 RVA: 0x000E7708 File Offset: 0x000E5908
		// (set) Token: 0x060021C2 RID: 8642 RVA: 0x0000D856 File Offset: 0x0000BA56
		public override HToken Token { get; protected set; }

		// Token: 0x060021C3 RID: 8643 RVA: 0x0000D861 File Offset: 0x0000BA61
		public Class412(HToken htoken_1, Class408 class408_2, Class408 class408_3)
		{
			this.Token = htoken_1;
			this.Left = class408_2;
			this.Right = class408_3;
		}

		// Token: 0x060021C4 RID: 8644 RVA: 0x000E7720 File Offset: 0x000E5920
		protected DataArray method_0(int int_0, double double_0)
		{
			return new DataArray(int_0, double_0);
		}

		// Token: 0x060021C5 RID: 8645 RVA: 0x000E7738 File Offset: 0x000E5938
		protected virtual object vmethod_2(object object_0, object object_1)
		{
			object result;
			if (object_0.GetType() == typeof(double) && object_1.GetType() == typeof(double))
			{
				result = this.vmethod_3((double)object_0, (double)object_1);
			}
			else if (object_0.GetType() == typeof(DataArray) && object_1.GetType() == typeof(DataArray))
			{
				result = this.vmethod_4(object_0 as DataArray, object_1 as DataArray);
			}
			else if (object_0.GetType() == typeof(DataArray) && object_1.GetType() == typeof(double))
			{
				DataArray dataArray_ = this.method_0((object_0 as DataArray).Data.Length, (double)object_1);
				result = this.vmethod_4(object_0 as DataArray, dataArray_);
			}
			else
			{
				if (object_0.GetType() != typeof(double) || object_1.GetType() != typeof(DataArray))
				{
					throw new Exception(this.Token.method_0("的操作数不是double或者DataArray类型"));
				}
				DataArray dataArray_2 = this.method_0((object_1 as DataArray).Data.Length, (double)object_0);
				result = this.vmethod_4(dataArray_2, object_1 as DataArray);
			}
			return result;
		}

		// Token: 0x060021C6 RID: 8646 RVA: 0x0000D880 File Offset: 0x0000BA80
		protected virtual double vmethod_3(double double_0, double double_1)
		{
			throw new Exception("在派生类中调用。");
		}

		// Token: 0x060021C7 RID: 8647 RVA: 0x000E7878 File Offset: 0x000E5A78
		protected virtual DataArray vmethod_4(DataArray dataArray_0, DataArray dataArray_1)
		{
			throw new Exception("在派生类中调用");
		}

		// Token: 0x060021C8 RID: 8648 RVA: 0x000E7890 File Offset: 0x000E5A90
		private string method_1(Class408 class408_2)
		{
			Type type = class408_2.GetType();
			string result;
			if (type == typeof(Class409))
			{
				result = ((Class409)class408_2).vmethod_0();
			}
			else
			{
				if (type != typeof(Class412))
				{
					throw new Exception("类型错误");
				}
				result = ((Class412)class408_2).vmethod_0();
			}
			return result;
		}

		// Token: 0x060021C9 RID: 8649 RVA: 0x000E78E8 File Offset: 0x000E5AE8
		public override string vmethod_0()
		{
			return this.method_1(this.Left) + this.Token.Symbol.Name + this.method_1(this.Right);
		}

		// Token: 0x060021CA RID: 8650 RVA: 0x0000D88C File Offset: 0x0000BA8C
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			throw new Exception(this.Token.method_0("不可解析"));
		}

		// Token: 0x04001064 RID: 4196
		[CompilerGenerated]
		private Class408 class408_0;

		// Token: 0x04001065 RID: 4197
		[CompilerGenerated]
		private Class408 class408_1;

		// Token: 0x04001066 RID: 4198
		[CompilerGenerated]
		private HToken htoken_0;
	}
}
