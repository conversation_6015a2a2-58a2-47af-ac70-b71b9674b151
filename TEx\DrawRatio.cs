﻿using System;
using System.Collections.Generic;
using System.Drawing.Drawing2D;
using System.Runtime.Serialization;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000071 RID: 113
	[Serializable]
	internal class DrawRatio : DrawObj, ISerializable
	{
		// Token: 0x06000413 RID: 1043 RVA: 0x000037AA File Offset: 0x000019AA
		public DrawRatio()
		{
		}

		// Token: 0x06000414 RID: 1044 RVA: 0x00003C0C File Offset: 0x00001E0C
		public DrawRatio(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = "百分比线";
			base.CanChgColor = true;
			base.IsOneClickLoc = false;
			this.IfShowTopDashLine = true;
			this.bool_6 = true;
		}

		// Token: 0x06000415 RID: 1045 RVA: 0x0002274C File Offset: 0x0002094C
		protected DrawRatio(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
			try
			{
				this.bool_7 = info.GetBoolean("IfShowTopDashLine");
			}
			catch
			{
				this.bool_7 = true;
			}
		}

		// Token: 0x06000416 RID: 1046 RVA: 0x00003C44 File Offset: 0x00001E44
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
			info.AddValue("IfShowTopDashLine", this.bool_7);
		}

		// Token: 0x06000417 RID: 1047 RVA: 0x00022798 File Offset: 0x00020998
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_6)
		{
			List<GraphObj> list = new List<GraphObj>();
			if (Base.UI.DrawingObj == this || base.MouseHovering || base.SelectBoxVisible)
			{
				LineObj lineObj = base.method_23(double_1, double_2, double_3, double_4, string_6);
				lineObj.Line.Style = DashStyle.Dash;
				lineObj.Tag = base.Tag + DrawRatio.string_5;
				list.Add(lineObj);
			}
			this.method_39(list, chartCS_1, double_1, double_2, double_3, double_4, string_6);
			this.vmethod_24(list, chartCS_1, double_1, double_2, double_3, double_4, string_6);
			return list;
		}

		// Token: 0x06000418 RID: 1048 RVA: 0x00022820 File Offset: 0x00020A20
		private void method_39(List<GraphObj> list_2, ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_6)
		{
			double double_5 = Math.Min(double_1, double_3);
			double max = chartCS_1.GraphPane.XAxis.Scale.Max;
			double max2 = chartCS_1.GraphPane.YAxis.Scale.Max;
			double double_6 = Math.Min(double_2, double_4);
			double num = Math.Max(double_2, double_4);
			this.method_40(list_2, double_5, max, double_6, string_6);
			if ((!this.bool_6 || this.IfShowTopDashLine) && num < max2)
			{
				this.method_40(list_2, double_5, max, num, string_6);
			}
		}

		// Token: 0x06000419 RID: 1049 RVA: 0x000228A8 File Offset: 0x00020AA8
		private void method_40(List<GraphObj> list_2, double double_1, double double_2, double double_3, string string_6)
		{
			LineObj lineObj = base.method_23(double_1, double_3, double_2, double_3, string_6);
			lineObj.Line.Style = DashStyle.Dash;
			list_2.Add(lineObj);
		}

		// Token: 0x0600041A RID: 1050 RVA: 0x000228DC File Offset: 0x00020ADC
		protected virtual void vmethod_24(List<GraphObj> list_2, ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_6)
		{
			List<DrawSublineParam> sublineParamList = base.SublineParamList;
			for (int i = 0; i < sublineParamList.Count; i++)
			{
				DrawSublineParam drawSublineParam = sublineParamList[i];
				if (drawSublineParam.Enabled)
				{
					this.method_41(list_2, chartCS_1, double_1, double_2, double_3, double_4, drawSublineParam.Value, string_6);
				}
			}
		}

		// Token: 0x0600041B RID: 1051 RVA: 0x0002292C File Offset: 0x00020B2C
		protected override List<DrawSublineParam> vmethod_22()
		{
			List<double> list_ = new List<double>(new double[]
			{
				0.25,
				0.5,
				0.75
			});
			return base.method_28(list_, 0.01, 0.99, 2);
		}

		// Token: 0x0600041C RID: 1052 RVA: 0x00022970 File Offset: 0x00020B70
		protected void method_41(List<GraphObj> list_2, ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, double double_5, string string_6)
		{
			this.method_42(list_2, chartCS_1, double_1, double_2, double_3, double_4, double_5, string_6, false);
		}

		// Token: 0x0600041D RID: 1053 RVA: 0x00022994 File Offset: 0x00020B94
		protected void method_42(List<GraphObj> list_2, ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, double double_5, string string_6, bool bool_8)
		{
			double double_6 = Math.Min(double_1, double_3);
			double max = chartCS_1.GraphPane.YAxis.Scale.Max;
			double min = chartCS_1.GraphPane.YAxis.Scale.Min;
			double num = Math.Max(double_2, double_4);
			double num2 = Math.Min(double_2, double_4);
			double num3 = Math.Abs(double_4 - double_2) * double_5;
			double? num4 = null;
			if (double_4 > double_2)
			{
				num4 = new double?(num2 + num3);
			}
			else
			{
				num4 = new double?(num - num3);
			}
			if (num4 != null)
			{
				double? num5 = num4;
				double num6 = max;
				if (num5.GetValueOrDefault() < num6 & num5 != null)
				{
					num5 = num4;
					num6 = min;
					if (num5.GetValueOrDefault() > num6 & num5 != null)
					{
						this.method_43(list_2, chartCS_1, double_6, num4.Value, double_5, string_6, bool_8);
					}
				}
			}
		}

		// Token: 0x0600041E RID: 1054 RVA: 0x00022A7C File Offset: 0x00020C7C
		private void method_43(List<GraphObj> list_2, ChartCS chartCS_1, double double_1, double double_2, double double_3, string string_6, bool bool_8)
		{
			double max = chartCS_1.GraphPane.XAxis.Scale.Max;
			this.method_44(list_2, double_1, max, double_2, string_6, bool_8);
			double double_4 = double_1;
			if (double_1 < 0.0)
			{
				double_4 = 0.0;
			}
			TextObj textObj = base.method_27(chartCS_1, double_4, double_2, double_3.ToString("f3"), null, string_6);
			textObj.Location.AlignH = AlignH.Left;
			textObj.Location.AlignV = AlignV.Bottom;
			list_2.Add(textObj);
			int digitNb = base.Chart.Symbol.DigitNb;
			TextObj textObj2 = base.method_27(chartCS_1, max, double_2, Math.Round(double_2, digitNb).ToString("f" + digitNb.ToString()), null, string_6);
			textObj2.Location.AlignH = AlignH.Right;
			textObj2.Location.AlignV = AlignV.Bottom;
			list_2.Add(textObj2);
		}

		// Token: 0x0600041F RID: 1055 RVA: 0x00022B68 File Offset: 0x00020D68
		private void method_44(List<GraphObj> list_2, double double_1, double double_2, double double_3, string string_6, bool bool_8)
		{
			LineObj lineObj = base.method_23(double_1, double_3, double_2, double_3, string_6);
			if (bool_8)
			{
				lineObj.Line.Style = DashStyle.Dash;
			}
			list_2.Add(lineObj);
		}

		// Token: 0x06000420 RID: 1056 RVA: 0x00022BA0 File Offset: 0x00020DA0
		protected override void vmethod_14(ChartCS chartCS_1)
		{
			base.vmethod_14(chartCS_1);
			if (chartCS_1 != null && chartCS_1.GraphPane != null && chartCS_1.GraphPane.GraphObjList != null)
			{
				chartCS_1.GraphPane.GraphObjList.RemoveAll(new Predicate<GraphObj>(DrawRatio.<>c.<>9.method_0));
			}
		}

		// Token: 0x06000421 RID: 1057 RVA: 0x0001FB74 File Offset: 0x0001DD74
		protected override DrawLineStyle vmethod_23()
		{
			return null;
		}

		// Token: 0x170000DC RID: 220
		// (get) Token: 0x06000422 RID: 1058 RVA: 0x00022C00 File Offset: 0x00020E00
		// (set) Token: 0x06000423 RID: 1059 RVA: 0x00003C61 File Offset: 0x00001E61
		public bool IfShowTopDashLine
		{
			get
			{
				return this.bool_7;
			}
			set
			{
				this.bool_7 = value;
			}
		}

		// Token: 0x0400014E RID: 334
		private static readonly string string_5 = "_CnnLine";

		// Token: 0x0400014F RID: 335
		private bool bool_6;

		// Token: 0x04000150 RID: 336
		private bool bool_7;
	}
}
