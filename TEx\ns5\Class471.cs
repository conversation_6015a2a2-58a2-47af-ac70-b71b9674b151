﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using TEx.ImportTrans;

namespace ns5
{
	// Token: 0x0200036C RID: 876
	internal class Class471
	{
		// Token: 0x0600249A RID: 9370 RVA: 0x000F4EE0 File Offset: 0x000F30E0
		protected bool method_0(IStoreElement istoreElement_0, List<IStoreElement> list_0)
		{
			Class471.Class472 @class = new Class471.Class472();
			@class.istoreElement_0 = istoreElement_0;
			return list_0.Any(new Func<IStoreElement, bool>(@class.method_0));
		}

		// Token: 0x0600249B RID: 9371 RVA: 0x0000E39A File Offset: 0x0000C59A
		public void method_1(string string_1)
		{
			this.string_0 = string_1;
		}

		// Token: 0x0600249C RID: 9372 RVA: 0x000F4F10 File Offset: 0x000F3110
		protected virtual List<IStoreElement> vmethod_0()
		{
			return null;
		}

		// Token: 0x0600249D RID: 9373 RVA: 0x000F4F24 File Offset: 0x000F3124
		public List<IStoreElement> imethod_3()
		{
			return this.vmethod_0();
		}

		// Token: 0x040011AC RID: 4524
		protected string string_0;

		// Token: 0x0200036D RID: 877
		[CompilerGenerated]
		private sealed class Class472
		{
			// Token: 0x060024A0 RID: 9376 RVA: 0x000F4F3C File Offset: 0x000F313C
			internal bool method_0(IStoreElement istoreElement_1)
			{
				return istoreElement_1.ID == this.istoreElement_0.ID;
			}

			// Token: 0x040011AD RID: 4525
			public IStoreElement istoreElement_0;
		}
	}
}
