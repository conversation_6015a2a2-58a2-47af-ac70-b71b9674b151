﻿using System;
using TEx.Trading;

namespace ns17
{
	// Token: 0x02000272 RID: 626
	internal sealed class EventArgs15 : EventArgs
	{
		// Token: 0x06001B71 RID: 7025 RVA: 0x0000B4ED File Offset: 0x000096ED
		public EventArgs15(ShownCondOrder shownCondOrder_1, string string_1)
		{
			this.shownCondOrder_0 = shownCondOrder_1;
			this.string_0 = string_1;
		}

		// Token: 0x17000475 RID: 1141
		// (get) Token: 0x06001B72 RID: 7026 RVA: 0x000B9B0C File Offset: 0x000B7D0C
		public ShownCondOrder ShownCondOrder
		{
			get
			{
				return this.shownCondOrder_0;
			}
		}

		// Token: 0x17000476 RID: 1142
		// (get) Token: 0x06001B73 RID: 7027 RVA: 0x000B9B24 File Offset: 0x000B7D24
		public string ExcResultNotes
		{
			get
			{
				return this.string_0;
			}
		}

		// Token: 0x04000D90 RID: 3472
		private readonly ShownCondOrder shownCondOrder_0;

		// Token: 0x04000D91 RID: 3473
		private readonly string string_0;
	}
}
