﻿namespace TEx
{
	// Token: 0x0200027E RID: 638
	internal sealed partial class MainForm : global::DevComponents.DotNetBar.Metro.MetroForm
	{
		// Token: 0x06001CE4 RID: 7396 RVA: 0x000C34D4 File Offset: 0x000C16D4
		private void InitializeComponent()
		{
			this.icontainer_0 = new global::System.ComponentModel.Container();
			this.dotNetBarManager_0 = new global::DevComponents.DotNetBar.DotNetBarManager(this.icontainer_0);
			this.dockSite_Below = new global::DevComponents.DotNetBar.DockSite();
			this.bar_AcctTrans = new global::DevComponents.DotNetBar.Bar();
			this.panelDockContainer_AcctTrans = new global::DevComponents.DotNetBar.PanelDockContainer();
			this.dockContainer_AcctTrans = new global::DevComponents.DotNetBar.DockContainerItem();
			this.dockSite_Left = new global::DevComponents.DotNetBar.DockSite();
			this.dockSite_Right = new global::DevComponents.DotNetBar.DockSite();
			this.dockSite_Bottom = new global::DevComponents.DotNetBar.DockSite();
			this.statusBar = new global::DevComponents.DotNetBar.Bar();
			this.labelItem_页面 = new global::DevComponents.DotNetBar.LabelItem();
			this.toolStripDropDownButton_Pages = new global::DevComponents.DotNetBar.ButtonItem();
			this.labelItem_账户 = new global::DevComponents.DotNetBar.LabelItem();
			this.toolStripDropDownButton_Accts = new global::DevComponents.DotNetBar.ButtonItem();
			this.labelItem_品种 = new global::DevComponents.DotNetBar.LabelItem();
			this.toolStripDropDownButton_Symbls = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripStatusLabel_AutoStopStatus = new global::DevComponents.DotNetBar.LabelItem();
			this.toolStripStatusLabel_AutoLimitStatus = new global::DevComponents.DotNetBar.LabelItem();
			this.toolStripStatusLabel_BlindTest = new global::DevComponents.DotNetBar.LabelItem();
			this.labelItem_平仓盈亏 = new global::DevComponents.DotNetBar.LabelItem();
			this.toolStripStatusLabel_Profits = new global::DevComponents.DotNetBar.LabelItem();
			this.labelItem_浮动盈亏 = new global::DevComponents.DotNetBar.LabelItem();
			this.toolStripStatusLabel_CurrProfits = new global::DevComponents.DotNetBar.LabelItem();
			this.toolStripStatusLabel_Info = new global::DevComponents.DotNetBar.LabelItem();
			this.dockSite_ToolbarLeft = new global::DevComponents.DotNetBar.DockSite();
			this.dockSite_ToolbarRight = new global::DevComponents.DotNetBar.DockSite();
			this.dockSite_ToolBar = new global::DevComponents.DotNetBar.DockSite();
			this.toolStrip_Trading = new global::DevComponents.DotNetBar.Bar();
			this.numericUpDown_Units = new global::ns32.Class302();
			this.numericUpDown_Price = new global::ns32.Class302();
			this.toolStripBttn_Long = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripBttn_ClsLong = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripBttn_Short = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripBttn_ClsShort = new global::DevComponents.DotNetBar.ButtonItem();
			this.labelItem_Lots = new global::DevComponents.DotNetBar.LabelItem();
			this.controlContainerItem_Units = new global::DevComponents.DotNetBar.ControlContainerItem();
			this.labelItem_Price = new global::DevComponents.DotNetBar.LabelItem();
			this.controlContainerItem_Price = new global::DevComponents.DotNetBar.ControlContainerItem();
			this.toolStrip_Ctrls = new global::DevComponents.DotNetBar.Bar();
			this.toolStripBtnItem_Start = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripBtnItem_SpanUnit_1m = new global::DevComponents.DotNetBar.ButtonItem();
			this.labelItem_Speed = new global::DevComponents.DotNetBar.LabelItem();
			this.slider_Speed = new global::DevComponents.DotNetBar.SliderItem();
			this.toolStrip_ExitCreateNewPg = new global::DevComponents.DotNetBar.Bar();
			this.toolStripButton_ExitCrtNewPg = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStrip_Setting = new global::DevComponents.DotNetBar.Bar();
			this.toolStripBtnItem_SelectDate = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripDropDownButton_Settings = new global::DevComponents.DotNetBar.ButtonItem();
			this.工具栏ToolStripMenuItem = new global::DevComponents.DotNetBar.ButtonItem();
			this.功能栏ToolStripMenuItem = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_市场板块 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_财报分析 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_条件选股 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_双盲测试 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_画线下单 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_交易账户 = new global::DevComponents.DotNetBar.ButtonItem();
			this.新建账户ToolStripMenuItem = new global::DevComponents.DotNetBar.ButtonItem();
			this.切换账户ToolStripMenuItem = new global::DevComponents.DotNetBar.ButtonItem();
			this.删除账户ToolStripMenuItem = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_品种设置 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_交易分析 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_交易分析_交易统计 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_交易分析_权益走势 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_交易分析_盈利曲线 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_交易分析_品种盈亏 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_交易分析_多空盈亏 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_交易分析_时间盈亏 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_视频讲座 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_画线工具 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_指标管理 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_系统参数 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_数据管理 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_热键定义 = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_页面设置 = new global::DevComponents.DotNetBar.ButtonItem();
			this.选择页面ToolStripMenuItem = new global::DevComponents.DotNetBar.ButtonItem();
			this.新建页面ToolStripMenuItem = new global::DevComponents.DotNetBar.ButtonItem();
			this.保存页面ToolStripMenuItem = new global::DevComponents.DotNetBar.ButtonItem();
			this.另存页面ToolStripMenuItem = new global::DevComponents.DotNetBar.ButtonItem();
			this.删除页面ToolStripMenuItem = new global::DevComponents.DotNetBar.ButtonItem();
			this.自动保存ToolStripMenuItem = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_界面风格 = new global::DevComponents.DotNetBar.ButtonItem();
			this.黑红经典ToolStripMenuItem = new global::DevComponents.DotNetBar.ButtonItem();
			this.绿白经典ToolStripMenuItem = new global::DevComponents.DotNetBar.ButtonItem();
			this.绿白现代ToolStripMenuItem = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_Help = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_VideoHelp = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_TExWeb = new global::DevComponents.DotNetBar.ButtonItem();
			this.btnItem_AppUpg = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripMenuItem_BuySoft = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripMenuItem_VerInfo = new global::DevComponents.DotNetBar.ButtonItem();
			this.关于ToolStripMenuItem = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStrip_Periods = new global::DevComponents.DotNetBar.Bar();
			this.toolStripBtn_NPeriod = new global::DevComponents.DotNetBar.ButtonItem();
			this.itemContainer_NMins = new global::DevComponents.DotNetBar.ItemContainer();
			this.txtBoxItem_NMins = new global::DevComponents.DotNetBar.TextBoxItem();
			this.toolStripBtnItem_NMins = new global::DevComponents.DotNetBar.ButtonItem();
			this.itemContainer_NHours = new global::DevComponents.DotNetBar.ItemContainer();
			this.txtBoxItem_NHours = new global::DevComponents.DotNetBar.TextBoxItem();
			this.toolStripBtnItem_NHours = new global::DevComponents.DotNetBar.ButtonItem();
			this.itemContainer_NDays = new global::DevComponents.DotNetBar.ItemContainer();
			this.txtBoxItem_NDays = new global::DevComponents.DotNetBar.TextBoxItem();
			this.toolStripBtnItem_NDays = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripBtn_Tick = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripBttn_1m = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripBttn_3m = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripBttn_5m = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripBttn_10m = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripBttn_15m = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripBttn_30m = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripBttn_1h = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripBttn_2h = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripBttn_4h = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripBttn_1d = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripBttn_1w = new global::DevComponents.DotNetBar.ButtonItem();
			this.toolStripBttn_1Mth = new global::DevComponents.DotNetBar.ButtonItem();
			this.dockSite_Top = new global::DevComponents.DotNetBar.DockSite();
			this.panel_Below = new global::System.Windows.Forms.Panel();
			this.toolTip_0 = new global::System.Windows.Forms.ToolTip(this.icontainer_0);
			this.dockSite_Below.SuspendLayout();
			((global::System.ComponentModel.ISupportInitialize)this.bar_AcctTrans).BeginInit();
			this.bar_AcctTrans.SuspendLayout();
			this.dockSite_Bottom.SuspendLayout();
			((global::System.ComponentModel.ISupportInitialize)this.statusBar).BeginInit();
			this.dockSite_ToolBar.SuspendLayout();
			((global::System.ComponentModel.ISupportInitialize)this.toolStrip_Trading).BeginInit();
			this.toolStrip_Trading.SuspendLayout();
			((global::System.ComponentModel.ISupportInitialize)this.numericUpDown_Units).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.numericUpDown_Price).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.toolStrip_Ctrls).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.toolStrip_ExitCreateNewPg).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.toolStrip_Setting).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.toolStrip_Periods).BeginInit();
			base.SuspendLayout();
			this.dotNetBarManager_0.AllowUserBarCustomize = false;
			this.dotNetBarManager_0.AutoDispatchShortcuts.Add(global::DevComponents.DotNetBar.eShortcut.F1);
			this.dotNetBarManager_0.AutoDispatchShortcuts.Add(global::DevComponents.DotNetBar.eShortcut.CtrlC);
			this.dotNetBarManager_0.AutoDispatchShortcuts.Add(global::DevComponents.DotNetBar.eShortcut.CtrlA);
			this.dotNetBarManager_0.AutoDispatchShortcuts.Add(global::DevComponents.DotNetBar.eShortcut.CtrlV);
			this.dotNetBarManager_0.AutoDispatchShortcuts.Add(global::DevComponents.DotNetBar.eShortcut.CtrlX);
			this.dotNetBarManager_0.AutoDispatchShortcuts.Add(global::DevComponents.DotNetBar.eShortcut.CtrlZ);
			this.dotNetBarManager_0.AutoDispatchShortcuts.Add(global::DevComponents.DotNetBar.eShortcut.CtrlY);
			this.dotNetBarManager_0.AutoDispatchShortcuts.Add(global::DevComponents.DotNetBar.eShortcut.Del);
			this.dotNetBarManager_0.AutoDispatchShortcuts.Add(global::DevComponents.DotNetBar.eShortcut.Ins);
			this.dotNetBarManager_0.BottomDockSite = this.dockSite_Below;
			this.dotNetBarManager_0.EnableFullSizeDock = false;
			this.dotNetBarManager_0.LeftDockSite = this.dockSite_Left;
			this.dotNetBarManager_0.ParentForm = this;
			this.dotNetBarManager_0.RightDockSite = this.dockSite_Right;
			this.dotNetBarManager_0.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.Metro;
			this.dotNetBarManager_0.ToolbarBottomDockSite = this.dockSite_Bottom;
			this.dotNetBarManager_0.ToolbarLeftDockSite = this.dockSite_ToolbarLeft;
			this.dotNetBarManager_0.ToolbarRightDockSite = this.dockSite_ToolbarRight;
			this.dotNetBarManager_0.ToolbarTopDockSite = this.dockSite_ToolBar;
			this.dotNetBarManager_0.TopDockSite = this.dockSite_Top;
			this.dockSite_Below.AccessibleRole = global::System.Windows.Forms.AccessibleRole.Window;
			this.dockSite_Below.Controls.Add(this.bar_AcctTrans);
			this.dockSite_Below.Dock = global::System.Windows.Forms.DockStyle.Bottom;
			this.dockSite_Below.DocumentDockContainer = new global::DevComponents.DotNetBar.DocumentDockContainer(new global::DevComponents.DotNetBar.DocumentBaseContainer[]
			{
				new global::DevComponents.DotNetBar.DocumentBarContainer(this.bar_AcctTrans, 1274, 275)
			}, global::DevComponents.DotNetBar.eOrientation.Vertical);
			this.dockSite_Below.Location = new global::System.Drawing.Point(0, 425);
			this.dockSite_Below.Margin = new global::System.Windows.Forms.Padding(0);
			this.dockSite_Below.Name = "dockSite_Below";
			this.dockSite_Below.Size = new global::System.Drawing.Size(1274, 278);
			this.dockSite_Below.TabIndex = 6;
			this.dockSite_Below.TabStop = false;
			this.bar_AcctTrans.AccessibleDescription = "DotNetBar Bar (bar_AcctTrans)";
			this.bar_AcctTrans.AccessibleName = "DotNetBar Bar";
			this.bar_AcctTrans.AccessibleRole = global::System.Windows.Forms.AccessibleRole.ToolBar;
			this.bar_AcctTrans.AntiAlias = true;
			this.bar_AcctTrans.AutoSyncBarCaption = true;
			this.bar_AcctTrans.BackColor = global::System.Drawing.SystemColors.Control;
			this.bar_AcctTrans.CanCustomize = false;
			this.bar_AcctTrans.CanDockDocument = true;
			this.bar_AcctTrans.CanDockLeft = false;
			this.bar_AcctTrans.CanDockRight = false;
			this.bar_AcctTrans.CanDockTab = false;
			this.bar_AcctTrans.CanHide = true;
			this.bar_AcctTrans.CanReorderTabs = false;
			this.bar_AcctTrans.CloseSingleTab = true;
			this.bar_AcctTrans.ColorScheme.BarCaptionBackground = global::System.Drawing.Color.FromArgb(251, 250, 247);
			this.bar_AcctTrans.ColorScheme.BarCaptionText = global::System.Drawing.Color.FromArgb(0, 0, 0);
			this.bar_AcctTrans.ColorScheme.ItemHotBorder = global::System.Drawing.Color.FromArgb(0, 255, 255, 255);
			this.bar_AcctTrans.Controls.Add(this.panelDockContainer_AcctTrans);
			this.bar_AcctTrans.DisabledImagesGrayScale = false;
			this.bar_AcctTrans.DisplayMoreItemsOnMenu = true;
			this.bar_AcctTrans.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 8.4f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 134);
			this.bar_AcctTrans.GrabHandleStyle = global::DevComponents.DotNetBar.eGrabHandleStyle.Caption;
			this.bar_AcctTrans.Items.AddRange(new global::DevComponents.DotNetBar.BaseItem[]
			{
				this.dockContainer_AcctTrans
			});
			this.bar_AcctTrans.LayoutType = global::DevComponents.DotNetBar.eLayoutType.DockContainer;
			this.bar_AcctTrans.Location = new global::System.Drawing.Point(0, 3);
			this.bar_AcctTrans.Margin = new global::System.Windows.Forms.Padding(0);
			this.bar_AcctTrans.Name = "bar_AcctTrans";
			this.bar_AcctTrans.Size = new global::System.Drawing.Size(1274, 275);
			this.bar_AcctTrans.Stretch = true;
			this.bar_AcctTrans.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.Metro;
			this.bar_AcctTrans.TabIndex = 0;
			this.bar_AcctTrans.TabStop = false;
			this.bar_AcctTrans.Text = "功能栏";
			this.bar_AcctTrans.BarDock += new global::System.EventHandler(this.bar_AcctTrans_BarDock);
			this.bar_AcctTrans.Closing += new global::DevComponents.DotNetBar.DotNetBarManager.BarClosingEventHandler(this.bar_AcctTrans_Closing);
			this.bar_AcctTrans.AutoHideDisplay += new global::DevComponents.DotNetBar.DotNetBarManager.AutoHideDisplayEventHandler(this.bar_AcctTrans_AutoHideDisplay);
			this.bar_AcctTrans.SizeChanged += new global::System.EventHandler(this.bar_AcctTrans_SizeChanged);
			this.panelDockContainer_AcctTrans.ColorSchemeStyle = global::DevComponents.DotNetBar.eDotNetBarStyle.Metro;
			this.panelDockContainer_AcctTrans.Location = new global::System.Drawing.Point(3, 23);
			this.panelDockContainer_AcctTrans.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.panelDockContainer_AcctTrans.Name = "panelDockContainer_AcctTrans";
			this.panelDockContainer_AcctTrans.Size = new global::System.Drawing.Size(1268, 249);
			this.panelDockContainer_AcctTrans.Style.Alignment = global::System.Drawing.StringAlignment.Center;
			this.panelDockContainer_AcctTrans.Style.BackColor1.ColorSchemePart = global::DevComponents.DotNetBar.eColorSchemePart.BarBackground;
			this.panelDockContainer_AcctTrans.Style.ForeColor.ColorSchemePart = global::DevComponents.DotNetBar.eColorSchemePart.ItemText;
			this.panelDockContainer_AcctTrans.Style.GradientAngle = 90;
			this.panelDockContainer_AcctTrans.TabIndex = 0;
			this.dockContainer_AcctTrans.Control = this.panelDockContainer_AcctTrans;
			this.dockContainer_AcctTrans.DefaultFloatingSize = new global::System.Drawing.Size(550, 200);
			this.dockContainer_AcctTrans.Name = "dockContainer_AcctTrans";
			this.dockContainer_AcctTrans.Text = "功能栏";
			this.dockSite_Left.AccessibleRole = global::System.Windows.Forms.AccessibleRole.Window;
			this.dockSite_Left.Dock = global::System.Windows.Forms.DockStyle.Left;
			this.dockSite_Left.DocumentDockContainer = new global::DevComponents.DotNetBar.DocumentDockContainer();
			this.dockSite_Left.Location = new global::System.Drawing.Point(0, 31);
			this.dockSite_Left.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.dockSite_Left.Name = "dockSite_Left";
			this.dockSite_Left.Size = new global::System.Drawing.Size(0, 672);
			this.dockSite_Left.TabIndex = 3;
			this.dockSite_Left.TabStop = false;
			this.dockSite_Right.AccessibleRole = global::System.Windows.Forms.AccessibleRole.Window;
			this.dockSite_Right.Dock = global::System.Windows.Forms.DockStyle.Right;
			this.dockSite_Right.DocumentDockContainer = new global::DevComponents.DotNetBar.DocumentDockContainer();
			this.dockSite_Right.Location = new global::System.Drawing.Point(1274, 31);
			this.dockSite_Right.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.dockSite_Right.Name = "dockSite_Right";
			this.dockSite_Right.Size = new global::System.Drawing.Size(0, 672);
			this.dockSite_Right.TabIndex = 4;
			this.dockSite_Right.TabStop = false;
			this.dockSite_Bottom.AccessibleRole = global::System.Windows.Forms.AccessibleRole.Window;
			this.dockSite_Bottom.Controls.Add(this.statusBar);
			this.dockSite_Bottom.Dock = global::System.Windows.Forms.DockStyle.Bottom;
			this.dockSite_Bottom.Location = new global::System.Drawing.Point(0, 703);
			this.dockSite_Bottom.Margin = new global::System.Windows.Forms.Padding(0);
			this.dockSite_Bottom.Name = "dockSite_Bottom";
			this.dockSite_Bottom.Size = new global::System.Drawing.Size(1274, 32);
			this.dockSite_Bottom.TabIndex = 10;
			this.dockSite_Bottom.TabStop = false;
			this.statusBar.AccessibleDescription = "DotNetBar Bar (statusBar)";
			this.statusBar.AccessibleName = "DotNetBar Bar";
			this.statusBar.AccessibleRole = global::System.Windows.Forms.AccessibleRole.StatusBar;
			this.statusBar.AntiAlias = true;
			this.statusBar.BackColor = global::System.Drawing.SystemColors.Control;
			this.statusBar.BarType = global::DevComponents.DotNetBar.eBarType.StatusBar;
			this.statusBar.CanCustomize = false;
			this.statusBar.CanDockLeft = false;
			this.statusBar.CanDockRight = false;
			this.statusBar.CanDockTab = false;
			this.statusBar.CanDockTop = false;
			this.statusBar.CanReorderTabs = false;
			this.statusBar.CanUndock = false;
			this.statusBar.Dock = global::System.Windows.Forms.DockStyle.Bottom;
			this.statusBar.DockSide = global::DevComponents.DotNetBar.eDockSide.Bottom;
			this.statusBar.Font = new global::System.Drawing.Font("Microsoft YaHei", 9f);
			this.statusBar.GrabHandleStyle = global::DevComponents.DotNetBar.eGrabHandleStyle.ResizeHandle;
			this.statusBar.Items.AddRange(new global::DevComponents.DotNetBar.BaseItem[]
			{
				this.labelItem_页面,
				this.toolStripDropDownButton_Pages,
				this.labelItem_账户,
				this.toolStripDropDownButton_Accts,
				this.labelItem_品种,
				this.toolStripDropDownButton_Symbls,
				this.toolStripStatusLabel_BlindTest,
				this.toolStripStatusLabel_AutoStopStatus,
				this.toolStripStatusLabel_AutoLimitStatus,
				this.labelItem_平仓盈亏,
				this.toolStripStatusLabel_Profits,
				this.labelItem_浮动盈亏,
				this.toolStripStatusLabel_CurrProfits,
				this.toolStripStatusLabel_Info
			});
			this.statusBar.Location = new global::System.Drawing.Point(0, 1);
			this.statusBar.Margin = new global::System.Windows.Forms.Padding(3, 0, 3, 2);
			this.statusBar.Name = "statusBar";
			this.statusBar.PaddingTop = 0;
			this.statusBar.Size = new global::System.Drawing.Size(1274, 31);
			this.statusBar.Stretch = true;
			this.statusBar.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.Metro;
			this.statusBar.TabIndex = 12;
			this.statusBar.TabStop = false;
			this.labelItem_页面.Name = "labelItem_页面";
			this.labelItem_页面.Text = "页面:";
			this.toolStripDropDownButton_Pages.AutoExpandOnClick = true;
			this.toolStripDropDownButton_Pages.FixedSize = new global::System.Drawing.Size(0, 20);
			this.toolStripDropDownButton_Pages.Name = "toolStripDropDownButton_Pages";
			this.toolStripDropDownButton_Pages.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripDropDownButton_Pages.Text = "页面选择";
			this.labelItem_账户.BeginGroup = false;
			this.labelItem_账户.Height = 20;
			this.labelItem_账户.Name = "labelItem_账户";
			this.labelItem_账户.Text = "账户:";
			this.toolStripDropDownButton_Accts.AutoExpandOnClick = true;
			this.toolStripDropDownButton_Accts.Name = "toolStripDropDownButton_Accts";
			this.toolStripDropDownButton_Accts.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripDropDownButton_Accts.Text = "默认账户";
			this.labelItem_品种.BeginGroup = false;
			this.labelItem_品种.Name = "labelItem_品种";
			this.labelItem_品种.Text = "品种:";
			this.toolStripDropDownButton_Symbls.AutoExpandOnClick = true;
			this.toolStripDropDownButton_Symbls.Name = "toolStripDropDownButton_Symbls";
			this.toolStripDropDownButton_Symbls.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripDropDownButton_Symbls.Text = "IF";
			this.toolStripStatusLabel_AutoStopStatus.BeginGroup = false;
			this.toolStripStatusLabel_AutoStopStatus.Height = 20;
			this.toolStripStatusLabel_AutoStopStatus.Image = global::ns28.Class372.autostop;
			this.toolStripStatusLabel_AutoStopStatus.Name = "toolStripStatusLabel_AutoStopStatus";
			this.toolStripStatusLabel_AutoStopStatus.Tooltip = "自动止损";
			this.toolStripStatusLabel_AutoStopStatus.Click += new global::System.EventHandler(this.toolStripStatusLabel_AutoStopStatus_Click);
			this.toolStripStatusLabel_AutoLimitStatus.Height = 20;
			this.toolStripStatusLabel_AutoLimitStatus.Image = global::ns28.Class372.autolimit;
			this.toolStripStatusLabel_AutoLimitStatus.Name = "toolStripStatusLabel_AutoLimitStatus";
			this.toolStripStatusLabel_AutoLimitStatus.Tooltip = "自动止盈";
			this.toolStripStatusLabel_AutoLimitStatus.Click += new global::System.EventHandler(this.toolStripStatusLabel_AutoLimitStatus_Click);
			this.toolStripStatusLabel_BlindTest.BeginGroup = false;
			this.toolStripStatusLabel_BlindTest.Image = global::ns28.Class372.GlassFace_18;
			this.toolStripStatusLabel_BlindTest.ItemAlignment = global::DevComponents.DotNetBar.eItemAlignment.Center;
			this.toolStripStatusLabel_BlindTest.Text = "双盲模式：关";
			this.toolStripStatusLabel_BlindTest.Name = "toolStripStatusLabel_BlindTest";
			this.toolStripStatusLabel_BlindTest.Tooltip = "双盲测试";
			this.toolStripStatusLabel_BlindTest.Click += new global::System.EventHandler(this.toolStripStatusLabel_BlindTest_Click);
			this.labelItem_平仓盈亏.BeginGroup = false;
			this.labelItem_平仓盈亏.Height = 20;
			this.labelItem_平仓盈亏.Name = "labelItem_平仓盈亏";
			this.labelItem_平仓盈亏.Text = "平仓盈亏:";
			this.toolStripStatusLabel_Profits.Height = 20;
			this.toolStripStatusLabel_Profits.Name = "toolStripStatusLabel_Profits";
			this.toolStripStatusLabel_Profits.Text = "0";
			this.labelItem_浮动盈亏.BeginGroup = false;
			this.labelItem_浮动盈亏.Height = 20;
			this.labelItem_浮动盈亏.Name = "labelItem_浮动盈亏";
			this.labelItem_浮动盈亏.Text = "浮动盈亏:";
			this.toolStripStatusLabel_CurrProfits.Height = 20;
			this.toolStripStatusLabel_CurrProfits.Name = "toolStripStatusLabel_CurrProfits";
			this.toolStripStatusLabel_CurrProfits.Text = "0";
			this.toolStripStatusLabel_Info.Height = 20;
			this.toolStripStatusLabel_Info.ItemAlignment = global::DevComponents.DotNetBar.eItemAlignment.Far;
			this.toolStripStatusLabel_Info.Name = "toolStripStatusLabel_Info";
			this.toolStripStatusLabel_Info.Text = "提示：";
			this.dockSite_ToolbarLeft.AccessibleRole = global::System.Windows.Forms.AccessibleRole.Window;
			this.dockSite_ToolbarLeft.Dock = global::System.Windows.Forms.DockStyle.Left;
			this.dockSite_ToolbarLeft.Location = new global::System.Drawing.Point(0, 31);
			this.dockSite_ToolbarLeft.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.dockSite_ToolbarLeft.Name = "dockSite_ToolbarLeft";
			this.dockSite_ToolbarLeft.Size = new global::System.Drawing.Size(0, 672);
			this.dockSite_ToolbarLeft.TabIndex = 7;
			this.dockSite_ToolbarLeft.TabStop = false;
			this.dockSite_ToolbarRight.AccessibleRole = global::System.Windows.Forms.AccessibleRole.Window;
			this.dockSite_ToolbarRight.Dock = global::System.Windows.Forms.DockStyle.Right;
			this.dockSite_ToolbarRight.Location = new global::System.Drawing.Point(1274, 31);
			this.dockSite_ToolbarRight.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.dockSite_ToolbarRight.Name = "dockSite_ToolbarRight";
			this.dockSite_ToolbarRight.Size = new global::System.Drawing.Size(0, 672);
			this.dockSite_ToolbarRight.TabIndex = 8;
			this.dockSite_ToolbarRight.TabStop = false;
			this.dockSite_ToolBar.AccessibleRole = global::System.Windows.Forms.AccessibleRole.Window;
			this.dockSite_ToolBar.BackColor = global::System.Drawing.SystemColors.Control;
			this.dockSite_ToolBar.CausesValidation = false;
			this.dockSite_ToolBar.Controls.Add(this.toolStrip_Trading);
			this.dockSite_ToolBar.Controls.Add(this.toolStrip_Ctrls);
			this.dockSite_ToolBar.Controls.Add(this.toolStrip_ExitCreateNewPg);
			this.dockSite_ToolBar.Controls.Add(this.toolStrip_Setting);
			this.dockSite_ToolBar.Controls.Add(this.toolStrip_Periods);
			this.dockSite_ToolBar.Dock = global::System.Windows.Forms.DockStyle.Top;
			this.dockSite_ToolBar.Location = new global::System.Drawing.Point(0, 0);
			this.dockSite_ToolBar.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.dockSite_ToolBar.Name = "dockSite_ToolBar";
			this.dockSite_ToolBar.OptimizeLayoutRedraw = false;
			this.dockSite_ToolBar.Size = new global::System.Drawing.Size(1274, 31);
			this.dockSite_ToolBar.TabIndex = 9;
			this.dockSite_ToolBar.TabStop = false;
			this.toolStrip_Trading.AccessibleDescription = "DotNetBar Bar (toolStrip_Trading)";
			this.toolStrip_Trading.AccessibleName = "DotNetBar Bar";
			this.toolStrip_Trading.AccessibleRole = global::System.Windows.Forms.AccessibleRole.ToolBar;
			this.toolStrip_Trading.AntiAlias = true;
			this.toolStrip_Trading.BackColor = global::System.Drawing.SystemColors.Control;
			this.toolStrip_Trading.CanAutoHide = false;
			this.toolStrip_Trading.CanHide = true;
			this.toolStrip_Trading.Controls.Add(this.numericUpDown_Units);
			this.toolStrip_Trading.Controls.Add(this.numericUpDown_Price);
			this.toolStrip_Trading.DockSide = global::DevComponents.DotNetBar.eDockSide.Top;
			this.toolStrip_Trading.Font = new global::System.Drawing.Font("Microsoft YaHei", 9f);
			this.toolStrip_Trading.GrabHandleStyle = global::DevComponents.DotNetBar.eGrabHandleStyle.None;
			this.toolStrip_Trading.Items.AddRange(new global::DevComponents.DotNetBar.BaseItem[]
			{
				this.toolStripBttn_Long,
				this.toolStripBttn_ClsLong,
				this.toolStripBttn_Short,
				this.toolStripBttn_ClsShort,
				this.labelItem_Lots,
				this.controlContainerItem_Units,
				this.labelItem_Price,
				this.controlContainerItem_Price
			});
			this.toolStrip_Trading.Location = new global::System.Drawing.Point(0, 0);
			this.toolStrip_Trading.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.toolStrip_Trading.Name = "toolStrip_Trading";
			this.toolStrip_Trading.RoundCorners = false;
			this.toolStrip_Trading.Size = new global::System.Drawing.Size(470, 31);
			this.toolStrip_Trading.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.Metro;
			this.toolStrip_Trading.TabIndex = 8;
			this.toolStrip_Trading.TabStop = false;
			this.toolStrip_Trading.Text = "交易栏";
			this.numericUpDown_Units.Font = new global::System.Drawing.Font("Microsoft YaHei", 8f);
			this.numericUpDown_Units.Location = new global::System.Drawing.Point(238, 3);
			this.numericUpDown_Units.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			global::System.Windows.Forms.NumericUpDown numericUpDown = this.numericUpDown_Units;
			int[] array = new int[4];
			array[0] = 9999999;
			numericUpDown.Maximum = new decimal(array);
			global::System.Windows.Forms.NumericUpDown numericUpDown2 = this.numericUpDown_Units;
			int[] array2 = new int[4];
			array2[0] = 1;
			numericUpDown2.Minimum = new decimal(array2);
			this.numericUpDown_Units.Name = "numericUpDown_Units";
			this.numericUpDown_Units.Size = new global::System.Drawing.Size(75, 25);
			this.numericUpDown_Units.TabIndex = 8;
			global::System.Windows.Forms.NumericUpDown numericUpDown3 = this.numericUpDown_Units;
			int[] array3 = new int[4];
			array3[0] = 1;
			numericUpDown3.Value = new decimal(array3);
			this.numericUpDown_Units.ValueChanged += new global::System.EventHandler(this.numericUpDown_Units_ValueChanged);
			this.numericUpDown_Price.Font = new global::System.Drawing.Font("Microsoft YaHei", 8f);
			this.numericUpDown_Price.Location = new global::System.Drawing.Point(376, 3);
			this.numericUpDown_Price.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			global::System.Windows.Forms.NumericUpDown numericUpDown4 = this.numericUpDown_Price;
			int[] array4 = new int[4];
			array4[0] = 9999999;
			numericUpDown4.Maximum = new decimal(array4);
			this.numericUpDown_Price.Name = "numericUpDown_Price";
			this.numericUpDown_Price.Size = new global::System.Drawing.Size(90, 25);
			this.numericUpDown_Price.TabIndex = 1;
			this.toolStripBttn_Long.FontBold = true;
			this.toolStripBttn_Long.ForeColor = global::System.Drawing.Color.Red;
			this.toolStripBttn_Long.Name = "toolStripBttn_Long";
			this.toolStripBttn_Long.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBttn_Long.Text = "买开";
			this.toolStripBttn_ClsLong.FontBold = true;
			this.toolStripBttn_ClsLong.ForeColor = global::System.Drawing.Color.FromArgb(0, 192, 0);
			this.toolStripBttn_ClsLong.Name = "toolStripBttn_ClsLong";
			this.toolStripBttn_ClsLong.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBttn_ClsLong.Text = "卖平";
			this.toolStripBttn_Short.BeginGroup = false;
			this.toolStripBttn_Short.FontBold = true;
			this.toolStripBttn_Short.ForeColor = global::System.Drawing.Color.FromArgb(0, 192, 0);
			this.toolStripBttn_Short.Name = "toolStripBttn_Short";
			this.toolStripBttn_Short.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBttn_Short.Text = "卖开";
			this.toolStripBttn_ClsShort.FontBold = true;
			this.toolStripBttn_ClsShort.ForeColor = global::System.Drawing.Color.Red;
			this.toolStripBttn_ClsShort.Name = "toolStripBttn_ClsShort";
			this.toolStripBttn_ClsShort.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBttn_ClsShort.Text = "买平";
			this.labelItem_Lots.BeginGroup = false;
			this.labelItem_Lots.Height = 18;
			this.labelItem_Lots.Name = "labelItem_Lots";
			this.labelItem_Lots.Text = "数量：";
			this.controlContainerItem_Units.AllowItemResize = false;
			this.controlContainerItem_Units.Control = this.numericUpDown_Units;
			this.controlContainerItem_Units.MenuVisibility = global::DevComponents.DotNetBar.eMenuVisibility.VisibleAlways;
			this.controlContainerItem_Units.Name = "controlContainerItem_Units";
			this.controlContainerItem_Units.Text = " ";
			this.labelItem_Price.Height = 18;
			this.labelItem_Price.Name = "labelItem_Price";
			this.labelItem_Price.Text = "价格：";
			this.controlContainerItem_Price.AllowItemResize = false;
			this.controlContainerItem_Price.Control = this.numericUpDown_Price;
			this.controlContainerItem_Price.MenuVisibility = global::DevComponents.DotNetBar.eMenuVisibility.VisibleAlways;
			this.controlContainerItem_Price.Name = "controlContainerItem_Price";
			this.controlContainerItem_Price.Text = " ";
			this.toolStrip_Ctrls.AccessibleDescription = "DotNetBar Bar (toolStrip_Ctrls)";
			this.toolStrip_Ctrls.AccessibleName = "DotNetBar Bar";
			this.toolStrip_Ctrls.AccessibleRole = global::System.Windows.Forms.AccessibleRole.ToolBar;
			this.toolStrip_Ctrls.AntiAlias = true;
			this.toolStrip_Ctrls.BackColor = global::System.Drawing.SystemColors.Control;
			this.toolStrip_Ctrls.CanAutoHide = false;
			this.toolStrip_Ctrls.CanDockTab = false;
			this.toolStrip_Ctrls.DockOffset = 379;
			this.toolStrip_Ctrls.DockSide = global::DevComponents.DotNetBar.eDockSide.Top;
			this.toolStrip_Ctrls.Font = new global::System.Drawing.Font("Microsoft YaHei", 9f);
			this.toolStrip_Ctrls.GrabHandleStyle = global::DevComponents.DotNetBar.eGrabHandleStyle.None;
			this.toolStrip_Ctrls.Items.AddRange(new global::DevComponents.DotNetBar.BaseItem[]
			{
				this.toolStripBtnItem_Start,
				this.labelItem_Speed,
				this.slider_Speed
			});
			this.toolStrip_Ctrls.Location = new global::System.Drawing.Point(472, 0);
			this.toolStrip_Ctrls.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.toolStrip_Ctrls.Name = "toolStrip_Ctrls";
			this.toolStrip_Ctrls.Size = new global::System.Drawing.Size(251, 31);
			this.toolStrip_Ctrls.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.Metro;
			this.toolStrip_Ctrls.TabIndex = 9;
			this.toolStrip_Ctrls.TabStop = false;
			this.toolStrip_Ctrls.Text = "行情控制栏";
			this.toolStrip_Ctrls.BarDock += new global::System.EventHandler(this.toolStrip_Ctrls_BarDock);
			this.toolStrip_Ctrls.BarUndock += new global::System.EventHandler(this.toolStrip_Ctrls_BarUndock);
			this.toolStripBtnItem_Start.ButtonStyle = global::DevComponents.DotNetBar.eButtonStyle.ImageAndText;
			this.toolStripBtnItem_Start.FixedSize = new global::System.Drawing.Size(0, 18);
			this.toolStripBtnItem_Start.Image = global::ns28.Class372.play;
			this.toolStripBtnItem_Start.ImageFixedSize = new global::System.Drawing.Size(18, 18);
			this.toolStripBtnItem_Start.Name = "toolStripBtnItem_Start";
			this.toolStripBtnItem_Start.SubItems.AddRange(new global::DevComponents.DotNetBar.BaseItem[]
			{
				this.toolStripBtnItem_SpanUnit_1m
			});
			this.toolStripBtnItem_Start.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBtnItem_Start.Text = "开始";
			this.toolStripBtnItem_SpanUnit_1m.Name = "toolStripBtnItem_SpanUnit_1m";
			this.toolStripBtnItem_SpanUnit_1m.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBtnItem_SpanUnit_1m.Text = "步进单位：1分钟";
			this.labelItem_Speed.BeginGroup = false;
			this.labelItem_Speed.Name = "labelItem_Speed";
			this.labelItem_Speed.Text = " 速度:";
			this.slider_Speed.LabelWidth = 42;
			this.slider_Speed.Minimum = 1;
			this.slider_Speed.Name = "slider_Speed";
			this.slider_Speed.Text = "100";
			this.slider_Speed.Value = 100;
			this.slider_Speed.Width = 86;
			this.slider_Speed.ValueChanged += new global::System.EventHandler(this.slider_Speed_ValueChanged);
			this.toolStrip_ExitCreateNewPg.AccessibleDescription = "DotNetBar Bar (toolStrip_ExitCreateNewPg)";
			this.toolStrip_ExitCreateNewPg.AccessibleName = "DotNetBar Bar";
			this.toolStrip_ExitCreateNewPg.AccessibleRole = global::System.Windows.Forms.AccessibleRole.ToolBar;
			this.toolStrip_ExitCreateNewPg.AntiAlias = true;
			this.toolStrip_ExitCreateNewPg.BackColor = global::System.Drawing.SystemColors.Control;
			this.toolStrip_ExitCreateNewPg.CanCustomize = false;
			this.toolStrip_ExitCreateNewPg.CanDockBottom = false;
			this.toolStrip_ExitCreateNewPg.CanDockLeft = false;
			this.toolStrip_ExitCreateNewPg.CanDockRight = false;
			this.toolStrip_ExitCreateNewPg.CanDockTab = false;
			this.toolStrip_ExitCreateNewPg.CanReorderTabs = false;
			this.toolStrip_ExitCreateNewPg.DockOffset = 440;
			this.toolStrip_ExitCreateNewPg.DockSide = global::DevComponents.DotNetBar.eDockSide.Top;
			this.toolStrip_ExitCreateNewPg.Font = new global::System.Drawing.Font("Microsoft YaHei", 9f);
			this.toolStrip_ExitCreateNewPg.GrabHandleStyle = global::DevComponents.DotNetBar.eGrabHandleStyle.None;
			this.toolStrip_ExitCreateNewPg.Items.AddRange(new global::DevComponents.DotNetBar.BaseItem[]
			{
				this.toolStripButton_ExitCrtNewPg
			});
			this.toolStrip_ExitCreateNewPg.Location = new global::System.Drawing.Point(725, 0);
			this.toolStrip_ExitCreateNewPg.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.toolStrip_ExitCreateNewPg.Name = "toolStrip_ExitCreateNewPg";
			this.toolStrip_ExitCreateNewPg.Size = new global::System.Drawing.Size(72, 31);
			this.toolStrip_ExitCreateNewPg.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.Metro;
			this.toolStrip_ExitCreateNewPg.TabIndex = 12;
			this.toolStrip_ExitCreateNewPg.TabStop = false;
			this.toolStrip_ExitCreateNewPg.Text = "退出栏";
			this.toolStripButton_ExitCrtNewPg.ButtonStyle = global::DevComponents.DotNetBar.eButtonStyle.ImageAndText;
			this.toolStripButton_ExitCrtNewPg.Image = global::ns28.Class372.door_in;
			this.toolStripButton_ExitCrtNewPg.ImageFixedSize = new global::System.Drawing.Size(18, 18);
			this.toolStripButton_ExitCrtNewPg.Name = "toolStripButton_ExitCrtNewPg";
			this.toolStripButton_ExitCrtNewPg.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripButton_ExitCrtNewPg.Text = "退出";
			this.toolStrip_Setting.AccessibleDescription = "DotNetBar Bar (toolStrip_Setting)";
			this.toolStrip_Setting.AccessibleName = "DotNetBar Bar";
			this.toolStrip_Setting.AccessibleRole = global::System.Windows.Forms.AccessibleRole.ToolBar;
			this.toolStrip_Setting.AntiAlias = true;
			this.toolStrip_Setting.BackColor = global::System.Drawing.SystemColors.Control;
			this.toolStrip_Setting.CanAutoHide = false;
			this.toolStrip_Setting.CanDockBottom = false;
			this.toolStrip_Setting.CanHide = true;
			this.toolStrip_Setting.DockOffset = 711;
			this.toolStrip_Setting.DockSide = global::DevComponents.DotNetBar.eDockSide.Top;
			this.toolStrip_Setting.Font = new global::System.Drawing.Font("Microsoft YaHei", 9f);
			this.toolStrip_Setting.GrabHandleStyle = global::DevComponents.DotNetBar.eGrabHandleStyle.None;
			this.toolStrip_Setting.Items.AddRange(new global::DevComponents.DotNetBar.BaseItem[]
			{
				this.toolStripBtnItem_SelectDate,
				this.toolStripDropDownButton_Settings
			});
			this.toolStrip_Setting.Location = new global::System.Drawing.Point(799, 0);
			this.toolStrip_Setting.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.toolStrip_Setting.Name = "toolStrip_Setting";
			this.toolStrip_Setting.RoundCorners = false;
			this.toolStrip_Setting.Size = new global::System.Drawing.Size(146, 31);
			this.toolStrip_Setting.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.Metro;
			this.toolStrip_Setting.TabIndex = 10;
			this.toolStrip_Setting.TabStop = false;
			this.toolStrip_Setting.Text = "设置栏";
			this.toolStripBtnItem_SelectDate.ButtonStyle = global::DevComponents.DotNetBar.eButtonStyle.ImageAndText;
			this.toolStripBtnItem_SelectDate.Image = global::ns28.Class372.calendar_date;
			this.toolStripBtnItem_SelectDate.ImageFixedSize = new global::System.Drawing.Size(16, 16);
			this.toolStripBtnItem_SelectDate.Name = "toolStripBtnItem_SelectDate";
			this.toolStripBtnItem_SelectDate.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBtnItem_SelectDate.Text = "期间";
			this.toolStripDropDownButton_Settings.AutoExpandOnClick = true;
			this.toolStripDropDownButton_Settings.ButtonStyle = global::DevComponents.DotNetBar.eButtonStyle.ImageAndText;
			this.toolStripDropDownButton_Settings.Image = global::ns28.Class372.chart;
			this.toolStripDropDownButton_Settings.ImageFixedSize = new global::System.Drawing.Size(18, 18);
			this.toolStripDropDownButton_Settings.Name = "toolStripDropDownButton_Settings";
			this.toolStripDropDownButton_Settings.SubItems.AddRange(new global::DevComponents.DotNetBar.BaseItem[]
			{
				this.工具栏ToolStripMenuItem,
				this.功能栏ToolStripMenuItem,
				this.btnItem_市场板块,
				this.btnItem_财报分析,
				this.btnItem_条件选股,
				this.btnItem_双盲测试,
				this.btnItem_画线下单,
				this.btnItem_交易分析,
				this.btnItem_视频讲座,
				this.btnItem_画线工具,
				this.btnItem_系统参数,
				this.btnItem_界面风格,
				this.btnItem_品种设置,
				this.btnItem_指标管理,
				this.btnItem_数据管理,
				this.btnItem_热键定义,
				this.btnItem_页面设置,
				this.btnItem_交易账户,
				this.btnItem_Help,
				this.btnItem_VideoHelp,
				this.btnItem_TExWeb,
				this.btnItem_AppUpg,
				this.toolStripMenuItem_BuySoft,
				this.toolStripMenuItem_VerInfo,
				this.关于ToolStripMenuItem
			});
			this.toolStripDropDownButton_Settings.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripDropDownButton_Settings.Text = "系统";
			this.工具栏ToolStripMenuItem.Name = "工具栏ToolStripMenuItem";
			this.工具栏ToolStripMenuItem.Shortcuts.Add(global::DevComponents.DotNetBar.eShortcut.F8);
			this.工具栏ToolStripMenuItem.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.工具栏ToolStripMenuItem.Text = "工具栏";
			this.工具栏ToolStripMenuItem.Click += new global::System.EventHandler(this.工具栏ToolStripMenuItem_Click);
			this.功能栏ToolStripMenuItem.Name = "功能栏ToolStripMenuItem";
			this.功能栏ToolStripMenuItem.Shortcuts.Add(global::DevComponents.DotNetBar.eShortcut.F12);
			this.功能栏ToolStripMenuItem.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.功能栏ToolStripMenuItem.Text = "功能栏";
			this.功能栏ToolStripMenuItem.Click += new global::System.EventHandler(this.功能栏ToolStripMenuItem_Click);
			this.btnItem_市场板块.BeginGroup = true;
			this.btnItem_市场板块.Name = "btnItem_市场板块";
			this.btnItem_市场板块.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_市场板块.Text = "市场板块";
			this.btnItem_财报分析.Name = "btnItem_财报分析";
			this.btnItem_财报分析.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_财报分析.Text = "财报分析";
			this.btnItem_条件选股.Name = "btnItem_条件选股";
			this.btnItem_条件选股.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_条件选股.Text = "条件选股";
			this.btnItem_双盲测试.Image = global::ns28.Class372.Glass_Face_48;
			this.btnItem_双盲测试.ImageFixedSize = new global::System.Drawing.Size(16, 16);
			this.btnItem_双盲测试.Name = "btnItem_双盲测试";
			this.btnItem_双盲测试.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_双盲测试.Text = "双盲测试";
			this.btnItem_双盲测试.Click += new global::System.EventHandler(this.btnItem_双盲测试_Click);
			this.btnItem_画线下单.Name = "btnItem_画线下单";
			this.btnItem_画线下单.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_画线下单.Text = "画线下单";
			this.btnItem_交易账户.Image = global::ns28.Class372.folder_accept;
			this.btnItem_交易账户.ImageFixedSize = new global::System.Drawing.Size(16, 16);
			this.btnItem_交易账户.Name = "btnItem_交易账户";
			this.btnItem_交易账户.SubItems.AddRange(new global::DevComponents.DotNetBar.BaseItem[]
			{
				this.新建账户ToolStripMenuItem,
				this.切换账户ToolStripMenuItem,
				this.删除账户ToolStripMenuItem
			});
			this.btnItem_交易账户.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_交易账户.Text = "交易账户";
			this.新建账户ToolStripMenuItem.Image = global::ns28.Class372.folder_add;
			this.新建账户ToolStripMenuItem.ImageFixedSize = new global::System.Drawing.Size(18, 18);
			this.新建账户ToolStripMenuItem.Name = "新建账户ToolStripMenuItem";
			this.新建账户ToolStripMenuItem.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.新建账户ToolStripMenuItem.Text = "新建账户...";
			this.切换账户ToolStripMenuItem.Name = "切换账户ToolStripMenuItem";
			this.切换账户ToolStripMenuItem.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.切换账户ToolStripMenuItem.Text = "切换账户";
			this.删除账户ToolStripMenuItem.Name = "删除账户ToolStripMenuItem";
			this.删除账户ToolStripMenuItem.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.删除账户ToolStripMenuItem.Text = "删除账户";
			this.btnItem_品种设置.Name = "btnItem_品种设置";
			this.btnItem_品种设置.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_品种设置.Text = "品种设置";
			this.btnItem_品种设置.Click += new global::System.EventHandler(this.btnItem_品种设置_Click);
			this.btnItem_交易分析.Name = "btnItem_交易分析";
			this.btnItem_交易分析.SubItems.AddRange(new global::DevComponents.DotNetBar.BaseItem[]
			{
				this.btnItem_交易分析_交易统计,
				this.btnItem_交易分析_权益走势,
				this.btnItem_交易分析_盈利曲线,
				this.btnItem_交易分析_品种盈亏,
				this.btnItem_交易分析_多空盈亏,
				this.btnItem_交易分析_时间盈亏
			});
			this.btnItem_交易分析.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_交易分析.Text = "交易分析";
			this.btnItem_交易分析_交易统计.Name = "btnItem_交易分析_交易统计";
			this.btnItem_交易分析_交易统计.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_交易分析_交易统计.Text = "交易统计";
			this.btnItem_交易分析_权益走势.Name = "btnItem_交易分析_权益走势";
			this.btnItem_交易分析_权益走势.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_交易分析_权益走势.Text = "权益走势";
			this.btnItem_交易分析_盈利曲线.Name = "btnItem_交易分析_盈利曲线";
			this.btnItem_交易分析_盈利曲线.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_交易分析_盈利曲线.Text = "盈利曲线";
			this.btnItem_交易分析_品种盈亏.Name = "btnItem_交易分析_品种盈亏";
			this.btnItem_交易分析_品种盈亏.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_交易分析_品种盈亏.Text = "品种盈亏";
			this.btnItem_交易分析_多空盈亏.Name = "btnItem_交易分析_多空盈亏";
			this.btnItem_交易分析_多空盈亏.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_交易分析_多空盈亏.Text = "多空盈亏";
			this.btnItem_交易分析_时间盈亏.Name = "btnItem_交易分析_时间盈亏";
			this.btnItem_交易分析_时间盈亏.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_交易分析_时间盈亏.Text = "时间盈亏";
			this.btnItem_视频讲座.Name = "btnItem_视频讲座";
			this.btnItem_视频讲座.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_视频讲座.Text = "视频讲座";
			this.btnItem_画线工具.ImageFixedSize = new global::System.Drawing.Size(16, 16);
			this.btnItem_画线工具.Name = "btnItem_画线工具";
			this.btnItem_画线工具.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_画线工具.Text = "画线工具";
			this.btnItem_指标管理.Name = "btnItem_指标管理";
			this.btnItem_指标管理.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_指标管理.Text = "指标管理";
			this.btnItem_系统参数.BeginGroup = true;
			this.btnItem_系统参数.Image = global::ns28.Class372.processes;
			this.btnItem_系统参数.ImageFixedSize = new global::System.Drawing.Size(16, 16);
			this.btnItem_系统参数.Name = "btnItem_系统参数";
			this.btnItem_系统参数.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_系统参数.Text = "系统参数";
			this.btnItem_系统参数.Click += new global::System.EventHandler(this.btnItem_系统参数_Click);
			this.btnItem_数据管理.Image = global::ns28.Class372.database_down;
			this.btnItem_数据管理.ImageFixedSize = new global::System.Drawing.Size(16, 16);
			this.btnItem_数据管理.Name = "btnItem_数据管理";
			this.btnItem_数据管理.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_数据管理.Text = "数据管理";
			this.btnItem_热键定义.Image = global::ns28.Class372.shortcuts24x24;
			this.btnItem_热键定义.ImageFixedSize = new global::System.Drawing.Size(16, 16);
			this.btnItem_热键定义.Name = "btnItem_热键定义";
			this.btnItem_热键定义.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_热键定义.Text = "热键定义";
			this.btnItem_页面设置.Image = global::ns28.Class372.application_split;
			this.btnItem_页面设置.ImageFixedSize = new global::System.Drawing.Size(16, 16);
			this.btnItem_页面设置.Name = "btnItem_页面设置";
			this.btnItem_页面设置.SubItems.AddRange(new global::DevComponents.DotNetBar.BaseItem[]
			{
				this.选择页面ToolStripMenuItem,
				this.新建页面ToolStripMenuItem,
				this.保存页面ToolStripMenuItem,
				this.另存页面ToolStripMenuItem,
				this.删除页面ToolStripMenuItem,
				this.自动保存ToolStripMenuItem
			});
			this.btnItem_页面设置.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_页面设置.Text = "页面设置";
			this.选择页面ToolStripMenuItem.Name = "选择页面ToolStripMenuItem";
			this.选择页面ToolStripMenuItem.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.选择页面ToolStripMenuItem.Text = "选择页面";
			this.新建页面ToolStripMenuItem.BeginGroup = true;
			this.新建页面ToolStripMenuItem.Image = global::ns28.Class372.application_split;
			this.新建页面ToolStripMenuItem.ImageFixedSize = new global::System.Drawing.Size(16, 16);
			this.新建页面ToolStripMenuItem.Name = "新建页面ToolStripMenuItem";
			this.新建页面ToolStripMenuItem.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.新建页面ToolStripMenuItem.Text = "新建页面...";
			this.保存页面ToolStripMenuItem.Image = global::ns28.Class372.floppy_disc;
			this.保存页面ToolStripMenuItem.ImageFixedSize = new global::System.Drawing.Size(16, 16);
			this.保存页面ToolStripMenuItem.Name = "保存页面ToolStripMenuItem";
			this.保存页面ToolStripMenuItem.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.保存页面ToolStripMenuItem.Text = "保存页面";
			this.另存页面ToolStripMenuItem.Name = "另存页面ToolStripMenuItem";
			this.另存页面ToolStripMenuItem.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.另存页面ToolStripMenuItem.Text = "另存页面...";
			this.删除页面ToolStripMenuItem.Name = "删除页面ToolStripMenuItem";
			this.删除页面ToolStripMenuItem.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.删除页面ToolStripMenuItem.Text = "删除页面";
			this.自动保存ToolStripMenuItem.Name = "自动保存ToolStripMenuItem";
			this.自动保存ToolStripMenuItem.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.自动保存ToolStripMenuItem.Text = "自动保存";
			this.btnItem_界面风格.ImageFixedSize = new global::System.Drawing.Size(16, 16);
			this.btnItem_界面风格.Name = "btnItem_界面风格";
			this.btnItem_界面风格.SubItems.AddRange(new global::DevComponents.DotNetBar.BaseItem[]
			{
				this.黑红经典ToolStripMenuItem,
				this.绿白经典ToolStripMenuItem,
				this.绿白现代ToolStripMenuItem
			});
			this.btnItem_界面风格.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_界面风格.Text = "界面风格";
			this.黑红经典ToolStripMenuItem.Name = "黑红经典ToolStripMenuItem";
			this.黑红经典ToolStripMenuItem.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.黑红经典ToolStripMenuItem.Text = "黑红经典";
			this.绿白经典ToolStripMenuItem.Name = "绿白经典ToolStripMenuItem";
			this.绿白经典ToolStripMenuItem.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.绿白经典ToolStripMenuItem.Text = "绿白经典";
			this.绿白经典ToolStripMenuItem.Click += new global::System.EventHandler(this.绿白经典ToolStripMenuItem_Click);
			this.绿白现代ToolStripMenuItem.Name = "绿白现代ToolStripMenuItem";
			this.绿白现代ToolStripMenuItem.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.绿白现代ToolStripMenuItem.Text = "绿白现代";
			this.btnItem_Help.BeginGroup = true;
			this.btnItem_Help.Image = global::ns28.Class372.quesmark;
			this.btnItem_Help.ImageFixedSize = new global::System.Drawing.Size(16, 16);
			this.btnItem_Help.Name = "btnItem_Help";
			this.btnItem_Help.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_Help.Text = "帮助文档";
			this.btnItem_Help.Click += new global::System.EventHandler(this.btnItem_Help_Click);
			this.btnItem_VideoHelp.Name = "btnItem_VideoHelp";
			this.btnItem_VideoHelp.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_VideoHelp.Text = "帮助视频";
			this.btnItem_VideoHelp.Click += new global::System.EventHandler(this.btnItem_VideoHelp_Click);
			this.btnItem_TExWeb.Name = "btnItem_TExWeb";
			this.btnItem_TExWeb.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_TExWeb.Text = "官方网站";
			this.btnItem_TExWeb.Click += new global::System.EventHandler(this.btnItem_TExWeb_Click);
			this.btnItem_AppUpg.Name = "btnItem_AppUpg";
			this.btnItem_AppUpg.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.btnItem_AppUpg.Text = "升级检测";
			this.btnItem_AppUpg.Click += new global::System.EventHandler(this.btnItem_AppUpg_Click);
			this.toolStripMenuItem_BuySoft.Name = "toolStripMenuItem_BuySoft";
			this.toolStripMenuItem_BuySoft.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripMenuItem_BuySoft.Text = "购买软件";
			this.toolStripMenuItem_VerInfo.Name = "toolStripMenuItem_VerInfo";
			this.toolStripMenuItem_VerInfo.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripMenuItem_VerInfo.Text = "版本说明";
			this.关于ToolStripMenuItem.Name = "关于ToolStripMenuItem";
			this.关于ToolStripMenuItem.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.关于ToolStripMenuItem.Text = "关于...";
			this.关于ToolStripMenuItem.Click += new global::System.EventHandler(this.关于ToolStripMenuItem_Click);
			this.toolStrip_Periods.AccessibleDescription = "DotNetBar Bar (toolStrip_Periods)";
			this.toolStrip_Periods.AccessibleName = "DotNetBar Bar";
			this.toolStrip_Periods.AccessibleRole = global::System.Windows.Forms.AccessibleRole.ToolBar;
			this.toolStrip_Periods.AntiAlias = true;
			this.toolStrip_Periods.BackColor = global::System.Drawing.SystemColors.Control;
			this.toolStrip_Periods.CanDockBottom = false;
			this.toolStrip_Periods.CanDockTab = false;
			this.toolStrip_Periods.CanHide = true;
			this.toolStrip_Periods.CanReorderTabs = false;
			this.toolStrip_Periods.DockOffset = 792;
			this.toolStrip_Periods.DockSide = global::DevComponents.DotNetBar.eDockSide.Top;
			this.toolStrip_Periods.Font = new global::System.Drawing.Font("Microsoft YaHei", 9f);
			this.toolStrip_Periods.GrabHandleStyle = global::DevComponents.DotNetBar.eGrabHandleStyle.None;
			this.toolStrip_Periods.Items.AddRange(new global::DevComponents.DotNetBar.BaseItem[]
			{
				this.toolStripBtn_Tick,
				this.toolStripBtn_NPeriod,
				this.toolStripBttn_1m,
				this.toolStripBttn_3m,
				this.toolStripBttn_5m,
				this.toolStripBttn_10m,
				this.toolStripBttn_15m,
				this.toolStripBttn_30m,
				this.toolStripBttn_1h,
				this.toolStripBttn_2h,
				this.toolStripBttn_4h,
				this.toolStripBttn_1d,
				this.toolStripBttn_1w,
				this.toolStripBttn_1Mth
			});
			this.toolStrip_Periods.Location = new global::System.Drawing.Point(947, 0);
			this.toolStrip_Periods.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.toolStrip_Periods.Name = "toolStrip_Periods";
			this.toolStrip_Periods.Size = new global::System.Drawing.Size(327, 31);
			this.toolStrip_Periods.Style = global::DevComponents.DotNetBar.eDotNetBarStyle.Metro;
			this.toolStrip_Periods.TabIndex = 11;
			this.toolStrip_Periods.TabStop = false;
			this.toolStrip_Periods.Text = "周期栏";
			this.toolStripBtn_NPeriod.AutoExpandOnClick = true;
			this.toolStripBtn_NPeriod.Name = "toolStripBtn_NPeriod";
			this.toolStripBtn_NPeriod.SubItems.AddRange(new global::DevComponents.DotNetBar.BaseItem[]
			{
				this.itemContainer_NMins,
				this.itemContainer_NHours,
				this.itemContainer_NDays
			});
			this.toolStripBtn_NPeriod.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBtn_NPeriod.Text = "N";
			this.itemContainer_NMins.BackgroundStyle.CornerType = global::DevComponents.DotNetBar.eCornerType.Square;
			this.itemContainer_NMins.Name = "itemContainer_NMins";
			this.itemContainer_NMins.SubItems.AddRange(new global::DevComponents.DotNetBar.BaseItem[]
			{
				this.txtBoxItem_NMins,
				this.toolStripBtnItem_NMins
			});
			this.itemContainer_NMins.TitleStyle.CornerType = global::DevComponents.DotNetBar.eCornerType.Square;
			this.txtBoxItem_NMins.AlwaysShowCaption = true;
			this.txtBoxItem_NMins.Caption = "分钟";
			this.txtBoxItem_NMins.Name = "txtBoxItem_NMins";
			this.txtBoxItem_NMins.Text = "10";
			this.txtBoxItem_NMins.TextBoxWidth = 18;
			this.txtBoxItem_NMins.WatermarkColor = global::System.Drawing.SystemColors.GrayText;
			this.txtBoxItem_NMins.HideBorder = true;
			this.toolStripBtnItem_NMins.Image = global::ns28.Class372.flash_16x16;
			this.toolStripBtnItem_NMins.Name = "toolStripBtnItem_NMins";
			this.toolStripBtnItem_NMins.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBtnItem_NMins.Text = "应用";
			this.itemContainer_NHours.BackgroundStyle.CornerType = global::DevComponents.DotNetBar.eCornerType.Square;
			this.itemContainer_NHours.Name = "itemContainer_NHours";
			this.itemContainer_NHours.SubItems.AddRange(new global::DevComponents.DotNetBar.BaseItem[]
			{
				this.txtBoxItem_NHours,
				this.toolStripBtnItem_NHours
			});
			this.itemContainer_NHours.TitleStyle.CornerType = global::DevComponents.DotNetBar.eCornerType.Square;
			this.txtBoxItem_NHours.AlwaysShowCaption = true;
			this.txtBoxItem_NHours.Caption = "小时";
			this.txtBoxItem_NHours.Name = "txtBoxItem_NHours";
			this.txtBoxItem_NHours.Text = "10";
			this.txtBoxItem_NHours.TextBoxWidth = 18;
			this.txtBoxItem_NHours.WatermarkColor = global::System.Drawing.SystemColors.GrayText;
			this.txtBoxItem_NHours.HideBorder = true;
			this.toolStripBtnItem_NHours.Image = global::ns28.Class372.flash_16x16;
			this.toolStripBtnItem_NHours.Name = "toolStripBtnItem_NHours";
			this.toolStripBtnItem_NHours.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBtnItem_NHours.Text = "应用";
			this.itemContainer_NDays.BackgroundStyle.CornerType = global::DevComponents.DotNetBar.eCornerType.Square;
			this.itemContainer_NDays.Name = "itemContainer_NDays";
			this.itemContainer_NDays.SubItems.AddRange(new global::DevComponents.DotNetBar.BaseItem[]
			{
				this.txtBoxItem_NDays,
				this.toolStripBtnItem_NDays
			});
			this.itemContainer_NDays.TitleStyle.CornerType = global::DevComponents.DotNetBar.eCornerType.Square;
			this.txtBoxItem_NDays.AlwaysShowCaption = true;
			this.txtBoxItem_NDays.Caption = "天数";
			this.txtBoxItem_NDays.Name = "txtBoxItem_NDays";
			this.txtBoxItem_NDays.Text = "10";
			this.txtBoxItem_NDays.TextBoxWidth = 18;
			this.txtBoxItem_NDays.WatermarkColor = global::System.Drawing.SystemColors.GrayText;
			this.txtBoxItem_NDays.HideBorder = true;
			this.toolStripBtnItem_NDays.Image = global::ns28.Class372.flash_16x16;
			this.toolStripBtnItem_NDays.Name = "toolStripBtnItem_NDays";
			this.toolStripBtnItem_NDays.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBtnItem_NDays.Text = "应用";
			this.toolStripBtn_Tick.Name = "toolStripBtn_Tick";
			this.toolStripBtn_Tick.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBtn_Tick.Text = "分时";
			this.toolStripBttn_1m.Name = "toolStripBttn_1m";
			this.toolStripBttn_1m.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBttn_1m.Text = "1m";
			this.toolStripBttn_1m.Tooltip = "1分钟";
			this.toolStripBttn_3m.Name = "toolStripBttn_3m";
			this.toolStripBttn_3m.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBttn_3m.Text = "3m";
			this.toolStripBttn_3m.Tooltip = "3分钟";
			this.toolStripBttn_5m.Name = "toolStripBttn_5m";
			this.toolStripBttn_5m.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBttn_5m.Text = "5m";
			this.toolStripBttn_5m.Tooltip = "5分钟";
			this.toolStripBttn_10m.Name = "toolStripBttn_10m";
			this.toolStripBttn_10m.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBttn_10m.Text = "10m";
			this.toolStripBttn_10m.Tooltip = "10分钟";
			this.toolStripBttn_15m.Name = "toolStripBttn_15m";
			this.toolStripBttn_15m.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBttn_15m.Text = "15m";
			this.toolStripBttn_15m.Tooltip = "15分钟";
			this.toolStripBttn_30m.Name = "toolStripBttn_30m";
			this.toolStripBttn_30m.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBttn_30m.Text = "30m";
			this.toolStripBttn_30m.Tooltip = "30分钟";
			this.toolStripBttn_1h.Name = "toolStripBttn_1h";
			this.toolStripBttn_1h.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBttn_1h.Text = "1h";
			this.toolStripBttn_1h.Tooltip = "1小时";
			this.toolStripBttn_2h.Name = "toolStripBttn_2h";
			this.toolStripBttn_2h.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBttn_2h.Text = "2h";
			this.toolStripBttn_2h.Tooltip = "2小时";
			this.toolStripBttn_4h.Name = "toolStripBttn_4h";
			this.toolStripBttn_4h.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBttn_4h.Text = "4h";
			this.toolStripBttn_4h.Tooltip = "4小时";
			this.toolStripBttn_1d.Name = "toolStripBttn_1d";
			this.toolStripBttn_1d.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBttn_1d.Text = "日";
			this.toolStripBttn_1d.Tooltip = "日线";
			this.toolStripBttn_1w.Name = "toolStripBttn_1w";
			this.toolStripBttn_1w.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBttn_1w.Text = "周";
			this.toolStripBttn_1w.Tooltip = "周线";
			this.toolStripBttn_1Mth.Name = "toolStripBttn_1Mth";
			this.toolStripBttn_1Mth.SubItemTriangleColor = global::System.Drawing.Color.Empty;
			this.toolStripBttn_1Mth.Text = "月";
			this.toolStripBttn_1Mth.Tooltip = "月线";
			this.dockSite_Top.AccessibleRole = global::System.Windows.Forms.AccessibleRole.Window;
			this.dockSite_Top.Dock = global::System.Windows.Forms.DockStyle.Top;
			this.dockSite_Top.DocumentDockContainer = new global::DevComponents.DotNetBar.DocumentDockContainer();
			this.dockSite_Top.Location = new global::System.Drawing.Point(0, 31);
			this.dockSite_Top.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			this.dockSite_Top.Name = "dockSite_Top";
			this.dockSite_Top.Size = new global::System.Drawing.Size(1274, 0);
			this.dockSite_Top.TabIndex = 5;
			this.dockSite_Top.TabStop = false;
			this.panel_Below.Dock = global::System.Windows.Forms.DockStyle.Fill;
			this.panel_Below.Location = new global::System.Drawing.Point(0, 31);
			this.panel_Below.Margin = new global::System.Windows.Forms.Padding(0);
			this.panel_Below.Name = "panel_Below";
			this.panel_Below.Size = new global::System.Drawing.Size(1274, 394);
			this.panel_Below.TabIndex = 11;
			this.toolTip_0.AutomaticDelay = 200;
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(8f, 15f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new global::System.Drawing.Size(1274, 735);
			base.Controls.Add(this.panel_Below);
			base.Controls.Add(this.dockSite_Below);
			base.Controls.Add(this.dockSite_Right);
			base.Controls.Add(this.dockSite_Left);
			base.Controls.Add(this.dockSite_Top);
			base.Controls.Add(this.dockSite_ToolbarLeft);
			base.Controls.Add(this.dockSite_ToolbarRight);
			base.Controls.Add(this.dockSite_ToolBar);
			base.Controls.Add(this.dockSite_Bottom);
			this.DoubleBuffered = true;
			base.Margin = new global::System.Windows.Forms.Padding(3, 2, 3, 2);
			base.Name = "MainForm";
			this.dockSite_Below.ResumeLayout(false);
			((global::System.ComponentModel.ISupportInitialize)this.bar_AcctTrans).EndInit();
			this.bar_AcctTrans.ResumeLayout(false);
			this.dockSite_Bottom.ResumeLayout(false);
			((global::System.ComponentModel.ISupportInitialize)this.statusBar).EndInit();
			this.dockSite_ToolBar.ResumeLayout(false);
			((global::System.ComponentModel.ISupportInitialize)this.toolStrip_Trading).EndInit();
			this.toolStrip_Trading.ResumeLayout(false);
			((global::System.ComponentModel.ISupportInitialize)this.numericUpDown_Units).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.numericUpDown_Price).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.toolStrip_Ctrls).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.toolStrip_ExitCreateNewPg).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.toolStrip_Setting).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.toolStrip_Periods).EndInit();
			base.ResumeLayout(false);
		}

		// Token: 0x04000DCE RID: 3534
		private global::System.ComponentModel.IContainer icontainer_0;

		// Token: 0x04000DCF RID: 3535
		private global::DevComponents.DotNetBar.DotNetBarManager dotNetBarManager_0;

		// Token: 0x04000DD0 RID: 3536
		private global::DevComponents.DotNetBar.DockSite dockSite_Below;

		// Token: 0x04000DD1 RID: 3537
		private global::DevComponents.DotNetBar.DockSite dockSite_Left;

		// Token: 0x04000DD2 RID: 3538
		private global::DevComponents.DotNetBar.DockSite dockSite_Right;

		// Token: 0x04000DD3 RID: 3539
		private global::DevComponents.DotNetBar.DockSite dockSite_Top;

		// Token: 0x04000DD4 RID: 3540
		private global::DevComponents.DotNetBar.DockSite dockSite_ToolbarLeft;

		// Token: 0x04000DD5 RID: 3541
		private global::DevComponents.DotNetBar.DockSite dockSite_ToolbarRight;

		// Token: 0x04000DD6 RID: 3542
		private global::DevComponents.DotNetBar.DockSite dockSite_ToolBar;

		// Token: 0x04000DD7 RID: 3543
		private global::DevComponents.DotNetBar.DockSite dockSite_Bottom;

		// Token: 0x04000DD8 RID: 3544
		private global::DevComponents.DotNetBar.Bar bar_AcctTrans;

		// Token: 0x04000DD9 RID: 3545
		private global::DevComponents.DotNetBar.PanelDockContainer panelDockContainer_AcctTrans;

		// Token: 0x04000DDA RID: 3546
		private global::DevComponents.DotNetBar.DockContainerItem dockContainer_AcctTrans;

		// Token: 0x04000DDB RID: 3547
		private global::DevComponents.DotNetBar.Bar toolStrip_Trading;

		// Token: 0x04000DDC RID: 3548
		private global::ns32.Class302 numericUpDown_Units;

		// Token: 0x04000DDD RID: 3549
		private global::ns32.Class302 numericUpDown_Price;

		// Token: 0x04000DDE RID: 3550
		private global::DevComponents.DotNetBar.ButtonItem toolStripBttn_Long;

		// Token: 0x04000DDF RID: 3551
		private global::DevComponents.DotNetBar.ButtonItem toolStripBttn_ClsLong;

		// Token: 0x04000DE0 RID: 3552
		private global::DevComponents.DotNetBar.ButtonItem toolStripBttn_Short;

		// Token: 0x04000DE1 RID: 3553
		private global::DevComponents.DotNetBar.ButtonItem toolStripBttn_ClsShort;

		// Token: 0x04000DE2 RID: 3554
		private global::DevComponents.DotNetBar.LabelItem labelItem_Lots;

		// Token: 0x04000DE3 RID: 3555
		private global::DevComponents.DotNetBar.ControlContainerItem controlContainerItem_Units;

		// Token: 0x04000DE4 RID: 3556
		private global::DevComponents.DotNetBar.LabelItem labelItem_Price;

		// Token: 0x04000DE5 RID: 3557
		private global::DevComponents.DotNetBar.ControlContainerItem controlContainerItem_Price;

		// Token: 0x04000DE6 RID: 3558
		private global::DevComponents.DotNetBar.Bar toolStrip_Ctrls;

		// Token: 0x04000DE7 RID: 3559
		private global::DevComponents.DotNetBar.ButtonItem toolStripBtnItem_Start;

		// Token: 0x04000DE8 RID: 3560
		private global::DevComponents.DotNetBar.LabelItem labelItem_Speed;

		// Token: 0x04000DE9 RID: 3561
		private global::DevComponents.DotNetBar.SliderItem slider_Speed;

		// Token: 0x04000DEA RID: 3562
		private global::DevComponents.DotNetBar.Bar toolStrip_Setting;

		// Token: 0x04000DEB RID: 3563
		private global::DevComponents.DotNetBar.ButtonItem toolStripDropDownButton_Settings;

		// Token: 0x04000DEC RID: 3564
		private global::DevComponents.DotNetBar.ButtonItem 功能栏ToolStripMenuItem;

		// Token: 0x04000DED RID: 3565
		private global::DevComponents.DotNetBar.ButtonItem btnItem_交易账户;

		// Token: 0x04000DEE RID: 3566
		private global::DevComponents.DotNetBar.ButtonItem 新建账户ToolStripMenuItem;

		// Token: 0x04000DEF RID: 3567
		private global::DevComponents.DotNetBar.ButtonItem 切换账户ToolStripMenuItem;

		// Token: 0x04000DF0 RID: 3568
		private global::DevComponents.DotNetBar.ButtonItem 删除账户ToolStripMenuItem;

		// Token: 0x04000DF1 RID: 3569
		private global::DevComponents.DotNetBar.ButtonItem toolStripBtnItem_SelectDate;

		// Token: 0x04000DF2 RID: 3570
		private global::DevComponents.DotNetBar.ButtonItem btnItem_页面设置;

		// Token: 0x04000DF3 RID: 3571
		private global::DevComponents.DotNetBar.ButtonItem 选择页面ToolStripMenuItem;

		// Token: 0x04000DF4 RID: 3572
		private global::DevComponents.DotNetBar.ButtonItem 新建页面ToolStripMenuItem;

		// Token: 0x04000DF5 RID: 3573
		private global::DevComponents.DotNetBar.ButtonItem 保存页面ToolStripMenuItem;

		// Token: 0x04000DF6 RID: 3574
		private global::DevComponents.DotNetBar.ButtonItem 另存页面ToolStripMenuItem;

		// Token: 0x04000DF7 RID: 3575
		private global::DevComponents.DotNetBar.ButtonItem 自动保存ToolStripMenuItem;

		// Token: 0x04000DF8 RID: 3576
		private global::DevComponents.DotNetBar.ButtonItem 删除页面ToolStripMenuItem;

		// Token: 0x04000DF9 RID: 3577
		private global::DevComponents.DotNetBar.ButtonItem btnItem_界面风格;

		// Token: 0x04000DFA RID: 3578
		private global::DevComponents.DotNetBar.ButtonItem 黑红经典ToolStripMenuItem;

		// Token: 0x04000DFB RID: 3579
		private global::DevComponents.DotNetBar.ButtonItem 绿白现代ToolStripMenuItem;

		// Token: 0x04000DFC RID: 3580
		private global::DevComponents.DotNetBar.Bar toolStrip_Periods;

		// Token: 0x04000DFD RID: 3581
		private global::DevComponents.DotNetBar.ButtonItem toolStripBtn_Tick;

		// Token: 0x04000DFE RID: 3582
		private global::DevComponents.DotNetBar.ButtonItem toolStripBttn_1m;

		// Token: 0x04000DFF RID: 3583
		private global::DevComponents.DotNetBar.ButtonItem toolStripBttn_3m;

		// Token: 0x04000E00 RID: 3584
		private global::DevComponents.DotNetBar.ButtonItem toolStripBttn_5m;

		// Token: 0x04000E01 RID: 3585
		private global::DevComponents.DotNetBar.ButtonItem toolStripBttn_10m;

		// Token: 0x04000E02 RID: 3586
		private global::DevComponents.DotNetBar.ButtonItem toolStripBttn_15m;

		// Token: 0x04000E03 RID: 3587
		private global::DevComponents.DotNetBar.ButtonItem toolStripBttn_30m;

		// Token: 0x04000E04 RID: 3588
		private global::DevComponents.DotNetBar.ButtonItem toolStripBttn_1h;

		// Token: 0x04000E05 RID: 3589
		private global::DevComponents.DotNetBar.ButtonItem toolStripBttn_2h;

		// Token: 0x04000E06 RID: 3590
		private global::DevComponents.DotNetBar.ButtonItem toolStripBttn_1d;

		// Token: 0x04000E07 RID: 3591
		private global::DevComponents.DotNetBar.ButtonItem toolStripBttn_1w;

		// Token: 0x04000E08 RID: 3592
		private global::DevComponents.DotNetBar.ButtonItem toolStripBttn_1Mth;

		// Token: 0x04000E09 RID: 3593
		private global::System.Windows.Forms.Panel panel_Below;

		// Token: 0x04000E0A RID: 3594
		private global::DevComponents.DotNetBar.Bar statusBar;

		// Token: 0x04000E0B RID: 3595
		private global::DevComponents.DotNetBar.ButtonItem toolStripDropDownButton_Pages;

		// Token: 0x04000E0C RID: 3596
		private global::DevComponents.DotNetBar.LabelItem labelItem_账户;

		// Token: 0x04000E0D RID: 3597
		private global::DevComponents.DotNetBar.LabelItem toolStripStatusLabel_AutoStopStatus;

		// Token: 0x04000E0E RID: 3598
		private global::DevComponents.DotNetBar.LabelItem toolStripStatusLabel_AutoLimitStatus;

		// Token: 0x04000E0F RID: 3599
		private global::DevComponents.DotNetBar.LabelItem labelItem_浮动盈亏;

		// Token: 0x04000E10 RID: 3600
		private global::DevComponents.DotNetBar.LabelItem toolStripStatusLabel_Profits;

		// Token: 0x04000E11 RID: 3601
		private global::DevComponents.DotNetBar.LabelItem toolStripStatusLabel_Info;

		// Token: 0x04000E12 RID: 3602
		private global::DevComponents.DotNetBar.Bar toolStrip_ExitCreateNewPg;

		// Token: 0x04000E13 RID: 3603
		private global::DevComponents.DotNetBar.ButtonItem toolStripButton_ExitCrtNewPg;

		// Token: 0x04000E14 RID: 3604
		private global::DevComponents.DotNetBar.ButtonItem 工具栏ToolStripMenuItem;

		// Token: 0x04000E15 RID: 3605
		private global::System.Windows.Forms.ToolTip toolTip_0;

		// Token: 0x04000E16 RID: 3606
		private global::DevComponents.DotNetBar.LabelItem labelItem_页面;

		// Token: 0x04000E17 RID: 3607
		private global::DevComponents.DotNetBar.ButtonItem toolStripDropDownButton_Accts;

		// Token: 0x04000E18 RID: 3608
		private global::DevComponents.DotNetBar.LabelItem labelItem_品种;

		// Token: 0x04000E19 RID: 3609
		private global::DevComponents.DotNetBar.ButtonItem toolStripDropDownButton_Symbls;

		// Token: 0x04000E1A RID: 3610
		private global::DevComponents.DotNetBar.ButtonItem 绿白经典ToolStripMenuItem;

		// Token: 0x04000E1B RID: 3611
		private global::DevComponents.DotNetBar.ButtonItem 关于ToolStripMenuItem;

		// Token: 0x04000E1C RID: 3612
		private global::DevComponents.DotNetBar.ButtonItem btnItem_品种设置;

		// Token: 0x04000E1D RID: 3613
		private global::DevComponents.DotNetBar.ButtonItem btnItem_Help;

		// Token: 0x04000E1E RID: 3614
		private global::DevComponents.DotNetBar.ButtonItem btnItem_VideoHelp;

		// Token: 0x04000E1F RID: 3615
		private global::DevComponents.DotNetBar.ButtonItem btnItem_TExWeb;

		// Token: 0x04000E20 RID: 3616
		private global::DevComponents.DotNetBar.ButtonItem btnItem_AppUpg;

		// Token: 0x04000E21 RID: 3617
		private global::DevComponents.DotNetBar.ButtonItem btnItem_系统参数;

		// Token: 0x04000E22 RID: 3618
		private global::DevComponents.DotNetBar.ButtonItem btnItem_画线工具;

		// Token: 0x04000E23 RID: 3619
		private global::DevComponents.DotNetBar.LabelItem labelItem_平仓盈亏;

		// Token: 0x04000E24 RID: 3620
		private global::DevComponents.DotNetBar.LabelItem toolStripStatusLabel_CurrProfits;

		// Token: 0x04000E25 RID: 3621
		private global::DevComponents.DotNetBar.ButtonItem btnItem_双盲测试;

		// Token: 0x04000E26 RID: 3622
		private global::DevComponents.DotNetBar.LabelItem toolStripStatusLabel_BlindTest;

		// Token: 0x04000E27 RID: 3623
		private global::DevComponents.DotNetBar.ButtonItem toolStripBttn_4h;

		// Token: 0x04000E28 RID: 3624
		private global::DevComponents.DotNetBar.ButtonItem btnItem_数据管理;

		// Token: 0x04000E29 RID: 3625
		private global::DevComponents.DotNetBar.ButtonItem toolStripMenuItem_BuySoft;

		// Token: 0x04000E2A RID: 3626
		private global::DevComponents.DotNetBar.ButtonItem toolStripMenuItem_VerInfo;

		// Token: 0x04000E2B RID: 3627
		private global::DevComponents.DotNetBar.ButtonItem btnItem_热键定义;

		// Token: 0x04000E2C RID: 3628
		private global::DevComponents.DotNetBar.ButtonItem btnItem_画线下单;

		// Token: 0x04000E2D RID: 3629
		private global::DevComponents.DotNetBar.ButtonItem btnItem_指标管理;

		// Token: 0x04000E2E RID: 3630
		private global::DevComponents.DotNetBar.ButtonItem toolStripBtn_NPeriod;

		// Token: 0x04000E2F RID: 3631
		private global::DevComponents.DotNetBar.ItemContainer itemContainer_NMins;

		// Token: 0x04000E30 RID: 3632
		private global::DevComponents.DotNetBar.TextBoxItem txtBoxItem_NMins;

		// Token: 0x04000E31 RID: 3633
		private global::DevComponents.DotNetBar.ButtonItem toolStripBtnItem_NMins;

		// Token: 0x04000E32 RID: 3634
		private global::DevComponents.DotNetBar.ItemContainer itemContainer_NHours;

		// Token: 0x04000E33 RID: 3635
		private global::DevComponents.DotNetBar.TextBoxItem txtBoxItem_NHours;

		// Token: 0x04000E34 RID: 3636
		private global::DevComponents.DotNetBar.ButtonItem toolStripBtnItem_NHours;

		// Token: 0x04000E35 RID: 3637
		private global::DevComponents.DotNetBar.ItemContainer itemContainer_NDays;

		// Token: 0x04000E36 RID: 3638
		private global::DevComponents.DotNetBar.TextBoxItem txtBoxItem_NDays;

		// Token: 0x04000E37 RID: 3639
		private global::DevComponents.DotNetBar.ButtonItem toolStripBtnItem_NDays;

		// Token: 0x04000E38 RID: 3640
		private global::DevComponents.DotNetBar.ButtonItem toolStripBtnItem_SpanUnit_1m;

		// Token: 0x04000E39 RID: 3641
		private global::DevComponents.DotNetBar.ButtonItem btnItem_市场板块;

		// Token: 0x04000E3A RID: 3642
		private global::DevComponents.DotNetBar.ButtonItem btnItem_交易分析;

		// Token: 0x04000E3B RID: 3643
		private global::DevComponents.DotNetBar.ButtonItem btnItem_财报分析;

		// Token: 0x04000E3C RID: 3644
		private global::DevComponents.DotNetBar.ButtonItem btnItem_条件选股;

		// Token: 0x04000E3D RID: 3645
		private global::DevComponents.DotNetBar.ButtonItem btnItem_交易分析_交易统计;

		// Token: 0x04000E3E RID: 3646
		private global::DevComponents.DotNetBar.ButtonItem btnItem_交易分析_权益走势;

		// Token: 0x04000E3F RID: 3647
		private global::DevComponents.DotNetBar.ButtonItem btnItem_交易分析_盈利曲线;

		// Token: 0x04000E40 RID: 3648
		private global::DevComponents.DotNetBar.ButtonItem btnItem_交易分析_品种盈亏;

		// Token: 0x04000E41 RID: 3649
		private global::DevComponents.DotNetBar.ButtonItem btnItem_交易分析_多空盈亏;

		// Token: 0x04000E42 RID: 3650
		private global::DevComponents.DotNetBar.ButtonItem btnItem_交易分析_时间盈亏;

		// Token: 0x04000E43 RID: 3651
		private global::DevComponents.DotNetBar.ButtonItem btnItem_视频讲座;
	}
}
