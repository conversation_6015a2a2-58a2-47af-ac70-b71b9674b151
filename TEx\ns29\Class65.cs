﻿using System;
using System.ComponentModel;
using ns11;
using TEx;

namespace ns29
{
	// Token: 0x02000202 RID: 514
	[DesignerCategory("Code")]
	internal sealed class Class65 : Class63
	{
		// Token: 0x06001505 RID: 5381 RVA: 0x00008630 File Offset: 0x00006830
		public Class65(DrawLineStyle drawLineStyle_1 = null, IContainer icontainer_1 = null) : base(typeof(DrawLineWidth), drawLineStyle_1, icontainer_1)
		{
		}

		// Token: 0x06001506 RID: 5382 RVA: 0x0008A128 File Offset: 0x00088328
		protected override float vmethod_1(int int_0)
		{
			return DrawLineStyle.smethod_0((DrawLineWidth)int_0);
		}
	}
}
