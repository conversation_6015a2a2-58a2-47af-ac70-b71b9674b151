﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using ns11;
using ns15;
using ns26;
using ns28;
using ns33;
using ns6;
using ns9;
using TEx.Chart;
using TEx.Comn;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x020002AD RID: 685
	[Docking(DockingBehavior.AutoDock)]
	public sealed class TrdAnalysisPanel : UserControl
	{
		// Token: 0x06001E3E RID: 7742 RVA: 0x000CD5C4 File Offset: 0x000CB7C4
		public TrdAnalysisPanel()
		{
			this.InitializeComponent();
			this.tabControlPanel_Acct.ThemeAware = false;
			this.tabControlPanel_Report.ThemeAware = false;
			this.tabControlPanel_Acct.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_Report.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_PrftLine.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_EquityGraph.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_SymbPrft.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_TimePrft.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_LngShrtPrft.Padding = new System.Windows.Forms.Padding(0);
			this.panel_AcctInfo.BorderStyle = BorderStyle.None;
			this.panel_TradingStat.BorderStyle = BorderStyle.None;
			this.comboBox_tradingOverview_CurrOrAll.SelectedIndexChanged += this.comboBox_tradingOverview_CurrOrAll_SelectedIndexChanged;
			this.comboBox_Year.SelectedIndexChanged += this.comboBox_Year_SelectedIndexChanged;
			this.comboBox_Month.SelectedIndexChanged += this.comboBox_Month_SelectedIndexChanged;
			this.comboBox_Symbl.SelectedIndexChanged += this.comboBox_Symbl_SelectedIndexChanged;
			this.method_3();
			this.method_0();
			Base.Trading.TransCreated += this.method_42;
			Base.Trading.TransactionsUpdated += this.method_43;
			Base.Acct.AccountChanged += this.method_41;
			Base.UI.ChartThemeChanged += this.method_2;
			this.tabCtrl_Acct.SelectedTabChanging += this.tabCtrl_Acct_SelectedTabChanging;
			base.Disposed += this.TrdAnalysisPanel_Disposed;
		}

		// Token: 0x06001E3F RID: 7743 RVA: 0x000CD950 File Offset: 0x000CBB50
		private void TrdAnalysisPanel_Disposed(object sender, EventArgs e)
		{
			this.comboBox_Year.SelectedIndexChanged -= this.comboBox_Year_SelectedIndexChanged;
			this.comboBox_Month.SelectedIndexChanged -= this.comboBox_Month_SelectedIndexChanged;
			this.comboBox_Symbl.SelectedIndexChanged -= this.comboBox_Symbl_SelectedIndexChanged;
			Base.Trading.TransCreated -= this.method_42;
			Base.Trading.TransactionsUpdated -= this.method_43;
			Base.UI.ChartThemeChanged -= this.method_2;
			this.tabCtrl_Acct.SelectedTabChanging -= this.tabCtrl_Acct_SelectedTabChanging;
			this.tabCtrl_Acct = null;
		}

		// Token: 0x06001E40 RID: 7744 RVA: 0x000CD9F8 File Offset: 0x000CBBF8
		private void method_0()
		{
			float emSize = TApp.smethod_4(9f, false);
			this.font_0 = new Font("Microsoft Sans Serif", emSize, FontStyle.Bold);
			this.font_1 = new Font("Microsoft Sans Serif", emSize, FontStyle.Regular);
			this.tabCtrl_Acct.Font = this.font_1;
			this.tabCtrl_Acct.SelectedTabFont = this.font_1;
			foreach (object obj in this.panel_AcctInfo.Controls)
			{
				((System.Windows.Forms.Label)obj).Font = this.font_1;
			}
			foreach (object obj2 in this.panel_tradeAnlys.Controls)
			{
				((System.Windows.Forms.Label)obj2).Font = this.font_1;
			}
			emSize = TApp.smethod_4(8.25f, false);
			Font font = new Font("Microsoft Sans Serif", emSize);
			foreach (object obj3 in this.panel_TradeAnlys_Header.Controls)
			{
				Control control = (Control)obj3;
				if (control is ComboBox)
				{
					control.Font = font;
				}
				else
				{
					control.Font = this.font_1;
				}
			}
		}

		// Token: 0x06001E41 RID: 7745 RVA: 0x0000CA46 File Offset: 0x0000AC46
		public void method_1(ContextMenuStrip contextMenuStrip_0)
		{
			this.tabControlPanel_Acct.ContextMenuStrip = contextMenuStrip_0;
			this.tabControlPanel_Report.ContextMenuStrip = contextMenuStrip_0;
		}

		// Token: 0x06001E42 RID: 7746 RVA: 0x0000CA62 File Offset: 0x0000AC62
		private void method_2(object sender, EventArgs e)
		{
			this.method_3();
		}

		// Token: 0x06001E43 RID: 7747 RVA: 0x0000CA6C File Offset: 0x0000AC6C
		public void method_3()
		{
			this.method_4();
		}

		// Token: 0x06001E44 RID: 7748 RVA: 0x000CDB80 File Offset: 0x000CBD80
		private void method_4()
		{
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.panel_AcctInfo.BackColor = Class179.color_4;
				this.label_acct_账户.ForeColor = Class179.color_10;
				this.label_acct_初始资金.ForeColor = Class179.color_10;
				this.label_acct_动态权益.ForeColor = Class179.color_10;
				this.label_acct_可用资金.ForeColor = Class179.color_10;
				this.label_acct_浮动盈亏.ForeColor = Class179.color_10;
				this.label_acct_平仓盈亏.ForeColor = Class179.color_10;
				this.label_acct_手续费.ForeColor = Class179.color_10;
				this.label_acct_保证金.ForeColor = Class179.color_10;
				this.label_AcctName.ForeColor = Class179.color_10;
				this.label_IniBalance.ForeColor = Class179.color_10;
				this.label_CurrEquity.ForeColor = Class179.color_10;
				this.label_FreeMargin.ForeColor = Class179.color_10;
				this.label_FeeSum.ForeColor = Class179.color_10;
				this.label_UsedMargin.ForeColor = Class179.color_10;
				this.panel_TradingStat.BackColor = Class179.color_4;
			}
			else
			{
				this.panel_AcctInfo.BackColor = Class179.color_9;
				this.label_acct_账户.ForeColor = Class179.color_1;
				this.label_acct_初始资金.ForeColor = Class179.color_1;
				this.label_acct_动态权益.ForeColor = Class179.color_1;
				this.label_acct_可用资金.ForeColor = Class179.color_1;
				this.label_acct_浮动盈亏.ForeColor = Class179.color_1;
				this.label_acct_平仓盈亏.ForeColor = Class179.color_1;
				this.label_acct_手续费.ForeColor = Class179.color_1;
				this.label_acct_保证金.ForeColor = Class179.color_1;
				this.label_AcctName.ForeColor = Class179.color_1;
				this.label_IniBalance.ForeColor = Class179.color_1;
				this.label_CurrEquity.ForeColor = Class179.color_1;
				this.label_FreeMargin.ForeColor = Class179.color_1;
				this.label_FeeSum.ForeColor = Class179.color_1;
				this.label_UsedMargin.ForeColor = Class179.color_1;
				this.panel_TradingStat.BackColor = Class179.color_9;
			}
		}

		// Token: 0x06001E45 RID: 7749 RVA: 0x0000CA76 File Offset: 0x0000AC76
		private void tabCtrl_Acct_SelectedTabChanging(object sender, TabStripTabChangingEventArgs e)
		{
			this.method_5(e.NewTab);
		}

		// Token: 0x06001E46 RID: 7750 RVA: 0x000CDDA4 File Offset: 0x000CBFA4
		private void method_5(TabItem tabItem_0)
		{
			string name = tabItem_0.Name;
			uint num = Class508.smethod_0(name);
			if (num <= 870461273U)
			{
				if (num != 364281376U)
				{
					if (num != 802314360U)
					{
						if (num == 870461273U)
						{
							if (name == "tabPg_Acct")
							{
								this.method_7();
							}
						}
					}
					else if (name == "tabPg_Report")
					{
						this.method_10();
					}
				}
				else if (name == "tabPg_LngShrtPrft")
				{
					this.method_31();
				}
			}
			else if (num <= 2170067807U)
			{
				if (num != 2059299811U)
				{
					if (num == 2170067807U)
					{
						if (name == "tabPg_EquityGraph")
						{
							this.method_16();
						}
					}
				}
				else if (name == "tabPg_TimePrft")
				{
					this.method_32();
				}
			}
			else if (num != 2758157361U)
			{
				if (num == 3147530452U)
				{
					if (name == "tabPg_PrftLine")
					{
						this.method_21();
					}
				}
			}
			else if (name == "tabPg_SymbPrft")
			{
				this.method_29();
			}
		}

		// Token: 0x06001E47 RID: 7751 RVA: 0x0000CA86 File Offset: 0x0000AC86
		public void method_6()
		{
			this.method_5(this.tabCtrl_Acct.SelectedTab);
		}

		// Token: 0x06001E48 RID: 7752 RVA: 0x000CDEC4 File Offset: 0x000CC0C4
		public void method_7()
		{
			this.method_38(this.label_AcctName, Base.Acct.CurrAccount.AcctName.Trim());
			decimal iniBal = Base.Acct.CurrAccount.IniBal;
			this.method_38(this.label_IniBalance, iniBal.ToString("N"));
			decimal num = Base.Acct.smethod_24();
			this.method_8(this.label_DynaProfit, num);
			if (TApp.SrvParams.TExPkg.Value != TExPackage.SVIP_St)
			{
				this.method_38(this.label_UsedMargin, Base.Trading.smethod_206().ToString("N"));
			}
			else
			{
				this.label_acct_保证金.Visible = false;
				this.label_UsedMargin.Visible = false;
			}
			IQueryable<Transaction> iqueryable_ = Base.Trading.smethod_136();
			decimal num2 = Base.Trading.smethod_177(iqueryable_);
			decimal d = Base.Trading.smethod_181(iqueryable_);
			decimal num3 = iniBal + num2 - d;
			decimal num4 = num3 + num;
			decimal? endingBal = Base.Acct.CurrAccount.EndingBal;
			decimal d2 = num3;
			if (!(endingBal.GetValueOrDefault() == d2 & endingBal != null))
			{
				Base.Acct.CurrAccount.EndingBal = new decimal?(num3);
				Base.Acct.smethod_7();
			}
			this.method_38(this.label_CurrEquity, num4.ToString("N"));
			this.method_38(this.label_FeeSum, d.ToString("N"));
			this.method_38(this.label_FreeMargin, Base.Acct.smethod_21(num3, Base.Acct.CurrAccount.ID, true).ToString("N"));
			decimal decimal_ = num2 - d;
			this.method_8(this.label_Profit, decimal_);
		}

		// Token: 0x06001E49 RID: 7753 RVA: 0x000CE050 File Offset: 0x000CC250
		private void method_8(Control control_0, decimal decimal_0)
		{
			string string_ = decimal_0.ToString("N");
			this.method_38(control_0, string_);
			this.method_9(control_0, string_);
		}

		// Token: 0x06001E4A RID: 7754 RVA: 0x000CE07C File Offset: 0x000CC27C
		private void method_9(Control control_0, string string_0)
		{
			double num = Convert.ToDouble(string_0.Replace("%", "").Replace("‰", "").Replace("$", "").Replace("￥", ""));
			if (num > 0.0)
			{
				this.method_39(control_0, Class179.color_20);
			}
			else if (num < 0.0)
			{
				this.method_39(control_0, Class179.color_24);
			}
			else if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.method_39(control_0, Class179.color_10);
			}
			else
			{
				this.method_39(control_0, Class179.color_1);
			}
		}

		// Token: 0x06001E4B RID: 7755 RVA: 0x0000CA9B File Offset: 0x0000AC9B
		private void method_10()
		{
			this.method_11(true);
		}

		// Token: 0x06001E4C RID: 7756 RVA: 0x000CE128 File Offset: 0x000CC328
		private void method_11(bool bool_0)
		{
			this.method_36();
			IQueryable<Transaction> queryable = this.method_12();
			if (queryable.Any<Transaction>())
			{
				queryable = this.method_14(queryable, bool_0);
				queryable = this.method_15(queryable, bool_0);
				queryable = this.method_13(queryable, bool_0);
			}
			this.method_37(queryable);
		}

		// Token: 0x06001E4D RID: 7757 RVA: 0x000CE170 File Offset: 0x000CC370
		private IQueryable<Transaction> method_12()
		{
			string text = string.Empty;
			if (this.comboBox_tradingOverview_CurrOrAll.Enabled && this.comboBox_tradingOverview_CurrOrAll.Items.Count > 0)
			{
				text = this.comboBox_tradingOverview_CurrOrAll.Text;
			}
			this.comboBox_tradingOverview_CurrOrAll.Items.Clear();
			if (Base.Trading.CurrHisTransList != null && Base.Trading.CurrHisTransList.Any<ShownHisTrans>())
			{
				TEx.Util.ComboBoxItem comboBoxItem = new TEx.Util.ComboBoxItem();
				comboBoxItem.Text = "本轮交易";
				this.comboBox_tradingOverview_CurrOrAll.Items.Add(comboBoxItem);
			}
			TEx.Util.ComboBoxItem comboBoxItem2 = new TEx.Util.ComboBoxItem();
			comboBoxItem2.Text = "所有交易";
			this.comboBox_tradingOverview_CurrOrAll.Items.Add(comboBoxItem2);
			this.comboBox_tradingOverview_CurrOrAll.SelectedIndexChanged -= this.comboBox_tradingOverview_CurrOrAll_SelectedIndexChanged;
			this.comboBox_tradingOverview_CurrOrAll.SelectedIndex = 0;
			if (this.comboBox_tradingOverview_CurrOrAll.Items.Count > 1 && !string.IsNullOrEmpty(text) && text == "所有交易")
			{
				this.comboBox_tradingOverview_CurrOrAll.SelectedIndex = 1;
			}
			this.comboBox_tradingOverview_CurrOrAll.SelectedIndexChanged += this.comboBox_tradingOverview_CurrOrAll_SelectedIndexChanged;
			IQueryable<Transaction> result;
			if (this.comboBox_tradingOverview_CurrOrAll.Text == "本轮交易")
			{
				result = Base.Trading.CurrHisTransList.Cast<Transaction>().AsQueryable<Transaction>();
			}
			else
			{
				result = Base.Trading.smethod_136();
			}
			return result;
		}

		// Token: 0x06001E4E RID: 7758 RVA: 0x000CE2BC File Offset: 0x000CC4BC
		private IQueryable<Transaction> method_13(IQueryable<Transaction> iqueryable_0, bool bool_0)
		{
			int? nullable_0 = null;
			this.comboBox_Symbl.SelectedIndexChanged -= this.comboBox_Symbl_SelectedIndexChanged;
			if (this.comboBox_Symbl.Enabled && this.comboBox_Symbl.Items.Count > 0 && this.comboBox_Symbl.SelectedIndex > 0)
			{
				nullable_0 = new int?((int)(this.comboBox_Symbl.SelectedItem as TEx.Util.ComboBoxItem).Value);
			}
			if (bool_0)
			{
				this.comboBox_Symbl.Items.Clear();
				TEx.Util.ComboBoxItem comboBoxItem = new TEx.Util.ComboBoxItem();
				comboBoxItem.Text = "全部";
				this.comboBox_Symbl.Items.Add(comboBoxItem);
				this.comboBox_Symbl.SelectedItem = comboBoxItem;
				IEnumerable<int> enumerable = (from t in iqueryable_0
				select t.SymbolID).Distinct<int>();
				List<StkSymbol> list = new List<StkSymbol>();
				foreach (int int_ in enumerable)
				{
					StkSymbol stkSymbol = SymbMgr.smethod_4(int_, false);
					if (stkSymbol != null)
					{
						list.Add(stkSymbol);
					}
				}
				list = list.OrderBy(new Func<StkSymbol, string>(TrdAnalysisPanel.<>c.<>9.method_0)).ToList<StkSymbol>();
				foreach (StkSymbol stkSymbol2 in list)
				{
					TEx.Util.ComboBoxItem comboBoxItem2 = new TEx.Util.ComboBoxItem();
					comboBoxItem2.Text = stkSymbol2.CNName + "(" + stkSymbol2.Code + ")";
					comboBoxItem2.Value = stkSymbol2.ID;
					this.comboBox_Symbl.Items.Add(comboBoxItem2);
					if (nullable_0 != null && nullable_0.Value == stkSymbol2.ID)
					{
						this.comboBox_Symbl.SelectedItem = comboBoxItem2;
					}
				}
				if (this.comboBox_Symbl.SelectedItem == comboBoxItem)
				{
					nullable_0 = null;
				}
			}
			this.comboBox_Symbl.SelectedIndexChanged += this.comboBox_Symbl_SelectedIndexChanged;
			if (nullable_0 != null)
			{
				iqueryable_0 = from t in iqueryable_0
				where t.SymbolID == nullable_0.Value
				select t;
			}
			return iqueryable_0;
		}

		// Token: 0x06001E4F RID: 7759 RVA: 0x000CE5CC File Offset: 0x000CC7CC
		private IQueryable<Transaction> method_14(IQueryable<Transaction> iqueryable_0, bool bool_0)
		{
			int? nullable_0 = null;
			this.comboBox_Year.SelectedIndexChanged -= this.comboBox_Year_SelectedIndexChanged;
			if (this.comboBox_Year.Enabled && this.comboBox_Year.Items.Count > 0 && this.comboBox_Year.SelectedIndex > 0)
			{
				nullable_0 = new int?((int)(this.comboBox_Year.SelectedItem as TEx.Util.ComboBoxItem).Value);
			}
			if (bool_0)
			{
				this.comboBox_Year.Items.Clear();
				TEx.Util.ComboBoxItem comboBoxItem = new TEx.Util.ComboBoxItem();
				comboBoxItem.Text = "全部";
				this.comboBox_Year.Items.Add(comboBoxItem);
				this.comboBox_Year.SelectedItem = comboBoxItem;
				foreach (int num in from y in (from t in iqueryable_0
				select t.CreateTime.Year).Distinct<int>()
				orderby y
				select y)
				{
					TEx.Util.ComboBoxItem comboBoxItem2 = new TEx.Util.ComboBoxItem();
					comboBoxItem2.Text = num.ToString();
					comboBoxItem2.Value = num;
					this.comboBox_Year.Items.Add(comboBoxItem2);
					if (nullable_0 != null && nullable_0.Value == num)
					{
						this.comboBox_Year.SelectedItem = comboBoxItem2;
					}
				}
				if (this.comboBox_Year.SelectedItem == comboBoxItem)
				{
					nullable_0 = null;
				}
			}
			this.comboBox_Year.SelectedIndexChanged += this.comboBox_Year_SelectedIndexChanged;
			if (nullable_0 != null)
			{
				iqueryable_0 = from t in iqueryable_0
				where t.CreateTime.Year == nullable_0.Value
				select t;
			}
			return iqueryable_0;
		}

		// Token: 0x06001E50 RID: 7760 RVA: 0x000CE88C File Offset: 0x000CCA8C
		private IQueryable<Transaction> method_15(IQueryable<Transaction> iqueryable_0, bool bool_0)
		{
			int? nullable_0 = null;
			this.comboBox_Month.SelectedIndexChanged -= this.comboBox_Month_SelectedIndexChanged;
			if (this.comboBox_Month.Enabled && this.comboBox_Month.Items.Count > 0 && this.comboBox_Month.SelectedIndex > 0)
			{
				nullable_0 = new int?((int)(this.comboBox_Month.SelectedItem as TEx.Util.ComboBoxItem).Value);
			}
			this.comboBox_Month.Enabled = true;
			if (bool_0)
			{
				this.comboBox_Month.Items.Clear();
				TEx.Util.ComboBoxItem comboBoxItem = new TEx.Util.ComboBoxItem();
				comboBoxItem.Text = "全部";
				this.comboBox_Month.Items.Add(comboBoxItem);
				this.comboBox_Month.SelectedItem = comboBoxItem;
				if (this.comboBox_Year.Items.Count > 2 && this.comboBox_Year.SelectedIndex < 1)
				{
					this.comboBox_Month.Enabled = false;
				}
				else
				{
					foreach (int num in from m in (from t in iqueryable_0
					select t.CreateTime.Month).Distinct<int>()
					orderby m
					select m)
					{
						TEx.Util.ComboBoxItem comboBoxItem2 = new TEx.Util.ComboBoxItem();
						comboBoxItem2.Text = num.ToString("D2");
						comboBoxItem2.Value = num;
						this.comboBox_Month.Items.Add(comboBoxItem2);
						if (nullable_0 != null && nullable_0.Value == num)
						{
							this.comboBox_Month.SelectedItem = comboBoxItem2;
						}
					}
				}
				if (this.comboBox_Month.SelectedItem == comboBoxItem)
				{
					nullable_0 = null;
				}
			}
			this.comboBox_Month.SelectedIndexChanged += this.comboBox_Month_SelectedIndexChanged;
			if (nullable_0 != null)
			{
				iqueryable_0 = from t in iqueryable_0
				where t.CreateTime.Month == nullable_0.Value
				select t;
			}
			return iqueryable_0;
		}

		// Token: 0x06001E51 RID: 7761 RVA: 0x0000CAA6 File Offset: 0x0000ACA6
		private void comboBox_tradingOverview_CurrOrAll_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.method_10();
		}

		// Token: 0x06001E52 RID: 7762 RVA: 0x000CEB90 File Offset: 0x000CCD90
		private void comboBox_Year_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.method_36();
			IQueryable<Transaction> queryable = this.method_12();
			if (queryable.Any<Transaction>())
			{
				queryable = this.method_14(queryable, false);
				queryable = this.method_15(queryable, true);
				queryable = this.method_13(queryable, true);
			}
			this.method_37(queryable);
		}

		// Token: 0x06001E53 RID: 7763 RVA: 0x000CEBD8 File Offset: 0x000CCDD8
		private void comboBox_Month_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.method_36();
			IQueryable<Transaction> queryable = this.method_12();
			if (queryable.Any<Transaction>())
			{
				queryable = this.method_15(queryable, false);
				queryable = this.method_14(queryable, true);
				queryable = this.method_13(queryable, true);
			}
			this.method_37(queryable);
		}

		// Token: 0x06001E54 RID: 7764 RVA: 0x000CEC20 File Offset: 0x000CCE20
		private void comboBox_Symbl_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.method_36();
			IQueryable<Transaction> queryable = this.method_12();
			if (queryable.Any<Transaction>())
			{
				queryable = this.method_13(queryable, false);
				queryable = this.method_14(queryable, true);
				queryable = this.method_15(queryable, true);
			}
			this.method_37(queryable);
		}

		// Token: 0x06001E55 RID: 7765 RVA: 0x000CEC68 File Offset: 0x000CCE68
		private void method_16()
		{
			this.tabControlPanel_EquityGraph.Controls.Clear();
			GraphCtrlStat graphCtrlStat = new GraphCtrlStat(false);
			GraphPane graphPane = graphCtrlStat.GraphPane;
			graphPane.Title.Text = "资金权益走势图";
			graphPane.XAxis.Title.Text = "交易笔数";
			graphPane.XAxis.Scale.MinorStep = 1.0;
			graphPane.XAxis.Scale.MinGrace = 1.0;
			graphPane.YAxis.Title.Text = "资金权益";
			graphPane.XAxis.MinorTic.Size = 0f;
			graphPane.XAxis.MajorTic.Size = 0f;
			graphPane.XAxis.Scale.MagAuto = false;
			graphPane.YAxis.Scale.MagAuto = false;
			graphPane.YAxis.Scale.Mag = 3;
			graphPane.Legend.FontSpec.Family = "Microsoft Sans Serif";
			graphPane.Title.FontSpec.Family = "Microsoft Sans Serif";
			List<Transaction> list = (from t in Base.Trading.smethod_136()
			where t.TransType == 2 || t.TransType == 4
			select t).ToList<Transaction>();
			int num = list.Count<Transaction>();
			if (num == 0)
			{
				graphPane.XAxis.Scale.IsVisible = false;
			}
			else if (num > 10)
			{
				graphPane.XAxis.Scale.MajorStepAuto = true;
				graphPane.XAxis.Scale.IsVisible = true;
			}
			else
			{
				graphPane.XAxis.Scale.MajorStep = 1.0;
			}
			this.method_17(graphCtrlStat, list);
			this.tabControlPanel_EquityGraph.Controls.Add(graphCtrlStat);
		}

		// Token: 0x06001E56 RID: 7766 RVA: 0x000CEE9C File Offset: 0x000CD09C
		private void method_17(GraphCtrlStat graphCtrlStat_1, List<Transaction> list_1)
		{
			graphCtrlStat_1.GraphPane.CurveList.Clear();
			graphCtrlStat_1.Tag = list_1;
			decimal iniBal = Base.Acct.CurrAccount.IniBal;
			LineItem lineItem = this.method_19(graphCtrlStat_1, list_1, "", null, Base.Acct.CurrAccount.IniBal);
			if (lineItem != null && lineItem.NPts > 0)
			{
				PointPair pointPair = lineItem.Points[lineItem.NPts - 1];
				double num = Convert.ToDouble(Base.Acct.smethod_42());
				if (pointPair.Y != num)
				{
					pointPair.Y = num;
				}
			}
			graphCtrlStat_1.method_1(new double[]
			{
				Convert.ToDouble(iniBal)
			});
			graphCtrlStat_1.AxisChange();
			graphCtrlStat_1.Refresh();
			graphCtrlStat_1.PointValueEvent += this.method_20;
		}

		// Token: 0x06001E57 RID: 7767 RVA: 0x000CEF54 File Offset: 0x000CD154
		private Color method_18(int int_1)
		{
			Color result;
			if (int_1 < this.color_0.Length)
			{
				result = this.color_0[int_1];
			}
			else
			{
				result = this.color_0[int_1 % (this.color_0.Length - 1)];
			}
			return result;
		}

		// Token: 0x06001E58 RID: 7768 RVA: 0x000CEF98 File Offset: 0x000CD198
		private LineItem method_19(ZedGraphControl zedGraphControl_0, List<Transaction> list_1, string string_0, TradingSymbol tradingSymbol_0, decimal decimal_0)
		{
			TrdAnalysisPanel.Class352 @class = new TrdAnalysisPanel.Class352();
			@class.tradingSymbol_0 = tradingSymbol_0;
			LineItem lineItem = null;
			if (list_1 == null)
			{
				list_1 = (zedGraphControl_0.Tag as List<Transaction>).Where(new Func<Transaction, bool>(@class.method_0)).ToList<Transaction>();
			}
			if (list_1 != null && list_1.Any<Transaction>())
			{
				PointPairList pointPairList = new PointPairList();
				Color color = this.method_18(zedGraphControl_0.GraphPane.CurveList.Count);
				lineItem = zedGraphControl_0.GraphPane.AddCurve(string_0, pointPairList, color, SymbolType.None);
				int num = 0;
				double num2 = Convert.ToDouble(decimal_0);
				PointPair point = new PointPair(0.0, num2);
				pointPairList.Add(point);
				foreach (Transaction transaction in list_1)
				{
					num++;
					int value = 2;
					if (@class.tradingSymbol_0 == null)
					{
						@class.tradingSymbol_0 = SymbMgr.smethod_3(transaction.SymbolID);
					}
					if (@class.tradingSymbol_0 != null && @class.tradingSymbol_0.IsOneSideFee != null && @class.tradingSymbol_0.IsOneSideFee.Value)
					{
						value = 1;
					}
					num2 += Convert.ToDouble(transaction.Profit) - Convert.ToDouble(transaction.Fee * value);
					pointPairList.Add(new PointPair(Convert.ToDouble(num), num2)
					{
						Tag = transaction
					});
				}
				lineItem.Tag = @class.tradingSymbol_0;
			}
			return lineItem;
		}

		// Token: 0x06001E59 RID: 7769 RVA: 0x000CF16C File Offset: 0x000CD36C
		private string method_20(ZedGraphControl zedGraphControl_0, GraphPane graphPane_0, CurveItem curveItem_0, int int_1)
		{
			PointPair pointPair = curveItem_0.Points[int_1];
			Transaction transaction = pointPair.Tag as Transaction;
			string result;
			try
			{
				result = string.Concat(new object[]
				{
					"权益：",
					Convert.ToInt32(Math.Round(pointPair.Y)),
					Environment.NewLine,
					(pointPair.Tag == null) ? string.Empty : ("时间：" + string.Format("{0:g}", Convert.ToDateTime(transaction.CreateTime)) + Environment.NewLine),
					"序号：",
					Convert.ToInt32(pointPair.X)
				});
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
				result = string.Empty;
			}
			return result;
		}

		// Token: 0x06001E5A RID: 7770 RVA: 0x000CF244 File Offset: 0x000CD444
		private void method_21()
		{
			this.tabControlPanel_PrftLine.Controls.Clear();
			this.graphCtrlStat_0 = new GraphCtrlStat(false);
			GraphPane graphPane = this.graphCtrlStat_0.GraphPane;
			graphPane.Title.Text = "品种盈利曲线图";
			graphPane.XAxis.Title.Text = "交易笔数";
			graphPane.XAxis.Scale.MinorStep = 1.0;
			graphPane.XAxis.Scale.MinGrace = 1.0;
			graphPane.YAxis.Title.Text = "盈利";
			graphPane.XAxis.MinorTic.Size = 0f;
			graphPane.XAxis.MajorTic.Size = 0f;
			graphPane.Legend.FontSpec.Family = "Microsoft Sans Serif";
			graphPane.Legend.Border.IsVisible = false;
			graphPane.Title.FontSpec.Family = "Microsoft Sans Serif";
			List<Transaction> list = (from t in Base.Trading.smethod_136()
			where t.TransType == 2 || t.TransType == 4
			select t).ToList<Transaction>();
			int num = list.Count<Transaction>();
			if (num == 0)
			{
				graphPane.XAxis.Scale.IsVisible = false;
			}
			else if (num > 10)
			{
				graphPane.XAxis.Scale.MajorStepAuto = true;
				graphPane.XAxis.Scale.IsVisible = true;
			}
			else
			{
				graphPane.XAxis.Scale.MajorStep = 1.0;
			}
			this.method_22(this.graphCtrlStat_0, list);
			this.tabControlPanel_PrftLine.Controls.Add(this.graphCtrlStat_0);
		}

		// Token: 0x06001E5B RID: 7771 RVA: 0x0000CAB0 File Offset: 0x0000ACB0
		private void method_22(ZedGraphControl zedGraphControl_0, List<Transaction> list_1)
		{
			zedGraphControl_0.Tag = list_1;
			this.method_24();
			zedGraphControl_0.ContextMenuBuilder += this.method_23;
			zedGraphControl_0.PointValueEvent += this.method_28;
		}

		// Token: 0x06001E5C RID: 7772 RVA: 0x000CF468 File Offset: 0x000CD668
		private void method_23(ZedGraphControl zedGraphControl_0, ContextMenuStrip contextMenuStrip_0, Point point_0, ZedGraphControl.ContextMenuObjectState contextMenuObjectState_0)
		{
			ToolStripItem[] array = new ToolStripItem[contextMenuStrip_0.Items.Count];
			contextMenuStrip_0.Items.CopyTo(array, 0);
			contextMenuStrip_0.Items.Clear();
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "品种选择";
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = "选择全部";
			toolStripMenuItem2.Click += this.method_25;
			if (this.list_0 == null)
			{
				toolStripMenuItem2.Checked = true;
			}
			toolStripMenuItem.DropDownItems.Add(toolStripMenuItem2);
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = "取消全部";
			toolStripMenuItem3.Click += this.method_26;
			if (this.list_0 != null && !this.list_0.Any<TradingSymbol>())
			{
				toolStripMenuItem3.Checked = true;
			}
			toolStripMenuItem.DropDownItems.Add(toolStripMenuItem3);
			toolStripMenuItem.DropDownItems.Add(new ToolStripSeparator());
			List<Transaction> list = zedGraphControl_0.Tag as List<Transaction>;
			if (list != null && list.Any<Transaction>())
			{
				foreach (TradingSymbol tradingSymbol in list.GroupBy(new Func<Transaction, TradingSymbol>(TrdAnalysisPanel.<>c.<>9.method_1)).Select(new Func<IGrouping<TradingSymbol, Transaction>, TradingSymbol>(TrdAnalysisPanel.<>c.<>9.method_2)).OrderBy(new Func<TradingSymbol, string>(TrdAnalysisPanel.<>c.<>9.method_3)))
				{
					ToolStripMenuItem toolStripMenuItem4 = Base.UI.smethod_76();
					toolStripMenuItem4.Text = tradingSymbol.AbbrCode + "(" + tradingSymbol.AbbrCNName + ")";
					toolStripMenuItem4.Tag = tradingSymbol;
					if (this.list_0 == null || this.list_0.Contains(tradingSymbol))
					{
						toolStripMenuItem4.Checked = true;
					}
					toolStripMenuItem4.Click += this.method_27;
					toolStripMenuItem.DropDownItems.Add(toolStripMenuItem4);
				}
			}
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			contextMenuStrip_0.Items.Add(new ToolStripSeparator());
			ToolStripMenuItem toolStripMenuItem5 = Base.UI.smethod_76();
			toolStripMenuItem5.Text = "图表选项";
			toolStripMenuItem5.DropDownItems.AddRange(array);
			contextMenuStrip_0.Items.Add(toolStripMenuItem5);
		}

		// Token: 0x06001E5D RID: 7773 RVA: 0x000CF6D8 File Offset: 0x000CD8D8
		private void method_24()
		{
			GraphCtrlStat graphCtrlStat = this.graphCtrlStat_0;
			List<Transaction> list = this.graphCtrlStat_0.Tag as List<Transaction>;
			list.RemoveAll(new Predicate<Transaction>(TrdAnalysisPanel.<>c.<>9.method_4));
			graphCtrlStat.GraphPane.CurveList.Clear();
			if (list != null && list.Any<Transaction>())
			{
				foreach (var <>f__AnonymousType in list.GroupBy(new Func<Transaction, TradingSymbol>(TrdAnalysisPanel.<>c.<>9.method_5)).Select(new Func<IGrouping<TradingSymbol, Transaction>, <>f__AnonymousType20<TradingSymbol, List<Transaction>>>(TrdAnalysisPanel.<>c.<>9.method_6)).Where(new Func<<>f__AnonymousType20<TradingSymbol, List<Transaction>>, bool>(TrdAnalysisPanel.<>c.<>9.method_8)).OrderBy(new Func<<>f__AnonymousType20<TradingSymbol, List<Transaction>>, string>(TrdAnalysisPanel.<>c.<>9.method_9)).Take(36))
				{
					this.method_19(graphCtrlStat, <>f__AnonymousType.Trans, <>f__AnonymousType.Symbol.AbbrCNName, <>f__AnonymousType.Symbol, 0m);
				}
				GraphPane graphPane = graphCtrlStat.GraphPane;
				if (graphPane.CurveList != null && graphPane.CurveList.Any<CurveItem>())
				{
					double num = graphPane.CurveList.Max(new Func<CurveItem, double>(TrdAnalysisPanel.<>c.<>9.method_10));
					double num2 = graphPane.CurveList.Min(new Func<CurveItem, double>(TrdAnalysisPanel.<>c.<>9.method_12));
					double[] double_ = new double[]
					{
						num,
						num2
					};
					graphCtrlStat.method_1(double_);
				}
			}
			graphCtrlStat.AxisChange();
			graphCtrlStat.Refresh();
		}

		// Token: 0x06001E5E RID: 7774 RVA: 0x0000CAE5 File Offset: 0x0000ACE5
		private void method_25(object sender, EventArgs e)
		{
			this.method_24();
			this.list_0 = null;
		}

		// Token: 0x06001E5F RID: 7775 RVA: 0x0000CAF6 File Offset: 0x0000ACF6
		private void method_26(object sender, EventArgs e)
		{
			this.graphCtrlStat_0.GraphPane.CurveList.Clear();
			this.graphCtrlStat_0.AxisChange();
			this.graphCtrlStat_0.Refresh();
			this.list_0 = new List<TradingSymbol>();
		}

		// Token: 0x06001E60 RID: 7776 RVA: 0x000CF8D4 File Offset: 0x000CDAD4
		private void method_27(object sender, EventArgs e)
		{
			TrdAnalysisPanel.Class353 @class = new TrdAnalysisPanel.Class353();
			ToolStripMenuItem toolStripMenuItem = sender as ToolStripMenuItem;
			@class.tradingSymbol_0 = (toolStripMenuItem.Tag as TradingSymbol);
			if (toolStripMenuItem.Tag != null && !toolStripMenuItem.Checked)
			{
				if (this.list_0 == null)
				{
					this.list_0 = new List<TradingSymbol>();
				}
				this.method_19(this.graphCtrlStat_0, null, @class.tradingSymbol_0.AbbrCNName, @class.tradingSymbol_0, 0m);
				if (!this.list_0.Contains(@class.tradingSymbol_0))
				{
					this.list_0.Add(@class.tradingSymbol_0);
				}
			}
			else if (this.list_0 == null)
			{
				this.list_0 = new List<TradingSymbol>();
				List<Transaction> list = this.graphCtrlStat_0.Tag as List<Transaction>;
				if (list != null && list.Any<Transaction>())
				{
					foreach (TradingSymbol tradingSymbol in list.GroupBy(new Func<Transaction, TradingSymbol>(TrdAnalysisPanel.<>c.<>9.method_14)).Select(new Func<IGrouping<TradingSymbol, Transaction>, TradingSymbol>(TrdAnalysisPanel.<>c.<>9.method_15)))
					{
						if (tradingSymbol != @class.tradingSymbol_0)
						{
							this.list_0.Add(tradingSymbol);
						}
					}
					this.graphCtrlStat_0.GraphPane.CurveList.RemoveAll(new Predicate<CurveItem>(@class.method_0));
				}
			}
			else
			{
				this.list_0.Remove(@class.tradingSymbol_0);
				this.graphCtrlStat_0.GraphPane.CurveList.RemoveAll(new Predicate<CurveItem>(@class.method_1));
			}
			this.graphCtrlStat_0.AxisChange();
			this.graphCtrlStat_0.Refresh();
		}

		// Token: 0x06001E61 RID: 7777 RVA: 0x000CFAB0 File Offset: 0x000CDCB0
		private string method_28(ZedGraphControl zedGraphControl_0, GraphPane graphPane_0, CurveItem curveItem_0, int int_1)
		{
			PointPair pointPair = curveItem_0.Points[int_1];
			TradingSymbol tradingSymbol = curveItem_0.Tag as TradingSymbol;
			Transaction transaction = pointPair.Tag as Transaction;
			string result;
			if (transaction != null)
			{
				DateTime dateTime = Convert.ToDateTime(transaction.CreateTime);
				result = string.Concat(new object[]
				{
					"交易品种：",
					tradingSymbol.AbbrCNName,
					"(",
					tradingSymbol.AbbrCode,
					")",
					Environment.NewLine,
					"累计盈亏：",
					Convert.ToInt64(Math.Round(pointPair.Y)),
					Environment.NewLine,
					"平仓手数：",
					transaction.Units,
					Environment.NewLine,
					(pointPair.Tag == null) ? string.Empty : ("平仓时间：" + string.Format("{0:g}", dateTime))
				});
			}
			else
			{
				Class182.smethod_0(new Exception("trans is null!"));
				result = string.Empty;
			}
			return result;
		}

		// Token: 0x06001E62 RID: 7778 RVA: 0x000CFBC8 File Offset: 0x000CDDC8
		private void method_29()
		{
			this.tabControlPanel_SymbPrft.Controls.Clear();
			GraphCtrlStat graphCtrlStat = new GraphCtrlStat(false);
			GraphPane graphPane = graphCtrlStat.GraphPane;
			graphPane.Title.Text = "品种盈亏分析图";
			graphPane.XAxis.Scale.IsVisible = false;
			graphPane.XAxis.MajorGrid.IsVisible = false;
			graphPane.XAxis.MinorGrid.IsVisible = false;
			graphPane.XAxis.MajorTic.Size = 0f;
			graphPane.XAxis.Title.Text = "品种";
			graphPane.YAxis.Title.Text = "盈亏";
			graphPane.Legend.Border.IsVisible = false;
			graphPane.Legend.FontSpec.Family = "Microsoft Sans Serif";
			graphPane.Title.FontSpec.Family = "Microsoft Sans Serif";
			List<SymbolProfit> list = Base.Trading.smethod_214(new int?(28));
			if (list.Any<SymbolProfit>())
			{
				Color[] array = new Color[list.Count];
				for (int i = 0; i < array.Length; i++)
				{
					array[i] = this.method_18(i);
				}
				for (int j = 0; j < list.Count; j++)
				{
					PointPairList pointPairList = new PointPairList();
					pointPairList.Add(Convert.ToDouble(j + 1), Convert.ToDouble(list[j].decimal_0));
					TradingSymbol tradingSymbol_ = list[j].tradingSymbol_0;
					if (tradingSymbol_ != null)
					{
						Color color = array[j];
						BarItem barItem = graphPane.AddBar(tradingSymbol_.AbbrCNName, pointPairList, color);
						barItem.Bar.Fill = new Fill(color, Color.White, color);
						barItem.Tag = tradingSymbol_.AbbrCNName;
					}
				}
				Base.UI.smethod_38(graphPane, list.Count);
				double num = Convert.ToDouble(list.Max(new Func<SymbolProfit, decimal>(TrdAnalysisPanel.<>c.<>9.method_16)));
				double num2 = Convert.ToDouble(list.Min(new Func<SymbolProfit, decimal>(TrdAnalysisPanel.<>c.<>9.method_17)));
				double[] double_ = new double[]
				{
					num,
					num2
				};
				graphCtrlStat.method_1(double_);
				BarItem.CreateBarLabels(graphPane, false, "f0", "Arial", 13f, Color.Black, true, false, false);
				graphCtrlStat.PointValueEvent += new ZedGraphControl.PointValueHandler(this.method_30);
				graphCtrlStat.AxisChange();
				graphPane.YAxis.Scale.Max += graphPane.YAxis.Scale.MajorStep;
				if (graphPane.YAxis.Scale.Min < 0.0)
				{
					graphPane.YAxis.Scale.Min -= graphPane.YAxis.Scale.MajorStep;
				}
			}
			else
			{
				graphPane.YAxis.Scale.IsVisible = false;
			}
			this.tabControlPanel_SymbPrft.Controls.Add(graphCtrlStat);
		}

		// Token: 0x06001E63 RID: 7779 RVA: 0x000CFED0 File Offset: 0x000CE0D0
		private string method_30(object object_0, GraphPane graphPane_0, CurveItem curveItem_0, int int_1)
		{
			PointPair pointPair = curveItem_0[int_1];
			string text = curveItem_0.Tag.ToString();
			return string.Concat(new string[]
			{
				"盈利：",
				pointPair.Y.ToString(),
				Environment.NewLine,
				"品种：",
				text
			});
		}

		// Token: 0x06001E64 RID: 7780 RVA: 0x000CFF2C File Offset: 0x000CE12C
		private void method_31()
		{
			this.splitContainer_LngShrtPrft.Panel1.Controls.Clear();
			this.splitContainer_LngShrtPrft.Panel2.Controls.Clear();
			GraphCtrlStat graphCtrlStat = new GraphCtrlStat(false);
			GraphCtrlStat graphCtrlStat2 = new GraphCtrlStat(false);
			graphCtrlStat.IsEnableZoom = false;
			graphCtrlStat2.IsEnableZoom = false;
			IQueryable<Transaction> queryable = Base.Trading.smethod_136();
			decimal num = Base.Trading.smethod_179(queryable);
			decimal num2 = Base.Trading.smethod_180(queryable);
			decimal? num3 = (from t in queryable
			where t.Profit > (decimal?)((decimal)0) && t.TransType == 2
			select t).Sum((Transaction t) => t.Profit);
			if (num3 == null)
			{
				num3 = new decimal?(0m);
			}
			decimal? num4 = (from t in queryable
			where t.Profit < (decimal?)((decimal)0) && t.TransType == 2
			select t).Sum((Transaction t) => t.Profit);
			if (num4 == null)
			{
				num4 = new decimal?(0m);
			}
			double num5;
			if (num != 0m)
			{
				num5 = Convert.ToDouble(num3 / num);
			}
			else
			{
				num5 = 0.0;
			}
			double num6;
			if (num2 != 0m)
			{
				num6 = Convert.ToDouble(num4 / num2);
			}
			else
			{
				num6 = 0.0;
			}
			GraphPane graphPane = graphCtrlStat.GraphPane;
			graphPane.Title.Text = "盈利结构分析";
			graphPane.Title.FontSpec.Size = 22f;
			graphPane.Legend.Border.IsVisible = false;
			graphPane.Legend.FontSpec.Size = 16f;
			graphPane.Legend.Position = LegendPos.InsideBotRight;
			PieItem pieItem = graphPane.AddPieSlice(num5, Color.Navy, Color.White, 45f, 0.0, "多单盈利");
			PieItem pieItem2 = graphPane.AddPieSlice(1.0 - num5, Color.Purple, Color.White, 45f, 0.0, "空单盈利");
			pieItem.LabelType = PieLabelType.Name_Percent;
			pieItem2.LabelType = PieLabelType.Name_Percent;
			pieItem.LabelDetail.FontSpec.Size = 16f;
			pieItem2.LabelDetail.FontSpec.Size = 16f;
			pieItem.LabelDetail.FontSpec.Border.IsVisible = false;
			pieItem2.LabelDetail.FontSpec.Border.IsVisible = false;
			graphCtrlStat.Dock = DockStyle.Fill;
			graphCtrlStat.AxisChange();
			this.splitContainer_LngShrtPrft.Panel1.Controls.Add(graphCtrlStat);
			GraphPane graphPane2 = graphCtrlStat2.GraphPane;
			graphPane2.Title.Text = "亏损结构分析";
			graphPane2.Title.FontSpec.Size = 22f;
			graphPane2.Legend.Border.IsVisible = false;
			graphPane2.Legend.FontSpec.Size = 16f;
			graphPane2.Legend.Position = LegendPos.InsideBotRight;
			graphPane2.Legend.FontSpec.Family = "Microsoft Sans Serif";
			graphPane2.Title.FontSpec.Family = "Microsoft Sans Serif";
			PieItem pieItem3 = graphPane2.AddPieSlice(num6, Color.Navy, Color.White, 45f, 0.0, "多单亏损");
			PieItem pieItem4 = graphPane2.AddPieSlice(1.0 - num6, Color.Purple, Color.White, 45f, 0.0, "空单亏损");
			pieItem3.LabelType = PieLabelType.Name_Percent;
			pieItem4.LabelType = PieLabelType.Name_Percent;
			pieItem3.LabelDetail.FontSpec.Size = 16f;
			pieItem4.LabelDetail.FontSpec.Size = 16f;
			pieItem3.LabelDetail.FontSpec.Border.IsVisible = false;
			pieItem4.LabelDetail.FontSpec.Border.IsVisible = false;
			graphCtrlStat2.Dock = DockStyle.Fill;
			graphCtrlStat2.AxisChange();
			this.splitContainer_LngShrtPrft.Panel2.Controls.Add(graphCtrlStat2);
		}

		// Token: 0x06001E65 RID: 7781 RVA: 0x000D0518 File Offset: 0x000CE718
		private void method_32()
		{
			this.tabControlPanel_TimePrft.Controls.Clear();
			ZedGraphControl zedGraphControl = new GraphCtrlStat(false);
			GraphPane graphPane = zedGraphControl.GraphPane;
			graphPane.Title.Text = "持仓时间盈亏分布图";
			graphPane.XAxis.Title.Text = "持仓时间";
			graphPane.YAxis.Title.Text = "盈利";
			graphPane.YAxis.MajorGrid.IsZeroLine = false;
			graphPane.XAxis.Scale.MagAuto = false;
			graphPane.YAxis.Scale.Mag = 3;
			TimeSpan t;
			List<Transaction> source = (from t in Base.Trading.smethod_136()
			where t.TransType == 2 || t.TransType == 4
			select t).ToList<Transaction>();
			if (source.Any<Transaction>())
			{
				List<Transaction> list = source.Where(new Func<Transaction, bool>(TrdAnalysisPanel.<>c.<>9.method_18)).ToList<Transaction>();
				int count = list.Count;
				if (count <= 0)
				{
					this.method_33(zedGraphControl);
				}
				else
				{
					List<TimeSpan> list2 = new List<TimeSpan>();
					double[] array = new double[count];
					double[] array2 = new double[count];
					double[] array3 = new double[count];
					for (int i = 0; i < count; i++)
					{
						TimeSpan? timeSpan = Base.Trading.smethod_150(list[i]);
						if (timeSpan != null)
						{
							list2.Add(timeSpan.Value);
							array2[i] = Convert.ToDouble(list[i].Profit);
							array3[i] = timeSpan.Value.TotalSeconds;
						}
					}
					if (!list2.Any<TimeSpan>())
					{
						this.method_33(zedGraphControl);
					}
					else
					{
						t = list2.Max<TimeSpan>();
						TimeSpan timeSpan2 = list2.Min<TimeSpan>();
						TimeSpan timeSpan3 = new TimeSpan(0, 5, 0);
						TimeSpan timeSpan4 = new TimeSpan(0, 15, 0);
						TimeSpan timeSpan5 = new TimeSpan(1, 0, 0);
						TimeSpan timeSpan6 = new TimeSpan(1, 0, 0, 0);
						TimeSpan timeSpan7 = new TimeSpan(7, 0, 0, 0);
						TimeSpan timeSpan8 = new TimeSpan(30, 0, 0, 0);
						TimeSpan timeSpan9 = new TimeSpan(365, 0, 0, 0);
						List<TimeSpan> list3 = new List<TimeSpan>();
						list3.Add(timeSpan3);
						list3.Add(timeSpan4);
						list3.Add(timeSpan5);
						list3.Add(timeSpan6);
						list3.Add(timeSpan7);
						list3.Add(timeSpan8);
						list3.Add(timeSpan9);
						TimeSpan timeSpan10;
						if (timeSpan2.TotalMinutes < 5.0)
						{
							timeSpan10 = timeSpan3;
						}
						else if (timeSpan2.TotalMinutes < 15.0)
						{
							timeSpan10 = timeSpan4;
						}
						else if (timeSpan2.TotalHours < 1.0)
						{
							timeSpan10 = timeSpan5;
						}
						else if (timeSpan2.TotalHours < 24.0)
						{
							timeSpan10 = timeSpan6;
						}
						else if (timeSpan2.TotalDays < 7.0)
						{
							timeSpan10 = timeSpan7;
						}
						else if (timeSpan2.TotalDays < 30.0)
						{
							timeSpan10 = timeSpan8;
						}
						else
						{
							timeSpan10 = timeSpan9;
						}
						TimeSpan timeSpan11;
						if (t > timeSpan9)
						{
							this.int_0 = Convert.ToInt32(Math.Floor(t.TotalDays / 365.0)) + 1;
							timeSpan11 = new TimeSpan(this.int_0 * 365, 0, 0, 0);
						}
						else if (t > timeSpan8)
						{
							timeSpan11 = timeSpan9;
						}
						else if (t > timeSpan7)
						{
							timeSpan11 = timeSpan8;
						}
						else if (t > timeSpan6)
						{
							timeSpan11 = timeSpan7;
						}
						else if (t > timeSpan5)
						{
							timeSpan11 = timeSpan6;
						}
						else if (t > timeSpan4)
						{
							timeSpan11 = timeSpan5;
						}
						else if (t > timeSpan3)
						{
							timeSpan11 = timeSpan4;
						}
						else
						{
							timeSpan11 = timeSpan3;
						}
						int num;
						if (t > timeSpan9)
						{
							num = 7;
						}
						else
						{
							num = list3.IndexOf(timeSpan11);
						}
						int num2 = num - list3.IndexOf(timeSpan10) + 1;
						double totalSeconds = timeSpan10.TotalSeconds;
						for (int j = 0; j < list2.Count; j++)
						{
							TimeSpan t2 = list2[j];
							if (t2 <= timeSpan10)
							{
								array[j] = t2.TotalSeconds;
							}
							else if (t2 > timeSpan9)
							{
								array[j] = (t2 - timeSpan9).TotalSeconds / (timeSpan11 - timeSpan9).TotalSeconds * totalSeconds + (double)(list3.IndexOf(timeSpan9) + 1 - list3.IndexOf(timeSpan10)) * totalSeconds;
							}
							else if (t2 > timeSpan8)
							{
								array[j] = (t2 - timeSpan8).TotalSeconds / (timeSpan9 - timeSpan8).TotalSeconds * totalSeconds + (double)(list3.IndexOf(timeSpan9) - list3.IndexOf(timeSpan10)) * totalSeconds;
							}
							else if (t2 > timeSpan7)
							{
								array[j] = (t2 - timeSpan7).TotalSeconds / (timeSpan8 - timeSpan7).TotalSeconds * totalSeconds + (double)(list3.IndexOf(timeSpan8) - list3.IndexOf(timeSpan10)) * totalSeconds;
							}
							else if (t2 > timeSpan6)
							{
								array[j] = (t2 - timeSpan6).TotalSeconds / (timeSpan7 - timeSpan6).TotalSeconds * totalSeconds + (double)(list3.IndexOf(timeSpan7) - list3.IndexOf(timeSpan10)) * totalSeconds;
							}
							else if (t2 > timeSpan5)
							{
								array[j] = (t2 - timeSpan5).TotalSeconds / (timeSpan6 - timeSpan5).TotalSeconds * totalSeconds + (double)(list3.IndexOf(timeSpan6) - list3.IndexOf(timeSpan10)) * totalSeconds;
							}
							else if (t2 > timeSpan4)
							{
								array[j] = (t2 - timeSpan4).TotalSeconds / (timeSpan5 - timeSpan4).TotalSeconds * totalSeconds + (double)(list3.IndexOf(timeSpan5) - list3.IndexOf(timeSpan10)) * totalSeconds;
							}
							else if (t2 > timeSpan3)
							{
								array[j] = (t2 - timeSpan3).TotalSeconds / (timeSpan4 - timeSpan3).TotalSeconds * totalSeconds + (double)(list3.IndexOf(timeSpan4) - list3.IndexOf(timeSpan10)) * totalSeconds;
							}
						}
						PointPairList points = new PointPairList(array, array2, array3);
						LineItem lineItem = graphPane.AddCurve("", points, Color.Red, SymbolType.Diamond);
						lineItem.Symbol.Size = 12f;
						lineItem.Symbol.Fill = new Fill(Color.Green, Color.Red);
						lineItem.Symbol.Border.IsVisible = false;
						lineItem.Line.IsVisible = false;
						lineItem.Symbol.Fill.Type = FillType.GradientByY;
						lineItem.Symbol.Fill.RangeMin = -1.0;
						lineItem.Symbol.Fill.RangeMax = 1.0;
						graphPane.Legend.IsVisible = false;
						graphPane.XAxis.MajorGrid.IsVisible = true;
						graphPane.XAxis.Scale.Max = (double)num2 * totalSeconds;
						graphPane.XAxis.ScaleFormatEvent += this.method_34;
						graphPane.XAxis.Scale.MajorStep = totalSeconds;
						zedGraphControl.PointValueEvent += new ZedGraphControl.PointValueHandler(this.method_35);
						zedGraphControl.AxisChange();
						this.tabControlPanel_TimePrft.Controls.Add(zedGraphControl);
					}
				}
			}
			else
			{
				this.method_33(zedGraphControl);
			}
		}

		// Token: 0x06001E66 RID: 7782 RVA: 0x0000CB30 File Offset: 0x0000AD30
		private void method_33(ZedGraphControl zedGraphControl_0)
		{
			GraphPane graphPane = zedGraphControl_0.GraphPane;
			graphPane.XAxis.Scale.IsVisible = false;
			graphPane.YAxis.Scale.IsVisible = false;
			this.tabControlPanel_TimePrft.Controls.Add(zedGraphControl_0);
		}

		// Token: 0x06001E67 RID: 7783 RVA: 0x000D0D24 File Offset: 0x000CEF24
		private string method_34(GraphPane graphPane_0, Axis axis_0, double double_0, int int_1)
		{
			switch (int_1)
			{
			case 0:
				return "0";
			case 1:
				if (double_0 == 300.0)
				{
					return "5分钟";
				}
				if (double_0 == 900.0)
				{
					return "15分钟";
				}
				if (double_0 == 3600.0)
				{
					return "1小时";
				}
				if (double_0 == 86400.0)
				{
					return "1天";
				}
				if (double_0 == 604800.0)
				{
					return "1周";
				}
				if (double_0 == 2592000.0)
				{
					return "1月";
				}
				if (double_0 == 31536000.0)
				{
					return "1年";
				}
				if (this.int_0 > 0)
				{
					return this.int_0.ToString() + "年";
				}
				break;
			case 2:
				if (double_0 / (double)int_1 == 300.0)
				{
					return "15分钟";
				}
				if (double_0 / (double)int_1 == 900.0)
				{
					return "1小时";
				}
				if (double_0 / (double)int_1 == 3600.0)
				{
					return "1天";
				}
				if (double_0 / (double)int_1 == 86400.0)
				{
					return "1周";
				}
				if (double_0 / (double)int_1 == 604800.0)
				{
					return "1月";
				}
				if (double_0 / (double)int_1 == 2592000.0)
				{
					return "1年";
				}
				if (double_0 / (double)int_1 == 31536000.0 && this.int_0 > 0)
				{
					return this.int_0.ToString() + "年";
				}
				break;
			case 3:
				if (double_0 / (double)int_1 == 300.0)
				{
					return "1小时";
				}
				if (double_0 / (double)int_1 == 900.0)
				{
					return "1天";
				}
				if (double_0 / (double)int_1 == 3600.0)
				{
					return "1周";
				}
				if (double_0 / (double)int_1 == 86400.0)
				{
					return "1月";
				}
				if (double_0 / (double)int_1 == 604800.0)
				{
					return "1年";
				}
				if (double_0 / (double)int_1 == 2592000.0 && this.int_0 > 0)
				{
					return this.int_0.ToString() + "年";
				}
				break;
			case 4:
				if (double_0 / (double)int_1 == 300.0)
				{
					return "1天";
				}
				if (double_0 / (double)int_1 == 900.0)
				{
					return "1周";
				}
				if (double_0 / (double)int_1 == 3600.0)
				{
					return "1月";
				}
				if (double_0 / (double)int_1 == 86400.0)
				{
					return "1年";
				}
				if (double_0 / (double)int_1 == 604800.0 && this.int_0 > 0)
				{
					return this.int_0.ToString() + "年";
				}
				break;
			case 5:
				if (double_0 / (double)int_1 == 300.0)
				{
					return "1周";
				}
				if (double_0 / (double)int_1 == 900.0)
				{
					return "1月";
				}
				if (double_0 / (double)int_1 == 3600.0)
				{
					return "1年";
				}
				if (double_0 / (double)int_1 == 86400.0 && this.int_0 > 0)
				{
					return this.int_0.ToString() + "年";
				}
				break;
			case 6:
				if (double_0 / (double)int_1 == 300.0)
				{
					return "1月";
				}
				if (double_0 / (double)int_1 == 900.0)
				{
					return "1年";
				}
				if (double_0 / (double)int_1 == 3600.0 && this.int_0 > 0)
				{
					return this.int_0.ToString() + "年";
				}
				break;
			case 7:
				if (double_0 / (double)int_1 == 300.0)
				{
					return "1年";
				}
				if (double_0 / (double)int_1 == 900.0 && this.int_0 > 0)
				{
					return this.int_0.ToString() + "年";
				}
				break;
			case 8:
				if (this.int_0 > 0)
				{
					return this.int_0.ToString() + "年";
				}
				break;
			}
			return "";
		}

		// Token: 0x06001E68 RID: 7784 RVA: 0x000D11E4 File Offset: 0x000CF3E4
		private string method_35(object object_0, GraphPane graphPane_0, CurveItem curveItem_0, int int_1)
		{
			PointPair pointPair = curveItem_0[int_1];
			long ticks = Convert.ToInt64(10000000.0 * pointPair.Z);
			TimeSpan timeSpan = new TimeSpan(ticks);
			string str = "持仓时间：";
			if (timeSpan.Days > 0)
			{
				str = str + timeSpan.Days.ToString() + "天";
			}
			if (timeSpan.Hours > 0)
			{
				str = str + timeSpan.Hours.ToString() + "小时";
			}
			if (timeSpan.Minutes > 0)
			{
				str = str + timeSpan.Minutes.ToString() + "分钟";
			}
			if (timeSpan.Seconds > 0)
			{
				str = str + timeSpan.Seconds.ToString() + "秒";
			}
			return str + ", 盈利：" + pointPair.Y.ToString();
		}

		// Token: 0x06001E69 RID: 7785 RVA: 0x000D12D4 File Offset: 0x000CF4D4
		private void method_36()
		{
			this.method_38(this.label_accnt, Base.Acct.CurrAccount.AcctName.Trim());
			decimal iniBal = Base.Acct.CurrAccount.IniBal;
			this.method_38(this.label_iniBal, iniBal.ToString("N0"));
			decimal num = Base.Trading.smethod_178();
			this.method_8(this.label_floatPrft, num);
			decimal num2 = Base.Acct.smethod_42() + num;
			this.method_38(this.label_equity, num2.ToString("N0"));
		}

		// Token: 0x06001E6A RID: 7786 RVA: 0x000D1358 File Offset: 0x000CF558
		private void method_37(IQueryable<Transaction> iqueryable_0)
		{
			decimal d = Base.Trading.smethod_177(iqueryable_0);
			decimal d2 = Base.Trading.smethod_181(iqueryable_0);
			decimal d3 = d - d2;
			decimal num = Base.Acct.smethod_44(iqueryable_0);
			this.method_38(this.label_pIniBal, num.ToString("N"));
			string string_ = (d3 / num * 100m).ToString("F02") + "%";
			this.method_38(this.label_prftRatio, string_);
			this.method_9(this.label_prftRatio, string_);
			string string_2 = d3.ToString("N");
			this.method_38(this.label_netPrft, string_2);
			this.method_9(this.label_netPrft, string_2);
			decimal num2 = Base.Trading.smethod_179(iqueryable_0);
			this.method_38(this.label_totalPrft, num2.ToString("N"));
			decimal num3 = Base.Trading.smethod_180(iqueryable_0);
			this.method_38(this.label_totalLoss, num3.ToString("N"));
			this.method_38(this.label_fee, Base.Trading.smethod_181(iqueryable_0).ToString("N"));
			int num4 = Base.Trading.smethod_182(iqueryable_0);
			this.method_38(this.label_tradesCount, num4.ToString());
			if (num4 == 0)
			{
				this.method_38(this.label_lngCount, "0");
				this.method_38(this.label_shtCount, "0");
				this.method_38(this.label_winCountRatio, "N/A");
				this.method_38(this.label_lossCountRatio, "N/A");
				this.method_38(this.label_exptPayoff, "N/A");
				this.method_38(this.label_absDrawDown, "0");
				this.method_38(this.label_maxDrwDownRatio, "N/A");
				this.method_38(this.label_maxPrft, "N/A");
				this.method_38(this.label_maxDrawDown, "0");
				this.method_38(this.label_maxDDPrftRatio, "N/A");
				this.method_38(this.label_max1prft, "N/A");
				this.method_38(this.label_max1loss, "N/A");
				this.method_38(this.label_avgPrft, "N/A");
				this.method_38(this.label_avgLoss, "N/A");
				this.method_38(this.label_maxCnscWins, "0");
				this.method_38(this.label_maxCnscLssCnt, "0");
				this.method_38(this.label_maxCnscPrft, "0");
				this.method_38(this.label_maxCnscLosses, "0");
				this.method_38(this.label_avgCnscWins, "0");
				this.method_38(this.label_avgCnscLssCnt, "0");
			}
			else
			{
				int num5 = Base.Trading.smethod_183(iqueryable_0);
				int num6 = Base.Trading.smethod_185(iqueryable_0);
				if (num5 == 0)
				{
					this.method_38(this.label_lngCount, num5.ToString());
				}
				else
				{
					this.method_38(this.label_lngCount, num5.ToString() + " (" + (100 * num6 / num5).ToString("F00") + "%)");
				}
				int num7 = Base.Trading.smethod_184(iqueryable_0);
				int num8 = Base.Trading.smethod_186(iqueryable_0);
				if (num7 == 0)
				{
					this.method_38(this.label_shtCount, num7.ToString());
				}
				else
				{
					this.method_38(this.label_shtCount, num7.ToString() + " (" + (100 * num8 / num7).ToString("F00") + "%)");
				}
				int num9 = Base.Trading.smethod_187(iqueryable_0);
				this.method_38(this.label_winCountRatio, num9.ToString() + " (" + (100 * num9 / num4).ToString("F00") + "%)");
				int num10 = num4 - num9;
				this.method_38(this.label_lossCountRatio, num10.ToString() + " (" + (100 * num10 / num4).ToString("F00") + "%)");
				if (num9 != 0 || num10 != 0)
				{
					if (num9 == 0)
					{
						this.method_38(this.label_exptPayoff, ((0m - 100 * num10 / num4 * (100m * num3 / num10)) / 10000m).ToString("F02"));
					}
					else if (num10 == 0)
					{
						this.method_38(this.label_exptPayoff, ((100 * num9 / num4 * (100m * num2 / num9) - 0m) / 10000m).ToString("F02"));
					}
					else
					{
						this.method_38(this.label_exptPayoff, ((100 * num9 / num4 * (100m * num2 / num9) + 100 * num10 / num4 * (100m * num3 / num10)) / 10000m).ToString("F02"));
					}
				}
				this.method_38(this.label_absDrawDown, Base.Trading.smethod_188(iqueryable_0).ToString("N"));
				Class59 @class = Base.Trading.smethod_189(iqueryable_0);
				if (@class != null)
				{
					decimal decimal_ = @class.decimal_2;
					this.method_38(this.label_maxDrawDown, decimal_.ToString("N"));
					this.method_38(this.label_maxDrwDownRatio, (100m * Math.Abs(decimal_ / (num + @class.decimal_1))).ToString("F02") + "%");
					this.method_38(this.label_maxPrft, @class.decimal_0.ToString("N"));
					if (decimal_ != 0m)
					{
						this.method_38(this.label_maxDDPrftRatio, Math.Abs((num2 + num3) / decimal_).ToString("F02"));
					}
					else
					{
						this.method_38(this.label_maxDDPrftRatio, "N/A");
					}
				}
				else
				{
					this.method_38(this.label_maxDrawDown, "0");
					this.method_38(this.label_maxDrwDownRatio, "N/A");
					this.method_38(this.label_maxPrft, "N/A");
					this.method_38(this.label_maxDDPrftRatio, "N/A");
				}
				this.method_38(this.label_max1prft, Base.Trading.smethod_203(iqueryable_0).ToString("N"));
				this.method_38(this.label_max1loss, Base.Trading.smethod_204(iqueryable_0).ToString("N"));
				if (num9 == 0)
				{
					this.method_38(this.label_avgPrft, "N/A");
				}
				else
				{
					this.method_38(this.label_avgPrft, (num2 / num9).ToString("N"));
				}
				if (num10 == 0)
				{
					this.method_38(this.label_avgLoss, "N/A");
				}
				else
				{
					this.method_38(this.label_avgLoss, (num3 / num10).ToString("N"));
				}
				this.method_38(this.label_maxCnscWins, Base.Trading.smethod_192(iqueryable_0).ToString() + " (" + Base.Trading.smethod_193(iqueryable_0).ToString("N00") + ")");
				this.method_38(this.label_maxCnscLssCnt, Base.Trading.smethod_200(iqueryable_0).ToString() + " (" + Base.Trading.smethod_201(iqueryable_0).ToString("N00") + ")");
				this.method_38(this.label_maxCnscPrft, Base.Trading.smethod_191(iqueryable_0).ToString("N"));
				this.method_38(this.label_maxCnscLosses, Base.Trading.smethod_190(iqueryable_0).ToString("N"));
				if (num9 == 0)
				{
					this.method_38(this.label_avgCnscWins, "N/A");
				}
				else
				{
					this.method_38(this.label_avgCnscWins, Base.Trading.smethod_195(iqueryable_0).ToString("F02"));
				}
				if (num10 == 0)
				{
					this.method_38(this.label_avgCnscLssCnt, "N/A");
				}
				else
				{
					this.method_38(this.label_avgCnscLssCnt, Base.Trading.smethod_196(iqueryable_0).ToString("F02"));
				}
			}
		}

		// Token: 0x06001E6B RID: 7787 RVA: 0x0000CB6C File Offset: 0x0000AD6C
		public void method_38(Control control_0, string string_0)
		{
			if (control_0.InvokeRequired)
			{
				control_0.BeginInvoke(new TrdAnalysisPanel.Delegate34(this.method_38), new object[]
				{
					control_0,
					string_0
				});
			}
			else
			{
				control_0.Text = string_0;
			}
		}

		// Token: 0x06001E6C RID: 7788 RVA: 0x0000CBA2 File Offset: 0x0000ADA2
		public void method_39(Control control_0, Color color_1)
		{
			if (control_0.InvokeRequired)
			{
				control_0.BeginInvoke(new TrdAnalysisPanel.Delegate35(this.method_39), new object[]
				{
					control_0,
					color_1
				});
			}
			else
			{
				control_0.ForeColor = color_1;
			}
		}

		// Token: 0x06001E6D RID: 7789 RVA: 0x0000CBDD File Offset: 0x0000ADDD
		public void method_40(string string_0)
		{
			this.tabCtrl_Acct.method_0(string_0);
		}

		// Token: 0x06001E6E RID: 7790 RVA: 0x0000CBED File Offset: 0x0000ADED
		private void method_41(object sender, EventArgs e)
		{
			if (!base.IsDisposed && base.IsHandleCreated)
			{
				this.method_6();
			}
		}

		// Token: 0x06001E6F RID: 7791 RVA: 0x0000CC07 File Offset: 0x0000AE07
		private void method_42(EventArgs17 eventArgs17_0)
		{
			this.method_44(false);
		}

		// Token: 0x06001E70 RID: 7792 RVA: 0x0000CC07 File Offset: 0x0000AE07
		private void method_43(object sender, EventArgs e)
		{
			this.method_44(false);
		}

		// Token: 0x06001E71 RID: 7793 RVA: 0x000D1BBC File Offset: 0x000CFDBC
		private void method_44(bool bool_0 = false)
		{
			if (!bool_0 || this.tabPg_Acct.Visible)
			{
				string text = this.tabPg_Acct.Parent.SelectedTab.Text;
				if (text.Equals("账户信息"))
				{
					this.method_7();
				}
				else if (text.Equals("交易统计"))
				{
					this.method_10();
				}
			}
		}

		// Token: 0x170004CA RID: 1226
		// (set) Token: 0x06001E72 RID: 7794 RVA: 0x0000CC12 File Offset: 0x0000AE12
		public TabColorScheme ColorScheme
		{
			set
			{
				if (this.tabCtrl_Acct != null)
				{
					this.tabCtrl_Acct.ColorScheme = value;
				}
			}
		}

		// Token: 0x06001E73 RID: 7795 RVA: 0x0000CC2A File Offset: 0x0000AE2A
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001E74 RID: 7796 RVA: 0x000D1C1C File Offset: 0x000CFE1C
		private void InitializeComponent()
		{
			this.icontainer_0 = new Container();
			this.tabCtrl_Acct = new Control2();
			this.tabControlPanel_Acct = new TabControlPanel();
			this.panel_AcctInfo = new Panel();
			this.label_UsedMargin = new System.Windows.Forms.Label();
			this.label_acct_账户 = new System.Windows.Forms.Label();
			this.label_Profit = new System.Windows.Forms.Label();
			this.label_acct_动态权益 = new System.Windows.Forms.Label();
			this.label_FreeMargin = new System.Windows.Forms.Label();
			this.label_acct_初始资金 = new System.Windows.Forms.Label();
			this.label_FeeSum = new System.Windows.Forms.Label();
			this.label_acct_可用资金 = new System.Windows.Forms.Label();
			this.label_DynaProfit = new System.Windows.Forms.Label();
			this.label_acct_浮动盈亏 = new System.Windows.Forms.Label();
			this.label_IniBalance = new System.Windows.Forms.Label();
			this.label_acct_平仓盈亏 = new System.Windows.Forms.Label();
			this.label_CurrEquity = new System.Windows.Forms.Label();
			this.label_acct_手续费 = new System.Windows.Forms.Label();
			this.label_AcctName = new System.Windows.Forms.Label();
			this.label_acct_保证金 = new System.Windows.Forms.Label();
			this.tabPg_Acct = new TabItem(this.icontainer_0);
			this.tabControlPanel_Report = new TabControlPanel();
			this.panel_TradingStat = new Panel();
			this.panel_TradeAnlys_Header = new Class296();
			this.label_floatPrft = new System.Windows.Forms.Label();
			this.label1 = new System.Windows.Forms.Label();
			this.label2 = new System.Windows.Forms.Label();
			this.label4 = new System.Windows.Forms.Label();
			this.label6 = new System.Windows.Forms.Label();
			this.label_equity = new System.Windows.Forms.Label();
			this.label_accnt = new System.Windows.Forms.Label();
			this.label_iniBal = new System.Windows.Forms.Label();
			this.panel_TradeAnlys_Sels = new Class296();
			this.comboBox_Year = new ComboBox();
			this.comboBox_tradingOverview_CurrOrAll = new ComboBox();
			this.comboBox_Symbl = new ComboBox();
			this.label_年份 = new System.Windows.Forms.Label();
			this.label_品种h = new System.Windows.Forms.Label();
			this.label_月份 = new System.Windows.Forms.Label();
			this.comboBox_Month = new ComboBox();
			this.groupBox2 = new GroupBox();
			this.panel_tradeAnlys = new Class296();
			this.label_pIniBal = new System.Windows.Forms.Label();
			this.label47 = new System.Windows.Forms.Label();
			this.label_prftRatio = new System.Windows.Forms.Label();
			this.label_盈利率 = new System.Windows.Forms.Label();
			this.label_maxPrft = new System.Windows.Forms.Label();
			this.label_maxDrwDownRatio = new System.Windows.Forms.Label();
			this.label43 = new System.Windows.Forms.Label();
			this.label42 = new System.Windows.Forms.Label();
			this.label35 = new System.Windows.Forms.Label();
			this.label_maxDDPrftRatio = new System.Windows.Forms.Label();
			this.label_maxDrawDown = new System.Windows.Forms.Label();
			this.label_absDrawDown = new System.Windows.Forms.Label();
			this.label34 = new System.Windows.Forms.Label();
			this.label33 = new System.Windows.Forms.Label();
			this.label_lossCountRatio = new System.Windows.Forms.Label();
			this.label_winCountRatio = new System.Windows.Forms.Label();
			this.label_avgCnscLssCnt = new System.Windows.Forms.Label();
			this.label_avgCnscWins = new System.Windows.Forms.Label();
			this.label_maxCnscLosses = new System.Windows.Forms.Label();
			this.label_maxCnscPrft = new System.Windows.Forms.Label();
			this.label_maxCnscLssCnt = new System.Windows.Forms.Label();
			this.label_fee = new System.Windows.Forms.Label();
			this.label26 = new System.Windows.Forms.Label();
			this.label_maxCnscWins = new System.Windows.Forms.Label();
			this.label_avgLoss = new System.Windows.Forms.Label();
			this.label_avgPrft = new System.Windows.Forms.Label();
			this.label_max1loss = new System.Windows.Forms.Label();
			this.label_max1prft = new System.Windows.Forms.Label();
			this.label_shtCount = new System.Windows.Forms.Label();
			this.label_lngCount = new System.Windows.Forms.Label();
			this.label_tradesCount = new System.Windows.Forms.Label();
			this.label_exptPayoff = new System.Windows.Forms.Label();
			this.label_netPrft = new System.Windows.Forms.Label();
			this.label_totalLoss = new System.Windows.Forms.Label();
			this.label_totalPrft = new System.Windows.Forms.Label();
			this.label32 = new System.Windows.Forms.Label();
			this.label31 = new System.Windows.Forms.Label();
			this.label30 = new System.Windows.Forms.Label();
			this.label29 = new System.Windows.Forms.Label();
			this.label28 = new System.Windows.Forms.Label();
			this.label27 = new System.Windows.Forms.Label();
			this.label25 = new System.Windows.Forms.Label();
			this.label24 = new System.Windows.Forms.Label();
			this.label23 = new System.Windows.Forms.Label();
			this.label22 = new System.Windows.Forms.Label();
			this.label21 = new System.Windows.Forms.Label();
			this.label20 = new System.Windows.Forms.Label();
			this.label19 = new System.Windows.Forms.Label();
			this.label18 = new System.Windows.Forms.Label();
			this.label17 = new System.Windows.Forms.Label();
			this.label16 = new System.Windows.Forms.Label();
			this.label15 = new System.Windows.Forms.Label();
			this.label14 = new System.Windows.Forms.Label();
			this.label13 = new System.Windows.Forms.Label();
			this.label12 = new System.Windows.Forms.Label();
			this.label11 = new System.Windows.Forms.Label();
			this.label9 = new System.Windows.Forms.Label();
			this.label8 = new System.Windows.Forms.Label();
			this.label7 = new System.Windows.Forms.Label();
			this.tabPg_Report = new TabItem(this.icontainer_0);
			this.tabControlPanel_EquityGraph = new TabControlPanel();
			this.tabPg_EquityGraph = new TabItem(this.icontainer_0);
			this.tabControlPanel_PrftLine = new TabControlPanel();
			this.tabPg_PrftLine = new TabItem(this.icontainer_0);
			this.tabControlPanel_SymbPrft = new TabControlPanel();
			this.tabPg_SymbPrft = new TabItem(this.icontainer_0);
			this.tabControlPanel_TimePrft = new TabControlPanel();
			this.tabPg_TimePrft = new TabItem(this.icontainer_0);
			this.tabControlPanel_LngShrtPrft = new TabControlPanel();
			this.splitContainer_LngShrtPrft = new SplitContainer();
			this.tabPg_LngShrtPrft = new TabItem(this.icontainer_0);
			((ISupportInitialize)this.tabCtrl_Acct).BeginInit();
			this.tabCtrl_Acct.SuspendLayout();
			this.tabControlPanel_Acct.SuspendLayout();
			this.panel_AcctInfo.SuspendLayout();
			this.tabControlPanel_Report.SuspendLayout();
			this.panel_TradingStat.SuspendLayout();
			this.panel_TradeAnlys_Header.SuspendLayout();
			this.panel_TradeAnlys_Sels.SuspendLayout();
			this.panel_tradeAnlys.SuspendLayout();
			this.tabControlPanel_LngShrtPrft.SuspendLayout();
			this.splitContainer_LngShrtPrft.SuspendLayout();
			base.SuspendLayout();
			this.tabCtrl_Acct.CanReorderTabs = true;
			this.tabCtrl_Acct.Controls.Add(this.tabControlPanel_Acct);
			this.tabCtrl_Acct.Controls.Add(this.tabControlPanel_Report);
			this.tabCtrl_Acct.Controls.Add(this.tabControlPanel_EquityGraph);
			this.tabCtrl_Acct.Controls.Add(this.tabControlPanel_PrftLine);
			this.tabCtrl_Acct.Controls.Add(this.tabControlPanel_SymbPrft);
			this.tabCtrl_Acct.Controls.Add(this.tabControlPanel_TimePrft);
			this.tabCtrl_Acct.Controls.Add(this.tabControlPanel_LngShrtPrft);
			this.tabCtrl_Acct.Dock = DockStyle.Fill;
			this.tabCtrl_Acct.Font = new Font("SimSun", 9f);
			this.tabCtrl_Acct.Location = new Point(0, 0);
			this.tabCtrl_Acct.MinimumSize = new Size(229, 138);
			this.tabCtrl_Acct.Name = "tabCtrl_Acct";
			this.tabCtrl_Acct.SelectedTabFont = new Font("SimSun", 9f, FontStyle.Bold);
			this.tabCtrl_Acct.SelectedTabIndex = 0;
			this.tabCtrl_Acct.Size = new Size(1189, 676);
			this.tabCtrl_Acct.Style = eTabStripStyle.Flat;
			this.tabCtrl_Acct.TabAlignment = eTabStripAlignment.Bottom;
			this.tabCtrl_Acct.TabIndex = 3;
			this.tabCtrl_Acct.TabLayoutType = eTabLayoutType.FixedWithNavigationBox;
			this.tabCtrl_Acct.Tabs.Add(this.tabPg_Acct);
			this.tabCtrl_Acct.Tabs.Add(this.tabPg_Report);
			this.tabCtrl_Acct.Tabs.Add(this.tabPg_EquityGraph);
			this.tabCtrl_Acct.Tabs.Add(this.tabPg_PrftLine);
			this.tabCtrl_Acct.Tabs.Add(this.tabPg_SymbPrft);
			this.tabCtrl_Acct.Tabs.Add(this.tabPg_LngShrtPrft);
			this.tabCtrl_Acct.Tabs.Add(this.tabPg_TimePrft);
			this.tabCtrl_Acct.Text = "tabCtrl";
			this.tabControlPanel_Acct.AutoScroll = true;
			this.tabControlPanel_Acct.CanvasColor = SystemColors.Control;
			this.tabControlPanel_Acct.ColorSchemeStyle = eDotNetBarStyle.Metro;
			this.tabControlPanel_Acct.Controls.Add(this.panel_AcctInfo);
			this.tabControlPanel_Acct.Dock = DockStyle.Fill;
			this.tabControlPanel_Acct.Location = new Point(0, 0);
			this.tabControlPanel_Acct.Name = "tabControlPanel_Acct";
			this.tabControlPanel_Acct.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_Acct.Size = new Size(1189, 648);
			this.tabControlPanel_Acct.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_Acct.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_Acct.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_Acct.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_Acct.Style.GradientAngle = -90;
			this.tabControlPanel_Acct.TabIndex = 4;
			this.tabControlPanel_Acct.TabItem = this.tabPg_Acct;
			this.panel_AcctInfo.BackColor = Color.Transparent;
			this.panel_AcctInfo.Controls.Add(this.label_UsedMargin);
			this.panel_AcctInfo.Controls.Add(this.label_acct_账户);
			this.panel_AcctInfo.Controls.Add(this.label_Profit);
			this.panel_AcctInfo.Controls.Add(this.label_acct_动态权益);
			this.panel_AcctInfo.Controls.Add(this.label_FreeMargin);
			this.panel_AcctInfo.Controls.Add(this.label_acct_初始资金);
			this.panel_AcctInfo.Controls.Add(this.label_FeeSum);
			this.panel_AcctInfo.Controls.Add(this.label_acct_可用资金);
			this.panel_AcctInfo.Controls.Add(this.label_DynaProfit);
			this.panel_AcctInfo.Controls.Add(this.label_acct_浮动盈亏);
			this.panel_AcctInfo.Controls.Add(this.label_IniBalance);
			this.panel_AcctInfo.Controls.Add(this.label_acct_平仓盈亏);
			this.panel_AcctInfo.Controls.Add(this.label_CurrEquity);
			this.panel_AcctInfo.Controls.Add(this.label_acct_手续费);
			this.panel_AcctInfo.Controls.Add(this.label_AcctName);
			this.panel_AcctInfo.Controls.Add(this.label_acct_保证金);
			this.panel_AcctInfo.Dock = DockStyle.Fill;
			this.panel_AcctInfo.Location = new Point(1, 1);
			this.panel_AcctInfo.Name = "panel_AcctInfo";
			this.panel_AcctInfo.Size = new Size(1187, 646);
			this.panel_AcctInfo.TabIndex = 16;
			this.label_UsedMargin.BackColor = Color.Transparent;
			this.label_UsedMargin.Font = new Font("Microsoft Sans Serif", 8.6f);
			this.label_UsedMargin.Location = new Point(456, 131);
			this.label_UsedMargin.MinimumSize = new Size(114, 0);
			this.label_UsedMargin.Name = "label_UsedMargin";
			this.label_UsedMargin.Size = new Size(135, 20);
			this.label_UsedMargin.TabIndex = 15;
			this.label_UsedMargin.Text = "label_UsedMargin";
			this.label_UsedMargin.TextAlign = ContentAlignment.MiddleRight;
			this.label_acct_账户.BackColor = Color.Transparent;
			this.label_acct_账户.Font = new Font("SimSun", 8.830189f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_acct_账户.Location = new Point(29, 23);
			this.label_acct_账户.Name = "label_acct_账户";
			this.label_acct_账户.Size = new Size(100, 20);
			this.label_acct_账户.TabIndex = 0;
			this.label_acct_账户.Text = "账户：";
			this.label_acct_账户.TextAlign = ContentAlignment.MiddleLeft;
			this.label_Profit.BackColor = Color.Transparent;
			this.label_Profit.Font = new Font("Microsoft Sans Serif", 8.6f);
			this.label_Profit.Location = new Point(456, 95);
			this.label_Profit.MinimumSize = new Size(114, 0);
			this.label_Profit.Name = "label_Profit";
			this.label_Profit.Size = new Size(135, 20);
			this.label_Profit.TabIndex = 14;
			this.label_Profit.Text = "label_Profit";
			this.label_Profit.TextAlign = ContentAlignment.MiddleRight;
			this.label_acct_动态权益.BackColor = Color.Transparent;
			this.label_acct_动态权益.Font = new Font("SimSun", 8.830189f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_acct_动态权益.Location = new Point(29, 59);
			this.label_acct_动态权益.Name = "label_acct_动态权益";
			this.label_acct_动态权益.Size = new Size(100, 20);
			this.label_acct_动态权益.TabIndex = 1;
			this.label_acct_动态权益.Text = "动态权益：";
			this.label_acct_动态权益.TextAlign = ContentAlignment.MiddleLeft;
			this.label_FreeMargin.BackColor = Color.Transparent;
			this.label_FreeMargin.Font = new Font("Microsoft Sans Serif", 8.6f);
			this.label_FreeMargin.Location = new Point(456, 59);
			this.label_FreeMargin.MinimumSize = new Size(114, 0);
			this.label_FreeMargin.Name = "label_FreeMargin";
			this.label_FreeMargin.Size = new Size(135, 20);
			this.label_FreeMargin.TabIndex = 13;
			this.label_FreeMargin.Text = "label_FreeMargin";
			this.label_FreeMargin.TextAlign = ContentAlignment.MiddleRight;
			this.label_acct_初始资金.BackColor = Color.Transparent;
			this.label_acct_初始资金.Font = new Font("SimSun", 8.830189f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_acct_初始资金.Location = new Point(354, 23);
			this.label_acct_初始资金.Name = "label_acct_初始资金";
			this.label_acct_初始资金.Size = new Size(100, 20);
			this.label_acct_初始资金.TabIndex = 2;
			this.label_acct_初始资金.Text = "初始资金：";
			this.label_acct_初始资金.TextAlign = ContentAlignment.MiddleLeft;
			this.label_FeeSum.BackColor = Color.Transparent;
			this.label_FeeSum.Font = new Font("Microsoft Sans Serif", 8.6f);
			this.label_FeeSum.Location = new Point(140, 131);
			this.label_FeeSum.MinimumSize = new Size(114, 0);
			this.label_FeeSum.Name = "label_FeeSum";
			this.label_FeeSum.Size = new Size(135, 20);
			this.label_FeeSum.TabIndex = 12;
			this.label_FeeSum.Text = "label_FeeSum";
			this.label_FeeSum.TextAlign = ContentAlignment.MiddleRight;
			this.label_acct_可用资金.BackColor = Color.Transparent;
			this.label_acct_可用资金.Font = new Font("SimSun", 8.830189f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_acct_可用资金.Location = new Point(354, 59);
			this.label_acct_可用资金.Name = "label_acct_可用资金";
			this.label_acct_可用资金.Size = new Size(100, 20);
			this.label_acct_可用资金.TabIndex = 3;
			this.label_acct_可用资金.Text = "可用资金：";
			this.label_acct_可用资金.TextAlign = ContentAlignment.MiddleLeft;
			this.label_DynaProfit.BackColor = Color.Transparent;
			this.label_DynaProfit.Font = new Font("Microsoft Sans Serif", 8.6f);
			this.label_DynaProfit.Location = new Point(140, 95);
			this.label_DynaProfit.MinimumSize = new Size(114, 0);
			this.label_DynaProfit.Name = "label_DynaProfit";
			this.label_DynaProfit.Size = new Size(135, 20);
			this.label_DynaProfit.TabIndex = 11;
			this.label_DynaProfit.Text = "label_DynaProfit";
			this.label_DynaProfit.TextAlign = ContentAlignment.MiddleRight;
			this.label_acct_浮动盈亏.BackColor = Color.Transparent;
			this.label_acct_浮动盈亏.Font = new Font("SimSun", 8.830189f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_acct_浮动盈亏.Location = new Point(29, 95);
			this.label_acct_浮动盈亏.Name = "label_acct_浮动盈亏";
			this.label_acct_浮动盈亏.Size = new Size(100, 20);
			this.label_acct_浮动盈亏.TabIndex = 4;
			this.label_acct_浮动盈亏.Text = "浮动盈亏：";
			this.label_acct_浮动盈亏.TextAlign = ContentAlignment.MiddleLeft;
			this.label_IniBalance.BackColor = Color.Transparent;
			this.label_IniBalance.Font = new Font("Microsoft Sans Serif", 8.6f);
			this.label_IniBalance.Location = new Point(456, 23);
			this.label_IniBalance.MinimumSize = new Size(114, 0);
			this.label_IniBalance.Name = "label_IniBalance";
			this.label_IniBalance.Size = new Size(135, 20);
			this.label_IniBalance.TabIndex = 10;
			this.label_IniBalance.Text = "label_IniBal";
			this.label_IniBalance.TextAlign = ContentAlignment.MiddleRight;
			this.label_acct_平仓盈亏.BackColor = Color.Transparent;
			this.label_acct_平仓盈亏.Font = new Font("SimSun", 8.830189f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_acct_平仓盈亏.Location = new Point(354, 95);
			this.label_acct_平仓盈亏.Name = "label_acct_平仓盈亏";
			this.label_acct_平仓盈亏.Size = new Size(100, 20);
			this.label_acct_平仓盈亏.TabIndex = 5;
			this.label_acct_平仓盈亏.Text = "平仓盈亏：";
			this.label_acct_平仓盈亏.TextAlign = ContentAlignment.MiddleLeft;
			this.label_CurrEquity.BackColor = Color.Transparent;
			this.label_CurrEquity.Font = new Font("Microsoft Sans Serif", 8.6f);
			this.label_CurrEquity.Location = new Point(140, 59);
			this.label_CurrEquity.MinimumSize = new Size(114, 0);
			this.label_CurrEquity.Name = "label_CurrEquity";
			this.label_CurrEquity.Size = new Size(135, 20);
			this.label_CurrEquity.TabIndex = 9;
			this.label_CurrEquity.Text = "label_Equity";
			this.label_CurrEquity.TextAlign = ContentAlignment.MiddleRight;
			this.label_acct_手续费.BackColor = Color.Transparent;
			this.label_acct_手续费.Font = new Font("SimSun", 8.830189f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_acct_手续费.Location = new Point(29, 131);
			this.label_acct_手续费.Name = "label_acct_手续费";
			this.label_acct_手续费.Size = new Size(100, 20);
			this.label_acct_手续费.TabIndex = 6;
			this.label_acct_手续费.Text = "手续费：";
			this.label_acct_手续费.TextAlign = ContentAlignment.MiddleLeft;
			this.label_AcctName.BackColor = Color.Transparent;
			this.label_AcctName.Font = new Font("Microsoft Sans Serif", 8.6f);
			this.label_AcctName.Location = new Point(140, 23);
			this.label_AcctName.MinimumSize = new Size(114, 0);
			this.label_AcctName.Name = "label_AcctName";
			this.label_AcctName.Size = new Size(135, 20);
			this.label_AcctName.TabIndex = 8;
			this.label_AcctName.Text = "label_AcctName";
			this.label_AcctName.TextAlign = ContentAlignment.MiddleRight;
			this.label_acct_保证金.BackColor = Color.Transparent;
			this.label_acct_保证金.Font = new Font("SimSun", 8.830189f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label_acct_保证金.Location = new Point(354, 131);
			this.label_acct_保证金.Name = "label_acct_保证金";
			this.label_acct_保证金.Size = new Size(100, 20);
			this.label_acct_保证金.TabIndex = 7;
			this.label_acct_保证金.Text = "保证金：";
			this.label_acct_保证金.TextAlign = ContentAlignment.MiddleLeft;
			this.tabPg_Acct.AttachedControl = this.tabControlPanel_Acct;
			this.tabPg_Acct.Name = "tabPg_Acct";
			this.tabPg_Acct.Text = "账户信息";
			this.tabControlPanel_Report.AutoScroll = true;
			this.tabControlPanel_Report.Controls.Add(this.panel_TradingStat);
			this.tabControlPanel_Report.Dock = DockStyle.Fill;
			this.tabControlPanel_Report.Location = new Point(0, 0);
			this.tabControlPanel_Report.Name = "tabControlPanel_Report";
			this.tabControlPanel_Report.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_Report.Size = new Size(1189, 648);
			this.tabControlPanel_Report.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_Report.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_Report.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_Report.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_Report.Style.GradientAngle = -90;
			this.tabControlPanel_Report.TabIndex = 5;
			this.tabControlPanel_Report.TabItem = this.tabPg_Report;
			this.panel_TradingStat.AutoScroll = true;
			this.panel_TradingStat.Controls.Add(this.panel_TradeAnlys_Header);
			this.panel_TradingStat.Controls.Add(this.panel_TradeAnlys_Sels);
			this.panel_TradingStat.Controls.Add(this.groupBox2);
			this.panel_TradingStat.Controls.Add(this.panel_tradeAnlys);
			this.panel_TradingStat.Dock = DockStyle.Fill;
			this.panel_TradingStat.Location = new Point(1, 1);
			this.panel_TradingStat.Margin = new System.Windows.Forms.Padding(0);
			this.panel_TradingStat.Name = "panel_TradingStat";
			this.panel_TradingStat.Size = new Size(1187, 646);
			this.panel_TradingStat.TabIndex = 32;
			this.panel_TradeAnlys_Header.BackColor = Color.Transparent;
			this.panel_TradeAnlys_Header.BorderColor = Color.Empty;
			this.panel_TradeAnlys_Header.Controls.Add(this.label_floatPrft);
			this.panel_TradeAnlys_Header.Controls.Add(this.label1);
			this.panel_TradeAnlys_Header.Controls.Add(this.label2);
			this.panel_TradeAnlys_Header.Controls.Add(this.label4);
			this.panel_TradeAnlys_Header.Controls.Add(this.label6);
			this.panel_TradeAnlys_Header.Controls.Add(this.label_equity);
			this.panel_TradeAnlys_Header.Controls.Add(this.label_accnt);
			this.panel_TradeAnlys_Header.Controls.Add(this.label_iniBal);
			this.panel_TradeAnlys_Header.DrawCustomBorder = false;
			this.panel_TradeAnlys_Header.Location = new Point(6, 6);
			this.panel_TradeAnlys_Header.Name = "panel_TradeAnlys_Header";
			this.panel_TradeAnlys_Header.Size = new Size(838, 34);
			this.panel_TradeAnlys_Header.TabIndex = 30;
			this.label_floatPrft.BackColor = Color.Transparent;
			this.label_floatPrft.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_floatPrft.Location = new Point(718, 4);
			this.label_floatPrft.Name = "label_floatPrft";
			this.label_floatPrft.Size = new Size(104, 20);
			this.label_floatPrft.TabIndex = 18;
			this.label_floatPrft.Text = "label_floatPrft";
			this.label_floatPrft.TextAlign = ContentAlignment.BottomRight;
			this.label1.BackColor = Color.Transparent;
			this.label1.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label1.Location = new Point(3, 5);
			this.label1.Name = "label1";
			this.label1.Size = new Size(65, 20);
			this.label1.TabIndex = 2;
			this.label1.Text = "账户：";
			this.label1.TextAlign = ContentAlignment.BottomLeft;
			this.label2.BackColor = Color.Transparent;
			this.label2.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label2.Location = new Point(196, 5);
			this.label2.Name = "label2";
			this.label2.Size = new Size(100, 20);
			this.label2.TabIndex = 3;
			this.label2.Text = "初始资金：";
			this.label2.TextAlign = ContentAlignment.BottomLeft;
			this.label4.BackColor = Color.Transparent;
			this.label4.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label4.Location = new Point(615, 5);
			this.label4.Name = "label4";
			this.label4.Size = new Size(100, 20);
			this.label4.TabIndex = 5;
			this.label4.Text = "浮动盈亏：";
			this.label4.TextAlign = ContentAlignment.BottomLeft;
			this.label6.BackColor = Color.Transparent;
			this.label6.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label6.Location = new Point(410, 5);
			this.label6.Name = "label6";
			this.label6.Size = new Size(100, 20);
			this.label6.TabIndex = 7;
			this.label6.Text = "当前权益：";
			this.label6.TextAlign = ContentAlignment.BottomLeft;
			this.label_equity.BackColor = Color.Transparent;
			this.label_equity.Font = new Font("Microsoft Sans Serif", 9f);
			this.label_equity.Location = new Point(471, 5);
			this.label_equity.Name = "label_equity";
			this.label_equity.Size = new Size(124, 20);
			this.label_equity.TabIndex = 19;
			this.label_equity.Text = "label_equity";
			this.label_equity.TextAlign = ContentAlignment.BottomRight;
			this.label_accnt.BackColor = Color.Transparent;
			this.label_accnt.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_accnt.Location = new Point(61, 5);
			this.label_accnt.Name = "label_accnt";
			this.label_accnt.Size = new Size(120, 20);
			this.label_accnt.TabIndex = 15;
			this.label_accnt.Text = "label_accnt";
			this.label_accnt.TextAlign = ContentAlignment.BottomRight;
			this.label_iniBal.BackColor = Color.Transparent;
			this.label_iniBal.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_iniBal.Location = new Point(275, 5);
			this.label_iniBal.Name = "label_iniBal";
			this.label_iniBal.Size = new Size(114, 20);
			this.label_iniBal.TabIndex = 16;
			this.label_iniBal.Text = "label_iniBal";
			this.label_iniBal.TextAlign = ContentAlignment.BottomRight;
			this.panel_TradeAnlys_Sels.BackColor = Color.Transparent;
			this.panel_TradeAnlys_Sels.BorderColor = Color.Empty;
			this.panel_TradeAnlys_Sels.Controls.Add(this.comboBox_Year);
			this.panel_TradeAnlys_Sels.Controls.Add(this.comboBox_tradingOverview_CurrOrAll);
			this.panel_TradeAnlys_Sels.Controls.Add(this.comboBox_Symbl);
			this.panel_TradeAnlys_Sels.Controls.Add(this.label_年份);
			this.panel_TradeAnlys_Sels.Controls.Add(this.label_品种h);
			this.panel_TradeAnlys_Sels.Controls.Add(this.label_月份);
			this.panel_TradeAnlys_Sels.Controls.Add(this.comboBox_Month);
			this.panel_TradeAnlys_Sels.DrawCustomBorder = false;
			this.panel_TradeAnlys_Sels.Location = new Point(6, 46);
			this.panel_TradeAnlys_Sels.Name = "panel_TradeAnlys_Sels";
			this.panel_TradeAnlys_Sels.Size = new Size(838, 30);
			this.panel_TradeAnlys_Sels.TabIndex = 31;
			this.comboBox_Year.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_Year.Font = new Font("Microsoft Sans Serif", 9f);
			this.comboBox_Year.FormattingEnabled = true;
			this.comboBox_Year.Location = new Point(277, 2);
			this.comboBox_Year.Name = "comboBox_Year";
			this.comboBox_Year.Size = new Size(75, 26);
			this.comboBox_Year.TabIndex = 22;
			this.comboBox_tradingOverview_CurrOrAll.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_tradingOverview_CurrOrAll.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.comboBox_tradingOverview_CurrOrAll.FormattingEnabled = true;
			this.comboBox_tradingOverview_CurrOrAll.Location = new Point(1, 2);
			this.comboBox_tradingOverview_CurrOrAll.Name = "comboBox_tradingOverview_CurrOrAll";
			this.comboBox_tradingOverview_CurrOrAll.Size = new Size(118, 26);
			this.comboBox_tradingOverview_CurrOrAll.TabIndex = 10;
			this.comboBox_Symbl.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_Symbl.Font = new Font("Microsoft Sans Serif", 9f);
			this.comboBox_Symbl.FormattingEnabled = true;
			this.comboBox_Symbl.Location = new Point(659, 2);
			this.comboBox_Symbl.Name = "comboBox_Symbl";
			this.comboBox_Symbl.Size = new Size(161, 26);
			this.comboBox_Symbl.TabIndex = 26;
			this.label_年份.BackColor = Color.Transparent;
			this.label_年份.Location = new Point(201, 7);
			this.label_年份.Name = "label_年份";
			this.label_年份.Size = new Size(70, 20);
			this.label_年份.TabIndex = 21;
			this.label_年份.Text = "年份：";
			this.label_年份.TextAlign = ContentAlignment.MiddleRight;
			this.label_品种h.BackColor = Color.Transparent;
			this.label_品种h.Location = new Point(584, 7);
			this.label_品种h.Name = "label_品种h";
			this.label_品种h.Size = new Size(70, 20);
			this.label_品种h.TabIndex = 25;
			this.label_品种h.Text = "品种：";
			this.label_品种h.TextAlign = ContentAlignment.MiddleRight;
			this.label_月份.BackColor = Color.Transparent;
			this.label_月份.Location = new Point(389, 7);
			this.label_月份.Name = "label_月份";
			this.label_月份.Size = new Size(70, 20);
			this.label_月份.TabIndex = 23;
			this.label_月份.Text = "月份：";
			this.label_月份.TextAlign = ContentAlignment.MiddleRight;
			this.comboBox_Month.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_Month.Font = new Font("Microsoft Sans Serif", 9f);
			this.comboBox_Month.FormattingEnabled = true;
			this.comboBox_Month.Location = new Point(464, 2);
			this.comboBox_Month.Name = "comboBox_Month";
			this.comboBox_Month.Size = new Size(75, 26);
			this.comboBox_Month.TabIndex = 24;
			this.groupBox2.Location = new Point(6, 39);
			this.groupBox2.Name = "groupBox2";
			this.groupBox2.Size = new Size(820, 2);
			this.groupBox2.TabIndex = 20;
			this.groupBox2.TabStop = false;
			this.panel_tradeAnlys.BackColor = Color.Transparent;
			this.panel_tradeAnlys.BorderColor = Color.Empty;
			this.panel_tradeAnlys.Controls.Add(this.label_pIniBal);
			this.panel_tradeAnlys.Controls.Add(this.label47);
			this.panel_tradeAnlys.Controls.Add(this.label_prftRatio);
			this.panel_tradeAnlys.Controls.Add(this.label_盈利率);
			this.panel_tradeAnlys.Controls.Add(this.label_maxPrft);
			this.panel_tradeAnlys.Controls.Add(this.label_maxDrwDownRatio);
			this.panel_tradeAnlys.Controls.Add(this.label43);
			this.panel_tradeAnlys.Controls.Add(this.label42);
			this.panel_tradeAnlys.Controls.Add(this.label35);
			this.panel_tradeAnlys.Controls.Add(this.label_maxDDPrftRatio);
			this.panel_tradeAnlys.Controls.Add(this.label_maxDrawDown);
			this.panel_tradeAnlys.Controls.Add(this.label_absDrawDown);
			this.panel_tradeAnlys.Controls.Add(this.label34);
			this.panel_tradeAnlys.Controls.Add(this.label33);
			this.panel_tradeAnlys.Controls.Add(this.label_lossCountRatio);
			this.panel_tradeAnlys.Controls.Add(this.label_winCountRatio);
			this.panel_tradeAnlys.Controls.Add(this.label_avgCnscLssCnt);
			this.panel_tradeAnlys.Controls.Add(this.label_avgCnscWins);
			this.panel_tradeAnlys.Controls.Add(this.label_maxCnscLosses);
			this.panel_tradeAnlys.Controls.Add(this.label_maxCnscPrft);
			this.panel_tradeAnlys.Controls.Add(this.label_maxCnscLssCnt);
			this.panel_tradeAnlys.Controls.Add(this.label_fee);
			this.panel_tradeAnlys.Controls.Add(this.label26);
			this.panel_tradeAnlys.Controls.Add(this.label_maxCnscWins);
			this.panel_tradeAnlys.Controls.Add(this.label_avgLoss);
			this.panel_tradeAnlys.Controls.Add(this.label_avgPrft);
			this.panel_tradeAnlys.Controls.Add(this.label_max1loss);
			this.panel_tradeAnlys.Controls.Add(this.label_max1prft);
			this.panel_tradeAnlys.Controls.Add(this.label_shtCount);
			this.panel_tradeAnlys.Controls.Add(this.label_lngCount);
			this.panel_tradeAnlys.Controls.Add(this.label_tradesCount);
			this.panel_tradeAnlys.Controls.Add(this.label_exptPayoff);
			this.panel_tradeAnlys.Controls.Add(this.label_netPrft);
			this.panel_tradeAnlys.Controls.Add(this.label_totalLoss);
			this.panel_tradeAnlys.Controls.Add(this.label_totalPrft);
			this.panel_tradeAnlys.Controls.Add(this.label32);
			this.panel_tradeAnlys.Controls.Add(this.label31);
			this.panel_tradeAnlys.Controls.Add(this.label30);
			this.panel_tradeAnlys.Controls.Add(this.label29);
			this.panel_tradeAnlys.Controls.Add(this.label28);
			this.panel_tradeAnlys.Controls.Add(this.label27);
			this.panel_tradeAnlys.Controls.Add(this.label25);
			this.panel_tradeAnlys.Controls.Add(this.label24);
			this.panel_tradeAnlys.Controls.Add(this.label23);
			this.panel_tradeAnlys.Controls.Add(this.label22);
			this.panel_tradeAnlys.Controls.Add(this.label21);
			this.panel_tradeAnlys.Controls.Add(this.label20);
			this.panel_tradeAnlys.Controls.Add(this.label19);
			this.panel_tradeAnlys.Controls.Add(this.label18);
			this.panel_tradeAnlys.Controls.Add(this.label17);
			this.panel_tradeAnlys.Controls.Add(this.label16);
			this.panel_tradeAnlys.Controls.Add(this.label15);
			this.panel_tradeAnlys.Controls.Add(this.label14);
			this.panel_tradeAnlys.Controls.Add(this.label13);
			this.panel_tradeAnlys.Controls.Add(this.label12);
			this.panel_tradeAnlys.Controls.Add(this.label11);
			this.panel_tradeAnlys.Controls.Add(this.label9);
			this.panel_tradeAnlys.Controls.Add(this.label8);
			this.panel_tradeAnlys.Controls.Add(this.label7);
			this.panel_tradeAnlys.DrawCustomBorder = false;
			this.panel_tradeAnlys.Location = new Point(6, 84);
			this.panel_tradeAnlys.Margin = new System.Windows.Forms.Padding(3, 3, 3, 12);
			this.panel_tradeAnlys.Name = "panel_tradeAnlys";
			this.panel_tradeAnlys.Size = new Size(845, 331);
			this.panel_tradeAnlys.TabIndex = 8;
			this.label_pIniBal.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_pIniBal.Location = new Point(412, 42);
			this.label_pIniBal.Name = "label_pIniBal";
			this.label_pIniBal.Size = new Size(130, 20);
			this.label_pIniBal.TabIndex = 54;
			this.label_pIniBal.Text = "label_pIniBal";
			this.label_pIniBal.TextAlign = ContentAlignment.MiddleRight;
			this.label47.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label47.Location = new Point(274, 42);
			this.label47.Name = "label47";
			this.label47.Size = new Size(140, 20);
			this.label47.TabIndex = 30;
			this.label47.Text = "期初权益：";
			this.label47.TextAlign = ContentAlignment.MiddleRight;
			this.label_prftRatio.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_prftRatio.Location = new Point(693, 42);
			this.label_prftRatio.Name = "label_prftRatio";
			this.label_prftRatio.Size = new Size(130, 20);
			this.label_prftRatio.TabIndex = 21;
			this.label_prftRatio.Text = "label_prftRatio";
			this.label_prftRatio.TextAlign = ContentAlignment.MiddleRight;
			this.label_盈利率.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_盈利率.Location = new Point(556, 42);
			this.label_盈利率.Name = "label_盈利率";
			this.label_盈利率.Size = new Size(144, 20);
			this.label_盈利率.TabIndex = 20;
			this.label_盈利率.Text = "盈利率：";
			this.label_盈利率.TextAlign = ContentAlignment.MiddleRight;
			this.label_maxPrft.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_maxPrft.Location = new Point(693, 126);
			this.label_maxPrft.Name = "label_maxPrft";
			this.label_maxPrft.Size = new Size(130, 20);
			this.label_maxPrft.TabIndex = 53;
			this.label_maxPrft.Text = "label_maxPrft";
			this.label_maxPrft.TextAlign = ContentAlignment.MiddleRight;
			this.label_maxDrwDownRatio.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_maxDrwDownRatio.Location = new Point(412, 126);
			this.label_maxDrwDownRatio.Name = "label_maxDrwDownRatio";
			this.label_maxDrwDownRatio.Size = new Size(130, 20);
			this.label_maxDrwDownRatio.TabIndex = 52;
			this.label_maxDrwDownRatio.Text = "label_maxDrwDownPerc";
			this.label_maxDrwDownRatio.TextAlign = ContentAlignment.MiddleRight;
			this.label43.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label43.Location = new Point(556, 126);
			this.label43.Name = "label43";
			this.label43.Size = new Size(144, 20);
			this.label43.TabIndex = 51;
			this.label43.Text = "最大盈利：";
			this.label43.TextAlign = ContentAlignment.MiddleRight;
			this.label42.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label42.Location = new Point(274, 126);
			this.label42.Name = "label42";
			this.label42.Size = new Size(140, 20);
			this.label42.TabIndex = 50;
			this.label42.Text = "最大回撤率：";
			this.label42.TextAlign = ContentAlignment.MiddleRight;
			this.label35.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label35.Location = new Point(556, 154);
			this.label35.Name = "label35";
			this.label35.Size = new Size(144, 20);
			this.label35.TabIndex = 49;
			this.label35.Text = "盈利/最大回撤：";
			this.label35.TextAlign = ContentAlignment.MiddleRight;
			this.label_maxDDPrftRatio.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_maxDDPrftRatio.Location = new Point(693, 154);
			this.label_maxDDPrftRatio.Name = "label_maxDDPrftRatio";
			this.label_maxDDPrftRatio.Size = new Size(130, 20);
			this.label_maxDDPrftRatio.TabIndex = 48;
			this.label_maxDDPrftRatio.Text = "label_maxDDPrftRatio";
			this.label_maxDDPrftRatio.TextAlign = ContentAlignment.MiddleRight;
			this.label_maxDrawDown.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_maxDrawDown.Location = new Point(412, 154);
			this.label_maxDrawDown.Name = "label_maxDrawDown";
			this.label_maxDrawDown.Size = new Size(130, 20);
			this.label_maxDrawDown.TabIndex = 47;
			this.label_maxDrawDown.Text = "label_maxDrawDown";
			this.label_maxDrawDown.TextAlign = ContentAlignment.MiddleRight;
			this.label_absDrawDown.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_absDrawDown.Location = new Point(136, 154);
			this.label_absDrawDown.Name = "label_absDrawDown";
			this.label_absDrawDown.Size = new Size(130, 20);
			this.label_absDrawDown.TabIndex = 46;
			this.label_absDrawDown.Text = "label_absDrawDown";
			this.label_absDrawDown.TextAlign = ContentAlignment.MiddleRight;
			this.label34.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label34.Location = new Point(274, 154);
			this.label34.Name = "label34";
			this.label34.Size = new Size(140, 20);
			this.label34.TabIndex = 6;
			this.label34.Text = "最大回撤额：";
			this.label34.TextAlign = ContentAlignment.MiddleRight;
			this.label33.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label33.Location = new Point(4, 154);
			this.label33.Name = "label33";
			this.label33.Size = new Size(140, 20);
			this.label33.TabIndex = 44;
			this.label33.Text = "最大绝对回撤：";
			this.label33.TextAlign = ContentAlignment.MiddleRight;
			this.label_lossCountRatio.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_lossCountRatio.ImageAlign = ContentAlignment.MiddleRight;
			this.label_lossCountRatio.Location = new Point(693, 98);
			this.label_lossCountRatio.Name = "label_lossCountRatio";
			this.label_lossCountRatio.Size = new Size(130, 20);
			this.label_lossCountRatio.TabIndex = 43;
			this.label_lossCountRatio.Text = "label_lossCountRatio";
			this.label_lossCountRatio.TextAlign = ContentAlignment.MiddleRight;
			this.label_winCountRatio.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_winCountRatio.Location = new Point(412, 98);
			this.label_winCountRatio.Name = "label_winCountRatio";
			this.label_winCountRatio.Size = new Size(130, 20);
			this.label_winCountRatio.TabIndex = 36;
			this.label_winCountRatio.Text = "label_winCountRatio";
			this.label_winCountRatio.TextAlign = ContentAlignment.MiddleRight;
			this.label_avgCnscLssCnt.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_avgCnscLssCnt.ImageAlign = ContentAlignment.MiddleRight;
			this.label_avgCnscLssCnt.Location = new Point(693, 294);
			this.label_avgCnscLssCnt.Name = "label_avgCnscLssCnt";
			this.label_avgCnscLssCnt.Size = new Size(130, 20);
			this.label_avgCnscLssCnt.TabIndex = 42;
			this.label_avgCnscLssCnt.Text = "label_avgCnscLssCnt";
			this.label_avgCnscLssCnt.TextAlign = ContentAlignment.MiddleRight;
			this.label_avgCnscWins.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_avgCnscWins.Location = new Point(412, 294);
			this.label_avgCnscWins.Name = "label_avgCnscWins";
			this.label_avgCnscWins.Size = new Size(130, 20);
			this.label_avgCnscWins.TabIndex = 41;
			this.label_avgCnscWins.Text = "label_avgCnscWins";
			this.label_avgCnscWins.TextAlign = ContentAlignment.MiddleRight;
			this.label_maxCnscLosses.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_maxCnscLosses.ImageAlign = ContentAlignment.MiddleRight;
			this.label_maxCnscLosses.Location = new Point(693, 266);
			this.label_maxCnscLosses.Name = "label_maxCnscLosses";
			this.label_maxCnscLosses.Size = new Size(130, 20);
			this.label_maxCnscLosses.TabIndex = 41;
			this.label_maxCnscLosses.Text = "label_maxCnscLosses";
			this.label_maxCnscLosses.TextAlign = ContentAlignment.MiddleRight;
			this.label_maxCnscPrft.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_maxCnscPrft.Location = new Point(412, 266);
			this.label_maxCnscPrft.Name = "label_maxCnscPrft";
			this.label_maxCnscPrft.Size = new Size(130, 20);
			this.label_maxCnscPrft.TabIndex = 40;
			this.label_maxCnscPrft.Text = "label_maxCnscPrft";
			this.label_maxCnscPrft.TextAlign = ContentAlignment.MiddleRight;
			this.label_maxCnscLssCnt.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_maxCnscLssCnt.ImageAlign = ContentAlignment.MiddleRight;
			this.label_maxCnscLssCnt.Location = new Point(693, 238);
			this.label_maxCnscLssCnt.Name = "label_maxCnscLssCnt";
			this.label_maxCnscLssCnt.Size = new Size(130, 20);
			this.label_maxCnscLssCnt.TabIndex = 40;
			this.label_maxCnscLssCnt.Text = "label_maxCnscLssCnt";
			this.label_maxCnscLssCnt.TextAlign = ContentAlignment.MiddleRight;
			this.label_fee.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_fee.ImageAlign = ContentAlignment.MiddleRight;
			this.label_fee.Location = new Point(136, 42);
			this.label_fee.Name = "label_fee";
			this.label_fee.Size = new Size(130, 20);
			this.label_fee.TabIndex = 31;
			this.label_fee.Text = "label_fee";
			this.label_fee.TextAlign = ContentAlignment.MiddleRight;
			this.label26.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label26.Location = new Point(4, 42);
			this.label26.Name = "label26";
			this.label26.Size = new Size(140, 20);
			this.label26.TabIndex = 19;
			this.label26.Text = "手续费：";
			this.label26.TextAlign = ContentAlignment.MiddleRight;
			this.label_maxCnscWins.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_maxCnscWins.Location = new Point(412, 238);
			this.label_maxCnscWins.Name = "label_maxCnscWins";
			this.label_maxCnscWins.Size = new Size(130, 20);
			this.label_maxCnscWins.TabIndex = 39;
			this.label_maxCnscWins.Text = "label_maxCnscWins";
			this.label_maxCnscWins.TextAlign = ContentAlignment.MiddleRight;
			this.label_avgLoss.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_avgLoss.ImageAlign = ContentAlignment.MiddleRight;
			this.label_avgLoss.Location = new Point(693, 210);
			this.label_avgLoss.Name = "label_avgLoss";
			this.label_avgLoss.Size = new Size(130, 20);
			this.label_avgLoss.TabIndex = 38;
			this.label_avgLoss.Text = "label_avgLoss";
			this.label_avgLoss.TextAlign = ContentAlignment.MiddleRight;
			this.label_avgPrft.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_avgPrft.Location = new Point(412, 210);
			this.label_avgPrft.Name = "label_avgPrft";
			this.label_avgPrft.Size = new Size(130, 20);
			this.label_avgPrft.TabIndex = 37;
			this.label_avgPrft.Text = "label_avgPrft";
			this.label_avgPrft.TextAlign = ContentAlignment.MiddleRight;
			this.label_max1loss.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_max1loss.ImageAlign = ContentAlignment.MiddleRight;
			this.label_max1loss.Location = new Point(693, 182);
			this.label_max1loss.Name = "label_max1loss";
			this.label_max1loss.Size = new Size(130, 20);
			this.label_max1loss.TabIndex = 36;
			this.label_max1loss.Text = "label_max1loss";
			this.label_max1loss.TextAlign = ContentAlignment.MiddleRight;
			this.label_max1prft.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_max1prft.Location = new Point(412, 182);
			this.label_max1prft.Name = "label_max1prft";
			this.label_max1prft.Size = new Size(130, 20);
			this.label_max1prft.TabIndex = 35;
			this.label_max1prft.Text = "label_max1prft";
			this.label_max1prft.TextAlign = ContentAlignment.MiddleRight;
			this.label_shtCount.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_shtCount.ImageAlign = ContentAlignment.MiddleRight;
			this.label_shtCount.Location = new Point(693, 70);
			this.label_shtCount.Name = "label_shtCount";
			this.label_shtCount.Size = new Size(130, 20);
			this.label_shtCount.TabIndex = 34;
			this.label_shtCount.Text = "label_shtCount";
			this.label_shtCount.TextAlign = ContentAlignment.MiddleRight;
			this.label_lngCount.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_lngCount.Location = new Point(412, 70);
			this.label_lngCount.Name = "label_lngCount";
			this.label_lngCount.Size = new Size(130, 20);
			this.label_lngCount.TabIndex = 33;
			this.label_lngCount.Text = "label_lngCount";
			this.label_lngCount.TextAlign = ContentAlignment.MiddleRight;
			this.label_tradesCount.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_tradesCount.Location = new Point(136, 98);
			this.label_tradesCount.Name = "label_tradesCount";
			this.label_tradesCount.Size = new Size(130, 20);
			this.label_tradesCount.TabIndex = 32;
			this.label_tradesCount.Text = "label_tradesCount";
			this.label_tradesCount.TextAlign = ContentAlignment.MiddleRight;
			this.label_exptPayoff.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_exptPayoff.Location = new Point(136, 70);
			this.label_exptPayoff.Name = "label_exptPayoff";
			this.label_exptPayoff.Size = new Size(130, 20);
			this.label_exptPayoff.TabIndex = 30;
			this.label_exptPayoff.Text = "label_exptPayoff";
			this.label_exptPayoff.TextAlign = ContentAlignment.MiddleRight;
			this.label_netPrft.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_netPrft.ImageAlign = ContentAlignment.MiddleRight;
			this.label_netPrft.Location = new Point(693, 14);
			this.label_netPrft.Name = "label_netPrft";
			this.label_netPrft.Size = new Size(130, 20);
			this.label_netPrft.TabIndex = 28;
			this.label_netPrft.Text = "label_netPrft";
			this.label_netPrft.TextAlign = ContentAlignment.MiddleRight;
			this.label_totalLoss.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_totalLoss.Location = new Point(412, 14);
			this.label_totalLoss.Name = "label_totalLoss";
			this.label_totalLoss.Size = new Size(130, 20);
			this.label_totalLoss.TabIndex = 27;
			this.label_totalLoss.Text = "label_totalLoss";
			this.label_totalLoss.TextAlign = ContentAlignment.MiddleRight;
			this.label_totalPrft.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label_totalPrft.Location = new Point(136, 14);
			this.label_totalPrft.Name = "label_totalPrft";
			this.label_totalPrft.Size = new Size(130, 20);
			this.label_totalPrft.TabIndex = 26;
			this.label_totalPrft.Text = "label_totalPrft";
			this.label_totalPrft.TextAlign = ContentAlignment.MiddleRight;
			this.label32.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label32.Location = new Point(556, 294);
			this.label32.Name = "label32";
			this.label32.Size = new Size(144, 20);
			this.label32.TabIndex = 25;
			this.label32.Text = "亏损笔数：";
			this.label32.TextAlign = ContentAlignment.MiddleRight;
			this.label31.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label31.Location = new Point(274, 294);
			this.label31.Name = "label31";
			this.label31.Size = new Size(140, 20);
			this.label31.TabIndex = 24;
			this.label31.Text = "盈利笔数：";
			this.label31.TextAlign = ContentAlignment.MiddleRight;
			this.label30.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label30.Location = new Point(4, 294);
			this.label30.Name = "label30";
			this.label30.Size = new Size(140, 20);
			this.label30.TabIndex = 23;
			this.label30.Text = "平均连续：";
			this.label30.TextAlign = ContentAlignment.MiddleRight;
			this.label29.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label29.Location = new Point(556, 266);
			this.label29.Name = "label29";
			this.label29.Size = new Size(144, 20);
			this.label29.TabIndex = 22;
			this.label29.Text = "亏损金额：";
			this.label29.TextAlign = ContentAlignment.MiddleRight;
			this.label28.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label28.Location = new Point(274, 266);
			this.label28.Name = "label28";
			this.label28.Size = new Size(140, 20);
			this.label28.TabIndex = 21;
			this.label28.Text = "盈利金额：";
			this.label28.TextAlign = ContentAlignment.MiddleRight;
			this.label27.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label27.Location = new Point(4, 266);
			this.label27.Name = "label27";
			this.label27.Size = new Size(140, 20);
			this.label27.TabIndex = 20;
			this.label27.Text = "最大连续：";
			this.label27.TextAlign = ContentAlignment.MiddleRight;
			this.label25.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label25.Location = new Point(556, 238);
			this.label25.Name = "label25";
			this.label25.Size = new Size(144, 20);
			this.label25.TabIndex = 18;
			this.label25.Text = "亏损笔数($)：";
			this.label25.TextAlign = ContentAlignment.MiddleRight;
			this.label24.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label24.Location = new Point(274, 238);
			this.label24.Name = "label24";
			this.label24.Size = new Size(140, 20);
			this.label24.TabIndex = 17;
			this.label24.Text = "盈利笔数($)：";
			this.label24.TextAlign = ContentAlignment.MiddleRight;
			this.label23.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label23.Location = new Point(4, 238);
			this.label23.Name = "label23";
			this.label23.Size = new Size(140, 20);
			this.label23.TabIndex = 16;
			this.label23.Text = "最大连续：";
			this.label23.TextAlign = ContentAlignment.MiddleRight;
			this.label22.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label22.Location = new Point(556, 210);
			this.label22.Name = "label22";
			this.label22.Size = new Size(144, 20);
			this.label22.TabIndex = 15;
			this.label22.Text = "单笔亏损：";
			this.label22.TextAlign = ContentAlignment.MiddleRight;
			this.label21.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label21.Location = new Point(274, 210);
			this.label21.Name = "label21";
			this.label21.Size = new Size(140, 20);
			this.label21.TabIndex = 14;
			this.label21.Text = "单笔盈利：";
			this.label21.TextAlign = ContentAlignment.MiddleRight;
			this.label20.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label20.Location = new Point(4, 210);
			this.label20.Name = "label20";
			this.label20.Size = new Size(140, 20);
			this.label20.TabIndex = 13;
			this.label20.Text = "平均：";
			this.label20.TextAlign = ContentAlignment.MiddleRight;
			this.label19.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label19.Location = new Point(4, 182);
			this.label19.Name = "label19";
			this.label19.Size = new Size(140, 20);
			this.label19.TabIndex = 12;
			this.label19.Text = "最大：";
			this.label19.TextAlign = ContentAlignment.MiddleRight;
			this.label18.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label18.Location = new Point(556, 182);
			this.label18.Name = "label18";
			this.label18.Size = new Size(144, 20);
			this.label18.TabIndex = 11;
			this.label18.Text = "单笔亏损：";
			this.label18.TextAlign = ContentAlignment.MiddleRight;
			this.label17.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label17.Location = new Point(274, 182);
			this.label17.Name = "label17";
			this.label17.Size = new Size(140, 20);
			this.label17.TabIndex = 10;
			this.label17.Text = "单笔盈利：";
			this.label17.TextAlign = ContentAlignment.MiddleRight;
			this.label16.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label16.Location = new Point(556, 98);
			this.label16.Name = "label16";
			this.label16.Size = new Size(144, 20);
			this.label16.TabIndex = 9;
			this.label16.Text = "亏损笔数(%)：";
			this.label16.TextAlign = ContentAlignment.MiddleRight;
			this.label15.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label15.Location = new Point(274, 98);
			this.label15.Name = "label15";
			this.label15.Size = new Size(140, 20);
			this.label15.TabIndex = 8;
			this.label15.Text = "盈利笔数(%)：";
			this.label15.TextAlign = ContentAlignment.MiddleRight;
			this.label14.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label14.Location = new Point(556, 70);
			this.label14.Name = "label14";
			this.label14.Size = new Size(144, 20);
			this.label14.TabIndex = 7;
			this.label14.Text = "卖开(赢率%)：";
			this.label14.TextAlign = ContentAlignment.MiddleRight;
			this.label13.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label13.Location = new Point(274, 70);
			this.label13.Name = "label13";
			this.label13.Size = new Size(140, 20);
			this.label13.TabIndex = 6;
			this.label13.Text = "买开(赢率%)：";
			this.label13.TextAlign = ContentAlignment.MiddleRight;
			this.label12.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label12.Location = new Point(4, 98);
			this.label12.Name = "label12";
			this.label12.Size = new Size(140, 20);
			this.label12.TabIndex = 5;
			this.label12.Text = "总交易笔数：";
			this.label12.TextAlign = ContentAlignment.MiddleRight;
			this.label11.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label11.Location = new Point(4, 70);
			this.label11.Name = "label11";
			this.label11.Size = new Size(140, 20);
			this.label11.TabIndex = 4;
			this.label11.Text = "期望盈利：";
			this.label11.TextAlign = ContentAlignment.MiddleRight;
			this.label9.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label9.Location = new Point(556, 14);
			this.label9.Name = "label9";
			this.label9.Size = new Size(144, 20);
			this.label9.TabIndex = 2;
			this.label9.Text = "净盈利：";
			this.label9.TextAlign = ContentAlignment.MiddleRight;
			this.label8.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label8.Location = new Point(274, 14);
			this.label8.Name = "label8";
			this.label8.Size = new Size(140, 20);
			this.label8.TabIndex = 1;
			this.label8.Text = "总亏损：";
			this.label8.TextAlign = ContentAlignment.MiddleRight;
			this.label7.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.label7.Location = new Point(4, 14);
			this.label7.Name = "label7";
			this.label7.Size = new Size(140, 20);
			this.label7.TabIndex = 0;
			this.label7.Text = "总盈利：";
			this.label7.TextAlign = ContentAlignment.MiddleRight;
			this.tabPg_Report.AttachedControl = this.tabControlPanel_Report;
			this.tabPg_Report.Name = "tabPg_Report";
			this.tabPg_Report.Text = "交易统计";
			this.tabControlPanel_EquityGraph.AutoScroll = true;
			this.tabControlPanel_EquityGraph.Dock = DockStyle.Fill;
			this.tabControlPanel_EquityGraph.Location = new Point(0, 0);
			this.tabControlPanel_EquityGraph.Name = "tabControlPanel_EquityGraph";
			this.tabControlPanel_EquityGraph.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_EquityGraph.Size = new Size(1189, 648);
			this.tabControlPanel_EquityGraph.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_EquityGraph.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_EquityGraph.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_EquityGraph.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_EquityGraph.Style.GradientAngle = -90;
			this.tabControlPanel_EquityGraph.TabIndex = 6;
			this.tabControlPanel_EquityGraph.TabItem = this.tabPg_EquityGraph;
			this.tabPg_EquityGraph.AttachedControl = this.tabControlPanel_EquityGraph;
			this.tabPg_EquityGraph.Name = "tabPg_EquityGraph";
			this.tabPg_EquityGraph.Text = "权益走势";
			this.tabControlPanel_PrftLine.Dock = DockStyle.Fill;
			this.tabControlPanel_PrftLine.Location = new Point(0, 0);
			this.tabControlPanel_PrftLine.Name = "tabControlPanel_PrftLine";
			this.tabControlPanel_PrftLine.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_PrftLine.Size = new Size(1189, 648);
			this.tabControlPanel_PrftLine.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_PrftLine.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_PrftLine.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_PrftLine.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_PrftLine.Style.GradientAngle = -90;
			this.tabControlPanel_PrftLine.TabIndex = 10;
			this.tabControlPanel_PrftLine.TabItem = this.tabPg_PrftLine;
			this.tabPg_PrftLine.AttachedControl = this.tabControlPanel_PrftLine;
			this.tabPg_PrftLine.Name = "tabPg_PrftLine";
			this.tabPg_PrftLine.Text = "盈利曲线";
			this.tabControlPanel_SymbPrft.AutoScroll = true;
			this.tabControlPanel_SymbPrft.Dock = DockStyle.Fill;
			this.tabControlPanel_SymbPrft.Location = new Point(0, 0);
			this.tabControlPanel_SymbPrft.Name = "tabControlPanel_SymbPrft";
			this.tabControlPanel_SymbPrft.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_SymbPrft.Size = new Size(1189, 648);
			this.tabControlPanel_SymbPrft.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_SymbPrft.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_SymbPrft.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_SymbPrft.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_SymbPrft.Style.GradientAngle = -90;
			this.tabControlPanel_SymbPrft.TabIndex = 7;
			this.tabControlPanel_SymbPrft.TabItem = this.tabPg_SymbPrft;
			this.tabPg_SymbPrft.AttachedControl = this.tabControlPanel_SymbPrft;
			this.tabPg_SymbPrft.Name = "tabPg_SymbPrft";
			this.tabPg_SymbPrft.Text = "品种盈亏";
			this.tabControlPanel_TimePrft.Dock = DockStyle.Fill;
			this.tabControlPanel_TimePrft.Location = new Point(0, 0);
			this.tabControlPanel_TimePrft.Name = "tabControlPanel_TimePrft";
			this.tabControlPanel_TimePrft.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_TimePrft.Size = new Size(1189, 648);
			this.tabControlPanel_TimePrft.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_TimePrft.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_TimePrft.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_TimePrft.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_TimePrft.Style.GradientAngle = -90;
			this.tabControlPanel_TimePrft.TabIndex = 9;
			this.tabControlPanel_TimePrft.TabItem = this.tabPg_TimePrft;
			this.tabPg_TimePrft.AttachedControl = this.tabControlPanel_TimePrft;
			this.tabPg_TimePrft.Name = "tabPg_TimePrft";
			this.tabPg_TimePrft.Text = "时间盈亏";
			this.tabControlPanel_LngShrtPrft.Controls.Add(this.splitContainer_LngShrtPrft);
			this.tabControlPanel_LngShrtPrft.Dock = DockStyle.Fill;
			this.tabControlPanel_LngShrtPrft.Location = new Point(0, 0);
			this.tabControlPanel_LngShrtPrft.Name = "tabControlPanel_LngShrtPrft";
			this.tabControlPanel_LngShrtPrft.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_LngShrtPrft.Size = new Size(1189, 648);
			this.tabControlPanel_LngShrtPrft.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_LngShrtPrft.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_LngShrtPrft.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_LngShrtPrft.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_LngShrtPrft.Style.GradientAngle = -90;
			this.tabControlPanel_LngShrtPrft.TabIndex = 8;
			this.tabControlPanel_LngShrtPrft.TabItem = this.tabPg_LngShrtPrft;
			this.splitContainer_LngShrtPrft.Dock = DockStyle.Fill;
			this.splitContainer_LngShrtPrft.IsSplitterFixed = true;
			this.splitContainer_LngShrtPrft.Location = new Point(1, 1);
			this.splitContainer_LngShrtPrft.Name = "splitContainer_LngShrtPrft";
			this.splitContainer_LngShrtPrft.Size = new Size(1187, 646);
			this.splitContainer_LngShrtPrft.SplitterDistance = 585;
			this.splitContainer_LngShrtPrft.SplitterWidth = 1;
			this.splitContainer_LngShrtPrft.TabIndex = 0;
			this.tabPg_LngShrtPrft.AttachedControl = this.tabControlPanel_LngShrtPrft;
			this.tabPg_LngShrtPrft.Name = "tabPg_LngShrtPrft";
			this.tabPg_LngShrtPrft.Text = "多空盈亏";
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.tabCtrl_Acct);
			base.Name = "TrdAnalysisPanel";
			base.Size = new Size(1189, 676);
			((ISupportInitialize)this.tabCtrl_Acct).EndInit();
			this.tabCtrl_Acct.ResumeLayout(false);
			this.tabControlPanel_Acct.ResumeLayout(false);
			this.panel_AcctInfo.ResumeLayout(false);
			this.tabControlPanel_Report.ResumeLayout(false);
			this.panel_TradingStat.ResumeLayout(false);
			this.panel_TradeAnlys_Header.ResumeLayout(false);
			this.panel_TradeAnlys_Sels.ResumeLayout(false);
			this.panel_tradeAnlys.ResumeLayout(false);
			this.tabControlPanel_LngShrtPrft.ResumeLayout(false);
			this.splitContainer_LngShrtPrft.ResumeLayout(false);
			base.ResumeLayout(false);
		}

		// Token: 0x04000EE5 RID: 3813
		private int int_0;

		// Token: 0x04000EE6 RID: 3814
		private Font font_0;

		// Token: 0x04000EE7 RID: 3815
		private Font font_1;

		// Token: 0x04000EE8 RID: 3816
		private Color[] color_0 = new Color[]
		{
			Color.Blue,
			Color.LightSalmon,
			Color.LightSeaGreen,
			Color.LimeGreen,
			Color.Aqua,
			Color.BurlyWood,
			Color.MediumOrchid,
			Color.Coral,
			Color.CornflowerBlue,
			Color.MistyRose,
			Color.DarkGray,
			Color.DarkKhaki,
			Color.DarkMagenta,
			Color.DarkOrange,
			Color.DarkOrchid,
			Color.DarkRed,
			Color.DarkSalmon,
			Color.PaleVioletRed,
			Color.PaleVioletRed,
			Color.PeachPuff,
			Color.DarkViolet,
			Color.DeepPink,
			Color.DeepSkyBlue,
			Color.Yellow,
			Color.YellowGreen,
			Color.Wheat,
			Color.Peru,
			Color.DarkSlateGray,
			Color.DarkSeaGreen,
			Color.DodgerBlue,
			Color.PowderBlue,
			Color.IndianRed,
			Color.LightCyan,
			Color.Firebrick,
			Color.DarkTurquoise,
			Color.OliveDrab,
			Color.DarkSlateBlue,
			Color.Maroon
		};

		// Token: 0x04000EE9 RID: 3817
		private GraphCtrlStat graphCtrlStat_0;

		// Token: 0x04000EEA RID: 3818
		private List<TradingSymbol> list_0;

		// Token: 0x04000EEB RID: 3819
		private IContainer icontainer_0;

		// Token: 0x04000EEC RID: 3820
		private Control2 tabCtrl_Acct;

		// Token: 0x04000EED RID: 3821
		private TabControlPanel tabControlPanel_Acct;

		// Token: 0x04000EEE RID: 3822
		private Panel panel_AcctInfo;

		// Token: 0x04000EEF RID: 3823
		private System.Windows.Forms.Label label_UsedMargin;

		// Token: 0x04000EF0 RID: 3824
		private System.Windows.Forms.Label label_acct_账户;

		// Token: 0x04000EF1 RID: 3825
		private System.Windows.Forms.Label label_Profit;

		// Token: 0x04000EF2 RID: 3826
		private System.Windows.Forms.Label label_acct_动态权益;

		// Token: 0x04000EF3 RID: 3827
		private System.Windows.Forms.Label label_FreeMargin;

		// Token: 0x04000EF4 RID: 3828
		private System.Windows.Forms.Label label_acct_初始资金;

		// Token: 0x04000EF5 RID: 3829
		private System.Windows.Forms.Label label_FeeSum;

		// Token: 0x04000EF6 RID: 3830
		private System.Windows.Forms.Label label_acct_可用资金;

		// Token: 0x04000EF7 RID: 3831
		private System.Windows.Forms.Label label_DynaProfit;

		// Token: 0x04000EF8 RID: 3832
		private System.Windows.Forms.Label label_acct_浮动盈亏;

		// Token: 0x04000EF9 RID: 3833
		private System.Windows.Forms.Label label_IniBalance;

		// Token: 0x04000EFA RID: 3834
		private System.Windows.Forms.Label label_acct_平仓盈亏;

		// Token: 0x04000EFB RID: 3835
		private System.Windows.Forms.Label label_CurrEquity;

		// Token: 0x04000EFC RID: 3836
		private System.Windows.Forms.Label label_acct_手续费;

		// Token: 0x04000EFD RID: 3837
		private System.Windows.Forms.Label label_AcctName;

		// Token: 0x04000EFE RID: 3838
		private System.Windows.Forms.Label label_acct_保证金;

		// Token: 0x04000EFF RID: 3839
		private TabItem tabPg_Acct;

		// Token: 0x04000F00 RID: 3840
		private TabControlPanel tabControlPanel_Report;

		// Token: 0x04000F01 RID: 3841
		private Panel panel_TradingStat;

		// Token: 0x04000F02 RID: 3842
		private Class296 panel_TradeAnlys_Header;

		// Token: 0x04000F03 RID: 3843
		private System.Windows.Forms.Label label_floatPrft;

		// Token: 0x04000F04 RID: 3844
		private System.Windows.Forms.Label label1;

		// Token: 0x04000F05 RID: 3845
		private System.Windows.Forms.Label label2;

		// Token: 0x04000F06 RID: 3846
		private System.Windows.Forms.Label label4;

		// Token: 0x04000F07 RID: 3847
		private System.Windows.Forms.Label label6;

		// Token: 0x04000F08 RID: 3848
		private System.Windows.Forms.Label label_equity;

		// Token: 0x04000F09 RID: 3849
		private System.Windows.Forms.Label label_accnt;

		// Token: 0x04000F0A RID: 3850
		private System.Windows.Forms.Label label_iniBal;

		// Token: 0x04000F0B RID: 3851
		private Class296 panel_TradeAnlys_Sels;

		// Token: 0x04000F0C RID: 3852
		private ComboBox comboBox_Year;

		// Token: 0x04000F0D RID: 3853
		private ComboBox comboBox_tradingOverview_CurrOrAll;

		// Token: 0x04000F0E RID: 3854
		private ComboBox comboBox_Symbl;

		// Token: 0x04000F0F RID: 3855
		private System.Windows.Forms.Label label_年份;

		// Token: 0x04000F10 RID: 3856
		private System.Windows.Forms.Label label_品种h;

		// Token: 0x04000F11 RID: 3857
		private System.Windows.Forms.Label label_月份;

		// Token: 0x04000F12 RID: 3858
		private ComboBox comboBox_Month;

		// Token: 0x04000F13 RID: 3859
		private GroupBox groupBox2;

		// Token: 0x04000F14 RID: 3860
		private Class296 panel_tradeAnlys;

		// Token: 0x04000F15 RID: 3861
		private System.Windows.Forms.Label label_pIniBal;

		// Token: 0x04000F16 RID: 3862
		private System.Windows.Forms.Label label47;

		// Token: 0x04000F17 RID: 3863
		private System.Windows.Forms.Label label_prftRatio;

		// Token: 0x04000F18 RID: 3864
		private System.Windows.Forms.Label label_盈利率;

		// Token: 0x04000F19 RID: 3865
		private System.Windows.Forms.Label label_maxPrft;

		// Token: 0x04000F1A RID: 3866
		private System.Windows.Forms.Label label_maxDrwDownRatio;

		// Token: 0x04000F1B RID: 3867
		private System.Windows.Forms.Label label43;

		// Token: 0x04000F1C RID: 3868
		private System.Windows.Forms.Label label42;

		// Token: 0x04000F1D RID: 3869
		private System.Windows.Forms.Label label35;

		// Token: 0x04000F1E RID: 3870
		private System.Windows.Forms.Label label_maxDDPrftRatio;

		// Token: 0x04000F1F RID: 3871
		private System.Windows.Forms.Label label_maxDrawDown;

		// Token: 0x04000F20 RID: 3872
		private System.Windows.Forms.Label label_absDrawDown;

		// Token: 0x04000F21 RID: 3873
		private System.Windows.Forms.Label label34;

		// Token: 0x04000F22 RID: 3874
		private System.Windows.Forms.Label label33;

		// Token: 0x04000F23 RID: 3875
		private System.Windows.Forms.Label label_lossCountRatio;

		// Token: 0x04000F24 RID: 3876
		private System.Windows.Forms.Label label_winCountRatio;

		// Token: 0x04000F25 RID: 3877
		private System.Windows.Forms.Label label_avgCnscLssCnt;

		// Token: 0x04000F26 RID: 3878
		private System.Windows.Forms.Label label_avgCnscWins;

		// Token: 0x04000F27 RID: 3879
		private System.Windows.Forms.Label label_maxCnscLosses;

		// Token: 0x04000F28 RID: 3880
		private System.Windows.Forms.Label label_maxCnscPrft;

		// Token: 0x04000F29 RID: 3881
		private System.Windows.Forms.Label label_maxCnscLssCnt;

		// Token: 0x04000F2A RID: 3882
		private System.Windows.Forms.Label label_fee;

		// Token: 0x04000F2B RID: 3883
		private System.Windows.Forms.Label label26;

		// Token: 0x04000F2C RID: 3884
		private System.Windows.Forms.Label label_maxCnscWins;

		// Token: 0x04000F2D RID: 3885
		private System.Windows.Forms.Label label_avgLoss;

		// Token: 0x04000F2E RID: 3886
		private System.Windows.Forms.Label label_avgPrft;

		// Token: 0x04000F2F RID: 3887
		private System.Windows.Forms.Label label_max1loss;

		// Token: 0x04000F30 RID: 3888
		private System.Windows.Forms.Label label_max1prft;

		// Token: 0x04000F31 RID: 3889
		private System.Windows.Forms.Label label_shtCount;

		// Token: 0x04000F32 RID: 3890
		private System.Windows.Forms.Label label_lngCount;

		// Token: 0x04000F33 RID: 3891
		private System.Windows.Forms.Label label_tradesCount;

		// Token: 0x04000F34 RID: 3892
		private System.Windows.Forms.Label label_exptPayoff;

		// Token: 0x04000F35 RID: 3893
		private System.Windows.Forms.Label label_netPrft;

		// Token: 0x04000F36 RID: 3894
		private System.Windows.Forms.Label label_totalLoss;

		// Token: 0x04000F37 RID: 3895
		private System.Windows.Forms.Label label_totalPrft;

		// Token: 0x04000F38 RID: 3896
		private System.Windows.Forms.Label label32;

		// Token: 0x04000F39 RID: 3897
		private System.Windows.Forms.Label label31;

		// Token: 0x04000F3A RID: 3898
		private System.Windows.Forms.Label label30;

		// Token: 0x04000F3B RID: 3899
		private System.Windows.Forms.Label label29;

		// Token: 0x04000F3C RID: 3900
		private System.Windows.Forms.Label label28;

		// Token: 0x04000F3D RID: 3901
		private System.Windows.Forms.Label label27;

		// Token: 0x04000F3E RID: 3902
		private System.Windows.Forms.Label label25;

		// Token: 0x04000F3F RID: 3903
		private System.Windows.Forms.Label label24;

		// Token: 0x04000F40 RID: 3904
		private System.Windows.Forms.Label label23;

		// Token: 0x04000F41 RID: 3905
		private System.Windows.Forms.Label label22;

		// Token: 0x04000F42 RID: 3906
		private System.Windows.Forms.Label label21;

		// Token: 0x04000F43 RID: 3907
		private System.Windows.Forms.Label label20;

		// Token: 0x04000F44 RID: 3908
		private System.Windows.Forms.Label label19;

		// Token: 0x04000F45 RID: 3909
		private System.Windows.Forms.Label label18;

		// Token: 0x04000F46 RID: 3910
		private System.Windows.Forms.Label label17;

		// Token: 0x04000F47 RID: 3911
		private System.Windows.Forms.Label label16;

		// Token: 0x04000F48 RID: 3912
		private System.Windows.Forms.Label label15;

		// Token: 0x04000F49 RID: 3913
		private System.Windows.Forms.Label label14;

		// Token: 0x04000F4A RID: 3914
		private System.Windows.Forms.Label label13;

		// Token: 0x04000F4B RID: 3915
		private System.Windows.Forms.Label label12;

		// Token: 0x04000F4C RID: 3916
		private System.Windows.Forms.Label label11;

		// Token: 0x04000F4D RID: 3917
		private System.Windows.Forms.Label label9;

		// Token: 0x04000F4E RID: 3918
		private System.Windows.Forms.Label label8;

		// Token: 0x04000F4F RID: 3919
		private System.Windows.Forms.Label label7;

		// Token: 0x04000F50 RID: 3920
		private TabItem tabPg_Report;

		// Token: 0x04000F51 RID: 3921
		private TabControlPanel tabControlPanel_EquityGraph;

		// Token: 0x04000F52 RID: 3922
		private TabItem tabPg_EquityGraph;

		// Token: 0x04000F53 RID: 3923
		private TabControlPanel tabControlPanel_PrftLine;

		// Token: 0x04000F54 RID: 3924
		private TabItem tabPg_PrftLine;

		// Token: 0x04000F55 RID: 3925
		private TabControlPanel tabControlPanel_SymbPrft;

		// Token: 0x04000F56 RID: 3926
		private TabItem tabPg_SymbPrft;

		// Token: 0x04000F57 RID: 3927
		private TabControlPanel tabControlPanel_TimePrft;

		// Token: 0x04000F58 RID: 3928
		private TabItem tabPg_TimePrft;

		// Token: 0x04000F59 RID: 3929
		private TabControlPanel tabControlPanel_LngShrtPrft;

		// Token: 0x04000F5A RID: 3930
		private SplitContainer splitContainer_LngShrtPrft;

		// Token: 0x04000F5B RID: 3931
		private TabItem tabPg_LngShrtPrft;

		// Token: 0x020002AE RID: 686
		// (Invoke) Token: 0x06001E76 RID: 7798
		private delegate void Delegate34(Control ctrl, string strshow);

		// Token: 0x020002AF RID: 687
		// (Invoke) Token: 0x06001E7A RID: 7802
		private delegate void Delegate35(Control ctrl, Color color);

		// Token: 0x020002B0 RID: 688
		[CompilerGenerated]
		private sealed class Class349
		{
			// Token: 0x04000F5C RID: 3932
			public int? nullable_0;
		}

		// Token: 0x020002B2 RID: 690
		[CompilerGenerated]
		private sealed class Class350
		{
			// Token: 0x04000F71 RID: 3953
			public int? nullable_0;
		}

		// Token: 0x020002B3 RID: 691
		[CompilerGenerated]
		private sealed class Class351
		{
			// Token: 0x04000F72 RID: 3954
			public int? nullable_0;
		}

		// Token: 0x020002B4 RID: 692
		[CompilerGenerated]
		private sealed class Class352
		{
			// Token: 0x06001E96 RID: 7830 RVA: 0x000D6A7C File Offset: 0x000D4C7C
			internal bool method_0(Transaction transaction_0)
			{
				return Base.Trading.smethod_215(transaction_0.SymbolID) == this.tradingSymbol_0;
			}

			// Token: 0x04000F73 RID: 3955
			public TradingSymbol tradingSymbol_0;
		}

		// Token: 0x020002B5 RID: 693
		[CompilerGenerated]
		private sealed class Class353
		{
			// Token: 0x06001E98 RID: 7832 RVA: 0x000D6AA0 File Offset: 0x000D4CA0
			internal bool method_0(CurveItem curveItem_0)
			{
				return curveItem_0.Tag == this.tradingSymbol_0;
			}

			// Token: 0x06001E99 RID: 7833 RVA: 0x000D6AA0 File Offset: 0x000D4CA0
			internal bool method_1(CurveItem curveItem_0)
			{
				return curveItem_0.Tag == this.tradingSymbol_0;
			}

			// Token: 0x04000F74 RID: 3956
			public TradingSymbol tradingSymbol_0;
		}
	}
}
