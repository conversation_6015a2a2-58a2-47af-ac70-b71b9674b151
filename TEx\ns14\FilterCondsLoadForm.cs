﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns23;
using ns3;
using TEx;
using TEx.Comn;
using TEx.Util;

namespace ns14
{
	// Token: 0x02000093 RID: 147
	internal sealed partial class FilterCondsLoadForm : Form
	{
		// Token: 0x1400001E RID: 30
		// (add) Token: 0x060004D7 RID: 1239 RVA: 0x000261B4 File Offset: 0x000243B4
		// (remove) Token: 0x060004D8 RID: 1240 RVA: 0x000261EC File Offset: 0x000243EC
		public event MsgEventHandler CondGroupSelected
		{
			[CompilerGenerated]
			add
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Combine(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Remove(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
		}

		// Token: 0x060004D9 RID: 1241 RVA: 0x0000423A File Offset: 0x0000243A
		protected void method_0(string string_0, List<FilterCond> list_0)
		{
			MsgEventHandler msgEventHandler = this.msgEventHandler_0;
			if (msgEventHandler != null)
			{
				msgEventHandler(this, new MsgEventArgs(string_0, list_0));
			}
		}

		// Token: 0x060004DA RID: 1242 RVA: 0x00026224 File Offset: 0x00024424
		public FilterCondsLoadForm()
		{
			this.InitializeComponent();
			base.Load += this.FilterCondsLoadForm_Load;
			this.button_OK.Click += this.button_OK_Click;
			this.button_Cancel.Click += this.button_Cancel_Click;
		}

		// Token: 0x060004DB RID: 1243 RVA: 0x00004257 File Offset: 0x00002457
		public FilterCondsLoadForm(Class300 class300_1) : this()
		{
			this.FilterCondsUserCfg = class300_1;
		}

		// Token: 0x060004DC RID: 1244 RVA: 0x00026280 File Offset: 0x00024480
		private void FilterCondsLoadForm_Load(object sender, EventArgs e)
		{
			if (this.FilterCondsUserCfg != null)
			{
				List<Class301> condGroups = this.FilterCondsUserCfg.CondGroups;
				if (condGroups != null && condGroups.Count > 0)
				{
					List<ComboBoxItem> list = new List<ComboBoxItem>();
					foreach (Class301 @class in condGroups)
					{
						ComboBoxItem item = new ComboBoxItem(@class.Name, @class.FilterConds);
						list.Add(item);
					}
					this.listBox.DataSource = list;
					this.listBox.SelectedIndex = 0;
				}
			}
		}

		// Token: 0x060004DD RID: 1245 RVA: 0x00026324 File Offset: 0x00024524
		private void button_OK_Click(object sender, EventArgs e)
		{
			ComboBoxItem comboBoxItem = this.listBox.SelectedItem as ComboBoxItem;
			this.method_0(comboBoxItem.Text, comboBoxItem.Value as List<FilterCond>);
			base.Close();
		}

		// Token: 0x060004DE RID: 1246 RVA: 0x00004268 File Offset: 0x00002468
		private void button_Cancel_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x17000100 RID: 256
		// (get) Token: 0x060004DF RID: 1247 RVA: 0x00026364 File Offset: 0x00024564
		// (set) Token: 0x060004E0 RID: 1248 RVA: 0x00004272 File Offset: 0x00002472
		public Class300 FilterCondsUserCfg { get; set; }

		// Token: 0x060004E1 RID: 1249 RVA: 0x0000427D File Offset: 0x0000247D
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x040001F6 RID: 502
		[CompilerGenerated]
		private MsgEventHandler msgEventHandler_0;

		// Token: 0x040001F7 RID: 503
		[CompilerGenerated]
		private Class300 class300_0;

		// Token: 0x040001F8 RID: 504
		private IContainer icontainer_0;
	}
}
