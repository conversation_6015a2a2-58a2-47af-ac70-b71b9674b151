﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using TEx.SIndicator;

namespace TEx
{
	// Token: 0x020001D0 RID: 464
	[Serializable]
	internal sealed class ChartParam
	{
		// Token: 0x06001241 RID: 4673 RVA: 0x00002D25 File Offset: 0x00000F25
		public ChartParam()
		{
		}

		// Token: 0x06001242 RID: 4674 RVA: 0x0007E970 File Offset: 0x0007CB70
		public ChartParam(ChartType chartType, List<Indicator> indList, List<UserDefineIndScript> UDSList, bool isYAxisLogType)
		{
			ChartParam.Class260 @class = new ChartParam.Class260();
			this.ChartType = chartType;
			this.IsYAxisLogType = isYAxisLogType;
			this._UDSList = UDSList;
			this._IndList = null;
			@class.list_0 = this.method_0(indList);
			if (@class.list_0 != null && @class.list_0.Any<UserDefineIndScript>())
			{
				ChartParam.Class261 class2 = new ChartParam.Class261();
				class2.class260_0 = @class;
				class2.int_0 = 0;
				while (class2.int_0 < class2.class260_0.list_0.Count)
				{
					if (!this._UDSList.Any(new Func<UserDefineIndScript, bool>(class2.method_0)))
					{
						this._UDSList.Add(class2.class260_0.list_0[class2.int_0]);
					}
					int int_ = class2.int_0;
					class2.int_0 = int_ + 1;
				}
			}
		}

		// Token: 0x06001243 RID: 4675 RVA: 0x0007EA48 File Offset: 0x0007CC48
		private List<UserDefineIndScript> method_0(List<Indicator> list_0)
		{
			List<UserDefineIndScript> result;
			if (list_0 == null)
			{
				result = new List<UserDefineIndScript>();
			}
			else
			{
				result = list_0.Select(new Func<Indicator, UserDefineIndScript>(this.method_2)).Where(new Func<UserDefineIndScript, bool>(ChartParam.<>c.<>9.method_0)).ToList<UserDefineIndScript>();
			}
			return result;
		}

		// Token: 0x06001244 RID: 4676 RVA: 0x0007EAA0 File Offset: 0x0007CCA0
		private UserDefineIndScript method_1(Indicator indicator_0)
		{
			ChartParam.Class262 @class = new ChartParam.Class262();
			@class.indicator_0 = indicator_0;
			IndEx indEx = @class.indicator_0 as IndEx;
			UserDefineIndScript result;
			if (@class.indicator_0 == null)
			{
				result = null;
			}
			else if (indEx != null)
			{
				result = null;
			}
			else if (@class.indicator_0.EnName == "MAs")
			{
				UserDefineIndScript userDefineIndScript = UserDefineFileMgr.UDSListChecked.SingleOrDefault(new Func<UserDefineIndScript, bool>(ChartParam.<>c.<>9.method_1));
				if (userDefineIndScript != null)
				{
					result = (userDefineIndScript.System.ICloneable.Clone() as UserDefineIndScript);
				}
				else
				{
					result = null;
				}
			}
			else
			{
				UserDefineIndScript userDefineIndScript2 = UserDefineFileMgr.UDSListChecked.SingleOrDefault(new Func<UserDefineIndScript, bool>(@class.method_0));
				if (userDefineIndScript2 != null)
				{
					result = (userDefineIndScript2.System.ICloneable.Clone() as UserDefineIndScript);
				}
				else
				{
					result = null;
				}
			}
			return result;
		}

		// Token: 0x170002B3 RID: 691
		// (get) Token: 0x06001245 RID: 4677 RVA: 0x0007EB6C File Offset: 0x0007CD6C
		// (set) Token: 0x06001246 RID: 4678 RVA: 0x00007984 File Offset: 0x00005B84
		public ChartType ChartType
		{
			get
			{
				return this._ChartType;
			}
			set
			{
				this._ChartType = value;
			}
		}

		// Token: 0x170002B4 RID: 692
		// (get) Token: 0x06001247 RID: 4679 RVA: 0x0007EB84 File Offset: 0x0007CD84
		// (set) Token: 0x06001248 RID: 4680 RVA: 0x0000798F File Offset: 0x00005B8F
		public bool IsYAxisLogType
		{
			get
			{
				return this._IsYAxisLogType;
			}
			set
			{
				this._IsYAxisLogType = value;
			}
		}

		// Token: 0x170002B5 RID: 693
		// (get) Token: 0x06001249 RID: 4681 RVA: 0x0007EB9C File Offset: 0x0007CD9C
		// (set) Token: 0x0600124A RID: 4682 RVA: 0x0007EBB4 File Offset: 0x0007CDB4
		public List<Indicator> IndList
		{
			get
			{
				return this._IndList;
			}
			set
			{
				List<UserDefineIndScript> list = this.method_0(value);
				if (list != null && list.Any<UserDefineIndScript>())
				{
					if (this._UDSList != null)
					{
						this._UDSList.AddRange(list);
					}
					else
					{
						this._UDSList = new List<UserDefineIndScript>(list);
					}
				}
			}
		}

		// Token: 0x170002B6 RID: 694
		// (get) Token: 0x0600124B RID: 4683 RVA: 0x0007EBF8 File Offset: 0x0007CDF8
		// (set) Token: 0x0600124C RID: 4684 RVA: 0x0000799A File Offset: 0x00005B9A
		public List<UserDefineIndScript> UDSList
		{
			get
			{
				return this._UDSList;
			}
			set
			{
				this._UDSList = value;
			}
		}

		// Token: 0x0600124D RID: 4685 RVA: 0x0007EC10 File Offset: 0x0007CE10
		[CompilerGenerated]
		private UserDefineIndScript method_2(Indicator indicator_0)
		{
			return this.method_1(indicator_0);
		}

		// Token: 0x0400098C RID: 2444
		private ChartType _ChartType;

		// Token: 0x0400098D RID: 2445
		private List<Indicator> _IndList;

		// Token: 0x0400098E RID: 2446
		private List<UserDefineIndScript> _UDSList;

		// Token: 0x0400098F RID: 2447
		private bool _IsYAxisLogType;

		// Token: 0x020001D1 RID: 465
		[CompilerGenerated]
		private sealed class Class260
		{
			// Token: 0x04000990 RID: 2448
			public List<UserDefineIndScript> list_0;
		}

		// Token: 0x020001D2 RID: 466
		[CompilerGenerated]
		private sealed class Class261
		{
			// Token: 0x06001250 RID: 4688 RVA: 0x0007EC28 File Offset: 0x0007CE28
			internal bool method_0(UserDefineIndScript userDefineIndScript_0)
			{
				return userDefineIndScript_0.Name == this.class260_0.list_0[this.int_0].Name;
			}

			// Token: 0x04000991 RID: 2449
			public int int_0;

			// Token: 0x04000992 RID: 2450
			public ChartParam.Class260 class260_0;
		}

		// Token: 0x020001D4 RID: 468
		[CompilerGenerated]
		private sealed class Class262
		{
			// Token: 0x06001256 RID: 4694 RVA: 0x0007EC9C File Offset: 0x0007CE9C
			internal bool method_0(UserDefineIndScript userDefineIndScript_0)
			{
				return userDefineIndScript_0.Name == this.indicator_0.EnName;
			}

			// Token: 0x04000996 RID: 2454
			public Indicator indicator_0;
		}
	}
}
