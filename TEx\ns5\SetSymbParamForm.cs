﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns11;
using ns18;
using ns28;
using ns6;
using TEx;
using TEx.Comn;

namespace ns5
{
	// Token: 0x02000170 RID: 368
	internal sealed partial class SetSymbParamForm : Form
	{
		// Token: 0x14000074 RID: 116
		// (add) Token: 0x06000DFE RID: 3582 RVA: 0x00059080 File Offset: 0x00057280
		// (remove) Token: 0x06000DFF RID: 3583 RVA: 0x000590B8 File Offset: 0x000572B8
		public event Delegate10 SymbParamsUpdated
		{
			[CompilerGenerated]
			add
			{
				Delegate10 @delegate = this.delegate10_0;
				Delegate10 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate10 value2 = (Delegate10)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate10>(ref this.delegate10_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate10 @delegate = this.delegate10_0;
				Delegate10 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate10 value2 = (Delegate10)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate10>(ref this.delegate10_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06000E00 RID: 3584 RVA: 0x000590F0 File Offset: 0x000572F0
		protected void method_0(bool bool_3, bool bool_4)
		{
			EventArgs21 e = new EventArgs21(bool_3, bool_4);
			Delegate10 @delegate = this.delegate10_0;
			if (@delegate != null)
			{
				@delegate(this, e);
			}
		}

		// Token: 0x06000E01 RID: 3585 RVA: 0x000063B2 File Offset: 0x000045B2
		public SetSymbParamForm() : this(null, false, false, false)
		{
		}

		// Token: 0x06000E02 RID: 3586 RVA: 0x0005911C File Offset: 0x0005731C
		public SetSymbParamForm(List<StkSymbol> list_3, bool bool_3, bool bool_4, bool bool_5)
		{
			SetSymbParamForm.Class205 @class = new SetSymbParamForm.Class205();
			this.InitializeComponent();
			this.BackColor = SystemColors.Control;
			this.list_2 = list_3;
			this.bool_0 = bool_3;
			this.bool_1 = bool_4;
			this.bool_2 = bool_5;
			this.nfBtn_Cancel = new Class306();
			this.nfBtn_Cancel.DialogResult = DialogResult.Cancel;
			this.nfBtn_Cancel.Name = "nfBtn_Cancel";
			this.nfBtn_Cancel.TabIndex = 14;
			this.nfBtn_Cancel.Text = "取消";
			this.nfBtn_Cancel.UseVisualStyleBackColor = true;
			this.nfBtn_Cancel.Visible = true;
			base.Controls.Add(this.nfBtn_Cancel);
			base.CancelButton = this.nfBtn_Cancel;
			this.list_0 = SymbMgr.LocalMstSymbolList;
			this.list_1 = Base.Acct.CurrAcctMstSymbols.Cast<TradingSymbol>().ToList<TradingSymbol>();
			List<ExchgHouse> dataSource = Base.Data.smethod_87();
			this.comboBox_Exchg.DisplayMember = "Name_CN";
			this.comboBox_Exchg.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_Exchg.DataSource = dataSource;
			this.comboBox_Exchg.SelectedIndexChanged += this.comboBox_Exchg_SelectedIndexChanged;
			this.radioBtn_Global.CheckedChanged += this.radioBtn_Global_CheckedChanged;
			this.radioBtn_CurrAcct.CheckedChanged += this.radioBtn_CurrAcct_CheckedChanged;
			this.button_OK.Click += this.button_OK_Click;
			this.btn_Apply.Enabled = false;
			base.Shown += this.SetSymbParamForm_Shown;
			this.nfBtn_Cancel.Size = this.button_OK.Size;
			this.nfBtn_Cancel.Location = new Point(this.button_OK.Location.X + (this.button_OK.Location.X - this.btn_Apply.Location.X), this.button_OK.Location.Y);
			if (this.bool_2)
			{
				SetSymbParamForm.Class206 class2 = new SetSymbParamForm.Class206();
				if (this.list_2 != null && this.list_2.Any<StkSymbol>())
				{
					class2.tradingSymbol_0 = this.list_2.First<StkSymbol>();
				}
				else
				{
					class2.tradingSymbol_0 = Base.UI.CurrSymbol;
				}
				if (class2.tradingSymbol_0 != null)
				{
					this.comboBox_Exchg.SelectedItem = (this.comboBox_Exchg.DataSource as List<ExchgHouse>).Single(new Func<ExchgHouse, bool>(class2.method_0));
				}
			}
			@class.exchgHouse_0 = (ExchgHouse)this.comboBox_Exchg.SelectedItem;
			List<TradingSymbol> dataSource2 = this.list_0.Where(new Func<TradingSymbol, bool>(@class.method_0)).ToList<TradingSymbol>();
			this.symbParamCtrl1.IfShowCurrSymbOnStartup = this.bool_2;
			this.symbParamCtrl1.IfFocusOnAutoLimitOnStartup = this.bool_1;
			this.symbParamCtrl1.IfFocusOnAutoStopOnStartup = this.bool_0;
			this.symbParamCtrl1.DataSource = dataSource2;
			this.symbParamCtrl1.IsInGlobalSymbls = true;
			if (list_3 != null && list_3.Any<StkSymbol>())
			{
				this.symbParamCtrl1.method_2(list_3.First<StkSymbol>());
			}
			else
			{
				this.symbParamCtrl1.method_2(null);
			}
			this.symbParamCtrl1.FieldChanged += this.method_2;
			this.symbParamCtrl1.FieldsChkNoChange += this.method_3;
		}

		// Token: 0x06000E03 RID: 3587 RVA: 0x000063BE File Offset: 0x000045BE
		private void SetSymbParamForm_Load(object sender, EventArgs e)
		{
			this.radioBtn_Global.Checked = true;
		}

		// Token: 0x06000E04 RID: 3588 RVA: 0x000063CE File Offset: 0x000045CE
		private void SetSymbParamForm_Shown(object sender, EventArgs e)
		{
			if (this.bool_2)
			{
				this.symbParamCtrl1.Focus();
				this.symbParamCtrl1.method_3();
			}
		}

		// Token: 0x06000E05 RID: 3589 RVA: 0x0005946C File Offset: 0x0005766C
		private BindingList<ExchgHouse> method_1()
		{
			BindingList<ExchgHouse> bindingList = new BindingList<ExchgHouse>();
			foreach (ExchgHouse item in Base.Data.ExchangeList)
			{
				bindingList.Add(item);
			}
			return bindingList;
		}

		// Token: 0x06000E06 RID: 3590 RVA: 0x000594CC File Offset: 0x000576CC
		private void comboBox_Exchg_SelectedIndexChanged(object sender, EventArgs e)
		{
			SetSymbParamForm.Class207 @class = new SetSymbParamForm.Class207();
			@class.exchgHouse_0 = (ExchgHouse)this.comboBox_Exchg.SelectedItem;
			List<TradingSymbol> source;
			if (this.radioBtn_Global.Checked)
			{
				source = this.list_0;
			}
			else
			{
				source = this.list_1;
			}
			this.symbParamCtrl1.DataSource = source.Where(new Func<TradingSymbol, bool>(@class.method_0)).ToList<TradingSymbol>();
		}

		// Token: 0x06000E07 RID: 3591 RVA: 0x000063F1 File Offset: 0x000045F1
		private void radioBtn_Global_CheckedChanged(object sender, EventArgs e)
		{
			this.method_4();
			if (this.radioBtn_Global.Checked)
			{
				this.label_acctNote.Text = "如未设置当前账户参数，则当前账户应用全局默认参数。";
			}
			else
			{
				this.label_acctNote.Text = "当前账户参数设置后仅应用于当前账户，优先级高于全局参数。";
			}
		}

		// Token: 0x06000E08 RID: 3592 RVA: 0x000041AE File Offset: 0x000023AE
		private void radioBtn_CurrAcct_CheckedChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x06000E09 RID: 3593 RVA: 0x0000642A File Offset: 0x0000462A
		private void method_2(object sender, EventArgs e)
		{
			if (!this.btn_Apply.Enabled)
			{
				this.btn_Apply.Enabled = true;
			}
		}

		// Token: 0x06000E0A RID: 3594 RVA: 0x00006447 File Offset: 0x00004647
		private void method_3(object sender, EventArgs e)
		{
			if (this.btn_Apply.Enabled)
			{
				this.btn_Apply.Enabled = false;
			}
		}

		// Token: 0x06000E0B RID: 3595 RVA: 0x00006464 File Offset: 0x00004664
		private void btn_Apply_Click(object sender, EventArgs e)
		{
			(sender as Button).Enabled = false;
			this.symbParamCtrl1.method_21();
			this.method_4();
		}

		// Token: 0x06000E0C RID: 3596 RVA: 0x00059538 File Offset: 0x00057738
		private void button_OK_Click(object sender, EventArgs e)
		{
			this.symbParamCtrl1.method_21();
			if ((this.bool_1 || this.bool_0) && this.list_2 != null)
			{
				foreach (StkSymbol stkSymbol in this.list_2)
				{
					StkSymbol stkSymbol2 = SymbMgr.smethod_3(stkSymbol.ID);
					if (this.bool_0 && stkSymbol2.AutoStopLossPoints == null)
					{
						if (MessageBox.Show("品种（" + stkSymbol2.Desc + "）尚未设置止损点数，确认关闭窗口吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
						{
							this.method_0(this.bool_0, this.bool_1);
							base.Dispose();
						}
						return;
					}
					if (this.bool_1 && stkSymbol2.AutoLimitTakePoints == null)
					{
						if (MessageBox.Show("品种（" + stkSymbol2.Desc + "）尚未设置止盈点数，确认关闭窗口吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
						{
							this.method_0(this.bool_0, this.bool_1);
							base.Dispose();
						}
						return;
					}
				}
			}
			this.method_0(this.bool_0, this.bool_1);
			base.Dispose();
		}

		// Token: 0x06000E0D RID: 3597 RVA: 0x00059684 File Offset: 0x00057884
		private void method_4()
		{
			this.list_0 = SymbMgr.LocalMstSymbolList;
			this.list_1 = Base.Acct.CurrAcctMstSymbols.Cast<TradingSymbol>().ToList<TradingSymbol>();
			int selectedIndex = this.comboBox_Exchg.SelectedIndex;
			int currSymblLstIndex = this.symbParamCtrl1.CurrSymblLstIndex;
			bool @checked = this.radioBtn_Global.Checked;
			List<ExchgHouse> dataSource = Base.Data.smethod_87();
			this.comboBox_Exchg.ForeColor = this.comboBox_Exchg.BackColor;
			this.comboBox_Exchg.DataSource = dataSource;
			this.comboBox_Exchg.SelectedIndex = selectedIndex;
			this.comboBox_Exchg.ForeColor = default(Color);
			this.symbParamCtrl1.IsInGlobalSymbls = @checked;
			this.symbParamCtrl1.CurrSymblLstIndex = currSymblLstIndex;
		}

		// Token: 0x06000E0E RID: 3598 RVA: 0x00006485 File Offset: 0x00004685
		public void method_5()
		{
			this.symbParamCtrl1.method_23();
		}

		// Token: 0x06000E0F RID: 3599 RVA: 0x00006494 File Offset: 0x00004694
		public void method_6()
		{
			this.symbParamCtrl1.method_24();
		}

		// Token: 0x17000233 RID: 563
		// (get) Token: 0x06000E10 RID: 3600 RVA: 0x00059740 File Offset: 0x00057940
		public SymbParamCtrl SymbParamCtrl
		{
			get
			{
				return this.symbParamCtrl1;
			}
		}

		// Token: 0x06000E11 RID: 3601 RVA: 0x000064A3 File Offset: 0x000046A3
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0400073B RID: 1851
		private List<TradingSymbol> list_0;

		// Token: 0x0400073C RID: 1852
		private List<TradingSymbol> list_1;

		// Token: 0x0400073D RID: 1853
		private List<StkSymbol> list_2;

		// Token: 0x0400073E RID: 1854
		private bool bool_0;

		// Token: 0x0400073F RID: 1855
		private bool bool_1;

		// Token: 0x04000740 RID: 1856
		private bool bool_2;

		// Token: 0x04000741 RID: 1857
		[CompilerGenerated]
		private Delegate10 delegate10_0;

		// Token: 0x04000742 RID: 1858
		private Class306 nfBtn_Cancel;

		// Token: 0x04000743 RID: 1859
		private IContainer icontainer_0;

		// Token: 0x02000171 RID: 369
		[CompilerGenerated]
		private sealed class Class205
		{
			// Token: 0x06000E14 RID: 3604 RVA: 0x00059DEC File Offset: 0x00057FEC
			internal bool method_0(TradingSymbol tradingSymbol_0)
			{
				return tradingSymbol_0.ExchangeID == this.exchgHouse_0.ID;
			}

			// Token: 0x0400074F RID: 1871
			public ExchgHouse exchgHouse_0;
		}

		// Token: 0x02000172 RID: 370
		[CompilerGenerated]
		private sealed class Class206
		{
			// Token: 0x06000E16 RID: 3606 RVA: 0x00059E10 File Offset: 0x00058010
			internal bool method_0(ExchgHouse exchgHouse_0)
			{
				return exchgHouse_0.ID == this.tradingSymbol_0.ExchangeID;
			}

			// Token: 0x04000750 RID: 1872
			public TradingSymbol tradingSymbol_0;
		}

		// Token: 0x02000173 RID: 371
		[CompilerGenerated]
		private sealed class Class207
		{
			// Token: 0x06000E18 RID: 3608 RVA: 0x00059E34 File Offset: 0x00058034
			internal bool method_0(TradingSymbol tradingSymbol_0)
			{
				return tradingSymbol_0.ExchangeID == this.exchgHouse_0.ID;
			}

			// Token: 0x04000751 RID: 1873
			public ExchgHouse exchgHouse_0;
		}
	}
}
