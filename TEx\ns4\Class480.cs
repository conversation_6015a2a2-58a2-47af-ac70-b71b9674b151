﻿using System;
using System.Linq;
using System.Windows.Forms;
using Microsoft.Win32;

namespace ns4
{
	// Token: 0x0200037C RID: 892
	internal sealed class Class480
	{
		// Token: 0x060024EB RID: 9451 RVA: 0x000F6744 File Offset: 0x000F4944
		private static int smethod_0()
		{
			int result;
			try
			{
				result = (int)Convert.ToDecimal(Registry.LocalMachine.OpenSubKey("SOFTWARE\\Microsoft\\Internet Explorer", true).GetValue("Version").ToString().Trim().Split(new char[]
				{
					'.'
				}).First<string>());
			}
			catch (Exception)
			{
				result = -1;
			}
			return result;
		}

		// Token: 0x060024EC RID: 9452 RVA: 0x000F67B4 File Offset: 0x000F49B4
		private static int smethod_1()
		{
			int result;
			try
			{
				result = new WebBrowser().Version.Major;
			}
			catch (Exception)
			{
				result = -1;
			}
			return result;
		}

		// Token: 0x060024ED RID: 9453 RVA: 0x000F67F0 File Offset: 0x000F49F0
		public static int smethod_2()
		{
			return Class480.smethod_1();
		}
	}
}
