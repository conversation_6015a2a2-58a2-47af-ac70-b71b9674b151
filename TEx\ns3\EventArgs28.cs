﻿using System;
using TEx;

namespace ns3
{
	// Token: 0x020002A5 RID: 677
	internal sealed class EventArgs28 : EventArgs
	{
		// Token: 0x06001E1D RID: 7709 RVA: 0x0000C93E File Offset: 0x0000AB3E
		public EventArgs28(Indicator indicator_1, int int_1, double[] double_1)
		{
			this.indicator_0 = indicator_1;
			this.int_0 = int_1;
			this.double_0 = double_1;
		}

		// Token: 0x04000ECD RID: 3789
		public Indicator indicator_0;

		// Token: 0x04000ECE RID: 3790
		public int int_0;

		// Token: 0x04000ECF RID: 3791
		public double[] double_0;
	}
}
