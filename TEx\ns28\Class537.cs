﻿using System;
using System.Drawing;
using System.IO;
using System.Reflection;

namespace ns28
{
	// Token: 0x020003FD RID: 1021
	internal sealed class Class537
	{
		// Token: 0x060027BE RID: 10174 RVA: 0x0010292C File Offset: 0x00100B2C
		public static Bitmap smethod_0(string string_0)
		{
			Bitmap result;
			try
			{
				Stream manifestResourceStream = Assembly.GetExecutingAssembly().GetManifestResourceStream("SmartAssembly.SmartExceptionsCore.Resources." + string_0 + ".png");
				result = ((manifestResourceStream == null) ? null : new Bitmap(manifestResourceStream));
			}
			catch
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060027BF RID: 10175 RVA: 0x0010297C File Offset: 0x00100B7C
		public static Icon smethod_1(string string_0)
		{
			Icon result;
			try
			{
				Stream manifestResourceStream = Assembly.GetExecutingAssembly().GetManifestResourceStream("SmartAssembly.SmartExceptionsCore.Resources." + string_0 + ".ico");
				result = ((manifestResourceStream == null) ? null : new Icon(manifestResourceStream));
			}
			catch
			{
				result = null;
			}
			return result;
		}
	}
}
