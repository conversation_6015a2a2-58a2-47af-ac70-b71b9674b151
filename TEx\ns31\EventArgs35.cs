﻿using System;
using System.IO;
using ns3;

namespace ns31
{
	// Token: 0x020003FB RID: 1019
	internal sealed class EventArgs35 : EventArgs
	{
		// Token: 0x170006D0 RID: 1744
		// (get) Token: 0x060027AB RID: 10155 RVA: 0x0000F350 File Offset: 0x0000D550
		public Exception Exception
		{
			get
			{
				return this.exception_0;
			}
		}

		// Token: 0x170006D1 RID: 1745
		// (get) Token: 0x060027AC RID: 10156 RVA: 0x0000F358 File Offset: 0x0000D558
		public bool CanDebug
		{
			get
			{
				return this.bool_0;
			}
		}

		// Token: 0x170006D2 RID: 1746
		// (get) Token: 0x060027AD RID: 10157 RVA: 0x0000F360 File Offset: 0x0000D560
		public bool CanSendReport
		{
			get
			{
				return this.bool_1;
			}
		}

		// Token: 0x170006D3 RID: 1747
		// (get) Token: 0x060027AE RID: 10158 RVA: 0x0000F368 File Offset: 0x0000D568
		public bool ShowContinueCheckbox
		{
			get
			{
				return this.bool_2;
			}
		}

		// Token: 0x170006D4 RID: 1748
		// (get) Token: 0x060027AF RID: 10159 RVA: 0x0000F368 File Offset: 0x0000D568
		[Obsolete("Use ShowContinueCheckbox instead, as this is now also false when the builder has chosen not to show the checkbox.")]
		public bool CanContinue
		{
			get
			{
				return this.bool_2;
			}
		}

		// Token: 0x060027B0 RID: 10160 RVA: 0x0000F370 File Offset: 0x0000D570
		internal void method_0(bool bool_4)
		{
			this.bool_2 = bool_4;
		}

		// Token: 0x060027B1 RID: 10161 RVA: 0x0000F379 File Offset: 0x0000D579
		internal void method_1()
		{
			this.bool_0 = true;
		}

		// Token: 0x060027B2 RID: 10162 RVA: 0x0000F382 File Offset: 0x0000D582
		internal void method_2()
		{
			this.bool_1 = false;
		}

		// Token: 0x170006D5 RID: 1749
		// (get) Token: 0x060027B3 RID: 10163 RVA: 0x0000F38B File Offset: 0x0000D58B
		// (set) Token: 0x060027B4 RID: 10164 RVA: 0x0000F393 File Offset: 0x0000D593
		public bool TryToContinue
		{
			get
			{
				return this.bool_3;
			}
			set
			{
				this.bool_3 = value;
			}
		}

		// Token: 0x060027B5 RID: 10165 RVA: 0x0000F39C File Offset: 0x0000D59C
		public void method_3()
		{
			if (this.bool_0)
			{
				this.class532_0.method_22();
			}
		}

		// Token: 0x060027B6 RID: 10166 RVA: 0x0000F3B1 File Offset: 0x0000D5B1
		public bool method_4(string string_0)
		{
			if (File.Exists(string_0))
			{
				File.Delete(string_0);
			}
			return this.class532_0.method_23(string_0);
		}

		// Token: 0x060027B7 RID: 10167 RVA: 0x0000F3CD File Offset: 0x0000D5CD
		public byte[] method_5()
		{
			return this.class532_0.method_12();
		}

		// Token: 0x060027B8 RID: 10168 RVA: 0x0000F3DA File Offset: 0x0000D5DA
		public bool method_6()
		{
			return this.bool_1 && this.class532_0.method_19();
		}

		// Token: 0x060027B9 RID: 10169 RVA: 0x0000F3F1 File Offset: 0x0000D5F1
		public void method_7(string string_0, string string_1)
		{
			this.class532_0.method_17(string_0, string_1);
		}

		// Token: 0x060027BA RID: 10170 RVA: 0x0000F400 File Offset: 0x0000D600
		public void method_8(string string_0, string string_1)
		{
			this.class532_0.method_18(string_0, string_1);
		}

		// Token: 0x060027BB RID: 10171 RVA: 0x0000F40F File Offset: 0x0000D60F
		internal EventArgs35(Class532 class532_1, Exception exception_1)
		{
			this.class532_0 = class532_1;
			this.exception_0 = exception_1;
		}

		// Token: 0x040013B3 RID: 5043
		private Class532 class532_0;

		// Token: 0x040013B4 RID: 5044
		private Exception exception_0;

		// Token: 0x040013B5 RID: 5045
		private bool bool_0;

		// Token: 0x040013B6 RID: 5046
		private bool bool_1 = true;

		// Token: 0x040013B7 RID: 5047
		private bool bool_2 = true;

		// Token: 0x040013B8 RID: 5048
		private bool bool_3;
	}
}
