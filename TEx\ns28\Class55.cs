﻿using System;
using System.Windows.Forms;
using ns7;
using TEx;

namespace ns28
{
	// Token: 0x0200029C RID: 668
	internal sealed class Class55 : Class51
	{
		// Token: 0x06001D9B RID: 7579 RVA: 0x0000C638 File Offset: 0x0000A838
		public Class55(Panel panel_1, int int_4, int int_5, ChtCtrl_KLine chtCtrl_KLine_0, bool bool_3) : base(panel_1, int_4, int_5, chtCtrl_KLine_0, bool_3)
		{
			base.method_0(Class372.zoomout);
		}

		// Token: 0x06001D9C RID: 7580 RVA: 0x0000C654 File Offset: 0x0000A854
		public Class55(Panel panel_1, int int_4, int int_5, ChtCtrl_KLine chtCtrl_KLine_0) : this(panel_1, int_4, int_5, chtCtrl_KLine_0, true)
		{
		}

		// Token: 0x06001D9D RID: 7581 RVA: 0x0000C662 File Offset: 0x0000A862
		protected override void Class48_MouseEnter(object sender, EventArgs e)
		{
			base.Class48_MouseEnter(sender, e);
			base.method_0(Class372.zoomout_red);
		}

		// Token: 0x06001D9E RID: 7582 RVA: 0x0000C679 File Offset: 0x0000A879
		protected override void Class48_MouseLeave(object sender, EventArgs e)
		{
			base.Class48_MouseLeave(sender, e);
			base.method_0(Class372.zoomout);
		}

		// Token: 0x06001D9F RID: 7583 RVA: 0x0000C690 File Offset: 0x0000A890
		protected override void Class48_Click(object sender, EventArgs e)
		{
			base.Class48_Click(sender, e);
			((ChtCtrl_KLine)base.ChtCtrl).method_117(true);
		}
	}
}
