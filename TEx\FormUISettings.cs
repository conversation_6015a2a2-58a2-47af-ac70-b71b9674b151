﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using TEx.Comn;
using TEx.ImportTrans;

namespace TEx
{
	// Token: 0x020000AD RID: 173
	[Serializable]
	internal sealed class FormUISettings
	{
		// Token: 0x0600060E RID: 1550 RVA: 0x00018F9C File Offset: 0x0001719C
		private void method_0(EventHandler eventHandler_14)
		{
			EventArgs e = new EventArgs();
			if (eventHandler_14 != null)
			{
				eventHandler_14(this, e);
			}
		}

		// Token: 0x14000025 RID: 37
		// (add) Token: 0x0600060F RID: 1551 RVA: 0x0002CAC0 File Offset: 0x0002ACC0
		// (remove) Token: 0x06000610 RID: 1552 RVA: 0x0002CAF8 File Offset: 0x0002ACF8
		public event EventHandler StartedSetOn
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000611 RID: 1553 RVA: 0x0000485C File Offset: 0x00002A5C
		private void method_1()
		{
			this.method_0(this.eventHandler_0);
		}

		// Token: 0x14000026 RID: 38
		// (add) Token: 0x06000612 RID: 1554 RVA: 0x0002CB30 File Offset: 0x0002AD30
		// (remove) Token: 0x06000613 RID: 1555 RVA: 0x0002CB68 File Offset: 0x0002AD68
		public event EventHandler StartedSetOff
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000614 RID: 1556 RVA: 0x0000486C File Offset: 0x00002A6C
		private void method_2()
		{
			this.method_0(this.eventHandler_1);
		}

		// Token: 0x14000027 RID: 39
		// (add) Token: 0x06000615 RID: 1557 RVA: 0x0002CBA0 File Offset: 0x0002ADA0
		// (remove) Token: 0x06000616 RID: 1558 RVA: 0x0002CBD8 File Offset: 0x0002ADD8
		public event EventHandler IfShowSymbCNNameInOpenTransGridViewChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_2;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_2, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_2;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_2, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000617 RID: 1559 RVA: 0x0000487C File Offset: 0x00002A7C
		private void method_3()
		{
			EventHandler eventHandler = this.eventHandler_2;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x14000028 RID: 40
		// (add) Token: 0x06000618 RID: 1560 RVA: 0x0002CC10 File Offset: 0x0002AE10
		// (remove) Token: 0x06000619 RID: 1561 RVA: 0x0002CC48 File Offset: 0x0002AE48
		public event EventHandler StockShortSettingChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_3;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_3, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_3;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_3, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600061A RID: 1562 RVA: 0x00004897 File Offset: 0x00002A97
		private void method_4()
		{
			this.method_0(this.eventHandler_3);
		}

		// Token: 0x14000029 RID: 41
		// (add) Token: 0x0600061B RID: 1563 RVA: 0x0002CC80 File Offset: 0x0002AE80
		// (remove) Token: 0x0600061C RID: 1564 RVA: 0x0002CCB8 File Offset: 0x0002AEB8
		public event EventHandler BackupSyncPeriodicallyChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_4;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_4, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_4;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_4, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600061D RID: 1565 RVA: 0x000048A7 File Offset: 0x00002AA7
		private void method_5()
		{
			this.method_0(this.eventHandler_4);
		}

		// Token: 0x1400002A RID: 42
		// (add) Token: 0x0600061E RID: 1566 RVA: 0x0002CCF0 File Offset: 0x0002AEF0
		// (remove) Token: 0x0600061F RID: 1567 RVA: 0x0002CD28 File Offset: 0x0002AF28
		public event EventHandler FollowPrcInTradingInputChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_5;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_5, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_5;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_5, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000620 RID: 1568 RVA: 0x000048B7 File Offset: 0x00002AB7
		private void method_6()
		{
			this.method_0(this.eventHandler_5);
		}

		// Token: 0x1400002B RID: 43
		// (add) Token: 0x06000621 RID: 1569 RVA: 0x0002CD60 File Offset: 0x0002AF60
		// (remove) Token: 0x06000622 RID: 1570 RVA: 0x0002CD98 File Offset: 0x0002AF98
		public event EventHandler StockRestorationMethodChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_6;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_6, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_6;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_6, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000623 RID: 1571 RVA: 0x000048C7 File Offset: 0x00002AC7
		private void method_7()
		{
			this.method_0(this.eventHandler_6);
		}

		// Token: 0x1400002C RID: 44
		// (add) Token: 0x06000624 RID: 1572 RVA: 0x0002CDD0 File Offset: 0x0002AFD0
		// (remove) Token: 0x06000625 RID: 1573 RVA: 0x0002CE08 File Offset: 0x0002B008
		public event EventHandler TransArrowTypeChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_7;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_7, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_7;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_7, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000626 RID: 1574 RVA: 0x000048D7 File Offset: 0x00002AD7
		private void method_8()
		{
			this.method_0(this.eventHandler_7);
		}

		// Token: 0x1400002D RID: 45
		// (add) Token: 0x06000627 RID: 1575 RVA: 0x0002CE40 File Offset: 0x0002B040
		// (remove) Token: 0x06000628 RID: 1576 RVA: 0x0002CE78 File Offset: 0x0002B078
		public event EventHandler AutoPlayUnitChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_8;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_8, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_8;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_8, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000629 RID: 1577 RVA: 0x000048E7 File Offset: 0x00002AE7
		private void method_9()
		{
			this.method_0(this.eventHandler_8);
		}

		// Token: 0x1400002E RID: 46
		// (add) Token: 0x0600062A RID: 1578 RVA: 0x0002CEB0 File Offset: 0x0002B0B0
		// (remove) Token: 0x0600062B RID: 1579 RVA: 0x0002CEE8 File Offset: 0x0002B0E8
		public event EventHandler SymbSwitchShowCurrDTChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_9;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_9, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_9;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_9, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600062C RID: 1580 RVA: 0x000048F7 File Offset: 0x00002AF7
		private void method_10()
		{
			this.method_0(this.eventHandler_9);
		}

		// Token: 0x1400002F RID: 47
		// (add) Token: 0x0600062D RID: 1581 RVA: 0x0002CF20 File Offset: 0x0002B120
		// (remove) Token: 0x0600062E RID: 1582 RVA: 0x0002CF58 File Offset: 0x0002B158
		public event EventHandler IfShowAcctInfoOnTransTabHeaderChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_10;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_10, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_10;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_10, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600062F RID: 1583 RVA: 0x00004907 File Offset: 0x00002B07
		private void method_11()
		{
			this.method_0(this.eventHandler_10);
		}

		// Token: 0x14000030 RID: 48
		// (add) Token: 0x06000630 RID: 1584 RVA: 0x0002CF90 File Offset: 0x0002B190
		// (remove) Token: 0x06000631 RID: 1585 RVA: 0x0002CFC8 File Offset: 0x0002B1C8
		public event EventHandler IfShowDayOfWeekChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_11;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_11, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_11;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_11, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000632 RID: 1586 RVA: 0x00004917 File Offset: 0x00002B17
		private void method_12()
		{
			this.method_0(this.eventHandler_11);
		}

		// Token: 0x14000031 RID: 49
		// (add) Token: 0x06000633 RID: 1587 RVA: 0x0002D000 File Offset: 0x0002B200
		// (remove) Token: 0x06000634 RID: 1588 RVA: 0x0002D038 File Offset: 0x0002B238
		public event EventHandler BlindTestModeOn
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_12;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_12, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_12;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_12, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000635 RID: 1589 RVA: 0x00004927 File Offset: 0x00002B27
		private void method_13()
		{
			this.method_0(this.eventHandler_12);
		}

		// Token: 0x14000032 RID: 50
		// (add) Token: 0x06000636 RID: 1590 RVA: 0x0002D070 File Offset: 0x0002B270
		// (remove) Token: 0x06000637 RID: 1591 RVA: 0x0002D0A8 File Offset: 0x0002B2A8
		public event EventHandler BlindTestModeOff
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_13;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_13, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_13;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_13, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06000638 RID: 1592 RVA: 0x00004937 File Offset: 0x00002B37
		private void method_14()
		{
			this.method_0(this.eventHandler_13);
		}

		// Token: 0x06000639 RID: 1593 RVA: 0x00004947 File Offset: 0x00002B47
		public FormUISettings()
		{
			this._TradingPrice = 0m;
		}

		// Token: 0x17000130 RID: 304
		// (get) Token: 0x0600063A RID: 1594 RVA: 0x0002D0E0 File Offset: 0x0002B2E0
		// (set) Token: 0x0600063B RID: 1595 RVA: 0x0000495D File Offset: 0x00002B5D
		public string UserID
		{
			get
			{
				return this._UserID;
			}
			set
			{
				this._UserID = value;
			}
		}

		// Token: 0x17000131 RID: 305
		// (get) Token: 0x0600063C RID: 1596 RVA: 0x0002D0F8 File Offset: 0x0002B2F8
		// (set) Token: 0x0600063D RID: 1597 RVA: 0x00004968 File Offset: 0x00002B68
		public bool IfSaveUsrID
		{
			get
			{
				return this._IfSaveUsrID;
			}
			set
			{
				this._IfSaveUsrID = value;
			}
		}

		// Token: 0x17000132 RID: 306
		// (get) Token: 0x0600063E RID: 1598 RVA: 0x0002D110 File Offset: 0x0002B310
		// (set) Token: 0x0600063F RID: 1599 RVA: 0x00004973 File Offset: 0x00002B73
		public string Pswd
		{
			get
			{
				return this._Pswd;
			}
			set
			{
				this._Pswd = value;
			}
		}

		// Token: 0x17000133 RID: 307
		// (get) Token: 0x06000640 RID: 1600 RVA: 0x0002D128 File Offset: 0x0002B328
		// (set) Token: 0x06000641 RID: 1601 RVA: 0x0000497E File Offset: 0x00002B7E
		public bool IfSavePswd
		{
			get
			{
				return this._IfSavePswd;
			}
			set
			{
				this._IfSavePswd = value;
			}
		}

		// Token: 0x17000134 RID: 308
		// (get) Token: 0x06000642 RID: 1602 RVA: 0x0002D140 File Offset: 0x0002B340
		// (set) Token: 0x06000643 RID: 1603 RVA: 0x00004989 File Offset: 0x00002B89
		public DateTime? LastAppUpdDateTime
		{
			get
			{
				return this._LastAppUpdDateTime;
			}
			set
			{
				this._LastAppUpdDateTime = value;
			}
		}

		// Token: 0x17000135 RID: 309
		// (get) Token: 0x06000644 RID: 1604 RVA: 0x0002D158 File Offset: 0x0002B358
		// (set) Token: 0x06000645 RID: 1605 RVA: 0x00004994 File Offset: 0x00002B94
		public DateTime? LastDisplayedLogonNoticeDT
		{
			get
			{
				return this._LastDisplayedLogonNoticeDT;
			}
			set
			{
				this._LastDisplayedLogonNoticeDT = value;
			}
		}

		// Token: 0x17000136 RID: 310
		// (get) Token: 0x06000646 RID: 1606 RVA: 0x0002D170 File Offset: 0x0002B370
		// (set) Token: 0x06000647 RID: 1607 RVA: 0x0000499F File Offset: 0x00002B9F
		public bool IfShowSameLogonNoticeNextTime
		{
			get
			{
				return this._IfShowSameLogonNoticeNextTime;
			}
			set
			{
				this._IfShowSameLogonNoticeNextTime = value;
			}
		}

		// Token: 0x17000137 RID: 311
		// (get) Token: 0x06000648 RID: 1608 RVA: 0x0002D188 File Offset: 0x0002B388
		// (set) Token: 0x06000649 RID: 1609 RVA: 0x000049AA File Offset: 0x00002BAA
		public Size? ClientSize
		{
			get
			{
				return this._ClientSize;
			}
			set
			{
				this._ClientSize = value;
			}
		}

		// Token: 0x17000138 RID: 312
		// (get) Token: 0x0600064A RID: 1610 RVA: 0x0002D1A0 File Offset: 0x0002B3A0
		// (set) Token: 0x0600064B RID: 1611 RVA: 0x000049B5 File Offset: 0x00002BB5
		public FormStartPosition? StartPosition
		{
			get
			{
				return this._StartPosition;
			}
			set
			{
				this._StartPosition = value;
			}
		}

		// Token: 0x17000139 RID: 313
		// (get) Token: 0x0600064C RID: 1612 RVA: 0x0002D1B8 File Offset: 0x0002B3B8
		// (set) Token: 0x0600064D RID: 1613 RVA: 0x000049C0 File Offset: 0x00002BC0
		public Point? Location
		{
			get
			{
				return this._Location;
			}
			set
			{
				this._Location = value;
			}
		}

		// Token: 0x1700013A RID: 314
		// (get) Token: 0x0600064E RID: 1614 RVA: 0x0002D1D0 File Offset: 0x0002B3D0
		// (set) Token: 0x0600064F RID: 1615 RVA: 0x000049CB File Offset: 0x00002BCB
		public int ScreenNumber
		{
			get
			{
				return this._ScreenNumber;
			}
			set
			{
				this._ScreenNumber = value;
			}
		}

		// Token: 0x1700013B RID: 315
		// (get) Token: 0x06000650 RID: 1616 RVA: 0x0002D1E8 File Offset: 0x0002B3E8
		// (set) Token: 0x06000651 RID: 1617 RVA: 0x000049D6 File Offset: 0x00002BD6
		public FormWindowState? WindowState
		{
			get
			{
				return this._WindowState;
			}
			set
			{
				this._WindowState = value;
			}
		}

		// Token: 0x1700013C RID: 316
		// (get) Token: 0x06000652 RID: 1618 RVA: 0x0002D200 File Offset: 0x0002B400
		// (set) Token: 0x06000653 RID: 1619 RVA: 0x000049E1 File Offset: 0x00002BE1
		public int OriDockSiteBelowHeight
		{
			get
			{
				return this._OriDockSiteBelowHeight;
			}
			set
			{
				this._OriDockSiteBelowHeight = value;
			}
		}

		// Token: 0x1700013D RID: 317
		// (get) Token: 0x06000654 RID: 1620 RVA: 0x0002D218 File Offset: 0x0002B418
		// (set) Token: 0x06000655 RID: 1621 RVA: 0x000049EC File Offset: 0x00002BEC
		public int OriDockSiteTopHeight
		{
			get
			{
				return this._OriDockSiteTopHeight;
			}
			set
			{
				this._OriDockSiteTopHeight = value;
			}
		}

		// Token: 0x1700013E RID: 318
		// (get) Token: 0x06000656 RID: 1622 RVA: 0x0002D230 File Offset: 0x0002B430
		// (set) Token: 0x06000657 RID: 1623 RVA: 0x000049F7 File Offset: 0x00002BF7
		public bool IfShowPointValues
		{
			get
			{
				return this._IfShowPointValues;
			}
			set
			{
				this._IfShowPointValues = value;
			}
		}

		// Token: 0x1700013F RID: 319
		// (get) Token: 0x06000658 RID: 1624 RVA: 0x0002D248 File Offset: 0x0002B448
		// (set) Token: 0x06000659 RID: 1625 RVA: 0x00004A02 File Offset: 0x00002C02
		public string CurrentPageName
		{
			get
			{
				return this._CurrentPageName;
			}
			set
			{
				this._CurrentPageName = value;
			}
		}

		// Token: 0x17000140 RID: 320
		// (get) Token: 0x0600065A RID: 1626 RVA: 0x0002D260 File Offset: 0x0002B460
		// (set) Token: 0x0600065B RID: 1627 RVA: 0x00004A0D File Offset: 0x00002C0D
		public ChartTheme ChartTheme
		{
			get
			{
				return this._ChartTheme;
			}
			set
			{
				this._ChartTheme = value;
			}
		}

		// Token: 0x17000141 RID: 321
		// (get) Token: 0x0600065C RID: 1628 RVA: 0x0002D278 File Offset: 0x0002B478
		// (set) Token: 0x0600065D RID: 1629 RVA: 0x00004A18 File Offset: 0x00002C18
		public bool IsSwitchingChart
		{
			get
			{
				return this._IsSwitchingChart;
			}
			set
			{
				this._IsSwitchingChart = value;
			}
		}

		// Token: 0x17000142 RID: 322
		// (get) Token: 0x0600065E RID: 1630 RVA: 0x0002D290 File Offset: 0x0002B490
		// (set) Token: 0x0600065F RID: 1631 RVA: 0x00004A23 File Offset: 0x00002C23
		public string NoShowTooltips
		{
			get
			{
				return this._NoShowTooltips;
			}
			set
			{
				this._NoShowTooltips = value;
			}
		}

		// Token: 0x17000143 RID: 323
		// (get) Token: 0x06000660 RID: 1632 RVA: 0x0002D2A8 File Offset: 0x0002B4A8
		// (set) Token: 0x06000661 RID: 1633 RVA: 0x00004A2E File Offset: 0x00002C2E
		public int TimerSpeed
		{
			get
			{
				return this._TimerSpeed;
			}
			set
			{
				this._TimerSpeed = value;
			}
		}

		// Token: 0x17000144 RID: 324
		// (get) Token: 0x06000662 RID: 1634 RVA: 0x0002D2C0 File Offset: 0x0002B4C0
		// (set) Token: 0x06000663 RID: 1635 RVA: 0x00004A39 File Offset: 0x00002C39
		public bool IsStarted
		{
			get
			{
				return this._IsStarted;
			}
			set
			{
				this._IsStarted = value;
				if (value)
				{
					this.method_1();
				}
				else
				{
					this.method_2();
				}
			}
		}

		// Token: 0x17000145 RID: 325
		// (get) Token: 0x06000664 RID: 1636 RVA: 0x0002D2D8 File Offset: 0x0002B4D8
		// (set) Token: 0x06000665 RID: 1637 RVA: 0x00004A55 File Offset: 0x00002C55
		public bool IfDisableOpenCloseSound
		{
			get
			{
				return this._IfDisableOpenCloseSound;
			}
			set
			{
				this._IfDisableOpenCloseSound = value;
			}
		}

		// Token: 0x17000146 RID: 326
		// (get) Token: 0x06000666 RID: 1638 RVA: 0x0002D2F0 File Offset: 0x0002B4F0
		// (set) Token: 0x06000667 RID: 1639 RVA: 0x0002D308 File Offset: 0x0002B508
		public PeriodType? AutoPlayPeriodType
		{
			get
			{
				return this._AutoPlayPeriodType;
			}
			set
			{
				PeriodType? autoPlayPeriodType = this._AutoPlayPeriodType;
				PeriodType? periodType = value;
				if (!(autoPlayPeriodType.GetValueOrDefault() == periodType.GetValueOrDefault() & autoPlayPeriodType != null == (periodType != null)))
				{
					this._AutoPlayPeriodType = value;
					this.method_9();
				}
			}
		}

		// Token: 0x17000147 RID: 327
		// (get) Token: 0x06000668 RID: 1640 RVA: 0x0002D350 File Offset: 0x0002B550
		// (set) Token: 0x06000669 RID: 1641 RVA: 0x0002D368 File Offset: 0x0002B568
		public int? AutoPlayPeriodUnits
		{
			get
			{
				return this._AutoPlayPeriodUnits;
			}
			set
			{
				int? autoPlayPeriodUnits = this._AutoPlayPeriodUnits;
				int? num = value;
				if (!(autoPlayPeriodUnits.GetValueOrDefault() == num.GetValueOrDefault() & autoPlayPeriodUnits != null == (num != null)))
				{
					this._AutoPlayPeriodUnits = value;
					this.method_9();
				}
			}
		}

		// Token: 0x17000148 RID: 328
		// (get) Token: 0x0600066A RID: 1642 RVA: 0x0002D3B0 File Offset: 0x0002B5B0
		// (set) Token: 0x0600066B RID: 1643 RVA: 0x00004A60 File Offset: 0x00002C60
		public bool IfSymbSwitchShowCurrDT
		{
			get
			{
				return this._IfSymbSwitchShowCurrDT;
			}
			set
			{
				if (this._IfSymbSwitchShowCurrDT != value)
				{
					this._IfSymbSwitchShowCurrDT = value;
					this.method_10();
				}
			}
		}

		// Token: 0x17000149 RID: 329
		// (get) Token: 0x0600066C RID: 1644 RVA: 0x0002D3C8 File Offset: 0x0002B5C8
		// (set) Token: 0x0600066D RID: 1645 RVA: 0x00004A7A File Offset: 0x00002C7A
		public PeriodType DataSrcPeriodType
		{
			get
			{
				return this._DataSrcPeriodType;
			}
			set
			{
				this._DataSrcPeriodType = value;
			}
		}

		// Token: 0x1700014A RID: 330
		// (get) Token: 0x0600066E RID: 1646 RVA: 0x0002D3E0 File Offset: 0x0002B5E0
		// (set) Token: 0x0600066F RID: 1647 RVA: 0x00004A85 File Offset: 0x00002C85
		public int DataSrcPeriodUnits
		{
			get
			{
				return this._DataSrcPeriodUnits;
			}
			set
			{
				this._DataSrcPeriodUnits = value;
			}
		}

		// Token: 0x1700014B RID: 331
		// (get) Token: 0x06000670 RID: 1648 RVA: 0x0002D3F8 File Offset: 0x0002B5F8
		// (set) Token: 0x06000671 RID: 1649 RVA: 0x00004A90 File Offset: 0x00002C90
		public bool IsSpanMoveNext
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x1700014C RID: 332
		// (get) Token: 0x06000672 RID: 1650 RVA: 0x0002D410 File Offset: 0x0002B610
		// (set) Token: 0x06000673 RID: 1651 RVA: 0x00004A9B File Offset: 0x00002C9B
		public bool IsSpanMovePrev
		{
			get
			{
				return this.bool_1;
			}
			set
			{
				this.bool_1 = value;
			}
		}

		// Token: 0x1700014D RID: 333
		// (get) Token: 0x06000674 RID: 1652 RVA: 0x0002D428 File Offset: 0x0002B628
		// (set) Token: 0x06000675 RID: 1653 RVA: 0x00004AA6 File Offset: 0x00002CA6
		public DateTime? LastSpanMoveDT
		{
			get
			{
				return this.nullable_0;
			}
			set
			{
				this.nullable_0 = value;
			}
		}

		// Token: 0x1700014E RID: 334
		// (get) Token: 0x06000676 RID: 1654 RVA: 0x0002D440 File Offset: 0x0002B640
		public bool IsJustSpanMoved
		{
			get
			{
				bool result;
				if (!this.bool_1)
				{
					result = this.bool_0;
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x1700014F RID: 335
		// (get) Token: 0x06000677 RID: 1655 RVA: 0x0002D464 File Offset: 0x0002B664
		// (set) Token: 0x06000678 RID: 1656 RVA: 0x00004AB1 File Offset: 0x00002CB1
		public ChtCtrl SpanMoveChtCtrl
		{
			get
			{
				return this.chtCtrl_0;
			}
			set
			{
				this.chtCtrl_0 = value;
			}
		}

		// Token: 0x17000150 RID: 336
		// (get) Token: 0x06000679 RID: 1657 RVA: 0x0002D47C File Offset: 0x0002B67C
		// (set) Token: 0x0600067A RID: 1658 RVA: 0x00004ABC File Offset: 0x00002CBC
		public CfmmcAutoDnldConfig CfmmcAutoDnldConfig
		{
			get
			{
				return this._CfmmcAutoDnldConfig;
			}
			set
			{
				this._CfmmcAutoDnldConfig = value;
			}
		}

		// Token: 0x17000151 RID: 337
		// (get) Token: 0x0600067B RID: 1659 RVA: 0x0002D494 File Offset: 0x0002B694
		// (set) Token: 0x0600067C RID: 1660 RVA: 0x00004AC7 File Offset: 0x00002CC7
		public int TradingUnits
		{
			get
			{
				return this._TradingUnits;
			}
			set
			{
				this._TradingUnits = value;
			}
		}

		// Token: 0x17000152 RID: 338
		// (get) Token: 0x0600067D RID: 1661 RVA: 0x0002D4AC File Offset: 0x0002B6AC
		// (set) Token: 0x0600067E RID: 1662 RVA: 0x00004AD2 File Offset: 0x00002CD2
		public decimal TradingPrice
		{
			get
			{
				return this._TradingPrice;
			}
			set
			{
				this._TradingPrice = value;
			}
		}

		// Token: 0x17000153 RID: 339
		// (get) Token: 0x0600067F RID: 1663 RVA: 0x0002D4C4 File Offset: 0x0002B6C4
		// (set) Token: 0x06000680 RID: 1664 RVA: 0x00004ADD File Offset: 0x00002CDD
		public bool IfPauseAtDayEnd
		{
			get
			{
				return this._IsOrderConfmNeeded;
			}
			set
			{
				this._IsOrderConfmNeeded = value;
			}
		}

		// Token: 0x17000154 RID: 340
		// (get) Token: 0x06000681 RID: 1665 RVA: 0x0002D4DC File Offset: 0x0002B6DC
		// (set) Token: 0x06000682 RID: 1666 RVA: 0x00004AE8 File Offset: 0x00002CE8
		public bool IfCloseNewTransFirst
		{
			get
			{
				return this._IfCloseNewTransFirst;
			}
			set
			{
				this._IfCloseNewTransFirst = value;
			}
		}

		// Token: 0x17000155 RID: 341
		// (get) Token: 0x06000683 RID: 1667 RVA: 0x0002D4F4 File Offset: 0x0002B6F4
		// (set) Token: 0x06000684 RID: 1668 RVA: 0x00004AF3 File Offset: 0x00002CF3
		public bool IfROpenFixedAmt
		{
			get
			{
				return this._IfROpenFixedAmt;
			}
			set
			{
				this._IfROpenFixedAmt = value;
			}
		}

		// Token: 0x17000156 RID: 342
		// (get) Token: 0x06000685 RID: 1669 RVA: 0x0002D50C File Offset: 0x0002B70C
		// (set) Token: 0x06000686 RID: 1670 RVA: 0x00004AFE File Offset: 0x00002CFE
		public decimal? ROpenRatio
		{
			get
			{
				return this._ROpenRatio;
			}
			set
			{
				this._ROpenRatio = value;
			}
		}

		// Token: 0x17000157 RID: 343
		// (get) Token: 0x06000687 RID: 1671 RVA: 0x0002D524 File Offset: 0x0002B724
		// (set) Token: 0x06000688 RID: 1672 RVA: 0x00004B09 File Offset: 0x00002D09
		public int? ROpenFixedAmt
		{
			get
			{
				return this._ROpenFixedAmt;
			}
			set
			{
				this._ROpenFixedAmt = value;
			}
		}

		// Token: 0x17000158 RID: 344
		// (get) Token: 0x06000689 RID: 1673 RVA: 0x0002D53C File Offset: 0x0002B73C
		// (set) Token: 0x0600068A RID: 1674 RVA: 0x00004B14 File Offset: 0x00002D14
		public bool IfROpenNoShowCnfmDlg
		{
			get
			{
				return this._IfROpenNoShowCnfmDlg;
			}
			set
			{
				this._IfROpenNoShowCnfmDlg = value;
			}
		}

		// Token: 0x17000159 RID: 345
		// (get) Token: 0x0600068B RID: 1675 RVA: 0x0002D554 File Offset: 0x0002B754
		// (set) Token: 0x0600068C RID: 1676 RVA: 0x00004B1F File Offset: 0x00002D1F
		public bool IfNotAutoCancelExistingOpenTransOrder
		{
			get
			{
				return this._IfNotAutoCancelExistingOpenTransOrder;
			}
			set
			{
				this._IfNotAutoCancelExistingOpenTransOrder = value;
			}
		}

		// Token: 0x1700015A RID: 346
		// (get) Token: 0x0600068D RID: 1677 RVA: 0x0002D56C File Offset: 0x0002B76C
		// (set) Token: 0x0600068E RID: 1678 RVA: 0x00004B2A File Offset: 0x00002D2A
		public bool IfNotAutoCancelExistingCloseTransOrder
		{
			get
			{
				return this._IfNotAutoCancelExistingCloseTransOrder;
			}
			set
			{
				this._IfNotAutoCancelExistingCloseTransOrder = value;
			}
		}

		// Token: 0x1700015B RID: 347
		// (get) Token: 0x0600068F RID: 1679 RVA: 0x0002D584 File Offset: 0x0002B784
		// (set) Token: 0x06000690 RID: 1680 RVA: 0x00004B35 File Offset: 0x00002D35
		public bool EnableShortForStock
		{
			get
			{
				return this._EnableShortForStock;
			}
			set
			{
				if (this._EnableShortForStock != value)
				{
					this._EnableShortForStock = value;
					this.method_4();
				}
			}
		}

		// Token: 0x1700015C RID: 348
		// (get) Token: 0x06000691 RID: 1681 RVA: 0x0002D59C File Offset: 0x0002B79C
		// (set) Token: 0x06000692 RID: 1682 RVA: 0x00004B4F File Offset: 0x00002D4F
		public bool EnableT0ForStock
		{
			get
			{
				return this._EnableT0ForStock;
			}
			set
			{
				this._EnableT0ForStock = value;
			}
		}

		// Token: 0x1700015D RID: 349
		// (get) Token: 0x06000693 RID: 1683 RVA: 0x0002D5B4 File Offset: 0x0002B7B4
		// (set) Token: 0x06000694 RID: 1684 RVA: 0x0002D5CC File Offset: 0x0002B7CC
		public StockRestorationMethod? StockRestorationMethod
		{
			get
			{
				return this._StockRestorationMethod;
			}
			set
			{
				StockRestorationMethod? stockRestorationMethod = this._StockRestorationMethod;
				StockRestorationMethod? stockRestorationMethod2 = value;
				if (!(stockRestorationMethod.GetValueOrDefault() == stockRestorationMethod2.GetValueOrDefault() & stockRestorationMethod != null == (stockRestorationMethod2 != null)))
				{
					this._StockRestorationMethod = value;
					this.method_7();
				}
			}
		}

		// Token: 0x1700015E RID: 350
		// (get) Token: 0x06000695 RID: 1685 RVA: 0x0002D614 File Offset: 0x0002B814
		public bool StockRstMethodIsNone
		{
			get
			{
				bool result;
				if (this.StockRestorationMethod != null)
				{
					StockRestorationMethod? stockRestorationMethod = this.StockRestorationMethod;
					result = (stockRestorationMethod.GetValueOrDefault() == TEx.StockRestorationMethod.None & stockRestorationMethod != null);
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x1700015F RID: 351
		// (get) Token: 0x06000696 RID: 1686 RVA: 0x0002D654 File Offset: 0x0002B854
		// (set) Token: 0x06000697 RID: 1687 RVA: 0x00004B5A File Offset: 0x00002D5A
		public bool IfNoBonusShare
		{
			get
			{
				return this._IfNoBonusShare;
			}
			set
			{
				this._IfNoBonusShare = value;
			}
		}

		// Token: 0x17000160 RID: 352
		// (get) Token: 0x06000698 RID: 1688 RVA: 0x0002D66C File Offset: 0x0002B86C
		// (set) Token: 0x06000699 RID: 1689 RVA: 0x00004B65 File Offset: 0x00002D65
		public bool IfNoDivident
		{
			get
			{
				return this._IfNoDivident;
			}
			set
			{
				this._IfNoDivident = value;
			}
		}

		// Token: 0x17000161 RID: 353
		// (get) Token: 0x0600069A RID: 1690 RVA: 0x0002D684 File Offset: 0x0002B884
		// (set) Token: 0x0600069B RID: 1691 RVA: 0x00004B70 File Offset: 0x00002D70
		public RationedShareTreatmt? RationedShareTreatmt
		{
			get
			{
				return this._RationedShareTreatmt;
			}
			set
			{
				this._RationedShareTreatmt = value;
			}
		}

		// Token: 0x17000162 RID: 354
		// (get) Token: 0x0600069C RID: 1692 RVA: 0x0002D69C File Offset: 0x0002B89C
		// (set) Token: 0x0600069D RID: 1693 RVA: 0x00004B7B File Offset: 0x00002D7B
		public bool IfDispDayDivLine
		{
			get
			{
				return this._IfDispDayDivLine;
			}
			set
			{
				this._IfDispDayDivLine = value;
			}
		}

		// Token: 0x17000163 RID: 355
		// (get) Token: 0x0600069E RID: 1694 RVA: 0x0002D6B4 File Offset: 0x0002B8B4
		// (set) Token: 0x0600069F RID: 1695 RVA: 0x00004B86 File Offset: 0x00002D86
		public TimeUnit PeriodOfChartDispDayDivLine
		{
			get
			{
				return this._PeriodOfChartDispDayDivLine;
			}
			set
			{
				this._PeriodOfChartDispDayDivLine = value;
			}
		}

		// Token: 0x17000164 RID: 356
		// (get) Token: 0x060006A0 RID: 1696 RVA: 0x0002D6CC File Offset: 0x0002B8CC
		// (set) Token: 0x060006A1 RID: 1697 RVA: 0x00004B91 File Offset: 0x00002D91
		public int DefaultSticksPerChart
		{
			get
			{
				return this._DefaultSticksPerChart;
			}
			set
			{
				this._DefaultSticksPerChart = value;
			}
		}

		// Token: 0x17000165 RID: 357
		// (get) Token: 0x060006A2 RID: 1698 RVA: 0x0002D6E4 File Offset: 0x0002B8E4
		// (set) Token: 0x060006A3 RID: 1699 RVA: 0x00004B9C File Offset: 0x00002D9C
		public int? NMinsPeriodUnits
		{
			get
			{
				return this._NMinsPeriodUnits;
			}
			set
			{
				this._NMinsPeriodUnits = value;
			}
		}

		// Token: 0x17000166 RID: 358
		// (get) Token: 0x060006A4 RID: 1700 RVA: 0x0002D6FC File Offset: 0x0002B8FC
		// (set) Token: 0x060006A5 RID: 1701 RVA: 0x00004BA7 File Offset: 0x00002DA7
		public int? NHoursPeriodUnits
		{
			get
			{
				return this._NHoursPeriodUnits;
			}
			set
			{
				this._NHoursPeriodUnits = value;
			}
		}

		// Token: 0x17000167 RID: 359
		// (get) Token: 0x060006A6 RID: 1702 RVA: 0x0002D714 File Offset: 0x0002B914
		// (set) Token: 0x060006A7 RID: 1703 RVA: 0x00004BB2 File Offset: 0x00002DB2
		public int? NDaysPeriodUnits
		{
			get
			{
				return this._NDaysPeriodUnits;
			}
			set
			{
				this._NDaysPeriodUnits = value;
			}
		}

		// Token: 0x17000168 RID: 360
		// (get) Token: 0x060006A8 RID: 1704 RVA: 0x0002D72C File Offset: 0x0002B92C
		// (set) Token: 0x060006A9 RID: 1705 RVA: 0x00004BBD File Offset: 0x00002DBD
		public Font LastSelectedFont
		{
			get
			{
				return this._LastSelectedFont;
			}
			set
			{
				this._LastSelectedFont = value;
			}
		}

		// Token: 0x17000169 RID: 361
		// (get) Token: 0x060006AA RID: 1706 RVA: 0x0002D744 File Offset: 0x0002B944
		// (set) Token: 0x060006AB RID: 1707 RVA: 0x00004BC8 File Offset: 0x00002DC8
		public Color? LastSelectedFontColor
		{
			get
			{
				return this._LastSelectedFontColor;
			}
			set
			{
				this._LastSelectedFontColor = value;
			}
		}

		// Token: 0x1700016A RID: 362
		// (get) Token: 0x060006AC RID: 1708 RVA: 0x0002D75C File Offset: 0x0002B95C
		// (set) Token: 0x060006AD RID: 1709 RVA: 0x00004BD3 File Offset: 0x00002DD3
		public Color? LastSelectedLineColor
		{
			get
			{
				return this._LastSelectedLineColor;
			}
			set
			{
				this._LastSelectedLineColor = value;
			}
		}

		// Token: 0x1700016B RID: 363
		// (get) Token: 0x060006AE RID: 1710 RVA: 0x0002D774 File Offset: 0x0002B974
		// (set) Token: 0x060006AF RID: 1711 RVA: 0x00004BDE File Offset: 0x00002DDE
		public KLineType? KLineType
		{
			get
			{
				return this._KLineType;
			}
			set
			{
				this._KLineType = value;
			}
		}

		// Token: 0x1700016C RID: 364
		// (get) Token: 0x060006B0 RID: 1712 RVA: 0x0002D78C File Offset: 0x0002B98C
		// (set) Token: 0x060006B1 RID: 1713 RVA: 0x00004BE9 File Offset: 0x00002DE9
		public bool IfShowDayOfWeek
		{
			get
			{
				return this._IfShowDayOfWeek;
			}
			set
			{
				if (this._IfShowDayOfWeek != value)
				{
					this._IfShowDayOfWeek = value;
					this.method_12();
				}
			}
		}

		// Token: 0x1700016D RID: 365
		// (get) Token: 0x060006B2 RID: 1714 RVA: 0x0002D7A4 File Offset: 0x0002B9A4
		// (set) Token: 0x060006B3 RID: 1715 RVA: 0x00004C03 File Offset: 0x00002E03
		public Dictionary<string, Dictionary<string, object>> UserDrawObjParamsDict
		{
			get
			{
				return this._UserDrawObjParamsDict;
			}
			set
			{
				this._UserDrawObjParamsDict = value;
			}
		}

		// Token: 0x1700016E RID: 366
		// (get) Token: 0x060006B4 RID: 1716 RVA: 0x0002D7BC File Offset: 0x0002B9BC
		// (set) Token: 0x060006B5 RID: 1717 RVA: 0x00004C0E File Offset: 0x00002E0E
		public bool NoShowHighLowMark
		{
			get
			{
				return this._NoShowHighLowMark;
			}
			set
			{
				this._NoShowHighLowMark = value;
			}
		}

		// Token: 0x1700016F RID: 367
		// (get) Token: 0x060006B6 RID: 1718 RVA: 0x0002D7D4 File Offset: 0x0002B9D4
		// (set) Token: 0x060006B7 RID: 1719 RVA: 0x00004C19 File Offset: 0x00002E19
		public TimeUnit PeriodOfChartDispTransArrow
		{
			get
			{
				return this._PeriodOfChartDispTransArrow;
			}
			set
			{
				this._PeriodOfChartDispTransArrow = value;
			}
		}

		// Token: 0x17000170 RID: 368
		// (get) Token: 0x060006B8 RID: 1720 RVA: 0x0002D7EC File Offset: 0x0002B9EC
		// (set) Token: 0x060006B9 RID: 1721 RVA: 0x00004C24 File Offset: 0x00002E24
		public bool IfShowNoTransArrow
		{
			get
			{
				return this._IfShowNoTransArrow;
			}
			set
			{
				this._IfShowNoTransArrow = value;
			}
		}

		// Token: 0x17000171 RID: 369
		// (get) Token: 0x060006BA RID: 1722 RVA: 0x0002D804 File Offset: 0x0002BA04
		// (set) Token: 0x060006BB RID: 1723 RVA: 0x00004C2F File Offset: 0x00002E2F
		public bool IfShowAllTransArrow
		{
			get
			{
				return this._IfShowAllTransArrow;
			}
			set
			{
				this._IfShowAllTransArrow = value;
			}
		}

		// Token: 0x17000172 RID: 370
		// (get) Token: 0x060006BC RID: 1724 RVA: 0x0002D81C File Offset: 0x0002BA1C
		// (set) Token: 0x060006BD RID: 1725 RVA: 0x00004C3A File Offset: 0x00002E3A
		public TransArrowType TransArrowType
		{
			get
			{
				return this._TransArrowType;
			}
			set
			{
				if (this._TransArrowType != value)
				{
					this._TransArrowType = value;
					this.method_8();
				}
			}
		}

		// Token: 0x17000173 RID: 371
		// (get) Token: 0x060006BE RID: 1726 RVA: 0x0002D834 File Offset: 0x0002BA34
		// (set) Token: 0x060006BF RID: 1727 RVA: 0x00004C54 File Offset: 0x00002E54
		public bool IfAlwaysShowTransNoteBox
		{
			get
			{
				return this._IfAlwaysShowTransNoteBox;
			}
			set
			{
				this._IfAlwaysShowTransNoteBox = value;
			}
		}

		// Token: 0x17000174 RID: 372
		// (get) Token: 0x060006C0 RID: 1728 RVA: 0x0002D84C File Offset: 0x0002BA4C
		// (set) Token: 0x060006C1 RID: 1729 RVA: 0x00004C5F File Offset: 0x00002E5F
		public bool IfShowAllNotesWhenAlwaysShowTransNoteBox
		{
			get
			{
				return this._IfShowAllNotesWhenAlwaysShowTransNoteBox;
			}
			set
			{
				this._IfShowAllNotesWhenAlwaysShowTransNoteBox = value;
			}
		}

		// Token: 0x17000175 RID: 373
		// (get) Token: 0x060006C2 RID: 1730 RVA: 0x0002D864 File Offset: 0x0002BA64
		// (set) Token: 0x060006C3 RID: 1731 RVA: 0x00004C6A File Offset: 0x00002E6A
		public bool IfHideTransNoteBorder
		{
			get
			{
				return this._IfHideTransNoteBorder;
			}
			set
			{
				this._IfHideTransNoteBorder = value;
			}
		}

		// Token: 0x17000176 RID: 374
		// (get) Token: 0x060006C4 RID: 1732 RVA: 0x0002D87C File Offset: 0x0002BA7C
		// (set) Token: 0x060006C5 RID: 1733 RVA: 0x00004C75 File Offset: 0x00002E75
		public bool IfTransNoteFillTransparent
		{
			get
			{
				return this._IfTransNoteFillTransparent;
			}
			set
			{
				this._IfTransNoteFillTransparent = value;
			}
		}

		// Token: 0x17000177 RID: 375
		// (get) Token: 0x060006C6 RID: 1734 RVA: 0x0002D894 File Offset: 0x0002BA94
		// (set) Token: 0x060006C7 RID: 1735 RVA: 0x00004C80 File Offset: 0x00002E80
		public bool IfDisableTsOdrLine
		{
			get
			{
				return this._IfDisableTsOdrLine;
			}
			set
			{
				this._IfDisableTsOdrLine = value;
			}
		}

		// Token: 0x17000178 RID: 376
		// (get) Token: 0x060006C8 RID: 1736 RVA: 0x0002D8AC File Offset: 0x0002BAAC
		// (set) Token: 0x060006C9 RID: 1737 RVA: 0x00004C8B File Offset: 0x00002E8B
		public bool IfShowTransTabsBar
		{
			get
			{
				return this._IfShowTransTabsBar;
			}
			set
			{
				this._IfShowTransTabsBar = value;
			}
		}

		// Token: 0x17000179 RID: 377
		// (get) Token: 0x060006CA RID: 1738 RVA: 0x0002D8C4 File Offset: 0x0002BAC4
		// (set) Token: 0x060006CB RID: 1739 RVA: 0x00004C96 File Offset: 0x00002E96
		public eDockSide AcctTransBar_DockSide
		{
			get
			{
				return this._AcctTransBar_DockSide;
			}
			set
			{
				this._AcctTransBar_DockSide = value;
			}
		}

		// Token: 0x1700017A RID: 378
		// (get) Token: 0x060006CC RID: 1740 RVA: 0x0002D8DC File Offset: 0x0002BADC
		// (set) Token: 0x060006CD RID: 1741 RVA: 0x00004CA1 File Offset: 0x00002EA1
		public string DotNetBarLayoutString
		{
			get
			{
				return this._DotNetBarLayoutString;
			}
			set
			{
				this._DotNetBarLayoutString = value;
			}
		}

		// Token: 0x1700017B RID: 379
		// (get) Token: 0x060006CE RID: 1742 RVA: 0x0002D8F4 File Offset: 0x0002BAF4
		// (set) Token: 0x060006CF RID: 1743 RVA: 0x00004CAC File Offset: 0x00002EAC
		public bool IsTransTabBarMaximized
		{
			get
			{
				return this._IsTransTabBarMaximized;
			}
			set
			{
				this._IsTransTabBarMaximized = value;
			}
		}

		// Token: 0x1700017C RID: 380
		// (get) Token: 0x060006D0 RID: 1744 RVA: 0x0002D90C File Offset: 0x0002BB0C
		// (set) Token: 0x060006D1 RID: 1745 RVA: 0x00004CB7 File Offset: 0x00002EB7
		public bool IsTransTabMaximized
		{
			get
			{
				return this._IsTransTabMaximized;
			}
			set
			{
				this._IsTransTabMaximized = value;
			}
		}

		// Token: 0x1700017D RID: 381
		// (get) Token: 0x060006D2 RID: 1746 RVA: 0x0002D924 File Offset: 0x0002BB24
		// (set) Token: 0x060006D3 RID: 1747 RVA: 0x00004CC2 File Offset: 0x00002EC2
		public int? LastFuncTabsIdx
		{
			get
			{
				return this._LastFuncTabsIdx;
			}
			set
			{
				this._LastFuncTabsIdx = value;
			}
		}

		// Token: 0x1700017E RID: 382
		// (get) Token: 0x060006D4 RID: 1748 RVA: 0x0002D93C File Offset: 0x0002BB3C
		// (set) Token: 0x060006D5 RID: 1749 RVA: 0x00004CCD File Offset: 0x00002ECD
		public int? LastMktSymbTabsIdx
		{
			get
			{
				return this._LastMktSymbTabsIdx;
			}
			set
			{
				this._LastMktSymbTabsIdx = value;
			}
		}

		// Token: 0x1700017F RID: 383
		// (get) Token: 0x060006D6 RID: 1750 RVA: 0x0002D954 File Offset: 0x0002BB54
		// (set) Token: 0x060006D7 RID: 1751 RVA: 0x00004CD8 File Offset: 0x00002ED8
		public bool IfDisableAutoShowCurrTransTab
		{
			get
			{
				return this._IfDisableAutoShowCurrTransTab;
			}
			set
			{
				this._IfDisableAutoShowCurrTransTab = value;
			}
		}

		// Token: 0x17000180 RID: 384
		// (get) Token: 0x060006D8 RID: 1752 RVA: 0x0002D96C File Offset: 0x0002BB6C
		// (set) Token: 0x060006D9 RID: 1753 RVA: 0x00004CE3 File Offset: 0x00002EE3
		public bool IfAutoOpenCloseInTradingTab
		{
			get
			{
				return this._IfAutoOpenCloseInTradingTab;
			}
			set
			{
				this._IfAutoOpenCloseInTradingTab = value;
			}
		}

		// Token: 0x17000181 RID: 385
		// (get) Token: 0x060006DA RID: 1754 RVA: 0x0002D984 File Offset: 0x0002BB84
		// (set) Token: 0x060006DB RID: 1755 RVA: 0x00004CEE File Offset: 0x00002EEE
		public bool IfFollowPrcInTradingTab
		{
			get
			{
				return this._IfFollowPrcInTradingTab;
			}
			set
			{
				if (this._IfFollowPrcInTradingTab != value)
				{
					this._IfFollowPrcInTradingTab = value;
					this.method_6();
				}
			}
		}

		// Token: 0x17000182 RID: 386
		// (get) Token: 0x060006DC RID: 1756 RVA: 0x0002D99C File Offset: 0x0002BB9C
		// (set) Token: 0x060006DD RID: 1757 RVA: 0x00004D08 File Offset: 0x00002F08
		public bool IfNoConfClsTransWhenDblClick
		{
			get
			{
				return this._IfNoConfClsTransWhenDblClick;
			}
			set
			{
				this._IfNoConfClsTransWhenDblClick = value;
			}
		}

		// Token: 0x17000183 RID: 387
		// (get) Token: 0x060006DE RID: 1758 RVA: 0x0002D9B4 File Offset: 0x0002BBB4
		// (set) Token: 0x060006DF RID: 1759 RVA: 0x00004D13 File Offset: 0x00002F13
		public bool IfNoSyncToolBarAndTradingTabPriceUnits
		{
			get
			{
				return this._IfNoSyncToolBarAndTradingTabPriceUnits;
			}
			set
			{
				this._IfNoSyncToolBarAndTradingTabPriceUnits = value;
			}
		}

		// Token: 0x17000184 RID: 388
		// (get) Token: 0x060006E0 RID: 1760 RVA: 0x0002D9CC File Offset: 0x0002BBCC
		// (set) Token: 0x060006E1 RID: 1761 RVA: 0x00004D1E File Offset: 0x00002F1E
		public bool IfShowIndividualShownOpenTrans
		{
			get
			{
				return this._IfShowIndividualShownOpenTrans;
			}
			set
			{
				this._IfShowIndividualShownOpenTrans = value;
			}
		}

		// Token: 0x17000185 RID: 389
		// (get) Token: 0x060006E2 RID: 1762 RVA: 0x0002D9E4 File Offset: 0x0002BBE4
		// (set) Token: 0x060006E3 RID: 1763 RVA: 0x00004D29 File Offset: 0x00002F29
		public bool IfShowSymbCNNameInOpenTransGridView
		{
			get
			{
				return this._IfShowSymbCNNameInOpenTransGridView;
			}
			set
			{
				if (this._IfShowSymbCNNameInOpenTransGridView != value)
				{
					this._IfShowSymbCNNameInOpenTransGridView = value;
					this.method_3();
				}
			}
		}

		// Token: 0x17000186 RID: 390
		// (get) Token: 0x060006E4 RID: 1764 RVA: 0x0002D9FC File Offset: 0x0002BBFC
		// (set) Token: 0x060006E5 RID: 1765 RVA: 0x00004D43 File Offset: 0x00002F43
		public bool IfNotShowAcctInfoOnTransTabHeader
		{
			get
			{
				return this._IfNotShowAcctInfoOnTransTabHeader;
			}
			set
			{
				if (this._IfNotShowAcctInfoOnTransTabHeader != value)
				{
					this._IfNotShowAcctInfoOnTransTabHeader = value;
					this.method_11();
				}
			}
		}

		// Token: 0x17000187 RID: 391
		// (get) Token: 0x060006E6 RID: 1766 RVA: 0x0002DA14 File Offset: 0x0002BC14
		// (set) Token: 0x060006E7 RID: 1767 RVA: 0x00004D5D File Offset: 0x00002F5D
		public bool IsInBlindTestMode
		{
			get
			{
				return this._IsInBlindTestMode;
			}
			set
			{
				if (this._IsInBlindTestMode != value)
				{
					this._IsInBlindTestMode = value;
					if (this._IsInBlindTestMode)
					{
						this.method_13();
					}
					else
					{
						this.method_14();
					}
				}
			}
		}

		// Token: 0x17000188 RID: 392
		// (get) Token: 0x060006E8 RID: 1768 RVA: 0x0002DA2C File Offset: 0x0002BC2C
		// (set) Token: 0x060006E9 RID: 1769 RVA: 0x00004D87 File Offset: 0x00002F87
		public bool IsSingleBlindTest
		{
			get
			{
				return this._IsSingleBlindTest;
			}
			set
			{
				this._IsSingleBlindTest = value;
			}
		}

		// Token: 0x17000189 RID: 393
		// (get) Token: 0x060006EA RID: 1770 RVA: 0x0002DA44 File Offset: 0x0002BC44
		// (set) Token: 0x060006EB RID: 1771 RVA: 0x00004D92 File Offset: 0x00002F92
		public int? LastSymbIDPriorToBlindTest
		{
			get
			{
				return this._LastSymbIDPriorToBlindTest;
			}
			set
			{
				this._LastSymbIDPriorToBlindTest = value;
			}
		}

		// Token: 0x1700018A RID: 394
		// (get) Token: 0x060006EC RID: 1772 RVA: 0x0002DA5C File Offset: 0x0002BC5C
		// (set) Token: 0x060006ED RID: 1773 RVA: 0x00004D9D File Offset: 0x00002F9D
		public DateTime? LastSymbDTPriorToBlindTest
		{
			get
			{
				return this._LastSymbDTPriorToBlindTest;
			}
			set
			{
				this._LastSymbDTPriorToBlindTest = value;
			}
		}

		// Token: 0x1700018B RID: 395
		// (get) Token: 0x060006EE RID: 1774 RVA: 0x0002DA74 File Offset: 0x0002BC74
		// (set) Token: 0x060006EF RID: 1775 RVA: 0x00004DA8 File Offset: 0x00002FA8
		public bool BlindTestForm_IfNoFtMI
		{
			get
			{
				return this._BlindTestForm_IfNoFtMI;
			}
			set
			{
				this._BlindTestForm_IfNoFtMI = value;
			}
		}

		// Token: 0x1700018C RID: 396
		// (get) Token: 0x060006F0 RID: 1776 RVA: 0x0002DA8C File Offset: 0x0002BC8C
		// (set) Token: 0x060006F1 RID: 1777 RVA: 0x00004DB3 File Offset: 0x00002FB3
		public bool BlindTestForm_IfFtIdx
		{
			get
			{
				return this._BlindTestForm_IfFtIdx;
			}
			set
			{
				this._BlindTestForm_IfFtIdx = value;
			}
		}

		// Token: 0x1700018D RID: 397
		// (get) Token: 0x060006F2 RID: 1778 RVA: 0x0002DAA4 File Offset: 0x0002BCA4
		// (set) Token: 0x060006F3 RID: 1779 RVA: 0x00004DBE File Offset: 0x00002FBE
		public bool BlindTestForm_IfFtMth
		{
			get
			{
				return this._BlindTestForm_IfFtMth;
			}
			set
			{
				this._BlindTestForm_IfFtMth = value;
			}
		}

		// Token: 0x1700018E RID: 398
		// (get) Token: 0x060006F4 RID: 1780 RVA: 0x0002DABC File Offset: 0x0002BCBC
		// (set) Token: 0x060006F5 RID: 1781 RVA: 0x00004DC9 File Offset: 0x00002FC9
		public bool BlindTestForm_IfStIdx
		{
			get
			{
				return this._BlindTestForm_IfStIdx;
			}
			set
			{
				this._BlindTestForm_IfStIdx = value;
			}
		}

		// Token: 0x1700018F RID: 399
		// (get) Token: 0x060006F6 RID: 1782 RVA: 0x0002DAD4 File Offset: 0x0002BCD4
		// (set) Token: 0x060006F7 RID: 1783 RVA: 0x00004DD4 File Offset: 0x00002FD4
		public bool BlindTestForm_IfStFond
		{
			get
			{
				return this._BlindTestForm_IfStFond;
			}
			set
			{
				this._BlindTestForm_IfStFond = value;
			}
		}

		// Token: 0x17000190 RID: 400
		// (get) Token: 0x060006F8 RID: 1784 RVA: 0x0002DAEC File Offset: 0x0002BCEC
		// (set) Token: 0x060006F9 RID: 1785 RVA: 0x00004DDF File Offset: 0x00002FDF
		public bool BlindTestForm_IfNoStMain
		{
			get
			{
				return this._BlindTestForm_IfNoStMain;
			}
			set
			{
				this._BlindTestForm_IfNoStMain = value;
			}
		}

		// Token: 0x17000191 RID: 401
		// (get) Token: 0x060006FA RID: 1786 RVA: 0x0002DB04 File Offset: 0x0002BD04
		// (set) Token: 0x060006FB RID: 1787 RVA: 0x00004DEA File Offset: 0x00002FEA
		public bool BlindTestForm_IfNoStZX
		{
			get
			{
				return this._BlindTestForm_IfNoStZX;
			}
			set
			{
				this._BlindTestForm_IfNoStZX = value;
			}
		}

		// Token: 0x17000192 RID: 402
		// (get) Token: 0x060006FC RID: 1788 RVA: 0x0002DB1C File Offset: 0x0002BD1C
		// (set) Token: 0x060006FD RID: 1789 RVA: 0x00004DF5 File Offset: 0x00002FF5
		public bool BlindTestForm_IfNoStCYB
		{
			get
			{
				return this._BlindTestForm_IfNoStCYB;
			}
			set
			{
				this._BlindTestForm_IfNoStCYB = value;
			}
		}

		// Token: 0x17000193 RID: 403
		// (get) Token: 0x060006FE RID: 1790 RVA: 0x0002DB34 File Offset: 0x0002BD34
		// (set) Token: 0x060006FF RID: 1791 RVA: 0x00004E00 File Offset: 0x00003000
		public bool BlindTestForm_IfNoStKCB
		{
			get
			{
				return this._BlindTestForm_IfNoStKCB;
			}
			set
			{
				this._BlindTestForm_IfNoStKCB = value;
			}
		}

		// Token: 0x17000194 RID: 404
		// (get) Token: 0x06000700 RID: 1792 RVA: 0x0002DB4C File Offset: 0x0002BD4C
		// (set) Token: 0x06000701 RID: 1793 RVA: 0x00004E0B File Offset: 0x0000300B
		public bool BlindTestForm_IfStConvBond
		{
			get
			{
				return this._BlindTestForm_IfStConvBond;
			}
			set
			{
				this._BlindTestForm_IfStConvBond = value;
			}
		}

		// Token: 0x17000195 RID: 405
		// (get) Token: 0x06000702 RID: 1794 RVA: 0x0002DB64 File Offset: 0x0002BD64
		// (set) Token: 0x06000703 RID: 1795 RVA: 0x00004E16 File Offset: 0x00003016
		public bool BlindTestForm_IfNoZixuan
		{
			get
			{
				return this._BlindTestForm_IfNoZixuan;
			}
			set
			{
				this._BlindTestForm_IfNoZixuan = value;
			}
		}

		// Token: 0x17000196 RID: 406
		// (get) Token: 0x06000704 RID: 1796 RVA: 0x0002DB7C File Offset: 0x0002BD7C
		// (set) Token: 0x06000705 RID: 1797 RVA: 0x00004E21 File Offset: 0x00003021
		public bool IfSaveSpeedOnQuit
		{
			get
			{
				return this._IfSaveSpeedOnQuit;
			}
			set
			{
				this._IfSaveSpeedOnQuit = value;
			}
		}

		// Token: 0x17000197 RID: 407
		// (get) Token: 0x06000706 RID: 1798 RVA: 0x0002DB94 File Offset: 0x0002BD94
		// (set) Token: 0x06000707 RID: 1799 RVA: 0x00004E2C File Offset: 0x0000302C
		public bool IfSaveWindowOnQuit
		{
			get
			{
				return this._IfSaveWindowOnQuit;
			}
			set
			{
				this._IfSaveWindowOnQuit = value;
			}
		}

		// Token: 0x17000198 RID: 408
		// (get) Token: 0x06000708 RID: 1800 RVA: 0x0002DBAC File Offset: 0x0002BDAC
		// (set) Token: 0x06000709 RID: 1801 RVA: 0x00004E37 File Offset: 0x00003037
		public bool IfAutoSavePageOnExit
		{
			get
			{
				return this._IfAutoSavePageOnExit;
			}
			set
			{
				this._IfAutoSavePageOnExit = value;
			}
		}

		// Token: 0x17000199 RID: 409
		// (get) Token: 0x0600070A RID: 1802 RVA: 0x0002DBC4 File Offset: 0x0002BDC4
		// (set) Token: 0x0600070B RID: 1803 RVA: 0x00004E42 File Offset: 0x00003042
		public bool IfConfirmQuit
		{
			get
			{
				return this._IfConfirmQuit;
			}
			set
			{
				this._IfConfirmQuit = value;
			}
		}

		// Token: 0x1700019A RID: 410
		// (get) Token: 0x0600070C RID: 1804 RVA: 0x0002DBDC File Offset: 0x0002BDDC
		// (set) Token: 0x0600070D RID: 1805 RVA: 0x00004E4D File Offset: 0x0000304D
		public string LastAcctTransExportDir
		{
			get
			{
				return this._LastAcctTransExportDir;
			}
			set
			{
				this._LastAcctTransExportDir = value;
			}
		}

		// Token: 0x1700019B RID: 411
		// (get) Token: 0x0600070E RID: 1806 RVA: 0x0002DBF4 File Offset: 0x0002BDF4
		// (set) Token: 0x0600070F RID: 1807 RVA: 0x00004E58 File Offset: 0x00003058
		public string LastHisDataExportDir
		{
			get
			{
				return this._LastHisDataExportDir;
			}
			set
			{
				this._LastHisDataExportDir = value;
			}
		}

		// Token: 0x1700019C RID: 412
		// (get) Token: 0x06000710 RID: 1808 RVA: 0x0002DC0C File Offset: 0x0002BE0C
		// (set) Token: 0x06000711 RID: 1809 RVA: 0x00004E63 File Offset: 0x00003063
		public string LastTransImportFilePath
		{
			get
			{
				return this._LastTransImportFilePath;
			}
			set
			{
				this._LastTransImportFilePath = value;
			}
		}

		// Token: 0x1700019D RID: 413
		// (get) Token: 0x06000712 RID: 1810 RVA: 0x0002DC24 File Offset: 0x0002BE24
		public bool BackupSyncDisabled
		{
			get
			{
				bool result;
				if (this.BackupSyncNotOnStartup && this.BackupSyncNotOnExit && !this.BackupSyncPeriodically)
				{
					result = true;
				}
				else if (this.BackupNoAcctTrans && this.BackupNoDrawObjs && this.BackupNoPages && this.BackupNoSymbParams && this.BackupNoUIParams)
				{
					result = this.BackupNoZiXuan;
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x1700019E RID: 414
		// (get) Token: 0x06000713 RID: 1811 RVA: 0x0002DC84 File Offset: 0x0002BE84
		// (set) Token: 0x06000714 RID: 1812 RVA: 0x00004E6E File Offset: 0x0000306E
		public bool BackupNoAcctTrans
		{
			get
			{
				return false;
			}
			set
			{
				this._BackupNoAcctTrans = value;
			}
		}

		// Token: 0x1700019F RID: 415
		// (get) Token: 0x06000715 RID: 1813 RVA: 0x0002DC84 File Offset: 0x0002BE84
		// (set) Token: 0x06000716 RID: 1814 RVA: 0x00004E79 File Offset: 0x00003079
		public bool BackupNoUIParams
		{
			get
			{
				return false;
			}
			set
			{
				this._BackupNoUIParams = value;
			}
		}

		// Token: 0x170001A0 RID: 416
		// (get) Token: 0x06000717 RID: 1815 RVA: 0x0002DC84 File Offset: 0x0002BE84
		// (set) Token: 0x06000718 RID: 1816 RVA: 0x00004E84 File Offset: 0x00003084
		public bool BackupNoSymbParams
		{
			get
			{
				return false;
			}
			set
			{
				this._BackupNoSymbParams = value;
			}
		}

		// Token: 0x170001A1 RID: 417
		// (get) Token: 0x06000719 RID: 1817 RVA: 0x0002DC84 File Offset: 0x0002BE84
		// (set) Token: 0x0600071A RID: 1818 RVA: 0x00004E8F File Offset: 0x0000308F
		public bool BackupNoPages
		{
			get
			{
				return false;
			}
			set
			{
				this._BackupNoPages = value;
			}
		}

		// Token: 0x170001A2 RID: 418
		// (get) Token: 0x0600071B RID: 1819 RVA: 0x0002DC84 File Offset: 0x0002BE84
		// (set) Token: 0x0600071C RID: 1820 RVA: 0x00004E9A File Offset: 0x0000309A
		public bool BackupNoZiXuan
		{
			get
			{
				return false;
			}
			set
			{
				this._BackupNoZiXuan = value;
			}
		}

		// Token: 0x170001A3 RID: 419
		// (get) Token: 0x0600071D RID: 1821 RVA: 0x0002DC84 File Offset: 0x0002BE84
		// (set) Token: 0x0600071E RID: 1822 RVA: 0x00004EA5 File Offset: 0x000030A5
		public bool BackupNoDrawObjs
		{
			get
			{
				return false;
			}
			set
			{
				this._BackupNoDrawObjs = value;
			}
		}

		// Token: 0x170001A4 RID: 420
		// (get) Token: 0x0600071F RID: 1823 RVA: 0x0002DC84 File Offset: 0x0002BE84
		// (set) Token: 0x06000720 RID: 1824 RVA: 0x00004EB0 File Offset: 0x000030B0
		public bool BackupSyncNotOnStartup
		{
			get
			{
				return false;
			}
			set
			{
				this._BackupSyncNotOnStartup = value;
			}
		}

		// Token: 0x170001A5 RID: 421
		// (get) Token: 0x06000721 RID: 1825 RVA: 0x0002DC84 File Offset: 0x0002BE84
		// (set) Token: 0x06000722 RID: 1826 RVA: 0x00004EBB File Offset: 0x000030BB
		public bool BackupSyncNotOnExit
		{
			get
			{
				return false;
			}
			set
			{
				this._BackupSyncNotOnExit = value;
			}
		}

		// Token: 0x170001A6 RID: 422
		// (get) Token: 0x06000723 RID: 1827 RVA: 0x0002DC98 File Offset: 0x0002BE98
		// (set) Token: 0x06000724 RID: 1828 RVA: 0x00004EC6 File Offset: 0x000030C6
		public BackupDirection? BackupDirection
		{
			get
			{
				return this._BackupDirection;
			}
			set
			{
				this._BackupDirection = value;
			}
		}

		// Token: 0x170001A7 RID: 423
		// (get) Token: 0x06000725 RID: 1829 RVA: 0x0002DCB0 File Offset: 0x0002BEB0
		// (set) Token: 0x06000726 RID: 1830 RVA: 0x00004ED1 File Offset: 0x000030D1
		public bool BackupSyncPeriodically
		{
			get
			{
				return this._BackupSyncPeriodically;
			}
			set
			{
				if (this._BackupSyncPeriodically != value)
				{
					this._BackupSyncPeriodically = value;
					this.method_5();
				}
			}
		}

		// Token: 0x170001A8 RID: 424
		// (get) Token: 0x06000727 RID: 1831 RVA: 0x0002DCC8 File Offset: 0x0002BEC8
		// (set) Token: 0x06000728 RID: 1832 RVA: 0x0002DCE0 File Offset: 0x0002BEE0
		public BackupSyncTimeInterval? BackupSyncTimeInterval
		{
			get
			{
				return this._BackupSyncTimeInterval;
			}
			set
			{
				BackupSyncTimeInterval? backupSyncTimeInterval = this._BackupSyncTimeInterval;
				BackupSyncTimeInterval? backupSyncTimeInterval2 = value;
				if (!(backupSyncTimeInterval.GetValueOrDefault() == backupSyncTimeInterval2.GetValueOrDefault() & backupSyncTimeInterval != null == (backupSyncTimeInterval2 != null)))
				{
					this._BackupSyncTimeInterval = value;
					this.method_5();
				}
			}
		}

		// Token: 0x170001A9 RID: 425
		// (get) Token: 0x06000729 RID: 1833 RVA: 0x0002DD28 File Offset: 0x0002BF28
		// (set) Token: 0x0600072A RID: 1834 RVA: 0x00004EEB File Offset: 0x000030EB
		public bool BackupSyncAutoOverwritingLocalFile
		{
			get
			{
				return this._BackupSyncAutoOverwritingLocalFile;
			}
			set
			{
				this._BackupSyncAutoOverwritingLocalFile = value;
			}
		}

		// Token: 0x170001AA RID: 426
		// (get) Token: 0x0600072B RID: 1835 RVA: 0x0002DD40 File Offset: 0x0002BF40
		// (set) Token: 0x0600072C RID: 1836 RVA: 0x00004EF6 File Offset: 0x000030F6
		public BackupSyncConflictTreatmt? BackupSyncConflictTreatmt
		{
			get
			{
				return this._BackupSyncConflictTreatmt;
			}
			set
			{
				this._BackupSyncConflictTreatmt = value;
			}
		}

		// Token: 0x0600072D RID: 1837 RVA: 0x0002DD58 File Offset: 0x0002BF58
		public double method_15()
		{
			double result = 1800000.0;
			if (Base.UI.Form.BackupSyncTimeInterval != null && Base.UI.Form.BackupSyncTimeInterval.Value > TEx.BackupSyncTimeInterval.PerHalfHour)
			{
				result = (double)(Base.UI.Form.BackupSyncTimeInterval.Value * (BackupSyncTimeInterval)60 * (BackupSyncTimeInterval)60 * (BackupSyncTimeInterval)1000);
			}
			return result;
		}

		// Token: 0x040002A3 RID: 675
		[CompilerGenerated]
		[NonSerialized]
		private EventHandler eventHandler_0;

		// Token: 0x040002A4 RID: 676
		[CompilerGenerated]
		[NonSerialized]
		private EventHandler eventHandler_1;

		// Token: 0x040002A5 RID: 677
		[CompilerGenerated]
		[NonSerialized]
		private EventHandler eventHandler_2;

		// Token: 0x040002A6 RID: 678
		[CompilerGenerated]
		[NonSerialized]
		private EventHandler eventHandler_3;

		// Token: 0x040002A7 RID: 679
		[CompilerGenerated]
		[NonSerialized]
		private EventHandler eventHandler_4;

		// Token: 0x040002A8 RID: 680
		[CompilerGenerated]
		[NonSerialized]
		private EventHandler eventHandler_5;

		// Token: 0x040002A9 RID: 681
		[CompilerGenerated]
		[NonSerialized]
		private EventHandler eventHandler_6;

		// Token: 0x040002AA RID: 682
		[CompilerGenerated]
		[NonSerialized]
		private EventHandler eventHandler_7;

		// Token: 0x040002AB RID: 683
		[CompilerGenerated]
		[NonSerialized]
		private EventHandler eventHandler_8;

		// Token: 0x040002AC RID: 684
		[CompilerGenerated]
		[NonSerialized]
		private EventHandler eventHandler_9;

		// Token: 0x040002AD RID: 685
		[CompilerGenerated]
		[NonSerialized]
		private EventHandler eventHandler_10;

		// Token: 0x040002AE RID: 686
		[CompilerGenerated]
		[NonSerialized]
		private EventHandler eventHandler_11;

		// Token: 0x040002AF RID: 687
		[CompilerGenerated]
		[NonSerialized]
		private EventHandler eventHandler_12;

		// Token: 0x040002B0 RID: 688
		[CompilerGenerated]
		[NonSerialized]
		private EventHandler eventHandler_13;

		// Token: 0x040002B1 RID: 689
		private int _DefaultSticksPerChart;

		// Token: 0x040002B2 RID: 690
		private int _TimerSpeed;

		// Token: 0x040002B3 RID: 691
		private int _TradingUnits;

		// Token: 0x040002B4 RID: 692
		private bool _IfShowTransTabsBar;

		// Token: 0x040002B5 RID: 693
		private eDockSide _AcctTransBar_DockSide;

		// Token: 0x040002B6 RID: 694
		private string _DotNetBarLayoutString;

		// Token: 0x040002B7 RID: 695
		private bool _IsTransTabBarMaximized;

		// Token: 0x040002B8 RID: 696
		private bool _IsTransTabMaximized;

		// Token: 0x040002B9 RID: 697
		private bool _IfAutoSavePageOnExit;

		// Token: 0x040002BA RID: 698
		private string _UserID;

		// Token: 0x040002BB RID: 699
		private bool _IfSaveUsrID;

		// Token: 0x040002BC RID: 700
		private string _Pswd;

		// Token: 0x040002BD RID: 701
		private bool _IfSavePswd;

		// Token: 0x040002BE RID: 702
		private bool _IfDispDayDivLine;

		// Token: 0x040002BF RID: 703
		private TimeUnit _PeriodOfChartDispDayDivLine;

		// Token: 0x040002C0 RID: 704
		private bool _IfSaveSpeedOnQuit;

		// Token: 0x040002C1 RID: 705
		private bool _IfSaveWindowOnQuit;

		// Token: 0x040002C2 RID: 706
		private bool _IsStarted;

		// Token: 0x040002C3 RID: 707
		private bool _IsOrderConfmNeeded;

		// Token: 0x040002C4 RID: 708
		private bool _IfConfirmQuit;

		// Token: 0x040002C5 RID: 709
		private DateTime? _LastAppUpdDateTime;

		// Token: 0x040002C6 RID: 710
		private DateTime? _LastDisplayedLogonNoticeDT;

		// Token: 0x040002C7 RID: 711
		private bool _IfShowSameLogonNoticeNextTime;

		// Token: 0x040002C8 RID: 712
		private string _LastAcctTransExportDir;

		// Token: 0x040002C9 RID: 713
		private string _LastHisDataExportDir;

		// Token: 0x040002CA RID: 714
		private TimeUnit _PeriodOfChartDispTransArrow;

		// Token: 0x040002CB RID: 715
		private bool _IfShowNoTransArrow;

		// Token: 0x040002CC RID: 716
		private bool _IfShowAllTransArrow;

		// Token: 0x040002CD RID: 717
		private bool _IfAlwaysShowTransNoteBox;

		// Token: 0x040002CE RID: 718
		private bool _IfShowAllNotesWhenAlwaysShowTransNoteBox;

		// Token: 0x040002CF RID: 719
		private bool _IfHideTransNoteBorder;

		// Token: 0x040002D0 RID: 720
		private bool _IfTransNoteFillTransparent;

		// Token: 0x040002D1 RID: 721
		private bool _IfDisableOpenCloseSound;

		// Token: 0x040002D2 RID: 722
		private bool _IfDisableTsOdrLine;

		// Token: 0x040002D3 RID: 723
		private int? _LastFuncTabsIdx;

		// Token: 0x040002D4 RID: 724
		private int? _LastMktSymbTabsIdx;

		// Token: 0x040002D5 RID: 725
		private bool _IfDisableAutoShowCurrTransTab;

		// Token: 0x040002D6 RID: 726
		private bool _IfAutoOpenCloseInTradingTab;

		// Token: 0x040002D7 RID: 727
		private bool _IfFollowPrcInTradingTab;

		// Token: 0x040002D8 RID: 728
		private decimal _TradingPrice;

		// Token: 0x040002D9 RID: 729
		private bool _EnableShortForStock;

		// Token: 0x040002DA RID: 730
		private bool _EnableT0ForStock;

		// Token: 0x040002DB RID: 731
		private bool _IfNoConfClsTransWhenDblClick;

		// Token: 0x040002DC RID: 732
		private bool _IfCloseNewTransFirst;

		// Token: 0x040002DD RID: 733
		private StockRestorationMethod? _StockRestorationMethod;

		// Token: 0x040002DE RID: 734
		private Size? _ClientSize;

		// Token: 0x040002DF RID: 735
		private FormStartPosition? _StartPosition;

		// Token: 0x040002E0 RID: 736
		private Point? _Location;

		// Token: 0x040002E1 RID: 737
		private int _ScreenNumber;

		// Token: 0x040002E2 RID: 738
		private FormWindowState? _WindowState;

		// Token: 0x040002E3 RID: 739
		private int _OriDockSiteBelowHeight;

		// Token: 0x040002E4 RID: 740
		private int _OriDockSiteTopHeight;

		// Token: 0x040002E5 RID: 741
		private bool _IfShowPointValues;

		// Token: 0x040002E6 RID: 742
		private string _CurrentPageName;

		// Token: 0x040002E7 RID: 743
		private ChartTheme _ChartTheme;

		// Token: 0x040002E8 RID: 744
		private bool _IsSwitchingChart;

		// Token: 0x040002E9 RID: 745
		private string _NoShowTooltips;

		// Token: 0x040002EA RID: 746
		private PeriodType? _AutoPlayPeriodType;

		// Token: 0x040002EB RID: 747
		private int? _AutoPlayPeriodUnits;

		// Token: 0x040002EC RID: 748
		private bool _IfSymbSwitchShowCurrDT;

		// Token: 0x040002ED RID: 749
		private PeriodType _DataSrcPeriodType;

		// Token: 0x040002EE RID: 750
		private int _DataSrcPeriodUnits;

		// Token: 0x040002EF RID: 751
		[NonSerialized]
		private bool bool_0;

		// Token: 0x040002F0 RID: 752
		[NonSerialized]
		private bool bool_1;

		// Token: 0x040002F1 RID: 753
		[NonSerialized]
		private DateTime? nullable_0;

		// Token: 0x040002F2 RID: 754
		[NonSerialized]
		private ChtCtrl chtCtrl_0;

		// Token: 0x040002F3 RID: 755
		private CfmmcAutoDnldConfig _CfmmcAutoDnldConfig;

		// Token: 0x040002F4 RID: 756
		public bool IsOrderConfmNeeded;

		// Token: 0x040002F5 RID: 757
		private bool _IfROpenFixedAmt;

		// Token: 0x040002F6 RID: 758
		private decimal? _ROpenRatio;

		// Token: 0x040002F7 RID: 759
		private int? _ROpenFixedAmt;

		// Token: 0x040002F8 RID: 760
		private bool _IfROpenNoShowCnfmDlg;

		// Token: 0x040002F9 RID: 761
		private bool _IfNotAutoCancelExistingOpenTransOrder;

		// Token: 0x040002FA RID: 762
		private bool _IfNotAutoCancelExistingCloseTransOrder;

		// Token: 0x040002FB RID: 763
		private bool _IfNoBonusShare;

		// Token: 0x040002FC RID: 764
		private bool _IfNoDivident;

		// Token: 0x040002FD RID: 765
		private RationedShareTreatmt? _RationedShareTreatmt;

		// Token: 0x040002FE RID: 766
		private int? _NMinsPeriodUnits;

		// Token: 0x040002FF RID: 767
		private int? _NHoursPeriodUnits;

		// Token: 0x04000300 RID: 768
		private int? _NDaysPeriodUnits;

		// Token: 0x04000301 RID: 769
		private Font _LastSelectedFont;

		// Token: 0x04000302 RID: 770
		private Color? _LastSelectedFontColor;

		// Token: 0x04000303 RID: 771
		private Color? _LastSelectedLineColor;

		// Token: 0x04000304 RID: 772
		private KLineType? _KLineType;

		// Token: 0x04000305 RID: 773
		private bool _IfShowDayOfWeek;

		// Token: 0x04000306 RID: 774
		private Dictionary<string, Dictionary<string, object>> _UserDrawObjParamsDict;

		// Token: 0x04000307 RID: 775
		private bool _NoShowHighLowMark;

		// Token: 0x04000308 RID: 776
		private TransArrowType _TransArrowType;

		// Token: 0x04000309 RID: 777
		private bool _IfNoSyncToolBarAndTradingTabPriceUnits;

		// Token: 0x0400030A RID: 778
		private bool _IfShowIndividualShownOpenTrans;

		// Token: 0x0400030B RID: 779
		private bool _IfShowSymbCNNameInOpenTransGridView;

		// Token: 0x0400030C RID: 780
		private bool _IfNotShowAcctInfoOnTransTabHeader;

		// Token: 0x0400030D RID: 781
		private bool _IsInBlindTestMode;

		// Token: 0x0400030E RID: 782
		private bool _IsSingleBlindTest;

		// Token: 0x0400030F RID: 783
		private int? _LastSymbIDPriorToBlindTest;

		// Token: 0x04000310 RID: 784
		private DateTime? _LastSymbDTPriorToBlindTest;

		// Token: 0x04000311 RID: 785
		private bool _BlindTestForm_IfNoFtMI;

		// Token: 0x04000312 RID: 786
		private bool _BlindTestForm_IfFtIdx;

		// Token: 0x04000313 RID: 787
		private bool _BlindTestForm_IfFtMth;

		// Token: 0x04000314 RID: 788
		private bool _BlindTestForm_IfStIdx;

		// Token: 0x04000315 RID: 789
		private bool _BlindTestForm_IfStFond;

		// Token: 0x04000316 RID: 790
		private bool _BlindTestForm_IfNoStMain;

		// Token: 0x04000317 RID: 791
		private bool _BlindTestForm_IfNoStZX;

		// Token: 0x04000318 RID: 792
		private bool _BlindTestForm_IfNoStCYB;

		// Token: 0x04000319 RID: 793
		private bool _BlindTestForm_IfNoStKCB;

		// Token: 0x0400031A RID: 794
		private bool _BlindTestForm_IfStConvBond;

		// Token: 0x0400031B RID: 795
		private bool _BlindTestForm_IfNoZixuan;

		// Token: 0x0400031C RID: 796
		private string _LastTransImportFilePath;

		// Token: 0x0400031D RID: 797
		private bool _BackupNoAcctTrans;

		// Token: 0x0400031E RID: 798
		private bool _BackupNoUIParams;

		// Token: 0x0400031F RID: 799
		private bool _BackupNoSymbParams;

		// Token: 0x04000320 RID: 800
		private bool _BackupNoPages;

		// Token: 0x04000321 RID: 801
		private bool _BackupNoZiXuan;

		// Token: 0x04000322 RID: 802
		private bool _BackupNoDrawObjs;

		// Token: 0x04000323 RID: 803
		private bool _BackupSyncNotOnStartup;

		// Token: 0x04000324 RID: 804
		private bool _BackupSyncNotOnExit;

		// Token: 0x04000325 RID: 805
		private BackupDirection? _BackupDirection;

		// Token: 0x04000326 RID: 806
		private bool _BackupSyncPeriodically;

		// Token: 0x04000327 RID: 807
		private BackupSyncTimeInterval? _BackupSyncTimeInterval;

		// Token: 0x04000328 RID: 808
		private bool _BackupSyncAutoOverwritingLocalFile;

		// Token: 0x04000329 RID: 809
		private BackupSyncConflictTreatmt? _BackupSyncConflictTreatmt;
	}
}
