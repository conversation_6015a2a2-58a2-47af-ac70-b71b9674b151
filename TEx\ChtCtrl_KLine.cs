﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns11;
using ns16;
using ns2;
using ns28;
using ns29;
using ns31;
using TEx.Comn;
using TEx.Inds;
using TEx.SIndicator;

namespace TEx
{
	// Token: 0x02000260 RID: 608
	internal sealed class ChtCtrl_KLine : ChtCtrl
	{
		// Token: 0x14000098 RID: 152
		// (add) Token: 0x06001A9D RID: 6813 RVA: 0x000B5EF8 File Offset: 0x000B40F8
		// (remove) Token: 0x06001A9E RID: 6814 RVA: 0x000B5F30 File Offset: 0x000B4130
		public event Delegate18 AfterEnteringRetroMode
		{
			[CompilerGenerated]
			add
			{
				Delegate18 @delegate = this.delegate18_0;
				Delegate18 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate18 value2 = (Delegate18)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate18>(ref this.delegate18_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate18 @delegate = this.delegate18_0;
				Delegate18 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate18 value2 = (Delegate18)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate18>(ref this.delegate18_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06001A9F RID: 6815 RVA: 0x000B5F68 File Offset: 0x000B4168
		private void method_49()
		{
			EventArgs e = new EventArgs();
			Delegate18 @delegate = this.delegate18_0;
			if (@delegate != null)
			{
				@delegate(e);
			}
		}

		// Token: 0x14000099 RID: 153
		// (add) Token: 0x06001AA0 RID: 6816 RVA: 0x000B5F90 File Offset: 0x000B4190
		// (remove) Token: 0x06001AA1 RID: 6817 RVA: 0x000B5FC8 File Offset: 0x000B41C8
		public event IndicatorEventHandler IndicatorRemoved
		{
			[CompilerGenerated]
			add
			{
				IndicatorEventHandler indicatorEventHandler = this.indicatorEventHandler_0;
				IndicatorEventHandler indicatorEventHandler2;
				do
				{
					indicatorEventHandler2 = indicatorEventHandler;
					IndicatorEventHandler value2 = (IndicatorEventHandler)Delegate.Combine(indicatorEventHandler2, value);
					indicatorEventHandler = Interlocked.CompareExchange<IndicatorEventHandler>(ref this.indicatorEventHandler_0, value2, indicatorEventHandler2);
				}
				while (indicatorEventHandler != indicatorEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				IndicatorEventHandler indicatorEventHandler = this.indicatorEventHandler_0;
				IndicatorEventHandler indicatorEventHandler2;
				do
				{
					indicatorEventHandler2 = indicatorEventHandler;
					IndicatorEventHandler value2 = (IndicatorEventHandler)Delegate.Remove(indicatorEventHandler2, value);
					indicatorEventHandler = Interlocked.CompareExchange<IndicatorEventHandler>(ref this.indicatorEventHandler_0, value2, indicatorEventHandler2);
				}
				while (indicatorEventHandler != indicatorEventHandler2);
			}
		}

		// Token: 0x06001AA2 RID: 6818 RVA: 0x000B6000 File Offset: 0x000B4200
		private void method_50(EventArgs27 eventArgs27_0)
		{
			IndicatorEventHandler indicatorEventHandler = this.indicatorEventHandler_0;
			if (indicatorEventHandler != null)
			{
				indicatorEventHandler(this, eventArgs27_0);
			}
		}

		// Token: 0x06001AA3 RID: 6819 RVA: 0x000B6024 File Offset: 0x000B4224
		public ChtCtrl_KLine(SymbDataSet sds, HisDataPeriodSet hisDataPeriodSet, string tag, int maxSticksPerChart) : base(sds, hisDataPeriodSet, tag, maxSticksPerChart)
		{
			Base.Data.CurrSymblChanged += this.method_139;
			base.SymbDataSet.HDPS_1hChanged += this.method_140;
			base.SymbDataSetChanging += this.method_141;
		}

		// Token: 0x06001AA4 RID: 6820 RVA: 0x000B60A0 File Offset: 0x000B42A0
		protected override void vmethod_2()
		{
			this.class54_0 = new Class54(base.ChtSpC.Panel1, 100, 7, this);
			this.class55_0 = new Class55(base.ChtSpC.Panel1, 84, 7, this);
			this.class52_0 = new Class52(base.ChtSpC.Panel1, 64, 7, this);
			this.class53_0 = new Class53(base.ChtSpC.Panel1, 48, 7, this);
			this.class54_0.Visible = false;
			this.class55_0.Visible = false;
			this.class52_0.Visible = false;
			this.class53_0.Visible = false;
			this.class54_0.MouseLeave += base.class56_0_MouseLeave;
			this.class55_0.MouseLeave += base.class56_0_MouseLeave;
			this.class52_0.MouseLeave += base.class56_0_MouseLeave;
			this.class53_0.MouseLeave += base.class56_0_MouseLeave;
			base.vmethod_2();
		}

		// Token: 0x06001AA5 RID: 6821 RVA: 0x000B61AC File Offset: 0x000B43AC
		protected override void vmethod_3()
		{
			try
			{
				this.class54_0.Visible = false;
				this.class55_0.Visible = false;
				this.class52_0.Visible = false;
				this.class53_0.Visible = false;
				this.class54_0.Visible = true;
				this.class55_0.Visible = true;
				this.class52_0.Visible = true;
				this.class53_0.Visible = true;
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
			base.vmethod_3();
		}

		// Token: 0x06001AA6 RID: 6822 RVA: 0x000B623C File Offset: 0x000B443C
		protected override void vmethod_34()
		{
			base.vmethod_34();
			try
			{
				if (!this.class54_0.Visible)
				{
					this.class54_0.Visible = true;
				}
				if (!this.class55_0.Visible)
				{
					this.class55_0.Visible = true;
				}
				if (!this.class52_0.Visible)
				{
					this.class52_0.Visible = true;
				}
				if (!this.class53_0.Visible)
				{
					this.class53_0.Visible = true;
				}
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x06001AA7 RID: 6823 RVA: 0x000B62D0 File Offset: 0x000B44D0
		protected override bool vmethod_28()
		{
			bool result;
			if (!base.vmethod_28())
			{
				if (!this.class54_0.IsMouseEnter && !this.class55_0.IsMouseEnter && !this.class52_0.IsMouseEnter)
				{
					result = this.class53_0.IsMouseEnter;
				}
				else
				{
					result = true;
				}
			}
			else
			{
				result = true;
			}
			return result;
		}

		// Token: 0x06001AA8 RID: 6824 RVA: 0x000B6324 File Offset: 0x000B4524
		protected override void timer_0_Tick(object sender, EventArgs e)
		{
			if (!base.IsAnyChtCtrlBtnEntered)
			{
				this.class54_0.Visible = false;
				this.class55_0.Visible = false;
				this.class52_0.Visible = false;
				this.class53_0.Visible = false;
			}
			base.timer_0_Tick(sender, e);
		}

		// Token: 0x06001AA9 RID: 6825 RVA: 0x0000B15B File Offset: 0x0000935B
		public void method_51()
		{
			this.method_52(0);
		}

		// Token: 0x06001AAA RID: 6826 RVA: 0x000B6374 File Offset: 0x000B4574
		public void method_52(int int_7)
		{
			this.method_53(int_7);
			base.ChtSpC.Panel2.Controls.Clear();
			base.ChtSpC.Panel2.Controls.Add(this.splitContainer_2);
			this.SpCVolMACD.SplitterMoved += this.method_137;
		}

		// Token: 0x06001AAB RID: 6827 RVA: 0x000B63D4 File Offset: 0x000B45D4
		private void method_53(int int_7)
		{
			this.splitContainer_2 = this.method_54();
			if (int_7 > 0)
			{
				ChartKLSub chartKLSub = this.method_69(this.SpCVolMACD.Panel1, true);
				IndEx indEx = this.method_93("VOL", chartKLSub.DP);
				if (indEx != null && indEx.InitInd(chartKLSub))
				{
					chartKLSub.method_122(indEx);
				}
				base.method_24(this.chartCS_0, false);
				if (int_7 == 1)
				{
					base.method_24(chartKLSub, true);
				}
				else if (int_7 == 2)
				{
					ChartKLSub chartKLSub2 = this.method_69(this.SpCVolMACD.Panel2, true);
					this.SpCVolMACD.SplitterDistance = (int)Math.Round(this.splitContainer_2.Height * this.method_104(), 0);
					IndEx indEx2 = this.method_93("MACD", chartKLSub2.DP);
					if (indEx2 != null && indEx2.InitInd(chartKLSub2))
					{
						chartKLSub2.method_122(indEx2);
					}
					base.method_24(chartKLSub2, true);
				}
				else if (int_7 == 3)
				{
					this.splitContainer_3 = this.method_58();
					ChartKLSub chartKLSub3 = this.method_69(this.splitContainer_3.Panel1, true);
					ChartKLSub chartKLSub4 = this.method_69(this.splitContainer_3.Panel2, true);
					this.SpCVolMACD.SplitterDistance = (int)Math.Round(this.splitContainer_2.Height * this.method_104(), 0);
					this.SpCVolMACD_Sub.SplitterDistance = (int)Math.Round(this.splitContainer_3.Height * this.method_106(), 0);
					IndEx indEx3 = this.method_93("MACD", chartKLSub3.DP);
					if (indEx3 != null && indEx3.InitInd(chartKLSub3))
					{
						chartKLSub3.method_122(indEx3);
					}
					IndEx indEx4 = this.method_93("KDJ", chartKLSub4.DP);
					if (indEx4 != null && !chartKLSub4.method_126(indEx4) && indEx4.InitInd(chartKLSub4))
					{
						chartKLSub4.method_122(indEx4);
					}
					base.method_24(chartKLSub4, true);
				}
			}
		}

		// Token: 0x06001AAC RID: 6828 RVA: 0x000B65D0 File Offset: 0x000B47D0
		private SplitContainer method_54()
		{
			return new SplitContainer
			{
				Name = "SpCVolMACD",
				Dock = DockStyle.Fill,
				Size = new Size(497, 130),
				SplitterDistance = 36,
				SplitterWidth = 1,
				Orientation = Orientation.Horizontal
			};
		}

		// Token: 0x06001AAD RID: 6829 RVA: 0x0000B166 File Offset: 0x00009366
		public void method_55()
		{
			this.method_56(false);
		}

		// Token: 0x06001AAE RID: 6830 RVA: 0x000B6624 File Offset: 0x000B4824
		public void method_56(bool bool_6)
		{
			if (this.splitContainer_3 != null)
			{
				if (bool_6)
				{
					this.method_57();
				}
			}
			else
			{
				this.method_57();
			}
			this.SpCVolMACD.Panel2.Controls.Clear();
			this.SpCVolMACD.Panel2.Controls.Add(this.splitContainer_3);
			this.SpCVolMACD_Sub.SplitterMoved += this.method_137;
		}

		// Token: 0x06001AAF RID: 6831 RVA: 0x0000B171 File Offset: 0x00009371
		private void method_57()
		{
			this.splitContainer_3 = this.method_58();
		}

		// Token: 0x06001AB0 RID: 6832 RVA: 0x000B6694 File Offset: 0x000B4894
		private SplitContainer method_58()
		{
			return new SplitContainer
			{
				Name = "SpCVolMACD_Sub",
				Dock = DockStyle.Fill,
				Size = new Size(497, 86),
				SplitterDistance = 43,
				SplitterWidth = 1,
				Orientation = Orientation.Horizontal
			};
		}

		// Token: 0x06001AB1 RID: 6833 RVA: 0x000B66E4 File Offset: 0x000B48E4
		public ChartCS method_59(bool bool_6)
		{
			return this.method_60(null, bool_6);
		}

		// Token: 0x06001AB2 RID: 6834 RVA: 0x000B6700 File Offset: 0x000B4900
		public ChartCS method_60(ChartParam chartParam_0, bool bool_6)
		{
			return this.method_61(chartParam_0, bool_6, false);
		}

		// Token: 0x06001AB3 RID: 6835 RVA: 0x000B671C File Offset: 0x000B491C
		public ChartCS method_61(ChartParam chartParam_0, bool bool_6, bool bool_7)
		{
			if (base.ChartList.Contains(this.chartCS_0))
			{
				base.ChartList.Remove(this.chartCS_0);
			}
			if (chartParam_0 != null)
			{
				this.chartCS_0 = new ChartCS(this, base.ChtSpC.Panel1, bool_6, chartParam_0.IsYAxisLogType);
			}
			else
			{
				this.chartCS_0 = new ChartCS(this, base.ChtSpC.Panel1, bool_6, false);
			}
			ChartCS result;
			if (this.chartCS_0 == null)
			{
				result = null;
			}
			else
			{
				this.method_108();
				this.chartCS_0.ZedGraphControl.AxisChange();
				this.chartCS_0.ZedGraphControl.Refresh();
				base.ChartList.Add(this.chartCS_0);
				this.vmethod_2();
				this.chartCS_0.ZedGraphControl.MouseEnter += base.method_45;
				this.chartCS_0.ZedGraphControl.MouseLeave += base.method_46;
				this.chartCS_0.ZedGraphControl.MouseHover += base.method_47;
				if (bool_7 && (chartParam_0 == null || chartParam_0.IndList == null || !chartParam_0.IndList.Any<Indicator>()))
				{
					List<UserDefineIndScript> list = new List<UserDefineIndScript>();
					UserDefineIndScript userDefineIndScript = UserDefineFileMgr.UDSListChecked.SingleOrDefault(new Func<UserDefineIndScript, bool>(ChtCtrl_KLine.<>c.<>9.method_0));
					if (userDefineIndScript != null)
					{
						list.Add(userDefineIndScript);
						if (chartParam_0 != null)
						{
							chartParam_0 = new ChartParam(ChartType.CandleStick, null, list, chartParam_0.IsYAxisLogType);
						}
						else
						{
							chartParam_0 = new ChartParam(ChartType.CandleStick, null, list, false);
						}
					}
				}
				if (chartParam_0 != null)
				{
					this.method_130(this.chartCS_0, chartParam_0.IndList, chartParam_0.UDSList);
				}
				result = this.chartCS_0;
			}
			return result;
		}

		// Token: 0x06001AB4 RID: 6836 RVA: 0x000B68C4 File Offset: 0x000B4AC4
		public Class223 method_62(bool bool_6)
		{
			return this.method_63(this.SpCVolMACD.Panel1, bool_6);
		}

		// Token: 0x06001AB5 RID: 6837 RVA: 0x000B68E8 File Offset: 0x000B4AE8
		public Class223 method_63(SplitterPanel splitterPanel_1, bool bool_6)
		{
			Class223 @class = new Class223(this, splitterPanel_1, bool_6);
			Class223 result;
			if (@class == null)
			{
				result = null;
			}
			else
			{
				this.method_65(@class, splitterPanel_1);
				result = @class;
			}
			return result;
		}

		// Token: 0x06001AB6 RID: 6838 RVA: 0x000B68E8 File Offset: 0x000B4AE8
		public Class223 method_64(SplitterPanel splitterPanel_1, bool bool_6)
		{
			Class223 @class = new Class223(this, splitterPanel_1, bool_6);
			Class223 result;
			if (@class == null)
			{
				result = null;
			}
			else
			{
				this.method_65(@class, splitterPanel_1);
				result = @class;
			}
			return result;
		}

		// Token: 0x06001AB7 RID: 6839 RVA: 0x000B6914 File Offset: 0x000B4B14
		public void method_65(ChartKLSub chartKLSub_3, SplitterPanel splitterPanel_1)
		{
			if (chartKLSub_3 != null && splitterPanel_1 != null)
			{
				if (!splitterPanel_1.Controls.Contains(chartKLSub_3.ZedGraphControl) || chartKLSub_3.ParentPanel != splitterPanel_1)
				{
					splitterPanel_1.Controls.Clear();
					splitterPanel_1.Controls.Add(chartKLSub_3.ZedGraphControl);
					chartKLSub_3.ParentPanel = splitterPanel_1;
				}
				if (!base.ChartList.Contains(chartKLSub_3))
				{
					base.ChartList.Add(chartKLSub_3);
				}
				this.method_70(chartKLSub_3);
			}
		}

		// Token: 0x06001AB8 RID: 6840 RVA: 0x000B698C File Offset: 0x000B4B8C
		public Class223 method_66()
		{
			return this.method_67(true);
		}

		// Token: 0x06001AB9 RID: 6841 RVA: 0x000B69A4 File Offset: 0x000B4BA4
		public Class223 method_67(bool bool_6)
		{
			return this.method_69(this.SpCVolMACD.Panel2, bool_6);
		}

		// Token: 0x06001ABA RID: 6842 RVA: 0x000B69C8 File Offset: 0x000B4BC8
		public Class223 method_68(SplitterPanel splitterPanel_1, bool bool_6, Indicator indicator_1)
		{
			Class223 @class = this.method_69(splitterPanel_1, bool_6);
			if (indicator_1 != null)
			{
				this.method_74(@class, indicator_1);
			}
			return @class;
		}

		// Token: 0x06001ABB RID: 6843 RVA: 0x000B69F0 File Offset: 0x000B4BF0
		public Class223 method_69(SplitterPanel splitterPanel_1, bool bool_6)
		{
			Class223 @class = new Class223(this, splitterPanel_1, bool_6);
			this.method_65(@class, splitterPanel_1);
			return @class;
		}

		// Token: 0x06001ABC RID: 6844 RVA: 0x0000B181 File Offset: 0x00009381
		private void method_70(ChartKLSub chartKLSub_3)
		{
			this.method_110(chartKLSub_3);
			chartKLSub_3.vmethod_15();
			chartKLSub_3.MouseClicked += this.method_77;
			chartKLSub_3.ZedGraphControl.AxisChange();
			chartKLSub_3.ZedGraphControl.Refresh();
		}

		// Token: 0x06001ABD RID: 6845 RVA: 0x000B6A14 File Offset: 0x000B4C14
		public List<IndEx> method_71()
		{
			List<IndEx> list = new List<IndEx>();
			foreach (ChartBase chartBase in base.ChartList)
			{
				ChartKLine chartKLine = chartBase as ChartKLine;
				if (chartKLine != null)
				{
					list.AddRange(chartKLine.IndExList.Where(new Func<IndEx, bool>(ChtCtrl_KLine.<>c.<>9.method_1)));
				}
			}
			return list;
		}

		// Token: 0x06001ABE RID: 6846 RVA: 0x000B6AA4 File Offset: 0x000B4CA4
		public List<IndEx> method_72()
		{
			List<IndEx> list = new List<IndEx>();
			foreach (ChartBase chartBase in base.ChartList)
			{
				if (chartBase.GetType() == typeof(ChartCS))
				{
					list.AddRange((chartBase as ChartKLine).IndExList);
				}
			}
			return list;
		}

		// Token: 0x06001ABF RID: 6847 RVA: 0x0000B1BA File Offset: 0x000093BA
		public void method_73(Indicator indicator_1)
		{
			if (this.EnteredSubChart != null)
			{
				this.method_74(this.EnteredSubChart, indicator_1);
			}
			else
			{
				MessageBox.Show("请先添加附图，然后再选择附图指标。", "注意", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
		}

		// Token: 0x06001AC0 RID: 6848 RVA: 0x000B6B20 File Offset: 0x000B4D20
		public void method_74(ChartKLSub chartKLSub_3, Indicator indicator_1)
		{
			if (chartKLSub_3 == null)
			{
				Class182.smethod_0(new Exception("chart == null!"));
			}
			else
			{
				this.method_76(chartKLSub_3);
				this.method_110(chartKLSub_3);
				chartKLSub_3.ZedGraphControl.AxisChange();
				chartKLSub_3.ZedGraphControl.Refresh();
				try
				{
					if (indicator_1.InitInd(chartKLSub_3))
					{
						chartKLSub_3.method_123(indicator_1, chartKLSub_3);
					}
				}
				catch (Exception ex)
				{
					if (chartKLSub_3.IndList != null)
					{
						chartKLSub_3.IndList.Clear();
					}
					MessageBox.Show(string.Concat(new string[]
					{
						"初始化指标'",
						indicator_1.EnName,
						"'时发生错误：",
						Environment.NewLine,
						Environment.NewLine,
						ex.Message
					}), "错误", MessageBoxButtons.OK, MessageBoxIcon.Hand);
				}
			}
		}

		// Token: 0x06001AC1 RID: 6849 RVA: 0x0000B1E8 File Offset: 0x000093E8
		private void method_75()
		{
			this.method_76(this.EnteredSubChart);
		}

		// Token: 0x06001AC2 RID: 6850 RVA: 0x000B6BF0 File Offset: 0x000B4DF0
		private void method_76(ChartKLSub chartKLSub_3)
		{
			if (this.IndList != null && this.IndList.Count > 0)
			{
				foreach (Indicator indicator in this.IndList)
				{
					if (indicator.ChartType == ChartType.MACD && indicator.Chart == chartKLSub_3)
					{
						indicator.RemoveFromChart();
					}
				}
			}
		}

		// Token: 0x06001AC3 RID: 6851 RVA: 0x000B6C70 File Offset: 0x000B4E70
		private void method_77(object sender, EventArgs e)
		{
			foreach (ChartKLSub chartKLSub in this.SubChartList)
			{
				if (chartKLSub != sender)
				{
					chartKLSub.IsEntered = false;
				}
			}
		}

		// Token: 0x06001AC4 RID: 6852 RVA: 0x0000B1F8 File Offset: 0x000093F8
		public override void vmethod_19(HisData hisData_0, bool bool_6, bool bool_7, bool bool_8)
		{
			base.vmethod_19(hisData_0, bool_6, bool_7, bool_8);
			if (bool_6)
			{
				this.int_5 = base.IndexOfLastItemShown;
			}
		}

		// Token: 0x06001AC5 RID: 6853 RVA: 0x000B6CCC File Offset: 0x000B4ECC
		protected override void vmethod_4(HisData hisData_0, bool bool_6)
		{
			foreach (ChartBase chartBase in base.ChartList)
			{
				ChartKLine chartKLine = (ChartKLine)chartBase;
				if (chartKLine.IsXAxisVisible)
				{
					chartKLine.vmethod_26();
					if (bool_6)
					{
						chartKLine.vmethod_11(hisData_0);
					}
				}
			}
		}

		// Token: 0x06001AC6 RID: 6854 RVA: 0x0000B216 File Offset: 0x00009416
		public override void vmethod_10(PeriodType periodType_0, int? nullable_2, DateTime dateTime_0)
		{
			if (!base.method_48(periodType_0, nullable_2))
			{
				this.Chart_CS.method_198();
			}
			this.IsInRetroMode = false;
			base.vmethod_10(periodType_0, nullable_2, dateTime_0);
		}

		// Token: 0x06001AC7 RID: 6855 RVA: 0x0000B23F File Offset: 0x0000943F
		protected override void vmethod_11(DateTime dateTime_0)
		{
			this.method_115();
			base.vmethod_11(dateTime_0);
		}

		// Token: 0x06001AC8 RID: 6856 RVA: 0x000B6D38 File Offset: 0x000B4F38
		public override void vmethod_8(HisData hisData_0)
		{
			base.vmethod_8(hisData_0);
			if (base.IndexOfLastItemShown < base.HisDataPeriodSet.PeriodHisDataList.Count - 1 && !base.HisDataPeriodSet.IsPeriod1m && base.HisDataPeriodSet.method_35(hisData_0))
			{
				this.int_5 = base.IndexOfLastItemShown;
			}
		}

		// Token: 0x06001AC9 RID: 6857 RVA: 0x000B6D90 File Offset: 0x000B4F90
		public override void vmethod_12(SortedList<DateTime, HisData> sortedList_0)
		{
			DateTime? dateTime = null;
			int count = base.HisDataPeriodSet.PeriodHisDataList.Count;
			if (this.RetroModeLastItemIdx >= count)
			{
				this.RetroModeLastItemIdx = count - 1;
			}
			if (this.RetroModeLastItemIdx < 0)
			{
				this.RetroModeLastItemIdx = 0;
			}
			try
			{
				dateTime = new DateTime?(base.HisDataPeriodSet.PeriodHisDataList.Keys[this.RetroModeLastItemIdx]);
			}
			catch
			{
			}
			base.vmethod_12(sortedList_0);
			if (dateTime != null)
			{
				this.method_79(dateTime.Value);
			}
			if (base.PeriodType == PeriodType.ByMins || base.PeriodType == PeriodType.ByDay)
			{
				this.method_115();
				this.method_116();
			}
		}

		// Token: 0x06001ACA RID: 6858 RVA: 0x000B6E4C File Offset: 0x000B504C
		public void method_78()
		{
			if (base.IsPeriodLong)
			{
				DateTime? dateTime = null;
				if (base.HisDataPeriodSet != null && base.HisDataPeriodSet.PeriodHisDataList != null && base.HisDataPeriodSet.PeriodHisDataList.Any<KeyValuePair<DateTime, HisData>>())
				{
					int count = base.HisDataPeriodSet.PeriodHisDataList.Count;
					if (this.int_5 >= count)
					{
						this.int_5 = count - 1;
					}
					if (this.int_5 >= 0 && this.int_5 < count)
					{
						dateTime = new DateTime?(base.HisDataPeriodSet.PeriodHisDataList.Keys[this.int_5]);
					}
				}
				DateTime? dateTime2 = null;
				if (base.IndexOfLastItemShown >= 0 && base.HisDataPeriodSet != null && base.HisDataPeriodSet.PeriodHisDataList != null && base.IndexOfLastItemShown < base.HisDataPeriodSet.PeriodHisDataList.Count)
				{
					dateTime2 = new DateTime?(base.HisDataPeriodSet.PeriodHisDataList.Keys[base.IndexOfLastItemShown]);
				}
				else
				{
					Class182.smethod_0(new Exception("this.IndexOfLastItemShown is not in valid range!"));
					if (base.SymbDataSet.CurrDate != null && base.HisDataPeriodSet != null)
					{
						dateTime2 = base.HisDataPeriodSet.method_38(base.SymbDataSet.CurrDate.Value);
					}
				}
				if (dateTime2 != null)
				{
					DateTime? nullable_ = null;
					if (base.IndexOfLastItemShown == base.HisDataPeriodSet.PeriodHisDataList.Count - 1)
					{
						nullable_ = base.SymbDataSet.CurrStkMeta.EndDate;
					}
					HisDataPeriodSet hisDataPeriodSet = base.SymbDataSet.method_61(base.SymbDataSet.CurrSymbol, base.PeriodType, base.PeriodUnits, null, nullable_);
					base.HisDataPeriodSet = hisDataPeriodSet;
					base.method_16(dateTime2.Value);
					if (dateTime != null)
					{
						this.method_79(dateTime.Value);
					}
					this.method_115();
				}
			}
		}

		// Token: 0x06001ACB RID: 6859 RVA: 0x0000B250 File Offset: 0x00009450
		private void method_79(DateTime dateTime_0)
		{
			this.RetroModeLastItemIdx = Class336.smethod_0(base.HisDataPeriodSet.PeriodHisDataList, dateTime_0);
			if (this.RetroModeLastItemIdx < 0)
			{
				this.RetroModeLastItemIdx = 0;
			}
		}

		// Token: 0x06001ACC RID: 6860 RVA: 0x000B703C File Offset: 0x000B523C
		public void method_80()
		{
			if (base.NumberOfCharts > 1)
			{
				this.decimal_0 = base.ChtSpC.SplitterDistance / base.ChtSpC.Height;
				if (!base.ChtSpC.Panel2Collapsed && !this.splitContainer_2.Panel2Collapsed)
				{
					this.decimal_1 = this.splitContainer_2.SplitterDistance / this.splitContainer_2.Height;
				}
				if (this.SubChart1 != null)
				{
					this.chartKLSub_0 = this.SubChart1;
				}
				if (this.SubChart2 != null)
				{
					this.chartKLSub_1 = this.SubChart2;
				}
				if (this.SubChart3 != null)
				{
					this.chartKLSub_2 = this.SubChart3;
				}
				foreach (ChartKLSub item in this.SubChartList)
				{
					base.ChartList.Remove(item);
				}
				base.ChtSpC.Panel2.Controls.Clear();
				base.ChtSpC.Panel2Collapsed = true;
				base.method_24(this.Chart_CS, true);
			}
		}

		// Token: 0x06001ACD RID: 6861 RVA: 0x0000B27B File Offset: 0x0000947B
		public void method_81()
		{
			this.method_82(null);
		}

		// Token: 0x06001ACE RID: 6862 RVA: 0x000B7180 File Offset: 0x000B5380
		public void method_82(ChartParam chartParam_0)
		{
			if (base.NumberOfCharts != 2)
			{
				if (base.NumberOfCharts == 1)
				{
					base.ChtSpC.Panel2.Controls.Clear();
					base.ChtSpC.Panel2Collapsed = false;
					this.method_88(chartParam_0, true);
					ChartKLSub subChart = this.SubChart1;
					if (subChart != null && !subChart.ZedGraphControl.Visible)
					{
						subChart.ZedGraphControl.Visible = true;
					}
					base.method_5();
				}
				else if (base.NumberOfCharts == 3 || base.NumberOfCharts == 4)
				{
					int numberOfCharts = base.NumberOfCharts;
					ChartKLSub subChart2 = this.SubChart2;
					ChartKLSub item = null;
					if (numberOfCharts == 4)
					{
						item = this.SubChart3;
					}
					base.ChartList.Remove(subChart2);
					if (subChart2 != null)
					{
						this.chartKLSub_1 = subChart2;
					}
					if (numberOfCharts == 4)
					{
						this.chartKLSub_2 = item;
						this.decimal_2 = this.splitContainer_3.SplitterDistance / this.splitContainer_3.Height;
						base.ChartList.Remove(item);
					}
					this.decimal_1 = this.splitContainer_2.SplitterDistance / this.splitContainer_2.Height;
					this.splitContainer_2.Panel2.Controls.Clear();
					this.splitContainer_2.Panel2Collapsed = true;
					base.method_24(this.SubChart1, true);
				}
				base.ChtSpC.SplitterDistance = (int)Math.Round(base.ChtSpC.Height * this.method_103(true), 0);
				base.method_44();
				this.method_128();
			}
		}

		// Token: 0x06001ACF RID: 6863 RVA: 0x0000B286 File Offset: 0x00009486
		public void method_83()
		{
			this.method_84(null, null);
		}

		// Token: 0x06001AD0 RID: 6864 RVA: 0x000B731C File Offset: 0x000B551C
		public void method_84(ChartParam chartParam_0, ChartParam chartParam_1)
		{
			if (base.NumberOfCharts != 3)
			{
				if (base.NumberOfCharts < 3)
				{
					if (base.NumberOfCharts == 1)
					{
						base.ChtSpC.Panel2Collapsed = false;
						base.method_24(this.chartCS_0, false);
						this.method_88(chartParam_0, false);
						this.method_90(chartParam_1, true);
					}
					else if (base.NumberOfCharts == 2)
					{
						base.method_24(this.SubChart1, false);
						this.method_90(chartParam_1, true);
					}
					base.method_5();
				}
				else if (base.NumberOfCharts == 4)
				{
					ChartKLSub subChart = this.SubChart3;
					this.chartKLSub_2 = subChart;
					base.ChartList.Remove(subChart);
					ChartKLSub subChart2 = this.SubChart2;
					this.method_65(subChart2, this.SpCVolMACD.Panel2);
					this.SpCVolMACD.Visible = true;
					this.splitContainer_3 = null;
					base.method_24(subChart2, true);
				}
				base.ChtSpC.SplitterDistance = (int)Math.Round(base.ChtSpC.Height * this.method_103(true), 0);
				this.SpCVolMACD.SplitterDistance = (int)Math.Round(this.splitContainer_2.Height * this.method_105(true), 0);
				base.method_44();
				this.method_128();
			}
		}

		// Token: 0x06001AD1 RID: 6865 RVA: 0x0000B292 File Offset: 0x00009492
		public void method_85()
		{
			this.method_86(null, null, null);
		}

		// Token: 0x06001AD2 RID: 6866 RVA: 0x000B7464 File Offset: 0x000B5664
		public void method_86(ChartParam chartParam_0, ChartParam chartParam_1, ChartParam chartParam_2)
		{
			if (base.NumberOfCharts < 4)
			{
				if (base.NumberOfCharts == 1)
				{
					base.ChtSpC.Panel2Collapsed = false;
					base.method_24(this.chartCS_0, false);
					this.method_88(chartParam_0, false);
					this.method_90(chartParam_1, false);
					this.method_92(chartParam_2, true);
				}
				else if (base.NumberOfCharts == 2)
				{
					base.method_24(this.SubChart1, false);
					this.method_90(chartParam_1, false);
					this.method_92(chartParam_2, true);
				}
				else if (base.NumberOfCharts == 3)
				{
					base.method_24(this.SubChart2, false);
					this.method_92(chartParam_2, true);
				}
				base.method_5();
				try
				{
					this.SpCVolMACD_Sub.SplitterDistance = (int)Math.Round(this.splitContainer_3.Height * this.method_106(), 0);
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
				try
				{
					this.SpCVolMACD.SplitterDistance = (int)Math.Round(this.splitContainer_2.Height * this.method_105(true), 0);
				}
				catch (Exception exception_2)
				{
					Class182.smethod_0(exception_2);
				}
				try
				{
					base.ChtSpC.SplitterDistance = (int)Math.Round(base.ChtSpC.Height * this.method_103(true), 0);
				}
				catch (Exception exception_3)
				{
					Class182.smethod_0(exception_3);
				}
				base.method_44();
				this.method_128();
			}
		}

		// Token: 0x06001AD3 RID: 6867 RVA: 0x0000B29F File Offset: 0x0000949F
		private void method_87(bool bool_6)
		{
			this.method_88(null, bool_6);
		}

		// Token: 0x06001AD4 RID: 6868 RVA: 0x000B75F0 File Offset: 0x000B57F0
		private void method_88(ChartParam chartParam_0, bool bool_6)
		{
			if (this.SpCVolMACD == null)
			{
				this.method_51();
			}
			if (base.ChtSpC.Panel2Collapsed)
			{
				base.ChtSpC.Panel2Collapsed = false;
			}
			if (base.ChtSpC.Panel2.Controls.Count == 0)
			{
				base.ChtSpC.Panel2.Controls.Add(this.SpCVolMACD);
			}
			if (chartParam_0 != null)
			{
				this.method_95(chartParam_0, this.SpCVolMACD.Panel1, bool_6);
			}
			else if (this.chartKLSub_0 != null)
			{
				ChartKLSub chartKLSub = this.chartKLSub_0;
				base.ChartList.Add(chartKLSub);
				if (base.ChtSpC.Panel2Collapsed)
				{
					base.ChtSpC.Panel2Collapsed = false;
				}
				if (base.ChtSpC.Panel2.Controls.Count < 1)
				{
					base.ChtSpC.Panel2.Controls.Add(this.SpCVolMACD);
				}
				Control control = this.SpCVolMACD.Panel1.Controls[0];
				if (control != null && control == chartKLSub.ZedGraphControl)
				{
					this.method_70(chartKLSub);
				}
				else
				{
					this.SpCVolMACD.Panel1.Controls.Clear();
					this.method_65(chartKLSub, this.SpCVolMACD.Panel1);
				}
				base.method_24(chartKLSub, bool_6);
			}
			else
			{
				UserDefineIndScript userDefineIndScript = UserDefineFileMgr.UDSListChecked.SingleOrDefault(new Func<UserDefineIndScript, bool>(ChtCtrl_KLine.<>c.<>9.method_2));
				if (userDefineIndScript != null)
				{
					chartParam_0 = new ChartParam(ChartType.MACD, null, new List<UserDefineIndScript>
					{
						userDefineIndScript
					}, false);
					this.method_95(chartParam_0, this.SpCVolMACD.Panel1, bool_6);
				}
			}
			if (bool_6)
			{
				this.SpCVolMACD.Panel2.Controls.Clear();
				this.SpCVolMACD.Panel2Collapsed = true;
				base.method_24(this.chartCS_0, false);
			}
		}

		// Token: 0x06001AD5 RID: 6869 RVA: 0x0000B2AB File Offset: 0x000094AB
		private void method_89(bool bool_6)
		{
			this.method_90(null, bool_6);
		}

		// Token: 0x06001AD6 RID: 6870 RVA: 0x000B77C8 File Offset: 0x000B59C8
		private void method_90(ChartParam chartParam_0, bool bool_6)
		{
			this.SpCVolMACD.Panel2Collapsed = false;
			this.SpCVolMACD.Panel2.Controls.Clear();
			if (this.chartKLSub_1 != null)
			{
				ChartKLSub chartKLSub = this.chartKLSub_1;
				this.method_65(chartKLSub, this.SpCVolMACD.Panel2);
				base.method_24(chartKLSub, bool_6);
			}
			else
			{
				if (chartParam_0 == null)
				{
					chartParam_0 = this.method_96();
				}
				this.method_95(chartParam_0, this.SpCVolMACD.Panel2, bool_6);
			}
		}

		// Token: 0x06001AD7 RID: 6871 RVA: 0x000B7844 File Offset: 0x000B5A44
		private void method_91(bool bool_6)
		{
			ChartParam chartParam_ = this.method_96();
			this.method_92(chartParam_, bool_6);
		}

		// Token: 0x06001AD8 RID: 6872 RVA: 0x000B7864 File Offset: 0x000B5A64
		private void method_92(ChartParam chartParam_0, bool bool_6)
		{
			if (this.SpCVolMACD.Panel2Collapsed)
			{
				this.SpCVolMACD.Panel2Collapsed = false;
			}
			ChartKLSub subChart = this.SubChart2;
			if (this.SpCVolMACD_Sub == null)
			{
				this.method_55();
			}
			else if (!this.SpCVolMACD.Panel2.Controls.Contains(this.SpCVolMACD_Sub))
			{
				this.SpCVolMACD_Sub.Visible = true;
				this.SpCVolMACD.Panel2.Controls.Add(this.SpCVolMACD_Sub);
			}
			this.method_65(subChart, this.SpCVolMACD_Sub.Panel1);
			if (this.chartKLSub_2 != null)
			{
				this.method_65(this.chartKLSub_2, this.SpCVolMACD_Sub.Panel2);
				base.method_24(this.chartKLSub_2, bool_6);
			}
			else
			{
				if (chartParam_0 == null)
				{
					chartParam_0 = this.method_98(this.method_97("KDJ"));
				}
				this.method_95(chartParam_0, this.SpCVolMACD_Sub.Panel2, bool_6);
			}
		}

		// Token: 0x06001AD9 RID: 6873 RVA: 0x000B7954 File Offset: 0x000B5B54
		public IndEx method_93(string string_1, DataProvider dataProvider_0)
		{
			ChtCtrl_KLine.Class325 @class = new ChtCtrl_KLine.Class325();
			@class.string_0 = string_1;
			IndEx result;
			if (dataProvider_0 == null)
			{
				result = null;
			}
			else
			{
				UserDefineIndScript userDefineIndScript = UserDefineFileMgr.UDSList.SingleOrDefault(new Func<UserDefineIndScript, bool>(@class.method_0));
				if (userDefineIndScript == null)
				{
					result = null;
				}
				else
				{
					result = this.method_94(dataProvider_0, userDefineIndScript);
				}
			}
			return result;
		}

		// Token: 0x06001ADA RID: 6874 RVA: 0x000B79A0 File Offset: 0x000B5BA0
		public IndEx method_94(DataProvider dataProvider_0, UserDefineIndScript userDefineIndScript_0)
		{
			IndEx result;
			if (dataProvider_0 != null && userDefineIndScript_0 != null)
			{
				UserDefineInd userDefineInd_ = new UserDefineInd(dataProvider_0, userDefineIndScript_0);
				if (UserDefineInd.smethod_5(userDefineIndScript_0, base.Symbol))
				{
					result = IndEx.smethod_0(userDefineInd_);
				}
				else
				{
					result = null;
				}
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001ADB RID: 6875 RVA: 0x000B79DC File Offset: 0x000B5BDC
		public ChartKLSub method_95(ChartParam chartParam_0, SplitterPanel splitterPanel_1, bool bool_6)
		{
			ChartKLSub result = null;
			if (chartParam_0.ChartType == ChartType.MACD && chartParam_0.UDSList != null && chartParam_0.UDSList.Any<UserDefineIndScript>())
			{
				UserDefineIndScript userDefineIndScript = chartParam_0.UDSList.First<UserDefineIndScript>();
				IndEx indEx = this.method_94(this.chartCS_0.DP, userDefineIndScript);
				new UserDefineInd(this.chartCS_0.DP, userDefineIndScript);
				if (indEx != null)
				{
					result = this.method_68(splitterPanel_1, bool_6, indEx);
				}
				else
				{
					result = this.method_68(splitterPanel_1, bool_6, null);
				}
			}
			return result;
		}

		// Token: 0x06001ADC RID: 6876 RVA: 0x000B7A58 File Offset: 0x000B5C58
		private ChartParam method_96()
		{
			return this.method_98(this.method_97("MACD"));
		}

		// Token: 0x06001ADD RID: 6877 RVA: 0x000B7A7C File Offset: 0x000B5C7C
		private UserDefineIndScript method_97(string string_1)
		{
			ChtCtrl_KLine.Class326 @class = new ChtCtrl_KLine.Class326();
			@class.string_0 = string_1;
			return UserDefineFileMgr.UDSList.SingleOrDefault(new Func<UserDefineIndScript, bool>(@class.method_0));
		}

		// Token: 0x06001ADE RID: 6878 RVA: 0x000B7AB0 File Offset: 0x000B5CB0
		private ChartParam method_98(UserDefineIndScript userDefineIndScript_0)
		{
			List<Indicator> indList = new List<Indicator>();
			List<UserDefineIndScript> list = new List<UserDefineIndScript>();
			if (userDefineIndScript_0 != null)
			{
				list.Add(userDefineIndScript_0);
			}
			return new ChartParam(ChartType.MACD, indList, list, false);
		}

		// Token: 0x06001ADF RID: 6879 RVA: 0x000B7AE0 File Offset: 0x000B5CE0
		private void method_99(SplitterPanel splitterPanel_1)
		{
			ChartKLSub chartKLSub = this.method_100(splitterPanel_1);
			if (chartKLSub != null && !base.ChartList.Contains(chartKLSub))
			{
				base.ChartList.Add(chartKLSub);
			}
		}

		// Token: 0x06001AE0 RID: 6880 RVA: 0x000B7B14 File Offset: 0x000B5D14
		private ChartKLSub method_100(SplitterPanel splitterPanel_1)
		{
			return this.method_101(this.SubChartList, splitterPanel_1);
		}

		// Token: 0x06001AE1 RID: 6881 RVA: 0x000B7B34 File Offset: 0x000B5D34
		private ChartKLSub method_101(List<ChartKLSub> list_1, SplitterPanel splitterPanel_1)
		{
			ChtCtrl_KLine.Class327 @class = new ChtCtrl_KLine.Class327();
			@class.splitterPanel_0 = splitterPanel_1;
			ChartKLSub result = null;
			if (list_1 != null)
			{
				IEnumerable<ChartKLSub> source = list_1.Where(new Func<ChartKLSub, bool>(@class.method_0));
				if (source.Any<ChartKLSub>())
				{
					return source.First<ChartKLSub>();
				}
			}
			return result;
		}

		// Token: 0x06001AE2 RID: 6882 RVA: 0x000B7B7C File Offset: 0x000B5D7C
		private decimal method_102()
		{
			return this.method_103(false);
		}

		// Token: 0x06001AE3 RID: 6883 RVA: 0x000B7B94 File Offset: 0x000B5D94
		private decimal method_103(bool bool_6)
		{
			decimal result;
			if (!bool_6 && this.decimal_0 != -1m)
			{
				result = this.decimal_0;
			}
			else if (base.NumberOfCharts == 2)
			{
				result = 0.7862407862407862407862407862m;
			}
			else if (base.NumberOfCharts == 3)
			{
				result = 0.6584766584766584766584766585m;
			}
			else if (base.NumberOfCharts == 4)
			{
				result = 0.4963144963144963144963144963m;
			}
			else
			{
				result = base.ChtSpC.SplitterDistance / base.ChtSpC.Height;
			}
			return result;
		}

		// Token: 0x06001AE4 RID: 6884 RVA: 0x000B7C50 File Offset: 0x000B5E50
		private decimal method_104()
		{
			return this.method_105(false);
		}

		// Token: 0x06001AE5 RID: 6885 RVA: 0x000B7C68 File Offset: 0x000B5E68
		private decimal method_105(bool bool_6)
		{
			decimal result;
			if (!bool_6 && this.decimal_1 != -1m)
			{
				result = this.decimal_1;
			}
			else if (base.NumberOfCharts == 3)
			{
				result = 0.3230769230769230769230769231m;
			}
			else if (base.NumberOfCharts == 4)
			{
				result = 0.2307692307692307692307692308m;
			}
			else
			{
				result = this.SpCVolMACD.SplitterDistance / this.SpCVolMACD.Height;
			}
			return result;
		}

		// Token: 0x06001AE6 RID: 6886 RVA: 0x000B7D00 File Offset: 0x000B5F00
		private decimal method_106()
		{
			decimal result;
			if (this.decimal_2 != -1m)
			{
				result = this.decimal_2;
			}
			else
			{
				result = 0.42m;
			}
			return result;
		}

		// Token: 0x06001AE7 RID: 6887 RVA: 0x000B7D3C File Offset: 0x000B5F3C
		public List<int> method_107()
		{
			List<int> result;
			if (this.Chart_CS != null)
			{
				result = this.Chart_CS.XLstToDispDayDivLine;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001AE8 RID: 6888 RVA: 0x0000B2B7 File Offset: 0x000094B7
		public override void vmethod_22()
		{
			this.Chart_CS.method_67();
		}

		// Token: 0x06001AE9 RID: 6889 RVA: 0x0000B2C6 File Offset: 0x000094C6
		public override void vmethod_23(bool bool_6)
		{
			this.Chart_CS.method_66(bool_6);
		}

		// Token: 0x06001AEA RID: 6890 RVA: 0x0000B2D6 File Offset: 0x000094D6
		public override void vmethod_33()
		{
			base.vmethod_33();
			base.SuspendLayout();
			this.method_108();
			this.method_109();
			base.ResumeLayout();
		}

		// Token: 0x06001AEB RID: 6891 RVA: 0x000B7D64 File Offset: 0x000B5F64
		private void method_108()
		{
			if (this.chartCS_0 != null)
			{
				if (this.chartCS_0.IsXAxisVisible)
				{
					try
					{
						this.chartCS_0.GraphPane.Chart.Rect = new RectangleF((float)base.ChartRect_LeftMargin, 2f, (float)(base.ChtSpC.Width - base.ChartRect_LeftMargin - this.int_4), (float)(base.ChtSpC.Panel1.Height - base.ChartBottom_DTLabel_Height - 2));
						goto IL_D0;
					}
					catch
					{
						goto IL_D0;
					}
				}
				try
				{
					this.chartCS_0.GraphPane.Chart.Rect = new RectangleF((float)base.ChartRect_LeftMargin, 2f, (float)(base.ChtSpC.Width - base.ChartRect_LeftMargin - this.int_4), (float)(base.ChtSpC.Panel1.Height - 3));
				}
				catch
				{
				}
				IL_D0:
				this.chartCS_0.method_200(false);
			}
		}

		// Token: 0x06001AEC RID: 6892 RVA: 0x000B7E6C File Offset: 0x000B606C
		private void method_109()
		{
			foreach (ChartKLSub chartKLSub_ in this.SubChartList)
			{
				this.method_110(chartKLSub_);
			}
		}

		// Token: 0x06001AED RID: 6893 RVA: 0x000B7EC4 File Offset: 0x000B60C4
		private void method_110(ChartKLSub chartKLSub_3)
		{
			if (chartKLSub_3 != null)
			{
				try
				{
					if (chartKLSub_3.IsXAxisVisible)
					{
						chartKLSub_3.GraphPane.Chart.Rect = new RectangleF((float)base.ChartRect_LeftMargin, 0f, (float)(base.ChtSpC.Width - base.ChartRect_LeftMargin - this.int_4), (float)(chartKLSub_3.ParentPanel.Height - base.ChartBottom_DTLabel_Height));
					}
					else
					{
						chartKLSub_3.GraphPane.Chart.Rect = new RectangleF((float)base.ChartRect_LeftMargin, 0f, (float)(base.ChtSpC.Width - base.ChartRect_LeftMargin - this.int_4), (float)(chartKLSub_3.ParentPanel.Height - 1));
					}
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
		}

		// Token: 0x06001AEE RID: 6894 RVA: 0x000B7F94 File Offset: 0x000B6194
		public void method_111(ChartParam chartParam_0)
		{
			if (this.SubChartList.Count > 0)
			{
				ChartKLSub chartKLSub = this.EnteredSubChart;
				if (chartKLSub == null)
				{
					chartKLSub = this.SubChartList.Last<ChartKLSub>();
					chartKLSub.IsEntered = true;
				}
				if (chartKLSub is Class223 && chartParam_0.ChartType == ChartType.MACD && chartParam_0.UDSList != null && chartParam_0.UDSList.Any<UserDefineIndScript>())
				{
					UserDefineIndScript uds = chartParam_0.UDSList.First<UserDefineIndScript>();
					IndEx indicator_ = IndEx.smethod_0(new UserDefineInd(chartKLSub.DP, uds));
					if (!chartKLSub.method_126(indicator_))
					{
						this.method_74(chartKLSub, indicator_);
					}
				}
			}
			else
			{
				this.method_82(chartParam_0);
			}
		}

		// Token: 0x06001AEF RID: 6895 RVA: 0x0000B2F8 File Offset: 0x000094F8
		public void method_112(ChartKLSub chartKLSub_3)
		{
			base.ChartList.Remove(chartKLSub_3);
			Class223 @class = this.method_63(chartKLSub_3.ParentPanel, chartKLSub_3.IsXAxisVisible);
			@class.ZedGraphControl.Visible = true;
			@class.IsEntered = true;
			base.method_5();
		}

		// Token: 0x06001AF0 RID: 6896 RVA: 0x0000B333 File Offset: 0x00009533
		public void method_113(ChartKLSub chartKLSub_3)
		{
			base.ChartList.Remove(chartKLSub_3);
			Class223 @class = this.method_64(chartKLSub_3.ParentPanel, chartKLSub_3.IsXAxisVisible);
			@class.ZedGraphControl.Visible = true;
			@class.IsEntered = true;
			base.method_5();
		}

		// Token: 0x06001AF1 RID: 6897 RVA: 0x000B802C File Offset: 0x000B622C
		public void method_114(UserDefineIndScript userDefineIndScript_0)
		{
			if (userDefineIndScript_0.MainK)
			{
				ChartCS chart_CS = this.Chart_CS;
				IndEx indEx = this.method_94(chart_CS.DP, userDefineIndScript_0);
				if (indEx != null)
				{
					if (!chart_CS.method_126(indEx) && indEx.InitInd(chart_CS))
					{
						chart_CS.method_122(indEx);
					}
				}
			}
			else
			{
				ChartParam chartParam_ = ChtCtrl_KLine.smethod_0(ChartType.MACD, userDefineIndScript_0);
				this.method_111(chartParam_);
			}
		}

		// Token: 0x06001AF2 RID: 6898 RVA: 0x000B8088 File Offset: 0x000B6288
		public static ChartParam smethod_0(ChartType chartType_0, UserDefineIndScript userDefineIndScript_0)
		{
			return new ChartParam
			{
				ChartType = chartType_0,
				UDSList = new List<UserDefineIndScript>
				{
					userDefineIndScript_0
				}
			};
		}

		// Token: 0x06001AF3 RID: 6899 RVA: 0x000B80BC File Offset: 0x000B62BC
		public void method_115()
		{
			foreach (ChartBase chartBase in base.ChartList)
			{
				ChartKLine chartKLine = (ChartKLine)chartBase;
				foreach (Indicator indicator in chartKLine.IndList)
				{
					indicator.InitInd(chartKLine);
				}
			}
		}

		// Token: 0x06001AF4 RID: 6900 RVA: 0x000B8154 File Offset: 0x000B6354
		public void method_116()
		{
			foreach (ChartBase chartBase in base.ChartList)
			{
				ChartKLine chartKLine = (ChartKLine)chartBase;
				foreach (Indicator indicator in chartKLine.IndList)
				{
					chartKLine.method_125(indicator);
				}
			}
		}

		// Token: 0x06001AF5 RID: 6901 RVA: 0x000B81EC File Offset: 0x000B63EC
		public void method_117(bool bool_6)
		{
			if (base.SymbDataSet.CurrHisDataSet != null)
			{
				double num = 1.2;
				double num2 = (double)base.MaxSticksPerChart;
				if (num2 >= 1800.0 && bool_6)
				{
					if (base.IndexOfLastItemShownInScr + 1 < 1800 && base.FirstItemShown.Date.Date > base.SymbDataSet.CurrStkMeta.BeginDate.Value.Date)
					{
						if (this.method_125())
						{
							base.method_5();
						}
					}
				}
				else if (num2 > 20.0 || bool_6)
				{
					int num3;
					if (bool_6)
					{
						num3 = Convert.ToInt32(Math.Round(num2 * num, 0));
						if (num3 > 1800)
						{
							num3 = 1800;
						}
					}
					else
					{
						num3 = Convert.ToInt32(Math.Round(num2 / num, 0));
						if (num3 < 20)
						{
							num3 = 20;
						}
					}
					base.MaxSticksPerChart = num3;
					foreach (ChartBase chartBase in base.ChartList)
					{
						chartBase.vmethod_1(num3);
					}
					if (this.IsInRetroMode)
					{
						if (this.int_5 >= 0)
						{
							if (num3 >= this.int_5 && base.IsNMinsPeriod)
							{
								this.method_125();
							}
							if (this.int_5 == base.IndexOfLastItemShown)
							{
								HisData hisData_ = base.method_19(base.SymbDataSet.DateTimeOfLastRec);
								this.vmethod_18(hisData_, false, false);
							}
							else
							{
								this.method_118(this.int_5);
							}
						}
					}
					else
					{
						if (num3 >= base.IndexOfLastItemShown && base.IsNMinsPeriod)
						{
							this.method_125();
						}
						base.method_5();
					}
				}
			}
		}

		// Token: 0x06001AF6 RID: 6902 RVA: 0x000B83B0 File Offset: 0x000B65B0
		public void method_118(int int_7)
		{
			if (int_7 >= 0 && int_7 < base.HisDataPeriodSet.PeriodHisDataList.Count)
			{
				DateTime dateTime_ = base.HisDataPeriodSet.PeriodHisDataList.Keys[int_7];
				HisData hisData_ = base.method_19(dateTime_);
				base.vmethod_18(hisData_, false, false);
			}
			else
			{
				Class182.smethod_0(new Exception("MoveToItem() - itemIdx is not valid!"));
			}
		}

		// Token: 0x06001AF7 RID: 6903 RVA: 0x000B8410 File Offset: 0x000B6610
		public override void vmethod_16(PeriodType periodType_0, int? nullable_2, bool bool_6)
		{
			if (HisDataPeriodSet.smethod_2(periodType_0, nullable_2))
			{
				if (bool_6)
				{
					int? num = nullable_2;
					if (!(num.GetValueOrDefault() == 1 & num != null))
					{
						DateTime key;
						if (base.IndexOfLastItemShown < base.PeriodHisDataList.Count && base.IndexOfLastItemShown >= 0)
						{
							key = base.PeriodHisDataList.Keys[base.IndexOfLastItemShown];
						}
						else
						{
							key = base.PeriodHisDataList.Keys.Last<DateTime>();
							Class182.smethod_0(new Exception("IndexOfLastItemShown out of index!"));
						}
						DateTime? dateTime = null;
						if (this.IsInRetroMode)
						{
							if (this.RetroModeLastItemIdx < 0)
							{
								this.RetroModeLastItemIdx = 0;
							}
							if (this.RetroModeLastItemIdx >= base.PeriodHisDataList.Count)
							{
								this.RetroModeLastItemIdx = base.PeriodHisDataList.Count - 1;
							}
							dateTime = new DateTime?(base.PeriodHisDataList.Keys[this.RetroModeLastItemIdx]);
						}
						base.HisDataPeriodSet = this.method_119(periodType_0, nullable_2);
						base.IndexOfLastItemShown = base.PeriodHisDataList.IndexOfKey(key);
						if (dateTime != null)
						{
							this.RetroModeLastItemIdx = base.PeriodHisDataList.IndexOfKey(dateTime.Value);
							return;
						}
						return;
					}
				}
				base.vmethod_16(periodType_0, nullable_2, bool_6);
			}
		}

		// Token: 0x06001AF8 RID: 6904 RVA: 0x000B8554 File Offset: 0x000B6754
		private HisDataPeriodSet method_119(PeriodType periodType_0, int? nullable_2)
		{
			int count = base.HisDataList.Count;
			if (periodType_0 == PeriodType.ByMins && nullable_2 != null)
			{
				int? num = nullable_2;
				HisDataPeriodSet result;
				if (num.GetValueOrDefault() == 1 & num != null)
				{
					result = new HisDataPeriodSet(base.SymbDataSet, periodType_0, nullable_2);
				}
				else
				{
					int int_ = 0;
					if (this.IsInRetroMode)
					{
						int_ = base.IndexOfLastItemShown - this.RetroModeLastItemIdx;
					}
					result = base.SymbDataSet.method_81(base.HisDataList, periodType_0, new int?(nullable_2.Value), new DateTime?(this.CurrDateTime), int_);
				}
				return result;
			}
			throw new Exception("PeriodType should be ByMins and PeriodUnits should not be null!");
		}

		// Token: 0x06001AF9 RID: 6905 RVA: 0x0000B36E File Offset: 0x0000956E
		public void method_120(int int_7)
		{
			if (!this.IsInRetroMode)
			{
				this.IsInRetroMode = true;
				this.RetroModeLastItemIdx = base.IndexOfLastItemShown;
				this.method_121(int_7);
			}
			else
			{
				this.method_121(int_7);
			}
		}

		// Token: 0x06001AFA RID: 6906 RVA: 0x000B85F8 File Offset: 0x000B67F8
		private void method_121(int int_7)
		{
			if (base.HisDataPeriodSet != null && base.HisDataPeriodSet.PeriodHisDataList != null)
			{
				DateTime? retroModeLastDT = this.RetroModeLastDT;
				if (this.int_5 <= base.MaxSticksPerChart * 2)
				{
					if (base.IsNMinsPeriod)
					{
						if (base.IndexOfLastItemShown - this.int_6 > 150000 / base.PeriodUnits.Value)
						{
							return;
						}
						if (base.PeriodHisDataList.Keys.First<DateTime>().Date != base.HisDataList.Keys.First<DateTime>().Date)
						{
							DateTime currDateTime = this.CurrDateTime;
							DateTime? retroModeLastDT2 = this.RetroModeLastDT;
							base.vmethod_13();
							this.method_126(currDateTime, retroModeLastDT2);
						}
						else
						{
							SymbDataSet symbDataSet = base.SymbDataSet;
							if (symbDataSet.CurrStkMeta.BeginDate != null)
							{
								if (symbDataSet.CurrHisDataSet != null && symbDataSet.CurrHisDataSet.FetchedHDDataStartDate.Date > symbDataSet.CurrStkMeta.BeginDate.Value.Date)
								{
									this.method_125();
								}
							}
							else
							{
								Class182.smethod_0(new Exception("this.SymbDataSet.CurrStkMeta.BeginDate is null!"));
							}
						}
					}
					else
					{
						if (base.PeriodType != PeriodType.ByDay)
						{
							if (base.PeriodType != PeriodType.ByMins)
							{
								goto IL_285;
							}
							int? periodUnits = base.PeriodUnits;
							if (!(periodUnits.GetValueOrDefault() > 60 & periodUnits != null))
							{
								goto IL_285;
							}
						}
						if (base.PeriodHisDataList != null && base.PeriodHisDataList.Count > 0 && base.SymbDataSet != null && base.SymbDataSet.Curr1hPeriodHisData != null && base.SymbDataSet.Curr1hPeriodHisData.PeriodHisDataList != null && base.SymbDataSet.Curr1hPeriodHisData.PeriodHisDataList.Count > 0 && base.PeriodHisDataList.Keys.First<DateTime>().Date != base.SymbDataSet.Curr1hPeriodHisData.PeriodHisDataList.Keys.First<DateTime>().Date)
						{
							DateTime currDateTime2 = this.CurrDateTime;
							DateTime? retroModeLastDT3 = this.RetroModeLastDT;
							base.HisDataPeriodSet = base.SymbDataSet.method_61(base.SymbDataSet.CurrSymbol, base.PeriodType, base.PeriodUnits, new DateTime?(currDateTime2), new DateTime?(base.HisDataList.Keys.Last<DateTime>()));
							this.method_126(currDateTime2, retroModeLastDT3);
						}
					}
				}
				IL_285:
				if (this.int_5 >= base.MaxSticksPerChart)
				{
					this.int_5 -= int_7;
					if (this.int_5 < base.MaxSticksPerChart - 1)
					{
						this.int_5 = base.MaxSticksPerChart - 1;
					}
					if (base.HisDataPeriodSet != null && base.HisDataPeriodSet.PeriodHisDataList != null && this.int_5 >= base.HisDataPeriodSet.PeriodHisDataList.Count)
					{
						this.int_5 = base.HisDataPeriodSet.PeriodHisDataList.Count - 1;
					}
					this.method_118(this.RetroModeLastItemIdx);
				}
				else if (retroModeLastDT != null)
				{
					this.int_5 = base.PeriodHisDataList.IndexOfKey(retroModeLastDT.Value);
					if (this.int_5 >= 0)
					{
						this.method_118(this.RetroModeLastItemIdx);
					}
					else
					{
						Class182.smethod_0(new Exception("_RetroModeLastItemIdx < 0 !"));
					}
				}
			}
			else
			{
				Class182.smethod_0(new Exception("this.HisDataPeriodSet is empty!"));
			}
		}

		// Token: 0x06001AFB RID: 6907 RVA: 0x0000B39D File Offset: 0x0000959D
		public void method_122(int int_7)
		{
			if (this.IsInRetroMode)
			{
				this.method_124(int_7);
			}
		}

		// Token: 0x06001AFC RID: 6908 RVA: 0x0000B3B0 File Offset: 0x000095B0
		public void method_123()
		{
			if (this.IsInRetroMode)
			{
				this.method_124(1);
				if (this.RetroModeLastItemIdx < base.IndexOfLastItemShown)
				{
					base.RevCrossXVal = new double?((double)(base.IndexOfLastItemShownInScr + 1));
				}
			}
		}

		// Token: 0x06001AFD RID: 6909 RVA: 0x000B8974 File Offset: 0x000B6B74
		private void method_124(int int_7)
		{
			if (this.int_5 < base.IndexOfLastItemShown)
			{
				this.int_5 += int_7;
				if (this.int_5 > base.PeriodHisDataList.Count - 1)
				{
					this.int_5 = base.PeriodHisDataList.Count - 1;
				}
				if (this.int_5 >= base.IndexOfLastItemShown)
				{
					this.int_6 = base.IndexOfLastItemShown;
					HisData hisData_ = base.method_19(base.SymbDataSet.DateTimeOfLastRec);
					this.vmethod_18(hisData_, true, false);
				}
				else
				{
					this.method_118(this.int_5);
				}
			}
		}

		// Token: 0x06001AFE RID: 6910 RVA: 0x000B8A0C File Offset: 0x000B6C0C
		private bool method_125()
		{
			if (base.SymbDataSet.CurrHisDataSet.FetchedHDDataStartDate.Date > base.SymbDataSet.CurrStkMeta.BeginDate.Value.Date)
			{
				DateTime currDateTime = this.CurrDateTime;
				DateTime? retroModeLastDT = this.RetroModeLastDT;
				if (base.SymbDataSet.CurrHisDataSet.method_8())
				{
					Base.UI.smethod_28();
					this.method_126(currDateTime, retroModeLastDT);
					return true;
				}
			}
			return false;
		}

		// Token: 0x06001AFF RID: 6911 RVA: 0x0000B3E5 File Offset: 0x000095E5
		private void method_126(DateTime dateTime_0, DateTime? nullable_2)
		{
			this.method_127(dateTime_0, nullable_2, true);
		}

		// Token: 0x06001B00 RID: 6912 RVA: 0x000B8A94 File Offset: 0x000B6C94
		private void method_127(DateTime dateTime_0, DateTime? nullable_2, bool bool_6)
		{
			if (bool_6)
			{
				Base.UI.smethod_30();
				using (List<ChtCtrl_KLine>.Enumerator enumerator = Base.UI.ChtCtrl_KLineList.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						ChtCtrl_KLine chtCtrl_KLine = enumerator.Current;
						if (chtCtrl_KLine != this)
						{
							HisData hisData_ = base.method_19(this.CurrDateTime);
							chtCtrl_KLine.vmethod_18(hisData_, true, false);
						}
					}
					goto IL_57;
				}
			}
			this.method_115();
			IL_57:
			if (this.IsInRetroMode && nullable_2 != null && base.PeriodHisDataList != null && base.PeriodHisDataList.Count > 0)
			{
				if (base.PeriodHisDataList.IndexOfKey(nullable_2.Value) >= 0)
				{
					this.int_5 = base.PeriodHisDataList.IndexOfKey(nullable_2.Value);
				}
				DateTime value = nullable_2.Value;
			}
		}

		// Token: 0x06001B01 RID: 6913 RVA: 0x000B8B68 File Offset: 0x000B6D68
		public override void vmethod_25(SplitContainer splitContainer_4, ChtCtrlParam chtCtrlParam_0)
		{
			base.vmethod_25(splitContainer_4, chtCtrlParam_0);
			if (chtCtrlParam_0 == null)
			{
				this.method_83();
			}
			else
			{
				ChtCtrlParam_KLine chtCtrlParam_KLine = chtCtrlParam_0 as ChtCtrlParam_KLine;
				ChartParam chartParam_ = null;
				if (chtCtrlParam_KLine.ChartParamList != null && chtCtrlParam_KLine.ChartParamList[0] != null)
				{
					chartParam_ = chtCtrlParam_KLine.ChartParamList[0];
				}
				base.SuspendLayout();
				base.ChtSpC.SuspendLayout();
				if (chtCtrlParam_0.SpCParam_this.Panel2Collapsed)
				{
					base.ChtSpC.Panel2Collapsed = true;
					this.method_60(chartParam_, true);
				}
				else
				{
					this.method_60(chartParam_, false);
					this.method_51();
					try
					{
						base.ChtSpC.SplitterDistance = (int)Math.Round(base.ChtSpC.Height * chtCtrlParam_0.SpCParam_this.SplitterDistance / chtCtrlParam_0.SpCParam_this.Size.Height);
					}
					catch
					{
					}
					ChartParam chartParam_2 = null;
					if (chtCtrlParam_KLine.ChartParamList != null && chtCtrlParam_KLine.ChartParamList.Count > 1 && chtCtrlParam_KLine.ChartParamList[1] != null)
					{
						chartParam_2 = chtCtrlParam_KLine.ChartParamList[1];
					}
					if (chtCtrlParam_KLine.SpCParam_VolMACD.Panel2Collapsed)
					{
						this.SpCVolMACD.Panel2Collapsed = true;
						this.method_88(chartParam_2, true);
					}
					else
					{
						this.method_129(this.SpCVolMACD, chtCtrlParam_KLine.SpCParam_VolMACD);
						this.method_88(chartParam_2, false);
						ChartParam chartParam_3 = null;
						if (chtCtrlParam_KLine.ChartParamList != null && chtCtrlParam_KLine.ChartParamList.Count > 2 && chtCtrlParam_KLine.ChartParamList[2] != null)
						{
							chartParam_3 = chtCtrlParam_KLine.ChartParamList[2];
						}
						if (chtCtrlParam_KLine.SpCParam_VolMACD_Sub == null)
						{
							this.method_90(chartParam_3, true);
						}
						else
						{
							this.method_55();
							this.method_129(this.SpCVolMACD_Sub, chtCtrlParam_KLine.SpCParam_VolMACD_Sub);
							this.method_90(chartParam_3, false);
							ChartParam chartParam_4 = null;
							if (chtCtrlParam_KLine.ChartParamList != null && chtCtrlParam_KLine.ChartParamList.Count > 3 && chtCtrlParam_KLine.ChartParamList[3] != null)
							{
								chartParam_4 = chtCtrlParam_KLine.ChartParamList[3];
							}
							this.method_92(chartParam_4, true);
						}
					}
				}
			}
			this.method_128();
			base.ChtSpC.ResumeLayout();
			base.ResumeLayout();
		}

		// Token: 0x06001B02 RID: 6914 RVA: 0x000B8DA0 File Offset: 0x000B6FA0
		private void method_128()
		{
			List<ChartKLSub> subChartList = this.SubChartList;
			if (subChartList.Any<ChartKLSub>())
			{
				List<ChartKLSub> list = subChartList.Where(new Func<ChartKLSub, bool>(ChtCtrl_KLine.<>c.<>9.method_3)).ToList<ChartKLSub>();
				if (list.Any<ChartKLSub>())
				{
					list.Last<ChartKLSub>().IsEntered = true;
					int count = list.Count;
					if (count > 1)
					{
						for (int i = 0; i < count - 1; i++)
						{
							list[0].IsEntered = false;
						}
					}
				}
				else
				{
					subChartList.Last<ChartKLSub>().IsEntered = true;
				}
			}
		}

		// Token: 0x06001B03 RID: 6915 RVA: 0x000B8E34 File Offset: 0x000B7034
		private void method_129(SplitContainer splitContainer_4, SplitContainerParam splitContainerParam_0)
		{
			splitContainer_4.Panel1MinSize = 1;
			splitContainer_4.Panel2MinSize = 1;
			decimal num = splitContainer_4.Height;
			int num2 = (int)Math.Round(num * splitContainerParam_0.SplitterDistance / splitContainerParam_0.Size.Height);
			if (num2 > num)
			{
				num2 = Convert.ToInt32(Math.Round(num * 0.3m));
			}
			try
			{
				splitContainer_4.SplitterDistance = num2;
			}
			catch
			{
			}
		}

		// Token: 0x06001B04 RID: 6916 RVA: 0x000B8ED8 File Offset: 0x000B70D8
		private void method_130(ChartKLine chartKLine_0, List<Indicator> list_1, List<UserDefineIndScript> list_2)
		{
			if (chartKLine_0 != null && list_1 != null)
			{
				foreach (Indicator indicator in list_1)
				{
					if (indicator.InitInd(chartKLine_0))
					{
						indicator.InitItem();
						chartKLine_0.IndList.Add(indicator);
						indicator.IndicatorRemoved += this.method_131;
					}
				}
			}
			if (chartKLine_0 != null && list_2 != null)
			{
				foreach (UserDefineIndScript userDefineIndScript in list_2)
				{
					try
					{
						UserDefineInd userDefineInd = new UserDefineInd(chartKLine_0.DP, userDefineIndScript);
						if (UserDefineInd.smethod_5(userDefineIndScript, base.Symbol))
						{
							try
							{
								IndEx indEx = IndEx.smethod_0(userDefineInd);
								if (indEx.InitInd(chartKLine_0))
								{
									indEx.InitItem();
									chartKLine_0.IndList.Add(indEx);
									indEx.IndicatorRemoved += this.method_131;
								}
							}
							catch (Exception ex)
							{
								MessageBox.Show(userDefineInd.Name + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Hand);
							}
						}
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
			}
		}

		// Token: 0x06001B05 RID: 6917 RVA: 0x0000B3F2 File Offset: 0x000095F2
		private void method_131(object sender, EventArgs27 e)
		{
			if (e.indicator_0.IsMainChartInd)
			{
				this.Chart_CS.method_269();
				this.Chart_CS.ZedGraphControl.Refresh();
			}
		}

		// Token: 0x06001B06 RID: 6918 RVA: 0x000B9040 File Offset: 0x000B7240
		public override ChtCtrlParam vmethod_26()
		{
			ChtCtrlParam chtCtrlParam = base.vmethod_26();
			ChtCtrlParam_KLine chtCtrlParam_KLine = new ChtCtrlParam_KLine();
			chtCtrlParam.method_0(chtCtrlParam_KLine);
			List<ChartParam> list = new List<ChartParam>();
			if (this.Chart_CS != null)
			{
				ChartParam item = new ChartParam(ChartType.CandleStick, this.Chart_CS.IndList, this.Chart_CS.UDSList, this.Chart_CS.IsYAxisCoordsLogType);
				list.Add(item);
			}
			this.method_132(list, this.SubChart1);
			this.method_132(list, this.SubChart2);
			this.method_132(list, this.SubChart3);
			chtCtrlParam_KLine.ChartParamList = list;
			chtCtrlParam_KLine.SpCParam_VolMACD = this.method_134(this.SpCVolMACD);
			chtCtrlParam_KLine.SpCParam_VolMACD_Sub = this.method_134(this.SpCVolMACD_Sub);
			return chtCtrlParam_KLine;
		}

		// Token: 0x06001B07 RID: 6919 RVA: 0x0000B41E File Offset: 0x0000961E
		private void method_132(List<ChartParam> list_1, ChartKLSub chartKLSub_3)
		{
			if (chartKLSub_3 != null)
			{
				list_1.Add(this.method_133(chartKLSub_3));
			}
		}

		// Token: 0x06001B08 RID: 6920 RVA: 0x000B90F4 File Offset: 0x000B72F4
		private ChartParam method_133(ChartKLine chartKLine_0)
		{
			ChartType chartType = this.method_135(chartKLine_0);
			ChartParam result;
			if (chartType == ChartType.CandleStick)
			{
				ChartCS chartCS = chartKLine_0 as ChartCS;
				result = new ChartParam(chartType, chartKLine_0.IndList, chartKLine_0.UDSList, chartCS.IsYAxisCoordsLogType);
			}
			else
			{
				result = new ChartParam(chartType, chartKLine_0.IndList, chartKLine_0.UDSList, false);
			}
			return result;
		}

		// Token: 0x06001B09 RID: 6921 RVA: 0x000B9148 File Offset: 0x000B7348
		private SplitContainerParam method_134(SplitContainer splitContainer_4)
		{
			SplitContainerParam result;
			if (splitContainer_4 != null)
			{
				SplitContainerParam splitContainerParam = new SplitContainerParam();
				splitContainerParam.Panel2Collapsed = splitContainer_4.Panel2Collapsed;
				splitContainerParam.Size = splitContainer_4.Size;
				try
				{
					splitContainerParam.SplitterDistance = splitContainer_4.SplitterDistance;
				}
				catch
				{
				}
				result = splitContainerParam;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001B0A RID: 6922 RVA: 0x000B91A0 File Offset: 0x000B73A0
		private ChartType method_135(ChartKLine chartKLine_0)
		{
			ChartType result;
			if (chartKLine_0 is ChartCS)
			{
				result = ChartType.CandleStick;
			}
			else
			{
				result = ChartType.MACD;
			}
			return result;
		}

		// Token: 0x06001B0B RID: 6923 RVA: 0x000B91C0 File Offset: 0x000B73C0
		public override bool vmethod_27()
		{
			bool result;
			if (this.IsInRetroMode)
			{
				if (this.IsInRetroMode)
				{
					result = (base.IndexOfLastItemShown == this.RetroModeLastItemIdx);
				}
				else
				{
					result = false;
				}
			}
			else
			{
				result = true;
			}
			return result;
		}

		// Token: 0x06001B0C RID: 6924 RVA: 0x000B91F8 File Offset: 0x000B73F8
		protected override void vmethod_24(bool bool_6)
		{
			base.vmethod_24(bool_6);
			if (this.SelectedInd != null)
			{
				this.SelectedInd.IsSelected = false;
			}
			if (!bool_6)
			{
				this.Chart_CS.method_169();
			}
			foreach (ChartBase chartBase in base.ChartList)
			{
				chartBase.vmethod_21(bool_6);
			}
		}

		// Token: 0x06001B0D RID: 6925 RVA: 0x000B9278 File Offset: 0x000B7478
		protected override void vmethod_31(bool bool_6)
		{
			base.vmethod_31(bool_6);
			foreach (ChartKLSub chartBase_ in this.SubChartList)
			{
				base.method_38(chartBase_, bool_6);
			}
		}

		// Token: 0x06001B0E RID: 6926 RVA: 0x000B92D8 File Offset: 0x000B74D8
		public void method_136()
		{
			try
			{
				if (this.Chart_CS != null)
				{
					this.Chart_CS.method_258();
				}
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x06001B0F RID: 6927 RVA: 0x000B9314 File Offset: 0x000B7514
		protected override int vmethod_1()
		{
			int result = 0;
			if (this.Chart_CS != null && this.Chart_CS.Curve != null)
			{
				result = this.Chart_CS.Curve.NPts;
			}
			return result;
		}

		// Token: 0x06001B10 RID: 6928 RVA: 0x000B9350 File Offset: 0x000B7550
		protected override void Dispose(bool disposing)
		{
			base.Dispose(disposing);
			try
			{
				Base.Data.CurrSymblChanged -= this.method_139;
				base.SymbDataSet.HDPS_1hChanged -= this.method_140;
				base.SymbDataSetChanging -= this.method_141;
			}
			catch
			{
			}
		}

		// Token: 0x06001B11 RID: 6929 RVA: 0x0000B432 File Offset: 0x00009632
		protected override void vmethod_32(object sender, SplitterEventArgs e)
		{
			this.method_108();
			if (this.splitContainer_2 != null && this.splitContainer_2.Panel2Collapsed)
			{
				this.method_110(this.SubChart1);
			}
		}

		// Token: 0x06001B12 RID: 6930 RVA: 0x0000B45D File Offset: 0x0000965D
		private void method_137(object sender, SplitterEventArgs e)
		{
			this.method_109();
		}

		// Token: 0x06001B13 RID: 6931 RVA: 0x0000B467 File Offset: 0x00009667
		private void method_138(object sender, EventArgs27 e)
		{
			this.method_50(e);
		}

		// Token: 0x06001B14 RID: 6932 RVA: 0x000041AE File Offset: 0x000023AE
		private void method_139(EventArgs1 eventArgs1_0)
		{
		}

		// Token: 0x06001B15 RID: 6933 RVA: 0x0000B472 File Offset: 0x00009672
		private void method_140(object sender, EventArgs e)
		{
			this.method_78();
		}

		// Token: 0x06001B16 RID: 6934 RVA: 0x000B93B8 File Offset: 0x000B75B8
		private void method_141(object sender, EventArgs e)
		{
			base.SymbDataSet.HDPS_1hChanged -= this.method_140;
			SymbDataSet symbDataSet = sender as SymbDataSet;
			if (symbDataSet != null)
			{
				symbDataSet.HDPS_1hChanged += this.method_140;
			}
		}

		// Token: 0x1700045E RID: 1118
		// (get) Token: 0x06001B17 RID: 6935 RVA: 0x000B93FC File Offset: 0x000B75FC
		public ChartCS Chart_CS
		{
			get
			{
				return this.chartCS_0;
			}
		}

		// Token: 0x1700045F RID: 1119
		// (get) Token: 0x06001B18 RID: 6936 RVA: 0x000B9414 File Offset: 0x000B7614
		// (set) Token: 0x06001B19 RID: 6937 RVA: 0x0000B47C File Offset: 0x0000967C
		public Indicator SelectedInd
		{
			get
			{
				return this.indicator_0;
			}
			set
			{
				this.indicator_0 = value;
			}
		}

		// Token: 0x17000460 RID: 1120
		// (get) Token: 0x06001B1A RID: 6938 RVA: 0x000B942C File Offset: 0x000B762C
		public SplitContainer SpCVolMACD
		{
			get
			{
				return this.splitContainer_2;
			}
		}

		// Token: 0x17000461 RID: 1121
		// (get) Token: 0x06001B1B RID: 6939 RVA: 0x000B9444 File Offset: 0x000B7644
		public SplitContainer SpCVolMACD_Sub
		{
			get
			{
				return this.splitContainer_3;
			}
		}

		// Token: 0x17000462 RID: 1122
		// (get) Token: 0x06001B1C RID: 6940 RVA: 0x000B945C File Offset: 0x000B765C
		public List<Indicator> IndList
		{
			get
			{
				List<Indicator> list = new List<Indicator>();
				foreach (ChartBase chartBase in base.ChartList)
				{
					ChartKLine chartKLine = (ChartKLine)chartBase;
					if (chartKLine.IndList.Count > 0)
					{
						list.AddRange(chartKLine.IndList);
					}
				}
				return list;
			}
		}

		// Token: 0x17000463 RID: 1123
		// (get) Token: 0x06001B1D RID: 6941 RVA: 0x000B94D4 File Offset: 0x000B76D4
		// (set) Token: 0x06001B1E RID: 6942 RVA: 0x0000B487 File Offset: 0x00009687
		public bool IsInRetroMode
		{
			get
			{
				return this.bool_5;
			}
			set
			{
				this.bool_5 = value;
				if (this.bool_5)
				{
					this.method_49();
				}
			}
		}

		// Token: 0x17000464 RID: 1124
		// (get) Token: 0x06001B1F RID: 6943 RVA: 0x000B94EC File Offset: 0x000B76EC
		// (set) Token: 0x06001B20 RID: 6944 RVA: 0x0000B4A0 File Offset: 0x000096A0
		public int RetroModeLastItemIdx
		{
			get
			{
				int indexOfLastItemShown;
				if (this.bool_5)
				{
					indexOfLastItemShown = this.int_5;
				}
				else
				{
					indexOfLastItemShown = base.IndexOfLastItemShown;
				}
				return indexOfLastItemShown;
			}
			set
			{
				this.int_5 = value;
			}
		}

		// Token: 0x17000465 RID: 1125
		// (get) Token: 0x06001B21 RID: 6945 RVA: 0x000B9514 File Offset: 0x000B7714
		public int RetroModeFirstItemIdx
		{
			get
			{
				int maxSticksPerChart = base.MaxSticksPerChart;
				if (this.int_5 < maxSticksPerChart)
				{
					this.int_6 = 0;
				}
				else
				{
					this.int_6 = this.int_5 - maxSticksPerChart + 1;
				}
				return this.int_6;
			}
		}

		// Token: 0x17000466 RID: 1126
		// (get) Token: 0x06001B22 RID: 6946 RVA: 0x000B9554 File Offset: 0x000B7754
		public DateTime? RetroModeLastDT
		{
			get
			{
				DateTime? result;
				if (this.IsInRetroMode && this.RetroModeLastItemIdx > 0 && this.RetroModeLastItemIdx < base.PeriodHisDataList.Count)
				{
					result = new DateTime?(base.PeriodHisDataList.Keys[this.RetroModeLastItemIdx]);
				}
				else
				{
					result = null;
				}
				return result;
			}
		}

		// Token: 0x17000467 RID: 1127
		// (get) Token: 0x06001B23 RID: 6947 RVA: 0x000B95B0 File Offset: 0x000B77B0
		public bool IsMouseDownOnRewindBtn
		{
			get
			{
				return this.class52_0.IsMouseDown;
			}
		}

		// Token: 0x17000468 RID: 1128
		// (get) Token: 0x06001B24 RID: 6948 RVA: 0x000B95CC File Offset: 0x000B77CC
		public bool IsMouseDownOnForwardBtn
		{
			get
			{
				return this.class53_0.IsMouseDown;
			}
		}

		// Token: 0x17000469 RID: 1129
		// (get) Token: 0x06001B25 RID: 6949 RVA: 0x000B95E8 File Offset: 0x000B77E8
		// (set) Token: 0x06001B26 RID: 6950 RVA: 0x0000B4AB File Offset: 0x000096AB
		public DrawObj SelectedDrawObj
		{
			get
			{
				return this.Chart_CS.SelectedDrawObj;
			}
			set
			{
				this.Chart_CS.SelectedDrawObj = value;
			}
		}

		// Token: 0x1700046A RID: 1130
		// (get) Token: 0x06001B27 RID: 6951 RVA: 0x000B9604 File Offset: 0x000B7804
		public override ChartBase MainChart
		{
			get
			{
				return this.Chart_CS;
			}
		}

		// Token: 0x1700046B RID: 1131
		// (get) Token: 0x06001B28 RID: 6952 RVA: 0x000B961C File Offset: 0x000B781C
		public override DateTime CurrDateTime
		{
			get
			{
				DateTime dateTime = default(DateTime);
				DateTime result;
				if (base.IsInCrossReviewMode && this.RetroModeLastItemIdx < base.PeriodHisDataList.Count && this.RetroModeLastItemIdx >= 0)
				{
					result = base.PeriodHisDataList.Keys[this.RetroModeLastItemIdx];
				}
				else
				{
					if (base.SymbDataSet.CurrHisDataSet.CurrHisData != null)
					{
						dateTime = base.SymbDataSet.CurrHisDataSet.CurrHisData.Date;
					}
					else
					{
						if (base.SymbDataSet.CurrHisDataSet.FetchedHisDataList != null && base.SymbDataSet.CurrHisDataSet.FetchedHisDataList.Count > 0)
						{
							dateTime = base.SymbDataSet.CurrHisDataSet.FetchedHDDataStartDate;
						}
						else
						{
							Class182.smethod_0(new Exception("this.SymbDataSet.CurrHisDataSet.FetchedHisDataList is empty!"));
							if (base.SymbDataSet.LastHisData != null)
							{
								dateTime = base.SymbDataSet.LastHisData.Date;
							}
						}
						base.SymbDataSet.CurrHisDataSet.CurrHisData = base.SymbDataSet.LastHisData;
					}
					result = dateTime;
				}
				return result;
			}
		}

		// Token: 0x1700046C RID: 1132
		// (get) Token: 0x06001B29 RID: 6953 RVA: 0x000B9728 File Offset: 0x000B7928
		public int HalfNbOfSticks
		{
			get
			{
				return Convert.ToInt32(Math.Floor(base.MaxSticksPerChart / 2m));
			}
		}

		// Token: 0x1700046D RID: 1133
		// (get) Token: 0x06001B2A RID: 6954 RVA: 0x000B975C File Offset: 0x000B795C
		public List<ChartKLSub> SubChartList
		{
			get
			{
				List<ChartKLSub> list = new List<ChartKLSub>();
				IEnumerable<ChartBase> enumerable = base.ChartList.Where(new Func<ChartBase, bool>(ChtCtrl_KLine.<>c.<>9.method_4));
				if (enumerable.Any<ChartBase>())
				{
					foreach (ChartBase chartBase in enumerable)
					{
						if (chartBase is ChartKLSub)
						{
							list.Add(chartBase as ChartKLSub);
						}
					}
				}
				return list;
			}
		}

		// Token: 0x1700046E RID: 1134
		// (get) Token: 0x06001B2B RID: 6955 RVA: 0x000B97F4 File Offset: 0x000B79F4
		public ChartKLSub EnteredSubChart
		{
			get
			{
				List<ChartKLSub> subChartList = this.SubChartList;
				ChartKLSub result;
				if (subChartList.Count > 0)
				{
					IEnumerable<ChartKLSub> source = subChartList.Where(new Func<ChartKLSub, bool>(ChtCtrl_KLine.<>c.<>9.method_5));
					if (source.Any<ChartKLSub>())
					{
						result = source.First<ChartKLSub>();
					}
					else
					{
						subChartList.Last<ChartKLSub>().IsEntered = true;
						result = subChartList.Last<ChartKLSub>();
					}
				}
				else
				{
					result = null;
				}
				return result;
			}
		}

		// Token: 0x1700046F RID: 1135
		// (get) Token: 0x06001B2C RID: 6956 RVA: 0x000B9864 File Offset: 0x000B7A64
		public ChartKLSub SubChart1
		{
			get
			{
				ChartKLSub chartKLSub = null;
				ChartKLSub result;
				if (base.ChtSpC.Panel2Collapsed)
				{
					result = null;
				}
				else
				{
					if (this.SpCVolMACD != null)
					{
						chartKLSub = this.method_100(this.SpCVolMACD.Panel1);
					}
					result = chartKLSub;
				}
				return result;
			}
		}

		// Token: 0x17000470 RID: 1136
		// (get) Token: 0x06001B2D RID: 6957 RVA: 0x000B98A4 File Offset: 0x000B7AA4
		public ChartKLSub SubChart2
		{
			get
			{
				ChartKLSub chartKLSub = null;
				ChartKLSub result;
				if (this.SpCVolMACD == null)
				{
					result = null;
				}
				else if (!base.ChtSpC.Panel2Collapsed && !this.SpCVolMACD.Panel2Collapsed)
				{
					if (this.SpCVolMACD.Panel2.Controls != null && this.SpCVolMACD.Panel2.Controls.Count != 0)
					{
						if (this.SpCVolMACD.Panel2.Controls[0] is SplitContainer)
						{
							if (this.SpCVolMACD_Sub != null)
							{
								chartKLSub = this.method_100(this.SpCVolMACD_Sub.Panel1);
							}
						}
						else if (this.SpCVolMACD != null)
						{
							chartKLSub = this.method_100(this.SpCVolMACD.Panel2);
						}
						result = chartKLSub;
					}
					else
					{
						result = null;
					}
				}
				else
				{
					result = null;
				}
				return result;
			}
		}

		// Token: 0x17000471 RID: 1137
		// (get) Token: 0x06001B2E RID: 6958 RVA: 0x000B996C File Offset: 0x000B7B6C
		public ChartKLSub SubChart3
		{
			get
			{
				ChartKLSub result;
				if (this.SpCVolMACD != null && this.SpCVolMACD.Panel2 != null && this.SpCVolMACD.Panel2.Controls.Count != 0)
				{
					if (!base.ChtSpC.Panel2Collapsed && !this.SpCVolMACD.Panel2Collapsed && this.SpCVolMACD.Panel2.Controls[0] is SplitContainer)
					{
						result = this.method_100(this.SpCVolMACD_Sub.Panel2);
					}
					else
					{
						result = null;
					}
				}
				else
				{
					result = null;
				}
				return result;
			}
		}

		// Token: 0x04000D6F RID: 3439
		private ChartCS chartCS_0;

		// Token: 0x04000D70 RID: 3440
		private SplitContainer splitContainer_2;

		// Token: 0x04000D71 RID: 3441
		private SplitContainer splitContainer_3;

		// Token: 0x04000D72 RID: 3442
		private Indicator indicator_0;

		// Token: 0x04000D73 RID: 3443
		private decimal decimal_0 = -1m;

		// Token: 0x04000D74 RID: 3444
		private decimal decimal_1 = -1m;

		// Token: 0x04000D75 RID: 3445
		private decimal decimal_2 = -1m;

		// Token: 0x04000D76 RID: 3446
		private int int_4 = 1;

		// Token: 0x04000D77 RID: 3447
		private ChartKLSub chartKLSub_0;

		// Token: 0x04000D78 RID: 3448
		private ChartKLSub chartKLSub_1;

		// Token: 0x04000D79 RID: 3449
		private ChartKLSub chartKLSub_2;

		// Token: 0x04000D7A RID: 3450
		private bool bool_5;

		// Token: 0x04000D7B RID: 3451
		private int int_5;

		// Token: 0x04000D7C RID: 3452
		private int int_6;

		// Token: 0x04000D7D RID: 3453
		private Class52 class52_0;

		// Token: 0x04000D7E RID: 3454
		private Class53 class53_0;

		// Token: 0x04000D7F RID: 3455
		private Class54 class54_0;

		// Token: 0x04000D80 RID: 3456
		private Class55 class55_0;

		// Token: 0x04000D81 RID: 3457
		[CompilerGenerated]
		private Delegate18 delegate18_0;

		// Token: 0x04000D82 RID: 3458
		[CompilerGenerated]
		private IndicatorEventHandler indicatorEventHandler_0;

		// Token: 0x02000262 RID: 610
		[CompilerGenerated]
		private sealed class Class325
		{
			// Token: 0x06001B38 RID: 6968 RVA: 0x000B9A5C File Offset: 0x000B7C5C
			internal bool method_0(UserDefineIndScript userDefineIndScript_0)
			{
				return userDefineIndScript_0.Name == this.string_0;
			}

			// Token: 0x04000D8A RID: 3466
			public string string_0;
		}

		// Token: 0x02000263 RID: 611
		[CompilerGenerated]
		private sealed class Class326
		{
			// Token: 0x06001B3A RID: 6970 RVA: 0x000B9A80 File Offset: 0x000B7C80
			internal bool method_0(UserDefineIndScript userDefineIndScript_0)
			{
				return userDefineIndScript_0.Name == this.string_0;
			}

			// Token: 0x04000D8B RID: 3467
			public string string_0;
		}

		// Token: 0x02000264 RID: 612
		[CompilerGenerated]
		private sealed class Class327
		{
			// Token: 0x06001B3C RID: 6972 RVA: 0x000B9AA4 File Offset: 0x000B7CA4
			internal bool method_0(ChartKLSub chartKLSub_0)
			{
				return chartKLSub_0.ParentPanel == this.splitterPanel_0;
			}

			// Token: 0x04000D8C RID: 3468
			public SplitterPanel splitterPanel_0;
		}
	}
}
