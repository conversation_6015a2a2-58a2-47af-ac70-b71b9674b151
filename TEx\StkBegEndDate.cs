﻿using System;
using System.Runtime.CompilerServices;

namespace TEx
{
	// Token: 0x02000041 RID: 65
	public sealed class StkBegEndDate
	{
		// Token: 0x1700009F RID: 159
		// (get) Token: 0x06000202 RID: 514 RVA: 0x000187F8 File Offset: 0x000169F8
		// (set) Token: 0x06000203 RID: 515 RVA: 0x0000329C File Offset: 0x0000149C
		public int StkId { get; set; }

		// Token: 0x170000A0 RID: 160
		// (get) Token: 0x06000204 RID: 516 RVA: 0x00018810 File Offset: 0x00016A10
		// (set) Token: 0x06000205 RID: 517 RVA: 0x000032A7 File Offset: 0x000014A7
		public DateTime MinBegDate_1m { get; set; }

		// Token: 0x170000A1 RID: 161
		// (get) Token: 0x06000206 RID: 518 RVA: 0x00018828 File Offset: 0x00016A28
		// (set) Token: 0x06000207 RID: 519 RVA: 0x000032B2 File Offset: 0x000014B2
		public DateTime MaxEndDate_1m { get; set; }

		// Token: 0x170000A2 RID: 162
		// (get) Token: 0x06000208 RID: 520 RVA: 0x00018840 File Offset: 0x00016A40
		// (set) Token: 0x06000209 RID: 521 RVA: 0x000032BD File Offset: 0x000014BD
		public DateTime MinBegDate_1h { get; set; }

		// Token: 0x170000A3 RID: 163
		// (get) Token: 0x0600020A RID: 522 RVA: 0x00018858 File Offset: 0x00016A58
		// (set) Token: 0x0600020B RID: 523 RVA: 0x000032C8 File Offset: 0x000014C8
		public DateTime MaxEndDate_1h { get; set; }

		// Token: 0x040000C1 RID: 193
		[CompilerGenerated]
		private int int_0;

		// Token: 0x040000C2 RID: 194
		[CompilerGenerated]
		private DateTime dateTime_0;

		// Token: 0x040000C3 RID: 195
		[CompilerGenerated]
		private DateTime dateTime_1;

		// Token: 0x040000C4 RID: 196
		[CompilerGenerated]
		private DateTime dateTime_2;

		// Token: 0x040000C5 RID: 197
		[CompilerGenerated]
		private DateTime dateTime_3;
	}
}
