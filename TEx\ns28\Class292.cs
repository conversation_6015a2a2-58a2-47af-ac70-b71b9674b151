﻿using System;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns27;
using TEx;
using TEx.Trading;

namespace ns28
{
	// Token: 0x02000215 RID: 533
	internal sealed class Class292 : Class290
	{
		// Token: 0x14000080 RID: 128
		// (add) Token: 0x060015D8 RID: 5592 RVA: 0x00091AA4 File Offset: 0x0008FCA4
		// (remove) Token: 0x060015D9 RID: 5593 RVA: 0x00091ADC File Offset: 0x0008FCDC
		public event EventHandler CreateCondOrderRequested
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060015DA RID: 5594 RVA: 0x00008C48 File Offset: 0x00006E48
		protected void method_5()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x060015DB RID: 5595 RVA: 0x00008C63 File Offset: 0x00006E63
		public Class292()
		{
			base.RowContextMenuStripNeeded += this.Class292_RowContextMenuStripNeeded;
			base.Resize += this.Class292_Resize;
		}

		// Token: 0x060015DC RID: 5596 RVA: 0x00008C91 File Offset: 0x00006E91
		protected override void vmethod_1()
		{
			base.DataSource = Base.Trading.ShownCondOrdersList;
			this.method_7();
		}

		// Token: 0x060015DD RID: 5597 RVA: 0x00008A1A File Offset: 0x00006C1A
		public void method_6()
		{
			base.DataSource = null;
			this.vmethod_1();
		}

		// Token: 0x060015DE RID: 5598 RVA: 0x00091B14 File Offset: 0x0008FD14
		protected override void vmethod_0()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = "otItemCreateCondOrdr";
			toolStripMenuItem.Text = "新建条件单...";
			toolStripMenuItem.Click += this.method_10;
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Name = "otItemCancelAllCondOrdr";
			toolStripMenuItem2.Text = "全部取消";
			toolStripMenuItem2.Click += this.method_11;
			ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
			contextMenuStrip.Items.Add(toolStripMenuItem);
			contextMenuStrip.Items.Add(toolStripMenuItem2);
			Base.UI.smethod_73(contextMenuStrip);
			this.ContextMenuStrip = contextMenuStrip;
		}

		// Token: 0x060015DF RID: 5599 RVA: 0x00091BAC File Offset: 0x0008FDAC
		protected override void vmethod_3(DataGridViewCellFormattingEventArgs dataGridViewCellFormattingEventArgs_0)
		{
			DataGridViewRow dataGridViewRow = base.Rows[dataGridViewCellFormattingEventArgs_0.RowIndex];
			DataGridViewCell dataGridViewCell = dataGridViewRow.Cells[dataGridViewCellFormattingEventArgs_0.ColumnIndex];
			if (dataGridViewCellFormattingEventArgs_0.ColumnIndex == 3)
			{
				base.method_2(dataGridViewCell);
			}
			if ((dataGridViewRow.DataBoundItem as ShownCondOrder).OrderStatus != OrderStatus.Active)
			{
				dataGridViewCellFormattingEventArgs_0.CellStyle.ForeColor = Color.Gray;
				dataGridViewCellFormattingEventArgs_0.CellStyle.SelectionForeColor = Color.Gray;
			}
			else
			{
				dataGridViewCellFormattingEventArgs_0.CellStyle.SelectionForeColor = dataGridViewCell.Style.ForeColor;
			}
		}

		// Token: 0x060015E0 RID: 5600 RVA: 0x00091C38 File Offset: 0x0008FE38
		private void method_7()
		{
			if (base.Columns.Count > 0)
			{
				base.Columns[0].HeaderText = "品种";
				base.Columns[0].DisplayIndex = 0;
				base.Columns[1].HeaderText = "触发条件";
				base.Columns[1].DisplayIndex = 2;
				base.Columns[2].HeaderText = "状态";
				base.Columns[2].DisplayIndex = 1;
				base.Columns[3].HeaderText = "买卖";
				base.Columns[3].DisplayIndex = 3;
				base.Columns[4].HeaderText = "开平";
				base.Columns[4].DisplayIndex = 4;
				base.Columns[5].Visible = false;
				base.Columns[6].Visible = false;
				base.Columns[7].Visible = false;
				base.Columns[8].Visible = false;
				base.Columns[9].HeaderText = "报单手数";
				base.Columns[9].DisplayIndex = 5;
				base.Columns[10].Visible = false;
				base.Columns[11].Visible = false;
				base.Columns[12].Visible = false;
				base.Columns[13].HeaderText = "报单价格";
				base.Columns[13].DisplayIndex = 6;
				base.Columns[14].Visible = false;
				base.Columns[15].HeaderText = "预埋时间";
				base.Columns[15].DisplayIndex = 7;
				base.Columns[16].Visible = false;
				base.Columns[17].Visible = false;
				base.Columns[18].Visible = false;
				base.Columns[19].Visible = false;
				base.Columns[20].Visible = false;
				base.Columns[21].Visible = false;
				if (Base.UI.Form.IsInBlindTestMode)
				{
					if (!Base.UI.Form.IsSingleBlindTest)
					{
						base.Columns[0].Visible = false;
					}
					else
					{
						base.Columns[0].Visible = true;
					}
					base.Columns[15].Visible = false;
				}
				else
				{
					base.Columns[0].Visible = true;
					base.Columns[15].Visible = true;
				}
				this.Refresh();
			}
		}

		// Token: 0x060015E1 RID: 5601 RVA: 0x00091F24 File Offset: 0x00090124
		private void Class292_RowContextMenuStripNeeded(object sender, DataGridViewRowContextMenuStripNeededEventArgs e)
		{
			DataGridViewRow dataGridViewRow = base.Rows[e.RowIndex];
			dataGridViewRow.Selected = true;
			ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = "otItemEditCondOrdr";
			toolStripMenuItem.Text = "修改...";
			toolStripMenuItem.Click += this.method_9;
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Name = "otItemCancelCondOrdr";
			toolStripMenuItem2.Text = "取消";
			toolStripMenuItem2.Click += this.method_8;
			contextMenuStrip.Items.Add(toolStripMenuItem);
			contextMenuStrip.Items.Add(toolStripMenuItem2);
			Base.UI.smethod_73(contextMenuStrip);
			e.ContextMenuStrip = contextMenuStrip;
			ShownCondOrder shownCondOrder = dataGridViewRow.DataBoundItem as ShownCondOrder;
			if (shownCondOrder.OrderStatus != OrderStatus.Active)
			{
				for (int i = 0; i < e.ContextMenuStrip.Items.Count; i++)
				{
					e.ContextMenuStrip.Items[i].Enabled = false;
				}
			}
			else
			{
				for (int j = 0; j < e.ContextMenuStrip.Items.Count; j++)
				{
					e.ContextMenuStrip.Items[j].Enabled = true;
					e.ContextMenuStrip.Items[j].Tag = shownCondOrder.ID;
				}
			}
		}

		// Token: 0x060015E2 RID: 5602 RVA: 0x00008CA6 File Offset: 0x00006EA6
		private void method_8(object sender, EventArgs e)
		{
			Base.Trading.smethod_94((int)(sender as ToolStripMenuItem).Tag, OrderStatus.Canceled);
			this.Refresh();
		}

		// Token: 0x060015E3 RID: 5603 RVA: 0x00008CC6 File Offset: 0x00006EC6
		private void method_9(object sender, EventArgs e)
		{
			new SetCondOrdForm(Base.Trading.smethod_114((int)(sender as ToolStripMenuItem).Tag))
			{
				Owner = Base.UI.MainForm
			}.Show();
		}

		// Token: 0x060015E4 RID: 5604 RVA: 0x00008CF4 File Offset: 0x00006EF4
		private void method_10(object sender, EventArgs e)
		{
			this.method_5();
		}

		// Token: 0x060015E5 RID: 5605 RVA: 0x00008CFE File Offset: 0x00006EFE
		private void method_11(object sender, EventArgs e)
		{
			Base.Trading.smethod_111();
			this.Refresh();
		}

		// Token: 0x060015E6 RID: 5606 RVA: 0x000041AE File Offset: 0x000023AE
		private void Class292_Resize(object sender, EventArgs e)
		{
		}

		// Token: 0x060015E7 RID: 5607 RVA: 0x0009207C File Offset: 0x0009027C
		private void method_12()
		{
			int num = 0;
			VScrollBar vscrollBar = base.Controls.OfType<VScrollBar>().First<VScrollBar>();
			if (vscrollBar.Visible)
			{
				num = vscrollBar.Width;
			}
			int num2 = base.Parent.Width - num;
			int num3 = 0;
			decimal d;
			bool flag;
			if (!Base.UI.Form.IsInBlindTestMode)
			{
				d = 625m;
				flag = (num2 - num3 > d);
				num2 -= num3;
				try
				{
					if (base.Columns.Count > 1)
					{
						base.Columns[0].Width = (flag ? Convert.ToInt32(Math.Floor(60 * num2 / d)) : 60);
						base.Columns[1].Width = (flag ? Convert.ToInt32(Math.Floor(110 * num2 / d)) : 110);
						base.Columns[2].Width = (flag ? Convert.ToInt32(Math.Floor(60 * num2 / d)) : 60);
						base.Columns[3].Width = (flag ? Convert.ToInt32(Math.Floor(50 * num2 / d)) : 50);
						base.Columns[4].Width = (flag ? Convert.ToInt32(Math.Floor(50 * num2 / d)) : 50);
						base.Columns[9].Width = (flag ? Convert.ToInt32(Math.Floor(80 * num2 / d)) : 80);
						base.Columns[13].Width = (flag ? Convert.ToInt32(Math.Floor(80 * num2 / d)) : 80);
						base.Columns[15].Width = (flag ? Convert.ToInt32(Math.Floor(135 * num2 / d)) : 135);
					}
					return;
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
					return;
				}
			}
			if (!Base.UI.Form.IsSingleBlindTest)
			{
				d = 430m;
				flag = (num2 - num3 > d);
				num2 -= num3;
			}
			else
			{
				d = 490m;
				flag = (num2 - num3 > d);
				num2 -= num3;
				try
				{
					base.Columns[0].Width = (flag ? Convert.ToInt32(Math.Floor(60 * num2 / d)) : 60);
				}
				catch
				{
				}
			}
			try
			{
				if (base.Columns.Count > 1)
				{
					base.Columns[1].Width = (flag ? Convert.ToInt32(Math.Floor(110 * num2 / d)) : 110);
					base.Columns[2].Width = (flag ? Convert.ToInt32(Math.Floor(60 * num2 / d)) : 60);
					base.Columns[3].Width = (flag ? Convert.ToInt32(Math.Floor(50 * num2 / d)) : 50);
					base.Columns[4].Width = (flag ? Convert.ToInt32(Math.Floor(50 * num2 / d)) : 50);
					base.Columns[9].Width = (flag ? Convert.ToInt32(Math.Floor(80 * num2 / d)) : 80);
					base.Columns[13].Width = (flag ? Convert.ToInt32(Math.Floor(80 * num2 / d)) : 80);
				}
			}
			catch (Exception exception_2)
			{
				Class182.smethod_0(exception_2);
			}
		}

		// Token: 0x04000B14 RID: 2836
		[CompilerGenerated]
		private EventHandler eventHandler_0;
	}
}
