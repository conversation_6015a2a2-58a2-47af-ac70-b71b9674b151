﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns14;
using ns32;
using TEx;

namespace ns11
{
	// Token: 0x02000233 RID: 563
	internal sealed class HotKeyEditCtrl : UserControl
	{
		// Token: 0x0600177E RID: 6014 RVA: 0x0009DDF0 File Offset: 0x0009BFF0
		public HotKeyEditCtrl()
		{
			this.InitializeComponent();
			this.txtHotKey.KeyDown += this.txtHotKey_KeyDown;
			this.txtHotKey.KeyPress += this.txtHotKey_KeyPress;
			this.txtHotKey.KeyUp += this.txtHotKey_KeyUp;
			this.txtHotKey.LostFocus += this.txtHotKey_LostFocus;
		}

		// Token: 0x0600177F RID: 6015 RVA: 0x0009DE68 File Offset: 0x0009C068
		public HotKeyEditCtrl(Class278 class278_1) : this()
		{
			this.HotKey = class278_1;
			this.label_name.Text = this.HotKey.CnName;
			this.txtHotKey.Text = ((this.HotKey.KeyModifier == KeyModifiers.None) ? "" : (this.HotKey.KeyModifier.ToString().Replace("_", " + ") + " + ")) + this.method_1(this.HotKey.Key);
			this.Modifier = this.HotKey.KeyModifier;
			this.Key = this.HotKey.Key;
		}

		// Token: 0x06001780 RID: 6016 RVA: 0x000098D9 File Offset: 0x00007AD9
		public HotKeyEditCtrl(Class264 class264_1) : this()
		{
			this.FnKey = class264_1;
			this.label_name.Text = this.FnKey.CnName;
			this.txtHotKey.Text = this.FnKey.KeyStr;
		}

		// Token: 0x170003DC RID: 988
		// (get) Token: 0x06001781 RID: 6017 RVA: 0x0009DF24 File Offset: 0x0009C124
		public TextBox InputBox
		{
			get
			{
				return this.txtHotKey;
			}
		}

		// Token: 0x170003DD RID: 989
		// (get) Token: 0x06001782 RID: 6018 RVA: 0x0009DF3C File Offset: 0x0009C13C
		// (set) Token: 0x06001783 RID: 6019 RVA: 0x00009916 File Offset: 0x00007B16
		public Class278 HotKey
		{
			get
			{
				return this.class278_0;
			}
			set
			{
				this.class278_0 = value;
			}
		}

		// Token: 0x170003DE RID: 990
		// (get) Token: 0x06001784 RID: 6020 RVA: 0x0009DF54 File Offset: 0x0009C154
		// (set) Token: 0x06001785 RID: 6021 RVA: 0x00009921 File Offset: 0x00007B21
		public Class264 FnKey
		{
			get
			{
				return this.class264_0;
			}
			set
			{
				this.class264_0 = value;
			}
		}

		// Token: 0x170003DF RID: 991
		// (get) Token: 0x06001786 RID: 6022 RVA: 0x0009DF6C File Offset: 0x0009C16C
		// (set) Token: 0x06001787 RID: 6023 RVA: 0x0000992C File Offset: 0x00007B2C
		public KeyModifiers Modifier
		{
			get
			{
				return this.keyModifiers_0;
			}
			set
			{
				this.keyModifiers_0 = value;
			}
		}

		// Token: 0x170003E0 RID: 992
		// (get) Token: 0x06001788 RID: 6024 RVA: 0x0009DF84 File Offset: 0x0009C184
		// (set) Token: 0x06001789 RID: 6025 RVA: 0x00009937 File Offset: 0x00007B37
		public Keys Key
		{
			get
			{
				return this.keys_0;
			}
			set
			{
				this.keys_0 = value;
			}
		}

		// Token: 0x170003E1 RID: 993
		// (get) Token: 0x0600178A RID: 6026 RVA: 0x0009DF9C File Offset: 0x0009C19C
		public string FnKeyStr
		{
			get
			{
				string result;
				if (this.IsFnKey)
				{
					result = this.InputBox.Text.Trim();
				}
				else
				{
					result = string.Empty;
				}
				return result;
			}
		}

		// Token: 0x170003E2 RID: 994
		// (get) Token: 0x0600178B RID: 6027 RVA: 0x0009DFD0 File Offset: 0x0009C1D0
		public bool IsHotKey
		{
			get
			{
				return this.HotKey != null;
			}
		}

		// Token: 0x170003E3 RID: 995
		// (get) Token: 0x0600178C RID: 6028 RVA: 0x0009DFEC File Offset: 0x0009C1EC
		public bool IsFnKey
		{
			get
			{
				return this.FnKey != null;
			}
		}

		// Token: 0x0600178D RID: 6029 RVA: 0x0009E008 File Offset: 0x0009C208
		private void txtHotKey_KeyDown(object sender, KeyEventArgs e)
		{
			if (this.IsHotKey)
			{
				int num = 0;
				string text = "";
				e.SuppressKeyPress = false;
				e.Handled = true;
				if (e.Modifiers != Keys.None)
				{
					Keys modifiers = e.Modifiers;
					if (modifiers <= (Keys.Shift | Keys.Control))
					{
						if (modifiers != Keys.Shift)
						{
							if (modifiers != Keys.Control)
							{
								if (modifiers == (Keys.Shift | Keys.Control))
								{
									text += "Ctrl + Shift + ";
									num = (int)e.Modifiers;
									this.keyModifiers_0 = KeyModifiers.Ctrl_Shift;
								}
							}
							else
							{
								text += "Ctrl + ";
								num = (int)e.Modifiers;
								this.keyModifiers_0 = KeyModifiers.Ctrl;
							}
						}
						else
						{
							text += "Shift + ";
							num = (int)e.Modifiers;
							this.keyModifiers_0 = KeyModifiers.Shift;
						}
					}
					else if (modifiers <= (Keys.Shift | Keys.Alt))
					{
						if (modifiers != Keys.Alt)
						{
							if (modifiers == (Keys.Shift | Keys.Alt))
							{
								text += "Alt + Shift + ";
								num = (int)e.Modifiers;
								this.keyModifiers_0 = KeyModifiers.Alt_Shift;
							}
						}
						else
						{
							text += "Alt + ";
							num = (int)e.Modifiers;
							this.keyModifiers_0 = KeyModifiers.Alt;
						}
					}
					else if (modifiers != (Keys.Control | Keys.Alt))
					{
						if (modifiers == (Keys.Shift | Keys.Control | Keys.Alt))
						{
							text += "Ctrl + Alt + Shift + ";
							num = (int)e.Modifiers;
							this.keyModifiers_0 = KeyModifiers.Ctrl_Alt_Shift;
						}
					}
					else
					{
						text += "Ctrl + Alt + ";
						num = (int)e.Modifiers;
						this.keyModifiers_0 = KeyModifiers.Ctrl_Alt;
					}
					if (e.KeyCode != Keys.None && e.KeyCode != Keys.ControlKey && e.KeyCode != Keys.Menu && e.KeyCode != Keys.ShiftKey && e.KeyCode != Keys.ProcessKey)
					{
						text += this.method_1(e.KeyCode);
						num = (int)(num + e.KeyCode);
						this.keys_0 = e.KeyCode;
					}
					else
					{
						this.keyModifiers_0 = KeyModifiers.None;
						this.keys_0 = Keys.None;
					}
				}
				else
				{
					if (e.KeyCode != Keys.Delete && e.KeyCode != Keys.Back && e.KeyCode != Keys.None && e.KeyCode != Keys.Left && e.KeyCode != Keys.Right && e.KeyCode != Keys.Up && e.KeyCode != Keys.Down && e.KeyCode != Keys.Escape)
					{
						if (e.KeyCode != Keys.ProcessKey)
						{
							text = this.method_1(e.KeyCode);
							num = (int)e.KeyCode;
							this.keys_0 = e.KeyCode;
							goto IL_252;
						}
					}
					text = string.Empty;
					num = -1;
					this.keys_0 = Keys.None;
					IL_252:
					this.keyModifiers_0 = KeyModifiers.None;
				}
				if (num == 0)
				{
					num = -1;
				}
				TextBox textBox = (TextBox)sender;
				if (!string.IsNullOrEmpty(text))
				{
					textBox.Text = text;
				}
				textBox.Tag = num;
				textBox.SelectionStart = textBox.Text.Length;
			}
			else
			{
				e.SuppressKeyPress = false;
				e.Handled = false;
			}
		}

		// Token: 0x0600178E RID: 6030 RVA: 0x00009942 File Offset: 0x00007B42
		private void txtHotKey_KeyPress(object sender, KeyPressEventArgs e)
		{
			if (this.IsHotKey)
			{
				e.Handled = true;
			}
			else if (!char.IsControl(e.KeyChar) && !char.IsLetterOrDigit(e.KeyChar))
			{
				e.Handled = true;
			}
		}

		// Token: 0x0600178F RID: 6031 RVA: 0x00009978 File Offset: 0x00007B78
		private void txtHotKey_KeyUp(object sender, KeyEventArgs e)
		{
			if (this.IsHotKey)
			{
				this.method_0(sender);
			}
		}

		// Token: 0x06001790 RID: 6032 RVA: 0x00009978 File Offset: 0x00007B78
		private void txtHotKey_LostFocus(object sender, EventArgs e)
		{
			if (this.IsHotKey)
			{
				this.method_0(sender);
			}
		}

		// Token: 0x06001791 RID: 6033 RVA: 0x0009E2B8 File Offset: 0x0009C4B8
		private void method_0(object object_0)
		{
			TextBox textBox = (TextBox)object_0;
			if (textBox.Text.EndsWith(" + ") || string.IsNullOrEmpty(textBox.Text))
			{
				textBox.Text = "";
				textBox.Tag = -1;
				textBox.SelectionStart = textBox.Text.Length;
				this.keyModifiers_0 = KeyModifiers.None;
				this.keys_0 = Keys.None;
			}
		}

		// Token: 0x06001792 RID: 6034 RVA: 0x0009E324 File Offset: 0x0009C524
		private string method_1(Keys keys_1)
		{
			string result;
			if (keys_1 >= Keys.D0 && keys_1 <= Keys.D9)
			{
				result = keys_1.ToString().Remove(0, 1);
			}
			else if (keys_1 >= Keys.NumPad0 && keys_1 <= Keys.NumPad9)
			{
				result = keys_1.ToString().Replace("Pad", "");
			}
			else
			{
				string text;
				if (keys_1 == Keys.OemMinus)
				{
					text = "-";
				}
				else if (keys_1 == Keys.Oemplus)
				{
					text = "=";
				}
				else if (keys_1 == Keys.OemOpenBrackets)
				{
					text = "[";
				}
				else if (keys_1 == Keys.OemCloseBrackets)
				{
					text = "]";
				}
				else if (keys_1 == Keys.OemPipe)
				{
					text = "\\";
				}
				else if (keys_1 == Keys.OemSemicolon)
				{
					text = ";";
				}
				else if (keys_1 == Keys.OemQuotes)
				{
					text = "'";
				}
				else if (keys_1 == Keys.Oemcomma)
				{
					text = ",";
				}
				else if (keys_1 == Keys.OemPeriod)
				{
					text = ".";
				}
				else if (keys_1 == Keys.OemQuestion)
				{
					text = "/";
				}
				else if (keys_1 == Keys.Oemtilde)
				{
					text = "`";
				}
				else if (keys_1 == Keys.Next)
				{
					text = "PageDn";
				}
				else if (keys_1 == Keys.None)
				{
					text = "";
				}
				else
				{
					text = keys_1.ToString();
				}
				result = text;
			}
			return result;
		}

		// Token: 0x06001793 RID: 6035 RVA: 0x0000998B File Offset: 0x00007B8B
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001794 RID: 6036 RVA: 0x0009E46C File Offset: 0x0009C66C
		private void InitializeComponent()
		{
			this.label_name = new Label();
			this.txtHotKey = new TextBox();
			base.SuspendLayout();
			this.label_name.Location = new Point(7, 2);
			this.label_name.Name = "label_name";
			this.label_name.Size = new Size(181, 23);
			this.label_name.TabIndex = 0;
			this.label_name.Text = "label1";
			this.label_name.TextAlign = ContentAlignment.MiddleLeft;
			this.txtHotKey.Location = new Point(194, 1);
			this.txtHotKey.Name = "txtHotKey";
			this.txtHotKey.Size = new Size(221, 25);
			this.txtHotKey.TabIndex = 1;
			this.txtHotKey.TextAlign = HorizontalAlignment.Center;
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.txtHotKey);
			base.Controls.Add(this.label_name);
			base.Name = "HotKeyEditCtrl";
			base.Size = new Size(418, 27);
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000BEA RID: 3050
		private Class278 class278_0;

		// Token: 0x04000BEB RID: 3051
		private Class264 class264_0;

		// Token: 0x04000BEC RID: 3052
		private KeyModifiers keyModifiers_0;

		// Token: 0x04000BED RID: 3053
		private Keys keys_0;

		// Token: 0x04000BEE RID: 3054
		private IContainer icontainer_0;

		// Token: 0x04000BEF RID: 3055
		private Label label_name;

		// Token: 0x04000BF0 RID: 3056
		private TextBox txtHotKey;
	}
}
