﻿using System;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using ns11;
using ns15;
using ns17;
using ns18;
using ns22;

namespace SmartAssembly.Shared.ReportHelper
{
	// Token: 0x020003C7 RID: 967
	public static class OsInformation
	{
		// Token: 0x060026DE RID: 9950 RVA: 0x0000EE62 File Offset: 0x0000D062
		public static string smethod_0(Version version_0)
		{
			return OsInformation.smethod_1(version_0);
		}

		// Token: 0x060026DF RID: 9951 RVA: 0x000FC620 File Offset: 0x000FA820
		private static string smethod_1(Version version_0)
		{
			bool flag = OsVersionInformation.smethod_0();
			bool isX = OsVersionInformation.IsX64;
			int major = version_0.Major;
			if (major != 5)
			{
				if (major != 6)
				{
					if (major == 10)
					{
						if (version_0.Minor == 0)
						{
							if (flag)
							{
								return "Windows 10";
							}
							if (version_0.Build < 17623)
							{
								return "Windows Server 2016";
							}
							return "Windows Server 2019";
						}
					}
				}
				else
				{
					switch (version_0.Minor)
					{
					case 0:
						if (!flag)
						{
							return "Windows Server 2008";
						}
						return "Windows Vista";
					case 1:
						if (!flag)
						{
							return "Windows Server 2008 R2";
						}
						return "Windows 7";
					case 2:
						if (!flag)
						{
							return "Windows Server 2012";
						}
						return "Windows 8";
					case 3:
						if (!flag)
						{
							return "Windows Server 2012 R2";
						}
						return "Windows 8.1";
					case 4:
						return "Windows 10";
					}
				}
			}
			else
			{
				switch (version_0.Minor)
				{
				case 0:
					return "Windows 2000";
				case 1:
					return "Windows XP";
				case 2:
					if (!flag || !isX)
					{
						return "Windows Server 2003";
					}
					return "Windows XP 64-Bit Edition";
				}
			}
			return string.Format("Windows {0}.{1}.{2}.{3}", new object[]
			{
				version_0.Major,
				version_0.Minor,
				version_0.Build,
				version_0.Revision
			});
		}

		// Token: 0x060026E0 RID: 9952 RVA: 0x0000EE6A File Offset: 0x0000D06A
		public static Version smethod_2(Version version_0)
		{
			return OsInformation.smethod_6(OsInformation.smethod_5(), version_0);
		}

		// Token: 0x060026E1 RID: 9953 RVA: 0x0000EE77 File Offset: 0x0000D077
		public static void smethod_3(out Enum31 enum31_0, ref Version version_0, ref Version version_1, ref string string_0, out string string_1, out bool bool_0)
		{
			enum31_0 = OsInformation.smethod_5();
			version_1 = OsInformation.smethod_6(enum31_0, version_1);
			string_1 = OsInformation.smethod_4(version_1);
			bool_0 = OsVersionInformation.IsX64;
		}

		// Token: 0x060026E2 RID: 9954 RVA: 0x000FC768 File Offset: 0x000FA968
		public static string smethod_4(Version version_0)
		{
			return string.Format("Microsoft Windows {0}.{1}.{2}.{3}", new object[]
			{
				version_0.Major,
				version_0.Minor,
				version_0.Build,
				version_0.Revision
			});
		}

		// Token: 0x060026E3 RID: 9955 RVA: 0x0000EE9D File Offset: 0x0000D09D
		private static Enum31 smethod_5()
		{
			return Enum31.const_0;
		}

		// Token: 0x060026E4 RID: 9956 RVA: 0x0000EEA0 File Offset: 0x0000D0A0
		private static Version smethod_6(Enum31 enum31_0, Version version_0)
		{
			return OsInformation.smethod_7(version_0);
		}

		// Token: 0x060026E5 RID: 9957 RVA: 0x000FC7C0 File Offset: 0x000FA9C0
		private static Version smethod_7(Version version_0)
		{
			try
			{
				if ((version_0.Major >= 6 && version_0.Minor >= 2) || (version_0.Major == 0 && version_0.Minor == 0))
				{
					Enum33 @enum;
					using (Class513 @class = Class515.smethod_0(Class512.uintptr_2, Enum34.const_0, Enum32.const_0, "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion", out @enum) ?? Class515.smethod_0(Class512.uintptr_2, Enum34.const_0, Enum32.const_0, "SOFTWARE\\WOW6432Node\\Microsoft\\Windows NT\\CurrentVersion", out @enum))
					{
						if (@enum == Enum33.const_2)
						{
							int major = 0;
							int minor = 0;
							int build = 0;
							int revision = 0;
							object obj;
							object obj2;
							object obj3;
							object obj4;
							string string_;
							if (@class.vmethod_1("CurrentMajorVersionNumber", out obj) && @class.vmethod_1("CurrentMinorVersionNumber", out obj2))
							{
								if ((obj3 = obj) is int)
								{
									int num = (int)obj3;
									major = num;
								}
								if ((obj3 = obj2) is int)
								{
									int num2 = (int)obj3;
									minor = num2;
								}
							}
							else if (@class.vmethod_1("CurrentVersion", out obj4) && (string_ = (obj4 as string)) != null)
							{
								Version version = OsInformation.smethod_8(string_);
								major = version.Major;
								minor = version.Minor;
							}
							object obj5;
							string string_2;
							if (@class.vmethod_1("CurrentBuildNumber", out obj5) && (string_2 = (obj5 as string)) != null)
							{
								build = OsInformation.smethod_8(string_2).Major;
							}
							object obj6;
							if (@class.vmethod_1("UBR", out obj6) && (obj3 = obj6) is int)
							{
								int num3 = (int)obj3;
								revision = num3;
							}
							return new Version(major, minor, build, revision);
						}
					}
				}
			}
			catch
			{
			}
			return version_0;
		}

		// Token: 0x060026E6 RID: 9958 RVA: 0x000FC960 File Offset: 0x000FAB60
		private static Version smethod_8(string string_0)
		{
			OsInformation.Struct18 @struct;
			@struct.string_0 = string_0;
			@struct.int_0 = 0;
			int[] array = new int[4];
			for (int i = 0; i < 4; i++)
			{
				OsInformation.smethod_9(ref @struct);
				int num = OsInformation.smethod_10(ref @struct);
				if (num == 0)
				{
					break;
				}
				int num2;
				if (int.TryParse(@struct.string_0.Substring(@struct.int_0, num), out num2))
				{
					array[i] = num2;
				}
				@struct.int_0 += num;
			}
			return new Version(array[0], array[1], array[2], array[3]);
		}

		// Token: 0x060026E7 RID: 9959 RVA: 0x000FC9E4 File Offset: 0x000FABE4
		[CompilerGenerated]
		internal static void smethod_9(ref OsInformation.Struct18 struct18_0)
		{
			while (struct18_0.int_0 < struct18_0.string_0.Length && !char.IsNumber(struct18_0.string_0[struct18_0.int_0]))
			{
				int int_ = struct18_0.int_0;
				struct18_0.int_0 = int_ + 1;
			}
		}

		// Token: 0x060026E8 RID: 9960 RVA: 0x000FCA30 File Offset: 0x000FAC30
		[CompilerGenerated]
		internal static int smethod_10(ref OsInformation.Struct18 struct18_0)
		{
			int num = struct18_0.int_0;
			while (num < struct18_0.string_0.Length && char.IsNumber(struct18_0.string_0[num]))
			{
				num++;
			}
			return num - struct18_0.int_0;
		}

		// Token: 0x020003C8 RID: 968
		[CompilerGenerated]
		[StructLayout(LayoutKind.Auto)]
		private struct Struct18
		{
			// Token: 0x040012C3 RID: 4803
			public string string_0;

			// Token: 0x040012C4 RID: 4804
			public int int_0;
		}
	}
}
