﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns11;
using ns22;
using ns24;

namespace ns3
{
	// Token: 0x02000097 RID: 151
	internal sealed partial class SetNPeriodWnd : Form
	{
		// Token: 0x14000021 RID: 33
		// (add) Token: 0x06000503 RID: 1283 RVA: 0x000274AC File Offset: 0x000256AC
		// (remove) Token: 0x06000504 RID: 1284 RVA: 0x000274E4 File Offset: 0x000256E4
		internal event Delegate2 NPeriodSet
		{
			[CompilerGenerated]
			add
			{
				Delegate2 @delegate = this.delegate2_0;
				Delegate2 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate2 value2 = (Delegate2)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate2>(ref this.delegate2_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate2 @delegate = this.delegate2_0;
				Delegate2 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate2 value2 = (Delegate2)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate2>(ref this.delegate2_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06000505 RID: 1285 RVA: 0x0002751C File Offset: 0x0002571C
		protected void method_0(EventArgs4 eventArgs4_0)
		{
			Delegate2 @delegate = this.delegate2_0;
			if (@delegate != null)
			{
				@delegate(eventArgs4_0);
			}
		}

		// Token: 0x06000506 RID: 1286 RVA: 0x000043DA File Offset: 0x000025DA
		public SetNPeriodWnd(Enum4 enum4_1) : this(enum4_1, 10)
		{
		}

		// Token: 0x06000507 RID: 1287 RVA: 0x0002753C File Offset: 0x0002573C
		public SetNPeriodWnd(Enum4 enum4_1, int int_0)
		{
			this.InitializeComponent();
			this.enum4_0 = enum4_1;
			this.numericUpDown1.Minimum = 1m;
			this.numericUpDown1.Maximum = 500m;
			this.numericUpDown1.Value = int_0;
			switch (enum4_1)
			{
			case Enum4.const_0:
				this.label_notice.Text = "请输入分钟数：";
				break;
			case Enum4.const_1:
				this.label_notice.Text = "请输入小时数：";
				break;
			case Enum4.const_2:
				this.label_notice.Text = "请输入天数：";
				break;
			}
			this.button_OK.Click += this.button_OK_Click;
			base.Shown += this.SetNPeriodWnd_Shown;
		}

		// Token: 0x06000508 RID: 1288 RVA: 0x000043E5 File Offset: 0x000025E5
		private void SetNPeriodWnd_Shown(object sender, EventArgs e)
		{
			this.numericUpDown1.Select(0, this.numericUpDown1.Text.Length);
		}

		// Token: 0x06000509 RID: 1289 RVA: 0x00027608 File Offset: 0x00025808
		private void button_OK_Click(object sender, EventArgs e)
		{
			EventArgs4 eventArgs4_ = new EventArgs4(this.enum4_0, Convert.ToInt32(this.numericUpDown1.Value));
			this.method_0(eventArgs4_);
			base.Close();
		}

		// Token: 0x0600050A RID: 1290 RVA: 0x00004405 File Offset: 0x00002605
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000212 RID: 530
		[CompilerGenerated]
		private Delegate2 delegate2_0;

		// Token: 0x04000213 RID: 531
		private Enum4 enum4_0;

		// Token: 0x04000214 RID: 532
		private IContainer icontainer_0;
	}
}
