﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using ns15;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000073 RID: 115
	[Serializable]
	internal sealed class DrawLineDExt : DrawLine, ISerializable
	{
		// Token: 0x06000428 RID: 1064 RVA: 0x00003C88 File Offset: 0x00001E88
		public DrawLineDExt()
		{
		}

		// Token: 0x06000429 RID: 1065 RVA: 0x00003C90 File Offset: 0x00001E90
		public DrawLineDExt(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = "射线";
			base.IsOneClickLoc = false;
		}

		// Token: 0x0600042A RID: 1066 RVA: 0x00003CB3 File Offset: 0x00001EB3
		protected DrawLineDExt(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x0600042B RID: 1067 RVA: 0x00003CC4 File Offset: 0x00001EC4
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x0600042C RID: 1068 RVA: 0x00022C54 File Offset: 0x00020E54
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			Class58 @class = DrawLine.smethod_0(chartCS_1, double_1, double_2, double_3, double_4);
			LineObj item = base.method_23(double_1, double_2, @class.X2, @class.Y2, string_5);
			list.Add(item);
			return list;
		}
	}
}
