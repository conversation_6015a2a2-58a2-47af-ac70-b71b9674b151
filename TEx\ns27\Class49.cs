﻿using System;
using System.Drawing;
using ns11;
using ns28;

namespace ns27
{
	// Token: 0x02000091 RID: 145
	internal sealed class Class49 : Class48
	{
		// Token: 0x060004C1 RID: 1217 RVA: 0x0000417E File Offset: 0x0000237E
		public Class49()
		{
			base.NormalImage = Class372.check;
			base.Image = base.NormalImage;
			base.Size = new Size(35, 35);
		}

		// Token: 0x060004C2 RID: 1218 RVA: 0x000041AE File Offset: 0x000023AE
		protected override void Class48_Click(object sender, EventArgs e)
		{
		}
	}
}
