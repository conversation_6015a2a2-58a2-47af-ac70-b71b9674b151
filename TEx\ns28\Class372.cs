﻿using System;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

namespace ns28
{
	// Token: 0x020002CB RID: 715
	[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
	[DebuggerNonUserCode]
	[CompilerGenerated]
	internal sealed class Class372
	{
		// Token: 0x06001F4E RID: 8014 RVA: 0x00002D25 File Offset: 0x00000F25
		internal Class372()
		{
		}

		// Token: 0x170004D6 RID: 1238
		// (get) Token: 0x06001F4F RID: 8015 RVA: 0x000DA274 File Offset: 0x000D8474
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static ResourceManager ResourceManager
		{
			get
			{
				if (Class372.resourceManager_0 == null)
				{
					Class372.resourceManager_0 = new ResourceManager("ns28.Class372", typeof(Class372).Assembly);
				}
				return Class372.resourceManager_0;
			}
		}

		// Token: 0x170004D7 RID: 1239
		// (get) Token: 0x06001F50 RID: 8016 RVA: 0x000DA2B0 File Offset: 0x000D84B0
		// (set) Token: 0x06001F51 RID: 8017 RVA: 0x0000D0A9 File Offset: 0x0000B2A9
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static CultureInfo Culture
		{
			get
			{
				return Class372.cultureInfo_0;
			}
			set
			{
				Class372.cultureInfo_0 = value;
			}
		}

		// Token: 0x170004D8 RID: 1240
		// (get) Token: 0x06001F52 RID: 8018 RVA: 0x000DA2C8 File Offset: 0x000D84C8
		internal static Bitmap _1683_Lightbulb_32x32
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("_1683_Lightbulb_32x32", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004D9 RID: 1241
		// (get) Token: 0x06001F53 RID: 8019 RVA: 0x000DA2F4 File Offset: 0x000D84F4
		internal static Bitmap _45DgreeDn
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("_45DgreeDn", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004DA RID: 1242
		// (get) Token: 0x06001F54 RID: 8020 RVA: 0x000DA320 File Offset: 0x000D8520
		internal static Bitmap _45DgreeUp
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("_45DgreeUp", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004DB RID: 1243
		// (get) Token: 0x06001F55 RID: 8021 RVA: 0x000DA34C File Offset: 0x000D854C
		internal static Bitmap aboutBanner
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("aboutBanner", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004DC RID: 1244
		// (get) Token: 0x06001F56 RID: 8022 RVA: 0x000DA378 File Offset: 0x000D8578
		internal static Bitmap aboutBanner_TExLY
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("aboutBanner_TExLY", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004DD RID: 1245
		// (get) Token: 0x06001F57 RID: 8023 RVA: 0x000DA3A4 File Offset: 0x000D85A4
		internal static Bitmap add_16x16
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("add_16x16", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004DE RID: 1246
		// (get) Token: 0x06001F58 RID: 8024 RVA: 0x000DA3D0 File Offset: 0x000D85D0
		internal static Bitmap AddFile_16x16
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("AddFile_16x16", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004DF RID: 1247
		// (get) Token: 0x06001F59 RID: 8025 RVA: 0x000DA3FC File Offset: 0x000D85FC
		internal static Bitmap application_side_expand
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("application_side_expand", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004E0 RID: 1248
		// (get) Token: 0x06001F5A RID: 8026 RVA: 0x000DA428 File Offset: 0x000D8628
		internal static Bitmap application_split
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("application_split", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004E1 RID: 1249
		// (get) Token: 0x06001F5B RID: 8027 RVA: 0x000DA454 File Offset: 0x000D8654
		internal static Bitmap Arrow
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("Arrow", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004E2 RID: 1250
		// (get) Token: 0x06001F5C RID: 8028 RVA: 0x000DA480 File Offset: 0x000D8680
		internal static Bitmap autolimit
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("autolimit", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004E3 RID: 1251
		// (get) Token: 0x06001F5D RID: 8029 RVA: 0x000DA4AC File Offset: 0x000D86AC
		internal static Bitmap autolimit_gray
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("autolimit_gray", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004E4 RID: 1252
		// (get) Token: 0x06001F5E RID: 8030 RVA: 0x000DA4D8 File Offset: 0x000D86D8
		internal static Bitmap autostop
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("autostop", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004E5 RID: 1253
		// (get) Token: 0x06001F5F RID: 8031 RVA: 0x000DA504 File Offset: 0x000D8704
		internal static Bitmap autostop_gray
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("autostop_gray", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004E6 RID: 1254
		// (get) Token: 0x06001F60 RID: 8032 RVA: 0x000DA530 File Offset: 0x000D8730
		internal static Bitmap biArrow
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("biArrow", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004E7 RID: 1255
		// (get) Token: 0x06001F61 RID: 8033 RVA: 0x000DA55C File Offset: 0x000D875C
		internal static Bitmap BigArrwSamples
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("BigArrwSamples", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004E8 RID: 1256
		// (get) Token: 0x06001F62 RID: 8034 RVA: 0x000DA588 File Offset: 0x000D8788
		internal static Bitmap BlindTest_48x48
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("BlindTest_48x48", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004E9 RID: 1257
		// (get) Token: 0x06001F63 RID: 8035 RVA: 0x000DA5B4 File Offset: 0x000D87B4
		internal static Icon BlindTestIcon
		{
			get
			{
				return (Icon)Class372.ResourceManager.GetObject("BlindTestIcon", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004EA RID: 1258
		// (get) Token: 0x06001F64 RID: 8036 RVA: 0x000DA5E0 File Offset: 0x000D87E0
		internal static Bitmap blueline
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("blueline", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004EB RID: 1259
		// (get) Token: 0x06001F65 RID: 8037 RVA: 0x000DA60C File Offset: 0x000D880C
		internal static Bitmap Book_angleHS
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("Book_angleHS", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004EC RID: 1260
		// (get) Token: 0x06001F66 RID: 8038 RVA: 0x000DA638 File Offset: 0x000D8838
		internal static Bitmap Book_openHS
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("Book_openHS", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004ED RID: 1261
		// (get) Token: 0x06001F67 RID: 8039 RVA: 0x000DA664 File Offset: 0x000D8864
		internal static Icon BookIcon
		{
			get
			{
				return (Icon)Class372.ResourceManager.GetObject("BookIcon", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004EE RID: 1262
		// (get) Token: 0x06001F68 RID: 8040 RVA: 0x000DA690 File Offset: 0x000D8890
		internal static Bitmap BTN_Thumb
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("BTN_Thumb", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004EF RID: 1263
		// (get) Token: 0x06001F69 RID: 8041 RVA: 0x000DA6BC File Offset: 0x000D88BC
		internal static Bitmap BTN_Thumb_Blue
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("BTN_Thumb_Blue", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004F0 RID: 1264
		// (get) Token: 0x06001F6A RID: 8042 RVA: 0x000DA6E8 File Offset: 0x000D88E8
		internal static Bitmap calendar
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("calendar", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004F1 RID: 1265
		// (get) Token: 0x06001F6B RID: 8043 RVA: 0x000DA714 File Offset: 0x000D8914
		internal static Bitmap calendar_date
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("calendar_date", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004F2 RID: 1266
		// (get) Token: 0x06001F6C RID: 8044 RVA: 0x000DA740 File Offset: 0x000D8940
		internal static Bitmap calendar_date_16x16
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("calendar_date_16x16", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004F3 RID: 1267
		// (get) Token: 0x06001F6D RID: 8045 RVA: 0x000DA76C File Offset: 0x000D896C
		internal static Bitmap Calendar_scheduleHS
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("Calendar_scheduleHS", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004F4 RID: 1268
		// (get) Token: 0x06001F6E RID: 8046 RVA: 0x000DA798 File Offset: 0x000D8998
		internal static Bitmap chart
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("chart", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004F5 RID: 1269
		// (get) Token: 0x06001F6F RID: 8047 RVA: 0x000DA7C4 File Offset: 0x000D89C4
		internal static Bitmap check
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("check", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004F6 RID: 1270
		// (get) Token: 0x06001F70 RID: 8048 RVA: 0x000DA7F0 File Offset: 0x000D89F0
		internal static byte[] chtpg
		{
			get
			{
				return (byte[])Class372.ResourceManager.GetObject("chtpg", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004F7 RID: 1271
		// (get) Token: 0x06001F71 RID: 8049 RVA: 0x000DA81C File Offset: 0x000D8A1C
		internal static Bitmap CloseLongArrow_Green
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("CloseLongArrow_Green", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004F8 RID: 1272
		// (get) Token: 0x06001F72 RID: 8050 RVA: 0x000DA848 File Offset: 0x000D8A48
		internal static Bitmap CloseLongArrow_GreenLt
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("CloseLongArrow_GreenLt", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004F9 RID: 1273
		// (get) Token: 0x06001F73 RID: 8051 RVA: 0x000DA874 File Offset: 0x000D8A74
		internal static Bitmap CloseShortArrow_Red
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("CloseShortArrow_Red", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004FA RID: 1274
		// (get) Token: 0x06001F74 RID: 8052 RVA: 0x000DA8A0 File Offset: 0x000D8AA0
		internal static Bitmap CloseShortArrow_RedLt
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("CloseShortArrow_RedLt", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004FB RID: 1275
		// (get) Token: 0x06001F75 RID: 8053 RVA: 0x000DA8CC File Offset: 0x000D8ACC
		internal static Bitmap CloudStorage
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("CloudStorage", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004FC RID: 1276
		// (get) Token: 0x06001F76 RID: 8054 RVA: 0x000DA8F8 File Offset: 0x000D8AF8
		internal static Bitmap computer_accept
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("computer_accept", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004FD RID: 1277
		// (get) Token: 0x06001F77 RID: 8055 RVA: 0x000DA924 File Offset: 0x000D8B24
		internal static Bitmap cross_gray
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("cross_gray", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004FE RID: 1278
		// (get) Token: 0x06001F78 RID: 8056 RVA: 0x000DA950 File Offset: 0x000D8B50
		internal static Bitmap cross_lightgray
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("cross_lightgray", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170004FF RID: 1279
		// (get) Token: 0x06001F79 RID: 8057 RVA: 0x000DA97C File Offset: 0x000D8B7C
		internal static Bitmap database_down
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("database_down", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000500 RID: 1280
		// (get) Token: 0x06001F7A RID: 8058 RVA: 0x000DA9A8 File Offset: 0x000D8BA8
		internal static Bitmap DelDraw
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("DelDraw", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000501 RID: 1281
		// (get) Token: 0x06001F7B RID: 8059 RVA: 0x000DA9D4 File Offset: 0x000D8BD4
		internal static Bitmap DeleteHS
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("DeleteHS", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000502 RID: 1282
		// (get) Token: 0x06001F7C RID: 8060 RVA: 0x000DAA00 File Offset: 0x000D8C00
		internal static Bitmap door_in
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("door_in", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000503 RID: 1283
		// (get) Token: 0x06001F7D RID: 8061 RVA: 0x000DAA2C File Offset: 0x000D8C2C
		internal static Bitmap DrawClsRevOpen
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("DrawClsRevOpen", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000504 RID: 1284
		// (get) Token: 0x06001F7E RID: 8062 RVA: 0x000DAA58 File Offset: 0x000D8C58
		internal static Bitmap DrawCondClose
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("DrawCondClose", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000505 RID: 1285
		// (get) Token: 0x06001F7F RID: 8063 RVA: 0x000DAA84 File Offset: 0x000D8C84
		internal static Bitmap DrawOdrLineBtnIcon
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("DrawOdrLineBtnIcon", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000506 RID: 1286
		// (get) Token: 0x06001F80 RID: 8064 RVA: 0x000DAAB0 File Offset: 0x000D8CB0
		internal static Bitmap DrawOdrLineBtnIconBlue
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("DrawOdrLineBtnIconBlue", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000507 RID: 1287
		// (get) Token: 0x06001F81 RID: 8065 RVA: 0x000DAADC File Offset: 0x000D8CDC
		internal static Bitmap DrawOpenLongOdr
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("DrawOpenLongOdr", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000508 RID: 1288
		// (get) Token: 0x06001F82 RID: 8066 RVA: 0x000DAB08 File Offset: 0x000D8D08
		internal static Bitmap DrawOpenShrtOdr
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("DrawOpenShrtOdr", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000509 RID: 1289
		// (get) Token: 0x06001F83 RID: 8067 RVA: 0x000DAB34 File Offset: 0x000D8D34
		internal static Bitmap DrawROpen
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("DrawROpen", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700050A RID: 1290
		// (get) Token: 0x06001F84 RID: 8068 RVA: 0x000DAB60 File Offset: 0x000D8D60
		internal static Bitmap DrawStopLoss
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("DrawStopLoss", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700050B RID: 1291
		// (get) Token: 0x06001F85 RID: 8069 RVA: 0x000DAB8C File Offset: 0x000D8D8C
		internal static Bitmap DrawStopPrft
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("DrawStopPrft", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700050C RID: 1292
		// (get) Token: 0x06001F86 RID: 8070 RVA: 0x000DABB8 File Offset: 0x000D8DB8
		internal static Bitmap EditHS
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("EditHS", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700050D RID: 1293
		// (get) Token: 0x06001F87 RID: 8071 RVA: 0x000DABE4 File Offset: 0x000D8DE4
		internal static Bitmap Ellipse
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("Ellipse", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700050E RID: 1294
		// (get) Token: 0x06001F88 RID: 8072 RVA: 0x000DAC10 File Offset: 0x000D8E10
		internal static Bitmap expand_blue1_28px
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("expand_blue1_28px", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700050F RID: 1295
		// (get) Token: 0x06001F89 RID: 8073 RVA: 0x000DAC3C File Offset: 0x000D8E3C
		internal static Bitmap expand_blue2_28px
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("expand_blue2_28px", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000510 RID: 1296
		// (get) Token: 0x06001F8A RID: 8074 RVA: 0x000DAC68 File Offset: 0x000D8E68
		internal static Bitmap fast_forward
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("fast_forward", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000511 RID: 1297
		// (get) Token: 0x06001F8B RID: 8075 RVA: 0x000DAC94 File Offset: 0x000D8E94
		internal static Bitmap fast_forward_red
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("fast_forward_red", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000512 RID: 1298
		// (get) Token: 0x06001F8C RID: 8076 RVA: 0x000DACC0 File Offset: 0x000D8EC0
		internal static Bitmap FbLines
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("FbLines", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000513 RID: 1299
		// (get) Token: 0x06001F8D RID: 8077 RVA: 0x000DACEC File Offset: 0x000D8EEC
		internal static Bitmap FbLines_Ext
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("FbLines_Ext", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000514 RID: 1300
		// (get) Token: 0x06001F8E RID: 8078 RVA: 0x000DAD18 File Offset: 0x000D8F18
		internal static Bitmap files
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("files", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000515 RID: 1301
		// (get) Token: 0x06001F8F RID: 8079 RVA: 0x000DAD44 File Offset: 0x000D8F44
		internal static Bitmap flash
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("flash", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000516 RID: 1302
		// (get) Token: 0x06001F90 RID: 8080 RVA: 0x000DAD70 File Offset: 0x000D8F70
		internal static Bitmap flash_16x16
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("flash_16x16", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000517 RID: 1303
		// (get) Token: 0x06001F91 RID: 8081 RVA: 0x000DAD9C File Offset: 0x000D8F9C
		internal static Bitmap floppy_disc
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("floppy_disc", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000518 RID: 1304
		// (get) Token: 0x06001F92 RID: 8082 RVA: 0x000DADC8 File Offset: 0x000D8FC8
		internal static Bitmap floppy_disc_16x16
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("floppy_disc_16x16", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000519 RID: 1305
		// (get) Token: 0x06001F93 RID: 8083 RVA: 0x000DADF4 File Offset: 0x000D8FF4
		internal static Bitmap folder_accept
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("folder_accept", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700051A RID: 1306
		// (get) Token: 0x06001F94 RID: 8084 RVA: 0x000DAE20 File Offset: 0x000D9020
		internal static Bitmap folder_add
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("folder_add", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700051B RID: 1307
		// (get) Token: 0x06001F95 RID: 8085 RVA: 0x000DAE4C File Offset: 0x000D904C
		internal static Bitmap folder_up_16x16
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("folder_up_16x16", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700051C RID: 1308
		// (get) Token: 0x06001F96 RID: 8086 RVA: 0x000DAE78 File Offset: 0x000D9078
		internal static Bitmap FolderClosed
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("FolderClosed", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700051D RID: 1309
		// (get) Token: 0x06001F97 RID: 8087 RVA: 0x000DAEA4 File Offset: 0x000D90A4
		internal static Bitmap FolderOpen
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("FolderOpen", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700051E RID: 1310
		// (get) Token: 0x06001F98 RID: 8088 RVA: 0x000DAED0 File Offset: 0x000D90D0
		internal static Bitmap GannFan
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("GannFan", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700051F RID: 1311
		// (get) Token: 0x06001F99 RID: 8089 RVA: 0x000DAEFC File Offset: 0x000D90FC
		internal static Bitmap GannLines
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("GannLines", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000520 RID: 1312
		// (get) Token: 0x06001F9A RID: 8090 RVA: 0x000DAF28 File Offset: 0x000D9128
		internal static Bitmap Glass_Face_48
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("Glass_Face_48", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000521 RID: 1313
		// (get) Token: 0x06001F9B RID: 8091 RVA: 0x000DAF54 File Offset: 0x000D9154
		internal static Bitmap GlassFace_18
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("GlassFace_18", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000522 RID: 1314
		// (get) Token: 0x06001F9C RID: 8092 RVA: 0x000DAF80 File Offset: 0x000D9180
		internal static Bitmap GlassFace_18_BW
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("GlassFace_18_BW", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000523 RID: 1315
		// (get) Token: 0x06001F9D RID: 8093 RVA: 0x000DAFAC File Offset: 0x000D91AC
		internal static Bitmap GoldenRatio
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("GoldenRatio", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000524 RID: 1316
		// (get) Token: 0x06001F9E RID: 8094 RVA: 0x000DAFD8 File Offset: 0x000D91D8
		internal static Bitmap GreenArrow_Down
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("GreenArrow_Down", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000525 RID: 1317
		// (get) Token: 0x06001F9F RID: 8095 RVA: 0x000DB004 File Offset: 0x000D9204
		internal static Bitmap GreenArrow_LDown
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("GreenArrow_LDown", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000526 RID: 1318
		// (get) Token: 0x06001FA0 RID: 8096 RVA: 0x000DB030 File Offset: 0x000D9230
		internal static Bitmap GreenArrow_RDown
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("GreenArrow_RDown", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000527 RID: 1319
		// (get) Token: 0x06001FA1 RID: 8097 RVA: 0x000DB05C File Offset: 0x000D925C
		internal static Bitmap GreenArrow8px
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("GreenArrow8px", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000528 RID: 1320
		// (get) Token: 0x06001FA2 RID: 8098 RVA: 0x000DB088 File Offset: 0x000D9288
		internal static Bitmap layout_add
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("layout_add", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000529 RID: 1321
		// (get) Token: 0x06001FA3 RID: 8099 RVA: 0x000DB0B4 File Offset: 0x000D92B4
		internal static Bitmap Line
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("Line", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700052A RID: 1322
		// (get) Token: 0x06001FA4 RID: 8100 RVA: 0x000DB0E0 File Offset: 0x000D92E0
		internal static Bitmap line_chart
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("line_chart", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700052B RID: 1323
		// (get) Token: 0x06001FA5 RID: 8101 RVA: 0x000DB10C File Offset: 0x000D930C
		internal static Bitmap line_chart_darkblack
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("line_chart_darkblack", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700052C RID: 1324
		// (get) Token: 0x06001FA6 RID: 8102 RVA: 0x000DB138 File Offset: 0x000D9338
		internal static Bitmap line_chart_lightgray
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("line_chart_lightgray", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700052D RID: 1325
		// (get) Token: 0x06001FA7 RID: 8103 RVA: 0x000DB164 File Offset: 0x000D9364
		internal static Bitmap LineD
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("LineD", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700052E RID: 1326
		// (get) Token: 0x06001FA8 RID: 8104 RVA: 0x000DB190 File Offset: 0x000D9390
		internal static Bitmap LineDExt
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("LineDExt", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700052F RID: 1327
		// (get) Token: 0x06001FA9 RID: 8105 RVA: 0x000DB1BC File Offset: 0x000D93BC
		internal static Bitmap LineDH
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("LineDH", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000530 RID: 1328
		// (get) Token: 0x06001FAA RID: 8106 RVA: 0x000DB1E8 File Offset: 0x000D93E8
		internal static Bitmap LineH
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("LineH", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000531 RID: 1329
		// (get) Token: 0x06001FAB RID: 8107 RVA: 0x000DB214 File Offset: 0x000D9414
		internal static Bitmap LineHExt
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("LineHExt", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000532 RID: 1330
		// (get) Token: 0x06001FAC RID: 8108 RVA: 0x000DB240 File Offset: 0x000D9440
		internal static Bitmap LineV
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("LineV", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000533 RID: 1331
		// (get) Token: 0x06001FAD RID: 8109 RVA: 0x000DB26C File Offset: 0x000D946C
		internal static Bitmap LoginBanner_LY
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("LoginBanner_LY", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000534 RID: 1332
		// (get) Token: 0x06001FAE RID: 8110 RVA: 0x000DB298 File Offset: 0x000D9498
		internal static Bitmap LoginBannerNew
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("LoginBannerNew", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000535 RID: 1333
		// (get) Token: 0x06001FAF RID: 8111 RVA: 0x000DB2C4 File Offset: 0x000D94C4
		internal static Bitmap ly_c01_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c01_s01_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000536 RID: 1334
		// (get) Token: 0x06001FB0 RID: 8112 RVA: 0x000DB2F0 File Offset: 0x000D94F0
		internal static Bitmap ly_c01_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c01_s02_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000537 RID: 1335
		// (get) Token: 0x06001FB1 RID: 8113 RVA: 0x000DB31C File Offset: 0x000D951C
		internal static Bitmap ly_c01_s03_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c01_s03_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000538 RID: 1336
		// (get) Token: 0x06001FB2 RID: 8114 RVA: 0x000DB348 File Offset: 0x000D9548
		internal static Bitmap ly_c01_s04_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c01_s04_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000539 RID: 1337
		// (get) Token: 0x06001FB3 RID: 8115 RVA: 0x000DB374 File Offset: 0x000D9574
		internal static Bitmap ly_c02_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c02_s01_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700053A RID: 1338
		// (get) Token: 0x06001FB4 RID: 8116 RVA: 0x000DB3A0 File Offset: 0x000D95A0
		internal static Bitmap ly_c02_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c02_s02_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700053B RID: 1339
		// (get) Token: 0x06001FB5 RID: 8117 RVA: 0x000DB3CC File Offset: 0x000D95CC
		internal static Bitmap ly_c02_s03_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c02_s03_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700053C RID: 1340
		// (get) Token: 0x06001FB6 RID: 8118 RVA: 0x000DB3F8 File Offset: 0x000D95F8
		internal static Bitmap ly_c03_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c03_s01_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700053D RID: 1341
		// (get) Token: 0x06001FB7 RID: 8119 RVA: 0x000DB424 File Offset: 0x000D9624
		internal static Bitmap ly_c03_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c03_s02_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700053E RID: 1342
		// (get) Token: 0x06001FB8 RID: 8120 RVA: 0x000DB450 File Offset: 0x000D9650
		internal static Bitmap ly_c04_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c04_s01_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700053F RID: 1343
		// (get) Token: 0x06001FB9 RID: 8121 RVA: 0x000DB47C File Offset: 0x000D967C
		internal static Bitmap ly_c04_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c04_s02_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000540 RID: 1344
		// (get) Token: 0x06001FBA RID: 8122 RVA: 0x000DB4A8 File Offset: 0x000D96A8
		internal static Bitmap ly_c04_s03_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c04_s03_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000541 RID: 1345
		// (get) Token: 0x06001FBB RID: 8123 RVA: 0x000DB4D4 File Offset: 0x000D96D4
		internal static Bitmap ly_c05_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c05_s01_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000542 RID: 1346
		// (get) Token: 0x06001FBC RID: 8124 RVA: 0x000DB500 File Offset: 0x000D9700
		internal static Bitmap ly_c05_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c05_s02_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000543 RID: 1347
		// (get) Token: 0x06001FBD RID: 8125 RVA: 0x000DB52C File Offset: 0x000D972C
		internal static Bitmap ly_c05_s03_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c05_s03_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000544 RID: 1348
		// (get) Token: 0x06001FBE RID: 8126 RVA: 0x000DB558 File Offset: 0x000D9758
		internal static Bitmap ly_c06_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c06_s01_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000545 RID: 1349
		// (get) Token: 0x06001FBF RID: 8127 RVA: 0x000DB584 File Offset: 0x000D9784
		internal static Bitmap ly_c06_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c06_s02_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000546 RID: 1350
		// (get) Token: 0x06001FC0 RID: 8128 RVA: 0x000DB5B0 File Offset: 0x000D97B0
		internal static Bitmap ly_c07_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c07_s01_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000547 RID: 1351
		// (get) Token: 0x06001FC1 RID: 8129 RVA: 0x000DB5DC File Offset: 0x000D97DC
		internal static Bitmap ly_c08_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c08_s01_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000548 RID: 1352
		// (get) Token: 0x06001FC2 RID: 8130 RVA: 0x000DB608 File Offset: 0x000D9808
		internal static Bitmap ly_c08_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c08_s02_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000549 RID: 1353
		// (get) Token: 0x06001FC3 RID: 8131 RVA: 0x000DB634 File Offset: 0x000D9834
		internal static Bitmap ly_c09_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c09_s01_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700054A RID: 1354
		// (get) Token: 0x06001FC4 RID: 8132 RVA: 0x000DB660 File Offset: 0x000D9860
		internal static Bitmap ly_c09_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c09_s02_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700054B RID: 1355
		// (get) Token: 0x06001FC5 RID: 8133 RVA: 0x000DB68C File Offset: 0x000D988C
		internal static Bitmap ly_c09_s03_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c09_s03_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700054C RID: 1356
		// (get) Token: 0x06001FC6 RID: 8134 RVA: 0x000DB6B8 File Offset: 0x000D98B8
		internal static Bitmap ly_c10_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c10_s01_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700054D RID: 1357
		// (get) Token: 0x06001FC7 RID: 8135 RVA: 0x000DB6E4 File Offset: 0x000D98E4
		internal static Bitmap ly_c10_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c10_s02_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700054E RID: 1358
		// (get) Token: 0x06001FC8 RID: 8136 RVA: 0x000DB710 File Offset: 0x000D9910
		internal static Bitmap ly_c10_s03_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c10_s03_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700054F RID: 1359
		// (get) Token: 0x06001FC9 RID: 8137 RVA: 0x000DB73C File Offset: 0x000D993C
		internal static Bitmap ly_c11_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c11_s01_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000550 RID: 1360
		// (get) Token: 0x06001FCA RID: 8138 RVA: 0x000DB768 File Offset: 0x000D9968
		internal static Bitmap ly_c11_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c11_s02_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000551 RID: 1361
		// (get) Token: 0x06001FCB RID: 8139 RVA: 0x000DB794 File Offset: 0x000D9994
		internal static Bitmap ly_c11_s03_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c11_s03_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000552 RID: 1362
		// (get) Token: 0x06001FCC RID: 8140 RVA: 0x000DB7C0 File Offset: 0x000D99C0
		internal static Bitmap ly_c11_s04_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c11_s04_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000553 RID: 1363
		// (get) Token: 0x06001FCD RID: 8141 RVA: 0x000DB7EC File Offset: 0x000D99EC
		internal static Bitmap ly_c12_s01_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c12_s01_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000554 RID: 1364
		// (get) Token: 0x06001FCE RID: 8142 RVA: 0x000DB818 File Offset: 0x000D9A18
		internal static Bitmap ly_c12_s02_thumbnail
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ly_c12_s02_thumbnail", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000555 RID: 1365
		// (get) Token: 0x06001FCF RID: 8143 RVA: 0x000DB844 File Offset: 0x000D9A44
		internal static Bitmap max_small
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("max_small", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000556 RID: 1366
		// (get) Token: 0x06001FD0 RID: 8144 RVA: 0x000DB870 File Offset: 0x000D9A70
		internal static Bitmap max_small_darkgray
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("max_small_darkgray", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000557 RID: 1367
		// (get) Token: 0x06001FD1 RID: 8145 RVA: 0x000DB89C File Offset: 0x000D9A9C
		internal static Bitmap max_small_gray
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("max_small_gray", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000558 RID: 1368
		// (get) Token: 0x06001FD2 RID: 8146 RVA: 0x000DB8C8 File Offset: 0x000D9AC8
		internal static Bitmap max_small_white
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("max_small_white", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000559 RID: 1369
		// (get) Token: 0x06001FD3 RID: 8147 RVA: 0x000DB8F4 File Offset: 0x000D9AF4
		internal static Bitmap MeasureObj
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("MeasureObj", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700055A RID: 1370
		// (get) Token: 0x06001FD4 RID: 8148 RVA: 0x000DB920 File Offset: 0x000D9B20
		internal static Bitmap MousePointer
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("MousePointer", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700055B RID: 1371
		// (get) Token: 0x06001FD5 RID: 8149 RVA: 0x000DB94C File Offset: 0x000D9B4C
		internal static Bitmap MoveLeft
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("MoveLeft", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700055C RID: 1372
		// (get) Token: 0x06001FD6 RID: 8150 RVA: 0x000DB978 File Offset: 0x000D9B78
		internal static Bitmap MoveRight
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("MoveRight", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700055D RID: 1373
		// (get) Token: 0x06001FD7 RID: 8151 RVA: 0x000DB9A4 File Offset: 0x000D9BA4
		internal static Bitmap NewBook
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("NewBook", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700055E RID: 1374
		// (get) Token: 0x06001FD8 RID: 8152 RVA: 0x000DB9D0 File Offset: 0x000D9BD0
		internal static Bitmap NewFolder
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("NewFolder", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700055F RID: 1375
		// (get) Token: 0x06001FD9 RID: 8153 RVA: 0x000DB9FC File Offset: 0x000D9BFC
		internal static Bitmap notebook
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("notebook", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000560 RID: 1376
		// (get) Token: 0x06001FDA RID: 8154 RVA: 0x000DBA28 File Offset: 0x000D9C28
		internal static Bitmap openHS
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("openHS", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000561 RID: 1377
		// (get) Token: 0x06001FDB RID: 8155 RVA: 0x000DBA54 File Offset: 0x000D9C54
		internal static Bitmap OpenLongArrow_Red
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("OpenLongArrow_Red", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000562 RID: 1378
		// (get) Token: 0x06001FDC RID: 8156 RVA: 0x000DBA80 File Offset: 0x000D9C80
		internal static Bitmap OpenLongArrow_RedLt
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("OpenLongArrow_RedLt", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000563 RID: 1379
		// (get) Token: 0x06001FDD RID: 8157 RVA: 0x000DBAAC File Offset: 0x000D9CAC
		internal static Bitmap OpenShortArrow_Green
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("OpenShortArrow_Green", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000564 RID: 1380
		// (get) Token: 0x06001FDE RID: 8158 RVA: 0x000DBAD8 File Offset: 0x000D9CD8
		internal static Bitmap OpenShortArrow_GreenLt
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("OpenShortArrow_GreenLt", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000565 RID: 1381
		// (get) Token: 0x06001FDF RID: 8159 RVA: 0x000DBB04 File Offset: 0x000D9D04
		internal static Bitmap page
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("page", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000566 RID: 1382
		// (get) Token: 0x06001FE0 RID: 8160 RVA: 0x000DBB30 File Offset: 0x000D9D30
		internal static Bitmap page_four_cht
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("page_four_cht", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000567 RID: 1383
		// (get) Token: 0x06001FE1 RID: 8161 RVA: 0x000DBB5C File Offset: 0x000D9D5C
		internal static Bitmap page_simple_cht
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("page_simple_cht", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000568 RID: 1384
		// (get) Token: 0x06001FE2 RID: 8162 RVA: 0x000DBB88 File Offset: 0x000D9D88
		internal static Bitmap page_symbs
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("page_symbs", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000569 RID: 1385
		// (get) Token: 0x06001FE3 RID: 8163 RVA: 0x000DBBB4 File Offset: 0x000D9DB4
		internal static Bitmap page_three_cht
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("page_three_cht", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700056A RID: 1386
		// (get) Token: 0x06001FE4 RID: 8164 RVA: 0x000DBBE0 File Offset: 0x000D9DE0
		internal static Bitmap page_tick_cht
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("page_tick_cht", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700056B RID: 1387
		// (get) Token: 0x06001FE5 RID: 8165 RVA: 0x000DBC0C File Offset: 0x000D9E0C
		internal static Bitmap page_two_cht
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("page_two_cht", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700056C RID: 1388
		// (get) Token: 0x06001FE6 RID: 8166 RVA: 0x000DBC38 File Offset: 0x000D9E38
		internal static Bitmap ParalLine
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("ParalLine", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700056D RID: 1389
		// (get) Token: 0x06001FE7 RID: 8167 RVA: 0x000DBC64 File Offset: 0x000D9E64
		internal static Bitmap pause
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("pause", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700056E RID: 1390
		// (get) Token: 0x06001FE8 RID: 8168 RVA: 0x000DBC90 File Offset: 0x000D9E90
		internal static Bitmap pause_blue1_28px
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("pause_blue1_28px", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700056F RID: 1391
		// (get) Token: 0x06001FE9 RID: 8169 RVA: 0x000DBCBC File Offset: 0x000D9EBC
		internal static Bitmap pause_blue2_28px
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("pause_blue2_28px", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000570 RID: 1392
		// (get) Token: 0x06001FEA RID: 8170 RVA: 0x000DBCE8 File Offset: 0x000D9EE8
		internal static Bitmap PeriodLines
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("PeriodLines", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000571 RID: 1393
		// (get) Token: 0x06001FEB RID: 8171 RVA: 0x000DBD14 File Offset: 0x000D9F14
		internal static Bitmap play
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("play", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000572 RID: 1394
		// (get) Token: 0x06001FEC RID: 8172 RVA: 0x000DBD40 File Offset: 0x000D9F40
		internal static Bitmap play_blue1_28px
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("play_blue1_28px", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000573 RID: 1395
		// (get) Token: 0x06001FED RID: 8173 RVA: 0x000DBD6C File Offset: 0x000D9F6C
		internal static Bitmap play_blue2_28px
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("play_blue2_28px", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000574 RID: 1396
		// (get) Token: 0x06001FEE RID: 8174 RVA: 0x000DBD98 File Offset: 0x000D9F98
		internal static Bitmap process
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("process", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000575 RID: 1397
		// (get) Token: 0x06001FEF RID: 8175 RVA: 0x000DBDC4 File Offset: 0x000D9FC4
		internal static Bitmap processes
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("processes", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000576 RID: 1398
		// (get) Token: 0x06001FF0 RID: 8176 RVA: 0x000DBDF0 File Offset: 0x000D9FF0
		internal static Bitmap quesmark
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("quesmark", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000577 RID: 1399
		// (get) Token: 0x06001FF1 RID: 8177 RVA: 0x000DBE1C File Offset: 0x000DA01C
		internal static Bitmap Range
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("Range", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000578 RID: 1400
		// (get) Token: 0x06001FF2 RID: 8178 RVA: 0x000DBE48 File Offset: 0x000DA048
		internal static Bitmap Ratio
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("Ratio", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000579 RID: 1401
		// (get) Token: 0x06001FF3 RID: 8179 RVA: 0x000DBE74 File Offset: 0x000DA074
		internal static Bitmap RedArrow_LUp
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("RedArrow_LUp", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700057A RID: 1402
		// (get) Token: 0x06001FF4 RID: 8180 RVA: 0x000DBEA0 File Offset: 0x000DA0A0
		internal static Bitmap RedArrow_RUp
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("RedArrow_RUp", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700057B RID: 1403
		// (get) Token: 0x06001FF5 RID: 8181 RVA: 0x000DBECC File Offset: 0x000DA0CC
		internal static Bitmap RedArrow_Up
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("RedArrow_Up", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700057C RID: 1404
		// (get) Token: 0x06001FF6 RID: 8182 RVA: 0x000DBEF8 File Offset: 0x000DA0F8
		internal static Bitmap RedArrow8px
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("RedArrow8px", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700057D RID: 1405
		// (get) Token: 0x06001FF7 RID: 8183 RVA: 0x000DBF24 File Offset: 0x000DA124
		internal static Bitmap remove_16x16
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("remove_16x16", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700057E RID: 1406
		// (get) Token: 0x06001FF8 RID: 8184 RVA: 0x000DBF50 File Offset: 0x000DA150
		internal static Bitmap remove_blue_16x
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("remove_blue_16x", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700057F RID: 1407
		// (get) Token: 0x06001FF9 RID: 8185 RVA: 0x000DBF7C File Offset: 0x000DA17C
		internal static Bitmap remove_gray_16x
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("remove_gray_16x", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000580 RID: 1408
		// (get) Token: 0x06001FFA RID: 8186 RVA: 0x000DBFA8 File Offset: 0x000DA1A8
		internal static Bitmap RenameHS
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("RenameHS", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000581 RID: 1409
		// (get) Token: 0x06001FFB RID: 8187 RVA: 0x000DBFD4 File Offset: 0x000DA1D4
		internal static Bitmap repeat
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("repeat", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000582 RID: 1410
		// (get) Token: 0x06001FFC RID: 8188 RVA: 0x000DC000 File Offset: 0x000DA200
		internal static Bitmap restore_small
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("restore_small", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000583 RID: 1411
		// (get) Token: 0x06001FFD RID: 8189 RVA: 0x000DC02C File Offset: 0x000DA22C
		internal static Bitmap restore_small_darkgray
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("restore_small_darkgray", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000584 RID: 1412
		// (get) Token: 0x06001FFE RID: 8190 RVA: 0x000DC058 File Offset: 0x000DA258
		internal static Bitmap restore_small_gray
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("restore_small_gray", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000585 RID: 1413
		// (get) Token: 0x06001FFF RID: 8191 RVA: 0x000DC084 File Offset: 0x000DA284
		internal static Bitmap restore_small_white
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("restore_small_white", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000586 RID: 1414
		// (get) Token: 0x06002000 RID: 8192 RVA: 0x000DC0B0 File Offset: 0x000DA2B0
		internal static Bitmap rewind
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("rewind", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000587 RID: 1415
		// (get) Token: 0x06002001 RID: 8193 RVA: 0x000DC0DC File Offset: 0x000DA2DC
		internal static Bitmap rewind_red
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("rewind_red", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000588 RID: 1416
		// (get) Token: 0x06002002 RID: 8194 RVA: 0x000DC108 File Offset: 0x000DA308
		internal static Bitmap saveHS
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("saveHS", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000589 RID: 1417
		// (get) Token: 0x06002003 RID: 8195 RVA: 0x000DC134 File Offset: 0x000DA334
		internal static Bitmap search_16x16
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("search_16x16", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700058A RID: 1418
		// (get) Token: 0x06002004 RID: 8196 RVA: 0x000DC160 File Offset: 0x000DA360
		internal static Bitmap shortcuts24x24
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("shortcuts24x24", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700058B RID: 1419
		// (get) Token: 0x06002005 RID: 8197 RVA: 0x000DC18C File Offset: 0x000DA38C
		internal static Bitmap SmallArrwSample
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("SmallArrwSample", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700058C RID: 1420
		// (get) Token: 0x06002006 RID: 8198 RVA: 0x000DC1B8 File Offset: 0x000DA3B8
		internal static Bitmap Square
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("Square", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700058D RID: 1421
		// (get) Token: 0x06002007 RID: 8199 RVA: 0x000DC1E4 File Offset: 0x000DA3E4
		internal static Bitmap stop_blue1_28px
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("stop_blue1_28px", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700058E RID: 1422
		// (get) Token: 0x06002008 RID: 8200 RVA: 0x000DC210 File Offset: 0x000DA410
		internal static Bitmap stop_blue2_28px
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("stop_blue2_28px", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700058F RID: 1423
		// (get) Token: 0x06002009 RID: 8201 RVA: 0x000DC23C File Offset: 0x000DA43C
		internal static Bitmap SupportWxQrCode
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("SupportWxQrCode", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000590 RID: 1424
		// (get) Token: 0x0600200A RID: 8202 RVA: 0x000DC268 File Offset: 0x000DA468
		internal static Icon TExIcoBlue
		{
			get
			{
				return (Icon)Class372.ResourceManager.GetObject("TExIcoBlue", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000591 RID: 1425
		// (get) Token: 0x0600200B RID: 8203 RVA: 0x000DC294 File Offset: 0x000DA494
		internal static Icon TExIcoRed
		{
			get
			{
				return (Icon)Class372.ResourceManager.GetObject("TExIcoRed", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000592 RID: 1426
		// (get) Token: 0x0600200C RID: 8204 RVA: 0x000DC2C0 File Offset: 0x000DA4C0
		internal static Bitmap Text
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("Text", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000593 RID: 1427
		// (get) Token: 0x0600200D RID: 8205 RVA: 0x000DC2EC File Offset: 0x000DA4EC
		internal static Bitmap theme_1_1
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("theme_1_1", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000594 RID: 1428
		// (get) Token: 0x0600200E RID: 8206 RVA: 0x000DC318 File Offset: 0x000DA518
		internal static Bitmap theme_1_2
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("theme_1_2", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000595 RID: 1429
		// (get) Token: 0x0600200F RID: 8207 RVA: 0x000DC344 File Offset: 0x000DA544
		internal static Bitmap theme_1_3
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("theme_1_3", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000596 RID: 1430
		// (get) Token: 0x06002010 RID: 8208 RVA: 0x000DC370 File Offset: 0x000DA570
		internal static Bitmap theme_2_1
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("theme_2_1", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000597 RID: 1431
		// (get) Token: 0x06002011 RID: 8209 RVA: 0x000DC39C File Offset: 0x000DA59C
		internal static Bitmap theme_2_2
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("theme_2_2", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000598 RID: 1432
		// (get) Token: 0x06002012 RID: 8210 RVA: 0x000DC3C8 File Offset: 0x000DA5C8
		internal static Bitmap theme_2_3
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("theme_2_3", Class372.cultureInfo_0);
			}
		}

		// Token: 0x17000599 RID: 1433
		// (get) Token: 0x06002013 RID: 8211 RVA: 0x000DC3F4 File Offset: 0x000DA5F4
		internal static Bitmap theme_3_1
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("theme_3_1", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700059A RID: 1434
		// (get) Token: 0x06002014 RID: 8212 RVA: 0x000DC420 File Offset: 0x000DA620
		internal static Bitmap theme_3_2
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("theme_3_2", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700059B RID: 1435
		// (get) Token: 0x06002015 RID: 8213 RVA: 0x000DC44C File Offset: 0x000DA64C
		internal static Bitmap theme_3_3
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("theme_3_3", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700059C RID: 1436
		// (get) Token: 0x06002016 RID: 8214 RVA: 0x000DC478 File Offset: 0x000DA678
		internal static Bitmap theme_4_1
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("theme_4_1", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700059D RID: 1437
		// (get) Token: 0x06002017 RID: 8215 RVA: 0x000DC4A4 File Offset: 0x000DA6A4
		internal static Bitmap theme_4_2
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("theme_4_2", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700059E RID: 1438
		// (get) Token: 0x06002018 RID: 8216 RVA: 0x000DC4D0 File Offset: 0x000DA6D0
		internal static Bitmap theme_4_3
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("theme_4_3", Class372.cultureInfo_0);
			}
		}

		// Token: 0x1700059F RID: 1439
		// (get) Token: 0x06002019 RID: 8217 RVA: 0x000DC4FC File Offset: 0x000DA6FC
		internal static Bitmap transparent
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("transparent", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170005A0 RID: 1440
		// (get) Token: 0x0600201A RID: 8218 RVA: 0x000DC528 File Offset: 0x000DA728
		internal static Bitmap TrendSpeed
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("TrendSpeed", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170005A1 RID: 1441
		// (get) Token: 0x0600201B RID: 8219 RVA: 0x000DC554 File Offset: 0x000DA754
		internal static Bitmap volume_dec_blue1_28px
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("volume_dec_blue1_28px", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170005A2 RID: 1442
		// (get) Token: 0x0600201C RID: 8220 RVA: 0x000DC580 File Offset: 0x000DA780
		internal static Bitmap volume_dec_blue2_28px
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("volume_dec_blue2_28px", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170005A3 RID: 1443
		// (get) Token: 0x0600201D RID: 8221 RVA: 0x000DC5AC File Offset: 0x000DA7AC
		internal static Bitmap volume_inc_blue1_28px
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("volume_inc_blue1_28px", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170005A4 RID: 1444
		// (get) Token: 0x0600201E RID: 8222 RVA: 0x000DC5D8 File Offset: 0x000DA7D8
		internal static Bitmap volume_inc_blue2_28px
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("volume_inc_blue2_28px", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170005A5 RID: 1445
		// (get) Token: 0x0600201F RID: 8223 RVA: 0x000DC604 File Offset: 0x000DA804
		internal static Bitmap WaveRuler
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("WaveRuler", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170005A6 RID: 1446
		// (get) Token: 0x06002020 RID: 8224 RVA: 0x000DC630 File Offset: 0x000DA830
		internal static Bitmap window_edit
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("window_edit", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170005A7 RID: 1447
		// (get) Token: 0x06002021 RID: 8225 RVA: 0x000DC65C File Offset: 0x000DA85C
		internal static Bitmap window_maximize_blue
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("window_maximize_blue", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170005A8 RID: 1448
		// (get) Token: 0x06002022 RID: 8226 RVA: 0x000DC688 File Offset: 0x000DA888
		internal static Bitmap window_maximize_red
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("window_maximize_red", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170005A9 RID: 1449
		// (get) Token: 0x06002023 RID: 8227 RVA: 0x000DC6B4 File Offset: 0x000DA8B4
		internal static Bitmap window_restore_blue
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("window_restore_blue", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170005AA RID: 1450
		// (get) Token: 0x06002024 RID: 8228 RVA: 0x000DC6E0 File Offset: 0x000DA8E0
		internal static Bitmap window_restore_red
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("window_restore_red", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170005AB RID: 1451
		// (get) Token: 0x06002025 RID: 8229 RVA: 0x000DC70C File Offset: 0x000DA90C
		internal static Bitmap zoomin
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("zoomin", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170005AC RID: 1452
		// (get) Token: 0x06002026 RID: 8230 RVA: 0x000DC738 File Offset: 0x000DA938
		internal static Bitmap zoomin_red
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("zoomin_red", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170005AD RID: 1453
		// (get) Token: 0x06002027 RID: 8231 RVA: 0x000DC764 File Offset: 0x000DA964
		internal static Bitmap zoomout
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("zoomout", Class372.cultureInfo_0);
			}
		}

		// Token: 0x170005AE RID: 1454
		// (get) Token: 0x06002028 RID: 8232 RVA: 0x000DC790 File Offset: 0x000DA990
		internal static Bitmap zoomout_red
		{
			get
			{
				return (Bitmap)Class372.ResourceManager.GetObject("zoomout_red", Class372.cultureInfo_0);
			}
		}

		// Token: 0x04000FB9 RID: 4025
		private static ResourceManager resourceManager_0;

		// Token: 0x04000FBA RID: 4026
		private static CultureInfo cultureInfo_0;
	}
}
