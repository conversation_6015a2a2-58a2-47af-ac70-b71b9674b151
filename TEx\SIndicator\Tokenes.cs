﻿using System;
using System.Collections.Generic;
using System.Linq;
using ns13;
using ns9;

namespace TEx.SIndicator
{
	// Token: 0x0200032C RID: 812
	public sealed class Tokenes
	{
		// Token: 0x06002266 RID: 8806 RVA: 0x0000DA48 File Offset: 0x0000BC48
		public Tokenes(List<HToken> tokenList)
		{
			this.tokenList = tokenList;
		}

		// Token: 0x06002267 RID: 8807 RVA: 0x000EA914 File Offset: 0x000E8B14
		public string[] method_0()
		{
			return this.tokenList.Where(new Func<HToken, bool>(Tokenes.<>c.<>9.method_0)).Select(new Func<HToken, string>(Tokenes.<>c.<>9.method_1)).ToArray<string>();
		}

		// Token: 0x06002268 RID: 8808 RVA: 0x000EA978 File Offset: 0x000E8B78
		public HToken method_1()
		{
			this.int_0++;
			HToken result;
			if (this.int_0 >= this.tokenList.Count)
			{
				result = new HToken(this.tokenList.Last<HToken>().Col + 1, this.tokenList.Last<HToken>().Line, new Class439(Enum26.const_30, "EOF"));
			}
			else
			{
				result = this.tokenList[this.int_0];
			}
			return result;
		}

		// Token: 0x06002269 RID: 8809 RVA: 0x000EA9F4 File Offset: 0x000E8BF4
		public HToken method_2()
		{
			this.int_0--;
			if (this.int_0 <= 0)
			{
				this.int_0 = 0;
			}
			return this.tokenList[this.int_0];
		}

		// Token: 0x170005E6 RID: 1510
		// (get) Token: 0x0600226A RID: 8810 RVA: 0x000EAA34 File Offset: 0x000E8C34
		public int Count
		{
			get
			{
				return this.tokenList.Count;
			}
		}

		// Token: 0x0600226B RID: 8811 RVA: 0x000EAA50 File Offset: 0x000E8C50
		public HToken method_3(int int_1)
		{
			return this.tokenList[int_1];
		}

		// Token: 0x0600226C RID: 8812 RVA: 0x0000DA64 File Offset: 0x0000BC64
		public void method_4()
		{
			this.int_0 = 0;
		}

		// Token: 0x170005E7 RID: 1511
		// (get) Token: 0x0600226D RID: 8813 RVA: 0x000EAA70 File Offset: 0x000E8C70
		public HToken Current
		{
			get
			{
				HToken result;
				if (this.int_0 >= this.tokenList.Count)
				{
					result = new HToken(this.tokenList.Last<HToken>().Col + 1, this.tokenList.Last<HToken>().Line, new Class439(Enum26.const_30, "EOF"));
				}
				else
				{
					result = this.tokenList[this.int_0];
				}
				return result;
			}
		}

		// Token: 0x040010C2 RID: 4290
		private List<HToken> tokenList = new List<HToken>();

		// Token: 0x040010C3 RID: 4291
		private int int_0;
	}
}
