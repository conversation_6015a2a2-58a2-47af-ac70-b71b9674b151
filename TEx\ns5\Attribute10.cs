﻿using System;

namespace ns5
{
	// Token: 0x020003FC RID: 1020
	[AttributeUsage(AttributeTargets.Constructor | AttributeTargets.Method)]
	internal sealed class Attribute10 : Attribute
	{
		// Token: 0x060027BC RID: 10172 RVA: 0x0000EF2D File Offset: 0x0000D12D
		public Attribute10()
		{
		}

		// Token: 0x060027BD RID: 10173 RVA: 0x0000EF2D File Offset: 0x0000D12D
		public Attribute10(string string_0)
		{
		}
	}
}
