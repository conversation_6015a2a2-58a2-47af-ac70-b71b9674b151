﻿using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading;
using ns10;

namespace ns12
{
	// Token: 0x0200037D RID: 893
	internal static class Class481
	{
		// Token: 0x140000B9 RID: 185
		// (add) Token: 0x060024EF RID: 9455 RVA: 0x000F6808 File Offset: 0x000F4A08
		// (remove) Token: 0x060024F0 RID: 9456 RVA: 0x000F6840 File Offset: 0x000F4A40
		public static event Action OnTimeOut
		{
			[CompilerGenerated]
			add
			{
				Action action = Class481.action_0;
				Action action2;
				do
				{
					action2 = action;
					Action value2 = (Action)Delegate.Combine(action2, value);
					action = Interlocked.CompareExchange<Action>(ref Class481.action_0, value2, action2);
				}
				while (action != action2);
			}
			[CompilerGenerated]
			remove
			{
				Action action = Class481.action_0;
				Action action2;
				do
				{
					action2 = action;
					Action value2 = (Action)Delegate.Remove(action2, value);
					action = Interlocked.CompareExchange<Action>(ref Class481.action_0, value2, action2);
				}
				while (action != action2);
			}
		}

		// Token: 0x060024F1 RID: 9457 RVA: 0x0000E48C File Offset: 0x0000C68C
		public static void smethod_0(DateTime dateTime_1)
		{
			Class481.dateTime_0 = dateTime_1;
		}

		// Token: 0x060024F2 RID: 9458 RVA: 0x0000E496 File Offset: 0x0000C696
		public static void smethod_1()
		{
			Class481.bool_0 = true;
		}

		// Token: 0x060024F3 RID: 9459 RVA: 0x0000E4A0 File Offset: 0x0000C6A0
		public static void smethod_2()
		{
			Class481.dateTime_0 = DateTime.Now;
			BackgroundWorker backgroundWorker = new BackgroundWorker();
			backgroundWorker.DoWork += Class481.smethod_4;
			backgroundWorker.RunWorkerCompleted += Class481.smethod_3;
			backgroundWorker.RunWorkerAsync();
		}

		// Token: 0x060024F4 RID: 9460 RVA: 0x0000E4DC File Offset: 0x0000C6DC
		private static void smethod_3(object sender, RunWorkerCompletedEventArgs e)
		{
			if (Class481.action_0 != null && Class481.bool_1)
			{
				Class481.action_0();
			}
		}

		// Token: 0x060024F5 RID: 9461 RVA: 0x000F6878 File Offset: 0x000F4A78
		private static void smethod_4(object sender, DoWorkEventArgs e)
		{
			Class481.bool_0 = false;
			while (!Class481.bool_0)
			{
				if (DateTime.Now - Class481.dateTime_0 >= Class481.timeSpan_0)
				{
					Class481.bool_1 = true;
					Class467.smethod_1(string.Format("当前时间{0}触发超时退出，超时时间{1}", DateTime.Now, Class481.dateTime_0));
					return;
				}
				Thread.Sleep(10);
			}
		}

		// Token: 0x040011CC RID: 4556
		[CompilerGenerated]
		private static Action action_0;

		// Token: 0x040011CD RID: 4557
		private static readonly TimeSpan timeSpan_0 = new TimeSpan(0, 3, 0);

		// Token: 0x040011CE RID: 4558
		private static DateTime dateTime_0;

		// Token: 0x040011CF RID: 4559
		private static bool bool_0;

		// Token: 0x040011D0 RID: 4560
		private static bool bool_1 = false;
	}
}
