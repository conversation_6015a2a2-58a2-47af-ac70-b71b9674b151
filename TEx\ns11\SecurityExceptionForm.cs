﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using ns23;
using ns24;
using ns25;
using ns30;

namespace ns11
{
	// Token: 0x0200041A RID: 1050
	internal sealed partial class SecurityExceptionForm : Form
	{
		// Token: 0x06002869 RID: 10345 RVA: 0x0000FBCB File Offset: 0x0000DDCB
		private string method_0(string string_0)
		{
			string_0 = string_0.Replace("%AppName%", "『交易练习者』");
			string_0 = string_0.Replace("%CompanyName%", "TEx Studio");
			return string_0;
		}

		// Token: 0x0600286A RID: 10346 RVA: 0x00105834 File Offset: 0x00103A34
		public SecurityExceptionForm()
		{
			this.InitializeComponent();
			base.Icon = Class543.smethod_0();
			this.Text = this.method_0(this.Text);
			if (this.Text.Length == 0)
			{
				this.Text = "Security Exception";
			}
			foreach (object obj in base.Controls)
			{
				Control control = (Control)obj;
				control.Text = this.method_0(control.Text);
				foreach (object obj2 in control.Controls)
				{
					Control control2 = (Control)obj2;
					control2.Text = this.method_0(control2.Text);
				}
			}
		}

		// Token: 0x0600286B RID: 10347 RVA: 0x00105934 File Offset: 0x00103B34
		public SecurityExceptionForm(EventArgs36 eventArgs36_1) : this()
		{
			if (eventArgs36_1 == null)
			{
				return;
			}
			if (!eventArgs36_1.CanContinue)
			{
				this.continueButton.Visible = false;
			}
			this.eventArgs36_0 = eventArgs36_1;
			if (eventArgs36_1.SecurityMessage.Length > 0)
			{
				this.errorMessage.Text = eventArgs36_1.SecurityMessage;
			}
			else
			{
				StringBuilder stringBuilder = new StringBuilder();
				stringBuilder.Append("%AppName% attempted to perform an operation not allowed by the security policy. To grant this application the required permission, contact your system administrator, or use the Microsoft .NET Framework Configuration tool.\n\n");
				if (eventArgs36_1.CanContinue)
				{
					stringBuilder.Append("If you click Continue, the application will ignore this error and attempt to continue. If you click Quit, the application will close immediately.\n\n");
				}
				stringBuilder.Append(eventArgs36_1.SecurityException.Message);
				this.errorMessage.Text = this.method_0(stringBuilder.ToString());
			}
			int num = this.errorMessage.Bottom + 60;
			if (num > base.ClientSize.Height)
			{
				base.ClientSize = new Size(base.ClientSize.Width, num);
			}
		}

		// Token: 0x0600286C RID: 10348 RVA: 0x0000FCCB File Offset: 0x0000DECB
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600286E RID: 10350 RVA: 0x0000FCEA File Offset: 0x0000DEEA
		private void continueButton_Click(object sender, EventArgs e)
		{
			if (this.eventArgs36_0 != null)
			{
				this.eventArgs36_0.TryToContinue = true;
			}
			base.Close();
		}

		// Token: 0x0600286F RID: 10351 RVA: 0x0000FD06 File Offset: 0x0000DF06
		private void quitButton_Click(object sender, EventArgs e)
		{
			if (this.eventArgs36_0 != null)
			{
				this.eventArgs36_0.TryToContinue = false;
			}
			base.Close();
		}

		// Token: 0x0400143D RID: 5181
		private EventArgs36 eventArgs36_0;

		// Token: 0x04001443 RID: 5187
		private IContainer icontainer_0;
	}
}
