﻿using System;
using ns14;
using ns30;
using ns9;
using TEx.SIndicator;

namespace ns23
{
	// Token: 0x02000341 RID: 833
	internal sealed class Class421 : Class412
	{
		// Token: 0x06002314 RID: 8980 RVA: 0x0000D8A3 File Offset: 0x0000BAA3
		public Class421(HToken htoken_1, Class408 class408_2, Class408 class408_3) : base(htoken_1, class408_2, class408_3)
		{
		}

		// Token: 0x06002315 RID: 8981 RVA: 0x000EE0A0 File Offset: 0x000EC2A0
		public override object vmethod_1(ParserEnvironment parserEnvironment_0)
		{
			object result;
			if (this.Left.Token.Symbol.HSymbolType != Enum26.const_31)
			{
				result = this.Left.vmethod_1(parserEnvironment_0);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06002316 RID: 8982 RVA: 0x000E8564 File Offset: 0x000E6764
		public override string vmethod_0()
		{
			return base.vmethod_0();
		}
	}
}
