﻿using System;
using System.Drawing;
using System.Runtime.Serialization;
using ns28;

namespace TEx
{
	// Token: 0x02000194 RID: 404
	[Serializable]
	internal sealed class DrawGrnArwDn : DrawRedArwUp, ISerializable
	{
		// Token: 0x06000FA1 RID: 4001 RVA: 0x00006B34 File Offset: 0x00004D34
		public DrawGrnArwDn()
		{
		}

		// Token: 0x06000FA2 RID: 4002 RVA: 0x00006B3C File Offset: 0x00004D3C
		public DrawGrnArwDn(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = "下箭头";
		}

		// Token: 0x06000FA3 RID: 4003 RVA: 0x00006B58 File Offset: 0x00004D58
		protected DrawGrnArwDn(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x06000FA4 RID: 4004 RVA: 0x00006B69 File Offset: 0x00004D69
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x06000FA5 RID: 4005 RVA: 0x00062280 File Offset: 0x00060480
		protected override Image vmethod_24()
		{
			return Class372.GreenArrow_Down;
		}
	}
}
