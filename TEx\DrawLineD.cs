﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000198 RID: 408
	[Serializable]
	internal sealed class DrawLineD : DrawObj, ISerializable
	{
		// Token: 0x06000FB9 RID: 4025 RVA: 0x000037AA File Offset: 0x000019AA
		public DrawLineD()
		{
		}

		// Token: 0x06000FBA RID: 4026 RVA: 0x00006BD7 File Offset: 0x00004DD7
		public DrawLineD(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = "线段";
			base.CanChgColor = true;
			base.IsOneClickLoc = false;
		}

		// Token: 0x06000FBB RID: 4027 RVA: 0x000037DC File Offset: 0x000019DC
		protected DrawLineD(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x06000FBC RID: 4028 RVA: 0x000037ED File Offset: 0x000019ED
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x06000FBD RID: 4029 RVA: 0x000623EC File Offset: 0x000605EC
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			LineObj item = base.method_23(double_1, double_2, double_3, double_4, string_5);
			list.Add(item);
			return list;
		}
	}
}
