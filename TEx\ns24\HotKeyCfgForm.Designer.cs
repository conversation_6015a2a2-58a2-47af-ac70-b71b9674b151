﻿namespace ns24
{
	// Token: 0x02000179 RID: 377
	internal sealed partial class HotKeyCfgForm : global::System.Windows.Forms.Form
	{
		// Token: 0x06000E36 RID: 3638 RVA: 0x0005AB64 File Offset: 0x00058D64
		private void InitializeComponent()
		{
			this.button_OK = new global::System.Windows.Forms.Button();
			this.panel1 = new global::System.Windows.Forms.Panel();
			this.label1 = new global::System.Windows.Forms.Label();
			this.label_acctNote = new global::System.Windows.Forms.Label();
			this.pictureBox1 = new global::System.Windows.Forms.PictureBox();
			((global::System.ComponentModel.ISupportInitialize)this.pictureBox1).BeginInit();
			base.SuspendLayout();
			this.button_OK.Location = new global::System.Drawing.Point(299, 372);
			this.button_OK.Name = "button_OK";
			this.button_OK.Size = new global::System.Drawing.Size(120, 30);
			this.button_OK.TabIndex = 10;
			this.button_OK.Text = "确定";
			this.button_OK.UseVisualStyleBackColor = true;
			this.button_OK.Click += new global::System.EventHandler(this.button_OK_Click);
			this.panel1.AutoScroll = true;
			this.panel1.BorderStyle = global::System.Windows.Forms.BorderStyle.FixedSingle;
			this.panel1.Location = new global::System.Drawing.Point(33, 39);
			this.panel1.Name = "panel1";
			this.panel1.Size = new global::System.Drawing.Size(519, 324);
			this.panel1.TabIndex = 12;
			this.label1.AutoSize = true;
			this.label1.Location = new global::System.Drawing.Point(59, 16);
			this.label1.Name = "label1";
			this.label1.Size = new global::System.Drawing.Size(45, 15);
			this.label1.TabIndex = 21;
			this.label1.Text = "提示:";
			this.label_acctNote.Location = new global::System.Drawing.Point(106, 16);
			this.label_acctNote.Name = "label_acctNote";
			this.label_acctNote.Size = new global::System.Drawing.Size(346, 20);
			this.label_acctNote.TabIndex = 20;
			this.label_acctNote.Text = "请点击项目文本框，键入欲设置快捷键。";
			this.pictureBox1.BackgroundImageLayout = global::System.Windows.Forms.ImageLayout.None;
			this.pictureBox1.Image = global::ns28.Class372._1683_Lightbulb_32x32;
			this.pictureBox1.Location = new global::System.Drawing.Point(36, 13);
			this.pictureBox1.Name = "pictureBox1";
			this.pictureBox1.Size = new global::System.Drawing.Size(20, 20);
			this.pictureBox1.SizeMode = global::System.Windows.Forms.PictureBoxSizeMode.StretchImage;
			this.pictureBox1.TabIndex = 19;
			this.pictureBox1.TabStop = false;
			base.AcceptButton = this.button_OK;
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(8f, 15f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			this.BackColor = global::System.Drawing.SystemColors.Control;
			base.ClientSize = new global::System.Drawing.Size(588, 411);
			base.Controls.Add(this.label1);
			base.Controls.Add(this.label_acctNote);
			base.Controls.Add(this.pictureBox1);
			base.Controls.Add(this.panel1);
			base.Controls.Add(this.button_OK);
			base.FormBorderStyle = global::System.Windows.Forms.FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = "HotKeyCfgForm";
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			this.Text = "热键设置";
			((global::System.ComponentModel.ISupportInitialize)this.pictureBox1).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000766 RID: 1894
		private global::System.Windows.Forms.Button button_OK;

		// Token: 0x04000767 RID: 1895
		private global::System.Windows.Forms.Panel panel1;

		// Token: 0x04000768 RID: 1896
		private global::System.Windows.Forms.Label label1;

		// Token: 0x04000769 RID: 1897
		private global::System.Windows.Forms.Label label_acctNote;

		// Token: 0x0400076A RID: 1898
		private global::System.Windows.Forms.PictureBox pictureBox1;
	}
}
