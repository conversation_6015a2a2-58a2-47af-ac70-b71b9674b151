﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace ns2
{
	// Token: 0x02000002 RID: 2
	[CompilerGenerated]
	internal sealed class Class1<T, U, V, W>
	{
		// Token: 0x17000001 RID: 1
		// (get) Token: 0x06000001 RID: 1 RVA: 0x0000FDB4 File Offset: 0x0000DFB4
		public T api
		{
			get
			{
				return this.gparam_0;
			}
		}

		// Token: 0x17000002 RID: 2
		// (get) Token: 0x06000002 RID: 2 RVA: 0x0000FDCC File Offset: 0x0000DFCC
		public U lc
		{
			get
			{
				return this.gparam_1;
			}
		}

		// Token: 0x17000003 RID: 3
		// (get) Token: 0x06000003 RID: 3 RVA: 0x0000FDE4 File Offset: 0x0000DFE4
		public V compress
		{
			get
			{
				return this.gparam_2;
			}
		}

		// Token: 0x17000004 RID: 4
		// (get) Token: 0x06000004 RID: 4 RVA: 0x0000FDFC File Offset: 0x0000DFFC
		public W param
		{
			get
			{
				return this.gparam_3;
			}
		}

		// Token: 0x06000005 RID: 5 RVA: 0x00002A54 File Offset: 0x00000C54
		[DebuggerHidden]
		public Class1(T gparam_4, U gparam_5, V gparam_6, W gparam_7)
		{
			this.gparam_0 = gparam_4;
			this.gparam_1 = gparam_5;
			this.gparam_2 = gparam_6;
			this.gparam_3 = gparam_7;
		}

		// Token: 0x06000006 RID: 6 RVA: 0x0000FE14 File Offset: 0x0000E014
		[DebuggerHidden]
		public bool Equals(object obj)
		{
			Class1<T, U, V, W> @class = obj as Class1<T, U, V, W>;
			bool result;
			if (@class != null && EqualityComparer<T>.Default.Equals(this.gparam_0, @class.gparam_0) && EqualityComparer<U>.Default.Equals(this.gparam_1, @class.gparam_1) && EqualityComparer<V>.Default.Equals(this.gparam_2, @class.gparam_2))
			{
				result = EqualityComparer<W>.Default.Equals(this.gparam_3, @class.gparam_3);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06000007 RID: 7 RVA: 0x0000FE94 File Offset: 0x0000E094
		[DebuggerHidden]
		public int GetHashCode()
		{
			return (((********** + EqualityComparer<T>.Default.GetHashCode(this.gparam_0)) * -********** + EqualityComparer<U>.Default.GetHashCode(this.gparam_1)) * -********** + EqualityComparer<V>.Default.GetHashCode(this.gparam_2)) * -********** + EqualityComparer<W>.Default.GetHashCode(this.gparam_3);
		}

		// Token: 0x06000008 RID: 8 RVA: 0x0000FF00 File Offset: 0x0000E100
		[DebuggerHidden]
		public string ToString()
		{
			IFormatProvider provider = null;
			string format = "{{ api = {0}, lc = {1}, compress = {2}, param = {3} }}";
			object[] array = new object[4];
			int num = 0;
			T t = this.gparam_0;
			ref T ptr = ref t;
			T t2 = default(T);
			object obj;
			if (t2 == null)
			{
				t2 = t;
				ptr = ref t2;
				if (t2 == null)
				{
					obj = null;
					goto IL_46;
				}
			}
			obj = ptr.ToString();
			IL_46:
			array[num] = obj;
			int num2 = 1;
			U u = this.gparam_1;
			ref U ptr2 = ref u;
			U u2 = default(U);
			object obj2;
			if (u2 == null)
			{
				u2 = u;
				ptr2 = ref u2;
				if (u2 == null)
				{
					obj2 = null;
					goto IL_81;
				}
			}
			obj2 = ptr2.ToString();
			IL_81:
			array[num2] = obj2;
			int num3 = 2;
			V v = this.gparam_2;
			ref V ptr3 = ref v;
			V v2 = default(V);
			object obj3;
			if (v2 == null)
			{
				v2 = v;
				ptr3 = ref v2;
				if (v2 == null)
				{
					obj3 = null;
					goto IL_C0;
				}
			}
			obj3 = ptr3.ToString();
			IL_C0:
			array[num3] = obj3;
			int num4 = 3;
			W w = this.gparam_3;
			ref W ptr4 = ref w;
			W w2 = default(W);
			object obj4;
			if (w2 == null)
			{
				w2 = w;
				ptr4 = ref w2;
				if (w2 == null)
				{
					obj4 = null;
					goto IL_FF;
				}
			}
			obj4 = ptr4.ToString();
			IL_FF:
			array[num4] = obj4;
			return string.Format(provider, format, array);
		}

		// Token: 0x04000001 RID: 1
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly T gparam_0;

		// Token: 0x04000002 RID: 2
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly U gparam_1;

		// Token: 0x04000003 RID: 3
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly V gparam_2;

		// Token: 0x04000004 RID: 4
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly W gparam_3;
	}
}
