﻿using System;

namespace ns10
{
	// Token: 0x02000360 RID: 864
	internal static class Class467
	{
		// Token: 0x060023ED RID: 9197 RVA: 0x000041AE File Offset: 0x000023AE
		public static void smethod_0(string string_0, string string_1)
		{
		}

		// Token: 0x060023EE RID: 9198 RVA: 0x0000E03B File Offset: 0x0000C23B
		public static void smethod_1(string string_0)
		{
			Class467.smethod_0("_log.ini", string_0);
		}

		// Token: 0x04001165 RID: 4453
		private static object object_0 = new object();
	}
}
