﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using TEx;

namespace ns11
{
	// Token: 0x020002CF RID: 719
	internal sealed partial class FormGroupName : Form
	{
		// Token: 0x06002032 RID: 8242 RVA: 0x000DCBF0 File Offset: 0x000DADF0
		public FormGroupName()
		{
			this.InitializeComponent();
			this.buttonCancel.Click += this.buttonCancel_Click;
			this.buttonOK.Click += this.buttonOK_Click;
			Base.UI.smethod_54(this);
		}

		// Token: 0x06002033 RID: 8243 RVA: 0x000DCC4C File Offset: 0x000DAE4C
		private void buttonOK_Click(object sender, EventArgs e)
		{
			if (this.textBox1.Text.Trim() == "")
			{
				MessageBox.Show("请输入分组名称。");
				this.textBox1.Focus();
			}
			else if (this.list_0.Contains(this.textBox1.Text.Trim()))
			{
				MessageBox.Show("此分组名称已经存在，请重新命名。");
				this.textBox1.Focus();
			}
			else
			{
				this.bool_0 = true;
				base.Close();
			}
		}

		// Token: 0x06002034 RID: 8244 RVA: 0x0000D0F4 File Offset: 0x0000B2F4
		private void buttonCancel_Click(object sender, EventArgs e)
		{
			this.bool_0 = false;
			base.Close();
		}

		// Token: 0x170005B0 RID: 1456
		// (get) Token: 0x06002035 RID: 8245 RVA: 0x000DCCD4 File Offset: 0x000DAED4
		public string GroupName
		{
			get
			{
				return this.textBox1.Text.Trim();
			}
		}

		// Token: 0x06002036 RID: 8246 RVA: 0x0000D105 File Offset: 0x0000B305
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000FC6 RID: 4038
		public bool bool_0;

		// Token: 0x04000FC7 RID: 4039
		public List<string> list_0 = new List<string>();

		// Token: 0x04000FC8 RID: 4040
		private IContainer icontainer_0;
	}
}
