﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProjectGuid>{FFB6CF4D-AE69-4B73-8AD5-37BCAAD270AC}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>TEx</RootNamespace>
    <AssemblyName>TEx</AssemblyName>
    <TargetFrameworkVersion>v3.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <ApplicationIcon>TEx.ico</ApplicationIcon>
    <StartupObject>ns23.Class348</StartupObject>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AutocompleteMenu-ScintillaNET">
      <HintPath>D:\Program Files\TEx Studio\交易练习者2\AutocompleteMenu-ScintillaNET.dll</HintPath>
    </Reference>
    <Reference Include="DevComponents.DotNetBar2">
      <HintPath>D:\Program Files\TEx Studio\交易练习者2\DevComponents.DotNetBar2.dll</HintPath>
    </Reference>
    <Reference Include="Interop.SHDocVw">
      <HintPath>D:\Program Files\TEx Studio\交易练习者2\Interop.SHDocVw.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.mshtml">
      <HintPath>D:\Program Files\TEx Studio\交易练习者2\Microsoft.mshtml.dll</HintPath>
    </Reference>
    <Reference Include="NAppUpdate.Framework">
      <HintPath>D:\Program Files\TEx Studio\交易练习者2\NAppUpdate.Framework.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>D:\Program Files\TEx Studio\交易练习者2\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NPOI">
      <HintPath>D:\Program Files\TEx Studio\交易练习者\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML">
      <HintPath>D:\Program Files\TEx Studio\交易练习者2\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="ScintillaNET">
      <HintPath>D:\Program Files\TEx Studio\交易练习者\ScintillaNET.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Runtime.Remoting" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="TEx.Chart">
      <HintPath>D:\Program Files\TEx Studio\交易练习者2\TEx.Chart.dll</HintPath>
    </Reference>
    <Reference Include="TEx.Comn">
      <HintPath>D:\Program Files\TEx Studio\交易练习者2\TEx.Comn.dll</HintPath>
    </Reference>
    <Reference Include="TEx.Inds">
      <HintPath>D:\Program Files\TEx Studio\交易练习者2\TEx.Inds.dll</HintPath>
    </Reference>
    <Reference Include="TEx.Util">
      <HintPath>D:\Program Files\TEx Studio\交易练习者2\TEx.Util.dll</HintPath>
    </Reference>
    <Reference Include="Vlc.DotNet.Core" />
    <Reference Include="Vlc.DotNet.Forms" />
  </ItemGroup>
  <ItemGroup>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AcctSymblDT.cs" />
    <Compile Include="BackupSyncConflictTreatmt.cs" />
    <Compile Include="BackupSyncTimeInterval.cs" />
    <Compile Include="BaoDian.cs" />
    <Compile Include="BaoDianMgr.cs" />
    <Compile Include="Base.cs" />
    <Compile Include="BkupSyncCnfmWnd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BkupSyncCnfmWnd.Designer.cs">
      <DependentUpon>BkupSyncCnfmWnd.cs</DependentUpon>
    </Compile>
    <Compile Include="BkupSyncMgr.cs" />
    <Compile Include="BlindTestForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BlindTestForm.Designer.cs">
      <DependentUpon>BlindTestForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ChartBase.cs" />
    <Compile Include="ChartCS.cs" />
    <Compile Include="ChartKLine.cs" />
    <Compile Include="ChartKLSub.cs" />
    <Compile Include="ChartPage.cs" />
    <Compile Include="ChartParam.cs" />
    <Compile Include="ChartTheme.cs" />
    <Compile Include="ChartTickM.cs" />
    <Compile Include="ChartTickV.cs" />
    <Compile Include="ChartType.cs" />
    <Compile Include="ChartUISettings.cs" />
    <Compile Include="ChtCtrl.cs" />
    <Compile Include="ChtCtrlParam.cs" />
    <Compile Include="ChtCtrlParam_KLine.cs" />
    <Compile Include="ChtCtrl_KLine.cs" />
    <Compile Include="ChtCtrl_Tick.cs" />
    <Compile Include="ComparisonOpt.cs" />
    <Compile Include="ComputerType.cs" />
    <Compile Include="ConnMgr.cs" />
    <Compile Include="DataGridViewHisTrans.cs" />
    <Compile Include="DataGridViewMkt.cs" />
    <Compile Include="DataGridViewOpenTrans.cs" />
    <Compile Include="DataMgmtForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DataMgmtForm.Designer.cs">
      <DependentUpon>DataMgmtForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DateSelectForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DateSelectForm.Designer.cs">
      <DependentUpon>DateSelectForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DrawArwLine.cs" />
    <Compile Include="DrawDegree45Dn.cs" />
    <Compile Include="DrawDegree45Up.cs" />
    <Compile Include="DrawEllipse.cs" />
    <Compile Include="DrawFibonacciExtLines.cs" />
    <Compile Include="DrawFibonacciLines.cs" />
    <Compile Include="DrawGannFan.cs" />
    <Compile Include="DrawGannLines.cs" />
    <Compile Include="DrawGoldenRatio.cs" />
    <Compile Include="DrawGrnArwDn.cs" />
    <Compile Include="DrawGrnArwLDn.cs" />
    <Compile Include="DrawGrnArwRDn.cs" />
    <Compile Include="DrawLine.cs" />
    <Compile Include="DrawLineD.cs" />
    <Compile Include="DrawLineDExt.cs" />
    <Compile Include="DrawLineDH.cs" />
    <Compile Include="DrawLineH.cs" />
    <Compile Include="DrawLineHExt.cs" />
    <Compile Include="DrawLineP.cs" />
    <Compile Include="DrawLineStyle.cs" />
    <Compile Include="DrawLineType.cs" />
    <Compile Include="DrawLineV.cs" />
    <Compile Include="DrawLineWidth.cs" />
    <Compile Include="DrawMeasureObj.cs" />
    <Compile Include="DrawMode.cs" />
    <Compile Include="DrawObj.cs" />
    <Compile Include="DrawObjParamType.cs" />
    <Compile Include="DrawParamWnd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DrawParamWnd.Designer.cs">
      <DependentUpon>DrawParamWnd.cs</DependentUpon>
    </Compile>
    <Compile Include="DrawPeriodLines.cs" />
    <Compile Include="DrawRange.cs" />
    <Compile Include="DrawRatio.cs" />
    <Compile Include="DrawRedArwLUp.cs" />
    <Compile Include="DrawRedArwRUp.cs" />
    <Compile Include="DrawRedArwUp.cs" />
    <Compile Include="DrawSquare.cs" />
    <Compile Include="DrawSublineParam.cs" />
    <Compile Include="DrawText.cs" />
    <Compile Include="DrawTrendSpeed.cs" />
    <Compile Include="DTValLocation.cs" />
    <Compile Include="FilterCond.cs" />
    <Compile Include="FilterCondItem.cs" />
    <Compile Include="FnDataApiWorker.cs" />
    <Compile Include="FormUISettings.cs" />
    <Compile Include="GraphCtrlStat.cs" />
    <Compile Include="HDFileMgr.cs" />
    <Compile Include="HiLowMarkTextObj.cs" />
    <Compile Include="HisDataPeriodSet.cs" />
    <Compile Include="HisDataSet.cs" />
    <Compile Include="ImportTransForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ImportTransForm.Designer.cs">
      <DependentUpon>ImportTransForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ImportTrans\AutoDownCfmmcFrequencyEnum.cs" />
    <Compile Include="ImportTrans\BindingAcct.cs" />
    <Compile Include="ImportTrans\Captcha\ConnectedComponent.cs" />
    <Compile Include="ImportTrans\Captcha\CutBigComponent.cs" />
    <Compile Include="ImportTrans\Captcha\ImageBeyes.cs" />
    <Compile Include="ImportTrans\Captcha\ImageProcessor.cs" />
    <Compile Include="ImportTrans\Captcha\LevenClassifer.cs" />
    <Compile Include="ImportTrans\Captcha\NaiveBeyes.cs" />
    <Compile Include="ImportTrans\Captcha\Reg.cs" />
    <Compile Include="ImportTrans\CCfmmcRecorderStore.cs" />
    <Compile Include="ImportTrans\CfmmcAcct.cs" />
    <Compile Include="ImportTrans\CfmmcAutoDnldConfig.cs" />
    <Compile Include="ImportTrans\CfmmcRecFieldsEnum.cs" />
    <Compile Include="ImportTrans\CfmmcRecImporter.cs" />
    <Compile Include="ImportTrans\CfmmcRecord.cs" />
    <Compile Include="ImportTrans\CfmmcWebDnloader.cs" />
    <Compile Include="ImportTrans\CWrongNameStore.cs" />
    <Compile Include="ImportTrans\CWrongUserName.cs" />
    <Compile Include="ImportTrans\EDownDay.cs" />
    <Compile Include="ImportTrans\IStoreElement.cs" />
    <Compile Include="ImportTrans\TransData.cs" />
    <Compile Include="ImportTrans\TransFileImporter.cs" />
    <Compile Include="IndCurve.cs" />
    <Compile Include="Indicator.cs" />
    <Compile Include="IndicatorEventHandler.cs" />
    <Compile Include="IndInputDataType.cs" />
    <Compile Include="IndParam.cs" />
    <Compile Include="InfoMineApiWorker.cs" />
    <Compile Include="InfoMineMgr.cs" />
    <Compile Include="KeyModifiers.cs" />
    <Compile Include="KLineType.cs" />
    <Compile Include="LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LoginForm.Designer.cs">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ns10\Attribute2.cs" />
    <Compile Include="ns10\Class193.cs" />
    <Compile Include="ns10\Class467.cs" />
    <Compile Include="ns10\Class479.cs" />
    <Compile Include="ns10\Class556.cs" />
    <Compile Include="ns10\Class9.cs" />
    <Compile Include="ns10\Control3.cs" />
    <Compile Include="ns10\Delegate19.cs" />
    <Compile Include="ns10\Enum28.cs" />
    <Compile Include="ns10\EventArgs30.cs" />
    <Compile Include="ns11\Class13.cs" />
    <Compile Include="ns11\Class284.cs" />
    <Compile Include="ns11\Class336.cs" />
    <Compile Include="ns11\Class380.cs" />
    <Compile Include="ns11\Class411.cs" />
    <Compile Include="ns11\Class415.cs" />
    <Compile Include="ns11\Class422.cs" />
    <Compile Include="ns11\Class423.cs" />
    <Compile Include="ns11\Class449.cs" />
    <Compile Include="ns11\Class48.cs" />
    <Compile Include="ns11\Class508.cs" />
    <Compile Include="ns11\Class53.cs" />
    <Compile Include="ns11\Class531.cs" />
    <Compile Include="ns11\Class63.cs" />
    <Compile Include="ns11\Delegate2.cs" />
    <Compile Include="ns11\Delegate23.cs" />
    <Compile Include="ns11\Delegate9.cs" />
    <Compile Include="ns11\Enum31.cs" />
    <Compile Include="ns11\EventArgs1.cs" />
    <Compile Include="ns11\EventArgs31.cs" />
    <Compile Include="ns11\EventArgs36.cs" />
    <Compile Include="ns11\EventArgs7.cs" />
    <Compile Include="ns11\FormGroupName.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns11\FormGroupName.Designer.cs">
      <DependentUpon>FormGroupName.cs</DependentUpon>
    </Compile>
    <Compile Include="ns11\HotKeyEditCtrl.cs" />
    <Compile Include="ns11\SecurityExceptionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns11\SecurityExceptionForm.Designer.cs">
      <DependentUpon>SecurityExceptionForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ns11\SymbParamCtrl.cs" />
    <Compile Include="ns12\Class14.cs" />
    <Compile Include="ns12\Class191.cs" />
    <Compile Include="ns12\Class382.cs" />
    <Compile Include="ns12\Class413.cs" />
    <Compile Include="ns12\Class418.cs" />
    <Compile Include="ns12\Class481.cs" />
    <Compile Include="ns12\Class529.cs" />
    <Compile Include="ns12\Delegate15.cs" />
    <Compile Include="ns12\Enum7.cs" />
    <Compile Include="ns12\Interface3.cs" />
    <Compile Include="ns13\AddBaoDianWnd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns13\AddBaoDianWnd.Designer.cs">
      <DependentUpon>AddBaoDianWnd.cs</DependentUpon>
    </Compile>
    <Compile Include="ns13\Attribute4.cs" />
    <Compile Include="ns13\BuyRationedShareForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns13\BuyRationedShareForm.Designer.cs">
      <DependentUpon>BuyRationedShareForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ns13\Class15.cs" />
    <Compile Include="ns13\Class393.cs" />
    <Compile Include="ns13\Class439.cs" />
    <Compile Include="ns13\Class463.cs" />
    <Compile Include="ns13\EventArgs34.cs" />
    <Compile Include="ns13\ROpenCnfmWnd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns13\ROpenCnfmWnd.Designer.cs">
      <DependentUpon>ROpenCnfmWnd.cs</DependentUpon>
    </Compile>
    <Compile Include="ns13\UserControlIndParam.cs" />
    <Compile Include="ns14\Class16.cs" />
    <Compile Include="ns14\Class264.cs" />
    <Compile Include="ns14\Class283.cs" />
    <Compile Include="ns14\Class324.cs" />
    <Compile Include="ns14\Class356.cs" />
    <Compile Include="ns14\Class384.cs" />
    <Compile Include="ns14\Class392.cs" />
    <Compile Include="ns14\Class408.cs" />
    <Compile Include="ns14\EventArgs19.cs" />
    <Compile Include="ns14\FilterCondsLoadForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns14\FilterCondsLoadForm.Designer.cs">
      <DependentUpon>FilterCondsLoadForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ns14\Struct5.cs" />
    <Compile Include="ns15\Class184.cs" />
    <Compile Include="ns15\Class19.cs" />
    <Compile Include="ns15\Class416.cs" />
    <Compile Include="ns15\Class512.cs" />
    <Compile Include="ns15\Class58.cs" />
    <Compile Include="ns15\Delegate38.cs" />
    <Compile Include="ns15\Enum33.cs" />
    <Compile Include="ns15\EventArgs0.cs" />
    <Compile Include="ns15\EventArgs17.cs" />
    <Compile Include="ns15\FnRptAnlysPanel.cs" />
    <Compile Include="ns15\FormPickColor.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns15\FormPickColor.Designer.cs">
      <DependentUpon>FormPickColor.cs</DependentUpon>
    </Compile>
    <Compile Include="ns15\Interface1.cs" />
    <Compile Include="ns16\Class178.cs" />
    <Compile Include="ns16\Class20.cs" />
    <Compile Include="ns16\Class239.cs" />
    <Compile Include="ns16\Class414.cs" />
    <Compile Include="ns16\Class420.cs" />
    <Compile Include="ns16\Class54.cs" />
    <Compile Include="ns16\Delegate12.cs" />
    <Compile Include="ns16\Enum12.cs" />
    <Compile Include="ns16\Enum2.cs" />
    <Compile Include="ns16\ExceptionReportingForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns16\ExceptionReportingForm.Designer.cs">
      <DependentUpon>ExceptionReportingForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ns17\Class21.cs" />
    <Compile Include="ns17\Class513.cs" />
    <Compile Include="ns17\Class517.cs" />
    <Compile Include="ns17\Enum20.cs" />
    <Compile Include="ns17\EventArgs15.cs" />
    <Compile Include="ns17\EventArgs24.cs" />
    <Compile Include="ns17\EventArgs5.cs" />
    <Compile Include="ns17\ResetDataWnd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns17\ResetDataWnd.Designer.cs">
      <DependentUpon>ResetDataWnd.cs</DependentUpon>
    </Compile>
    <Compile Include="ns18\Class22.cs" />
    <Compile Include="ns18\Class263.cs" />
    <Compile Include="ns18\Class281.cs" />
    <Compile Include="ns18\Class394.cs" />
    <Compile Include="ns18\Class437.cs" />
    <Compile Include="ns18\Class507.cs" />
    <Compile Include="ns18\Class510.cs" />
    <Compile Include="ns18\Class515.cs" />
    <Compile Include="ns18\Delegate10.cs" />
    <Compile Include="ns18\Delegate28.cs" />
    <Compile Include="ns18\FilterCondsSaveForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns18\FilterCondsSaveForm.Designer.cs">
      <DependentUpon>FilterCondsSaveForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ns19\Attribute8.cs" />
    <Compile Include="ns19\Class180.cs" />
    <Compile Include="ns19\Class396.cs" />
    <Compile Include="ns19\Class511.cs" />
    <Compile Include="ns19\Class536.cs" />
    <Compile Include="ns19\Delegate39.cs" />
    <Compile Include="ns19\Enum0.cs" />
    <Compile Include="ns19\Enum25.cs" />
    <Compile Include="ns19\EventArgs6.cs" />
    <Compile Include="ns20\Class285.cs" />
    <Compile Include="ns20\Class45.cs" />
    <Compile Include="ns20\Class67.cs" />
    <Compile Include="ns20\Delegate3.cs" />
    <Compile Include="ns20\DrawTxtWnd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns20\DrawTxtWnd.Designer.cs">
      <DependentUpon>DrawTxtWnd.cs</DependentUpon>
    </Compile>
    <Compile Include="ns20\Enum16.cs" />
    <Compile Include="ns20\EventArgs14.cs" />
    <Compile Include="ns20\Exception0.cs" />
    <Compile Include="ns20\Struct3.cs" />
    <Compile Include="ns21\Class347.cs" />
    <Compile Include="ns21\Delegate0.cs" />
    <Compile Include="ns21\Delegate30.cs" />
    <Compile Include="ns21\EventArgs16.cs" />
    <Compile Include="ns21\Interface0.cs" />
    <Compile Include="ns22\Class297.cs" />
    <Compile Include="ns22\Class391.cs" />
    <Compile Include="ns22\Class417.cs" />
    <Compile Include="ns22\Class46.cs" />
    <Compile Include="ns22\Class488.cs" />
    <Compile Include="ns22\Class530.cs" />
    <Compile Include="ns22\Class64.cs" />
    <Compile Include="ns22\CreateAcctForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns22\CreateAcctForm.Designer.cs">
      <DependentUpon>CreateAcctForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ns22\CreateCfmmcAcctFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns22\CreateCfmmcAcctFrm.Designer.cs">
      <DependentUpon>CreateCfmmcAcctFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="ns22\Delegate25.cs" />
    <Compile Include="ns22\Delegate29.cs" />
    <Compile Include="ns22\Delegate8.cs" />
    <Compile Include="ns22\Enum1.cs" />
    <Compile Include="ns22\Enum32.cs" />
    <Compile Include="ns22\Enum34.cs" />
    <Compile Include="ns22\EventArgs4.cs" />
    <Compile Include="ns22\MsgWindow.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns22\MsgWindow.Designer.cs">
      <DependentUpon>MsgWindow.cs</DependentUpon>
    </Compile>
    <Compile Include="ns23\AboutForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns23\AboutForm.Designer.cs">
      <DependentUpon>AboutForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ns23\Attribute7.cs" />
    <Compile Include="ns23\Class183.cs" />
    <Compile Include="ns23\Class287.cs" />
    <Compile Include="ns23\Class300.cs" />
    <Compile Include="ns23\Class348.cs" />
    <Compile Include="ns23\Class398.cs" />
    <Compile Include="ns23\Class421.cs" />
    <Compile Include="ns23\Class541.cs" />
    <Compile Include="ns23\Control4.cs" />
    <Compile Include="ns23\Delegate5.cs" />
    <Compile Include="ns23\Enum3.cs" />
    <Compile Include="ns23\EventArgs32.cs" />
    <Compile Include="ns24\Attribute9.cs" />
    <Compile Include="ns24\Class208.cs" />
    <Compile Include="ns24\Class386.cs" />
    <Compile Include="ns24\Class387.cs" />
    <Compile Include="ns24\Class399.cs" />
    <Compile Include="ns24\Class514.cs" />
    <Compile Include="ns24\Class544.cs" />
    <Compile Include="ns24\Class547.cs" />
    <Compile Include="ns24\Enum36.cs" />
    <Compile Include="ns24\Enum4.cs" />
    <Compile Include="ns24\HotKeyCfgForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns24\HotKeyCfgForm.Designer.cs">
      <DependentUpon>HotKeyCfgForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ns25\Class181.cs" />
    <Compile Include="ns25\Class291.cs" />
    <Compile Include="ns25\Class440.cs" />
    <Compile Include="ns25\Class543.cs" />
    <Compile Include="ns25\Enum6.cs" />
    <Compile Include="ns25\Enum8.cs" />
    <Compile Include="ns25\EventArgs13.cs" />
    <Compile Include="ns25\VideoPanel.cs" />
    <Compile Include="ns26\Class385.cs" />
    <Compile Include="ns26\Class506.cs" />
    <Compile Include="ns26\Class59.cs" />
    <Compile Include="ns26\Delegate14.cs" />
    <Compile Include="ns26\DrawObjDTVal.cs" />
    <Compile Include="ns26\Enum17.cs" />
    <Compile Include="ns26\EventArgs2.cs" />
    <Compile Include="ns26\EventArgs29.cs" />
    <Compile Include="ns27\Class290.cs" />
    <Compile Include="ns27\Class395.cs" />
    <Compile Include="ns27\Class483.cs" />
    <Compile Include="ns27\Class49.cs" />
    <Compile Include="ns27\Class494.cs" />
    <Compile Include="ns27\Delegate1.cs" />
    <Compile Include="ns27\Delegate20.cs" />
    <Compile Include="ns27\Delegate24.cs" />
    <Compile Include="ns27\Delegate33.cs" />
    <Compile Include="ns27\FormICON.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns27\FormICON.Designer.cs">
      <DependentUpon>FormICON.cs</DependentUpon>
    </Compile>
    <Compile Include="ns27\FormSetInd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns27\FormSetInd.Designer.cs">
      <DependentUpon>FormSetInd.cs</DependentUpon>
    </Compile>
    <Compile Include="ns27\QuickWnd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns27\QuickWnd.Designer.cs">
      <DependentUpon>QuickWnd.cs</DependentUpon>
    </Compile>
    <Compile Include="ns28\Attribute5.cs" />
    <Compile Include="ns28\Class182.cs" />
    <Compile Include="ns28\Class292.cs" />
    <Compile Include="ns28\Class308.cs" />
    <Compile Include="ns28\Class372.cs" />
    <Compile Include="ns28\Class409.cs" />
    <Compile Include="ns28\Class537.cs" />
    <Compile Include="ns28\Class55.cs" />
    <Compile Include="ns28\Enum35.cs" />
    <Compile Include="ns28\EventArgs21.cs" />
    <Compile Include="ns28\EventArgs27.cs" />
    <Compile Include="ns28\Form0.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns28\Form0.Designer.cs">
      <DependentUpon>Form0.cs</DependentUpon>
    </Compile>
    <Compile Include="ns28\SetCondOrdForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns28\SetCondOrdForm.Designer.cs">
      <DependentUpon>SetCondOrdForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ns29\Class223.cs" />
    <Compile Include="ns29\Class289.cs" />
    <Compile Include="ns29\Class539.cs" />
    <Compile Include="ns29\Class65.cs" />
    <Compile Include="ns29\Control0.cs" />
    <Compile Include="ns29\Enum11.cs" />
    <Compile Include="ns29\EventArgs22.cs" />
    <Compile Include="ns29\TickPanel.cs" />
    <Compile Include="ns2\Attribute6.cs" />
    <Compile Include="ns2\Class1.cs" />
    <Compile Include="ns2\Class448.cs" />
    <Compile Include="ns2\Class478.cs" />
    <Compile Include="ns2\Class52.cs" />
    <Compile Include="ns2\EventArgs23.cs" />
    <Compile Include="ns30\Class282.cs" />
    <Compile Include="ns30\Class412.cs" />
    <Compile Include="ns30\Class419.cs" />
    <Compile Include="ns30\Class518.cs" />
    <Compile Include="ns30\Class540.cs" />
    <Compile Include="ns30\Class545.cs" />
    <Compile Include="ns30\Class68.cs" />
    <Compile Include="ns30\Control5.cs" />
    <Compile Include="ns30\Delegate13.cs" />
    <Compile Include="ns30\Delegate27.cs" />
    <Compile Include="ns30\Enum18.cs" />
    <Compile Include="ns30\EventArgs26.cs" />
    <Compile Include="ns30\Interface4.cs" />
    <Compile Include="ns31\Class410.cs" />
    <Compile Include="ns31\Control6.cs" />
    <Compile Include="ns31\Delegate18.cs" />
    <Compile Include="ns31\Delegate6.cs" />
    <Compile Include="ns31\EventArgs35.cs" />
    <Compile Include="ns31\PageSaveAsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns31\PageSaveAsForm.Designer.cs">
      <DependentUpon>PageSaveAsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ns32\Attribute3.cs" />
    <Compile Include="ns32\Class192.cs" />
    <Compile Include="ns32\Class278.cs" />
    <Compile Include="ns32\Class302.cs" />
    <Compile Include="ns32\Class542.cs" />
    <Compile Include="ns32\Struct4.cs" />
    <Compile Include="ns33\Class296.cs" />
    <Compile Include="ns33\Class388.cs" />
    <Compile Include="ns33\Class397.cs" />
    <Compile Include="ns33\Delegate21.cs" />
    <Compile Include="ns33\Delegate22.cs" />
    <Compile Include="ns33\DrawOdrWnd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns33\DrawOdrWnd.Designer.cs">
      <DependentUpon>DrawOdrWnd.cs</DependentUpon>
    </Compile>
    <Compile Include="ns33\EditOrderForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns33\EditOrderForm.Designer.cs">
      <DependentUpon>EditOrderForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ns33\EventArgs20.cs" />
    <Compile Include="ns33\EventArgs33.cs" />
    <Compile Include="ns33\Interface2.cs" />
    <Compile Include="ns34\Class516.cs" />
    <Compile Include="ns3\Class2.cs" />
    <Compile Include="ns3\Class224.cs" />
    <Compile Include="ns3\Class294.cs" />
    <Compile Include="ns3\Class301.cs" />
    <Compile Include="ns3\Class383.cs" />
    <Compile Include="ns3\Class470.cs" />
    <Compile Include="ns3\Class532.cs" />
    <Compile Include="ns3\Delegate37.cs" />
    <Compile Include="ns3\EventArgs12.cs" />
    <Compile Include="ns3\EventArgs28.cs" />
    <Compile Include="ns3\SetNPeriodWnd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns3\SetNPeriodWnd.Designer.cs">
      <DependentUpon>SetNPeriodWnd.cs</DependentUpon>
    </Compile>
    <Compile Include="ns4\Class18.cs" />
    <Compile Include="ns4\Class3.cs" />
    <Compile Include="ns4\Class450.cs" />
    <Compile Include="ns4\Class480.cs" />
    <Compile Include="ns4\Class482.cs" />
    <Compile Include="ns4\ColorComboBox.cs" />
    <Compile Include="ns4\Enum13.cs" />
    <Compile Include="ns4\Enum14.cs" />
    <Compile Include="ns4\Enum5.cs" />
    <Compile Include="ns4\EventArgs11.cs" />
    <Compile Include="ns4\EventArgs18.cs" />
    <Compile Include="ns5\Attribute10.cs" />
    <Compile Include="ns5\Class286.cs" />
    <Compile Include="ns5\Class381.cs" />
    <Compile Include="ns5\Class4.cs" />
    <Compile Include="ns5\Class44.cs" />
    <Compile Include="ns5\Class466.cs" />
    <Compile Include="ns5\Class471.cs" />
    <Compile Include="ns5\Class509.cs" />
    <Compile Include="ns5\EventArgs9.cs" />
    <Compile Include="ns5\SetSymbParamForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns5\SetSymbParamForm.Designer.cs">
      <DependentUpon>SetSymbParamForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ns6\Class179.cs" />
    <Compile Include="ns6\Class306.cs" />
    <Compile Include="ns6\Class5.cs" />
    <Compile Include="ns6\Class555.cs" />
    <Compile Include="ns6\DrawWnd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns6\DrawWnd.Designer.cs">
      <DependentUpon>DrawWnd.cs</DependentUpon>
    </Compile>
    <Compile Include="ns6\EventArgs37.cs" />
    <Compile Include="ns6\TransTabCtrl_1.cs" />
    <Compile Include="ns7\Class389.cs" />
    <Compile Include="ns7\Class390.cs" />
    <Compile Include="ns7\Class41.cs" />
    <Compile Include="ns7\Class441.cs" />
    <Compile Include="ns7\Class51.cs" />
    <Compile Include="ns7\Class546.cs" />
    <Compile Include="ns7\Class548.cs" />
    <Compile Include="ns7\Class6.cs" />
    <Compile Include="ns7\Enum15.cs" />
    <Compile Include="ns8\CfmmcWebFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns8\CfmmcWebFrm.Designer.cs">
      <DependentUpon>CfmmcWebFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="ns8\Class323.cs" />
    <Compile Include="ns8\Class538.cs" />
    <Compile Include="ns8\Class557.cs" />
    <Compile Include="ns8\Class7.cs" />
    <Compile Include="ns8\Delegate17.cs" />
    <Compile Include="ns8\Delegate26.cs" />
    <Compile Include="ns8\Enum30.cs" />
    <Compile Include="ns8\EventArgs8.cs" />
    <Compile Include="ns9\BaoDianPanel.cs" />
    <Compile Include="ns9\Class400.cs" />
    <Compile Include="ns9\Class50.cs" />
    <Compile Include="ns9\Class56.cs" />
    <Compile Include="ns9\Class66.cs" />
    <Compile Include="ns9\Class8.cs" />
    <Compile Include="ns9\Control2.cs" />
    <Compile Include="ns9\Delegate16.cs" />
    <Compile Include="ns9\Delegate4.cs" />
    <Compile Include="ns9\Enum26.cs" />
    <Compile Include="ns9\EventArgs10.cs" />
    <Compile Include="ns9\EventArgs25.cs" />
    <Compile Include="ns9\EventArgs3.cs" />
    <Compile Include="ns9\FormDelInd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ns9\FormDelInd.Designer.cs">
      <DependentUpon>FormDelInd.cs</DependentUpon>
    </Compile>
    <Compile Include="ns9\QuoteTabs.cs" />
    <Compile Include="OrderStatus.cs" />
    <Compile Include="OrderType.cs" />
    <Compile Include="PageSelWnd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PageSelWnd.Designer.cs">
      <DependentUpon>PageSelWnd.cs</DependentUpon>
    </Compile>
    <Compile Include="PageUISettingCancelEventArgs.cs" />
    <Compile Include="PageUISettingEventHandler.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <DependentUpon>Settings.settings</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="QuickWndItem.cs" />
    <Compile Include="RangeSlider.cs" />
    <Compile Include="RationedShareTreatmt.cs" />
    <Compile Include="ScrShotPictureBox.cs" />
    <Compile Include="SetStopLimitForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SetStopLimitForm.Designer.cs">
      <DependentUpon>SetStopLimitForm.cs</DependentUpon>
    </Compile>
    <Compile Include="SettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SettingsForm.Designer.cs">
      <DependentUpon>SettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ShowMktSymb.cs" />
    <Compile Include="SIndicator\FormIndEditer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SIndicator\FormIndEditer.Designer.cs">
      <DependentUpon>FormIndEditer.cs</DependentUpon>
    </Compile>
    <Compile Include="SIndicator\FormIndFunction.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SIndicator\FormIndFunction.Designer.cs">
      <DependentUpon>FormIndFunction.cs</DependentUpon>
    </Compile>
    <Compile Include="SIndicator\FormIndMgr.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SIndicator\FormIndMgr.Designer.cs">
      <DependentUpon>FormIndMgr.cs</DependentUpon>
    </Compile>
    <Compile Include="SIndicator\HToken.cs" />
    <Compile Include="SIndicator\IndEx.cs" />
    <Compile Include="SIndicator\ParserEnvironment.cs" />
    <Compile Include="SIndicator\ShapeCurve.cs" />
    <Compile Include="SIndicator\ShapeDrawICON.cs" />
    <Compile Include="SIndicator\Tokenes.cs" />
    <Compile Include="SIndicator\TreeFunction.cs" />
    <Compile Include="SIndicator\TreeSentence.cs" />
    <Compile Include="SIndicator\UserDefineFileMgr.cs" />
    <Compile Include="SIndicator\UserDefineInd.cs" />
    <Compile Include="SIndicator\UserDefineIndGroup.cs" />
    <Compile Include="SIndicator\UserDefineIndScript.cs" />
    <Compile Include="SmartAssembly\Shared\ReportHelper\OsInformation.cs" />
    <Compile Include="SmartAssembly\Shared\ReportHelper\OsVersionInformation.cs" />
    <Compile Include="SmartAssembly\SmartExceptionsCore\ReportingService.cs" />
    <Compile Include="SmartAssembly\SmartExceptionsCore\SmartStackFrame.cs" />
    <Compile Include="SmartAssembly\SmartExceptionsCore\UploadReportLoginService.cs" />
    <Compile Include="SplitContainerParam.cs" />
    <Compile Include="SrvParams.cs" />
    <Compile Include="StartEndDT.cs" />
    <Compile Include="StkBegEndDate.cs" />
    <Compile Include="StkSymbol.cs" />
    <Compile Include="StockRestorationMethod.cs" />
    <Compile Include="StSplit.cs" />
    <Compile Include="SymbDataSet.cs" />
    <Compile Include="SymbFilterApiWorker.cs" />
    <Compile Include="SymbFilterPanel.cs" />
    <Compile Include="SymbMgr.cs" />
    <Compile Include="SyncParam.cs" />
    <Compile Include="TApp.cs" />
    <Compile Include="TimeUnit.cs" />
    <Compile Include="TOdrLine.cs" />
    <Compile Include="Trading\Account.cs" />
    <Compile Include="Trading\AcctSymbol.cs" />
    <Compile Include="Trading\CondOrder.cs" />
    <Compile Include="Trading\Order.cs" />
    <Compile Include="Trading\ShownCondOrder.cs" />
    <Compile Include="Trading\ShownHisTrans.cs" />
    <Compile Include="Trading\ShownOpenTrans.cs" />
    <Compile Include="Trading\ShownOrder.cs" />
    <Compile Include="Trading\ShownSLOrder.cs" />
    <Compile Include="Trading\SymbolProfit.cs" />
    <Compile Include="Trading\Transaction.cs" />
    <Compile Include="Trading\TranStock.cs" />
    <Compile Include="TransArrow.cs" />
    <Compile Include="TransArrowType.cs" />
    <Compile Include="TransTabCtrl.cs" />
    <Compile Include="TransTabCtrlParam.cs" />
    <Compile Include="TransTabs.cs" />
    <Compile Include="TrdAnalysisPanel.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="BkupSyncCnfmWnd.resources" />
    <EmbeddedResource Include="BlindTestForm.resources" />
    <EmbeddedResource Include="DataMgmtForm.resources" />
    <EmbeddedResource Include="DateSelectForm.resources" />
    <EmbeddedResource Include="DrawParamWnd.resources" />
    <EmbeddedResource Include="FilterCondItem.resources" />
    <EmbeddedResource Include="ImportTransForm.resources" />
    <EmbeddedResource Include="LoginForm.resources" />
    <EmbeddedResource Include="MainForm.resources" />
    <EmbeddedResource Include="ns11\FormGroupName.resources" />
    <EmbeddedResource Include="ns11\HotKeyEditCtrl.resources" />
    <EmbeddedResource Include="ns11\SecurityExceptionForm.resources" />
    <EmbeddedResource Include="ns11\SymbParamCtrl.resources" />
    <EmbeddedResource Include="ns13\AddBaoDianWnd.resources" />
    <EmbeddedResource Include="ns13\BuyRationedShareForm.resources" />
    <EmbeddedResource Include="ns13\ROpenCnfmWnd.resources" />
    <EmbeddedResource Include="ns13\UserControlIndParam.resources" />
    <EmbeddedResource Include="ns14\FilterCondsLoadForm.resources" />
    <EmbeddedResource Include="ns15\FnRptAnlysPanel.resources" />
    <EmbeddedResource Include="ns15\FormPickColor.resources" />
    <EmbeddedResource Include="ns16\ExceptionReportingForm.resources" />
    <EmbeddedResource Include="ns17\ResetDataWnd.resources" />
    <EmbeddedResource Include="ns18\FilterCondsSaveForm.resources" />
    <EmbeddedResource Include="ns20\DrawTxtWnd.resources" />
    <EmbeddedResource Include="ns22\Class488.resources" />
    <EmbeddedResource Include="ns22\CreateAcctForm.resources" />
    <EmbeddedResource Include="ns22\CreateCfmmcAcctFrm.resources" />
    <EmbeddedResource Include="ns22\MsgWindow.resources" />
    <EmbeddedResource Include="ns23\AboutForm.resources" />
    <EmbeddedResource Include="ns24\HotKeyCfgForm.resources" />
    <EmbeddedResource Include="ns25\VideoPanel.resources" />
    <EmbeddedResource Include="ns26\DrawObjDTVal.resources" />
    <EmbeddedResource Include="ns27\FormICON.resources" />
    <EmbeddedResource Include="ns27\FormSetInd.resources" />
    <EmbeddedResource Include="ns27\QuickWnd.resources" />
    <EmbeddedResource Include="ns28\Class372.resources" />
    <EmbeddedResource Include="ns28\SetCondOrdForm.resources" />
    <EmbeddedResource Include="ns29\TickPanel.resources" />
    <EmbeddedResource Include="ns31\PageSaveAsForm.resources" />
    <EmbeddedResource Include="ns33\DrawOdrWnd.resources" />
    <EmbeddedResource Include="ns33\EditOrderForm.resources" />
    <EmbeddedResource Include="ns3\SetNPeriodWnd.resources" />
    <EmbeddedResource Include="ns4\ColorComboBox.resources" />
    <EmbeddedResource Include="ns5\SetSymbParamForm.resources" />
    <EmbeddedResource Include="ns6\DrawWnd.resources" />
    <EmbeddedResource Include="ns6\TransTabCtrl_1.resources" />
    <EmbeddedResource Include="ns7\Class548.resources" />
    <EmbeddedResource Include="ns8\CfmmcWebFrm.resources" />
    <EmbeddedResource Include="ns9\BaoDianPanel.resources" />
    <EmbeddedResource Include="ns9\FormDelInd.resources" />
    <EmbeddedResource Include="ns9\QuoteTabs.resources" />
    <EmbeddedResource Include="PageSelWnd.resources" />
    <EmbeddedResource Include="Resources\ICON\1.png" />
    <EmbeddedResource Include="Resources\ICON\10.png" />
    <EmbeddedResource Include="Resources\ICON\11.png" />
    <EmbeddedResource Include="Resources\ICON\12.png" />
    <EmbeddedResource Include="Resources\ICON\13.png" />
    <EmbeddedResource Include="Resources\ICON\14.png" />
    <EmbeddedResource Include="Resources\ICON\15.png" />
    <EmbeddedResource Include="Resources\ICON\16.png" />
    <EmbeddedResource Include="Resources\ICON\17.png" />
    <EmbeddedResource Include="Resources\ICON\18.png" />
    <EmbeddedResource Include="Resources\ICON\19.png" />
    <EmbeddedResource Include="Resources\ICON\2.png" />
    <EmbeddedResource Include="Resources\ICON\20.png" />
    <EmbeddedResource Include="Resources\ICON\21.png" />
    <EmbeddedResource Include="Resources\ICON\22.png" />
    <EmbeddedResource Include="Resources\ICON\23.png" />
    <EmbeddedResource Include="Resources\ICON\24.png" />
    <EmbeddedResource Include="Resources\ICON\25.png" />
    <EmbeddedResource Include="Resources\ICON\26.png" />
    <EmbeddedResource Include="Resources\ICON\27.png" />
    <EmbeddedResource Include="Resources\ICON\28.png" />
    <EmbeddedResource Include="Resources\ICON\29.png" />
    <EmbeddedResource Include="Resources\ICON\3.png" />
    <EmbeddedResource Include="Resources\ICON\30.png" />
    <EmbeddedResource Include="Resources\ICON\31.png" />
    <EmbeddedResource Include="Resources\ICON\32.png" />
    <EmbeddedResource Include="Resources\ICON\33.png" />
    <EmbeddedResource Include="Resources\ICON\34.png" />
    <EmbeddedResource Include="Resources\ICON\35.png" />
    <EmbeddedResource Include="Resources\ICON\36.png" />
    <EmbeddedResource Include="Resources\ICON\37.png" />
    <EmbeddedResource Include="Resources\ICON\38.png" />
    <EmbeddedResource Include="Resources\ICON\39.png" />
    <EmbeddedResource Include="Resources\ICON\4.png" />
    <EmbeddedResource Include="Resources\ICON\40.png" />
    <EmbeddedResource Include="Resources\ICON\41.png" />
    <EmbeddedResource Include="Resources\ICON\42.png" />
    <EmbeddedResource Include="Resources\ICON\43.png" />
    <EmbeddedResource Include="Resources\ICON\44.png" />
    <EmbeddedResource Include="Resources\ICON\45.png" />
    <EmbeddedResource Include="Resources\ICON\46.png" />
    <EmbeddedResource Include="Resources\ICON\47.png" />
    <EmbeddedResource Include="Resources\ICON\48.png" />
    <EmbeddedResource Include="Resources\ICON\49.png" />
    <EmbeddedResource Include="Resources\ICON\5.png" />
    <EmbeddedResource Include="Resources\ICON\50.png" />
    <EmbeddedResource Include="Resources\ICON\51.png" />
    <EmbeddedResource Include="Resources\ICON\6.png" />
    <EmbeddedResource Include="Resources\ICON\7.png" />
    <EmbeddedResource Include="Resources\ICON\8.png" />
    <EmbeddedResource Include="Resources\ICON\9.png" />
    <EmbeddedResource Include="SetStopLimitForm.resources" />
    <EmbeddedResource Include="SettingsForm.resources" />
    <EmbeddedResource Include="SIndicator\FormIndEditer.resources" />
    <EmbeddedResource Include="SIndicator\FormIndFunction.resources" />
    <EmbeddedResource Include="SIndicator\FormIndMgr.resources" />
    <EmbeddedResource Include="SmartAssembly\SmartExceptionsCore\Resources\current.png" />
    <EmbeddedResource Include="SmartAssembly\SmartExceptionsCore\Resources\data.png" />
    <EmbeddedResource Include="SmartAssembly\SmartExceptionsCore\Resources\default.ico" />
    <EmbeddedResource Include="SmartAssembly\SmartExceptionsCore\Resources\error.png" />
    <EmbeddedResource Include="SmartAssembly\SmartExceptionsCore\Resources\error16.png" />
    <EmbeddedResource Include="SmartAssembly\SmartExceptionsCore\Resources\network.png" />
    <EmbeddedResource Include="SmartAssembly\SmartExceptionsCore\Resources\ok.png" />
    <EmbeddedResource Include="SmartAssembly\SmartExceptionsCore\Resources\warning16.png" />
    <EmbeddedResource Include="SmartAssembly\SmartExceptionsCore\Resources\{logo}.png" />
    <EmbeddedResource Include="SymbFilterPanel.resources" />
    <EmbeddedResource Include="TestForm.resources" />
    <EmbeddedResource Include="TransTabCtrl.resources" />
    <EmbeddedResource Include="TransTabs.resources" />
    <EmbeddedResource Include="TrdAnalysisPanel.resources" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="app.manifest" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>