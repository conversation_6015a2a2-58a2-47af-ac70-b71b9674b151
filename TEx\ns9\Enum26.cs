﻿using System;

namespace ns9
{
	// Token: 0x02000327 RID: 807
	internal enum Enum26
	{
		// Token: 0x0400108F RID: 4239
		const_0,
		// Token: 0x04001090 RID: 4240
		const_1,
		// Token: 0x04001091 RID: 4241
		const_2,
		// Token: 0x04001092 RID: 4242
		const_3,
		// Token: 0x04001093 RID: 4243
		const_4,
		// Token: 0x04001094 RID: 4244
		const_5,
		// Token: 0x04001095 RID: 4245
		const_6,
		// Token: 0x04001096 RID: 4246
		const_7,
		// Token: 0x04001097 RID: 4247
		const_8,
		// Token: 0x04001098 RID: 4248
		const_9,
		// Token: 0x04001099 RID: 4249
		const_10,
		// Token: 0x0400109A RID: 4250
		const_11,
		// Token: 0x0400109B RID: 4251
		const_12,
		// Token: 0x0400109C RID: 4252
		const_13,
		// Token: 0x0400109D RID: 4253
		const_14,
		// Token: 0x0400109E RID: 4254
		const_15,
		// Token: 0x0400109F RID: 4255
		const_16,
		// Token: 0x040010A0 RID: 4256
		const_17,
		// Token: 0x040010A1 RID: 4257
		const_18,
		// Token: 0x040010A2 RID: 4258
		const_19,
		// Token: 0x040010A3 RID: 4259
		const_20,
		// Token: 0x040010A4 RID: 4260
		const_21,
		// Token: 0x040010A5 RID: 4261
		const_22,
		// Token: 0x040010A6 RID: 4262
		const_23,
		// Token: 0x040010A7 RID: 4263
		const_24,
		// Token: 0x040010A8 RID: 4264
		const_25,
		// Token: 0x040010A9 RID: 4265
		const_26,
		// Token: 0x040010AA RID: 4266
		const_27,
		// Token: 0x040010AB RID: 4267
		const_28,
		// Token: 0x040010AC RID: 4268
		const_29,
		// Token: 0x040010AD RID: 4269
		const_30,
		// Token: 0x040010AE RID: 4270
		const_31,
		// Token: 0x040010AF RID: 4271
		const_32,
		// Token: 0x040010B0 RID: 4272
		const_33,
		// Token: 0x040010B1 RID: 4273
		const_34,
		// Token: 0x040010B2 RID: 4274
		const_35,
		// Token: 0x040010B3 RID: 4275
		const_36,
		// Token: 0x040010B4 RID: 4276
		const_37,
		// Token: 0x040010B5 RID: 4277
		const_38,
		// Token: 0x040010B6 RID: 4278
		const_39,
		// Token: 0x040010B7 RID: 4279
		const_40
	}
}
