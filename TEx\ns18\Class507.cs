﻿using System;

namespace ns18
{
	// Token: 0x020003B2 RID: 946
	internal sealed class Class507
	{
		// Token: 0x17000692 RID: 1682
		// (get) Token: 0x06002675 RID: 9845 RVA: 0x000FBEA4 File Offset: 0x000FA0A4
		// (set) Token: 0x06002676 RID: 9846 RVA: 0x0000EAD7 File Offset: 0x0000CCD7
		public int SymbID
		{
			get
			{
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
			}
		}

		// Token: 0x17000693 RID: 1683
		// (get) Token: 0x06002677 RID: 9847 RVA: 0x000FBEBC File Offset: 0x000FA0BC
		// (set) Token: 0x06002678 RID: 9848 RVA: 0x0000EAE2 File Offset: 0x0000CCE2
		public string SymbCode
		{
			get
			{
				return this.string_0;
			}
			set
			{
				this.string_0 = value;
			}
		}

		// Token: 0x17000694 RID: 1684
		// (get) Token: 0x06002679 RID: 9849 RVA: 0x000FBED4 File Offset: 0x000FA0D4
		// (set) Token: 0x0600267A RID: 9850 RVA: 0x0000EAED File Offset: 0x0000CCED
		public decimal? StopPoints
		{
			get
			{
				return this.nullable_0;
			}
			set
			{
				this.nullable_0 = value;
			}
		}

		// Token: 0x17000695 RID: 1685
		// (get) Token: 0x0600267B RID: 9851 RVA: 0x000FBEEC File Offset: 0x000FA0EC
		// (set) Token: 0x0600267C RID: 9852 RVA: 0x0000EAF8 File Offset: 0x0000CCF8
		public decimal? LimitPoints
		{
			get
			{
				return this.nullable_1;
			}
			set
			{
				this.nullable_1 = value;
			}
		}

		// Token: 0x04001285 RID: 4741
		private int int_0;

		// Token: 0x04001286 RID: 4742
		private string string_0;

		// Token: 0x04001287 RID: 4743
		private decimal? nullable_0;

		// Token: 0x04001288 RID: 4744
		private decimal? nullable_1;
	}
}
