﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace ns10
{
	// Token: 0x02000015 RID: 21
	[CompilerGenerated]
	internal sealed class Class9<T, U, V>
	{
		// Token: 0x1700002D RID: 45
		// (get) Token: 0x06000079 RID: 121 RVA: 0x00011AB0 File Offset: 0x0000FCB0
		public T code
		{
			get
			{
				return this.gparam_0;
			}
		}

		// Token: 0x1700002E RID: 46
		// (get) Token: 0x0600007A RID: 122 RVA: 0x00011AC8 File Offset: 0x0000FCC8
		public U start_date
		{
			get
			{
				return this.gparam_1;
			}
		}

		// Token: 0x1700002F RID: 47
		// (get) Token: 0x0600007B RID: 123 RVA: 0x00011AE0 File Offset: 0x0000FCE0
		public V end_date
		{
			get
			{
				return this.gparam_2;
			}
		}

		// Token: 0x0600007C RID: 124 RVA: 0x00002C49 File Offset: 0x00000E49
		[DebuggerHidden]
		public Class9(T gparam_3, U gparam_4, V gparam_5)
		{
			this.gparam_0 = gparam_3;
			this.gparam_1 = gparam_4;
			this.gparam_2 = gparam_5;
		}

		// Token: 0x0600007D RID: 125 RVA: 0x00011AF8 File Offset: 0x0000FCF8
		[DebuggerHidden]
		public bool Equals(object obj)
		{
			Class9<T, U, V> @class = obj as Class9<T, U, V>;
			bool result;
			if (@class != null && EqualityComparer<T>.Default.Equals(this.gparam_0, @class.gparam_0) && EqualityComparer<U>.Default.Equals(this.gparam_1, @class.gparam_1))
			{
				result = EqualityComparer<V>.Default.Equals(this.gparam_2, @class.gparam_2);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600007E RID: 126 RVA: 0x00011B60 File Offset: 0x0000FD60
		[DebuggerHidden]
		public int GetHashCode()
		{
			return ((-204461928 + EqualityComparer<T>.Default.GetHashCode(this.gparam_0)) * -********** + EqualityComparer<U>.Default.GetHashCode(this.gparam_1)) * -********** + EqualityComparer<V>.Default.GetHashCode(this.gparam_2);
		}

		// Token: 0x0600007F RID: 127 RVA: 0x00011BB8 File Offset: 0x0000FDB8
		[DebuggerHidden]
		public string ToString()
		{
			IFormatProvider provider = null;
			string format = "{{ code = {0}, start_date = {1}, end_date = {2} }}";
			object[] array = new object[3];
			int num = 0;
			T t = this.gparam_0;
			ref T ptr = ref t;
			T t2 = default(T);
			object obj;
			if (t2 == null)
			{
				t2 = t;
				ptr = ref t2;
				if (t2 == null)
				{
					obj = null;
					goto IL_46;
				}
			}
			obj = ptr.ToString();
			IL_46:
			array[num] = obj;
			int num2 = 1;
			U u = this.gparam_1;
			ref U ptr2 = ref u;
			U u2 = default(U);
			object obj2;
			if (u2 == null)
			{
				u2 = u;
				ptr2 = ref u2;
				if (u2 == null)
				{
					obj2 = null;
					goto IL_81;
				}
			}
			obj2 = ptr2.ToString();
			IL_81:
			array[num2] = obj2;
			int num3 = 2;
			V v = this.gparam_2;
			ref V ptr3 = ref v;
			V v2 = default(V);
			object obj3;
			if (v2 == null)
			{
				v2 = v;
				ptr3 = ref v2;
				if (v2 == null)
				{
					obj3 = null;
					goto IL_C0;
				}
			}
			obj3 = ptr3.ToString();
			IL_C0:
			array[num3] = obj3;
			return string.Format(provider, format, array);
		}

		// Token: 0x0400002D RID: 45
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly T gparam_0;

		// Token: 0x0400002E RID: 46
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly U gparam_1;

		// Token: 0x0400002F RID: 47
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private readonly V gparam_2;
	}
}
