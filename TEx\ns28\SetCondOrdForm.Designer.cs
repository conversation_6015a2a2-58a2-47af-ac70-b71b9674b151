﻿namespace ns28
{
	// Token: 0x0200016C RID: 364
	internal sealed partial class SetCondOrdForm : global::System.Windows.Forms.Form
	{
		// Token: 0x06000DF6 RID: 3574 RVA: 0x0005829C File Offset: 0x0005649C
		private void InitializeComponent()
		{
			this.comboBox_CondOpt = new global::System.Windows.Forms.ComboBox();
			this.label2 = new global::System.Windows.Forms.Label();
			this.numericUpDown_CondPrice = new global::System.Windows.Forms.NumericUpDown();
			this.label3 = new global::System.Windows.Forms.Label();
			this.label4 = new global::System.Windows.Forms.Label();
			this.label5 = new global::System.Windows.Forms.Label();
			this.comboBox_LngOrSht = new global::System.Windows.Forms.ComboBox();
			this.groupBox_line = new global::System.Windows.Forms.GroupBox();
			this.label_Symb = new global::System.Windows.Forms.Label();
			this.comboBox_OpenOrClose = new global::System.Windows.Forms.ComboBox();
			this.numericUpDown_Units = new global::System.Windows.Forms.NumericUpDown();
			this.label_unit单位 = new global::System.Windows.Forms.Label();
			this.button_Cancel = new global::System.Windows.Forms.Button();
			this.button_OK = new global::System.Windows.Forms.Button();
			this.label_TrailingStop = new global::System.Windows.Forms.Label();
			this.numericUpDown_TrailingStopPts = new global::System.Windows.Forms.NumericUpDown();
			this.chkBox_TrailingStop = new global::System.Windows.Forms.CheckBox();
			this.groupBox_Cond = new global::System.Windows.Forms.GroupBox();
			this.label6 = new global::System.Windows.Forms.Label();
			this.label1 = new global::System.Windows.Forms.Label();
			this.numericUpDown_ExPrice = new global::ns32.Class302();
			this.groupBox_TrailingStop = new global::System.Windows.Forms.GroupBox();
			this.label_trans = new global::System.Windows.Forms.Label();
			this.comboBox_currTrans = new global::System.Windows.Forms.ComboBox();
			((global::System.ComponentModel.ISupportInitialize)this.numericUpDown_CondPrice).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.numericUpDown_Units).BeginInit();
			((global::System.ComponentModel.ISupportInitialize)this.numericUpDown_TrailingStopPts).BeginInit();
			this.groupBox_Cond.SuspendLayout();
			((global::System.ComponentModel.ISupportInitialize)this.numericUpDown_ExPrice).BeginInit();
			this.groupBox_TrailingStop.SuspendLayout();
			base.SuspendLayout();
			this.comboBox_CondOpt.DropDownStyle = global::System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_CondOpt.FormattingEnabled = true;
			this.comboBox_CondOpt.Items.AddRange(new object[]
			{
				">",
				">=",
				"<",
				"<="
			});
			this.comboBox_CondOpt.Location = new global::System.Drawing.Point(333, 33);
			this.comboBox_CondOpt.Name = "comboBox_CondOpt";
			this.comboBox_CondOpt.Size = new global::System.Drawing.Size(65, 23);
			this.comboBox_CondOpt.TabIndex = 1;
			this.label2.AutoSize = true;
			this.label2.Location = new global::System.Drawing.Point(256, 37);
			this.label2.Name = "label2";
			this.label2.Size = new global::System.Drawing.Size(67, 15);
			this.label2.TabIndex = 2;
			this.label2.Text = "当最新价";
			this.numericUpDown_CondPrice.Location = new global::System.Drawing.Point(410, 32);
			this.numericUpDown_CondPrice.Name = "numericUpDown_CondPrice";
			this.numericUpDown_CondPrice.Size = new global::System.Drawing.Size(96, 25);
			this.numericUpDown_CondPrice.TabIndex = 3;
			this.label3.AutoSize = true;
			this.label3.Location = new global::System.Drawing.Point(512, 37);
			this.label3.Name = "label3";
			this.label3.Size = new global::System.Drawing.Size(22, 15);
			this.label3.TabIndex = 4;
			this.label3.Text = "时";
			this.label4.AutoSize = true;
			this.label4.Location = new global::System.Drawing.Point(22, 93);
			this.label4.Name = "label4";
			this.label4.Size = new global::System.Drawing.Size(22, 15);
			this.label4.TabIndex = 5;
			this.label4.Text = "以";
			this.label5.AutoSize = true;
			this.label5.Location = new global::System.Drawing.Point(157, 93);
			this.label5.Name = "label5";
			this.label5.Size = new global::System.Drawing.Size(37, 15);
			this.label5.TabIndex = 7;
			this.label5.Text = "价格";
			this.comboBox_LngOrSht.DropDownStyle = global::System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_LngOrSht.FormattingEnabled = true;
			this.comboBox_LngOrSht.Items.AddRange(new object[]
			{
				"买入",
				"卖出"
			});
			this.comboBox_LngOrSht.Location = new global::System.Drawing.Point(257, 90);
			this.comboBox_LngOrSht.Name = "comboBox_LngOrSht";
			this.comboBox_LngOrSht.Size = new global::System.Drawing.Size(64, 23);
			this.comboBox_LngOrSht.TabIndex = 8;
			this.groupBox_line.Location = new global::System.Drawing.Point(21, 73);
			this.groupBox_line.Name = "groupBox_line";
			this.groupBox_line.Size = new global::System.Drawing.Size(508, 2);
			this.groupBox_line.TabIndex = 9;
			this.groupBox_line.TabStop = false;
			this.label_Symb.AutoSize = true;
			this.label_Symb.Location = new global::System.Drawing.Point(73, 37);
			this.label_Symb.Name = "label_Symb";
			this.label_Symb.Size = new global::System.Drawing.Size(55, 15);
			this.label_Symb.TabIndex = 10;
			this.label_Symb.Text = "IF0000";
			this.label_Symb.TextAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.comboBox_OpenOrClose.DropDownStyle = global::System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_OpenOrClose.FormattingEnabled = true;
			this.comboBox_OpenOrClose.Items.AddRange(new object[]
			{
				"开仓",
				"平仓",
				"反手"
			});
			this.comboBox_OpenOrClose.Location = new global::System.Drawing.Point(333, 90);
			this.comboBox_OpenOrClose.Name = "comboBox_OpenOrClose";
			this.comboBox_OpenOrClose.Size = new global::System.Drawing.Size(64, 23);
			this.comboBox_OpenOrClose.TabIndex = 11;
			this.numericUpDown_Units.Location = new global::System.Drawing.Point(409, 89);
			this.numericUpDown_Units.Name = "numericUpDown_Units";
			this.numericUpDown_Units.Size = new global::System.Drawing.Size(97, 25);
			this.numericUpDown_Units.TabIndex = 12;
			this.label_unit单位.AutoSize = true;
			this.label_unit单位.Location = new global::System.Drawing.Point(511, 94);
			this.label_unit单位.Name = "label_unit单位";
			this.label_unit单位.Size = new global::System.Drawing.Size(22, 15);
			this.label_unit单位.TabIndex = 13;
			this.label_unit单位.Text = "手";
			this.button_Cancel.DialogResult = global::System.Windows.Forms.DialogResult.Cancel;
			this.button_Cancel.Location = new global::System.Drawing.Point(475, 288);
			this.button_Cancel.Name = "button_Cancel";
			this.button_Cancel.Size = new global::System.Drawing.Size(106, 30);
			this.button_Cancel.TabIndex = 15;
			this.button_Cancel.Text = "取消";
			this.button_Cancel.UseVisualStyleBackColor = true;
			this.button_OK.Location = new global::System.Drawing.Point(357, 288);
			this.button_OK.Name = "button_OK";
			this.button_OK.Size = new global::System.Drawing.Size(106, 30);
			this.button_OK.TabIndex = 14;
			this.button_OK.Text = "确定";
			this.button_OK.UseVisualStyleBackColor = true;
			this.label_TrailingStop.Location = new global::System.Drawing.Point(124, 33);
			this.label_TrailingStop.Name = "label_TrailingStop";
			this.label_TrailingStop.Size = new global::System.Drawing.Size(47, 23);
			this.label_TrailingStop.TabIndex = 26;
			this.label_TrailingStop.Text = "点价";
			this.label_TrailingStop.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.numericUpDown_TrailingStopPts.Location = new global::System.Drawing.Point(178, 33);
			this.numericUpDown_TrailingStopPts.Name = "numericUpDown_TrailingStopPts";
			this.numericUpDown_TrailingStopPts.Size = new global::System.Drawing.Size(76, 25);
			this.numericUpDown_TrailingStopPts.TabIndex = 25;
			this.chkBox_TrailingStop.AutoSize = true;
			this.chkBox_TrailingStop.Location = new global::System.Drawing.Point(24, 36);
			this.chkBox_TrailingStop.Name = "chkBox_TrailingStop";
			this.chkBox_TrailingStop.Size = new global::System.Drawing.Size(89, 19);
			this.chkBox_TrailingStop.TabIndex = 27;
			this.chkBox_TrailingStop.Text = "跟踪止损";
			this.chkBox_TrailingStop.UseVisualStyleBackColor = true;
			this.groupBox_Cond.Controls.Add(this.label6);
			this.groupBox_Cond.Controls.Add(this.label1);
			this.groupBox_Cond.Controls.Add(this.comboBox_OpenOrClose);
			this.groupBox_Cond.Controls.Add(this.comboBox_CondOpt);
			this.groupBox_Cond.Controls.Add(this.label4);
			this.groupBox_Cond.Controls.Add(this.label_Symb);
			this.groupBox_Cond.Controls.Add(this.numericUpDown_ExPrice);
			this.groupBox_Cond.Controls.Add(this.label2);
			this.groupBox_Cond.Controls.Add(this.label_unit单位);
			this.groupBox_Cond.Controls.Add(this.comboBox_LngOrSht);
			this.groupBox_Cond.Controls.Add(this.groupBox_line);
			this.groupBox_Cond.Controls.Add(this.numericUpDown_CondPrice);
			this.groupBox_Cond.Controls.Add(this.label5);
			this.groupBox_Cond.Controls.Add(this.numericUpDown_Units);
			this.groupBox_Cond.Controls.Add(this.label3);
			this.groupBox_Cond.Location = new global::System.Drawing.Point(28, 21);
			this.groupBox_Cond.Name = "groupBox_Cond";
			this.groupBox_Cond.Size = new global::System.Drawing.Size(553, 150);
			this.groupBox_Cond.TabIndex = 18;
			this.groupBox_Cond.TabStop = false;
			this.label6.AutoSize = true;
			this.label6.Location = new global::System.Drawing.Point(21, 37);
			this.label6.Name = "label6";
			this.label6.Size = new global::System.Drawing.Size(45, 15);
			this.label6.TabIndex = 15;
			this.label6.Text = "品种:";
			this.label1.AutoSize = true;
			this.label1.Location = new global::System.Drawing.Point(43, 117);
			this.label1.Name = "label1";
			this.label1.Size = new global::System.Drawing.Size(105, 15);
			this.label1.TabIndex = 14;
			this.label1.Text = "（0则为市价）";
			this.numericUpDown_ExPrice.Location = new global::System.Drawing.Point(51, 88);
			this.numericUpDown_ExPrice.Name = "numericUpDown_ExPrice";
			this.numericUpDown_ExPrice.Size = new global::System.Drawing.Size(97, 25);
			this.numericUpDown_ExPrice.TabIndex = 6;
			this.groupBox_TrailingStop.Controls.Add(this.label_trans);
			this.groupBox_TrailingStop.Controls.Add(this.chkBox_TrailingStop);
			this.groupBox_TrailingStop.Controls.Add(this.label_TrailingStop);
			this.groupBox_TrailingStop.Controls.Add(this.comboBox_currTrans);
			this.groupBox_TrailingStop.Controls.Add(this.numericUpDown_TrailingStopPts);
			this.groupBox_TrailingStop.Location = new global::System.Drawing.Point(28, 178);
			this.groupBox_TrailingStop.Name = "groupBox_TrailingStop";
			this.groupBox_TrailingStop.Size = new global::System.Drawing.Size(553, 80);
			this.groupBox_TrailingStop.TabIndex = 19;
			this.groupBox_TrailingStop.TabStop = false;
			this.label_trans.AutoSize = true;
			this.label_trans.Location = new global::System.Drawing.Point(260, 37);
			this.label_trans.Name = "label_trans";
			this.label_trans.Size = new global::System.Drawing.Size(67, 15);
			this.label_trans.TabIndex = 29;
			this.label_trans.Text = "对应持仓";
			this.comboBox_currTrans.DropDownStyle = global::System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_currTrans.FormattingEnabled = true;
			this.comboBox_currTrans.Location = new global::System.Drawing.Point(333, 33);
			this.comboBox_currTrans.Name = "comboBox_currTrans";
			this.comboBox_currTrans.Size = new global::System.Drawing.Size(196, 23);
			this.comboBox_currTrans.TabIndex = 28;
			base.AcceptButton = this.button_OK;
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(8f, 15f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			base.CancelButton = this.button_Cancel;
			base.ClientSize = new global::System.Drawing.Size(609, 330);
			base.Controls.Add(this.groupBox_TrailingStop);
			base.Controls.Add(this.groupBox_Cond);
			base.Controls.Add(this.button_Cancel);
			base.Controls.Add(this.button_OK);
			base.FormBorderStyle = global::System.Windows.Forms.FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = "SetCondOrdForm";
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.StartPosition = global::System.Windows.Forms.FormStartPosition.CenterParent;
			this.Text = "新建条件单";
			base.Load += new global::System.EventHandler(this.SetCondOrdForm_Load);
			((global::System.ComponentModel.ISupportInitialize)this.numericUpDown_CondPrice).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.numericUpDown_Units).EndInit();
			((global::System.ComponentModel.ISupportInitialize)this.numericUpDown_TrailingStopPts).EndInit();
			this.groupBox_Cond.ResumeLayout(false);
			this.groupBox_Cond.PerformLayout();
			((global::System.ComponentModel.ISupportInitialize)this.numericUpDown_ExPrice).EndInit();
			this.groupBox_TrailingStop.ResumeLayout(false);
			this.groupBox_TrailingStop.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x04000720 RID: 1824
		private global::System.Windows.Forms.ComboBox comboBox_CondOpt;

		// Token: 0x04000721 RID: 1825
		private global::System.Windows.Forms.Label label2;

		// Token: 0x04000722 RID: 1826
		private global::System.Windows.Forms.NumericUpDown numericUpDown_CondPrice;

		// Token: 0x04000723 RID: 1827
		private global::System.Windows.Forms.Label label3;

		// Token: 0x04000724 RID: 1828
		private global::System.Windows.Forms.Label label4;

		// Token: 0x04000725 RID: 1829
		private global::ns32.Class302 numericUpDown_ExPrice;

		// Token: 0x04000726 RID: 1830
		private global::System.Windows.Forms.Label label5;

		// Token: 0x04000727 RID: 1831
		private global::System.Windows.Forms.ComboBox comboBox_LngOrSht;

		// Token: 0x04000728 RID: 1832
		private global::System.Windows.Forms.GroupBox groupBox_line;

		// Token: 0x04000729 RID: 1833
		private global::System.Windows.Forms.Label label_Symb;

		// Token: 0x0400072A RID: 1834
		private global::System.Windows.Forms.ComboBox comboBox_OpenOrClose;

		// Token: 0x0400072B RID: 1835
		private global::System.Windows.Forms.NumericUpDown numericUpDown_Units;

		// Token: 0x0400072C RID: 1836
		private global::System.Windows.Forms.Label label_unit单位;

		// Token: 0x0400072D RID: 1837
		private global::System.Windows.Forms.Button button_Cancel;

		// Token: 0x0400072E RID: 1838
		private global::System.Windows.Forms.Button button_OK;

		// Token: 0x0400072F RID: 1839
		private global::System.Windows.Forms.Label label_TrailingStop;

		// Token: 0x04000730 RID: 1840
		private global::System.Windows.Forms.NumericUpDown numericUpDown_TrailingStopPts;

		// Token: 0x04000731 RID: 1841
		private global::System.Windows.Forms.CheckBox chkBox_TrailingStop;

		// Token: 0x04000732 RID: 1842
		private global::System.Windows.Forms.GroupBox groupBox_Cond;

		// Token: 0x04000733 RID: 1843
		private global::System.Windows.Forms.GroupBox groupBox_TrailingStop;

		// Token: 0x04000734 RID: 1844
		private global::System.Windows.Forms.Label label_trans;

		// Token: 0x04000735 RID: 1845
		private global::System.Windows.Forms.ComboBox comboBox_currTrans;

		// Token: 0x04000736 RID: 1846
		private global::System.Windows.Forms.Label label1;

		// Token: 0x04000737 RID: 1847
		private global::System.Windows.Forms.Label label6;
	}
}
