﻿using System;
using System.Data;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using ns14;
using ns16;

namespace ns20
{
	// Token: 0x0200020A RID: 522
	internal sealed class Class285 : Class283
	{
		// Token: 0x0600155F RID: 5471 RVA: 0x0008D684 File Offset: 0x0008B884
		protected override void Class283_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
		{
			base.Class283_CellFormatting(sender, e);
			DataGridView dataGridView = sender as DataGridView;
			if (e.Value != null && !this.vmethod_4(dataGridView, e))
			{
				e.CellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
			}
			if (dataGridView.Rows[e.RowIndex].Selected)
			{
				e.CellStyle.Font = new Font(e.CellStyle.Font, FontStyle.Bold);
			}
		}

		// Token: 0x06001560 RID: 5472 RVA: 0x0008D6F8 File Offset: 0x0008B8F8
		protected override bool vmethod_4(DataGridView dataGridView_0, DataGridViewCellFormattingEventArgs dataGridViewCellFormattingEventArgs_0)
		{
			return dataGridViewCellFormattingEventArgs_0.ColumnIndex > 0;
		}

		// Token: 0x06001561 RID: 5473 RVA: 0x000088AA File Offset: 0x00006AAA
		protected override void vmethod_0()
		{
			base.vmethod_0();
		}

		// Token: 0x06001562 RID: 5474 RVA: 0x0008D714 File Offset: 0x0008B914
		protected override void vmethod_3()
		{
			base.Columns[0].FillWeight = 175f;
			foreach (object obj in base.Columns)
			{
				((DataGridViewColumn)obj).SortMode = DataGridViewColumnSortMode.NotSortable;
			}
			base.vmethod_3();
		}

		// Token: 0x17000383 RID: 899
		// (get) Token: 0x06001563 RID: 5475 RVA: 0x0008D78C File Offset: 0x0008B98C
		// (set) Token: 0x06001564 RID: 5476 RVA: 0x000088B4 File Offset: 0x00006AB4
		public Enum2 ReportAnlysType { get; set; }

		// Token: 0x17000384 RID: 900
		// (get) Token: 0x06001565 RID: 5477 RVA: 0x0008D7A4 File Offset: 0x0008B9A4
		// (set) Token: 0x06001566 RID: 5478 RVA: 0x000088BF File Offset: 0x00006ABF
		public DataTable SrcFnDataTable { get; set; }

		// Token: 0x06001567 RID: 5479 RVA: 0x000088CA File Offset: 0x00006ACA
		public Class285() : base(false)
		{
		}

		// Token: 0x04000B02 RID: 2818
		[CompilerGenerated]
		private Enum2 enum2_0;

		// Token: 0x04000B03 RID: 2819
		[CompilerGenerated]
		private DataTable dataTable_0;
	}
}
