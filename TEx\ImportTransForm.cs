﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using DevComponents.DotNetBar;
using DevComponents.DotNetBar.Controls;
using DevComponents.Editors;
using ns10;
using ns12;
using ns2;
using ns28;
using ns33;
using ns5;
using TEx.ImportTrans;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x020001AB RID: 427
	public sealed partial class ImportTransForm : Form, Interface2, Interface3
	{
		// Token: 0x06001089 RID: 4233 RVA: 0x00070FAC File Offset: 0x0006F1AC
		public ImportTransForm(string filePath)
		{
			this.InitializeComponent();
			this.filePath = filePath;
			this.label_filePath.Text = this.filePath;
			this.label_recCount.Text = "(读取中...)";
			base.Load += this.ImportTransForm_Load;
			this.gridViewX.RowHeadersWidth = 60;
		}

		// Token: 0x0600108A RID: 4234 RVA: 0x00071010 File Offset: 0x0006F210
		private void ImportTransForm_Load(object sender, EventArgs e)
		{
			Base.UI.smethod_177("正在读取记录...", this.method_0());
			this.method_4();
			this.btn_Import.Click += this.btn_Import_Click;
			this.transFileImporter_0 = new TransFileImporter(this, DateTime.Now);
			string text = string.Empty;
			try
			{
				text = this.method_2(this.filePath);
			}
			catch (Exception ex)
			{
				Base.UI.smethod_178();
				MessageBox.Show(ex.Message, "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				base.Close();
				return;
			}
			Base.UI.smethod_178();
			base.Activate();
			if (text == "ok")
			{
				this.Text = "交易记录导入";
				this.label_recCount.Text = this.transFileImporter_0.BackData.Data.Count.ToString();
			}
			else
			{
				MessageBox.Show(text, "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				base.Close();
			}
		}

		// Token: 0x0600108B RID: 4235 RVA: 0x00029524 File Offset: 0x00027724
		private Point? method_0()
		{
			Point? result = null;
			if (base.Visible)
			{
				result = new Point?(new Point(base.Location.X + base.Width / 2, base.Location.Y + base.Height / 2));
			}
			return result;
		}

		// Token: 0x0600108C RID: 4236 RVA: 0x00007126 File Offset: 0x00005326
		private void method_1(object sender, DataGridViewDataErrorEventArgs e)
		{
			MessageBox.Show("数据加载异常：" + e.Exception.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			base.Close();
		}

		// Token: 0x0600108D RID: 4237 RVA: 0x00071104 File Offset: 0x0006F304
		private string method_2(string string_0)
		{
			this.gridViewX.DataSource = null;
			this.label_Hint.Text = "请检查确认数据，并根据需要修改。";
			string text = this.transFileImporter_0.method_9(string_0);
			if (text == "ok")
			{
				try
				{
					this.gridViewX.DataSource = this.transFileImporter_0.BackData.Data;
				}
				catch (Exception ex)
				{
					MessageBox.Show(ex.Message);
				}
				this.method_3();
			}
			return text;
		}

		// Token: 0x0600108E RID: 4238 RVA: 0x0007118C File Offset: 0x0006F38C
		private void method_3()
		{
			for (int i = 0; i < this.gridViewX.Rows.Count; i++)
			{
				DataGridViewRow dataGridViewRow = this.gridViewX.Rows[i];
				dataGridViewRow.HeaderCell.Value = string.Format("{0}", dataGridViewRow.Index + 1);
			}
		}

		// Token: 0x0600108F RID: 4239 RVA: 0x000711EC File Offset: 0x0006F3EC
		private void method_4()
		{
			List<Account> list = Base.Acct.Accounts.Where(new Func<Account, bool>(ImportTransForm.<>c.<>9.method_0)).ToList<Account>();
			int selectedIndex = list.FindIndex(new Predicate<Account>(ImportTransForm.<>c.<>9.method_1));
			foreach (Account account in list)
			{
				TEx.Util.ComboBoxItem comboBoxItem = new TEx.Util.ComboBoxItem();
				comboBoxItem.Text = account.AcctName;
				comboBoxItem.Value = account.ID;
				this.comboAcct.Items.Add(comboBoxItem);
			}
			this.comboAcct.SelectedIndex = selectedIndex;
		}

		// Token: 0x06001090 RID: 4240 RVA: 0x00007153 File Offset: 0x00005353
		public void imethod_0(string string_0, int int_0)
		{
			this.label_Hint.Text = string_0;
		}

		// Token: 0x06001091 RID: 4241 RVA: 0x000712CC File Offset: 0x0006F4CC
		private void btn_Import_Click(object sender, EventArgs e)
		{
			this.gridViewX.EndEdit();
			if (this.transFileImporter_0.BackData.Data.Count > 0)
			{
				if (this.comboAcct.Text == "")
				{
					this.label_Hint.Text = "请选择账户名称";
				}
				else
				{
					this.label_Hint.Text = "正在导入交易记录...";
					try
					{
						this.btn_Import.Enabled = false;
						string text;
						if (!this.transFileImporter_0.method_11(out text))
						{
							this.label_Hint.Text = text;
							this.btn_Import.Enabled = true;
						}
						else
						{
							for (int i = 0; i < this.transFileImporter_0.BackData.Data.Count; i++)
							{
								if (this.transFileImporter_0.BackData.Data[i].CloseID != null)
								{
									int num = Convert.ToInt32(this.transFileImporter_0.BackData.Data[i].CloseID);
									num--;
									this.transFileImporter_0.BackData.Data[i].method_0(num.ToString());
								}
							}
							TEx.Util.ComboBoxItem comboBoxItem = this.comboAcct.SelectedItem as TEx.Util.ComboBoxItem;
							TransFileImporter.smethod_1(this.transFileImporter_0.BackData, Convert.ToInt32(comboBoxItem.Value));
							this.btn_Import.Enabled = true;
							this.label_Hint.Text = "导入完成。";
							MessageBox.Show("已成功导入交易记录。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
							base.Close();
						}
					}
					catch (Exception ex)
					{
						Class182.smethod_0(ex);
						MessageBox.Show("导入错误！请检查文件或联系客服解决。错误信息：" + Environment.NewLine + ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
						this.btn_Import.Enabled = true;
						this.label_Hint.Text = "";
					}
				}
			}
		}

		// Token: 0x06001092 RID: 4242 RVA: 0x000714C8 File Offset: 0x0006F6C8
		private string method_5(string string_0)
		{
			for (int i = 0; i < this.gridViewX.Columns.Count; i++)
			{
				if (this.gridViewX.Columns[i].DataPropertyName == string_0)
				{
					return this.gridViewX.Columns[i].Name;
				}
			}
			return "";
		}

		// Token: 0x06001093 RID: 4243 RVA: 0x00071530 File Offset: 0x0006F730
		private void method_6(object sender, DataGridViewCellEventArgs e)
		{
			if (e.ColumnIndex != -1)
			{
				if (e.RowIndex != -1)
				{
					TransData transData = this.transFileImporter_0.BackData.Data[e.RowIndex];
					string dataPropertyName = this.gridViewX.Columns[e.ColumnIndex].DataPropertyName;
					object editedFormattedValue = this.gridViewX.CurrentCell.EditedFormattedValue;
					transData.method_2(dataPropertyName, editedFormattedValue);
					List<TransData> data = this.transFileImporter_0.BackData.Data;
					if (data.Count > e.RowIndex)
					{
						data[e.RowIndex] = transData;
					}
					this.gridViewX.Refresh();
				}
			}
		}

		// Token: 0x06001094 RID: 4244 RVA: 0x00007163 File Offset: 0x00005363
		private void ImportTransForm_Load_1(object sender, EventArgs e)
		{
			Array.ForEach<string>(TransData.FieldBuyOrSell, new Action<string>(this.method_8));
			Array.ForEach<string>(TransData.FieldOpenOrClose, new Action<string>(this.method_9));
		}

		// Token: 0x06001095 RID: 4245 RVA: 0x000715E4 File Offset: 0x0006F7E4
		private void method_7(object sender, DataGridViewBindingCompleteEventArgs e)
		{
			try
			{
				for (int i = 0; i < this.transFileImporter_0.DefaultVar.Count; i++)
				{
					Class479 @class = this.transFileImporter_0.DefaultVar[i];
					string text = this.method_5(@class.string_0);
					if (text != "")
					{
						this.gridViewX.Rows[@class.int_0].Cells[text].Style.BackColor = Color.Yellow;
					}
				}
			}
			catch
			{
			}
		}

		// Token: 0x06001096 RID: 4246 RVA: 0x00007193 File Offset: 0x00005393
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001098 RID: 4248 RVA: 0x000071B4 File Offset: 0x000053B4
		[CompilerGenerated]
		private void method_8(string string_0)
		{
			(this.gridViewX.Columns["buyOrSellCol"] as DataGridViewComboBoxColumn).Items.Add(string_0);
		}

		// Token: 0x06001099 RID: 4249 RVA: 0x000071DE File Offset: 0x000053DE
		[CompilerGenerated]
		private void method_9(string string_0)
		{
			(this.gridViewX.Columns["openOrCloseCol"] as DataGridViewComboBoxColumn).Items.Add(string_0);
		}

		// Token: 0x040008B1 RID: 2225
		private TransFileImporter transFileImporter_0;

		// Token: 0x040008B2 RID: 2226
		private string filePath;

		// Token: 0x040008B7 RID: 2231
		private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_0;

		// Token: 0x040008B8 RID: 2232
		private DataGridViewDateTimeInputColumn dataGridViewDateTimeInputColumn_0;

		// Token: 0x040008B9 RID: 2233
		private DataGridViewDateTimeInputColumn dataGridViewDateTimeInputColumn_1;

		// Token: 0x040008BA RID: 2234
		private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_1;

		// Token: 0x040008BB RID: 2235
		private DataGridViewComboBoxColumn dataGridViewComboBoxColumn_0;

		// Token: 0x040008BC RID: 2236
		private DataGridViewComboBoxColumn dataGridViewComboBoxColumn_1;

		// Token: 0x040008BD RID: 2237
		private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_2;

		// Token: 0x040008BE RID: 2238
		private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_3;

		// Token: 0x040008BF RID: 2239
		private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_4;
	}
}
