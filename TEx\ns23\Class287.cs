﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ns6;
using TEx;

namespace ns23
{
	// Token: 0x02000206 RID: 518
	[DesignerCategory("Code")]
	internal sealed class Class287 : RadioButton
	{
		// Token: 0x06001519 RID: 5401 RVA: 0x000086C2 File Offset: 0x000068C2
		public Class287()
		{
			this.bool_0 = (LicenseManager.UsageMode == LicenseUsageMode.Designtime);
			if (!this.bool_0)
			{
				this.method_1();
				Base.UI.ChartThemeChanged += this.method_0;
			}
			base.SetStyle(ControlStyles.UserPaint, true);
		}

		// Token: 0x0600151A RID: 5402 RVA: 0x00008701 File Offset: 0x00006901
		private void method_0(object sender, EventArgs e)
		{
			this.method_1();
		}

		// Token: 0x0600151B RID: 5403 RVA: 0x0000870B File Offset: 0x0000690B
		private void method_1()
		{
			this.ForeColor = this.method_2();
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.BackColor = Class179.color_3;
			}
			else
			{
				this.BackColor = Class179.color_9;
			}
		}

		// Token: 0x0600151C RID: 5404 RVA: 0x0008A598 File Offset: 0x00088798
		private Color method_2()
		{
			Color result;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				if (base.Enabled)
				{
					result = Class179.color_9;
				}
				else
				{
					result = Class179.color_7;
				}
			}
			else if (base.Enabled)
			{
				result = Class179.color_1;
			}
			else
			{
				result = Class179.color_5;
			}
			return result;
		}

		// Token: 0x0600151D RID: 5405 RVA: 0x0008A5E4 File Offset: 0x000887E4
		protected void OnPaint(PaintEventArgs pevent)
		{
			base.OnPaint(pevent);
			if (!this.bool_0)
			{
				pevent.Graphics.FillRectangle(new SolidBrush(this.BackColor), new Rectangle(15, 0, base.Width - 15, base.Height));
				Color foreColor = this.method_2();
				TextRenderer.DrawText(pevent.Graphics, this.Text, this.Font, new Rectangle(16, 0, base.Width - 16, base.Height), foreColor, TextFormatFlags.VerticalCenter);
			}
		}

		// Token: 0x0600151E RID: 5406 RVA: 0x0000873F File Offset: 0x0000693F
		protected void OnEnabledChanged(EventArgs e)
		{
			base.OnEnabledChanged(e);
			base.Invalidate();
		}

		// Token: 0x0600151F RID: 5407 RVA: 0x00008750 File Offset: 0x00006950
		protected void Dispose(bool disposing)
		{
			if (!this.bool_0)
			{
				Base.UI.ChartThemeChanged -= this.method_0;
			}
			base.Dispose(disposing);
		}

		// Token: 0x04000AE7 RID: 2791
		private bool bool_0;
	}
}
