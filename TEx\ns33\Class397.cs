﻿using System;
using System.Drawing;
using ns28;
using TEx.Chart;
using TEx.Comn;
using TEx.Inds;
using TEx.SIndicator;

namespace ns33
{
	// Token: 0x020002FE RID: 766
	internal sealed class Class397 : ShapeCurve
	{
		// Token: 0x06002144 RID: 8516 RVA: 0x000E3E1C File Offset: 0x000E201C
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (zedGraphControl_0.GraphPane != null)
			{
				if (this.curveItem_0 != null)
				{
					zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
				}
				JapaneseCandleStickItem japaneseCandleStickItem = zedGraphControl_0.GraphPane.AddJapaneseCandleStick(base.IndData.Name, base.DataView);
				japaneseCandleStickItem.Stick.Color = Color.Red;
				japaneseCandleStickItem.Stick.RisingFill.Color = Color.Black;
				japaneseCandleStickItem.Stick.RisingBorder.Color = Color.Red;
				japaneseCandleStickItem.Stick.FallingColor = Color.Cyan;
				japaneseCandleStickItem.Stick.FallingBorder.Color = Color.Cyan;
				japaneseCandleStickItem.Stick.FallingFill.Color = Color.Cyan;
				this.curveItem_0 = japaneseCandleStickItem;
				japaneseCandleStickItem.Tag = string_0 + "_" + base.IndData.Name;
				japaneseCandleStickItem.Stick.IsAutoSize = true;
				japaneseCandleStickItem.Stick.Width = (float)base.method_2();
			}
		}

		// Token: 0x06002145 RID: 8517 RVA: 0x000E3F2C File Offset: 0x000E212C
		protected override PointPair vmethod_0(int int_0, DataArray dataArray_1)
		{
			double date = new XDate(base.method_0(int_0));
			HisData hisData;
			if (int_0 < base.DP.PeriodHisDataList.Values.Count)
			{
				hisData = base.DP.PeriodHisDataList.Values[int_0];
			}
			else
			{
				hisData = base.DP.PeriodHisDataList.Values[base.DP.PeriodHisDataList.Values.Count - 1];
				Class182.smethod_0(new Exception("idx > DP.PeriodHisDataList.Values.Count!"));
			}
			if (int_0 >= dataArray_1.Data.Length)
			{
				int_0 = dataArray_1.Data.Length - 1;
				Class182.smethod_0(new Exception("ShapeVol.GetFromData的idx长度溢出。"));
			}
			double num = dataArray_1.Data[int_0];
			if (double.IsNaN(num))
			{
				if (this.rollingPointPairList_0.Count > 0)
				{
					num = this.rollingPointPairList_0[this.rollingPointPairList_0.Count - 1].Y;
				}
				else
				{
					num = 0.0;
				}
			}
			double high = num;
			double low = 0.0;
			double close = hisData.Close;
			if (this.bool_0 && this.hisData_0 != null)
			{
				close = this.hisData_0.Close;
			}
			double open;
			double close2;
			if (close >= hisData.Open)
			{
				open = 0.0;
				close2 = num;
			}
			else
			{
				open = num;
				close2 = 0.0;
			}
			return new StockPt(date, high, low, open, close2, (hisData.Amount != null) ? hisData.Amount.Value : 0.0);
		}

		// Token: 0x06002146 RID: 8518 RVA: 0x000E40BC File Offset: 0x000E22BC
		public void method_6(int int_0, HisData hisData_1, bool bool_1)
		{
			if (bool_1)
			{
				if (int_0 >= 0 && int_0 < base.DP.PeriodHisDataList.Values.Count)
				{
					this.hisData_0 = base.DP.PeriodHisDataList.Values[int_0].Clone();
					if (hisData_1 != null)
					{
						this.hisData_0.Close = hisData_1.Close;
					}
				}
			}
			else if (this.hisData_0 != null)
			{
				this.hisData_0.Close = hisData_1.Close;
				if (this.hisData_0.High < hisData_1.High)
				{
					this.hisData_0.High = hisData_1.High;
				}
				if (this.hisData_0.Low > hisData_1.Low)
				{
					this.hisData_0.Low = hisData_1.Low;
				}
			}
			else
			{
				this.hisData_0 = hisData_1.Clone();
			}
		}

		// Token: 0x06002147 RID: 8519 RVA: 0x000E419C File Offset: 0x000E239C
		public override void vmethod_2(int int_0)
		{
			base.vmethod_2(int_0);
			int num = int_0;
			if (int_0 >= base.DP.PeriodHisDataList.Values.Count)
			{
				num = base.DP.PeriodHisDataList.Values.Count - 1;
			}
			if (num >= 0)
			{
				this.method_6(int_0, base.DP.PeriodHisDataList.Values[num], true);
			}
		}

		// Token: 0x06002148 RID: 8520 RVA: 0x000E4208 File Offset: 0x000E2408
		public override void vmethod_4(int int_0, DataArray dataArray_1)
		{
			this.bool_0 = true;
			PointPair value = this.vmethod_0(int_0, dataArray_1);
			IPointListEdit rollingPointPairList_ = this.rollingPointPairList_0;
			if (rollingPointPairList_.Count > 0)
			{
				rollingPointPairList_[rollingPointPairList_.Count - 1] = value;
			}
			this.bool_0 = false;
		}

		// Token: 0x06002149 RID: 8521 RVA: 0x0000D56B File Offset: 0x0000B76B
		public Class397(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}

		// Token: 0x04001033 RID: 4147
		private HisData hisData_0;

		// Token: 0x04001034 RID: 4148
		private bool bool_0;
	}
}
