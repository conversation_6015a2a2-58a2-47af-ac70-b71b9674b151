﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using DevComponents.AdvTree;
using DevComponents.DotNetBar;
using Newtonsoft.Json;
using ns11;
using ns12;
using ns15;
using ns17;
using ns20;
using ns21;
using ns22;
using ns23;
using ns25;
using ns28;
using ns33;
using ns6;
using ns8;
using ns9;
using TEx.Comn;
using TEx.Trading;
using TEx.Util;

namespace TEx
{
	// Token: 0x02000255 RID: 597
	internal sealed class TransTabs : UserControl
	{
		// Token: 0x14000092 RID: 146
		// (add) Token: 0x06001976 RID: 6518 RVA: 0x000AB220 File Offset: 0x000A9420
		// (remove) Token: 0x06001977 RID: 6519 RVA: 0x000AB258 File Offset: 0x000A9458
		public event EventHandler DateTimePickerEnter
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06001978 RID: 6520 RVA: 0x000AB290 File Offset: 0x000A9490
		protected void method_0()
		{
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x14000093 RID: 147
		// (add) Token: 0x06001979 RID: 6521 RVA: 0x000AB2B8 File Offset: 0x000A94B8
		// (remove) Token: 0x0600197A RID: 6522 RVA: 0x000AB2F0 File Offset: 0x000A94F0
		public event EventHandler DateTimePickerLeave
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600197B RID: 6523 RVA: 0x000AB328 File Offset: 0x000A9528
		protected void method_1()
		{
			EventHandler eventHandler = this.eventHandler_1;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x14000094 RID: 148
		// (add) Token: 0x0600197C RID: 6524 RVA: 0x000AB350 File Offset: 0x000A9550
		// (remove) Token: 0x0600197D RID: 6525 RVA: 0x000AB388 File Offset: 0x000A9588
		public event EventHandler TUnitsInputChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_2;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_2, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_2;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_2, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x0600197E RID: 6526 RVA: 0x000AB3C0 File Offset: 0x000A95C0
		protected void method_2()
		{
			EventHandler eventHandler = this.eventHandler_2;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x14000095 RID: 149
		// (add) Token: 0x0600197F RID: 6527 RVA: 0x000AB3E8 File Offset: 0x000A95E8
		// (remove) Token: 0x06001980 RID: 6528 RVA: 0x000AB420 File Offset: 0x000A9620
		public event EventHandler TPriceInputChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_3;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_3, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_3;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_3, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06001981 RID: 6529 RVA: 0x000AB458 File Offset: 0x000A9658
		protected void method_3()
		{
			EventHandler eventHandler = this.eventHandler_3;
			if (eventHandler != null)
			{
				eventHandler(this, new EventArgs());
			}
		}

		// Token: 0x14000096 RID: 150
		// (add) Token: 0x06001982 RID: 6530 RVA: 0x000AB480 File Offset: 0x000A9680
		// (remove) Token: 0x06001983 RID: 6531 RVA: 0x000AB4B8 File Offset: 0x000A96B8
		public event Delegate22 ChangeToHisTransDTRequested
		{
			[CompilerGenerated]
			add
			{
				Delegate22 @delegate = this.delegate22_0;
				Delegate22 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate22 value2 = (Delegate22)Delegate.Combine(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate22>(ref this.delegate22_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
			[CompilerGenerated]
			remove
			{
				Delegate22 @delegate = this.delegate22_0;
				Delegate22 delegate2;
				do
				{
					delegate2 = @delegate;
					Delegate22 value2 = (Delegate22)Delegate.Remove(delegate2, value);
					@delegate = Interlocked.CompareExchange<Delegate22>(ref this.delegate22_0, value2, delegate2);
				}
				while (@delegate != delegate2);
			}
		}

		// Token: 0x06001984 RID: 6532 RVA: 0x000AB4F0 File Offset: 0x000A96F0
		protected void method_4(ShownHisTrans shownHisTrans_0)
		{
			EventArgs16 e = new EventArgs16(shownHisTrans_0);
			Delegate22 @delegate = this.delegate22_0;
			if (@delegate != null)
			{
				@delegate(e);
			}
		}

		// Token: 0x14000097 RID: 151
		// (add) Token: 0x06001985 RID: 6533 RVA: 0x000AB518 File Offset: 0x000A9718
		// (remove) Token: 0x06001986 RID: 6534 RVA: 0x000AB550 File Offset: 0x000A9750
		public event MsgEventHandler MsgNotifyNeeded
		{
			[CompilerGenerated]
			add
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Combine(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				MsgEventHandler msgEventHandler = this.msgEventHandler_0;
				MsgEventHandler msgEventHandler2;
				do
				{
					msgEventHandler2 = msgEventHandler;
					MsgEventHandler value2 = (MsgEventHandler)Delegate.Remove(msgEventHandler2, value);
					msgEventHandler = Interlocked.CompareExchange<MsgEventHandler>(ref this.msgEventHandler_0, value2, msgEventHandler2);
				}
				while (msgEventHandler != msgEventHandler2);
			}
		}

		// Token: 0x06001987 RID: 6535 RVA: 0x0000A9A7 File Offset: 0x00008BA7
		protected void method_5(string string_1)
		{
			MsgEventHandler msgEventHandler = this.msgEventHandler_0;
			if (msgEventHandler != null)
			{
				msgEventHandler(this, new MsgEventArgs(string_1, null));
			}
		}

		// Token: 0x06001988 RID: 6536 RVA: 0x000AB588 File Offset: 0x000A9788
		public TransTabs(SplitterPanel panel)
		{
			this.IsInitiating = true;
			this.InitializeComponent();
			this.method_6();
			if (panel != null)
			{
				panel.Controls.Add(this);
				this.ParentSplitPanel = panel;
			}
			this.trdAnalysisPanel_0 = new TrdAnalysisPanel();
			this.trdAnalysisPanel_0.Dock = DockStyle.Fill;
			this.tabControlPanel_Below_Acct.Controls.Add(this.trdAnalysisPanel_0);
			this.method_8();
			this.IsInitiating = false;
		}

		// Token: 0x06001989 RID: 6537 RVA: 0x000AB608 File Offset: 0x000A9808
		private void method_6()
		{
			float emSize = TApp.smethod_4(9f, false);
			this.font_0 = new Font("SimSun", emSize, FontStyle.Bold);
			this.font_1 = new Font("SimSun", emSize, FontStyle.Regular);
			this.tabControl_Below.Font = this.font_1;
			this.tabControl_Below.SelectedTabFont = this.font_0;
			this.tabCtrl_Trans.Font = this.font_1;
			this.tabCtrl_Trans.SelectedTabFont = this.font_1;
			emSize = TApp.smethod_4(9f, false);
			Font font = new Font("SimSun", emSize, FontStyle.Regular);
			this.label_品种.Font = font;
			this.label_数量.Font = font;
			this.label_价格.Font = font;
			this.comboBox_TradingSymbs.Font = font;
			this.chkBox_FollowPrc.Font = font;
			this.chkBox_AutoOpenClose.Font = font;
			this.radioBtn_Close.Font = font;
			this.radioBtn_Open.Font = font;
			Font font2 = new Font("Microsoft Sans Serif", emSize);
			this.numericUpDown_TPrice.Font = font2;
			this.numericUpDown_TUnits.Font = font2;
			Font font3 = new Font("Microsoft Sans Serif", TApp.smethod_4(8f, false));
			this.label_maxPosUnits.Font = font3;
			foreach (object obj in this.panel_PercRadioBtns.Controls)
			{
				((RadioButton)obj).Font = font3;
			}
			Font font4 = new Font("SimHei", TApp.smethod_4(11f, false));
			this.btn_TOpenLong.Font = font4;
			this.btn_TOpenShrt.Font = font4;
		}

		// Token: 0x0600198A RID: 6538 RVA: 0x000AB7D4 File Offset: 0x000A99D4
		public void method_7(SplitterPanel splitterPanel_2)
		{
			if (splitterPanel_2 != null)
			{
				if (this.ParentSplitPanel != null)
				{
					this.ParentSplitPanel.Controls.Remove(this);
				}
				splitterPanel_2.Controls.Clear();
				splitterPanel_2.Controls.Add(this);
				this.ParentSplitPanel = splitterPanel_2;
				base.Enabled = true;
				base.Visible = true;
			}
		}

		// Token: 0x0600198B RID: 6539 RVA: 0x000AB82C File Offset: 0x000A9A2C
		public void method_8()
		{
			this.fnDataApiWorker_0 = new FnDataApiWorker();
			this.fnDataApiWorker_0.ResultReceived += this.fnDataApiWorker_0_ResultReceived;
			this.fnDataApiWorker_0.RequestError += this.fnDataApiWorker_0_RequestError;
			this.smethod_0();
			this.Dock = DockStyle.Fill;
			this.tabControl_Below.ThemeAware = false;
			this.tabControl_Below.Style = eTabStripStyle.OneNote;
			Base.UI.ChartThemeChanged += this.method_10;
			this.tabControlPanel_Below_Acct.ThemeAware = false;
			this.tabControlPanel_Below_Acct.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_MktSymbs.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_FnRpt.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_SelSymb.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_BaoDian_Below.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_Trading.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_OpenPos.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_Order.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_Orders_Curr.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_Orders_Hist.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_Trans.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_Trans_Curr.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_Trans_Hist.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_CondOrder.Padding = new System.Windows.Forms.Padding(0);
			this.tabControlPanel_Video.Padding = new System.Windows.Forms.Padding(0);
			this.method_19();
			this.method_96();
			this.method_97();
			this.method_67();
			this.method_86();
			this.method_93();
			this.method_35();
			this.method_26(true, null);
			this.tabControl_Below.SelectedTabChanging += this.tabControl_Below_SelectedTabChanging;
			this.comboBox_TradingSymbs.SelectedIndexChanged += this.comboBox_TradingSymbs_SelectedIndexChanged;
			base.Disposed += this.TransTabs_Disposed;
			if (!TApp.IsStIncluded)
			{
				this.tabPg_FnRpt.Visible = false;
				this.tabPg_SelSymb.Visible = false;
			}
			this.tabPg_Video.Visible = false;
			this.method_12();
			if (Base.Data.SymbDataSets != null)
			{
				foreach (SymbDataSet symbDataSet_ in Base.Data.SymbDataSets)
				{
					this.method_17(symbDataSet_);
				}
			}
			Base.Data.CurrSymblChanged += this.method_107;
			Base.Data.SymbDataSetAdded += this.method_108;
			Base.Data.SymbDataSetRemoved += this.method_109;
			Base.Data.DateSelectionChanged += this.method_110;
			Base.Acct.AccountChanging += this.method_105;
			Base.Acct.AccountChanged += this.method_106;
			Base.Trading.TransCreated += this.method_112;
			Base.Trading.OrderCanceled += this.method_113;
			Base.Trading.OrderCreated += this.method_114;
			Base.Trading.OrderPriceUnitsUpdated += this.method_115;
			Base.Trading.CondOrderCreated += this.method_116;
			Base.Trading.CondOrderExecuted += this.method_117;
			Base.Trading.CondOrderUpdated += this.method_118;
			Base.Trading.CondOrderStatusUpdated += this.method_119;
			Base.Trading.OpenTransListUpdated += this.method_120;
			Base.UI.CurrTradingSymbChanged += this.method_128;
			Base.UI.Form.StockShortSettingChanged += this.method_121;
			Base.UI.Form.IfShowSymbCNNameInOpenTransGridViewChanged += this.method_122;
			this.smethod_1();
		}

		// Token: 0x0600198C RID: 6540 RVA: 0x000ABBE8 File Offset: 0x000A9DE8
		private void fnDataApiWorker_0_ResultReceived(object sender, WebApiEventArgs e)
		{
			if (e.ApiResult != null)
			{
				try
				{
					Dictionary<string, object> requestDict = e.RequestDict;
					if (requestDict.ContainsKey("mktDgv"))
					{
						DataGridViewMkt dataGridViewMkt = requestDict["mktDgv"] as DataGridViewMkt;
						if (requestDict.ContainsKey("newDS"))
						{
							this.method_9(dataGridViewMkt, requestDict["newDS"] as SortableBindingList<ShowMktSymb>);
							DateTime dateTime = Convert.ToDateTime(requestDict["date"]);
							dataGridViewMkt.Tag = dateTime;
						}
					}
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
		}

		// Token: 0x0600198D RID: 6541 RVA: 0x000ABC80 File Offset: 0x000A9E80
		private void method_9(DataGridViewMkt dataGridViewMkt_1, SortableBindingList<ShowMktSymb> sortableBindingList_0)
		{
			TransTabs.Class318 @class = new TransTabs.Class318();
			@class.dataGridViewMkt_0 = dataGridViewMkt_1;
			@class.sortableBindingList_0 = sortableBindingList_0;
			DataGridViewColumn sortedColumn = @class.dataGridViewMkt_0.SortedColumn;
			@class.string_0 = null;
			@class.listSortDirection_0 = ListSortDirection.Ascending;
			if (sortedColumn != null)
			{
				@class.string_0 = sortedColumn.Name;
				if (sortedColumn.HeaderCell.SortGlyphDirection == SortOrder.Ascending)
				{
					@class.listSortDirection_0 = ListSortDirection.Ascending;
				}
				else
				{
					@class.listSortDirection_0 = ListSortDirection.Descending;
				}
			}
			@class.int_0 = -1;
			if (@class.dataGridViewMkt_0.SelectedRows != null && @class.dataGridViewMkt_0.SelectedRows.Count > 0)
			{
				@class.int_0 = @class.dataGridViewMkt_0.SelectedRows[0].Index;
			}
			if (base.IsHandleCreated)
			{
				base.Invoke(new Action(@class.method_0));
			}
		}

		// Token: 0x0600198E RID: 6542 RVA: 0x0000A9C4 File Offset: 0x00008BC4
		private void fnDataApiWorker_0_RequestError(object sender, TEx.Comn.ErrorEventArgs e)
		{
			if (e.Exception is WebException)
			{
				e.Exception.Message == "操作超时";
			}
			Class182.smethod_0(e.Exception);
		}

		// Token: 0x0600198F RID: 6543 RVA: 0x0000A9F6 File Offset: 0x00008BF6
		private void method_10(object sender, EventArgs e)
		{
			this.method_11();
		}

		// Token: 0x06001990 RID: 6544 RVA: 0x0000AA00 File Offset: 0x00008C00
		private void method_11()
		{
			this.method_12();
			this.method_66();
		}

		// Token: 0x06001991 RID: 6545 RVA: 0x000ABD48 File Offset: 0x000A9F48
		private void method_12()
		{
			TabColorScheme colorScheme;
			TabColorScheme tabColorScheme_;
			TabColorScheme tabColorScheme_2;
			ColorScheme colorScheme_;
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				colorScheme = Base.UI.smethod_58();
				tabColorScheme_ = Base.UI.smethod_64();
				tabColorScheme_2 = Base.UI.smethod_59();
				colorScheme_ = Base.UI.smethod_56();
			}
			else
			{
				colorScheme = Base.UI.smethod_60();
				tabColorScheme_ = Base.UI.smethod_65();
				tabColorScheme_2 = Base.UI.smethod_66();
				colorScheme_ = Base.UI.smethod_57();
			}
			this.tabControl_Below.ColorScheme = colorScheme;
			this.method_15(colorScheme_);
			this.method_13(tabColorScheme_);
			this.method_14(tabColorScheme_2);
		}

		// Token: 0x06001992 RID: 6546 RVA: 0x0000AA10 File Offset: 0x00008C10
		private void method_13(TabColorScheme tabColorScheme_0)
		{
			this.tabControl_0.ColorScheme = tabColorScheme_0;
			this.tabCtrl_Trans.ColorScheme = tabColorScheme_0;
			this.trdAnalysisPanel_0.ColorScheme = tabColorScheme_0;
		}

		// Token: 0x06001993 RID: 6547 RVA: 0x0000AA38 File Offset: 0x00008C38
		private void method_14(TabColorScheme tabColorScheme_0)
		{
			this.tabCtrl_HisTrans.ColorScheme = tabColorScheme_0;
			this.tabControl_Orders.ColorScheme = tabColorScheme_0;
		}

		// Token: 0x06001994 RID: 6548 RVA: 0x000ABDB8 File Offset: 0x000A9FB8
		private void method_15(ColorScheme colorScheme_0)
		{
			this.method_16(this.tabControlPanel_MktSymbs, colorScheme_0);
			this.method_16(this.tabControlPanel_Below_Acct, colorScheme_0);
			this.method_16(this.tabControlPanel_Trading, colorScheme_0);
			this.method_16(this.tabControlPanel_BaoDian_Below, colorScheme_0);
			this.method_16(this.tabControlPanel_FnRpt, colorScheme_0);
			this.method_16(this.tabControlPanel_SelSymb, colorScheme_0);
		}

		// Token: 0x06001995 RID: 6549 RVA: 0x0000AA54 File Offset: 0x00008C54
		private void method_16(TabControlPanel tabControlPanel_0, ColorScheme colorScheme_0)
		{
			tabControlPanel_0.ColorScheme = colorScheme_0;
			tabControlPanel_0.ApplyPanelStyle();
		}

		// Token: 0x06001996 RID: 6550 RVA: 0x0000AA65 File Offset: 0x00008C65
		private void method_17(SymbDataSet symbDataSet_0)
		{
			symbDataSet_0.CurrHisDataChanged += this.method_101;
			symbDataSet_0.CurrDateChanged += this.method_102;
		}

		// Token: 0x06001997 RID: 6551 RVA: 0x0000AA8D File Offset: 0x00008C8D
		private void method_18(SymbDataSet symbDataSet_0)
		{
			symbDataSet_0.CurrHisDataChanged -= this.method_101;
			symbDataSet_0.CurrDateChanged -= this.method_102;
		}

		// Token: 0x06001998 RID: 6552 RVA: 0x000ABE18 File Offset: 0x000AA018
		private void method_19()
		{
			this.tabControlPanel_MktSymbs.ThemeAware = false;
			this.tabControl_0 = new DevComponents.DotNetBar.TabControl();
			Base.UI.smethod_69(this.tabControl_0);
			this.tabControlPanel_MktSymbs.Controls.Add(this.tabControl_0);
			this.tabControl_0.Font = this.font_1;
			this.tabControl_0.SelectedTabFont = this.font_1;
			this.tabControl_0.SelectedTabChanged += this.tabControl_0_SelectedTabChanged;
			this.method_28();
			if (this.ParentSplitPanel != null)
			{
				foreach (DataGridViewMkt dataGridView_ in this.list_0)
				{
					this.method_36(dataGridView_);
				}
				this.method_36(this.dataGridViewMkt_0);
			}
			this.tabControl_0.RecalcLayout();
			this.CurrShowMktSymbList = this.method_53();
			this.dataGridViewMkt_0.Paint += this.dataGridViewMkt_0_Paint;
			this.dataGridViewMkt_0.MouseMove += this.dataGridViewMkt_0_MouseMove;
			this.dataGridViewMkt_0.MouseDown += this.dataGridViewMkt_0_MouseDown;
			this.dataGridViewMkt_0.DragOver += this.dataGridViewMkt_0_DragOver;
			this.dataGridViewMkt_0.DragDrop += this.dataGridViewMkt_0_DragDrop;
			this.method_25();
			try
			{
				this.method_51();
			}
			catch
			{
			}
		}

		// Token: 0x06001999 RID: 6553 RVA: 0x000ABFA0 File Offset: 0x000AA1A0
		private ToolStripMenuItem method_20()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = "otItemAddZX";
			toolStripMenuItem.Text = "加入自选";
			toolStripMenuItem.Click += this.method_38;
			return toolStripMenuItem;
		}

		// Token: 0x0600199A RID: 6554 RVA: 0x000ABFE0 File Offset: 0x000AA1E0
		private ToolStripMenuItem method_21()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = "otItemDelZX";
			toolStripMenuItem.Text = "删除自选";
			toolStripMenuItem.Click += this.method_40;
			return toolStripMenuItem;
		}

		// Token: 0x0600199B RID: 6555 RVA: 0x000AC020 File Offset: 0x000AA220
		private ToolStripMenuItem method_22()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Name = "otItemLoadSymb";
			toolStripMenuItem.Text = "加载行情";
			toolStripMenuItem.Click += this.method_42;
			return toolStripMenuItem;
		}

		// Token: 0x0600199C RID: 6556 RVA: 0x000AC060 File Offset: 0x000AA260
		private ToolStripMenuItem method_23()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "导出自选...";
			toolStripMenuItem.Click += this.method_43;
			return toolStripMenuItem;
		}

		// Token: 0x0600199D RID: 6557 RVA: 0x000AC094 File Offset: 0x000AA294
		private ToolStripMenuItem method_24()
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "导入自选...";
			toolStripMenuItem.Click += this.method_44;
			return toolStripMenuItem;
		}

		// Token: 0x0600199E RID: 6558 RVA: 0x000AC0C8 File Offset: 0x000AA2C8
		private void method_25()
		{
			this.dataGridViewMkt_0.ContextMenuStrip = new ContextMenuStrip();
			this.dataGridViewMkt_0.ContextMenuStrip.Items.Add(this.method_23());
			this.dataGridViewMkt_0.ContextMenuStrip.Items.Add(this.method_24());
			Base.UI.smethod_73(this.dataGridViewMkt_0.ContextMenuStrip);
		}

		// Token: 0x0600199F RID: 6559 RVA: 0x0000AAB5 File Offset: 0x00008CB5
		private void tabControl_0_SelectedTabChanged(object sender, TabStripTabChangedEventArgs e)
		{
			if (!this.IsInitiating)
			{
				this.method_26(true, e.NewTab);
			}
		}

		// Token: 0x060019A0 RID: 6560 RVA: 0x000AC130 File Offset: 0x000AA330
		private void method_26(bool bool_2 = false, TabItem tabItem_0 = null)
		{
			DataGridViewMkt dataGridViewMkt = this.method_27(tabItem_0);
			if (dataGridViewMkt != null)
			{
				bool flag = true;
				if (bool_2)
				{
					bool flag2 = false;
					SortableBindingList<ShowMktSymb> sortableBindingList = dataGridViewMkt.DataSource as SortableBindingList<ShowMktSymb>;
					if (sortableBindingList != null && sortableBindingList.Count > 0)
					{
						int num = 15;
						if (15 > sortableBindingList.Count)
						{
							num = sortableBindingList.Count;
						}
						int num2 = sortableBindingList.Take(num).Where(new Func<ShowMktSymb, bool>(TransTabs.<>c.<>9.method_0)).Count<ShowMktSymb>();
						if ((double)num * 0.8 < (double)num2)
						{
							flag2 = true;
						}
					}
					if (flag2 && dataGridViewMkt.Tag != null)
					{
						DateTime? dateTime = Base.Data.smethod_54();
						if (dateTime != null && dateTime.Value.AddHours(4.0).Date == ((DateTime)dataGridViewMkt.Tag).AddHours(4.0).Date)
						{
							flag = false;
						}
					}
				}
				if (flag)
				{
					this.fnDataApiWorker_0.method_3(dataGridViewMkt, this.list_1);
				}
			}
		}

		// Token: 0x060019A1 RID: 6561 RVA: 0x000AC250 File Offset: 0x000AA450
		private DataGridViewMkt method_27(TabItem tabItem_0 = null)
		{
			if (tabItem_0 == null)
			{
				tabItem_0 = this.tabControl_0.SelectedTab;
			}
			Control attachedControl = tabItem_0.AttachedControl;
			DataGridViewMkt result;
			if (attachedControl != null && attachedControl.Controls.Count > 0)
			{
				result = (attachedControl.Controls[0] as DataGridViewMkt);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060019A2 RID: 6562 RVA: 0x000AC2A0 File Offset: 0x000AA4A0
		private void method_28()
		{
			this.method_46();
			this.list_0 = new List<DataGridViewMkt>();
			bool flag = true;
			List<ShowMktSymb> list = new List<ShowMktSymb>();
			List<ShowMktSymb> list2 = new List<ShowMktSymb>();
			List<ShowMktSymb> list3 = new List<ShowMktSymb>();
			List<ShowMktSymb> list4 = new List<ShowMktSymb>();
			List<ShowMktSymb> list5 = new List<ShowMktSymb>();
			List<ShowMktSymb> list6 = new List<ShowMktSymb>();
			List<ShowMktSymb> list7 = new List<ShowMktSymb>();
			List<ShowMktSymb> list8 = new List<ShowMktSymb>();
			List<ShowMktSymb> list9 = new List<ShowMktSymb>();
			List<ShowMktSymb> list10 = new List<ShowMktSymb>();
			List<ShowMktSymb> list11 = new List<ShowMktSymb>();
			List<ShowMktSymb> list12 = new List<ShowMktSymb>();
			List<ShowMktSymb> list13 = new List<ShowMktSymb>();
			List<ShowMktSymb> list14 = new List<ShowMktSymb>();
			List<ShowMktSymb> list15 = new List<ShowMktSymb>();
			foreach (ShowMktSymb showMktSymb in this.list_1)
			{
				if (showMktSymb.ExchgId == -1)
				{
					list6.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 0)
				{
					list2.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 1)
				{
					list.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 2)
				{
					list3.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 3)
				{
					list5.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 4)
				{
					list4.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 5 && showMktSymb.StkCode.StartsWith("60"))
				{
					list7.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 6 && (showMktSymb.StkCode.StartsWith("000") || (showMktSymb.StkCode.StartsWith("001") && !showMktSymb.StkCode.StartsWith("0010")) || showMktSymb.StkCode.StartsWith("003")))
				{
					list8.Add(showMktSymb);
				}
				else if ((showMktSymb.ExchgId == 5 && showMktSymb.StkCode.StartsWith("000")) || (showMktSymb.ExchgId == 6 && showMktSymb.StkCode.StartsWith("399")))
				{
					list9.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 6 && showMktSymb.StkCode.StartsWith("002"))
				{
					list10.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 6 && (showMktSymb.StkCode.StartsWith("300") || showMktSymb.StkCode.StartsWith("301")))
				{
					list11.Add(showMktSymb);
				}
				else if (showMktSymb.ExchgId == 5 && showMktSymb.StkCode.StartsWith("688"))
				{
					list12.Add(showMktSymb);
				}
				else if ((showMktSymb.ExchgId == 5 && showMktSymb.StkCode.StartsWith("5")) || (showMktSymb.ExchgId == 6 && (showMktSymb.StkCode.StartsWith("15") || showMktSymb.StkCode.StartsWith("18"))))
				{
					list13.Add(showMktSymb);
				}
				else if ((showMktSymb.ExchgId == 5 && showMktSymb.StkCode.StartsWith("11")) || (showMktSymb.ExchgId == 6 && showMktSymb.StkCode.StartsWith("12")))
				{
					list14.Add(showMktSymb);
				}
				if (showMktSymb.IsInZiXuan)
				{
					list15.Add(showMktSymb);
				}
			}
			if (flag)
			{
				if (list7.Count > 0)
				{
					this.method_31("上证A股", "上海证券交易所Ａ股主板股票", Enum1.const_0, list7, null);
				}
				if (list8.Count > 0)
				{
					this.method_31("深证A股", "深圳证券交易所Ａ股主板股票", Enum1.const_0, list8, null);
				}
				if (list10.Count > 0)
				{
					this.method_31("中小板", "深市中小板股票", Enum1.const_0, list10, null);
				}
				if (list11.Count > 0)
				{
					this.method_31("创业板", "深市创业板股票", Enum1.const_0, list11, null);
				}
				if (list12.Count > 0)
				{
					this.method_31("科创版", "沪市科创版股票", Enum1.const_0, list12, null);
				}
				if (list9.Count > 0)
				{
					this.method_31("指数", "沪深股票市场指数", Enum1.const_1, list9, null);
				}
				if (list13.Count > 0)
				{
					this.method_31("基金", "沪深基金", Enum1.const_1, list13, null);
				}
			}
			if (list14.Count > 0)
			{
				this.method_31("可转债", "沪深可转债", Enum1.const_4, list14, null);
			}
			if (list.Count > 0)
			{
				list = list.OrderBy(new Func<ShowMktSymb, string>(TransTabs.<>c.<>9.method_1)).ToList<ShowMktSymb>();
				this.method_31("中金所", "中国金融期货交易所期货", Enum1.const_2, list, null);
			}
			if (list2.Count > 0)
			{
				this.method_31("上期能源", "上海国际能源交易中心期货", Enum1.const_2, list2, null);
			}
			if (list3.Count > 0)
			{
				this.method_31("上期所", "上海期货交易所期货", Enum1.const_2, list3, null);
			}
			if (list4.Count > 0)
			{
				this.method_31("大商所", "大连商品交易所期货", Enum1.const_2, list4, null);
			}
			if (list5.Count > 0)
			{
				this.method_31("郑商所", "郑州商品交易所期货", Enum1.const_2, list5, null);
			}
			if (list6.Count > 0)
			{
				this.method_31("广期所", "广州期货交易所", Enum1.const_2, list6, null);
			}
			List<ShowMktSymb> list16 = this.method_30(list15);
			this.dataGridViewMkt_0 = this.method_31("自选板块", "自选板块", this.method_29(list16), list16, null);
			this.dataGridViewMkt_0.KeyUp += this.dataGridViewMkt_0_KeyUp;
			this.dataGridViewMkt_0.MultiSelect = true;
			this.dataGridViewMkt_0.AllowDrop = true;
			this.dataGridViewMkt_0.Refresh();
		}

		// Token: 0x060019A3 RID: 6563 RVA: 0x000AC864 File Offset: 0x000AAA64
		private void dataGridViewMkt_0_KeyUp(object sender, KeyEventArgs e)
		{
			DataGridViewMkt dataGridViewMkt = sender as DataGridViewMkt;
			if (e.KeyCode == Keys.Delete && dataGridViewMkt.SelectedRows.Count > 0 && MessageBox.Show("删除选中的自选品种吗？", "请确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
			{
				foreach (object obj in dataGridViewMkt.SelectedRows)
				{
					DataGridViewRow dataGridViewRow_ = (DataGridViewRow)obj;
					this.method_41(dataGridViewRow_);
				}
			}
		}

		// Token: 0x060019A4 RID: 6564 RVA: 0x000AC8F4 File Offset: 0x000AAAF4
		private Enum1 method_29(IList<ShowMktSymb> ilist_0)
		{
			Enum1 result = Enum1.const_0;
			if (TApp.IsStIncluded)
			{
				if (TApp.IsFtIncluded)
				{
					result = Enum1.const_1;
				}
			}
			else
			{
				result = Enum1.const_2;
			}
			return result;
		}

		// Token: 0x060019A5 RID: 6565 RVA: 0x000AC91C File Offset: 0x000AAB1C
		private List<ShowMktSymb> method_30(List<ShowMktSymb> list_4 = null)
		{
			if (list_4 == null)
			{
				list_4 = this.list_1.Where(new Func<ShowMktSymb, bool>(TransTabs.<>c.<>9.method_2)).ToList<ShowMktSymb>();
			}
			if (list_4.Count > 0)
			{
				try
				{
					list_4 = list_4.OrderBy(new Func<ShowMktSymb, int?>(TransTabs.<>c.<>9.method_3)).ToList<ShowMktSymb>();
				}
				catch
				{
				}
				this.method_33(list_4);
			}
			return list_4;
		}

		// Token: 0x060019A6 RID: 6566 RVA: 0x000AC9B4 File Offset: 0x000AABB4
		private DataGridViewMkt method_31(string string_1, string string_2, Enum1 enum1_0, List<ShowMktSymb> list_4, string[] string_3 = null)
		{
			TabItem tabItem = new TabItem(this.icontainer_0);
			this.tabControl_0.Tabs.Add(tabItem);
			tabItem.Text = string_1;
			tabItem.Tooltip = string_2;
			tabItem.Name = string_1;
			TabControlPanel tabControlPanel = new TabControlPanel();
			this.tabControl_0.Controls.Add(tabControlPanel);
			tabControlPanel.Dock = DockStyle.Fill;
			tabControlPanel.Padding = new System.Windows.Forms.Padding(0, 0, 0, 0);
			tabControlPanel.TabItem = tabItem;
			tabItem.AttachedControl = tabControlPanel;
			DataGridViewMkt dataGridViewMkt = new DataGridViewMkt(1350);
			tabControlPanel.Controls.Add(dataGridViewMkt);
			dataGridViewMkt.CellDoubleClick += this.method_49;
			this.list_0.Add(dataGridViewMkt);
			dataGridViewMkt.RowContextMenuStripNeeded += this.method_45;
			dataGridViewMkt.CellToolTipTextNeeded += this.method_34;
			dataGridViewMkt.ShowCellToolTips = true;
			dataGridViewMkt.Name = string_1;
			dataGridViewMkt.MktDgvType = enum1_0;
			dataGridViewMkt.SourceMktSymbLst = list_4;
			dataGridViewMkt.IdxClassAry = string_3;
			if (list_4 != null)
			{
				SortableBindingList<ShowMktSymb> sortableBindingList_ = new SortableBindingList<ShowMktSymb>(list_4);
				dataGridViewMkt.method_8(sortableBindingList_);
			}
			if (this.class323_0 != null)
			{
				dataGridViewMkt.Tag = this.class323_0.CurrDate;
			}
			return dataGridViewMkt;
		}

		// Token: 0x060019A7 RID: 6567 RVA: 0x000ACAE8 File Offset: 0x000AACE8
		private void dataGridViewMkt_0_MouseMove(object sender, MouseEventArgs e)
		{
			DataGridView dataGridView = sender as DataGridView;
			if (e.Button == MouseButtons.Left && this.rectangle_0 != Rectangle.Empty && !this.rectangle_0.Contains(e.X, e.Y))
			{
				dataGridView.DoDragDrop(dataGridView.Rows[this.int_1], DragDropEffects.Move);
			}
		}

		// Token: 0x060019A8 RID: 6568 RVA: 0x000ACB50 File Offset: 0x000AAD50
		private void dataGridViewMkt_0_MouseDown(object sender, MouseEventArgs e)
		{
			DataGridView dataGridView = sender as DataGridView;
			this.int_1 = dataGridView.HitTest(e.X, e.Y).RowIndex;
			if (this.int_1 != -1)
			{
				Size dragSize = SystemInformation.DragSize;
				this.rectangle_0 = new Rectangle(new Point(e.X - dragSize.Width / 2, e.Y - dragSize.Height / 2), dragSize);
			}
			else
			{
				this.rectangle_0 = Rectangle.Empty;
				if (e.Button == MouseButtons.Right)
				{
					this.method_25();
				}
			}
		}

		// Token: 0x060019A9 RID: 6569 RVA: 0x0000AACE File Offset: 0x00008CCE
		private void dataGridViewMkt_0_DragOver(object sender, DragEventArgs e)
		{
			e.Effect = DragDropEffects.Move;
		}

		// Token: 0x060019AA RID: 6570 RVA: 0x000ACBE4 File Offset: 0x000AADE4
		private void dataGridViewMkt_0_DragDrop(object sender, DragEventArgs e)
		{
			DataGridView dataGridView = sender as DataGridView;
			Point point = dataGridView.PointToClient(new Point(e.X, e.Y));
			this.int_2 = dataGridView.HitTest(point.X, point.Y).RowIndex;
			if (e.Effect == DragDropEffects.Move && this.int_2 >= 0)
			{
				ShowMktSymb item = ((DataGridViewRow)e.Data.GetData(typeof(DataGridViewRow))).DataBoundItem as ShowMktSymb;
				SortableBindingList<ShowMktSymb> sortableBindingList = dataGridView.DataSource as SortableBindingList<ShowMktSymb>;
				int num = this.int_2;
				if (num < 0)
				{
					num = 0;
				}
				sortableBindingList.Remove(item);
				sortableBindingList.Insert(num, item);
				dataGridView.Rows[num].Selected = true;
				this.method_33(sortableBindingList);
			}
		}

		// Token: 0x060019AB RID: 6571 RVA: 0x0000AAD9 File Offset: 0x00008CD9
		private void method_32()
		{
			if (this.dataGridViewMkt_0.DataSource != null)
			{
				this.method_33(this.dataGridViewMkt_0.DataSource as SortableBindingList<ShowMktSymb>);
			}
		}

		// Token: 0x060019AC RID: 6572 RVA: 0x000ACCB0 File Offset: 0x000AAEB0
		private void method_33(IList<ShowMktSymb> ilist_0)
		{
			if (ilist_0 != null)
			{
				for (int i = 0; i < ilist_0.Count; i++)
				{
					ilist_0[i].IdxInZixuanDGV = new int?(i);
				}
			}
		}

		// Token: 0x060019AD RID: 6573 RVA: 0x000ACCE8 File Offset: 0x000AAEE8
		private void method_34(object sender, DataGridViewCellToolTipTextNeededEventArgs e)
		{
			DataGridView dataGridView = sender as DataGridView;
			if (e.RowIndex >= 0 && !Base.UI.IsInCreateNewPageState)
			{
				DataGridViewRow dataGridViewRow = dataGridView.Rows[e.RowIndex];
				try
				{
					if ((dataGridViewRow.DataBoundItem as ShowMktSymb).StkId != Base.Data.CurrSelectedSymbol.ID)
					{
						e.ToolTipText = "双击鼠标切换至该品种";
					}
				}
				catch
				{
				}
			}
		}

		// Token: 0x060019AE RID: 6574 RVA: 0x000ACD5C File Offset: 0x000AAF5C
		private void method_35()
		{
			try
			{
				if (Base.UI.Form.LastFuncTabsIdx != null)
				{
					this.SelectedTabIndex = Base.UI.Form.LastFuncTabsIdx.Value;
				}
				if (Base.UI.Form.LastMktSymbTabsIdx != null)
				{
					this.MktSelectedTabIndex = Base.UI.Form.LastMktSymbTabsIdx.Value;
				}
				if (this.SelectedTab.Text == "交易分析" || this.SelectedTab.Text == "账户统计")
				{
					this.trdAnalysisPanel_0.method_6();
				}
			}
			catch
			{
			}
		}

		// Token: 0x060019AF RID: 6575 RVA: 0x0000AB00 File Offset: 0x00008D00
		private void method_36(DataGridView dataGridView_0)
		{
			dataGridView_0.ContextMenuStrip = new ContextMenuStrip();
			this.method_87(dataGridView_0.ContextMenuStrip);
		}

		// Token: 0x060019B0 RID: 6576 RVA: 0x000ACE14 File Offset: 0x000AB014
		public void method_37(int int_3)
		{
			ShowMktSymb showMktSymb = this.method_54(int_3);
			if (showMktSymb != null)
			{
				this.method_57(showMktSymb);
				this.method_39(showMktSymb);
			}
		}

		// Token: 0x060019B1 RID: 6577 RVA: 0x000ACE40 File Offset: 0x000AB040
		private void method_38(object sender, EventArgs e)
		{
			ToolStripMenuItem toolStripMenuItem = sender as ToolStripMenuItem;
			if (toolStripMenuItem != null && toolStripMenuItem.Tag != null)
			{
				ShowMktSymb showMktSymb_ = (toolStripMenuItem.Tag as DataGridViewRow).DataBoundItem as ShowMktSymb;
				this.method_39(showMktSymb_);
			}
		}

		// Token: 0x060019B2 RID: 6578 RVA: 0x000ACE80 File Offset: 0x000AB080
		private void method_39(ShowMktSymb showMktSymb_0)
		{
			if (showMktSymb_0 != null && !showMktSymb_0.IsInZiXuan)
			{
				SortableBindingList<ShowMktSymb> sortableBindingList = this.dataGridViewMkt_0.DataSource as SortableBindingList<ShowMktSymb>;
				if (sortableBindingList == null)
				{
					sortableBindingList = new SortableBindingList<ShowMktSymb>();
				}
				showMktSymb_0.IsInZiXuan = true;
				sortableBindingList.Add(showMktSymb_0);
				if (sortableBindingList.Count == 1)
				{
					this.dataGridViewMkt_0.MktDgvType = this.method_29(sortableBindingList);
					this.dataGridViewMkt_0.Refresh();
				}
				this.method_32();
				this.method_47();
			}
		}

		// Token: 0x060019B3 RID: 6579 RVA: 0x000ACEF4 File Offset: 0x000AB0F4
		private void method_40(object sender, EventArgs e)
		{
			ToolStripMenuItem toolStripMenuItem = sender as ToolStripMenuItem;
			if (toolStripMenuItem != null && toolStripMenuItem.Tag != null)
			{
				DataGridViewRow dataGridViewRow = toolStripMenuItem.Tag as DataGridViewRow;
				if (dataGridViewRow != null)
				{
					this.method_41(dataGridViewRow);
				}
			}
		}

		// Token: 0x060019B4 RID: 6580 RVA: 0x000ACF2C File Offset: 0x000AB12C
		private void method_41(DataGridViewRow dataGridViewRow_0)
		{
			ShowMktSymb showMktSymb = dataGridViewRow_0.DataBoundItem as ShowMktSymb;
			if (showMktSymb != null && showMktSymb.IsInZiXuan)
			{
				SortableBindingList<ShowMktSymb> sortableBindingList = this.dataGridViewMkt_0.DataSource as SortableBindingList<ShowMktSymb>;
				showMktSymb.IsInZiXuan = false;
				sortableBindingList.Remove(showMktSymb);
				this.method_33(sortableBindingList);
			}
		}

		// Token: 0x060019B5 RID: 6581 RVA: 0x000ACF7C File Offset: 0x000AB17C
		private void method_42(object sender, EventArgs e)
		{
			ShowMktSymb showMktSymb_ = ((sender as ToolStripMenuItem).Tag as DataGridViewRow).DataBoundItem as ShowMktSymb;
			this.method_50(showMktSymb_, true);
		}

		// Token: 0x060019B6 RID: 6582 RVA: 0x000ACFB0 File Offset: 0x000AB1B0
		private void method_43(object sender, EventArgs e)
		{
			SortableBindingList<ShowMktSymb> sortableBindingList = this.dataGridViewMkt_0.DataSource as SortableBindingList<ShowMktSymb>;
			if (sortableBindingList != null && sortableBindingList.Count > 0)
			{
				string text = Utility.SaveFile("导出自选列表", "TEx自选列表文件(*.tzx)|*.tzx|文本文件(*.txt)|*.txt|所有文件(*.*)|*.*", Environment.GetFolderPath(Environment.SpecialFolder.Personal), "自选列表.tzx");
				if (!string.IsNullOrEmpty(text))
				{
					StringBuilder stringBuilder = new StringBuilder();
					bool flag = text.EndsWith("txt");
					try
					{
						foreach (ShowMktSymb showMktSymb in sortableBindingList)
						{
							if (stringBuilder.Length > 0)
							{
								stringBuilder.Append(Environment.NewLine);
							}
							string value;
							if (flag)
							{
								value = showMktSymb.StkCode;
							}
							else
							{
								value = showMktSymb.method_0();
							}
							stringBuilder.Append(value);
						}
						string content = stringBuilder.ToString();
						Utility.SaveFile(text, content, null);
						MessageBox.Show("导出成功！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
			}
		}

		// Token: 0x060019B7 RID: 6583 RVA: 0x000AD0C4 File Offset: 0x000AB2C4
		private void method_44(object sender, EventArgs e)
		{
			OpenFileDialog openFileDialog = new OpenFileDialog();
			openFileDialog.Title = "打开自选列表文件";
			openFileDialog.Filter = "TEx自选列表文件(*.tzx)|*.tzx|文本文件(*.txt)|*.txt|所有文件(*.*)|*.*";
			if (openFileDialog.ShowDialog() == DialogResult.OK)
			{
				SortableBindingList<ShowMktSymb> sortableBindingList = new SortableBindingList<ShowMktSymb>();
				try
				{
					Dictionary<string, StkSymbol> dictionary = null;
					foreach (string text in File.ReadAllLines(openFileDialog.FileName))
					{
						try
						{
							if (!string.IsNullOrEmpty(text))
							{
								if (text.Contains(","))
								{
									ShowMktSymb showMktSymb = new ShowMktSymb();
									showMktSymb.method_1(text);
									showMktSymb.IsInZiXuan = true;
									sortableBindingList.Add(showMktSymb);
								}
								else
								{
									if (dictionary == null)
									{
										dictionary = Base.Data.UsrStkSymbols.Values.ToDictionary(new Func<StkSymbol, string>(TransTabs.<>c.<>9.method_4), new Func<StkSymbol, StkSymbol>(TransTabs.<>c.<>9.method_5));
									}
									string value = null;
									if (text.IndexOf(" ") > 0)
									{
										value = " ";
									}
									else if (text.IndexOf("   ") > 0)
									{
										value = "   ";
									}
									int startIndex = 0;
									int length = text.Length;
									if (!string.IsNullOrEmpty(value))
									{
										length = text.IndexOf(value);
									}
									string text2 = text.Substring(startIndex, length).Trim();
									string text3 = text2.Substring(0, 1);
									bool flag = false;
									bool flag2 = false;
									if (text2.Length > 6)
									{
										if (text2.Length == 7 && (text3 == "0" || text3 == "1"))
										{
											flag2 = true;
										}
									}
									else
									{
										if (text2.Length <= 2)
										{
											break;
										}
										if (!Utility.IsDigitChars(text3, false, false))
										{
											string text4 = text2.Substring(0, 2);
											bool flag3 = text4.Equals("SZ", StringComparison.InvariantCultureIgnoreCase);
											bool flag4 = text4.Equals("SH", StringComparison.InvariantCultureIgnoreCase);
											if (flag3 || flag4)
											{
												text2 = (flag3 ? "1" : ("0" + text2.Substring(2)));
												flag2 = true;
											}
											else
											{
												flag = true;
											}
										}
									}
									if (!flag && !flag2)
									{
										text2 = "0" + text2;
									}
									StkSymbol stkSymbol = null;
									text2 = text2.ToUpper();
									dictionary.TryGetValue(text2, out stkSymbol);
									if (stkSymbol == null)
									{
										if (flag)
										{
											if (text2.Length > 4 && Utility.IsDigitChars(text2.Substring(text2.Length - 3), false, false))
											{
												text2 = text2.Substring(0, text2.Length - 4) + text2.Substring(text2.Length - 2);
												dictionary.TryGetValue(text2, out stkSymbol);
											}
										}
										else if (!flag2)
										{
											text2 = "1" + text2.Substring(1);
											dictionary.TryGetValue(text2, out stkSymbol);
										}
									}
									if (stkSymbol != null)
									{
										sortableBindingList.Add(new ShowMktSymb(stkSymbol)
										{
											IsInZiXuan = true
										});
									}
								}
							}
						}
						catch (Exception exception_)
						{
							Class182.smethod_0(exception_);
						}
					}
					if (sortableBindingList.Count > 0)
					{
						SortableBindingList<ShowMktSymb> sortableBindingList2 = this.dataGridViewMkt_0.DataSource as SortableBindingList<ShowMktSymb>;
						if (sortableBindingList2 != null && sortableBindingList2.Count != 0)
						{
							try
							{
								Dictionary<int, ShowMktSymb> dictionary2 = sortableBindingList2.ToDictionary(new Func<ShowMktSymb, int>(TransTabs.<>c.<>9.method_6), new Func<ShowMktSymb, ShowMktSymb>(TransTabs.<>c.<>9.method_7));
								foreach (ShowMktSymb showMktSymb2 in sortableBindingList)
								{
									if (!dictionary2.ContainsKey(showMktSymb2.StkId))
									{
										sortableBindingList2.Add(showMktSymb2);
									}
								}
								goto IL_3AC;
							}
							catch (Exception exception_2)
							{
								Class182.smethod_0(exception_2);
								goto IL_3AC;
							}
						}
						this.dataGridViewMkt_0.method_8(sortableBindingList);
						IL_3AC:
						this.method_47();
						MessageBox.Show(string.Format("导入成功！共导入{0}条记录。", sortableBindingList.Count), "提醒", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					}
					else
					{
						MessageBox.Show("无有效记录，导入失败！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					}
				}
				catch
				{
					MessageBox.Show("文件内容读取错误，导入失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
			}
		}

		// Token: 0x060019B8 RID: 6584 RVA: 0x000AD53C File Offset: 0x000AB73C
		private void method_45(object sender, DataGridViewRowContextMenuStripNeededEventArgs e)
		{
			DataGridViewMkt dataGridViewMkt = sender as DataGridView;
			DataGridViewRow dataGridViewRow = (sender as DataGridView).Rows[e.RowIndex];
			ShowMktSymb showMktSymb = dataGridViewRow.DataBoundItem as ShowMktSymb;
			dataGridViewRow.Selected = true;
			ContextMenuStrip contextMenuStrip = new ContextMenuStrip();
			StkSymbol stkSymbol = SymbMgr.smethod_3(showMktSymb.StkId);
			if (!Base.UI.IsInCreateNewPageState && (Base.Data.CurrSelectedSymbol == null || stkSymbol.ID != Base.Data.CurrSelectedSymbol.ID))
			{
				contextMenuStrip.Items.Add(this.method_22());
			}
			if (dataGridViewMkt == this.dataGridViewMkt_0)
			{
				ToolStripMenuItem toolStripMenuItem = this.method_21();
				toolStripMenuItem.Tag = dataGridViewRow;
				contextMenuStrip.Items.Add(toolStripMenuItem);
				contextMenuStrip.Items.Add(this.method_23());
				contextMenuStrip.Items.Add(this.method_24());
			}
			else
			{
				ToolStripMenuItem toolStripMenuItem2 = this.method_20();
				toolStripMenuItem2.Tag = dataGridViewRow;
				contextMenuStrip.Items.Add(toolStripMenuItem2);
			}
			if (this.ParentSplitPanel != null)
			{
				contextMenuStrip.Items.Add(new ToolStripSeparator());
				this.method_87(contextMenuStrip);
			}
			Base.UI.smethod_73(contextMenuStrip);
			e.ContextMenuStrip = contextMenuStrip;
			e.ContextMenuStrip.Items[0].Tag = dataGridViewRow;
		}

		// Token: 0x060019B9 RID: 6585 RVA: 0x000AD668 File Offset: 0x000AB868
		private void method_46()
		{
			this.class323_0 = TransTabs.smethod_0();
			Dictionary<int, ShowMktSymb> dictionary = null;
			if (this.class323_0 != null)
			{
				dictionary = this.class323_0.ShowMktSymbDict;
			}
			this.list_1 = new List<ShowMktSymb>();
			foreach (KeyValuePair<int, StkSymbol> keyValuePair in Base.Data.UsrStkSymbols)
			{
				ShowMktSymb showMktSymb = null;
				if (dictionary != null)
				{
					dictionary.TryGetValue(keyValuePair.Key, out showMktSymb);
				}
				if (showMktSymb == null)
				{
					showMktSymb = new ShowMktSymb(keyValuePair.Value);
				}
				this.list_1.Add(showMktSymb);
			}
		}

		// Token: 0x060019BA RID: 6586 RVA: 0x0000AB1B File Offset: 0x00008D1B
		private void dataGridViewMkt_0_Paint(object sender, PaintEventArgs e)
		{
			if (this.dataGridViewMkt_0.Columns[0].Visible)
			{
				this.dataGridViewMkt_0.Columns[0].Visible = false;
				this.dataGridViewMkt_0.Refresh();
			}
		}

		// Token: 0x060019BB RID: 6587 RVA: 0x000AD714 File Offset: 0x000AB914
		private static Class323 smethod_0()
		{
			string filePath = TransTabs.string_0;
			if (Utility.FileExists(filePath))
			{
				Class323 result;
				try
				{
					string value = null;
					try
					{
						value = Utility.GetStringFromCompressedFile(filePath, CompressAlgm.LZMA, null);
					}
					catch
					{
					}
					if (!string.IsNullOrEmpty(value))
					{
						Class323 @class = JsonConvert.DeserializeObject<Class323>(value);
						DateTime? dateTime = Base.Data.smethod_54();
						if (@class != null && dateTime != null && @class.UserName == TApp.UserName && @class.CurrDate == dateTime.Value.Date)
						{
							TExPackage? texPkg = @class.TExPkg;
							TExPackage? texPkg2 = TApp.SrvParams.TExPkg;
							if (texPkg.GetValueOrDefault() == texPkg2.GetValueOrDefault() & texPkg != null == (texPkg2 != null))
							{
								if (!string.IsNullOrEmpty(@class.ShowMktSymbCsv))
								{
									string[] array = @class.ShowMktSymbCsv.Split(new string[]
									{
										Environment.NewLine
									}, StringSplitOptions.None);
									@class.ShowMktSymbDict = new Dictionary<int, ShowMktSymb>();
									foreach (string text in array)
									{
										ShowMktSymb showMktSymb = new ShowMktSymb();
										showMktSymb.method_1(text);
										@class.ShowMktSymbDict[showMktSymb.StkId] = showMktSymb;
									}
								}
								result = @class;
								goto IL_137;
							}
						}
					}
					goto IL_13D;
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
					goto IL_13D;
				}
				IL_137:
				return result;
			}
			IL_13D:
			return null;
		}

		// Token: 0x060019BC RID: 6588 RVA: 0x000AD89C File Offset: 0x000ABA9C
		public void method_47()
		{
			DateTime? dateTime = Base.Data.smethod_54();
			if (dateTime != null && this.list_0 != null)
			{
				Class323 @class = new Class323();
				@class.UserName = TApp.UserName;
				@class.TExPkg = TApp.SrvParams.TExPkg;
				@class.CurrDate = dateTime.Value.Date;
				Dictionary<int, ShowMktSymb> dictionary = new Dictionary<int, ShowMktSymb>();
				int num = 0;
				foreach (DataGridViewMkt dataGridViewMkt in this.list_0)
				{
					if (dataGridViewMkt.DataSource != null)
					{
						bool flag = dataGridViewMkt.Tag != null && ((DateTime)dataGridViewMkt.Tag).AddHours(4.0).Date == dateTime.Value.AddHours(4.0).Date;
						foreach (ShowMktSymb showMktSymb in (dataGridViewMkt.DataSource as SortableBindingList<ShowMktSymb>))
						{
							if (showMktSymb.IsInZiXuan || showMktSymb.close != null || (flag && (!string.IsNullOrEmpty(showMktSymb.cb_stk_code) || showMktSymb.total_share != null)))
							{
								dictionary[showMktSymb.StkId] = showMktSymb;
								num++;
							}
						}
					}
				}
				try
				{
					StringBuilder stringBuilder = new StringBuilder();
					foreach (KeyValuePair<int, ShowMktSymb> keyValuePair in dictionary)
					{
						if (stringBuilder.Length > 0)
						{
							stringBuilder.Append(Environment.NewLine);
						}
						stringBuilder.Append(keyValuePair.Value.method_0());
					}
					@class.ShowMktSymbCsv = stringBuilder.ToString();
					string str = JsonConvert.SerializeObject(@class);
					string filePath = TransTabs.string_0;
					Utility.GenCompressedFile(str, filePath, CompressAlgm.LZMA, null);
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
		}

		// Token: 0x060019BD RID: 6589 RVA: 0x000ADB20 File Offset: 0x000ABD20
		public void method_48()
		{
			List<StkSymbol> list = Base.Data.smethod_115();
			foreach (DataGridViewMkt dataGridViewMkt in this.list_0)
			{
				BindingList<ShowMktSymb> bindingList = dataGridViewMkt.DataSource as BindingList<ShowMktSymb>;
				if (bindingList != null)
				{
					using (IEnumerator<ShowMktSymb> enumerator2 = bindingList.GetEnumerator())
					{
						while (enumerator2.MoveNext())
						{
							TransTabs.Class319 @class = new TransTabs.Class319();
							@class.showMktSymb_0 = enumerator2.Current;
							if (list.Exists(new Predicate<StkSymbol>(@class.method_0)))
							{
								@class.showMktSymb_0.IsDownloaded = true;
							}
						}
					}
				}
			}
		}

		// Token: 0x060019BE RID: 6590 RVA: 0x000ADBE4 File Offset: 0x000ABDE4
		private void method_49(object sender, DataGridViewCellEventArgs e)
		{
			DataGridView dataGridView = sender as DataGridView;
			if (e.RowIndex < dataGridView.Rows.Count && e.RowIndex >= 0)
			{
				ShowMktSymb showMktSymb_ = dataGridView.Rows[e.RowIndex].DataBoundItem as ShowMktSymb;
				this.method_50(showMktSymb_, true);
			}
		}

		// Token: 0x060019BF RID: 6591 RVA: 0x000ADC3C File Offset: 0x000ABE3C
		private void method_50(ShowMktSymb showMktSymb_0, bool bool_2 = true)
		{
			StkSymbol stkSymbol = SymbMgr.smethod_3(showMktSymb_0.StkId);
			if (Base.Data.CurrSelectedSymbol == null || stkSymbol.ID != Base.Data.CurrSelectedSymbol.ID)
			{
				Base.UI.smethod_176(Base.Data.string_1);
				Base.Data.smethod_69(stkSymbol, false, false, false, null);
				Base.UI.smethod_178();
			}
			if (bool_2 && Base.UI.Chart.IsSingleFixedContent && Base.UI.VisibleChtCtrlList == null)
			{
				this.method_88();
			}
		}

		// Token: 0x060019C0 RID: 6592 RVA: 0x000ADCB4 File Offset: 0x000ABEB4
		private void method_51()
		{
			foreach (SymbDataSet symbDataSet in Base.Data.SymbDataSets)
			{
				this.method_52(symbDataSet.SymblID);
			}
		}

		// Token: 0x060019C1 RID: 6593 RVA: 0x000ADD10 File Offset: 0x000ABF10
		private void method_52(int int_3)
		{
			this.method_56(int_3);
			foreach (KeyValuePair<DataGridView, int> keyValuePair in this.DataGridViewListWithCurrMktSymb)
			{
				keyValuePair.Key.Refresh();
			}
		}

		// Token: 0x060019C2 RID: 6594 RVA: 0x000ADD74 File Offset: 0x000ABF74
		private List<ShowMktSymb> method_53()
		{
			List<ShowMktSymb> list = null;
			if (Base.Data.SymbDataSets != null)
			{
				list = new List<ShowMktSymb>();
				foreach (SymbDataSet symbDataSet in Base.Data.SymbDataSets)
				{
					ShowMktSymb showMktSymb = this.method_54(symbDataSet.SymblID);
					if (showMktSymb != null)
					{
						list.Add(showMktSymb);
					}
				}
			}
			return list;
		}

		// Token: 0x060019C3 RID: 6595 RVA: 0x000ADDF0 File Offset: 0x000ABFF0
		private ShowMktSymb method_54(int int_3)
		{
			ShowMktSymb result = null;
			this.list_3 = new List<KeyValuePair<DataGridView, int>>();
			foreach (DataGridViewMkt dataGridViewMkt in this.list_0)
			{
				ShowMktSymb showMktSymb = this.method_55(int_3, dataGridViewMkt);
				if (showMktSymb != null)
				{
					int num = (dataGridViewMkt.DataSource as SortableBindingList<ShowMktSymb>).IndexOf(showMktSymb);
					if (num >= 0 && num < dataGridViewMkt.Rows.Count)
					{
						KeyValuePair<DataGridView, int> item = new KeyValuePair<DataGridView, int>(dataGridViewMkt, num);
						this.list_3.Add(item);
					}
					result = showMktSymb;
				}
			}
			return result;
		}

		// Token: 0x060019C4 RID: 6596 RVA: 0x000ADEA0 File Offset: 0x000AC0A0
		private ShowMktSymb method_55(int int_3, DataGridView dataGridView_0)
		{
			TransTabs.Class320 @class = new TransTabs.Class320();
			@class.int_0 = int_3;
			ShowMktSymb result = null;
			SortableBindingList<ShowMktSymb> sortableBindingList = dataGridView_0.DataSource as SortableBindingList<ShowMktSymb>;
			if (sortableBindingList != null)
			{
				result = sortableBindingList.FirstOrDefault(new Func<ShowMktSymb, bool>(@class.method_0));
			}
			return result;
		}

		// Token: 0x060019C5 RID: 6597 RVA: 0x000ADEE4 File Offset: 0x000AC0E4
		private ShowMktSymb method_56(int int_3)
		{
			ShowMktSymb showMktSymb = this.method_54(int_3);
			ShowMktSymb result;
			if (showMktSymb != null)
			{
				result = this.method_57(showMktSymb);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060019C6 RID: 6598 RVA: 0x000ADF0C File Offset: 0x000AC10C
		private ShowMktSymb method_57(ShowMktSymb showMktSymb_0)
		{
			SymbDataSet symbDataSet = Base.Data.smethod_49(showMktSymb_0.StkId, false);
			ShowMktSymb result;
			if (symbDataSet != null && symbDataSet.HasValidDataSet)
			{
				showMktSymb_0.LastDT = new DateTime?(symbDataSet.CurrHisDataSet.CurrHisData.Date);
				double? lastDayClose = symbDataSet.CurrHisDataSet.LastDayClose;
				if (lastDayClose != null)
				{
					showMktSymb_0.LastDayClose = new decimal?(Convert.ToDecimal(lastDayClose));
				}
				showMktSymb_0.Price = new decimal?(Convert.ToDecimal(symbDataSet.CurrHisDataSet.CurrHisData.Close));
				bool flag = (Base.UI.Form.IsSpanMoveNext || Base.UI.Form.IsSpanMovePrev) && Base.UI.Form.LastSpanMoveDT != null;
				try
				{
					if (flag)
					{
						List<HisData> source = Base.Data.smethod_56(showMktSymb_0.StkId, null);
						showMktSymb_0.Vol = new decimal?(Convert.ToDecimal(source.Sum(new Func<HisData, double?>(TransTabs.<>c.<>9.method_8))));
						showMktSymb_0.Open = new decimal?(Convert.ToDecimal(source.First<HisData>().Open));
						showMktSymb_0.High = new decimal?(Convert.ToDecimal(source.Max(new Func<HisData, double>(TransTabs.<>c.<>9.method_9))));
						showMktSymb_0.Low = new decimal?(Convert.ToDecimal(source.Min(new Func<HisData, double>(TransTabs.<>c.<>9.method_10))));
					}
					else
					{
						List<HisData> list = symbDataSet.method_106(symbDataSet.CurrHisDataSet.FetchedHisDataList, symbDataSet.CurrHisDataSet.CurrDayBeginDT, symbDataSet.CurrHisDataSet.CurrHisData.Date, false, true, new int?(1));
						if (list != null && list.Any<HisData>())
						{
							showMktSymb_0.Vol = new decimal?(Convert.ToDecimal(list.Sum(new Func<HisData, double?>(TransTabs.<>c.<>9.method_11))));
							showMktSymb_0.Open = new decimal?(Convert.ToDecimal(list.First<HisData>().Open));
							showMktSymb_0.High = new decimal?(Convert.ToDecimal(list.Max(new Func<HisData, double>(TransTabs.<>c.<>9.method_12))));
							showMktSymb_0.Low = new decimal?(Convert.ToDecimal(list.Min(new Func<HisData, double>(TransTabs.<>c.<>9.method_13))));
						}
						else
						{
							showMktSymb_0.Vol = new decimal?(Convert.ToDecimal(symbDataSet.CurrHisDataSet.CurrHisData.Volume));
							showMktSymb_0.Open = new decimal?(Convert.ToDecimal(symbDataSet.CurrHisDataSet.CurrHisData.Open));
							showMktSymb_0.High = new decimal?(Convert.ToDecimal(symbDataSet.CurrHisDataSet.CurrHisData.High));
							showMktSymb_0.Low = new decimal?(Convert.ToDecimal(symbDataSet.CurrHisDataSet.CurrHisData.Low));
						}
					}
					if (symbDataSet.CurrHisDataSet.CurrHisData != null && symbDataSet.CurrHisDataSet.CurrHisData.Amount != null && !symbDataSet.CurrSymbol.IsStock)
					{
						showMktSymb_0.Amount = new decimal?(Convert.ToDecimal(symbDataSet.CurrHisDataSet.CurrHisData.Amount));
					}
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
				showMktSymb_0 = this.method_58(showMktSymb_0);
				showMktSymb_0.IsDownloaded = true;
				result = showMktSymb_0;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060019C7 RID: 6599 RVA: 0x000AE2D8 File Offset: 0x000AC4D8
		private ShowMktSymb method_58(ShowMktSymb showMktSymb_0)
		{
			StkSymbol stkSymbol = Base.Data.smethod_49(showMktSymb_0.StkId, false).CurrSymbol;
			if (stkSymbol.ID != showMktSymb_0.StkId)
			{
				stkSymbol = SymbMgr.smethod_3(showMktSymb_0.StkId);
			}
			int digitNb = stkSymbol.DigitNb;
			showMktSymb_0.Price = new decimal?(Math.Round(showMktSymb_0.Price.Value, digitNb));
			if (showMktSymb_0.Vol != null)
			{
				showMktSymb_0.Vol = new decimal?(Math.Round(showMktSymb_0.Vol.Value) / 1.0000000000000000000000m);
			}
			if (showMktSymb_0.Open != null)
			{
				showMktSymb_0.Open = new decimal?(Math.Round(showMktSymb_0.Open.Value, digitNb));
			}
			if (showMktSymb_0.High != null)
			{
				showMktSymb_0.High = new decimal?(Math.Round(showMktSymb_0.High.Value, digitNb));
			}
			if (showMktSymb_0.Low != null)
			{
				showMktSymb_0.Low = new decimal?(Math.Round(showMktSymb_0.Low.Value, digitNb));
			}
			if (showMktSymb_0.LastDayClose != null)
			{
				showMktSymb_0.LastDayClose = new decimal?(Math.Round(showMktSymb_0.LastDayClose.Value, digitNb));
			}
			return showMktSymb_0;
		}

		// Token: 0x060019C8 RID: 6600 RVA: 0x000AE444 File Offset: 0x000AC644
		private object method_59(DataGridViewRow dataGridViewRow_0)
		{
			object result;
			if (dataGridViewRow_0 != null)
			{
				result = dataGridViewRow_0.DataBoundItem;
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060019C9 RID: 6601 RVA: 0x000AE464 File Offset: 0x000AC664
		private DataGridViewRow method_60()
		{
			DataGridView dataGridView_ = this.method_62();
			return this.method_61(dataGridView_);
		}

		// Token: 0x060019CA RID: 6602 RVA: 0x000AE484 File Offset: 0x000AC684
		private DataGridViewRow method_61(DataGridView dataGridView_0)
		{
			DataGridViewRow result;
			if (dataGridView_0 == null)
			{
				result = null;
			}
			else if (dataGridView_0.Rows.GetRowCount(DataGridViewElementStates.Selected) > 0)
			{
				result = dataGridView_0.SelectedRows[0];
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060019CB RID: 6603 RVA: 0x000AE4BC File Offset: 0x000AC6BC
		private DataGridView method_62()
		{
			Control control = this.method_64(this.SelectedTab);
			if (control is DevComponents.DotNetBar.TabControl)
			{
				DevComponents.DotNetBar.TabControl tabControl = control as DevComponents.DotNetBar.TabControl;
				Control control2 = this.method_65(tabControl.SelectedPanel);
				if (control2 is DataGridView)
				{
					return control2 as DataGridView;
				}
			}
			return null;
		}

		// Token: 0x060019CC RID: 6604 RVA: 0x000AE508 File Offset: 0x000AC708
		public bool method_63()
		{
			if (Base.Data.CurrSymbDataSet != null && Base.Data.CurrSymbDataSet.CurrSymbol != null)
			{
				StkSymbol currSymbol = Base.Data.CurrSymbDataSet.CurrSymbol;
				for (int i = 0; i < this.list_0.Count; i++)
				{
					DataGridViewMkt dataGridViewMkt = this.list_0[i];
					ShowMktSymb showMktSymb = this.method_55(currSymbol.ID, dataGridViewMkt);
					if (showMktSymb != null)
					{
						int num = (dataGridViewMkt.DataSource as SortableBindingList<ShowMktSymb>).IndexOf(showMktSymb);
						if (num >= 0 && num < dataGridViewMkt.Rows.Count)
						{
							DataGridViewRow dataGridViewRow = dataGridViewMkt.Rows[num];
							bool result;
							try
							{
								DataGridViewCell dataGridViewCell = dataGridViewRow.Cells[1];
								if (dataGridViewCell.Visible)
								{
									dataGridViewMkt.CurrentCell = dataGridViewCell;
								}
								dataGridViewRow.Selected = true;
								this.tabControl_0.SelectedTabIndex = i;
								result = true;
								goto IL_D6;
							}
							catch (Exception exception_)
							{
								Class182.smethod_0(exception_);
							}
							goto IL_BF;
							IL_D6:
							return result;
						}
					}
					IL_BF:;
				}
			}
			return false;
		}

		// Token: 0x060019CD RID: 6605 RVA: 0x000AE608 File Offset: 0x000AC808
		private Control method_64(TabItem tabItem_0)
		{
			TabControlPanel tabControlPanel_ = tabItem_0.AttachedControl as TabControlPanel;
			return this.method_65(tabControlPanel_);
		}

		// Token: 0x060019CE RID: 6606 RVA: 0x000AE62C File Offset: 0x000AC82C
		private Control method_65(TabControlPanel tabControlPanel_0)
		{
			return tabControlPanel_0.Controls[0];
		}

		// Token: 0x060019CF RID: 6607 RVA: 0x000AE64C File Offset: 0x000AC84C
		private void method_66()
		{
			this.btn_CondOrder.ForeColor = Class179.color_1;
			this.btn_HisTransQuery.ForeColor = Class179.color_1;
			this.btn_OrderQuery.ForeColor = Class179.color_1;
			this.transTabTopBtn1.ForeColor = Class179.color_1;
			this.transTabTopBtn2.ForeColor = Class179.color_1;
			this.transTabTopBtn3.ForeColor = Class179.color_1;
			this.transTabTopBtn4.ForeColor = Class179.color_1;
			this.btn_TOpenShrt.ForeColor = Color.Green;
			this.btn_TOpenLong.ForeColor = Color.Red;
			Base.UI.smethod_74(this.expSplitter_Trade);
			if (Base.UI.Form.ChartTheme == ChartTheme.Classic)
			{
				this.tabControlPanel_Trading.BackColor = Class179.color_3;
				this.panel_Trading.BackColor = Class179.color_3;
				this.tabControlPanel_OpenPos.BackColor = Class179.color_3;
				this.panel_Trans.BackColor = Class179.color_3;
				this.splitContainer_TransTabsBelow.Panel1.BackColor = Class179.color_3;
				this.splitContainer_TransTabsBelow.Panel2.BackColor = Class179.color_3;
				this.splitContainer_Trans_Hist.Panel1.BackColor = Class179.color_3;
				this.splitContainer_Orders_Hist.Panel1.BackColor = Class179.color_3;
				this.label_数量.ForeColor = Class179.color_9;
				this.label_品种.ForeColor = Class179.color_9;
				this.label_价格.ForeColor = Class179.color_9;
				this.label_maxPosUnits.ForeColor = Class179.color_9;
				this.chkBox_FollowPrc.ForeColor = Class179.color_9;
				this.numericUpDown_TPrice.ForeColor = Class179.color_10;
				this.numericUpDown_TUnits.ForeColor = Class179.color_10;
				this.numericUpDown_TPrice.BackColor = Class179.color_3;
				this.numericUpDown_TUnits.BackColor = Class179.color_3;
				this.panel_PercRadioBtns.BackColor = Class179.color_3;
				this.radioBtn_100Pers.ForeColor = Class179.color_9;
				this.radioBtn_80Pers.ForeColor = Class179.color_9;
				this.radioBtn_50Pers.ForeColor = Class179.color_9;
				this.radioBtn_30Pers.ForeColor = Class179.color_9;
				this.radioBtn_15Pers.ForeColor = Class179.color_9;
				this.radioBtn_100Pers.BackColor = Class179.color_3;
				this.radioBtn_80Pers.BackColor = Class179.color_3;
				this.radioBtn_50Pers.BackColor = Class179.color_3;
				this.radioBtn_30Pers.BackColor = Class179.color_3;
				this.radioBtn_15Pers.BackColor = Class179.color_3;
				this.panel_OpenClose.BackColor = Class179.color_3;
				this.chkBox_AutoOpenClose.ForeColor = Class179.color_9;
				this.chkBox_AutoOpenClose.BackColor = Class179.color_3;
				this.tblLayoutPanel_TrdBtns.BackColor = Class179.color_3;
				this.tblLayoutPanel_TransHistQry.BackColor = Class179.color_3;
				this.tblLayoutPanel_OdrHistQry.BackColor = Class179.color_3;
				this.radioBtn_AggregateSOT.ForeColor = Class179.color_9;
				this.radioBtn_IndividualSOT.ForeColor = Class179.color_9;
				this.radioBtn_AggregateSOT.BackColor = Class179.color_3;
				this.radioBtn_IndividualSOT.BackColor = Class179.color_3;
			}
			else
			{
				this.tabControlPanel_Trading.BackColor = Class179.color_9;
				this.panel_Trading.BackColor = Class179.color_9;
				this.tabControlPanel_OpenPos.BackColor = Class179.color_9;
				this.panel_Trans.BackColor = Class179.color_9;
				this.splitContainer_TransTabsBelow.Panel1.BackColor = Class179.color_9;
				this.splitContainer_TransTabsBelow.Panel2.BackColor = Class179.color_9;
				this.splitContainer_Trans_Hist.Panel1.BackColor = Class179.color_9;
				this.splitContainer_Orders_Hist.Panel1.BackColor = Class179.color_9;
				this.label_数量.ForeColor = Class179.color_1;
				this.label_品种.ForeColor = Class179.color_1;
				this.label_价格.ForeColor = Class179.color_1;
				this.label_maxPosUnits.ForeColor = Class179.color_1;
				this.chkBox_FollowPrc.ForeColor = Class179.color_1;
				this.numericUpDown_TPrice.ForeColor = Class179.color_1;
				this.numericUpDown_TUnits.ForeColor = Class179.color_1;
				this.numericUpDown_TPrice.BackColor = Color.White;
				this.numericUpDown_TUnits.BackColor = Color.White;
				this.panel_PercRadioBtns.BackColor = Class179.color_9;
				this.radioBtn_100Pers.ForeColor = Class179.color_1;
				this.radioBtn_80Pers.ForeColor = Class179.color_1;
				this.radioBtn_50Pers.ForeColor = Class179.color_1;
				this.radioBtn_30Pers.ForeColor = Class179.color_1;
				this.radioBtn_15Pers.ForeColor = Class179.color_1;
				this.radioBtn_100Pers.BackColor = Class179.color_9;
				this.radioBtn_80Pers.BackColor = Class179.color_9;
				this.radioBtn_50Pers.BackColor = Class179.color_9;
				this.radioBtn_30Pers.BackColor = Class179.color_9;
				this.radioBtn_15Pers.BackColor = Class179.color_9;
				this.panel_OpenClose.BackColor = Class179.color_9;
				this.chkBox_AutoOpenClose.ForeColor = Class179.color_1;
				this.chkBox_AutoOpenClose.BackColor = Class179.color_9;
				this.tblLayoutPanel_TrdBtns.BackColor = Class179.color_9;
				this.tblLayoutPanel_TransHistQry.BackColor = Class179.color_9;
				this.tblLayoutPanel_OdrHistQry.BackColor = Class179.color_9;
				this.radioBtn_AggregateSOT.ForeColor = Class179.color_1;
				this.radioBtn_IndividualSOT.ForeColor = Class179.color_1;
				this.radioBtn_AggregateSOT.BackColor = Class179.color_9;
				this.radioBtn_IndividualSOT.BackColor = Class179.color_9;
			}
		}

		// Token: 0x060019D0 RID: 6608 RVA: 0x000AEBF4 File Offset: 0x000ACDF4
		private void method_67()
		{
			this.method_66();
			this.numericUpDown_TUnits.Maximum = 9999999m;
			this.numericUpDown_TUnits.Minimum = 1m;
			this.numericUpDown_TUnits.Increment = 1m;
			this.numericUpDown_TPrice.Maximum = 9999999m;
			this.numericUpDown_TPrice.Minimum = 0m;
			this.method_73();
			this.numericUpDown_TPrice.ValueChanged += this.numericUpDown_TPrice_ValueChanged;
			this.numericUpDown_TUnits.ValueChanged += this.numericUpDown_TUnits_ValueChanged;
			this.numericUpDown_TUnits.KeyPress += this.numericUpDown_TUnits_KeyPress;
			this.chkBox_FollowPrc.Click += this.chkBox_FollowPrc_Click;
			this.chkBox_FollowPrc.CheckedChanged += this.chkBox_FollowPrc_CheckedChanged;
			this.radioBtn_15Pers.Click += this.radioBtn_15Pers_Click;
			this.radioBtn_30Pers.Click += this.radioBtn_30Pers_Click;
			this.radioBtn_50Pers.Click += this.radioBtn_50Pers_Click;
			this.radioBtn_80Pers.Click += this.radioBtn_80Pers_Click;
			this.radioBtn_100Pers.Click += this.radioBtn_100Pers_Click;
			this.btn_TOpenLong.Click += this.btn_TOpenLong_Click;
			this.btn_TOpenShrt.Click += this.btn_TOpenShrt_Click;
			this.btn_CondOrder.Click += this.btn_CondOrder_Click;
			this.radioBtn_Open.Checked = true;
			this.radioBtn_Open.CheckedChanged += this.radioBtn_Open_CheckedChanged;
			this.radioBtn_Close.CheckedChanged += this.radioBtn_Close_CheckedChanged;
			this.method_111();
			this.chkBox_AutoOpenClose.CheckedChanged += this.chkBox_AutoOpenClose_CheckedChanged;
			this.radioBtn_15Pers.Tag = 0.15m;
			this.radioBtn_30Pers.Tag = 0.3m;
			this.radioBtn_50Pers.Tag = 0.5m;
			this.radioBtn_80Pers.Tag = 0.8m;
			this.radioBtn_100Pers.Tag = 1m;
			Base.UI.Form.FollowPrcInTradingInputChanged += this.method_80;
			Base.UI.smethod_69(this.tabCtrl_Trans);
			if (TApp.DpiScaleMulti > 1f)
			{
				this.splitContainer_TransTabsBelow.SplitterDistance = Convert.ToInt32(Math.Round((double)((float)this.splitContainer_TransTabsBelow.SplitterDistance * TApp.DpiScaleMulti) * 1.1));
				this.splitContainer_Orders_Hist.SplitterDistance = Convert.ToInt32(Math.Round((double)((float)this.splitContainer_Orders_Hist.SplitterDistance * TApp.DpiScaleMulti)));
				this.splitContainer_Trans_Hist.SplitterDistance = Convert.ToInt32(Math.Round((double)((float)this.splitContainer_Trans_Hist.SplitterDistance * TApp.DpiScaleMulti)));
			}
			this.tabCtrl_Trans.SelectedTabChanged += this.tabCtrl_Trans_SelectedTabChanged;
			this.transTabTopBtn1.Click += this.transTabTopBtn1_Click;
			this.transTabTopBtn2.Click += this.transTabTopBtn2_Click;
			this.transTabTopBtn3.Click += this.transTabTopBtn3_Click;
			this.transTabTopBtn4.Click += this.transTabTopBtn4_Click;
			if (!TApp.IsFtIncluded)
			{
				this.transTabTopBtn3.Visible = false;
			}
			else
			{
				this.transTabTopBtn3.Visible = true;
			}
			this.tabControlPanel_Trading.ThemeAware = false;
			this.dataGridViewOpenTrans_0 = new DataGridViewOpenTrans();
			this.tabControlPanel_OpenPos.Controls.Add(this.dataGridViewOpenTrans_0);
			this.dataGridViewOpenTrans_0.method_5();
			if (Base.UI.Form.IfShowIndividualShownOpenTrans)
			{
				this.radioBtn_IndividualSOT.Checked = true;
			}
			else
			{
				this.radioBtn_AggregateSOT.Checked = true;
			}
			this.radioBtn_IndividualSOT.CheckedChanged += this.radioBtn_IndividualSOT_CheckedChanged;
			this.radioBtn_AggregateSOT.CheckedChanged += this.radioBtn_AggregateSOT_CheckedChanged;
			this.dataGridViewHisTrans_0 = new DataGridViewHisTrans();
			this.dataGridViewHisTrans_0.ChangeToHisTransDTRequested += this.method_84;
			this.tabControlPanel_Trans_Curr.Controls.Add(this.dataGridViewHisTrans_0);
			this.dataGridViewHisTrans_0.method_6();
			this.transTabPg_Hist.Click += this.transTabPg_Hist_Click;
			this.dateTimePicker_StartHisTrans.Enter += this.dateTimePicker_EndOrders_Enter;
			this.dateTimePicker_EndHisTrans.Enter += this.dateTimePicker_EndOrders_Enter;
			this.dateTimePicker_StartHisTrans.Leave += this.dateTimePicker_StartOrders_Leave;
			this.dateTimePicker_EndHisTrans.Leave += this.dateTimePicker_StartOrders_Leave;
			this.class291_0 = new Class291();
			this.tabControlPanel_Orders_Curr.Controls.Add(this.class291_0);
			this.class291_0.method_5();
			this.btn_OrderQuery.Click += this.btn_OrderQuery_Click;
			this.orderTabPg_Hist.Click += this.orderTabPg_Hist_Click;
			this.dateTimePicker_StartOrders.Enter += this.dateTimePicker_EndOrders_Enter;
			this.dateTimePicker_EndOrders.Enter += this.dateTimePicker_EndOrders_Enter;
			this.dateTimePicker_EndOrders.Leave += this.dateTimePicker_StartOrders_Leave;
			this.dateTimePicker_StartOrders.Leave += this.dateTimePicker_StartOrders_Leave;
			this.class292_0 = new Class292();
			this.class292_0.CreateCondOrderRequested += this.method_83;
			this.tabControlPanel_CondOrder.Controls.Add(this.class292_0);
			this.class292_0.method_6();
			if (this.ParentSplitPanel != null)
			{
				this.dataGridViewOpenTrans_0.ContextMenuStrip.Items.Add(new ToolStripSeparator());
				this.method_87(this.dataGridViewOpenTrans_0.ContextMenuStrip);
				this.dataGridViewHisTrans_0.ContextMenuStrip.Items.Add(new ToolStripSeparator());
				this.method_87(this.dataGridViewHisTrans_0.ContextMenuStrip);
				if (this.dataGridViewHisTrans_1 != null && this.dataGridViewHisTrans_1.ContextMenuStrip != null)
				{
					this.dataGridViewHisTrans_1.ContextMenuStrip.Items.Add(new ToolStripSeparator());
					this.method_87(this.dataGridViewHisTrans_1.ContextMenuStrip);
				}
				this.class291_0.ContextMenuStrip.Items.Add(new ToolStripSeparator());
				this.method_87(this.class291_0.ContextMenuStrip);
			}
		}

		// Token: 0x060019D1 RID: 6609 RVA: 0x0000AB59 File Offset: 0x00008D59
		public void method_68()
		{
			this.method_69();
			this.method_70();
			this.method_72();
		}

		// Token: 0x060019D2 RID: 6610 RVA: 0x000AF2BC File Offset: 0x000AD4BC
		public void method_69()
		{
			this.comboBox_TradingSymbs.Items.Clear();
			if (Base.UI.Form.IsInBlindTestMode && !Base.UI.Form.IsSingleBlindTest)
			{
				this.comboBox_TradingSymbs.Text = "●●";
			}
			else if (Base.Data.SymbDataSets != null)
			{
				foreach (SymbDataSet symbDataSet in Base.Data.SymbDataSets)
				{
					if (!symbDataSet.IsCurrDateNotListedYet)
					{
						TEx.Util.ComboBoxItem comboBoxItem = new TEx.Util.ComboBoxItem();
						comboBoxItem.Text = symbDataSet.CurrSymbol.CNName + "(" + symbDataSet.CurrSymbol.Code.Trim() + ")";
						comboBoxItem.Value = symbDataSet.CurrSymbol;
						this.comboBox_TradingSymbs.Items.Add(comboBoxItem);
						if (symbDataSet.CurrSymbol.ID == Base.UI.CurrTradingSymbol.ID)
						{
							this.comboBox_TradingSymbs.SelectedIndexChanged -= this.comboBox_TradingSymbs_SelectedIndexChanged;
							this.comboBox_TradingSymbs.SelectedIndex = this.comboBox_TradingSymbs.Items.Count - 1;
							this.comboBox_TradingSymbs.SelectedValue = Base.UI.CurrTradingSymbol;
							this.comboBox_TradingSymbs.SelectedIndexChanged += this.comboBox_TradingSymbs_SelectedIndexChanged;
						}
					}
				}
			}
		}

		// Token: 0x060019D3 RID: 6611 RVA: 0x0000AB6F File Offset: 0x00008D6F
		private void method_70()
		{
			this.method_71();
			this.numericUpDown_TUnits.Value = Base.UI.smethod_180();
			this.method_77();
		}

		// Token: 0x060019D4 RID: 6612 RVA: 0x000AF428 File Offset: 0x000AD628
		private void method_71()
		{
			this.label_maxPosUnits.Text = "≤ " + Base.Trading.smethod_207(Base.UI.CurrTradingSymbol).ToString();
		}

		// Token: 0x060019D5 RID: 6613 RVA: 0x000AF460 File Offset: 0x000AD660
		private void method_72()
		{
			if (Base.UI.CurrTradingSymbol != null)
			{
				this.numericUpDown_TPrice.DecimalPlaces = Base.UI.CurrTradingSymbol.DigitNb;
				this.numericUpDown_TPrice.Increment = Base.UI.CurrTradingSymbol.LeastPriceVar.Value;
				try
				{
					SymbDataSet symbDataSet = Base.Data.smethod_49(Base.UI.CurrTradingSymbol.ID, false);
					if (symbDataSet != null && symbDataSet.CurrHisData != null)
					{
						decimal value = Convert.ToDecimal(symbDataSet.CurrHisData.Close);
						this.numericUpDown_TPrice.Value = value;
					}
				}
				catch
				{
				}
			}
		}

		// Token: 0x060019D6 RID: 6614 RVA: 0x0000AB94 File Offset: 0x00008D94
		private void method_73()
		{
			this.method_74();
			this.method_75();
		}

		// Token: 0x060019D7 RID: 6615 RVA: 0x000AF4F8 File Offset: 0x000AD6F8
		private void method_74()
		{
			if (this.label_usableBal.Visible)
			{
				this.label_usableBal.Text = string.Format("{0:0,0}", Base.Acct.smethod_19() / 1.000000000000000000000m);
			}
		}

		// Token: 0x060019D8 RID: 6616 RVA: 0x000AF54C File Offset: 0x000AD74C
		private void method_75()
		{
			if (this.label_PosRatio.Visible)
			{
				decimal? num = Base.Acct.smethod_23();
				if (num != null)
				{
					decimal num2 = Math.Round(num.Value, 0) / 1.00000000000000000000000m;
					this.label_PosRatio.Text = num2.ToString() + "%";
				}
				else
				{
					this.label_PosRatio.Text = "N/A";
				}
			}
		}

		// Token: 0x060019D9 RID: 6617 RVA: 0x0000ABA4 File Offset: 0x00008DA4
		private void btn_TOpenLong_Click(object sender, EventArgs e)
		{
			this.method_76(true);
		}

		// Token: 0x060019DA RID: 6618 RVA: 0x0000ABAF File Offset: 0x00008DAF
		private void btn_TOpenShrt_Click(object sender, EventArgs e)
		{
			this.method_76(false);
		}

		// Token: 0x060019DB RID: 6619 RVA: 0x000AF5D0 File Offset: 0x000AD7D0
		private void method_76(bool bool_2)
		{
			bool? nullable_ = new bool?(this.radioBtn_Open.Checked);
			if (this.chkBox_AutoOpenClose.Checked)
			{
				nullable_ = null;
			}
			if (!Base.UI.Form.EnableShortForStock && Base.UI.CurrTradingSymbol.IsStock)
			{
				if (bool_2)
				{
					nullable_ = new bool?(true);
				}
				else
				{
					nullable_ = new bool?(false);
				}
			}
			decimal value = this.numericUpDown_TPrice.Value;
			decimal value2 = this.numericUpDown_TUnits.Value;
			Base.Trading.smethod_222(Base.UI.CurrTradingSymbol.ID, bool_2, nullable_, value, value2);
		}

		// Token: 0x060019DC RID: 6620 RVA: 0x000AF660 File Offset: 0x000AD860
		private void chkBox_FollowPrc_Click(object sender, EventArgs e)
		{
			if (this.chkBox_FollowPrc.Checked && Base.UI.CurrTradingSymbol != null)
			{
				SymbDataSet symbDataSet = Base.Data.smethod_49(Base.UI.CurrTradingSymbol.ID, false);
				if (symbDataSet != null && symbDataSet.HasValidDataSet)
				{
					double close = symbDataSet.CurrHisDataSet.CurrHisData.Close;
					try
					{
						this.numericUpDown_TPrice.Value = Convert.ToDecimal(close);
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
			}
		}

		// Token: 0x060019DD RID: 6621 RVA: 0x0000ABBA File Offset: 0x00008DBA
		private void chkBox_FollowPrc_CheckedChanged(object sender, EventArgs e)
		{
			if (Base.UI.Form.IfFollowPrcInTradingTab != this.chkBox_FollowPrc.Checked)
			{
				Base.UI.Form.IfFollowPrcInTradingTab = this.chkBox_FollowPrc.Checked;
			}
		}

		// Token: 0x060019DE RID: 6622 RVA: 0x0000ABEA File Offset: 0x00008DEA
		private void radioBtn_15Pers_Click(object sender, EventArgs e)
		{
			this.method_78(0.15m);
		}

		// Token: 0x060019DF RID: 6623 RVA: 0x0000ABFF File Offset: 0x00008DFF
		private void radioBtn_30Pers_Click(object sender, EventArgs e)
		{
			this.method_78(0.3m);
		}

		// Token: 0x060019E0 RID: 6624 RVA: 0x0000AC13 File Offset: 0x00008E13
		private void radioBtn_50Pers_Click(object sender, EventArgs e)
		{
			this.method_78(0.5m);
		}

		// Token: 0x060019E1 RID: 6625 RVA: 0x0000AC27 File Offset: 0x00008E27
		private void radioBtn_80Pers_Click(object sender, EventArgs e)
		{
			this.method_78(0.8m);
		}

		// Token: 0x060019E2 RID: 6626 RVA: 0x0000AC3B File Offset: 0x00008E3B
		private void radioBtn_100Pers_Click(object sender, EventArgs e)
		{
			this.method_78(1m);
		}

		// Token: 0x060019E3 RID: 6627 RVA: 0x0000AC4A File Offset: 0x00008E4A
		private void radioBtn_Open_CheckedChanged(object sender, EventArgs e)
		{
			this.method_77();
		}

		// Token: 0x060019E4 RID: 6628 RVA: 0x0000AC4A File Offset: 0x00008E4A
		private void radioBtn_Close_CheckedChanged(object sender, EventArgs e)
		{
			this.method_77();
		}

		// Token: 0x060019E5 RID: 6629 RVA: 0x000AF6DC File Offset: 0x000AD8DC
		private void method_77()
		{
			decimal? num = this.method_79();
			if (num != null)
			{
				this.method_78(num.Value);
			}
		}

		// Token: 0x060019E6 RID: 6630 RVA: 0x000AF708 File Offset: 0x000AD908
		private void method_78(decimal decimal_0)
		{
			long value = Base.Trading.smethod_207(Base.UI.CurrTradingSymbol);
			if (this.radioBtn_Close.Enabled && this.radioBtn_Close.Visible && this.radioBtn_Close.Checked)
			{
				long? num = Base.Trading.CurrOpenTransList.Where(new Func<ShownOpenTrans, bool>(TransTabs.<>c.<>9.method_14)).Sum(new Func<ShownOpenTrans, long?>(TransTabs.<>c.<>9.method_15));
				if (num != null)
				{
					long? num2 = num;
					if (num2.GetValueOrDefault() > 0L & num2 != null)
					{
						value = num.Value;
						goto IL_152;
					}
				}
				long? num3 = Base.Trading.CurrOpenTransList.Where(new Func<ShownOpenTrans, bool>(TransTabs.<>c.<>9.method_16)).Sum(new Func<ShownOpenTrans, long?>(TransTabs.<>c.<>9.method_17));
				if (num3 != null)
				{
					long? num2 = num3;
					if (num2.GetValueOrDefault() > 0L & num2 != null)
					{
						value = num3.Value;
					}
				}
			}
			IL_152:
			decimal num4 = Math.Floor(value * decimal_0);
			if (num4 >= 1m)
			{
				try
				{
					this.numericUpDown_TUnits.Value = num4;
					return;
				}
				catch
				{
					return;
				}
			}
			this.numericUpDown_TUnits.Value = 1m;
		}

		// Token: 0x060019E7 RID: 6631 RVA: 0x000AF8BC File Offset: 0x000ADABC
		private decimal? method_79()
		{
			IEnumerable<RadioButton> source = this.panel_PercRadioBtns.Controls.OfType<RadioButton>().Where(new Func<RadioButton, bool>(TransTabs.<>c.<>9.method_18));
			decimal? result;
			if (source.Any<RadioButton>())
			{
				result = new decimal?(Convert.ToDecimal(source.First<RadioButton>().Tag));
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060019E8 RID: 6632 RVA: 0x000AF92C File Offset: 0x000ADB2C
		private void chkBox_AutoOpenClose_CheckedChanged(object sender, EventArgs e)
		{
			if (this.chkBox_AutoOpenClose.Checked)
			{
				this.radioBtn_Open.Enabled = false;
				this.radioBtn_Close.Enabled = false;
			}
			else
			{
				this.radioBtn_Open.Enabled = true;
				this.radioBtn_Close.Enabled = true;
			}
		}

		// Token: 0x060019E9 RID: 6633 RVA: 0x0000AC54 File Offset: 0x00008E54
		private void method_80(object sender, EventArgs e)
		{
			if (this.chkBox_FollowPrc.Checked != Base.UI.Form.IfFollowPrcInTradingTab)
			{
				this.chkBox_FollowPrc.Checked = Base.UI.Form.IfFollowPrcInTradingTab;
			}
		}

		// Token: 0x060019EA RID: 6634 RVA: 0x0000AC84 File Offset: 0x00008E84
		private void numericUpDown_TPrice_ValueChanged(object sender, EventArgs e)
		{
			Base.UI.Form.TradingPrice = this.numericUpDown_TPrice.Value;
			this.method_3();
		}

		// Token: 0x060019EB RID: 6635 RVA: 0x0000ACA3 File Offset: 0x00008EA3
		private void numericUpDown_TUnits_ValueChanged(object sender, EventArgs e)
		{
			if (this.numericUpDown_TUnits.Focused)
			{
				this.method_81();
			}
			Base.UI.Form.TradingUnits = Convert.ToInt32(this.numericUpDown_TUnits.Value);
			this.method_2();
		}

		// Token: 0x060019EC RID: 6636 RVA: 0x0000ACDA File Offset: 0x00008EDA
		private void numericUpDown_TUnits_KeyPress(object sender, KeyPressEventArgs e)
		{
			if (char.IsDigit(e.KeyChar))
			{
				this.method_81();
			}
		}

		// Token: 0x060019ED RID: 6637 RVA: 0x000AF97C File Offset: 0x000ADB7C
		private void method_81()
		{
			IEnumerable<RadioButton> source = this.panel_PercRadioBtns.Controls.OfType<RadioButton>().Where(new Func<RadioButton, bool>(TransTabs.<>c.<>9.method_19));
			if (source.Any<RadioButton>())
			{
				source.First<RadioButton>().Checked = false;
			}
		}

		// Token: 0x060019EE RID: 6638 RVA: 0x000AF9D4 File Offset: 0x000ADBD4
		private void btn_CondOrder_Click(object sender, EventArgs e)
		{
			TransTabs.Class321 @class = new TransTabs.Class321();
			if (Base.Acct.CurrAccount.IsReadOnly)
			{
				Base.Acct.smethod_50();
			}
			else
			{
				int num2;
				int num = this.radioBtn_Open.Enabled ? (num2 = (this.radioBtn_Open.Checked ? 1 : 0)) : (num2 = 1);
				bool flag = true;
				List<ShownOpenTrans> list = null;
				int num3;
				if (Base.Trading.CurrOpenTransList != null)
				{
					list = Base.Trading.CurrOpenTransList.Where(new Func<ShownOpenTrans, bool>(TransTabs.<>c.<>9.method_20)).ToList<ShownOpenTrans>();
					if ((num3 = num2) != 0)
					{
						goto IL_B2;
					}
				}
				else if ((num3 = num) != 0)
				{
					goto IL_B2;
				}
				if (list != null && list.Any<ShownOpenTrans>())
				{
					if (list.Where(new Func<ShownOpenTrans, bool>(TransTabs.<>c.<>9.method_21)).Any<ShownOpenTrans>())
					{
						flag = false;
					}
				}
				IL_B2:
				OrderType orderType_ = Base.Trading.smethod_76(num3 != 0, flag);
				Base.UI.smethod_125();
				@class.int_0 = Base.UI.CurrTradingSymbol.ID;
				SymbDataSet symbDataSet = Base.Data.smethod_49(@class.int_0, false);
				if (symbDataSet != null && symbDataSet.HasValidDataSet)
				{
					List<Transaction> list2 = Base.Trading.Transactions.Where(new Func<Transaction, bool>(@class.method_0)).ToList<Transaction>();
					new SetCondOrdForm(null, Base.UI.CurrTradingSymbol, orderType_, null, Convert.ToDecimal(symbDataSet.CurrHisDataSet.CurrHisData.Close), 0m, this.numericUpDown_TUnits.Value, null, list2)
					{
						ShowInTaskbar = false
					}.ShowDialog();
				}
				else
				{
					Class182.smethod_0(new Exception("No valid sds for CurrTradingSymbol to create cond order!"));
				}
			}
		}

		// Token: 0x060019EF RID: 6639 RVA: 0x000AFB5C File Offset: 0x000ADD5C
		private void tabCtrl_Trans_SelectedTabChanged(object sender, TabStripTabChangedEventArgs e)
		{
			string a = e.NewTab.Text.Trim();
			if (a == "当前持仓")
			{
				this.transTabTopBtn2.Visible = true;
				if (!TApp.IsFtIncluded)
				{
					this.transTabTopBtn3.Visible = false;
				}
				else
				{
					this.transTabTopBtn3.Visible = true;
				}
				this.transTabTopBtn4.Visible = true;
				this.transTabTopBtn1.Text = "市价平仓";
				this.transTabTopBtn2.Text = "全部平仓";
				this.transTabTopBtn3.Text = "市价反手";
				this.radioBtn_IndividualSOT.Visible = true;
				this.radioBtn_AggregateSOT.Visible = true;
			}
			else if (a == "成交记录")
			{
				this.transTabTopBtn2.Visible = false;
				this.transTabTopBtn3.Visible = false;
				this.transTabTopBtn4.Visible = false;
				this.transTabTopBtn1.Text = "导出记录";
				this.radioBtn_IndividualSOT.Visible = false;
				this.radioBtn_AggregateSOT.Visible = false;
			}
			else if (a == "委托记录")
			{
				this.transTabTopBtn2.Visible = true;
				this.transTabTopBtn3.Visible = false;
				this.transTabTopBtn4.Visible = false;
				this.transTabTopBtn1.Text = "撤销";
				this.transTabTopBtn2.Text = "全部撤销";
				this.radioBtn_IndividualSOT.Visible = false;
				this.radioBtn_AggregateSOT.Visible = false;
			}
			else if (a == "条件单")
			{
				this.transTabTopBtn2.Visible = true;
				this.transTabTopBtn3.Visible = true;
				this.transTabTopBtn4.Visible = false;
				this.transTabTopBtn1.Text = "取消";
				this.transTabTopBtn2.Text = "全部取消";
				this.transTabTopBtn3.Text = "新建...";
				this.radioBtn_IndividualSOT.Visible = false;
				this.radioBtn_AggregateSOT.Visible = false;
			}
		}

		// Token: 0x060019F0 RID: 6640 RVA: 0x000AFD54 File Offset: 0x000ADF54
		private void transTabTopBtn1_Click(object sender, EventArgs e)
		{
			Button button = sender as Button;
			if (button.Text == "市价平仓")
			{
				if (this.dataGridViewOpenTrans_0.SelectedRows.Count == 1)
				{
					this.dataGridViewOpenTrans_0.method_14();
				}
				else if (this.dataGridViewOpenTrans_0.RowCount > 0)
				{
					MessageBox.Show("请先选中持仓记录！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
			}
			else if (button.Text == "导出记录")
			{
				if (this.tabCtrl_HisTrans.SelectedTab.Text.Trim() == "当前记录")
				{
					if (this.dataGridViewHisTrans_0.DataSource != null)
					{
						SortableBindingList<ShownHisTrans> sortableBindingList = this.dataGridViewHisTrans_0.DataSource as SortableBindingList<ShownHisTrans>;
						if (sortableBindingList.Count > 0)
						{
							this.dataGridViewHisTrans_0.method_10(sortableBindingList);
						}
					}
				}
				else if (this.dataGridViewHisTrans_1 != null && this.dataGridViewHisTrans_1.DataSource != null)
				{
					SortableBindingList<ShownHisTrans> sortableBindingList2 = this.dataGridViewHisTrans_1.DataSource as SortableBindingList<ShownHisTrans>;
					if (sortableBindingList2.Count > 0)
					{
						this.dataGridViewHisTrans_0.method_10(sortableBindingList2);
					}
				}
			}
			else if (button.Text == "撤销")
			{
				if (this.class291_0.SelectedRows.Count == 1)
				{
					Base.Trading.smethod_73((this.class291_0.SelectedRows[0].DataBoundItem as ShownOrder).ID);
					this.class291_0.method_5();
				}
				else if (this.class291_0.RowCount > 0)
				{
					MessageBox.Show("请先选中委托记录！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
			}
			else if (button.Text == "取消")
			{
				if (this.class292_0.SelectedRows.Count == 1)
				{
					Base.Trading.smethod_94((this.class292_0.SelectedRows[0].DataBoundItem as ShownCondOrder).ID, OrderStatus.Canceled);
					this.class292_0.Refresh();
				}
				else if (this.class292_0.RowCount > 0)
				{
					MessageBox.Show("请先选中条件单记录！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
			}
		}

		// Token: 0x060019F1 RID: 6641 RVA: 0x000AFF80 File Offset: 0x000AE180
		private void transTabTopBtn2_Click(object sender, EventArgs e)
		{
			Button button = sender as Button;
			if (button.Text == "全部平仓")
			{
				if (this.dataGridViewOpenTrans_0.RowCount > 0)
				{
					if (Base.Acct.CurrAccount.IsReadOnly)
					{
						Base.Acct.smethod_50();
					}
					else if (Base.Trading.smethod_217() && Base.Trading.smethod_55())
					{
						this.dataGridViewOpenTrans_0.method_5();
					}
				}
			}
			else if (button.Text == "全部撤销")
			{
				if (this.class291_0.RowCount > 0)
				{
					Base.Trading.smethod_74();
					this.class291_0.method_5();
				}
			}
			else if (button.Text == "全部取消" && this.class292_0.RowCount > 0)
			{
				Base.Trading.smethod_111();
				this.class292_0.Refresh();
			}
		}

		// Token: 0x060019F2 RID: 6642 RVA: 0x000B004C File Offset: 0x000AE24C
		private void transTabTopBtn3_Click(object sender, EventArgs e)
		{
			Button button = sender as Button;
			if (button.Text == "市价反手")
			{
				if (this.dataGridViewOpenTrans_0.SelectedRows.Count == 1)
				{
					ShownOpenTrans shownOpenTrans = this.dataGridViewOpenTrans_0.SelectedRows[0].DataBoundItem as ShownOpenTrans;
					if (Base.Trading.smethod_57(shownOpenTrans))
					{
						OrderType orderType_ = OrderType.Order_OpenLong;
						if (shownOpenTrans.TransType == 1)
						{
							orderType_ = OrderType.Order_OpenShort;
						}
						Base.Trading.smethod_197(shownOpenTrans.SymbolID, orderType_, shownOpenTrans.Units, 0m);
						this.dataGridViewOpenTrans_0.method_5();
					}
				}
				else if (this.dataGridViewOpenTrans_0.RowCount > 0)
				{
					MessageBox.Show("请先选中持仓记录！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
			}
			else if (button.Text == "新建...")
			{
				this.btn_CondOrder_Click(this, null);
			}
		}

		// Token: 0x060019F3 RID: 6643 RVA: 0x000B0120 File Offset: 0x000AE320
		private void transTabTopBtn4_Click(object sender, EventArgs e)
		{
			if (this.dataGridViewOpenTrans_0.SelectedRows.Count == 1)
			{
				ShownOpenTrans shownOpenTrans = this.dataGridViewOpenTrans_0.SelectedRows[0].DataBoundItem as ShownOpenTrans;
				SymbDataSet symbDataSet = Base.Data.smethod_49(shownOpenTrans.SymbolID, false);
				if (symbDataSet != null && symbDataSet.HasValidDataSet && symbDataSet.CurrHisData.Close > 0.0)
				{
					this.dataGridViewOpenTrans_0.method_12(shownOpenTrans);
				}
				else
				{
					MessageBox.Show("当前页面无选中持仓品种的有效行情图表，无法设置止损止盈！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
			}
		}

		// Token: 0x060019F4 RID: 6644 RVA: 0x0000ACF1 File Offset: 0x00008EF1
		public void method_82()
		{
			this.dataGridViewOpenTrans_0.method_5();
			this.dataGridViewHisTrans_0.method_6();
			this.class291_0.method_5();
			this.class292_0.method_6();
		}

		// Token: 0x060019F5 RID: 6645 RVA: 0x000B01B0 File Offset: 0x000AE3B0
		private void radioBtn_AggregateSOT_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioBtn_AggregateSOT.Checked && Base.UI.Form.IfShowIndividualShownOpenTrans)
			{
				Base.UI.Form.IfShowIndividualShownOpenTrans = false;
				Base.Trading.CurrOpenTransList = Base.Trading.smethod_139(Base.Acct.CurrAccount.ID, Base.UI.Form.IfShowIndividualShownOpenTrans);
			}
		}

		// Token: 0x060019F6 RID: 6646 RVA: 0x000B0204 File Offset: 0x000AE404
		private void radioBtn_IndividualSOT_CheckedChanged(object sender, EventArgs e)
		{
			if (this.radioBtn_IndividualSOT.Checked && !Base.UI.Form.IfShowIndividualShownOpenTrans)
			{
				Base.UI.Form.IfShowIndividualShownOpenTrans = true;
				Base.Trading.CurrOpenTransList = Base.Trading.smethod_139(Base.Acct.CurrAccount.ID, Base.UI.Form.IfShowIndividualShownOpenTrans);
			}
		}

		// Token: 0x060019F7 RID: 6647 RVA: 0x0000AD21 File Offset: 0x00008F21
		private void method_83(object sender, EventArgs e)
		{
			this.btn_CondOrder_Click(this, new EventArgs());
		}

		// Token: 0x060019F8 RID: 6648 RVA: 0x0000AD31 File Offset: 0x00008F31
		private void dateTimePicker_EndOrders_Enter(object sender, EventArgs e)
		{
			this.method_0();
		}

		// Token: 0x060019F9 RID: 6649 RVA: 0x0000AD3B File Offset: 0x00008F3B
		private void dateTimePicker_StartOrders_Leave(object sender, EventArgs e)
		{
			this.method_1();
		}

		// Token: 0x060019FA RID: 6650 RVA: 0x0000AD45 File Offset: 0x00008F45
		private void method_84(EventArgs16 eventArgs16_0)
		{
			this.method_4(eventArgs16_0.ShownHisTrans);
		}

		// Token: 0x060019FB RID: 6651 RVA: 0x000041AE File Offset: 0x000023AE
		public void method_85()
		{
		}

		// Token: 0x060019FC RID: 6652 RVA: 0x000B0258 File Offset: 0x000AE458
		public void method_86()
		{
			if (this.ParentSplitPanel != null)
			{
				ContextMenuStrip contextMenuStrip_ = new ContextMenuStrip();
				this.method_87(contextMenuStrip_);
				this.trdAnalysisPanel_0.method_1(contextMenuStrip_);
			}
		}

		// Token: 0x060019FD RID: 6653 RVA: 0x000B0288 File Offset: 0x000AE488
		public void method_87(ContextMenuStrip contextMenuStrip_0)
		{
			ToolStripMenuItem toolStripMenuItem = Base.UI.smethod_76();
			toolStripMenuItem.Text = "切换画面";
			contextMenuStrip_0.Items.Add(toolStripMenuItem);
			ToolStripMenuItem toolStripMenuItem2 = Base.UI.smethod_76();
			toolStripMenuItem2.Text = "分时图";
			toolStripMenuItem2.Click += this.method_90;
			ToolStripMenuItem toolStripMenuItem3 = Base.UI.smethod_76();
			toolStripMenuItem3.Text = "K线图";
			toolStripMenuItem3.Click += this.method_92;
			ToolStripMenuItem toolStripMenuItem4 = Base.UI.smethod_76();
			toolStripMenuItem4.Text = "功能栏";
			toolStripMenuItem4.Checked = true;
			toolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[]
			{
				toolStripMenuItem2,
				toolStripMenuItem3,
				toolStripMenuItem4
			});
		}

		// Token: 0x060019FE RID: 6654 RVA: 0x000B0330 File Offset: 0x000AE530
		public ChtCtrl_Tick method_88()
		{
			SplitterPanel parentSplitPanel = this.ParentSplitPanel;
			if (this.ParentTransTabCtrl != null)
			{
				parentSplitPanel = this.ParentTransTabCtrl.ParentSplitPanel;
			}
			ChtCtrl_Tick result;
			if (parentSplitPanel != null)
			{
				this.method_89();
				result = Base.UI.smethod_150(this, parentSplitPanel);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x060019FF RID: 6655 RVA: 0x000B0370 File Offset: 0x000AE570
		private void method_89()
		{
			Base.UI.Form.LastFuncTabsIdx = new int?(this.SelectedTabIndex);
			Base.UI.Form.LastMktSymbTabsIdx = new int?(this.MktSelectedTabIndex);
			Base.UI.TransTabs = null;
			base.Enabled = false;
			base.Visible = false;
			if (this.ParentTransTabCtrl != null)
			{
				this.ParentTransTabCtrl.IsSwitchedBehind = true;
			}
		}

		// Token: 0x06001A00 RID: 6656 RVA: 0x0000AD55 File Offset: 0x00008F55
		private void method_90(object sender, EventArgs e)
		{
			this.method_88();
		}

		// Token: 0x06001A01 RID: 6657 RVA: 0x000B03D4 File Offset: 0x000AE5D4
		public ChtCtrl_KLine method_91()
		{
			ChtCtrl_KLine result;
			if (this.ParentTransTabCtrl != null && this.ParentTransTabCtrl.ParentSplitPanel != null)
			{
				this.method_89();
				result = Base.UI.smethod_152(this, this.ParentTransTabCtrl.ParentSplitPanel);
			}
			else
			{
				result = null;
			}
			return result;
		}

		// Token: 0x06001A02 RID: 6658 RVA: 0x0000AD60 File Offset: 0x00008F60
		private void method_92(object sender, EventArgs e)
		{
			this.method_91();
		}

		// Token: 0x06001A03 RID: 6659 RVA: 0x0000AD6B File Offset: 0x00008F6B
		private void method_93()
		{
			this.baoDianPanel.method_0();
		}

		// Token: 0x06001A04 RID: 6660 RVA: 0x000B0418 File Offset: 0x000AE618
		public List<string> method_94()
		{
			return this.baoDianPanel.method_26();
		}

		// Token: 0x06001A05 RID: 6661 RVA: 0x000B0434 File Offset: 0x000AE634
		public AdvTree method_95()
		{
			return this.baoDianPanel.method_27();
		}

		// Token: 0x06001A06 RID: 6662 RVA: 0x0000AD7A File Offset: 0x00008F7A
		private void method_96()
		{
			this.fnRptAnlysPanel.method_0();
		}

		// Token: 0x06001A07 RID: 6663 RVA: 0x0000AD89 File Offset: 0x00008F89
		private void method_97()
		{
			this.symbFilterPanel.method_1();
			this.symbFilterPanel.MsgNotifyNeeded += this.method_98;
		}

		// Token: 0x06001A08 RID: 6664 RVA: 0x0000ADAF File Offset: 0x00008FAF
		private void method_98(object sender, MsgEventArgs e)
		{
			this.method_5(e.Msg);
		}

		// Token: 0x06001A09 RID: 6665 RVA: 0x0000ADBF File Offset: 0x00008FBF
		private void method_99()
		{
			if (this.tabControlPanel_Video.Controls.Count == 0)
			{
				this.method_100();
			}
		}

		// Token: 0x06001A0A RID: 6666 RVA: 0x000B0450 File Offset: 0x000AE650
		private void method_100()
		{
			VideoPanel videoPanel = new VideoPanel();
			videoPanel.Dock = DockStyle.Fill;
			this.tabControlPanel_Video.Controls.Add(videoPanel);
		}

		// Token: 0x06001A0B RID: 6667 RVA: 0x0000ADDB File Offset: 0x00008FDB
		private void method_101(object sender, EventArgs e)
		{
			this.method_103();
			this.method_104();
			this.method_75();
		}

		// Token: 0x06001A0C RID: 6668 RVA: 0x0000ADF1 File Offset: 0x00008FF1
		private void method_102(object sender, EventArgs e)
		{
			this.method_74();
			this.method_26(false, null);
		}

		// Token: 0x06001A0D RID: 6669 RVA: 0x000B0480 File Offset: 0x000AE680
		private void method_103()
		{
			if (this.CurrShowMktSymbList != null)
			{
				for (int i = 0; i < this.CurrShowMktSymbList.Count; i++)
				{
					ShowMktSymb showMktSymb = this.CurrShowMktSymbList[i];
					SymbDataSet symbDataSet = Base.Data.smethod_49(showMktSymb.StkId, false);
					if (symbDataSet != null && symbDataSet.HasValidDataSet)
					{
						DateTime? lastDT = showMktSymb.LastDT;
						DateTime date = symbDataSet.CurrHisDataSet.CurrHisData.Date;
						if (lastDT == null || (lastDT != null && !(lastDT.GetValueOrDefault() == date)))
						{
							DateTime? lastDT2 = showMktSymb.LastDT;
							if (showMktSymb.StkId != symbDataSet.CurrSymbol.ID || (lastDT2 == null || symbDataSet.CurrHisDataSet.CurrExchgOBT == null || !(lastDT2.Value >= symbDataSet.CurrHisDataSet.CurrDayBeginDT)) || !(lastDT2.Value <= symbDataSet.CurrHisDataSet.CurrHisData.Date))
							{
								this.method_57(showMktSymb);
							}
							else
							{
								showMktSymb.method_17(symbDataSet.CurrHisDataSet.CurrHisData);
								showMktSymb = this.method_58(showMktSymb);
							}
							if (!Base.UI.Form.IsInBlindTestMode)
							{
								if (this.DataGridViewListWithCurrMktSymb != null)
								{
									using (List<KeyValuePair<DataGridView, int>>.Enumerator enumerator = this.DataGridViewListWithCurrMktSymb.GetEnumerator())
									{
										while (enumerator.MoveNext())
										{
											KeyValuePair<DataGridView, int> keyValuePair = enumerator.Current;
											if (keyValuePair.Key.Visible)
											{
												keyValuePair.Key.Refresh();
											}
										}
										goto IL_194;
									}
								}
								if (showMktSymb.IsInZiXuan && this.dataGridViewMkt_0.Visible)
								{
									this.dataGridViewMkt_0.Refresh();
								}
							}
						}
					}
					IL_194:;
				}
			}
		}

		// Token: 0x06001A0E RID: 6670 RVA: 0x000B0648 File Offset: 0x000AE848
		private void method_104()
		{
			if (this.IfFollowPrice)
			{
				SymbDataSet symbDataSet = Base.Data.smethod_49(Base.UI.CurrTradingSymbol.ID, false);
				try
				{
					if (symbDataSet != null && symbDataSet.HasValidDataSet)
					{
						this.numericUpDown_TPrice.Value = Convert.ToDecimal(symbDataSet.CurrHisDataSet.CurrHisData.Close);
					}
				}
				catch
				{
				}
			}
			this.method_77();
			this.label_maxPosUnits.Text = "≤ " + Base.Trading.smethod_207(Base.UI.CurrTradingSymbol).ToString();
		}

		// Token: 0x06001A0F RID: 6671 RVA: 0x0000AE03 File Offset: 0x00009003
		private void method_105(object sender, EventArgs e)
		{
			BaoDianMgr.smethod_8();
		}

		// Token: 0x06001A10 RID: 6672 RVA: 0x0000AE0C File Offset: 0x0000900C
		private void method_106(object sender, EventArgs e)
		{
			this.CurrShowMktSymbList = this.method_53();
			this.method_51();
			this.method_123();
			this.method_124();
			this.method_68();
			this.method_73();
		}

		// Token: 0x06001A11 RID: 6673 RVA: 0x000B06E0 File Offset: 0x000AE8E0
		private void method_107(EventArgs1 eventArgs1_0)
		{
			int newSymbID = eventArgs1_0.NewSymbID;
			this.CurrShowMktSymbList = this.method_53();
			this.method_52(newSymbID);
			this.method_68();
			this.method_111();
		}

		// Token: 0x06001A12 RID: 6674 RVA: 0x000B0718 File Offset: 0x000AE918
		private void method_108(object sender, EventArgs e)
		{
			SymbDataSet symbDataSet_ = sender as SymbDataSet;
			this.method_17(symbDataSet_);
		}

		// Token: 0x06001A13 RID: 6675 RVA: 0x000B0738 File Offset: 0x000AE938
		private void method_109(object sender, EventArgs e)
		{
			SymbDataSet symbDataSet_ = sender as SymbDataSet;
			this.method_18(symbDataSet_);
		}

		// Token: 0x06001A14 RID: 6676 RVA: 0x0000AE3A File Offset: 0x0000903A
		private void method_110(object sender, EventArgs e)
		{
			if (this.tabControl_Below.Name == "tabPg_Below_Symbs")
			{
				this.method_26(false, null);
			}
		}

		// Token: 0x06001A15 RID: 6677 RVA: 0x000B0758 File Offset: 0x000AE958
		private void method_111()
		{
			if (Base.UI.CurrTradingSymbol.IsStock && !Base.UI.Form.EnableShortForStock)
			{
				this.radioBtn_Open.Visible = false;
				this.radioBtn_Close.Visible = false;
				this.chkBox_AutoOpenClose.Visible = false;
			}
			else
			{
				this.radioBtn_Open.Visible = true;
				this.radioBtn_Close.Visible = true;
				this.chkBox_AutoOpenClose.Visible = true;
			}
		}

		// Token: 0x06001A16 RID: 6678 RVA: 0x000B07CC File Offset: 0x000AE9CC
		private void method_112(EventArgs17 eventArgs17_0)
		{
			if (!Base.UI.Form.IfDisableAutoShowCurrTransTab)
			{
				Transaction transaction = eventArgs17_0.Transaction;
				if ((transaction.TransType == 1 || transaction.TransType == 3) && this.SelectedTabIndex != 1)
				{
					this.SelectedTabIndex = 1;
					this.TransSelectedTabIndex = 0;
				}
				this.method_82();
				this.method_70();
				this.method_73();
			}
		}

		// Token: 0x06001A17 RID: 6679 RVA: 0x0000AE5D File Offset: 0x0000905D
		private void method_113(object sender, EventArgs e)
		{
			this.method_82();
			this.method_70();
			this.method_73();
		}

		// Token: 0x06001A18 RID: 6680 RVA: 0x0000AE5D File Offset: 0x0000905D
		private void method_114(EventArgs14 eventArgs14_0)
		{
			this.method_82();
			this.method_70();
			this.method_73();
		}

		// Token: 0x06001A19 RID: 6681 RVA: 0x0000AE73 File Offset: 0x00009073
		private void method_115(EventArgs14 eventArgs14_0)
		{
			this.class291_0.Refresh();
		}

		// Token: 0x06001A1A RID: 6682 RVA: 0x0000AE82 File Offset: 0x00009082
		private void method_116(EventArgs15 eventArgs15_0)
		{
			this.class292_0.Refresh();
			this.method_85();
			this.method_129();
		}

		// Token: 0x06001A1B RID: 6683 RVA: 0x0000AE82 File Offset: 0x00009082
		private void method_117(object sender, EventArgs e)
		{
			this.class292_0.Refresh();
			this.method_85();
			this.method_129();
		}

		// Token: 0x06001A1C RID: 6684 RVA: 0x0000AE9D File Offset: 0x0000909D
		private void method_118(EventArgs15 eventArgs15_0)
		{
			this.class292_0.Refresh();
		}

		// Token: 0x06001A1D RID: 6685 RVA: 0x0000AE82 File Offset: 0x00009082
		private void method_119(EventArgs15 eventArgs15_0)
		{
			this.class292_0.Refresh();
			this.method_85();
			this.method_129();
		}

		// Token: 0x06001A1E RID: 6686 RVA: 0x0000AEAC File Offset: 0x000090AC
		private void method_120(object sender, EventArgs e)
		{
			this.method_129();
		}

		// Token: 0x06001A1F RID: 6687 RVA: 0x0000AEB6 File Offset: 0x000090B6
		private void method_121(object sender, EventArgs e)
		{
			this.method_111();
		}

		// Token: 0x06001A20 RID: 6688 RVA: 0x0000AEC0 File Offset: 0x000090C0
		private void method_122(object sender, EventArgs e)
		{
			Base.Trading.CurrOpenTransList = Base.Trading.smethod_139(Base.Acct.CurrAccount.ID, Base.UI.Form.IfShowIndividualShownOpenTrans);
		}

		// Token: 0x06001A21 RID: 6689 RVA: 0x000B082C File Offset: 0x000AEA2C
		private void tabControl_Below_SelectedTabChanging(object sender, TabStripTabChangingEventArgs e)
		{
			Base.UI.MainForm.AcceptButton = null;
			if (!(e.NewTab.Name == "tabPg_Video"))
			{
				this.method_134(false);
				this.method_135();
				if (e.NewTab.Name == "tabPg_Below_Symbs")
				{
					this.method_26(true, null);
				}
				else if (e.NewTab.Name == "tabPg_Below_Acct")
				{
					this.trdAnalysisPanel_0.method_7();
				}
				else if (e.NewTab.Name == "tabPg_SelSymb")
				{
					Base.UI.MainForm.AcceptButton = this.symbFilterPanel.AcceptButton;
				}
			}
		}

		// Token: 0x06001A22 RID: 6690 RVA: 0x000B08DC File Offset: 0x000AEADC
		private void transTabPg_Hist_Click(object sender, EventArgs e)
		{
			if (this.dataGridViewHisTrans_1 == null)
			{
				this.splitContainer_Trans_Hist.Panel2.SuspendLayout();
				this.dataGridViewHisTrans_1 = new DataGridViewHisTrans(new SortableBindingList<ShownHisTrans>());
				this.dataGridViewHisTrans_1.ChangeToHisTransDTRequested += this.method_84;
				this.splitContainer_Trans_Hist.Panel2.Controls.Add(this.dataGridViewHisTrans_1);
				this.dataGridViewHisTrans_1.method_6();
				this.splitContainer_Trans_Hist.Panel2.ResumeLayout();
				this.btn_HisTransQuery.Click += this.btn_HisTransQuery_Click;
				if ((this.dateTimePicker_StartHisTrans.Value - DateTime.Now).TotalMinutes < 1.0)
				{
					try
					{
						this.dateTimePicker_StartHisTrans.Value = Base.Data.CurrSymbDataSet.CurrHisData.Date.Date.AddDays(-7.0);
						this.dateTimePicker_EndHisTrans.Value = Base.Data.CurrSymbDataSet.CurrHisData.Date.Date;
					}
					catch (Exception exception_)
					{
						Class182.smethod_0(exception_);
					}
				}
			}
		}

		// Token: 0x06001A23 RID: 6691 RVA: 0x000B0A10 File Offset: 0x000AEC10
		private void btn_HisTransQuery_Click(object sender, EventArgs e)
		{
			DateTime value = this.dateTimePicker_StartHisTrans.Value;
			DateTime value2 = this.dateTimePicker_EndHisTrans.Value;
			SortableBindingList<ShownHisTrans> dataSource = Base.Trading.smethod_146(value, value2);
			this.dataGridViewHisTrans_1.DataSource = dataSource;
			this.dataGridViewHisTrans_1.Refresh();
		}

		// Token: 0x06001A24 RID: 6692 RVA: 0x000B0A54 File Offset: 0x000AEC54
		private void method_123()
		{
			if (this.dataGridViewHisTrans_0 != null)
			{
				this.dataGridViewHisTrans_0.DataSource = null;
				this.dataGridViewHisTrans_0.Refresh();
			}
			if (this.dataGridViewHisTrans_1 != null)
			{
				this.dataGridViewHisTrans_1.DataSource = null;
				this.dataGridViewHisTrans_1.Refresh();
			}
		}

		// Token: 0x06001A25 RID: 6693 RVA: 0x000B0AA4 File Offset: 0x000AECA4
		private void method_124()
		{
			if (this.class291_0 != null)
			{
				this.class291_0.DataSource = null;
				this.class291_0.Refresh();
			}
			if (this.class291_1 != null)
			{
				this.class291_1.DataSource = null;
				this.class291_1.Refresh();
			}
		}

		// Token: 0x06001A26 RID: 6694 RVA: 0x0000AEE2 File Offset: 0x000090E2
		private void orderTabPg_Hist_Click(object sender, EventArgs e)
		{
			if (this.class291_1 == null)
			{
				this.method_125();
			}
		}

		// Token: 0x06001A27 RID: 6695 RVA: 0x000B0AF4 File Offset: 0x000AECF4
		private void method_125()
		{
			this.class291_1 = new Class291(new SortableBindingList<ShownOrder>());
			this.splitContainer_Orders_Hist.Panel2.Controls.Add(this.class291_1);
			this.class291_1.method_5();
			if ((this.dateTimePicker_StartOrders.Value - DateTime.Now).TotalMinutes < 1.0 && Base.Data.CurrSymbDataSet != null && Base.Data.CurrSymbDataSet.CurrHisData != null)
			{
				this.dateTimePicker_StartOrders.Value = Base.Data.CurrSymbDataSet.CurrHisData.Date.Date.AddDays(-7.0);
				this.dateTimePicker_EndOrders.Value = Base.Data.CurrSymbDataSet.CurrHisData.Date.Date;
			}
		}

		// Token: 0x06001A28 RID: 6696 RVA: 0x000B0BCC File Offset: 0x000AEDCC
		private void btn_OrderQuery_Click(object sender, EventArgs e)
		{
			if (this.class291_1 == null)
			{
				this.method_125();
			}
			DateTime value = this.dateTimePicker_StartOrders.Value;
			DateTime value2 = this.dateTimePicker_EndOrders.Value;
			this.class291_1.DataSource = null;
			SortableBindingList<ShownOrder> dataSource = Base.Trading.smethod_42(value, value2);
			this.class291_1.DataSource = dataSource;
			this.class291_1.method_9();
			this.class291_1.Refresh();
		}

		// Token: 0x06001A29 RID: 6697 RVA: 0x0000AEF4 File Offset: 0x000090F4
		private void method_126(object sender, ScrollEventArgs e)
		{
			((TabControlPanel)sender).Invalidate();
		}

		// Token: 0x06001A2A RID: 6698 RVA: 0x000B0C38 File Offset: 0x000AEE38
		private bool method_127(ShowMktSymb showMktSymb_0)
		{
			bool result;
			if (this.dataGridViewMkt_0.DataSource != null)
			{
				result = (this.dataGridViewMkt_0.DataSource as SortableBindingList<ShowMktSymb>).Contains(showMktSymb_0);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x06001A2B RID: 6699 RVA: 0x0000AF03 File Offset: 0x00009103
		private void method_128(object sender, EventArgs e)
		{
			this.method_69();
			this.method_70();
			this.method_72();
			this.method_111();
		}

		// Token: 0x06001A2C RID: 6700 RVA: 0x000B0C70 File Offset: 0x000AEE70
		private void comboBox_TradingSymbs_SelectedIndexChanged(object sender, EventArgs e)
		{
			TEx.Util.ComboBoxItem comboBoxItem = (sender as ComboBox).SelectedItem as TEx.Util.ComboBoxItem;
			if (comboBoxItem != null && comboBoxItem.Value != null)
			{
				StkSymbol stkSymbol = comboBoxItem.Value as StkSymbol;
				if (Base.UI.CurrTradingSymbol != null && Base.UI.CurrTradingSymbol.ID != stkSymbol.ID)
				{
					Base.UI.CurrTradingSymbol = stkSymbol;
					this.method_70();
					this.method_72();
				}
			}
		}

		// Token: 0x06001A2D RID: 6701 RVA: 0x000B0CD4 File Offset: 0x000AEED4
		private void TransTabs_Disposed(object sender, EventArgs e)
		{
			try
			{
				Base.Data.CurrSymblChanged -= this.method_107;
				Base.Acct.AccountChanging -= this.method_105;
				Base.Acct.AccountChanged -= this.method_106;
				Base.Trading.TransCreated -= this.method_112;
				Base.Trading.OrderCanceled -= this.method_113;
				Base.Trading.OrderCreated -= this.method_114;
				Base.Trading.OrderPriceUnitsUpdated -= this.method_115;
				Base.Trading.CondOrderCreated -= this.method_116;
				Base.Trading.CondOrderExecuted -= this.method_117;
				Base.Trading.CondOrderUpdated -= this.method_118;
				Base.Trading.CondOrderStatusUpdated -= this.method_119;
				Base.Trading.OpenTransListUpdated -= this.method_120;
				Base.UI.Form.StockShortSettingChanged -= this.method_121;
				Base.UI.CurrTradingSymbChanged -= this.method_128;
				this.dataGridViewMkt_0.Dispose();
				if (this.ParentSplitPanel != null)
				{
					this.ParentSplitPanel.Controls.Remove(this);
				}
				if (Base.Data.SymbDataSets != null)
				{
					foreach (SymbDataSet symbDataSet in Base.Data.SymbDataSets)
					{
						symbDataSet.CurrHisDataChanged -= this.method_101;
						symbDataSet.CurrDateChanged -= this.method_102;
					}
				}
			}
			catch (Exception exception_)
			{
				Class182.smethod_0(exception_);
			}
		}

		// Token: 0x06001A2E RID: 6702 RVA: 0x0000AF1F File Offset: 0x0000911F
		public void method_129()
		{
			if (this.dataGridViewOpenTrans_0 != null)
			{
				if (this.dataGridViewOpenTrans_0.DataSource != Base.Trading.CurrOpenTransList)
				{
					this.dataGridViewOpenTrans_0.method_5();
				}
				this.dataGridViewOpenTrans_0.Refresh();
			}
		}

		// Token: 0x06001A2F RID: 6703 RVA: 0x0000AF53 File Offset: 0x00009153
		public void method_130()
		{
			if (this.symbFilterPanel != null)
			{
				this.symbFilterPanel.method_26();
			}
		}

		// Token: 0x06001A30 RID: 6704 RVA: 0x000B0E90 File Offset: 0x000AF090
		public void method_131()
		{
			VideoPanel videoPanel = this.method_132();
			if (videoPanel != null)
			{
				videoPanel.method_32();
			}
			this.method_47();
		}

		// Token: 0x06001A31 RID: 6705 RVA: 0x000B0EB8 File Offset: 0x000AF0B8
		private VideoPanel method_132()
		{
			VideoPanel result = null;
			if (this.tabControlPanel_Video.Controls.Count > 0)
			{
				result = (this.tabControlPanel_Video.Controls[0] as VideoPanel);
			}
			return result;
		}

		// Token: 0x06001A32 RID: 6706 RVA: 0x000B0EF8 File Offset: 0x000AF0F8
		public void method_133()
		{
			if (this.IsInVideoPage)
			{
				VideoPanel videoPanel = this.method_132();
				if (videoPanel != null)
				{
					videoPanel.method_23();
				}
			}
		}

		// Token: 0x06001A33 RID: 6707 RVA: 0x000B0F20 File Offset: 0x000AF120
		private void method_134(bool bool_2)
		{
			VideoPanel videoPanel = this.method_132();
			if (videoPanel != null)
			{
				videoPanel.method_25(bool_2);
			}
		}

		// Token: 0x06001A34 RID: 6708 RVA: 0x000B0F40 File Offset: 0x000AF140
		public void method_135()
		{
			VideoPanel videoPanel = this.method_132();
			if (videoPanel != null)
			{
				videoPanel.method_24();
			}
		}

		// Token: 0x06001A35 RID: 6709 RVA: 0x0000AF6A File Offset: 0x0000916A
		public void method_136(string string_1)
		{
			this.method_140(this.tabControl_Below, string_1);
		}

		// Token: 0x06001A36 RID: 6710 RVA: 0x0000AF7B File Offset: 0x0000917B
		public void method_137(string string_1)
		{
			this.method_140(this.tabControl_0, string_1);
		}

		// Token: 0x06001A37 RID: 6711 RVA: 0x0000AF8C File Offset: 0x0000918C
		public void method_138(string string_1)
		{
			this.method_140(this.tabCtrl_Trans, string_1);
		}

		// Token: 0x06001A38 RID: 6712 RVA: 0x0000AF9D File Offset: 0x0000919D
		public void method_139(string string_1)
		{
			this.trdAnalysisPanel_0.method_40(string_1);
		}

		// Token: 0x06001A39 RID: 6713 RVA: 0x000B0F60 File Offset: 0x000AF160
		private void method_140(DevComponents.DotNetBar.TabControl tabControl_1, string string_1)
		{
			TransTabs.Class322 @class = new TransTabs.Class322();
			@class.string_0 = string_1;
			TabItem tabItem = tabControl_1.Tabs.Cast<TabItem>().ToList<TabItem>().SingleOrDefault(new Func<TabItem, bool>(@class.method_0));
			if (tabItem != null)
			{
				tabControl_1.SelectedTab = tabItem;
			}
		}

		// Token: 0x1700043D RID: 1085
		// (get) Token: 0x06001A3A RID: 6714 RVA: 0x000B0FA8 File Offset: 0x000AF1A8
		public SplitterPanel SplitterPanel
		{
			get
			{
				return this.splitterPanel_1;
			}
		}

		// Token: 0x1700043E RID: 1086
		// (get) Token: 0x06001A3B RID: 6715 RVA: 0x000B0FC0 File Offset: 0x000AF1C0
		// (set) Token: 0x06001A3C RID: 6716 RVA: 0x0000AFAD File Offset: 0x000091AD
		public TransTabCtrl ParentTransTabCtrl
		{
			get
			{
				return this.transTabCtrl_0;
			}
			set
			{
				this.transTabCtrl_0 = value;
			}
		}

		// Token: 0x1700043F RID: 1087
		// (get) Token: 0x06001A3D RID: 6717 RVA: 0x000B0FD8 File Offset: 0x000AF1D8
		// (set) Token: 0x06001A3E RID: 6718 RVA: 0x0000AFB8 File Offset: 0x000091B8
		public bool IsMaximized
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
			}
		}

		// Token: 0x17000440 RID: 1088
		// (get) Token: 0x06001A3F RID: 6719 RVA: 0x000B0FF0 File Offset: 0x000AF1F0
		// (set) Token: 0x06001A40 RID: 6720 RVA: 0x0000AFC3 File Offset: 0x000091C3
		public int OriContainerBarHeight
		{
			get
			{
				return this.int_0;
			}
			set
			{
				this.int_0 = value;
			}
		}

		// Token: 0x17000441 RID: 1089
		// (get) Token: 0x06001A41 RID: 6721 RVA: 0x000B1008 File Offset: 0x000AF208
		// (set) Token: 0x06001A42 RID: 6722 RVA: 0x000B1024 File Offset: 0x000AF224
		public int SelectedTabIndex
		{
			get
			{
				return this.tabControl_Below.SelectedTabIndex;
			}
			set
			{
				try
				{
					if (this.tabControl_Below != null)
					{
						this.tabControl_Below.SelectedTabIndex = value;
					}
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
		}

		// Token: 0x17000442 RID: 1090
		// (get) Token: 0x06001A43 RID: 6723 RVA: 0x000B1060 File Offset: 0x000AF260
		// (set) Token: 0x06001A44 RID: 6724 RVA: 0x0000AFCE File Offset: 0x000091CE
		public int MktSelectedTabIndex
		{
			get
			{
				return this.tabControl_0.SelectedTabIndex;
			}
			set
			{
				this.tabControl_0.SelectedTabIndex = value;
			}
		}

		// Token: 0x17000443 RID: 1091
		// (get) Token: 0x06001A45 RID: 6725 RVA: 0x000B107C File Offset: 0x000AF27C
		// (set) Token: 0x06001A46 RID: 6726 RVA: 0x0000AFDE File Offset: 0x000091DE
		public TabItem MktSelectedTab
		{
			get
			{
				return this.tabControl_0.SelectedTab;
			}
			set
			{
				this.tabControl_0.SelectedTab = value;
			}
		}

		// Token: 0x17000444 RID: 1092
		// (get) Token: 0x06001A47 RID: 6727 RVA: 0x000B1098 File Offset: 0x000AF298
		// (set) Token: 0x06001A48 RID: 6728 RVA: 0x0000AFEE File Offset: 0x000091EE
		public int TransSelectedTabIndex
		{
			get
			{
				return this.tabCtrl_Trans.SelectedTabIndex;
			}
			set
			{
				this.tabCtrl_Trans.SelectedTabIndex = value;
			}
		}

		// Token: 0x17000445 RID: 1093
		// (get) Token: 0x06001A49 RID: 6729 RVA: 0x000B10B4 File Offset: 0x000AF2B4
		// (set) Token: 0x06001A4A RID: 6730 RVA: 0x0000AFFE File Offset: 0x000091FE
		public TabItem TransSelectedTab
		{
			get
			{
				return this.tabCtrl_Trans.SelectedTab;
			}
			set
			{
				this.tabCtrl_Trans.SelectedTab = value;
			}
		}

		// Token: 0x17000446 RID: 1094
		// (get) Token: 0x06001A4B RID: 6731 RVA: 0x000B10D0 File Offset: 0x000AF2D0
		// (set) Token: 0x06001A4C RID: 6732 RVA: 0x0000B00E File Offset: 0x0000920E
		public TabItem SelectedTab
		{
			get
			{
				return this.tabControl_Below.SelectedTab;
			}
			set
			{
				this.tabControl_Below.SelectedTab = value;
			}
		}

		// Token: 0x17000447 RID: 1095
		// (get) Token: 0x06001A4D RID: 6733 RVA: 0x000B10EC File Offset: 0x000AF2EC
		public TabsCollection TabsCollection
		{
			get
			{
				return this.tabControl_Below.Tabs;
			}
		}

		// Token: 0x17000448 RID: 1096
		// (get) Token: 0x06001A4E RID: 6734 RVA: 0x000B1108 File Offset: 0x000AF308
		public TabsCollection MarketTabsCollection
		{
			get
			{
				return this.tabControl_0.Tabs;
			}
		}

		// Token: 0x17000449 RID: 1097
		// (get) Token: 0x06001A4F RID: 6735 RVA: 0x000B1124 File Offset: 0x000AF324
		public TabsCollection TransTabsCollection
		{
			get
			{
				return this.tabCtrl_Trans.Tabs;
			}
		}

		// Token: 0x1700044A RID: 1098
		// (get) Token: 0x06001A50 RID: 6736 RVA: 0x000B1140 File Offset: 0x000AF340
		// (set) Token: 0x06001A51 RID: 6737 RVA: 0x0000B01E File Offset: 0x0000921E
		public SplitterPanel ParentSplitPanel
		{
			get
			{
				return this.splitterPanel_0;
			}
			set
			{
				this.splitterPanel_0 = value;
			}
		}

		// Token: 0x1700044B RID: 1099
		// (get) Token: 0x06001A52 RID: 6738 RVA: 0x000B1158 File Offset: 0x000AF358
		// (set) Token: 0x06001A53 RID: 6739 RVA: 0x0000B029 File Offset: 0x00009229
		public List<ShowMktSymb> CurrShowMktSymbList
		{
			get
			{
				return this.list_2;
			}
			private set
			{
				this.list_2 = value;
			}
		}

		// Token: 0x1700044C RID: 1100
		// (get) Token: 0x06001A54 RID: 6740 RVA: 0x000B1170 File Offset: 0x000AF370
		public List<KeyValuePair<DataGridView, int>> DataGridViewListWithCurrMktSymb
		{
			get
			{
				return this.list_3;
			}
		}

		// Token: 0x1700044D RID: 1101
		// (get) Token: 0x06001A55 RID: 6741 RVA: 0x000B1188 File Offset: 0x000AF388
		public bool IsDateTimePickersFocused
		{
			get
			{
				bool result;
				if (!this.dateTimePicker_EndHisTrans.Focused && !this.dateTimePicker_EndOrders.Focused && !this.dateTimePicker_StartHisTrans.Focused)
				{
					result = this.dateTimePicker_StartOrders.Focused;
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x1700044E RID: 1102
		// (get) Token: 0x06001A56 RID: 6742 RVA: 0x000B11D0 File Offset: 0x000AF3D0
		public List<StkSymbol> ZixuanStkSymbList
		{
			get
			{
				List<StkSymbol> list = null;
				if (this.dataGridViewMkt_0.DataSource != null)
				{
					Collection<ShowMktSymb> collection = this.dataGridViewMkt_0.DataSource as SortableBindingList<ShowMktSymb>;
					list = new List<StkSymbol>();
					foreach (ShowMktSymb showMktSymb in collection)
					{
						list.Add(SymbMgr.smethod_3(showMktSymb.StkId));
					}
				}
				return list;
			}
		}

		// Token: 0x1700044F RID: 1103
		// (get) Token: 0x06001A57 RID: 6743 RVA: 0x000B124C File Offset: 0x000AF44C
		public bool IsInInputState
		{
			get
			{
				bool result;
				if (!this.numericUpDown_TUnits.Focused && !this.numericUpDown_TPrice.Focused && !this.baoDianPanel.IsInInputState)
				{
					result = this.symbFilterPanel.IsInInputState;
				}
				else
				{
					result = true;
				}
				return result;
			}
		}

		// Token: 0x17000450 RID: 1104
		// (get) Token: 0x06001A58 RID: 6744 RVA: 0x000B1294 File Offset: 0x000AF494
		// (set) Token: 0x06001A59 RID: 6745 RVA: 0x0000B034 File Offset: 0x00009234
		public bool IfAutoOpenClose
		{
			get
			{
				return this.chkBox_AutoOpenClose.Checked;
			}
			set
			{
				if (this.chkBox_AutoOpenClose.Checked != value)
				{
					this.chkBox_AutoOpenClose.Checked = value;
				}
			}
		}

		// Token: 0x17000451 RID: 1105
		// (get) Token: 0x06001A5A RID: 6746 RVA: 0x000B12B0 File Offset: 0x000AF4B0
		// (set) Token: 0x06001A5B RID: 6747 RVA: 0x0000B052 File Offset: 0x00009252
		public bool IfFollowPrice
		{
			get
			{
				return this.chkBox_FollowPrc.Checked;
			}
			set
			{
				if (this.chkBox_FollowPrc.Checked != value)
				{
					this.chkBox_FollowPrc.Checked = value;
				}
			}
		}

		// Token: 0x17000452 RID: 1106
		// (get) Token: 0x06001A5C RID: 6748 RVA: 0x000B12CC File Offset: 0x000AF4CC
		// (set) Token: 0x06001A5D RID: 6749 RVA: 0x0000B070 File Offset: 0x00009270
		public decimal InputTradingUnits
		{
			get
			{
				return this.numericUpDown_TUnits.Value;
			}
			set
			{
				if (this.numericUpDown_TUnits.Value != value)
				{
					this.numericUpDown_TUnits.Value = value;
				}
			}
		}

		// Token: 0x17000453 RID: 1107
		// (get) Token: 0x06001A5E RID: 6750 RVA: 0x000B12E8 File Offset: 0x000AF4E8
		// (set) Token: 0x06001A5F RID: 6751 RVA: 0x0000B093 File Offset: 0x00009293
		public decimal InputTradingPrice
		{
			get
			{
				return this.numericUpDown_TPrice.Value;
			}
			set
			{
				if (this.numericUpDown_TPrice.Value != value)
				{
					this.numericUpDown_TPrice.Value = value;
				}
			}
		}

		// Token: 0x17000454 RID: 1108
		// (get) Token: 0x06001A60 RID: 6752 RVA: 0x000B1304 File Offset: 0x000AF504
		public bool IsScrollableCtrlFocused
		{
			get
			{
				return this.baoDianPanel.IsScrollableCtrlFocused;
			}
		}

		// Token: 0x17000455 RID: 1109
		// (get) Token: 0x06001A61 RID: 6753 RVA: 0x000B1320 File Offset: 0x000AF520
		public bool IsInVideoPage
		{
			get
			{
				return this.tabControl_Below.SelectedTab.Name.Contains("Video");
			}
		}

		// Token: 0x17000456 RID: 1110
		// (get) Token: 0x06001A62 RID: 6754 RVA: 0x000B134C File Offset: 0x000AF54C
		public bool IsPlayingVideo
		{
			get
			{
				VideoPanel videoPanel = this.method_132();
				bool result;
				if (videoPanel != null && videoPanel.IsPlaying)
				{
					result = true;
				}
				else
				{
					result = false;
				}
				return result;
			}
		}

		// Token: 0x17000457 RID: 1111
		// (get) Token: 0x06001A63 RID: 6755 RVA: 0x000B1374 File Offset: 0x000AF574
		// (set) Token: 0x06001A64 RID: 6756 RVA: 0x0000B0B6 File Offset: 0x000092B6
		public bool IsInitiating { get; private set; }

		// Token: 0x06001A65 RID: 6757 RVA: 0x0000B0C1 File Offset: 0x000092C1
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001A66 RID: 6758 RVA: 0x000B138C File Offset: 0x000AF58C
		private void InitializeComponent()
		{
			this.icontainer_0 = new Container();
			this.tabControl_Below = new DevComponents.DotNetBar.TabControl();
			this.tabControlPanel_Trading = new TabControlPanel();
			this.panel_Trans = new Panel();
			this.splitContainer_TransTabsBelow = new SplitContainer();
			this.tblLayoutPanel_TrdBtns = new TableLayoutPanel();
			this.transTabTopBtn1 = new Button();
			this.transTabTopBtn2 = new Button();
			this.transTabTopBtn3 = new Button();
			this.transTabTopBtn4 = new Button();
			this.radioBtn_IndividualSOT = new RadioButton();
			this.radioBtn_AggregateSOT = new RadioButton();
			this.tabCtrl_Trans = new DevComponents.DotNetBar.TabControl();
			this.tabControlPanel_Order = new TabControlPanel();
			this.tabControl_Orders = new DevComponents.DotNetBar.TabControl();
			this.tabControlPanel_Orders_Hist = new TabControlPanel();
			this.splitContainer_Orders_Hist = new SplitContainer();
			this.tblLayoutPanel_OdrHistQry = new TableLayoutPanel();
			this.btn_OrderQuery = new Button();
			this.label36 = new Label();
			this.dateTimePicker_EndOrders = new DateTimePicker();
			this.dateTimePicker_StartOrders = new DateTimePicker();
			this.label5 = new Label();
			this.orderTabPg_Hist = new TabItem(this.icontainer_0);
			this.tabControlPanel_Orders_Curr = new TabControlPanel();
			this.orderTabPg_Curr = new TabItem(this.icontainer_0);
			this.tabPg_Order = new TabItem(this.icontainer_0);
			this.tabControlPanel_Trans = new TabControlPanel();
			this.tabCtrl_HisTrans = new DevComponents.DotNetBar.TabControl();
			this.tabControlPanel_Trans_Hist = new TabControlPanel();
			this.splitContainer_Trans_Hist = new SplitContainer();
			this.tblLayoutPanel_TransHistQry = new TableLayoutPanel();
			this.btn_HisTransQuery = new Button();
			this.label_startDate = new Label();
			this.dateTimePicker_EndHisTrans = new DateTimePicker();
			this.dateTimePicker_StartHisTrans = new DateTimePicker();
			this.label_endDate = new Label();
			this.transTabPg_Hist = new TabItem(this.icontainer_0);
			this.tabControlPanel_Trans_Curr = new TabControlPanel();
			this.transTabPg_Curr = new TabItem(this.icontainer_0);
			this.tabPg_Trans = new TabItem(this.icontainer_0);
			this.tabControlPanel_OpenPos = new TabControlPanel();
			this.tabPg_OpenPos = new TabItem(this.icontainer_0);
			this.tabControlPanel_CondOrder = new TabControlPanel();
			this.tabPg_CondOrder = new TabItem(this.icontainer_0);
			this.expSplitter_Trade = new ExpandableSplitter();
			this.panel_Trading = new Panel();
			this.comboBox_TradingSymbs = new ComboBox();
			this.label_PosRatio = new Label();
			this.label_仓位 = new Label();
			this.label_usableBal = new Label();
			this.label40 = new Label();
			this.chkBox_AutoOpenClose = new CheckBox();
			this.panel_OpenClose = new Panel();
			this.radioBtn_Close = new Class287();
			this.radioBtn_Open = new Class287();
			this.chkBox_FollowPrc = new CheckBox();
			this.btn_CondOrder = new Button();
			this.btn_TOpenShrt = new Button();
			this.btn_TOpenLong = new Button();
			this.panel_PercRadioBtns = new Panel();
			this.radioBtn_100Pers = new RadioButton();
			this.radioBtn_80Pers = new RadioButton();
			this.radioBtn_50Pers = new RadioButton();
			this.radioBtn_30Pers = new RadioButton();
			this.radioBtn_15Pers = new RadioButton();
			this.numericUpDown_TPrice = new NumericUpDown();
			this.label_价格 = new Label();
			this.label_maxPosUnits = new Label();
			this.numericUpDown_TUnits = new NumericUpDown();
			this.label_数量 = new Label();
			this.label_品种 = new Label();
			this.tabPg_Below_Trade = new TabItem(this.icontainer_0);
			this.tabControlPanel_MktSymbs = new TabControlPanel();
			this.tabPg_Below_Symbs = new TabItem(this.icontainer_0);
			this.tabControlPanel_Below_Acct = new TabControlPanel();
			this.tabPg_Below_Acct = new TabItem(this.icontainer_0);
			this.tabControlPanel_SelSymb = new TabControlPanel();
			this.symbFilterPanel = new SymbFilterPanel();
			this.tabPg_SelSymb = new TabItem(this.icontainer_0);
			this.tabControlPanel_Video = new TabControlPanel();
			this.tabPg_Video = new TabItem(this.icontainer_0);
			this.tabControlPanel_FnRpt = new TabControlPanel();
			this.fnRptAnlysPanel = new FnRptAnlysPanel();
			this.tabPg_FnRpt = new TabItem(this.icontainer_0);
			this.tabControlPanel_BaoDian_Below = new TabControlPanel();
			this.baoDianPanel = new BaoDianPanel();
			this.tabPg_BaoDian = new TabItem(this.icontainer_0);
			((ISupportInitialize)this.tabControl_Below).BeginInit();
			this.tabControl_Below.SuspendLayout();
			this.tabControlPanel_Trading.SuspendLayout();
			this.panel_Trans.SuspendLayout();
			this.splitContainer_TransTabsBelow.Panel1.SuspendLayout();
			this.splitContainer_TransTabsBelow.Panel2.SuspendLayout();
			this.splitContainer_TransTabsBelow.SuspendLayout();
			this.tblLayoutPanel_TrdBtns.SuspendLayout();
			((ISupportInitialize)this.tabCtrl_Trans).BeginInit();
			this.tabCtrl_Trans.SuspendLayout();
			this.tabControlPanel_Order.SuspendLayout();
			((ISupportInitialize)this.tabControl_Orders).BeginInit();
			this.tabControl_Orders.SuspendLayout();
			this.tabControlPanel_Orders_Hist.SuspendLayout();
			this.splitContainer_Orders_Hist.Panel1.SuspendLayout();
			this.splitContainer_Orders_Hist.SuspendLayout();
			this.tblLayoutPanel_OdrHistQry.SuspendLayout();
			this.tabControlPanel_Trans.SuspendLayout();
			((ISupportInitialize)this.tabCtrl_HisTrans).BeginInit();
			this.tabCtrl_HisTrans.SuspendLayout();
			this.tabControlPanel_Trans_Hist.SuspendLayout();
			this.splitContainer_Trans_Hist.Panel1.SuspendLayout();
			this.splitContainer_Trans_Hist.SuspendLayout();
			this.tblLayoutPanel_TransHistQry.SuspendLayout();
			this.panel_Trading.SuspendLayout();
			this.panel_OpenClose.SuspendLayout();
			this.panel_PercRadioBtns.SuspendLayout();
			((ISupportInitialize)this.numericUpDown_TPrice).BeginInit();
			((ISupportInitialize)this.numericUpDown_TUnits).BeginInit();
			this.tabControlPanel_SelSymb.SuspendLayout();
			this.tabControlPanel_FnRpt.SuspendLayout();
			this.tabControlPanel_BaoDian_Below.SuspendLayout();
			base.SuspendLayout();
			this.tabControl_Below.CanReorderTabs = false;
			this.tabControl_Below.CloseButtonOnTabsAlwaysDisplayed = false;
			this.tabControl_Below.ColorScheme.TabItemBorder = Color.Empty;
			this.tabControl_Below.ColorScheme.TabPanelBorder = Color.Empty;
			this.tabControl_Below.Controls.Add(this.tabControlPanel_Trading);
			this.tabControl_Below.Controls.Add(this.tabControlPanel_MktSymbs);
			this.tabControl_Below.Controls.Add(this.tabControlPanel_Below_Acct);
			this.tabControl_Below.Controls.Add(this.tabControlPanel_SelSymb);
			this.tabControl_Below.Controls.Add(this.tabControlPanel_Video);
			this.tabControl_Below.Controls.Add(this.tabControlPanel_FnRpt);
			this.tabControl_Below.Controls.Add(this.tabControlPanel_BaoDian_Below);
			this.tabControl_Below.Dock = DockStyle.Fill;
			this.tabControl_Below.Location = new Point(0, 0);
			this.tabControl_Below.Name = "tabControl_Below";
			this.tabControl_Below.SelectedTabIndex = 0;
			this.tabControl_Below.Size = new Size(1116, 528);
			this.tabControl_Below.Style = eTabStripStyle.Metro;
			this.tabControl_Below.TabIndex = 0;
			this.tabControl_Below.TabLayoutType = eTabLayoutType.FixedWithNavigationBox;
			this.tabControl_Below.Tabs.Add(this.tabPg_Below_Symbs);
			this.tabControl_Below.Tabs.Add(this.tabPg_Below_Trade);
			this.tabControl_Below.Tabs.Add(this.tabPg_Below_Acct);
			this.tabControl_Below.Tabs.Add(this.tabPg_SelSymb);
			this.tabControl_Below.Tabs.Add(this.tabPg_FnRpt);
			this.tabControl_Below.Tabs.Add(this.tabPg_BaoDian);
			this.tabControl_Below.Tabs.Add(this.tabPg_Video);
			this.tabControlPanel_Trading.Controls.Add(this.panel_Trans);
			this.tabControlPanel_Trading.Controls.Add(this.expSplitter_Trade);
			this.tabControlPanel_Trading.Controls.Add(this.panel_Trading);
			this.tabControlPanel_Trading.Dock = DockStyle.Fill;
			this.tabControlPanel_Trading.Location = new Point(0, 30);
			this.tabControlPanel_Trading.Name = "tabControlPanel_Trading";
			this.tabControlPanel_Trading.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_Trading.Size = new Size(1116, 498);
			this.tabControlPanel_Trading.Style.BackColor1.Color = Color.FromArgb(227, 239, 255);
			this.tabControlPanel_Trading.Style.BackColor2.Color = Color.FromArgb(176, 210, 255);
			this.tabControlPanel_Trading.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_Trading.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Bottom);
			this.tabControlPanel_Trading.Style.GradientAngle = 90;
			this.tabControlPanel_Trading.TabIndex = 3;
			this.tabControlPanel_Trading.TabItem = this.tabPg_Below_Trade;
			this.panel_Trans.Controls.Add(this.splitContainer_TransTabsBelow);
			this.panel_Trans.Dock = DockStyle.Fill;
			this.panel_Trans.Location = new Point(317, 1);
			this.panel_Trans.Name = "panel_Trans";
			this.panel_Trans.Size = new Size(798, 496);
			this.panel_Trans.TabIndex = 2;
			this.splitContainer_TransTabsBelow.Dock = DockStyle.Fill;
			this.splitContainer_TransTabsBelow.FixedPanel = FixedPanel.Panel1;
			this.splitContainer_TransTabsBelow.IsSplitterFixed = true;
			this.splitContainer_TransTabsBelow.Location = new Point(0, 0);
			this.splitContainer_TransTabsBelow.Name = "splitContainer_TransTabsBelow";
			this.splitContainer_TransTabsBelow.Orientation = Orientation.Horizontal;
			this.splitContainer_TransTabsBelow.Panel1.Controls.Add(this.tblLayoutPanel_TrdBtns);
			this.splitContainer_TransTabsBelow.Panel2.Controls.Add(this.tabCtrl_Trans);
			this.splitContainer_TransTabsBelow.Size = new Size(798, 496);
			this.splitContainer_TransTabsBelow.SplitterDistance = 35;
			this.splitContainer_TransTabsBelow.SplitterWidth = 1;
			this.splitContainer_TransTabsBelow.TabIndex = 1;
			this.tblLayoutPanel_TrdBtns.ColumnCount = 7;
			this.tblLayoutPanel_TrdBtns.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100f));
			this.tblLayoutPanel_TrdBtns.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100f));
			this.tblLayoutPanel_TrdBtns.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100f));
			this.tblLayoutPanel_TrdBtns.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100f));
			this.tblLayoutPanel_TrdBtns.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100f));
			this.tblLayoutPanel_TrdBtns.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100f));
			this.tblLayoutPanel_TrdBtns.ColumnStyles.Add(new ColumnStyle());
			this.tblLayoutPanel_TrdBtns.Controls.Add(this.transTabTopBtn1, 0, 0);
			this.tblLayoutPanel_TrdBtns.Controls.Add(this.transTabTopBtn2, 1, 0);
			this.tblLayoutPanel_TrdBtns.Controls.Add(this.transTabTopBtn3, 2, 0);
			this.tblLayoutPanel_TrdBtns.Controls.Add(this.transTabTopBtn4, 3, 0);
			this.tblLayoutPanel_TrdBtns.Controls.Add(this.radioBtn_IndividualSOT, 5, 0);
			this.tblLayoutPanel_TrdBtns.Controls.Add(this.radioBtn_AggregateSOT, 4, 0);
			this.tblLayoutPanel_TrdBtns.Dock = DockStyle.Fill;
			this.tblLayoutPanel_TrdBtns.Location = new Point(0, 0);
			this.tblLayoutPanel_TrdBtns.Margin = new System.Windows.Forms.Padding(0);
			this.tblLayoutPanel_TrdBtns.Name = "tblLayoutPanel_TrdBtns";
			this.tblLayoutPanel_TrdBtns.RowCount = 1;
			this.tblLayoutPanel_TrdBtns.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tblLayoutPanel_TrdBtns.Size = new Size(798, 35);
			this.tblLayoutPanel_TrdBtns.TabIndex = 0;
			this.transTabTopBtn1.Anchor = AnchorStyles.None;
			this.transTabTopBtn1.BackColor = Color.Transparent;
			this.transTabTopBtn1.Location = new Point(6, 5);
			this.transTabTopBtn1.Margin = new System.Windows.Forms.Padding(0);
			this.transTabTopBtn1.Name = "transTabTopBtn1";
			this.transTabTopBtn1.Size = new Size(88, 25);
			this.transTabTopBtn1.TabIndex = 0;
			this.transTabTopBtn1.Text = "市价平仓";
			this.transTabTopBtn1.UseVisualStyleBackColor = false;
			this.transTabTopBtn2.Anchor = AnchorStyles.None;
			this.transTabTopBtn2.BackColor = Color.Transparent;
			this.transTabTopBtn2.Location = new Point(106, 4);
			this.transTabTopBtn2.Margin = new System.Windows.Forms.Padding(0);
			this.transTabTopBtn2.Name = "transTabTopBtn2";
			this.transTabTopBtn2.Size = new Size(88, 26);
			this.transTabTopBtn2.TabIndex = 1;
			this.transTabTopBtn2.Text = "全部平仓";
			this.transTabTopBtn2.UseVisualStyleBackColor = false;
			this.transTabTopBtn3.Anchor = AnchorStyles.None;
			this.transTabTopBtn3.BackColor = Color.Transparent;
			this.transTabTopBtn3.Location = new Point(206, 4);
			this.transTabTopBtn3.Margin = new System.Windows.Forms.Padding(0);
			this.transTabTopBtn3.Name = "transTabTopBtn3";
			this.transTabTopBtn3.Size = new Size(88, 26);
			this.transTabTopBtn3.TabIndex = 2;
			this.transTabTopBtn3.Text = "市价反手";
			this.transTabTopBtn3.UseVisualStyleBackColor = false;
			this.transTabTopBtn4.Anchor = AnchorStyles.None;
			this.transTabTopBtn4.BackColor = Color.Transparent;
			this.transTabTopBtn4.Location = new Point(306, 4);
			this.transTabTopBtn4.Margin = new System.Windows.Forms.Padding(0);
			this.transTabTopBtn4.Name = "transTabTopBtn4";
			this.transTabTopBtn4.Size = new Size(88, 26);
			this.transTabTopBtn4.TabIndex = 3;
			this.transTabTopBtn4.Text = "止损止盈";
			this.transTabTopBtn4.UseVisualStyleBackColor = false;
			this.radioBtn_IndividualSOT.Anchor = AnchorStyles.None;
			this.radioBtn_IndividualSOT.AutoSize = true;
			this.radioBtn_IndividualSOT.FlatStyle = FlatStyle.Popup;
			this.radioBtn_IndividualSOT.Location = new Point(506, 8);
			this.radioBtn_IndividualSOT.Margin = new System.Windows.Forms.Padding(0);
			this.radioBtn_IndividualSOT.Name = "radioBtn_IndividualSOT";
			this.radioBtn_IndividualSOT.Size = new Size(87, 19);
			this.radioBtn_IndividualSOT.TabIndex = 1;
			this.radioBtn_IndividualSOT.TabStop = true;
			this.radioBtn_IndividualSOT.Text = "明细持仓";
			this.radioBtn_IndividualSOT.UseVisualStyleBackColor = false;
			this.radioBtn_AggregateSOT.Anchor = AnchorStyles.None;
			this.radioBtn_AggregateSOT.AutoSize = true;
			this.radioBtn_AggregateSOT.FlatStyle = FlatStyle.Popup;
			this.radioBtn_AggregateSOT.Location = new Point(406, 8);
			this.radioBtn_AggregateSOT.Margin = new System.Windows.Forms.Padding(0);
			this.radioBtn_AggregateSOT.Name = "radioBtn_AggregateSOT";
			this.radioBtn_AggregateSOT.Size = new Size(87, 19);
			this.radioBtn_AggregateSOT.TabIndex = 0;
			this.radioBtn_AggregateSOT.TabStop = true;
			this.radioBtn_AggregateSOT.Text = "合并持仓";
			this.radioBtn_AggregateSOT.UseVisualStyleBackColor = false;
			this.tabCtrl_Trans.BackColor = Color.FromArgb(194, 217, 247);
			this.tabCtrl_Trans.CanReorderTabs = true;
			this.tabCtrl_Trans.ColorScheme.TabBackground = SystemColors.Control;
			this.tabCtrl_Trans.ColorScheme.TabBackground2 = SystemColors.Control;
			this.tabCtrl_Trans.Controls.Add(this.tabControlPanel_Order);
			this.tabCtrl_Trans.Controls.Add(this.tabControlPanel_Trans);
			this.tabCtrl_Trans.Controls.Add(this.tabControlPanel_OpenPos);
			this.tabCtrl_Trans.Controls.Add(this.tabControlPanel_CondOrder);
			this.tabCtrl_Trans.Dock = DockStyle.Fill;
			this.tabCtrl_Trans.Location = new Point(0, 0);
			this.tabCtrl_Trans.Name = "tabCtrl_Trans";
			this.tabCtrl_Trans.SelectedTabIndex = 0;
			this.tabCtrl_Trans.Size = new Size(798, 460);
			this.tabCtrl_Trans.Style = eTabStripStyle.Flat;
			this.tabCtrl_Trans.TabAlignment = eTabStripAlignment.Bottom;
			this.tabCtrl_Trans.TabIndex = 0;
			this.tabCtrl_Trans.TabLayoutType = eTabLayoutType.FixedWithNavigationBox;
			this.tabCtrl_Trans.Tabs.Add(this.tabPg_OpenPos);
			this.tabCtrl_Trans.Tabs.Add(this.tabPg_Trans);
			this.tabCtrl_Trans.Tabs.Add(this.tabPg_Order);
			this.tabCtrl_Trans.Tabs.Add(this.tabPg_CondOrder);
			this.tabCtrl_Trans.Text = "tabControlTrans";
			this.tabControlPanel_Order.AutoScroll = true;
			this.tabControlPanel_Order.Controls.Add(this.tabControl_Orders);
			this.tabControlPanel_Order.Dock = DockStyle.Fill;
			this.tabControlPanel_Order.Location = new Point(0, 0);
			this.tabControlPanel_Order.Name = "tabControlPanel_Order";
			this.tabControlPanel_Order.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_Order.Size = new Size(798, 432);
			this.tabControlPanel_Order.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_Order.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_Order.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_Order.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_Order.Style.GradientAngle = -90;
			this.tabControlPanel_Order.TabIndex = 3;
			this.tabControlPanel_Order.TabItem = this.tabPg_Order;
			this.tabControl_Orders.CanReorderTabs = true;
			this.tabControl_Orders.ColorScheme.TabBorder = Color.Transparent;
			this.tabControl_Orders.ColorScheme.TabItemBackgroundColorBlend.AddRange(new BackgroundColorBlend[]
			{
				new BackgroundColorBlend(Color.FromArgb(215, 230, 249), 0f),
				new BackgroundColorBlend(Color.FromArgb(199, 220, 248), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(179, 208, 245), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(215, 229, 247), 1f)
			});
			this.tabControl_Orders.ColorScheme.TabItemBorder = Color.Transparent;
			this.tabControl_Orders.ColorScheme.TabItemHotBackgroundColorBlend.AddRange(new BackgroundColorBlend[]
			{
				new BackgroundColorBlend(Color.FromArgb(255, 253, 235), 0f),
				new BackgroundColorBlend(Color.FromArgb(255, 236, 168), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(255, 218, 89), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(255, 230, 141), 1f)
			});
			this.tabControl_Orders.ColorScheme.TabItemSelectedBackgroundColorBlend.AddRange(new BackgroundColorBlend[]
			{
				new BackgroundColorBlend(Color.White, 0f),
				new BackgroundColorBlend(Color.FromArgb(253, 253, 254), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(253, 253, 254), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(253, 253, 254), 1f)
			});
			this.tabControl_Orders.ColorScheme.TabPanelBorder = Color.Transparent;
			this.tabControl_Orders.Controls.Add(this.tabControlPanel_Orders_Hist);
			this.tabControl_Orders.Controls.Add(this.tabControlPanel_Orders_Curr);
			this.tabControl_Orders.Dock = DockStyle.Fill;
			this.tabControl_Orders.Location = new Point(1, 1);
			this.tabControl_Orders.Name = "tabControl_Orders";
			this.tabControl_Orders.SelectedTabIndex = 0;
			this.tabControl_Orders.Size = new Size(796, 430);
			this.tabControl_Orders.Style = eTabStripStyle.Office2007Dock;
			this.tabControl_Orders.TabAlignment = eTabStripAlignment.Right;
			this.tabControl_Orders.TabIndex = 1;
			this.tabControl_Orders.TabLayoutType = eTabLayoutType.FixedWithNavigationBox;
			this.tabControl_Orders.Tabs.Add(this.orderTabPg_Curr);
			this.tabControl_Orders.Tabs.Add(this.orderTabPg_Hist);
			this.tabControl_Orders.Text = "tabControl1";
			this.tabControl_Orders.ThemeAware = true;
			this.tabControlPanel_Orders_Hist.Controls.Add(this.splitContainer_Orders_Hist);
			this.tabControlPanel_Orders_Hist.Dock = DockStyle.Fill;
			this.tabControlPanel_Orders_Hist.Location = new Point(0, 0);
			this.tabControlPanel_Orders_Hist.Name = "tabControlPanel_Orders_Hist";
			this.tabControlPanel_Orders_Hist.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_Orders_Hist.Size = new Size(769, 430);
			this.tabControlPanel_Orders_Hist.Style.BackColor1.Color = Color.FromArgb(253, 253, 254);
			this.tabControlPanel_Orders_Hist.Style.BackColor2.Color = Color.FromArgb(157, 188, 227);
			this.tabControlPanel_Orders_Hist.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_Orders_Hist.Style.BorderColor.Color = Color.Transparent;
			this.tabControlPanel_Orders_Hist.Style.BorderSide = (eBorderSide.Left | eBorderSide.Top | eBorderSide.Bottom);
			this.tabControlPanel_Orders_Hist.Style.GradientAngle = 180;
			this.tabControlPanel_Orders_Hist.TabIndex = 2;
			this.tabControlPanel_Orders_Hist.TabItem = this.orderTabPg_Hist;
			this.splitContainer_Orders_Hist.Dock = DockStyle.Fill;
			this.splitContainer_Orders_Hist.FixedPanel = FixedPanel.Panel1;
			this.splitContainer_Orders_Hist.IsSplitterFixed = true;
			this.splitContainer_Orders_Hist.Location = new Point(1, 1);
			this.splitContainer_Orders_Hist.Name = "splitContainer_Orders_Hist";
			this.splitContainer_Orders_Hist.Orientation = Orientation.Horizontal;
			this.splitContainer_Orders_Hist.Panel1.BackColor = Color.Transparent;
			this.splitContainer_Orders_Hist.Panel1.Controls.Add(this.tblLayoutPanel_OdrHistQry);
			this.splitContainer_Orders_Hist.Panel1MinSize = 28;
			this.splitContainer_Orders_Hist.Size = new Size(767, 428);
			this.splitContainer_Orders_Hist.SplitterDistance = 32;
			this.splitContainer_Orders_Hist.SplitterWidth = 1;
			this.splitContainer_Orders_Hist.TabIndex = 0;
			this.tblLayoutPanel_OdrHistQry.ColumnCount = 6;
			this.tblLayoutPanel_OdrHistQry.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 70f));
			this.tblLayoutPanel_OdrHistQry.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 170f));
			this.tblLayoutPanel_OdrHistQry.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 70f));
			this.tblLayoutPanel_OdrHistQry.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 170f));
			this.tblLayoutPanel_OdrHistQry.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80f));
			this.tblLayoutPanel_OdrHistQry.ColumnStyles.Add(new ColumnStyle());
			this.tblLayoutPanel_OdrHistQry.Controls.Add(this.btn_OrderQuery, 4, 0);
			this.tblLayoutPanel_OdrHistQry.Controls.Add(this.label36, 0, 0);
			this.tblLayoutPanel_OdrHistQry.Controls.Add(this.dateTimePicker_EndOrders, 3, 0);
			this.tblLayoutPanel_OdrHistQry.Controls.Add(this.dateTimePicker_StartOrders, 1, 0);
			this.tblLayoutPanel_OdrHistQry.Controls.Add(this.label5, 2, 0);
			this.tblLayoutPanel_OdrHistQry.Dock = DockStyle.Fill;
			this.tblLayoutPanel_OdrHistQry.Location = new Point(0, 0);
			this.tblLayoutPanel_OdrHistQry.Margin = new System.Windows.Forms.Padding(0);
			this.tblLayoutPanel_OdrHistQry.Name = "tblLayoutPanel_OdrHistQry";
			this.tblLayoutPanel_OdrHistQry.RowCount = 1;
			this.tblLayoutPanel_OdrHistQry.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tblLayoutPanel_OdrHistQry.Size = new Size(767, 32);
			this.tblLayoutPanel_OdrHistQry.TabIndex = 5;
			this.btn_OrderQuery.Anchor = AnchorStyles.None;
			this.btn_OrderQuery.BackColor = Color.Transparent;
			this.btn_OrderQuery.Location = new Point(489, 3);
			this.btn_OrderQuery.Margin = new System.Windows.Forms.Padding(0);
			this.btn_OrderQuery.Name = "btn_OrderQuery";
			this.btn_OrderQuery.Size = new Size(62, 25);
			this.btn_OrderQuery.TabIndex = 4;
			this.btn_OrderQuery.Text = "查询";
			this.btn_OrderQuery.UseVisualStyleBackColor = false;
			this.label36.Anchor = AnchorStyles.Right;
			this.label36.AutoSize = true;
			this.label36.Location = new Point(18, 8);
			this.label36.Margin = new System.Windows.Forms.Padding(0);
			this.label36.Name = "label36";
			this.label36.Size = new Size(52, 15);
			this.label36.TabIndex = 1;
			this.label36.Text = "起始：";
			this.label36.BackColor = Color.Transparent;
			this.dateTimePicker_EndOrders.Anchor = AnchorStyles.None;
			this.dateTimePicker_EndOrders.Location = new Point(313, 3);
			this.dateTimePicker_EndOrders.Margin = new System.Windows.Forms.Padding(0);
			this.dateTimePicker_EndOrders.Name = "dateTimePicker_EndOrders";
			this.dateTimePicker_EndOrders.Size = new Size(163, 25);
			this.dateTimePicker_EndOrders.TabIndex = 3;
			this.dateTimePicker_StartOrders.Anchor = AnchorStyles.None;
			this.dateTimePicker_StartOrders.Location = new Point(73, 3);
			this.dateTimePicker_StartOrders.Margin = new System.Windows.Forms.Padding(0);
			this.dateTimePicker_StartOrders.Name = "dateTimePicker_StartOrders";
			this.dateTimePicker_StartOrders.Size = new Size(163, 25);
			this.dateTimePicker_StartOrders.TabIndex = 0;
			this.label5.Anchor = AnchorStyles.Right;
			this.label5.AutoSize = true;
			this.label5.Location = new Point(258, 8);
			this.label5.Margin = new System.Windows.Forms.Padding(0);
			this.label5.Name = "label5";
			this.label5.Size = new Size(52, 15);
			this.label5.TabIndex = 2;
			this.label5.Text = "截止：";
			this.label5.BackColor = Color.Transparent;
			this.orderTabPg_Hist.AttachedControl = this.tabControlPanel_Orders_Hist;
			this.orderTabPg_Hist.Name = "orderTabPg_Hist";
			this.orderTabPg_Hist.Text = "历史记录";
			this.tabControlPanel_Orders_Curr.Dock = DockStyle.Fill;
			this.tabControlPanel_Orders_Curr.Location = new Point(0, 0);
			this.tabControlPanel_Orders_Curr.Name = "tabControlPanel_Orders_Curr";
			this.tabControlPanel_Orders_Curr.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_Orders_Curr.Size = new Size(769, 430);
			this.tabControlPanel_Orders_Curr.Style.BackColor1.Color = Color.FromArgb(253, 253, 254);
			this.tabControlPanel_Orders_Curr.Style.BackColor2.Color = Color.FromArgb(157, 188, 227);
			this.tabControlPanel_Orders_Curr.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_Orders_Curr.Style.BorderColor.Color = Color.Transparent;
			this.tabControlPanel_Orders_Curr.Style.BorderSide = (eBorderSide.Left | eBorderSide.Top | eBorderSide.Bottom);
			this.tabControlPanel_Orders_Curr.Style.GradientAngle = 180;
			this.tabControlPanel_Orders_Curr.TabIndex = 1;
			this.tabControlPanel_Orders_Curr.TabItem = this.orderTabPg_Curr;
			this.orderTabPg_Curr.AttachedControl = this.tabControlPanel_Orders_Curr;
			this.orderTabPg_Curr.Name = "orderTabPg_Curr";
			this.orderTabPg_Curr.Text = "当前记录";
			this.tabPg_Order.AttachedControl = this.tabControlPanel_Order;
			this.tabPg_Order.Name = "tabPg_Order";
			this.tabPg_Order.Text = "委托记录";
			this.tabControlPanel_Trans.AutoScroll = true;
			this.tabControlPanel_Trans.Controls.Add(this.tabCtrl_HisTrans);
			this.tabControlPanel_Trans.Dock = DockStyle.Fill;
			this.tabControlPanel_Trans.Location = new Point(0, 0);
			this.tabControlPanel_Trans.Name = "tabControlPanel_Trans";
			this.tabControlPanel_Trans.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_Trans.Size = new Size(798, 432);
			this.tabControlPanel_Trans.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_Trans.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_Trans.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_Trans.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_Trans.Style.GradientAngle = -90;
			this.tabControlPanel_Trans.TabIndex = 2;
			this.tabControlPanel_Trans.TabItem = this.tabPg_Trans;
			this.tabCtrl_HisTrans.CanReorderTabs = true;
			this.tabCtrl_HisTrans.ColorScheme.TabBorder = Color.Transparent;
			this.tabCtrl_HisTrans.ColorScheme.TabItemBackgroundColorBlend.AddRange(new BackgroundColorBlend[]
			{
				new BackgroundColorBlend(Color.FromArgb(215, 230, 249), 0f),
				new BackgroundColorBlend(Color.FromArgb(199, 220, 248), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(179, 208, 245), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(215, 229, 247), 1f)
			});
			this.tabCtrl_HisTrans.ColorScheme.TabItemBorder = Color.Transparent;
			this.tabCtrl_HisTrans.ColorScheme.TabItemHotBackgroundColorBlend.AddRange(new BackgroundColorBlend[]
			{
				new BackgroundColorBlend(Color.FromArgb(255, 253, 235), 0f),
				new BackgroundColorBlend(Color.FromArgb(255, 236, 168), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(255, 218, 89), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(255, 230, 141), 1f)
			});
			this.tabCtrl_HisTrans.ColorScheme.TabItemSelectedBackgroundColorBlend.AddRange(new BackgroundColorBlend[]
			{
				new BackgroundColorBlend(Color.White, 0f),
				new BackgroundColorBlend(Color.FromArgb(253, 253, 254), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(253, 253, 254), 0.45f),
				new BackgroundColorBlend(Color.FromArgb(253, 253, 254), 1f)
			});
			this.tabCtrl_HisTrans.ColorScheme.TabPanelBorder = Color.Transparent;
			this.tabCtrl_HisTrans.Controls.Add(this.tabControlPanel_Trans_Hist);
			this.tabCtrl_HisTrans.Controls.Add(this.tabControlPanel_Trans_Curr);
			this.tabCtrl_HisTrans.Dock = DockStyle.Fill;
			this.tabCtrl_HisTrans.Location = new Point(1, 1);
			this.tabCtrl_HisTrans.Name = "tabCtrl_HisTrans";
			this.tabCtrl_HisTrans.SelectedTabIndex = 0;
			this.tabCtrl_HisTrans.Size = new Size(796, 430);
			this.tabCtrl_HisTrans.Style = eTabStripStyle.Office2007Dock;
			this.tabCtrl_HisTrans.TabAlignment = eTabStripAlignment.Right;
			this.tabCtrl_HisTrans.TabIndex = 0;
			this.tabCtrl_HisTrans.TabLayoutType = eTabLayoutType.FixedWithNavigationBox;
			this.tabCtrl_HisTrans.Tabs.Add(this.transTabPg_Curr);
			this.tabCtrl_HisTrans.Tabs.Add(this.transTabPg_Hist);
			this.tabCtrl_HisTrans.Text = "tabControl1";
			this.tabCtrl_HisTrans.ThemeAware = true;
			this.tabControlPanel_Trans_Hist.Controls.Add(this.splitContainer_Trans_Hist);
			this.tabControlPanel_Trans_Hist.Dock = DockStyle.Fill;
			this.tabControlPanel_Trans_Hist.Location = new Point(0, 0);
			this.tabControlPanel_Trans_Hist.Name = "tabControlPanel_Trans_Hist";
			this.tabControlPanel_Trans_Hist.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_Trans_Hist.Size = new Size(769, 430);
			this.tabControlPanel_Trans_Hist.Style.BackColor1.Color = Color.FromArgb(253, 253, 254);
			this.tabControlPanel_Trans_Hist.Style.BackColor2.Color = Color.FromArgb(157, 188, 227);
			this.tabControlPanel_Trans_Hist.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_Trans_Hist.Style.BorderColor.Color = Color.Transparent;
			this.tabControlPanel_Trans_Hist.Style.BorderSide = (eBorderSide.Left | eBorderSide.Top | eBorderSide.Bottom);
			this.tabControlPanel_Trans_Hist.Style.GradientAngle = 180;
			this.tabControlPanel_Trans_Hist.TabIndex = 2;
			this.tabControlPanel_Trans_Hist.TabItem = this.transTabPg_Hist;
			this.splitContainer_Trans_Hist.Dock = DockStyle.Fill;
			this.splitContainer_Trans_Hist.FixedPanel = FixedPanel.Panel1;
			this.splitContainer_Trans_Hist.IsSplitterFixed = true;
			this.splitContainer_Trans_Hist.Location = new Point(1, 1);
			this.splitContainer_Trans_Hist.Name = "splitContainer_Trans_Hist";
			this.splitContainer_Trans_Hist.Orientation = Orientation.Horizontal;
			this.splitContainer_Trans_Hist.Panel1.BackColor = Color.Transparent;
			this.splitContainer_Trans_Hist.Panel1.Controls.Add(this.tblLayoutPanel_TransHistQry);
			this.splitContainer_Trans_Hist.Panel1MinSize = 28;
			this.splitContainer_Trans_Hist.Size = new Size(767, 428);
			this.splitContainer_Trans_Hist.SplitterDistance = 32;
			this.splitContainer_Trans_Hist.SplitterWidth = 1;
			this.splitContainer_Trans_Hist.TabIndex = 0;
			this.tblLayoutPanel_TransHistQry.ColumnCount = 6;
			this.tblLayoutPanel_TransHistQry.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 70f));
			this.tblLayoutPanel_TransHistQry.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 170f));
			this.tblLayoutPanel_TransHistQry.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 70f));
			this.tblLayoutPanel_TransHistQry.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 170f));
			this.tblLayoutPanel_TransHistQry.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80f));
			this.tblLayoutPanel_TransHistQry.ColumnStyles.Add(new ColumnStyle());
			this.tblLayoutPanel_TransHistQry.Controls.Add(this.btn_HisTransQuery, 4, 0);
			this.tblLayoutPanel_TransHistQry.Controls.Add(this.label_startDate, 0, 0);
			this.tblLayoutPanel_TransHistQry.Controls.Add(this.dateTimePicker_EndHisTrans, 3, 0);
			this.tblLayoutPanel_TransHistQry.Controls.Add(this.dateTimePicker_StartHisTrans, 1, 0);
			this.tblLayoutPanel_TransHistQry.Controls.Add(this.label_endDate, 2, 0);
			this.tblLayoutPanel_TransHistQry.Dock = DockStyle.Fill;
			this.tblLayoutPanel_TransHistQry.Location = new Point(0, 0);
			this.tblLayoutPanel_TransHistQry.Margin = new System.Windows.Forms.Padding(0);
			this.tblLayoutPanel_TransHistQry.Name = "tblLayoutPanel_TransHistQry";
			this.tblLayoutPanel_TransHistQry.RowCount = 1;
			this.tblLayoutPanel_TransHistQry.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
			this.tblLayoutPanel_TransHistQry.Size = new Size(767, 32);
			this.tblLayoutPanel_TransHistQry.TabIndex = 0;
			this.btn_HisTransQuery.Anchor = AnchorStyles.None;
			this.btn_HisTransQuery.BackColor = Color.Transparent;
			this.btn_HisTransQuery.Location = new Point(489, 3);
			this.btn_HisTransQuery.Margin = new System.Windows.Forms.Padding(0);
			this.btn_HisTransQuery.Name = "btn_HisTransQuery";
			this.btn_HisTransQuery.Size = new Size(62, 26);
			this.btn_HisTransQuery.TabIndex = 4;
			this.btn_HisTransQuery.Text = "查询";
			this.btn_HisTransQuery.UseVisualStyleBackColor = false;
			this.label_startDate.Anchor = AnchorStyles.Right;
			this.label_startDate.AutoSize = true;
			this.label_startDate.Location = new Point(18, 8);
			this.label_startDate.Margin = new System.Windows.Forms.Padding(0);
			this.label_startDate.Name = "label_startDate";
			this.label_startDate.Size = new Size(52, 15);
			this.label_startDate.TabIndex = 1;
			this.label_startDate.Text = "起始：";
			this.label_startDate.BackColor = Color.Transparent;
			this.dateTimePicker_EndHisTrans.Anchor = AnchorStyles.None;
			this.dateTimePicker_EndHisTrans.Location = new Point(313, 3);
			this.dateTimePicker_EndHisTrans.Margin = new System.Windows.Forms.Padding(0);
			this.dateTimePicker_EndHisTrans.Name = "dateTimePicker_EndHisTrans";
			this.dateTimePicker_EndHisTrans.Size = new Size(163, 25);
			this.dateTimePicker_EndHisTrans.TabIndex = 3;
			this.dateTimePicker_StartHisTrans.Anchor = AnchorStyles.None;
			this.dateTimePicker_StartHisTrans.Location = new Point(73, 3);
			this.dateTimePicker_StartHisTrans.Margin = new System.Windows.Forms.Padding(0);
			this.dateTimePicker_StartHisTrans.Name = "dateTimePicker_StartHisTrans";
			this.dateTimePicker_StartHisTrans.Size = new Size(163, 25);
			this.dateTimePicker_StartHisTrans.TabIndex = 0;
			this.label_endDate.Anchor = AnchorStyles.Right;
			this.label_endDate.AutoSize = true;
			this.label_endDate.Location = new Point(258, 8);
			this.label_endDate.Margin = new System.Windows.Forms.Padding(0);
			this.label_endDate.Name = "label_endDate";
			this.label_endDate.Size = new Size(52, 15);
			this.label_endDate.TabIndex = 2;
			this.label_endDate.Text = "截止：";
			this.label_endDate.BackColor = Color.Transparent;
			this.transTabPg_Hist.AttachedControl = this.tabControlPanel_Trans_Hist;
			this.transTabPg_Hist.Name = "transTabPg_Hist";
			this.transTabPg_Hist.Text = "历史记录";
			this.tabControlPanel_Trans_Curr.Dock = DockStyle.Fill;
			this.tabControlPanel_Trans_Curr.Location = new Point(0, 0);
			this.tabControlPanel_Trans_Curr.Name = "tabControlPanel_Trans_Curr";
			this.tabControlPanel_Trans_Curr.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_Trans_Curr.Size = new Size(769, 430);
			this.tabControlPanel_Trans_Curr.Style.BackColor1.Color = Color.FromArgb(253, 253, 254);
			this.tabControlPanel_Trans_Curr.Style.BackColor2.Color = Color.FromArgb(157, 188, 227);
			this.tabControlPanel_Trans_Curr.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_Trans_Curr.Style.BorderColor.Color = Color.Transparent;
			this.tabControlPanel_Trans_Curr.Style.BorderSide = (eBorderSide.Left | eBorderSide.Top | eBorderSide.Bottom);
			this.tabControlPanel_Trans_Curr.Style.GradientAngle = 180;
			this.tabControlPanel_Trans_Curr.TabIndex = 1;
			this.tabControlPanel_Trans_Curr.TabItem = this.transTabPg_Curr;
			this.transTabPg_Curr.AttachedControl = this.tabControlPanel_Trans_Curr;
			this.transTabPg_Curr.Name = "transTabPg_Curr";
			this.transTabPg_Curr.Text = "当前记录";
			this.tabPg_Trans.AttachedControl = this.tabControlPanel_Trans;
			this.tabPg_Trans.Name = "tabPg_Trans";
			this.tabPg_Trans.Text = "成交记录";
			this.tabControlPanel_OpenPos.Dock = DockStyle.Fill;
			this.tabControlPanel_OpenPos.Location = new Point(0, 0);
			this.tabControlPanel_OpenPos.Name = "tabControlPanel_OpenPos";
			this.tabControlPanel_OpenPos.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_OpenPos.Size = new Size(798, 432);
			this.tabControlPanel_OpenPos.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_OpenPos.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_OpenPos.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_OpenPos.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_OpenPos.Style.GradientAngle = -90;
			this.tabControlPanel_OpenPos.TabIndex = 1;
			this.tabControlPanel_OpenPos.TabItem = this.tabPg_OpenPos;
			this.tabPg_OpenPos.AttachedControl = this.tabControlPanel_OpenPos;
			this.tabPg_OpenPos.Name = "tabPg_OpenPos";
			this.tabPg_OpenPos.Text = "当前持仓 ";
			this.tabControlPanel_CondOrder.Dock = DockStyle.Fill;
			this.tabControlPanel_CondOrder.Location = new Point(0, 0);
			this.tabControlPanel_CondOrder.Name = "tabControlPanel_CondOrder";
			this.tabControlPanel_CondOrder.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_CondOrder.Size = new Size(798, 432);
			this.tabControlPanel_CondOrder.Style.BackColor1.Color = SystemColors.Control;
			this.tabControlPanel_CondOrder.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_CondOrder.Style.BorderColor.Color = SystemColors.ControlDark;
			this.tabControlPanel_CondOrder.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Top);
			this.tabControlPanel_CondOrder.Style.GradientAngle = -90;
			this.tabControlPanel_CondOrder.TabIndex = 1;
			this.tabControlPanel_CondOrder.TabItem = this.tabPg_CondOrder;
			this.tabPg_CondOrder.AttachedControl = this.tabControlPanel_CondOrder;
			this.tabPg_CondOrder.Name = "tabPg_CondOrder";
			this.tabPg_CondOrder.Text = "条件单";
			this.expSplitter_Trade.BackColor = SystemColors.ControlLight;
			this.expSplitter_Trade.BackColor2 = Color.Empty;
			this.expSplitter_Trade.BackColor2SchemePart = eColorSchemePart.None;
			this.expSplitter_Trade.BackColorSchemePart = eColorSchemePart.None;
			this.expSplitter_Trade.ExpandableControl = this.panel_Trading;
			this.expSplitter_Trade.ExpandFillColor = Color.FromArgb(254, 142, 75);
			this.expSplitter_Trade.ExpandFillColorSchemePart = eColorSchemePart.ItemPressedBackground;
			this.expSplitter_Trade.ExpandLineColor = Color.FromArgb(0, 0, 128);
			this.expSplitter_Trade.ExpandLineColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expSplitter_Trade.GripDarkColor = Color.FromArgb(0, 0, 128);
			this.expSplitter_Trade.GripDarkColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expSplitter_Trade.GripLightColor = Color.FromArgb(246, 246, 246);
			this.expSplitter_Trade.GripLightColorSchemePart = eColorSchemePart.MenuBackground;
			this.expSplitter_Trade.HotBackColor = Color.FromArgb(255, 213, 140);
			this.expSplitter_Trade.HotBackColor2 = Color.Empty;
			this.expSplitter_Trade.HotBackColor2SchemePart = eColorSchemePart.None;
			this.expSplitter_Trade.HotBackColorSchemePart = eColorSchemePart.ItemCheckedBackground;
			this.expSplitter_Trade.HotExpandFillColor = Color.FromArgb(254, 142, 75);
			this.expSplitter_Trade.HotExpandFillColorSchemePart = eColorSchemePart.ItemPressedBackground;
			this.expSplitter_Trade.HotExpandLineColor = Color.FromArgb(0, 0, 128);
			this.expSplitter_Trade.HotExpandLineColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expSplitter_Trade.HotGripDarkColor = Color.FromArgb(0, 0, 128);
			this.expSplitter_Trade.HotGripDarkColorSchemePart = eColorSchemePart.ItemPressedBorder;
			this.expSplitter_Trade.HotGripLightColor = Color.FromArgb(246, 246, 246);
			this.expSplitter_Trade.HotGripLightColorSchemePart = eColorSchemePart.MenuBackground;
			this.expSplitter_Trade.Location = new Point(313, 1);
			this.expSplitter_Trade.Name = "expSplitter_Trade";
			this.expSplitter_Trade.Size = new Size(4, 496);
			this.expSplitter_Trade.Style = eSplitterStyle.Mozilla;
			this.expSplitter_Trade.TabIndex = 1;
			this.expSplitter_Trade.TabStop = false;
			this.panel_Trading.Controls.Add(this.comboBox_TradingSymbs);
			this.panel_Trading.Controls.Add(this.label_PosRatio);
			this.panel_Trading.Controls.Add(this.label_仓位);
			this.panel_Trading.Controls.Add(this.label_usableBal);
			this.panel_Trading.Controls.Add(this.label40);
			this.panel_Trading.Controls.Add(this.chkBox_AutoOpenClose);
			this.panel_Trading.Controls.Add(this.panel_OpenClose);
			this.panel_Trading.Controls.Add(this.chkBox_FollowPrc);
			this.panel_Trading.Controls.Add(this.btn_CondOrder);
			this.panel_Trading.Controls.Add(this.btn_TOpenShrt);
			this.panel_Trading.Controls.Add(this.btn_TOpenLong);
			this.panel_Trading.Controls.Add(this.panel_PercRadioBtns);
			this.panel_Trading.Controls.Add(this.numericUpDown_TPrice);
			this.panel_Trading.Controls.Add(this.label_价格);
			this.panel_Trading.Controls.Add(this.label_maxPosUnits);
			this.panel_Trading.Controls.Add(this.numericUpDown_TUnits);
			this.panel_Trading.Controls.Add(this.label_数量);
			this.panel_Trading.Controls.Add(this.label_品种);
			this.panel_Trading.Dock = DockStyle.Left;
			this.panel_Trading.Location = new Point(1, 1);
			this.panel_Trading.Name = "panel_Trading";
			this.panel_Trading.Size = new Size(312, 496);
			this.panel_Trading.TabIndex = 0;
			this.comboBox_TradingSymbs.DropDownStyle = ComboBoxStyle.DropDownList;
			this.comboBox_TradingSymbs.FormattingEnabled = true;
			this.comboBox_TradingSymbs.Location = new Point(58, 16);
			this.comboBox_TradingSymbs.MaxDropDownItems = 80;
			this.comboBox_TradingSymbs.Name = "comboBox_TradingSymbs";
			this.comboBox_TradingSymbs.Size = new Size(152, 23);
			this.comboBox_TradingSymbs.TabIndex = 21;
			this.label_PosRatio.AutoSize = true;
			this.label_PosRatio.Font = new Font("Microsoft Sans Serif", 8f);
			this.label_PosRatio.Location = new Point(246, 224);
			this.label_PosRatio.Name = "label_PosRatio";
			this.label_PosRatio.Size = new Size(36, 17);
			this.label_PosRatio.TabIndex = 20;
			this.label_PosRatio.Text = "30%";
			this.label_PosRatio.Visible = false;
			this.label_仓位.AutoSize = true;
			this.label_仓位.Location = new Point(199, 225);
			this.label_仓位.Name = "label_仓位";
			this.label_仓位.Size = new Size(52, 15);
			this.label_仓位.TabIndex = 19;
			this.label_仓位.Text = "仓位：";
			this.label_仓位.Visible = false;
			this.label_usableBal.AutoSize = true;
			this.label_usableBal.Font = new Font("Microsoft Sans Serif", 8f);
			this.label_usableBal.Location = new Point(91, 224);
			this.label_usableBal.Name = "label_usableBal";
			this.label_usableBal.Size = new Size(56, 17);
			this.label_usableBal.TabIndex = 18;
			this.label_usableBal.Text = "250000";
			this.label_usableBal.Visible = false;
			this.label40.AutoSize = true;
			this.label40.Location = new Point(12, 225);
			this.label40.Name = "label40";
			this.label40.Size = new Size(82, 15);
			this.label40.TabIndex = 17;
			this.label40.Text = "可用资金：";
			this.label40.Visible = false;
			this.chkBox_AutoOpenClose.AutoSize = true;
			this.chkBox_AutoOpenClose.FlatStyle = FlatStyle.Popup;
			this.chkBox_AutoOpenClose.Location = new Point(77, 174);
			this.chkBox_AutoOpenClose.Name = "chkBox_AutoOpenClose";
			this.chkBox_AutoOpenClose.Size = new Size(56, 19);
			this.chkBox_AutoOpenClose.TabIndex = 15;
			this.chkBox_AutoOpenClose.Text = "自动";
			this.chkBox_AutoOpenClose.UseVisualStyleBackColor = false;
			this.panel_OpenClose.Controls.Add(this.radioBtn_Close);
			this.panel_OpenClose.Controls.Add(this.radioBtn_Open);
			this.panel_OpenClose.Location = new Point(11, 159);
			this.panel_OpenClose.Name = "panel_OpenClose";
			this.panel_OpenClose.Size = new Size(64, 48);
			this.panel_OpenClose.TabIndex = 14;
			this.radioBtn_Close.AutoSize = true;
			this.radioBtn_Close.FlatStyle = FlatStyle.Popup;
			this.radioBtn_Close.Location = new Point(4, 27);
			this.radioBtn_Close.Name = "radioBtn_Close";
			this.radioBtn_Close.Size = new Size(57, 19);
			this.radioBtn_Close.TabIndex = 1;
			this.radioBtn_Close.TabStop = true;
			this.radioBtn_Close.Text = "平仓";
			this.radioBtn_Close.UseVisualStyleBackColor = false;
			this.radioBtn_Open.AutoSize = true;
			this.radioBtn_Open.FlatStyle = FlatStyle.Popup;
			this.radioBtn_Open.Location = new Point(4, 5);
			this.radioBtn_Open.Name = "radioBtn_Open";
			this.radioBtn_Open.Size = new Size(57, 19);
			this.radioBtn_Open.TabIndex = 0;
			this.radioBtn_Open.TabStop = true;
			this.radioBtn_Open.Text = "开仓";
			this.radioBtn_Open.UseVisualStyleBackColor = false;
			this.chkBox_FollowPrc.AutoSize = true;
			this.chkBox_FollowPrc.BackColor = Color.Transparent;
			this.chkBox_FollowPrc.FlatStyle = FlatStyle.Popup;
			this.chkBox_FollowPrc.Location = new Point(228, 52);
			this.chkBox_FollowPrc.Name = "chkBox_FollowPrc";
			this.chkBox_FollowPrc.Size = new Size(56, 19);
			this.chkBox_FollowPrc.TabIndex = 13;
			this.chkBox_FollowPrc.Text = "跟盘";
			this.chkBox_FollowPrc.UseVisualStyleBackColor = false;
			this.btn_CondOrder.BackColor = Color.Transparent;
			this.btn_CondOrder.Location = new Point(216, 14);
			this.btn_CondOrder.Name = "btn_CondOrder";
			this.btn_CondOrder.Size = new Size(80, 25);
			this.btn_CondOrder.TabIndex = 11;
			this.btn_CondOrder.Text = "条件单";
			this.btn_CondOrder.UseVisualStyleBackColor = false;
			this.btn_TOpenShrt.BackColor = Color.Transparent;
			this.btn_TOpenShrt.Font = new Font("SimHei", 13f);
			this.btn_TOpenShrt.ForeColor = Color.Green;
			this.btn_TOpenShrt.Location = new Point(222, 159);
			this.btn_TOpenShrt.Name = "btn_TOpenShrt";
			this.btn_TOpenShrt.Size = new Size(76, 48);
			this.btn_TOpenShrt.TabIndex = 9;
			this.btn_TOpenShrt.Text = "卖出";
			this.btn_TOpenShrt.UseVisualStyleBackColor = false;
			this.btn_TOpenLong.BackColor = Color.Transparent;
			this.btn_TOpenLong.Font = new Font("SimHei", 13f);
			this.btn_TOpenLong.ForeColor = Color.Red;
			this.btn_TOpenLong.Location = new Point(141, 159);
			this.btn_TOpenLong.Name = "btn_TOpenLong";
			this.btn_TOpenLong.Size = new Size(76, 48);
			this.btn_TOpenLong.TabIndex = 8;
			this.btn_TOpenLong.Text = "买入";
			this.btn_TOpenLong.UseVisualStyleBackColor = false;
			this.panel_PercRadioBtns.Controls.Add(this.radioBtn_100Pers);
			this.panel_PercRadioBtns.Controls.Add(this.radioBtn_80Pers);
			this.panel_PercRadioBtns.Controls.Add(this.radioBtn_50Pers);
			this.panel_PercRadioBtns.Controls.Add(this.radioBtn_30Pers);
			this.panel_PercRadioBtns.Controls.Add(this.radioBtn_15Pers);
			this.panel_PercRadioBtns.Font = new Font("Arial Narrow", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.panel_PercRadioBtns.Location = new Point(14, 111);
			this.panel_PercRadioBtns.Name = "panel_PercRadioBtns";
			this.panel_PercRadioBtns.Size = new Size(285, 28);
			this.panel_PercRadioBtns.TabIndex = 7;
			this.radioBtn_100Pers.AutoSize = true;
			this.radioBtn_100Pers.BackColor = Color.Transparent;
			this.radioBtn_100Pers.FlatStyle = FlatStyle.Popup;
			this.radioBtn_100Pers.Font = new Font("Microsoft Sans Serif", 7.8f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.radioBtn_100Pers.Location = new Point(218, 5);
			this.radioBtn_100Pers.Name = "radioBtn_100Pers";
			this.radioBtn_100Pers.Size = new Size(64, 21);
			this.radioBtn_100Pers.TabIndex = 9;
			this.radioBtn_100Pers.TabStop = true;
			this.radioBtn_100Pers.Text = "100%";
			this.radioBtn_100Pers.UseVisualStyleBackColor = false;
			this.radioBtn_80Pers.AutoSize = true;
			this.radioBtn_80Pers.BackColor = Color.Transparent;
			this.radioBtn_80Pers.FlatStyle = FlatStyle.Popup;
			this.radioBtn_80Pers.Font = new Font("Microsoft Sans Serif", 7.8f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.radioBtn_80Pers.Location = new Point(165, 5);
			this.radioBtn_80Pers.Name = "radioBtn_80Pers";
			this.radioBtn_80Pers.Size = new Size(56, 21);
			this.radioBtn_80Pers.TabIndex = 8;
			this.radioBtn_80Pers.TabStop = true;
			this.radioBtn_80Pers.Text = "80%";
			this.radioBtn_80Pers.UseVisualStyleBackColor = false;
			this.radioBtn_50Pers.AutoSize = true;
			this.radioBtn_50Pers.BackColor = Color.Transparent;
			this.radioBtn_50Pers.FlatStyle = FlatStyle.Popup;
			this.radioBtn_50Pers.Font = new Font("Microsoft Sans Serif", 7.8f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.radioBtn_50Pers.Location = new Point(111, 5);
			this.radioBtn_50Pers.Name = "radioBtn_50Pers";
			this.radioBtn_50Pers.Size = new Size(56, 21);
			this.radioBtn_50Pers.TabIndex = 2;
			this.radioBtn_50Pers.TabStop = true;
			this.radioBtn_50Pers.Text = "50%";
			this.radioBtn_50Pers.UseVisualStyleBackColor = false;
			this.radioBtn_30Pers.AutoSize = true;
			this.radioBtn_30Pers.BackColor = Color.Transparent;
			this.radioBtn_30Pers.FlatStyle = FlatStyle.Popup;
			this.radioBtn_30Pers.Font = new Font("Microsoft Sans Serif", 7.8f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.radioBtn_30Pers.Location = new Point(55, 5);
			this.radioBtn_30Pers.Name = "radioBtn_30Pers";
			this.radioBtn_30Pers.Size = new Size(56, 21);
			this.radioBtn_30Pers.TabIndex = 1;
			this.radioBtn_30Pers.TabStop = true;
			this.radioBtn_30Pers.Text = "30%";
			this.radioBtn_30Pers.UseVisualStyleBackColor = false;
			this.radioBtn_15Pers.AutoSize = true;
			this.radioBtn_15Pers.BackColor = Color.Transparent;
			this.radioBtn_15Pers.FlatStyle = FlatStyle.Popup;
			this.radioBtn_15Pers.Font = new Font("Microsoft Sans Serif", 7.8f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.radioBtn_15Pers.Location = new Point(1, 5);
			this.radioBtn_15Pers.Name = "radioBtn_15Pers";
			this.radioBtn_15Pers.Size = new Size(56, 21);
			this.radioBtn_15Pers.TabIndex = 0;
			this.radioBtn_15Pers.TabStop = true;
			this.radioBtn_15Pers.Text = "15%";
			this.radioBtn_15Pers.UseVisualStyleBackColor = false;
			this.numericUpDown_TPrice.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.numericUpDown_TPrice.Location = new Point(58, 48);
			this.numericUpDown_TPrice.Name = "numericUpDown_TPrice";
			this.numericUpDown_TPrice.Size = new Size(152, 24);
			this.numericUpDown_TPrice.TabIndex = 6;
			this.label_价格.AutoSize = true;
			this.label_价格.BackColor = Color.Transparent;
			this.label_价格.Location = new Point(11, 53);
			this.label_价格.Name = "label_价格";
			this.label_价格.Size = new Size(37, 15);
			this.label_价格.TabIndex = 5;
			this.label_价格.Text = "价格";
			this.label_maxPosUnits.AutoSize = true;
			this.label_maxPosUnits.BackColor = Color.Transparent;
			this.label_maxPosUnits.Font = new Font("Microsoft Sans Serif", 8f);
			this.label_maxPosUnits.Location = new Point(225, 84);
			this.label_maxPosUnits.Name = "label_maxPosUnits";
			this.label_maxPosUnits.Size = new Size(40, 17);
			this.label_maxPosUnits.TabIndex = 4;
			this.label_maxPosUnits.Text = "<=25";
			this.numericUpDown_TUnits.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			this.numericUpDown_TUnits.Location = new Point(58, 81);
			this.numericUpDown_TUnits.Name = "numericUpDown_TUnits";
			this.numericUpDown_TUnits.Size = new Size(152, 24);
			this.numericUpDown_TUnits.TabIndex = 3;
			this.label_数量.AutoSize = true;
			this.label_数量.BackColor = Color.Transparent;
			this.label_数量.Location = new Point(11, 85);
			this.label_数量.Name = "label_数量";
			this.label_数量.Size = new Size(37, 15);
			this.label_数量.TabIndex = 1;
			this.label_数量.Text = "数量";
			this.label_品种.AutoSize = true;
			this.label_品种.BackColor = Color.Transparent;
			this.label_品种.Location = new Point(11, 20);
			this.label_品种.Name = "label_品种";
			this.label_品种.Size = new Size(37, 15);
			this.label_品种.TabIndex = 0;
			this.label_品种.Text = "品种";
			this.tabPg_Below_Trade.AttachedControl = this.tabControlPanel_Trading;
			this.tabPg_Below_Trade.Name = "tabPg_Below_Trade";
			this.tabPg_Below_Trade.Text = "模拟交易";
			this.tabControlPanel_MktSymbs.Dock = DockStyle.Fill;
			this.tabControlPanel_MktSymbs.Location = new Point(0, 30);
			this.tabControlPanel_MktSymbs.Name = "tabControlPanel_MktSymbs";
			this.tabControlPanel_MktSymbs.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_MktSymbs.Size = new Size(1116, 498);
			this.tabControlPanel_MktSymbs.Style.BackColor1.Color = Color.FromArgb(227, 239, 255);
			this.tabControlPanel_MktSymbs.Style.BackColor2.Color = Color.FromArgb(176, 210, 255);
			this.tabControlPanel_MktSymbs.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_MktSymbs.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Bottom);
			this.tabControlPanel_MktSymbs.Style.GradientAngle = 90;
			this.tabControlPanel_MktSymbs.TabIndex = 2;
			this.tabControlPanel_MktSymbs.TabItem = this.tabPg_Below_Symbs;
			this.tabPg_Below_Symbs.AttachedControl = this.tabControlPanel_MktSymbs;
			this.tabPg_Below_Symbs.Name = "tabPg_Below_Symbs";
			this.tabPg_Below_Symbs.Text = "市场品种";
			this.tabControlPanel_Below_Acct.Dock = DockStyle.Fill;
			this.tabControlPanel_Below_Acct.Location = new Point(0, 30);
			this.tabControlPanel_Below_Acct.Name = "tabControlPanel_Below_Acct";
			this.tabControlPanel_Below_Acct.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_Below_Acct.Size = new Size(1116, 498);
			this.tabControlPanel_Below_Acct.Style.BackColor1.Color = Color.FromArgb(227, 239, 255);
			this.tabControlPanel_Below_Acct.Style.BackColor2.Color = Color.FromArgb(176, 210, 255);
			this.tabControlPanel_Below_Acct.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_Below_Acct.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Bottom);
			this.tabControlPanel_Below_Acct.Style.GradientAngle = 90;
			this.tabControlPanel_Below_Acct.TabIndex = 1;
			this.tabControlPanel_Below_Acct.TabItem = this.tabPg_Below_Acct;
			this.tabPg_Below_Acct.AttachedControl = this.tabControlPanel_Below_Acct;
			this.tabPg_Below_Acct.Name = "tabPg_Below_Acct";
			this.tabPg_Below_Acct.Text = "交易分析";
			this.tabControlPanel_SelSymb.Controls.Add(this.symbFilterPanel);
			this.tabControlPanel_SelSymb.Dock = DockStyle.Fill;
			this.tabControlPanel_SelSymb.Location = new Point(0, 30);
			this.tabControlPanel_SelSymb.Name = "tabControlPanel_SelSymb";
			this.tabControlPanel_SelSymb.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_SelSymb.Size = new Size(1116, 498);
			this.tabControlPanel_SelSymb.Style.BackColor1.Color = Color.FromArgb(227, 239, 255);
			this.tabControlPanel_SelSymb.Style.BackColor2.Color = Color.FromArgb(176, 210, 255);
			this.tabControlPanel_SelSymb.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_SelSymb.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Bottom);
			this.tabControlPanel_SelSymb.Style.GradientAngle = 90;
			this.tabControlPanel_SelSymb.TabIndex = 6;
			this.tabControlPanel_SelSymb.TabItem = this.tabPg_SelSymb;
			this.symbFilterPanel.CurrUserCfgCondGrpName = null;
			this.symbFilterPanel.Dock = DockStyle.Fill;
			this.symbFilterPanel.Location = new Point(1, 1);
			this.symbFilterPanel.Name = "symbFilterPanel";
			this.symbFilterPanel.Size = new Size(1114, 496);
			this.symbFilterPanel.TabIndex = 0;
			this.tabPg_SelSymb.AttachedControl = this.tabControlPanel_SelSymb;
			this.tabPg_SelSymb.Name = "tabPg_SelSymb";
			this.tabPg_SelSymb.Text = "条件选股";
			this.tabControlPanel_Video.Dock = DockStyle.Fill;
			this.tabControlPanel_Video.Location = new Point(0, 30);
			this.tabControlPanel_Video.Name = "tabControlPanel_Video";
			this.tabControlPanel_Video.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_Video.Size = new Size(1116, 498);
			this.tabControlPanel_Video.Style.BackColor1.Color = Color.FromArgb(227, 239, 255);
			this.tabControlPanel_Video.Style.BackColor2.Color = Color.FromArgb(176, 210, 255);
			this.tabControlPanel_Video.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_Video.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Bottom);
			this.tabControlPanel_Video.Style.GradientAngle = 90;
			this.tabControlPanel_Video.TabIndex = 7;
			this.tabControlPanel_Video.TabItem = this.tabPg_Video;
			this.tabPg_Video.AttachedControl = this.tabControlPanel_Video;
			this.tabPg_Video.Name = "tabPg_Video";
			this.tabPg_Video.Text = "视频讲座";
			this.tabControlPanel_FnRpt.Controls.Add(this.fnRptAnlysPanel);
			this.tabControlPanel_FnRpt.Dock = DockStyle.Fill;
			this.tabControlPanel_FnRpt.Location = new Point(0, 30);
			this.tabControlPanel_FnRpt.Name = "tabControlPanel_FnRpt";
			this.tabControlPanel_FnRpt.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_FnRpt.Size = new Size(1116, 498);
			this.tabControlPanel_FnRpt.Style.BackColor1.Color = Color.FromArgb(227, 239, 255);
			this.tabControlPanel_FnRpt.Style.BackColor2.Color = Color.FromArgb(176, 210, 255);
			this.tabControlPanel_FnRpt.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_FnRpt.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Bottom);
			this.tabControlPanel_FnRpt.Style.GradientAngle = 90;
			this.tabControlPanel_FnRpt.TabIndex = 5;
			this.tabControlPanel_FnRpt.TabItem = this.tabPg_FnRpt;
			this.fnRptAnlysPanel.Dock = DockStyle.Fill;
			this.fnRptAnlysPanel.Location = new Point(1, 1);
			this.fnRptAnlysPanel.Name = "fnRptAnlysPanel";
			this.fnRptAnlysPanel.Size = new Size(1114, 496);
			this.fnRptAnlysPanel.TabIndex = 0;
			this.tabPg_FnRpt.AttachedControl = this.tabControlPanel_FnRpt;
			this.tabPg_FnRpt.Name = "tabPg_FnRpt";
			this.tabPg_FnRpt.Text = "财报分析";
			this.tabControlPanel_BaoDian_Below.Controls.Add(this.baoDianPanel);
			this.tabControlPanel_BaoDian_Below.Dock = DockStyle.Fill;
			this.tabControlPanel_BaoDian_Below.Location = new Point(0, 30);
			this.tabControlPanel_BaoDian_Below.Name = "tabControlPanel_BaoDian_Below";
			this.tabControlPanel_BaoDian_Below.Padding = new System.Windows.Forms.Padding(1);
			this.tabControlPanel_BaoDian_Below.Size = new Size(1116, 498);
			this.tabControlPanel_BaoDian_Below.Style.BackColor1.Color = Color.FromArgb(227, 239, 255);
			this.tabControlPanel_BaoDian_Below.Style.BackColor2.Color = Color.FromArgb(176, 210, 255);
			this.tabControlPanel_BaoDian_Below.Style.Border = eBorderType.SingleLine;
			this.tabControlPanel_BaoDian_Below.Style.BorderSide = (eBorderSide.Left | eBorderSide.Right | eBorderSide.Bottom);
			this.tabControlPanel_BaoDian_Below.Style.GradientAngle = 90;
			this.tabControlPanel_BaoDian_Below.TabIndex = 4;
			this.tabControlPanel_BaoDian_Below.TabItem = this.tabPg_BaoDian;
			this.baoDianPanel.Dock = DockStyle.Fill;
			this.baoDianPanel.Location = new Point(1, 1);
			this.baoDianPanel.Name = "baoDianPanel";
			this.baoDianPanel.Size = new Size(1114, 496);
			this.baoDianPanel.TabIndex = 0;
			this.tabPg_BaoDian.AttachedControl = this.tabControlPanel_BaoDian_Below;
			this.tabPg_BaoDian.Name = "tabPg_BaoDian";
			this.tabPg_BaoDian.Text = "交易宝典";
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.tabControl_Below);
			base.Name = "TransTabs";
			base.Size = new Size(1116, 528);
			((ISupportInitialize)this.tabControl_Below).EndInit();
			this.tabControl_Below.ResumeLayout(false);
			this.tabControlPanel_Trading.ResumeLayout(false);
			this.panel_Trans.ResumeLayout(false);
			this.splitContainer_TransTabsBelow.Panel1.ResumeLayout(false);
			this.splitContainer_TransTabsBelow.Panel2.ResumeLayout(false);
			this.splitContainer_TransTabsBelow.ResumeLayout(false);
			this.tblLayoutPanel_TrdBtns.ResumeLayout(false);
			this.tblLayoutPanel_TrdBtns.PerformLayout();
			((ISupportInitialize)this.tabCtrl_Trans).EndInit();
			this.tabCtrl_Trans.ResumeLayout(false);
			this.tabControlPanel_Order.ResumeLayout(false);
			((ISupportInitialize)this.tabControl_Orders).EndInit();
			this.tabControl_Orders.ResumeLayout(false);
			this.tabControlPanel_Orders_Hist.ResumeLayout(false);
			this.splitContainer_Orders_Hist.Panel1.ResumeLayout(false);
			this.splitContainer_Orders_Hist.ResumeLayout(false);
			this.tblLayoutPanel_OdrHistQry.ResumeLayout(false);
			this.tblLayoutPanel_OdrHistQry.PerformLayout();
			this.tabControlPanel_Trans.ResumeLayout(false);
			((ISupportInitialize)this.tabCtrl_HisTrans).EndInit();
			this.tabCtrl_HisTrans.ResumeLayout(false);
			this.tabControlPanel_Trans_Hist.ResumeLayout(false);
			this.splitContainer_Trans_Hist.Panel1.ResumeLayout(false);
			this.splitContainer_Trans_Hist.ResumeLayout(false);
			this.tblLayoutPanel_TransHistQry.ResumeLayout(false);
			this.tblLayoutPanel_TransHistQry.PerformLayout();
			this.panel_Trading.ResumeLayout(false);
			this.panel_Trading.PerformLayout();
			this.panel_OpenClose.ResumeLayout(false);
			this.panel_OpenClose.PerformLayout();
			this.panel_PercRadioBtns.ResumeLayout(false);
			this.panel_PercRadioBtns.PerformLayout();
			((ISupportInitialize)this.numericUpDown_TPrice).EndInit();
			((ISupportInitialize)this.numericUpDown_TUnits).EndInit();
			this.tabControlPanel_SelSymb.ResumeLayout(false);
			this.tabControlPanel_FnRpt.ResumeLayout(false);
			this.tabControlPanel_BaoDian_Below.ResumeLayout(false);
			base.ResumeLayout(false);
		}

		// Token: 0x04000CCA RID: 3274
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000CCB RID: 3275
		[CompilerGenerated]
		private EventHandler eventHandler_1;

		// Token: 0x04000CCC RID: 3276
		[CompilerGenerated]
		private EventHandler eventHandler_2;

		// Token: 0x04000CCD RID: 3277
		[CompilerGenerated]
		private EventHandler eventHandler_3;

		// Token: 0x04000CCE RID: 3278
		[CompilerGenerated]
		private Delegate22 delegate22_0;

		// Token: 0x04000CCF RID: 3279
		[CompilerGenerated]
		private MsgEventHandler msgEventHandler_0;

		// Token: 0x04000CD0 RID: 3280
		private DataGridViewOpenTrans dataGridViewOpenTrans_0;

		// Token: 0x04000CD1 RID: 3281
		private DataGridViewHisTrans dataGridViewHisTrans_0;

		// Token: 0x04000CD2 RID: 3282
		private DataGridViewHisTrans dataGridViewHisTrans_1;

		// Token: 0x04000CD3 RID: 3283
		private Class291 class291_0;

		// Token: 0x04000CD4 RID: 3284
		private Class291 class291_1;

		// Token: 0x04000CD5 RID: 3285
		private Class292 class292_0;

		// Token: 0x04000CD6 RID: 3286
		private static string string_0 = TApp.UserAcctFolder + "\\smktsymb.dat";

		// Token: 0x04000CD7 RID: 3287
		private SplitterPanel splitterPanel_0;

		// Token: 0x04000CD8 RID: 3288
		private SplitterPanel splitterPanel_1;

		// Token: 0x04000CD9 RID: 3289
		private Class56 class56_0;

		// Token: 0x04000CDA RID: 3290
		private TransTabCtrl transTabCtrl_0;

		// Token: 0x04000CDB RID: 3291
		private bool bool_0;

		// Token: 0x04000CDC RID: 3292
		private int int_0;

		// Token: 0x04000CDD RID: 3293
		private DevComponents.DotNetBar.TabControl tabControl_0;

		// Token: 0x04000CDE RID: 3294
		private List<DataGridViewMkt> list_0;

		// Token: 0x04000CDF RID: 3295
		private FnDataApiWorker fnDataApiWorker_0;

		// Token: 0x04000CE0 RID: 3296
		private Font font_0;

		// Token: 0x04000CE1 RID: 3297
		private Font font_1;

		// Token: 0x04000CE2 RID: 3298
		private TrdAnalysisPanel trdAnalysisPanel_0;

		// Token: 0x04000CE3 RID: 3299
		private Class323 class323_0;

		// Token: 0x04000CE4 RID: 3300
		private List<ShowMktSymb> list_1;

		// Token: 0x04000CE5 RID: 3301
		private List<ShowMktSymb> list_2;

		// Token: 0x04000CE6 RID: 3302
		private List<KeyValuePair<DataGridView, int>> list_3;

		// Token: 0x04000CE7 RID: 3303
		private DataGridViewMkt dataGridViewMkt_0;

		// Token: 0x04000CE8 RID: 3304
		private Rectangle rectangle_0;

		// Token: 0x04000CE9 RID: 3305
		private int int_1;

		// Token: 0x04000CEA RID: 3306
		private int int_2 = -1;

		// Token: 0x04000CEB RID: 3307
		[CompilerGenerated]
		private bool bool_1;

		// Token: 0x04000CEC RID: 3308
		private IContainer icontainer_0;

		// Token: 0x04000CED RID: 3309
		private DevComponents.DotNetBar.TabControl tabControl_Below;

		// Token: 0x04000CEE RID: 3310
		private TabControlPanel tabControlPanel_Below_Acct;

		// Token: 0x04000CEF RID: 3311
		private TabControlPanel tabControlPanel_Trans;

		// Token: 0x04000CF0 RID: 3312
		private DevComponents.DotNetBar.TabControl tabCtrl_HisTrans;

		// Token: 0x04000CF1 RID: 3313
		private TabControlPanel tabControlPanel_Trans_Curr;

		// Token: 0x04000CF2 RID: 3314
		private TabItem transTabPg_Curr;

		// Token: 0x04000CF3 RID: 3315
		private TabControlPanel tabControlPanel_Trans_Hist;

		// Token: 0x04000CF4 RID: 3316
		private SplitContainer splitContainer_Trans_Hist;

		// Token: 0x04000CF5 RID: 3317
		private Button btn_HisTransQuery;

		// Token: 0x04000CF6 RID: 3318
		private DateTimePicker dateTimePicker_EndHisTrans;

		// Token: 0x04000CF7 RID: 3319
		private Label label_endDate;

		// Token: 0x04000CF8 RID: 3320
		private Label label_startDate;

		// Token: 0x04000CF9 RID: 3321
		private DateTimePicker dateTimePicker_StartHisTrans;

		// Token: 0x04000CFA RID: 3322
		private TabItem transTabPg_Hist;

		// Token: 0x04000CFB RID: 3323
		private TabItem tabPg_Trans;

		// Token: 0x04000CFC RID: 3324
		private TabControlPanel tabControlPanel_OpenPos;

		// Token: 0x04000CFD RID: 3325
		private TabItem tabPg_OpenPos;

		// Token: 0x04000CFE RID: 3326
		private TabControlPanel tabControlPanel_Order;

		// Token: 0x04000CFF RID: 3327
		private DevComponents.DotNetBar.TabControl tabControl_Orders;

		// Token: 0x04000D00 RID: 3328
		private TabControlPanel tabControlPanel_Orders_Curr;

		// Token: 0x04000D01 RID: 3329
		private TabItem orderTabPg_Curr;

		// Token: 0x04000D02 RID: 3330
		private TabControlPanel tabControlPanel_Orders_Hist;

		// Token: 0x04000D03 RID: 3331
		private SplitContainer splitContainer_Orders_Hist;

		// Token: 0x04000D04 RID: 3332
		private Button btn_OrderQuery;

		// Token: 0x04000D05 RID: 3333
		private DateTimePicker dateTimePicker_EndOrders;

		// Token: 0x04000D06 RID: 3334
		private Label label5;

		// Token: 0x04000D07 RID: 3335
		private Label label36;

		// Token: 0x04000D08 RID: 3336
		private DateTimePicker dateTimePicker_StartOrders;

		// Token: 0x04000D09 RID: 3337
		private TabItem orderTabPg_Hist;

		// Token: 0x04000D0A RID: 3338
		private TabItem tabPg_Order;

		// Token: 0x04000D0B RID: 3339
		private TabItem tabPg_Below_Acct;

		// Token: 0x04000D0C RID: 3340
		private TabControlPanel tabControlPanel_MktSymbs;

		// Token: 0x04000D0D RID: 3341
		private TabItem tabPg_Below_Symbs;

		// Token: 0x04000D0E RID: 3342
		private TabControlPanel tabControlPanel_Trading;

		// Token: 0x04000D0F RID: 3343
		private TabItem tabPg_Below_Trade;

		// Token: 0x04000D10 RID: 3344
		private Panel panel_Trans;

		// Token: 0x04000D11 RID: 3345
		private ExpandableSplitter expSplitter_Trade;

		// Token: 0x04000D12 RID: 3346
		private Panel panel_Trading;

		// Token: 0x04000D13 RID: 3347
		private Label label_maxPosUnits;

		// Token: 0x04000D14 RID: 3348
		private NumericUpDown numericUpDown_TUnits;

		// Token: 0x04000D15 RID: 3349
		private Label label_数量;

		// Token: 0x04000D16 RID: 3350
		private Label label_品种;

		// Token: 0x04000D17 RID: 3351
		private Panel panel_PercRadioBtns;

		// Token: 0x04000D18 RID: 3352
		private RadioButton radioBtn_50Pers;

		// Token: 0x04000D19 RID: 3353
		private RadioButton radioBtn_30Pers;

		// Token: 0x04000D1A RID: 3354
		private RadioButton radioBtn_15Pers;

		// Token: 0x04000D1B RID: 3355
		private NumericUpDown numericUpDown_TPrice;

		// Token: 0x04000D1C RID: 3356
		private Label label_价格;

		// Token: 0x04000D1D RID: 3357
		private RadioButton radioBtn_80Pers;

		// Token: 0x04000D1E RID: 3358
		private RadioButton radioBtn_100Pers;

		// Token: 0x04000D1F RID: 3359
		private Button btn_TOpenLong;

		// Token: 0x04000D20 RID: 3360
		private Button btn_TOpenShrt;

		// Token: 0x04000D21 RID: 3361
		private Button btn_CondOrder;

		// Token: 0x04000D22 RID: 3362
		private CheckBox chkBox_FollowPrc;

		// Token: 0x04000D23 RID: 3363
		private Panel panel_OpenClose;

		// Token: 0x04000D24 RID: 3364
		private Class287 radioBtn_Close;

		// Token: 0x04000D25 RID: 3365
		private Class287 radioBtn_Open;

		// Token: 0x04000D26 RID: 3366
		private CheckBox chkBox_AutoOpenClose;

		// Token: 0x04000D27 RID: 3367
		private DevComponents.DotNetBar.TabControl tabCtrl_Trans;

		// Token: 0x04000D28 RID: 3368
		private TabControlPanel tabControlPanel_CondOrder;

		// Token: 0x04000D29 RID: 3369
		private TabItem tabPg_CondOrder;

		// Token: 0x04000D2A RID: 3370
		private Label label_usableBal;

		// Token: 0x04000D2B RID: 3371
		private Label label40;

		// Token: 0x04000D2C RID: 3372
		private Label label_PosRatio;

		// Token: 0x04000D2D RID: 3373
		private Label label_仓位;

		// Token: 0x04000D2E RID: 3374
		private ComboBox comboBox_TradingSymbs;

		// Token: 0x04000D2F RID: 3375
		private SplitContainer splitContainer_TransTabsBelow;

		// Token: 0x04000D30 RID: 3376
		private Button transTabTopBtn1;

		// Token: 0x04000D31 RID: 3377
		private Button transTabTopBtn2;

		// Token: 0x04000D32 RID: 3378
		private Button transTabTopBtn3;

		// Token: 0x04000D33 RID: 3379
		private Button transTabTopBtn4;

		// Token: 0x04000D34 RID: 3380
		private RadioButton radioBtn_IndividualSOT;

		// Token: 0x04000D35 RID: 3381
		private RadioButton radioBtn_AggregateSOT;

		// Token: 0x04000D36 RID: 3382
		private TabControlPanel tabControlPanel_BaoDian_Below;

		// Token: 0x04000D37 RID: 3383
		private TabItem tabPg_BaoDian;

		// Token: 0x04000D38 RID: 3384
		private TabControlPanel tabControlPanel_FnRpt;

		// Token: 0x04000D39 RID: 3385
		private TabItem tabPg_FnRpt;

		// Token: 0x04000D3A RID: 3386
		private TabControlPanel tabControlPanel_SelSymb;

		// Token: 0x04000D3B RID: 3387
		private TabItem tabPg_SelSymb;

		// Token: 0x04000D3C RID: 3388
		private FnRptAnlysPanel fnRptAnlysPanel;

		// Token: 0x04000D3D RID: 3389
		private SymbFilterPanel symbFilterPanel;

		// Token: 0x04000D3E RID: 3390
		private BaoDianPanel baoDianPanel;

		// Token: 0x04000D3F RID: 3391
		private TabControlPanel tabControlPanel_Video;

		// Token: 0x04000D40 RID: 3392
		private TabItem tabPg_Video;

		// Token: 0x04000D41 RID: 3393
		private TableLayoutPanel tblLayoutPanel_TrdBtns;

		// Token: 0x04000D42 RID: 3394
		private TableLayoutPanel tblLayoutPanel_TransHistQry;

		// Token: 0x04000D43 RID: 3395
		private TableLayoutPanel tblLayoutPanel_OdrHistQry;

		// Token: 0x02000256 RID: 598
		[CompilerGenerated]
		private sealed class Class318
		{
			// Token: 0x06001A69 RID: 6761 RVA: 0x000B5ACC File Offset: 0x000B3CCC
			internal void method_0()
			{
				this.dataGridViewMkt_0.smethod_0();
				this.dataGridViewMkt_0.SuspendLayout();
				this.dataGridViewMkt_0.method_8(this.sortableBindingList_0);
				if (!string.IsNullOrEmpty(this.string_0))
				{
					this.dataGridViewMkt_0.Sort(this.dataGridViewMkt_0.Columns[this.string_0], this.listSortDirection_0);
				}
				if (this.int_0 > -1 && this.int_0 < this.sortableBindingList_0.Count)
				{
					this.dataGridViewMkt_0.Rows[this.int_0].Selected = true;
				}
				this.dataGridViewMkt_0.ResumeLayout();
				this.dataGridViewMkt_0.smethod_1();
			}

			// Token: 0x04000D44 RID: 3396
			public DataGridViewMkt dataGridViewMkt_0;

			// Token: 0x04000D45 RID: 3397
			public SortableBindingList<ShowMktSymb> sortableBindingList_0;

			// Token: 0x04000D46 RID: 3398
			public string string_0;

			// Token: 0x04000D47 RID: 3399
			public ListSortDirection listSortDirection_0;

			// Token: 0x04000D48 RID: 3400
			public int int_0;
		}

		// Token: 0x02000258 RID: 600
		[CompilerGenerated]
		private sealed class Class319
		{
			// Token: 0x06001A83 RID: 6787 RVA: 0x000B5D18 File Offset: 0x000B3F18
			internal bool method_0(StkSymbol stkSymbol_0)
			{
				return stkSymbol_0.ID == this.showMktSymb_0.StkId;
			}

			// Token: 0x04000D60 RID: 3424
			public ShowMktSymb showMktSymb_0;
		}

		// Token: 0x02000259 RID: 601
		[CompilerGenerated]
		private sealed class Class320
		{
			// Token: 0x06001A85 RID: 6789 RVA: 0x000B5D3C File Offset: 0x000B3F3C
			internal bool method_0(ShowMktSymb showMktSymb_0)
			{
				return showMktSymb_0.StkId == this.int_0;
			}

			// Token: 0x04000D61 RID: 3425
			public int int_0;
		}

		// Token: 0x0200025A RID: 602
		[CompilerGenerated]
		private sealed class Class321
		{
			// Token: 0x06001A87 RID: 6791 RVA: 0x000B5D5C File Offset: 0x000B3F5C
			internal bool method_0(Transaction transaction_0)
			{
				if (transaction_0.AcctID == Base.Acct.CurrAccount.ID && transaction_0.SymbolID == this.int_0)
				{
					long? openUnits = transaction_0.OpenUnits;
					if (openUnits.GetValueOrDefault() > 0L & openUnits != null)
					{
						if (transaction_0.TransType != 1)
						{
							return transaction_0.TransType == 3;
						}
						return true;
					}
				}
				return false;
			}

			// Token: 0x04000D62 RID: 3426
			public int int_0;
		}

		// Token: 0x0200025B RID: 603
		[CompilerGenerated]
		private sealed class Class322
		{
			// Token: 0x06001A89 RID: 6793 RVA: 0x000B5DD4 File Offset: 0x000B3FD4
			internal bool method_0(TabItem tabItem_0)
			{
				return tabItem_0.Text.Contains(this.string_0);
			}

			// Token: 0x04000D63 RID: 3427
			public string string_0;
		}
	}
}
