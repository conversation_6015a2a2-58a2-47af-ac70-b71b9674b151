﻿using System;
using System.Runtime.CompilerServices;
using ns14;
using ns16;
using ns30;
using ns9;
using TEx.SIndicator;

namespace ns7
{
	// Token: 0x0200032B RID: 811
	internal sealed class Class441
	{
		// Token: 0x170005E5 RID: 1509
		// (get) Token: 0x06002261 RID: 8801 RVA: 0x000EA8A0 File Offset: 0x000E8AA0
		// (set) Token: 0x06002262 RID: 8802 RVA: 0x0000DA2C File Offset: 0x0000BC2C
		private ParserEnvironment PE { get; set; }

		// Token: 0x06002263 RID: 8803 RVA: 0x0000DA37 File Offset: 0x0000BC37
		public Class441(ParserEnvironment parserEnvironment_1)
		{
			this.PE = parserEnvironment_1;
		}

		// Token: 0x06002264 RID: 8804 RVA: 0x000EA8B8 File Offset: 0x000E8AB8
		public Class408 method_0(Tokenes tokenes_0)
		{
			tokenes_0.method_4();
			return Class419.smethod_0(tokenes_0, this.PE);
		}

		// Token: 0x06002265 RID: 8805 RVA: 0x000EA8DC File Offset: 0x000E8ADC
		public Class408 method_1(Tokenes tokenes_0)
		{
			tokenes_0.method_4();
			Class408 result;
			if (tokenes_0.Current.Symbol.HSymbolType == Enum26.const_30)
			{
				result = null;
			}
			else
			{
				result = Class414.smethod_0(tokenes_0);
			}
			return result;
		}

		// Token: 0x040010C1 RID: 4289
		[CompilerGenerated]
		private ParserEnvironment parserEnvironment_0;
	}
}
