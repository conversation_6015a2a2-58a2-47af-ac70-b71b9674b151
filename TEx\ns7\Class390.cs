﻿using System;
using System.Drawing;
using TEx.Chart;
using TEx.Inds;
using TEx.SIndicator;

namespace ns7
{
	// Token: 0x020002F1 RID: 753
	internal sealed class Class390 : ShapeCurve
	{
		// Token: 0x0600211C RID: 8476 RVA: 0x000E2E98 File Offset: 0x000E1098
		public override void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0)
		{
			if (this.curveItem_0 != null)
			{
				zedGraphControl_0.GraphPane.CurveList.Remove(this.curveItem_0);
			}
			StickColorItem stickColorItem = zedGraphControl_0.GraphPane.AddStickColorItem(base.IndData.Name, base.DataView, Color.Red, Color.Cyan);
			this.curveItem_0 = stickColorItem;
			stickColorItem.Tag = string_0 + "_" + base.IndData.Name;
			base.method_3(string_0, stickColorItem);
		}

		// Token: 0x0600211D RID: 8477 RVA: 0x0000D56B File Offset: 0x0000B76B
		public Class390(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1) : base(dataArray_1, dataProvider_1, indEx_1)
		{
		}
	}
}
