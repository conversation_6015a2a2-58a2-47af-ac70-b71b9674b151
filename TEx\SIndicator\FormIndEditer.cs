﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using AutocompleteMenuNS;
using ns10;
using ns11;
using ns13;
using ns19;
using ns27;
using ScintillaNET;
using TEx.Inds;

namespace TEx.SIndicator
{
	// Token: 0x02000308 RID: 776
	public sealed partial class FormIndEditer : Form
	{
		// Token: 0x140000A2 RID: 162
		// (add) Token: 0x06002170 RID: 8560 RVA: 0x000E4E64 File Offset: 0x000E3064
		// (remove) Token: 0x06002171 RID: 8561 RVA: 0x000E4E9C File Offset: 0x000E309C
		public event EventHandler ReadyToLoadNewIndToChart
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x140000A3 RID: 163
		// (add) Token: 0x06002172 RID: 8562 RVA: 0x000E4ED4 File Offset: 0x000E30D4
		// (remove) Token: 0x06002173 RID: 8563 RVA: 0x000E4F0C File Offset: 0x000E310C
		public event EventHandler ReadyToReloadModifiedIndToChart
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x06002174 RID: 8564 RVA: 0x000E4F44 File Offset: 0x000E3144
		private void method_0(UserDefineIndScript userDefineIndScript_1)
		{
			EventArgs30 e = new EventArgs30(userDefineIndScript_1);
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x06002175 RID: 8565 RVA: 0x000E4F70 File Offset: 0x000E3170
		private void method_1(UserDefineIndScript userDefineIndScript_1)
		{
			EventArgs30 e = new EventArgs30(userDefineIndScript_1);
			EventHandler eventHandler = this.eventHandler_1;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x06002176 RID: 8566 RVA: 0x000E4F9C File Offset: 0x000E319C
		public FormIndEditer()
		{
			this.InitializeComponent();
			this.scintilla_0 = new Scintilla();
			this.tabControl_0 = new TabControl();
			this.textBox_0 = new TextBox();
			this.autocompleteMenu_0 = new AutocompleteMenu();
			base.Load += this.FormIndEditer_Load;
			base.FormClosed += this.FormIndEditer_FormClosed;
			Base.UI.smethod_54(this);
			base.Shown += this.FormIndEditer_Shown;
			Base.UI.smethod_54(this);
			this.textBoxYLine.KeyPress += this.textBoxYLine_KeyPress;
			this.textBoxYLine.GotFocus += this.textBoxYLine_GotFocus;
			this.textBoxYLine.LostFocus += this.textBoxYLine_LostFocus;
			this.buttonOK.Click += this.buttonOK_Click;
			this.buttonCancel.Click += this.buttonCancel_Click;
			base.KeyDown += this.FormIndEditer_KeyDown;
			this.buttonTestInd.Click += this.buttonTestInd_Click;
			this.buttonInsertColor.Click += this.buttonInsertColor_Click;
			this.buttonInsertFunc.Click += this.buttonInsertFunc_Click;
			this.buttonInsertMap.Click += this.buttonInsertMap_Click;
			this.comboBox_MainOrByChart.SelectedIndexChanged += this.comboBox_MainOrByChart_SelectedIndexChanged;
			if (this.comboBox_MainOrByChart.Items.Count > 0)
			{
				this.comboBox_MainOrByChart.SelectedIndex = 0;
				this.textBoxYLine.Enabled = false;
			}
			this.comboBoxGroup.Items.Clear();
			foreach (UserDefineIndGroup userDefineIndGroup in UserDefineFileMgr.UDGList)
			{
				this.comboBoxGroup.Items.Add(userDefineIndGroup.Group);
			}
			if (this.comboBoxGroup.Items.Count > 0)
			{
				this.comboBoxGroup.SelectedIndex = this.comboBoxGroup.Items.Count - 1;
			}
			else
			{
				this.comboBoxGroup.Text = "自定义";
			}
			this.tabControl_0.Parent = this;
			TabPage tabPage = new TabPage("编辑");
			this.scintilla_0.GotFocus += this.scintilla_0_GotFocus;
			tabPage.Controls.Add(this.scintilla_0);
			this.scintilla_0.Dock = DockStyle.Fill;
			this.tabControl_0.TabPages.Add(tabPage);
			TabPage tabPage2 = new TabPage("说明");
			this.textBox_0.Multiline = true;
			this.textBox_0.Parent = tabPage2;
			this.textBox_0.Dock = DockStyle.Fill;
			this.tabControl_0.TabPages.Add(tabPage2);
			this.userControlIndParam_0 = new UserControlIndParam();
			this.userControlIndParam_0.Parent = this.groupBox1;
			this.userControlIndParam_0.Dock = DockStyle.Fill;
			this.userControlIndParam_0.method_2(new List<UserDefineParam>(), Enum25.flag_0 | Enum25.flag_1 | Enum25.flag_2, UserControlIndParam.Enum27.const_0);
			this.lableText.Text = "";
			this.method_3();
		}

		// Token: 0x06002177 RID: 8567 RVA: 0x000E52DC File Offset: 0x000E34DC
		private void textBoxYLine_KeyPress(object sender, KeyPressEventArgs e)
		{
			if (e.KeyChar != ';' && !char.IsDigit(e.KeyChar) && !char.IsControl(e.KeyChar) && e.KeyChar != '-')
			{
				if (e.KeyChar != '.')
				{
					this.method_2();
					e.Handled = true;
					return;
				}
			}
			e.Handled = false;
		}

		// Token: 0x06002178 RID: 8568 RVA: 0x0000D696 File Offset: 0x0000B896
		private void method_2()
		{
			MessageBox.Show("坐标值栏只能输入数字或者分号！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
		}

		// Token: 0x06002179 RID: 8569 RVA: 0x000E533C File Offset: 0x000E353C
		private void textBoxYLine_LostFocus(object sender, EventArgs e)
		{
			string text = this.textBoxYLine.Text;
			if (string.IsNullOrEmpty(text))
			{
				this.textBoxYLine.Text = "自动";
			}
			else
			{
				foreach (char c in text.ToCharArray())
				{
					if (c != ';' && !char.IsDigit(c) && !char.IsControl(c) && c != ' ' && c != '.' && c != '-')
					{
						this.method_2();
						this.textBoxYLine.Focus();
						return;
					}
				}
				text = text.Trim().Replace(' ', '\0');
				foreach (string value in text.Split(new char[]
				{
					';'
				}))
				{
					try
					{
						Convert.ToDouble(value);
					}
					catch
					{
						this.method_2();
						this.textBoxYLine.Focus();
						break;
					}
				}
			}
		}

		// Token: 0x0600217A RID: 8570 RVA: 0x0000D6AD File Offset: 0x0000B8AD
		private void textBoxYLine_GotFocus(object sender, EventArgs e)
		{
			if (this.textBoxYLine.Text == "自动")
			{
				this.textBoxYLine.Text = string.Empty;
			}
		}

		// Token: 0x0600217B RID: 8571 RVA: 0x0000D6D8 File Offset: 0x0000B8D8
		private void FormIndEditer_Load(object sender, EventArgs e)
		{
			HToken.WrongToken += this.method_9;
			this.textBox_Name.Focus();
		}

		// Token: 0x0600217C RID: 8572 RVA: 0x000E5430 File Offset: 0x000E3630
		private void FormIndEditer_Shown(object sender, EventArgs e)
		{
			this.tabControl_0.Top = this.buttonTestInd.Top + this.buttonTestInd.Height / 2;
			this.tabControl_0.Left = this.groupBox3.Left;
			this.tabControl_0.Height = base.ClientSize.Height - this.tabControl_0.Top - this.statusStrip1.Height;
			this.tabControl_0.Width = base.ClientSize.Width - this.tabControl_0.Left - 8;
			if (this.userDefineIndScript_0 != null)
			{
				this.scintilla_0.Focus();
				this.scintilla_0.SelectionStart = this.scintilla_0.Text.Length;
			}
			else
			{
				this.textBox_Name.Focus();
			}
		}

		// Token: 0x0600217D RID: 8573 RVA: 0x0000D6F9 File Offset: 0x0000B8F9
		private void FormIndEditer_FormClosed(object sender, FormClosedEventArgs e)
		{
			HToken.WrongToken -= this.method_9;
		}

		// Token: 0x0600217E RID: 8574 RVA: 0x000041AE File Offset: 0x000023AE
		private void FormIndEditer_KeyDown(object sender, KeyEventArgs e)
		{
		}

		// Token: 0x0600217F RID: 8575 RVA: 0x000E5510 File Offset: 0x000E3710
		private void comboBox_MainOrByChart_SelectedIndexChanged(object sender, EventArgs e)
		{
			if (this.comboBox_MainOrByChart.SelectedIndex == 0)
			{
				this.textBoxYLine.Text = "";
				this.textBoxYLine.Enabled = false;
			}
			else
			{
				this.textBoxYLine.Text = "自动";
				this.textBoxYLine.Enabled = true;
			}
		}

		// Token: 0x06002180 RID: 8576 RVA: 0x000E5568 File Offset: 0x000E3768
		private void method_3()
		{
			this.autocompleteMenu_0.TargetControlWrapper = new ScintillaWrapper(this.scintilla_0);
			this.method_14(this.scintilla_0);
			for (int i = 0; i < this.scintilla_0.Styles.Count; i++)
			{
				this.scintilla_0.Styles[i].Case = StyleCase.Upper;
			}
			this.scintilla_0.MouseDwellTime = 100;
			this.scintilla_0.VScrollBar = true;
			this.scintilla_0.HScrollBar = true;
			this.scintilla_0.Styles[38].Font = "宋体";
			this.scintilla_0.Styles[38].SizeF = 10f;
			this.scintilla_0.Styles[38].Bold = false;
			this.scintilla_0.Styles[38].ForeColor = SystemColors.InfoText;
			this.scintilla_0.Styles[38].BackColor = SystemColors.Info;
			this.scintilla_0.DwellStart += this.method_7;
			this.scintilla_0.MouseEnter += this.scintilla_0_MouseEnter;
			this.scintilla_0.MouseDown += this.scintilla_0_MouseDown;
			this.scintilla_0.KeyDown += this.scintilla_0_KeyDown;
			this.scintilla_0.KeyUp += this.scintilla_0_KeyUp;
			this.autocompleteMenu_0.AutoPopup = true;
			this.autocompleteMenu_0.AllowsTabKey = true;
			this.method_13(ParserEnvironment.smethod_11(this.userControlIndParam_0.ShowList.ToList<UserDefineParam>()));
			this.scintilla_0.UpdateUI += this.method_12;
		}

		// Token: 0x06002181 RID: 8577 RVA: 0x0000D70E File Offset: 0x0000B90E
		private void scintilla_0_KeyUp(object sender, KeyEventArgs e)
		{
			this.method_4();
			this.lableText.Text = "";
		}

		// Token: 0x06002182 RID: 8578 RVA: 0x0000D70E File Offset: 0x0000B90E
		private void scintilla_0_MouseDown(object sender, MouseEventArgs e)
		{
			this.method_4();
			this.lableText.Text = "";
		}

		// Token: 0x06002183 RID: 8579 RVA: 0x000E5738 File Offset: 0x000E3938
		private void method_4()
		{
			int num = this.scintilla_0.CurrentLine + 1;
			int column = this.scintilla_0.GetColumn(this.scintilla_0.CurrentPosition);
			this.positionLabel.Text = string.Concat(new object[]
			{
				"行:",
				num,
				" 列:",
				column
			});
		}

		// Token: 0x06002184 RID: 8580 RVA: 0x0000D728 File Offset: 0x0000B928
		private void scintilla_0_KeyDown(object sender, KeyEventArgs e)
		{
			this.method_10();
			this.lableText.Text = "";
		}

		// Token: 0x06002185 RID: 8581 RVA: 0x000E57A4 File Offset: 0x000E39A4
		private void buttonInsertMap_Click(object sender, EventArgs e)
		{
			FormICON formICON = new FormICON();
			if (formICON.ShowDialog() == DialogResult.OK)
			{
				this.method_19(",'" + formICON.string_0 + "'");
			}
		}

		// Token: 0x06002186 RID: 8582 RVA: 0x000E57E0 File Offset: 0x000E39E0
		private void method_5()
		{
			UserDefineIndScript userDefineIndScript_ = this.method_20();
			if (this.method_16(userDefineIndScript_))
			{
				this.method_22(userDefineIndScript_, false, false);
				base.Close();
			}
		}

		// Token: 0x06002187 RID: 8583 RVA: 0x000E5810 File Offset: 0x000E3A10
		private void buttonInsertColor_Click(object sender, EventArgs e)
		{
			ColorDialog colorDialog = new ColorDialog();
			colorDialog.AllowFullOpen = true;
			colorDialog.ShowHelp = true;
			colorDialog.Color = Color.Red;
			colorDialog.AnyColor = true;
			if (colorDialog.ShowDialog() == DialogResult.OK)
			{
				string string_ = this.method_6(colorDialog.Color);
				this.method_19(string_);
			}
		}

		// Token: 0x06002188 RID: 8584 RVA: 0x000E5864 File Offset: 0x000E3A64
		private string method_6(Color color_0)
		{
			return ",COLOR" + color_0.B.ToString("X4").Substring(2, 2) + color_0.G.ToString("X4").Substring(2, 2) + color_0.R.ToString("X4").Substring(2, 2);
		}

		// Token: 0x06002189 RID: 8585 RVA: 0x0000D742 File Offset: 0x0000B942
		private void scintilla_0_MouseEnter(object sender, EventArgs e)
		{
			this.scintilla_0.CallTipCancel();
		}

		// Token: 0x0600218A RID: 8586 RVA: 0x000E58D0 File Offset: 0x000E3AD0
		private void method_7(object sender, DwellEventArgs e)
		{
			int position = e.Position;
			if (position > 0)
			{
				if (position < this.scintilla_0.Text.Length)
				{
					if (char.IsLetter(this.scintilla_0.Text[position]))
					{
						string text = "";
						string text2 = "";
						for (int i = position; i >= 0; i--)
						{
							char c = this.scintilla_0.Text[i];
							if (!char.IsLetterOrDigit(c))
							{
								break;
							}
							text = c.ToString() + text;
						}
						for (int j = position + 1; j < this.scintilla_0.Text.Length; j++)
						{
							char c2 = this.scintilla_0.Text[j];
							if (!char.IsLetterOrDigit(c2))
							{
								break;
							}
							text2 += c2.ToString();
						}
						string text3 = text + text2;
						if (text3 != "")
						{
							string text4 = ParserEnvironment.smethod_0(text3.ToUpper());
							if (text4 != "")
							{
								this.scintilla_0.CallTipShow(e.Position, text4);
							}
						}
					}
				}
			}
		}

		// Token: 0x0600218B RID: 8587 RVA: 0x000E5A00 File Offset: 0x000E3C00
		public bool method_8(UserDefineIndScript userDefineIndScript_1)
		{
			FormIndEditer.Class424 @class = new FormIndEditer.Class424();
			@class.userDefineIndScript_0 = userDefineIndScript_1;
			@class.userDefineIndScript_1 = UserDefineFileMgr.UDSList.FirstOrDefault(new Func<UserDefineIndScript, bool>(@class.method_0));
			bool result;
			if (@class.userDefineIndScript_1 == null)
			{
				MessageBox.Show("没有在文件中发现名称为" + @class.userDefineIndScript_0.Name + "的指标，加载失败！", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation);
				result = false;
			}
			else
			{
				this.userDefineIndScript_0 = @class.userDefineIndScript_1;
				this.textBox_Name.Enabled = false;
				this.textBox_Name.Text = @class.userDefineIndScript_1.Name;
				this.textBox_describe.Text = @class.userDefineIndScript_1.Script;
				if (this.comboBox_MainOrByChart.Items.Count != 2)
				{
					throw new Exception("指标位置无法确定");
				}
				if (@class.userDefineIndScript_1.MainK)
				{
					this.comboBox_MainOrByChart.SelectedItem = this.comboBox_MainOrByChart.Items[0];
					this.textBoxYLine.Enabled = false;
					this.textBoxYLine.Text = "";
				}
				else
				{
					this.comboBox_MainOrByChart.SelectedItem = this.comboBox_MainOrByChart.Items[1];
					this.textBoxYLine.Enabled = true;
					if (@class.userDefineIndScript_1.YLine == "")
					{
						this.textBoxYLine.Text = "自动";
					}
					else
					{
						this.textBoxYLine.Text = @class.userDefineIndScript_1.YLine;
					}
				}
				this.userControlIndParam_0.method_2(@class.userDefineIndScript_1.UserDefineParams, Enum25.flag_0 | Enum25.flag_1 | Enum25.flag_2, UserControlIndParam.Enum27.const_0);
				this.textBox_0.Text = @class.userDefineIndScript_1.Instruction.Replace("\n", "\r\n");
				this.scintilla_0.Text = @class.userDefineIndScript_1.Code;
				foreach (UserDefineIndGroup userDefineIndGroup in UserDefineFileMgr.UDGList)
				{
					IEnumerable<UserDefineIndScript> udslist = userDefineIndGroup.UDSList;
					Func<UserDefineIndScript, bool> predicate;
					if ((predicate = @class.func_0) == null)
					{
						predicate = (@class.func_0 = new Func<UserDefineIndScript, bool>(@class.method_1));
					}
					if (udslist.Any(predicate))
					{
						this.comboBoxGroup.Text = userDefineIndGroup.Group;
					}
				}
				result = true;
			}
			return result;
		}

		// Token: 0x0600218C RID: 8588 RVA: 0x0000D751 File Offset: 0x0000B951
		private void scintilla_0_GotFocus(object sender, EventArgs e)
		{
			this.method_13(ParserEnvironment.smethod_11(this.userControlIndParam_0.ShowList));
		}

		// Token: 0x0600218D RID: 8589 RVA: 0x000E5C54 File Offset: 0x000E3E54
		private void method_9(object sender, EventArgs e)
		{
			EventArgs31 eventArgs = e as EventArgs31;
			if (eventArgs != null)
			{
				HToken htoken = eventArgs.Obj as HToken;
				if (htoken != null)
				{
					int line = htoken.Line;
					this.method_11(htoken);
				}
			}
		}

		// Token: 0x0600218E RID: 8590 RVA: 0x0000D76B File Offset: 0x0000B96B
		private void method_10()
		{
			this.scintilla_0.IndicatorCurrent = 8;
			this.scintilla_0.IndicatorClearRange(0, this.scintilla_0.TextLength);
		}

		// Token: 0x0600218F RID: 8591 RVA: 0x000E5C90 File Offset: 0x000E3E90
		private void method_11(HToken htoken_0)
		{
			this.method_10();
			this.scintilla_0.Indicators[8].Style = IndicatorStyle.StraightBox;
			this.scintilla_0.Indicators[8].Under = true;
			this.scintilla_0.Indicators[8].ForeColor = Color.Red;
			this.scintilla_0.Indicators[8].OutlineAlpha = 50;
			this.scintilla_0.Indicators[8].Alpha = 100;
			if (htoken_0.Line - 1 < this.scintilla_0.Lines.Count)
			{
				Line line = this.scintilla_0.Lines[htoken_0.Line - 1];
				this.scintilla_0.IndicatorFillRange(line.Position, line.Length);
				if (!string.IsNullOrEmpty(htoken_0.Symbol.Name))
				{
					this.lableText.Text = "错误位置";
				}
			}
		}

		// Token: 0x06002190 RID: 8592 RVA: 0x000E5D8C File Offset: 0x000E3F8C
		private void method_12(object sender, UpdateUIEventArgs e)
		{
			Scintilla scintilla = sender as Scintilla;
			if (scintilla != null)
			{
				if ((e.Change & UpdateChange.Selection) > (UpdateChange)0)
				{
					int currentPosition = scintilla.CurrentPosition;
					int anchorPosition = scintilla.AnchorPosition;
				}
			}
		}

		// Token: 0x06002191 RID: 8593 RVA: 0x000E5DC0 File Offset: 0x000E3FC0
		private void method_13(string[] string_1)
		{
			List<AutocompleteItem> list = new List<AutocompleteItem>();
			foreach (string text in string_1)
			{
				list.Add(new AutocompleteItem(text)
				{
					ImageIndex = 1
				});
			}
			this.autocompleteMenu_0.SetAutocompleteItems(list);
		}

		// Token: 0x06002192 RID: 8594 RVA: 0x000E5E08 File Offset: 0x000E4008
		private void method_14(Scintilla scintilla_1)
		{
			scintilla_1.Margins[0].Width = 20;
			scintilla_1.Styles[0].ForeColor = Color.Silver;
			scintilla_1.Styles[1].ForeColor = Color.FromArgb(0, 128, 0);
			scintilla_1.Styles[2].ForeColor = Color.FromArgb(0, 128, 0);
			scintilla_1.Styles[15].ForeColor = Color.FromArgb(128, 128, 128);
			scintilla_1.Styles[4].ForeColor = Color.Olive;
			scintilla_1.Styles[5].ForeColor = Color.Blue;
			scintilla_1.Styles[16].ForeColor = Color.Blue;
			scintilla_1.Styles[6].ForeColor = Color.FromArgb(163, 21, 21);
			scintilla_1.Styles[7].ForeColor = Color.FromArgb(163, 21, 21);
			scintilla_1.Styles[13].ForeColor = Color.FromArgb(163, 21, 21);
			scintilla_1.Styles[12].BackColor = Color.Pink;
			scintilla_1.Styles[10].ForeColor = Color.Purple;
			scintilla_1.Styles[9].ForeColor = Color.Maroon;
			scintilla_1.Lexer = Lexer.Cpp;
			string[] value = ParserEnvironment.smethod_11(this.userControlIndParam_0.ShowList.ToList<UserDefineParam>());
			string keywords = string.Join(" ", value);
			scintilla_1.SetKeywords(0, keywords);
			scintilla_1.SetProperty("fold", "1");
			scintilla_1.SetProperty("fold.compact", "1");
			scintilla_1.Margins[2].Type = MarginType.Symbol;
			scintilla_1.Margins[2].Mask = 4261412864U;
			scintilla_1.Margins[2].Sensitive = true;
			scintilla_1.Margins[2].Width = 20;
			for (int i = 25; i <= 31; i++)
			{
				scintilla_1.Markers[i].SetForeColor(SystemColors.ControlLightLight);
				scintilla_1.Markers[i].SetBackColor(SystemColors.ControlDark);
			}
			scintilla_1.Markers[30].Symbol = MarkerSymbol.BoxPlus;
			scintilla_1.Markers[31].Symbol = MarkerSymbol.BoxMinus;
			scintilla_1.Markers[25].Symbol = MarkerSymbol.BoxPlusConnected;
			scintilla_1.Markers[27].Symbol = MarkerSymbol.TCorner;
			scintilla_1.Markers[26].Symbol = MarkerSymbol.BoxMinusConnected;
			scintilla_1.Markers[29].Symbol = MarkerSymbol.VLine;
			scintilla_1.Markers[28].Symbol = MarkerSymbol.LCorner;
			scintilla_1.AutomaticFold = (AutomaticFold.Show | AutomaticFold.Click | AutomaticFold.Change);
		}

		// Token: 0x06002193 RID: 8595 RVA: 0x000E60F4 File Offset: 0x000E42F4
		public static bool smethod_0(List<UserDefineParam> list_0)
		{
			bool result;
			using (List<UserDefineParam>.Enumerator enumerator = list_0.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					if (!enumerator.Current.Check())
					{
						result = false;
						goto IL_77;
					}
				}
			}
			List<string> list = list_0.Select(new Func<UserDefineParam, string>(FormIndEditer.<>c.<>9.method_0)).ToList<string>();
			list.Distinct<string>().Count<string>();
			int count = list.Count;
			return true;
			IL_77:
			return result;
		}

		// Token: 0x06002194 RID: 8596 RVA: 0x0002DC84 File Offset: 0x0002BE84
		private bool method_15(string string_1)
		{
			return false;
		}

		// Token: 0x06002195 RID: 8597 RVA: 0x000E6190 File Offset: 0x000E4390
		private bool method_16(UserDefineIndScript userDefineIndScript_1)
		{
			string name = userDefineIndScript_1.Name;
			string text = "";
			bool result;
			if (!UserDefineIndScript.smethod_0(name, out text))
			{
				MessageBox.Show(text, "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				this.textBox_Name.Focus();
				result = false;
			}
			else if (string.IsNullOrEmpty(userDefineIndScript_1.Code))
			{
				MessageBox.Show("请输入公式内容。", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				this.scintilla_0.Focus();
				result = false;
			}
			else
			{
				if (string.IsNullOrEmpty(this.comboBoxGroup.Text))
				{
					MessageBox.Show("请输入分组信息", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				}
				if (!FormIndEditer.smethod_0(userDefineIndScript_1.UserDefineParams))
				{
					result = false;
				}
				else
				{
					result = true;
				}
			}
			return result;
		}

		// Token: 0x06002196 RID: 8598 RVA: 0x000E623C File Offset: 0x000E443C
		private string method_17()
		{
			string result = string.Empty;
			string text = this.textBoxYLine.Text.Trim();
			if (text == "自动")
			{
				result = string.Empty;
			}
			else
			{
				result = text;
			}
			return result;
		}

		// Token: 0x06002197 RID: 8599 RVA: 0x000E627C File Offset: 0x000E447C
		private bool method_18(UserDefineIndScript userDefineIndScript_1)
		{
			bool result;
			try
			{
				this.method_10();
				if (!this.method_16(userDefineIndScript_1))
				{
					result = false;
				}
				else
				{
					string code = userDefineIndScript_1.Code;
					if (UserDefineInd.smethod_5(userDefineIndScript_1, Base.Data.CurrSymbDataSet.CurrSymbol.MstSymbol))
					{
						userDefineIndScript_1.Check = true;
						result = true;
					}
					else
					{
						result = false;
					}
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message);
				result = false;
			}
			return result;
		}

		// Token: 0x06002198 RID: 8600 RVA: 0x0000D792 File Offset: 0x0000B992
		private void buttonTestInd_Click(object sender, EventArgs e)
		{
			if (this.method_18(this.method_20()))
			{
				MessageBox.Show("公式检查正确。", "确定", MessageBoxButtons.OKCancel, MessageBoxIcon.Asterisk);
			}
		}

		// Token: 0x06002199 RID: 8601 RVA: 0x000E62F0 File Offset: 0x000E44F0
		private void method_19(string string_1)
		{
			int position = this.scintilla_0.CurrentPosition + string_1.Length;
			this.scintilla_0.InsertText(this.scintilla_0.CurrentPosition, string_1);
			this.scintilla_0.Select();
			this.scintilla_0.GotoPosition(position);
		}

		// Token: 0x0600219A RID: 8602 RVA: 0x000E6340 File Offset: 0x000E4540
		private void buttonInsertFunc_Click(object sender, EventArgs e)
		{
			FormIndFunction formIndFunction = new FormIndFunction();
			formIndFunction.Top = base.Top;
			formIndFunction.Left = base.Left;
			if (formIndFunction.ShowDialog() == DialogResult.OK && formIndFunction.FunctionName != "")
			{
				this.method_19(formIndFunction.FunctionName);
			}
		}

		// Token: 0x0600219B RID: 8603 RVA: 0x000E6394 File Offset: 0x000E4594
		private UserDefineIndScript method_20()
		{
			bool mainK = this.comboBox_MainOrByChart.SelectedIndex == 0;
			return new UserDefineIndScript(this.textBox_Name.Text, this.scintilla_0.Text.ToUpper(), this.userControlIndParam_0.ShowList, mainK, this.textBox_describe.Text, false, this.method_17(), this.textBox_0.Text.Replace("\r\n", "\n").Trim());
		}

		// Token: 0x0600219C RID: 8604 RVA: 0x000E6414 File Offset: 0x000E4614
		private void buttonCancel_Click(object sender, EventArgs e)
		{
			try
			{
				if (FormIndEditer.smethod_0(this.userControlIndParam_0.ShowList))
				{
					this.AddOrMdfIndName = "";
					UserDefineIndScript userDefineIndScript = this.method_20();
					if (userDefineIndScript.method_1(this.userDefineIndScript_0))
					{
						base.Close();
					}
					else
					{
						userDefineIndScript.UserIndFlag = true;
						DialogResult dialogResult = MessageBox.Show("指标已经被修改，是否保存?", "请确认", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
						if (dialogResult == DialogResult.Yes)
						{
							if (this.method_18(userDefineIndScript))
							{
								this.method_22(userDefineIndScript, true, true);
								base.Close();
							}
						}
						else if (dialogResult == DialogResult.No)
						{
							base.Close();
						}
					}
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message);
			}
		}

		// Token: 0x0600219D RID: 8605 RVA: 0x000E64C4 File Offset: 0x000E46C4
		private void method_21(UserDefineIndScript userDefineIndScript_1)
		{
			FormIndEditer.Class425 @class = new FormIndEditer.Class425();
			@class.userDefineIndScript_0 = userDefineIndScript_1;
			if (Base.UI.ChtCtrl_KLineList != null)
			{
				foreach (ChtCtrl_KLine chtCtrl_KLine in Base.UI.ChtCtrl_KLineList)
				{
					IEnumerable<Indicator> indList = chtCtrl_KLine.IndList;
					Func<Indicator, bool> predicate;
					if ((predicate = @class.func_0) == null)
					{
						predicate = (@class.func_0 = new Func<Indicator, bool>(@class.method_0));
					}
					foreach (Indicator indicator in indList.Where(predicate))
					{
						if (indicator != null)
						{
							indicator.Chart.method_84(@class.userDefineIndScript_0);
						}
					}
				}
			}
			Base.UI.smethod_181(@class.userDefineIndScript_0);
		}

		// Token: 0x0600219E RID: 8606 RVA: 0x000E65A4 File Offset: 0x000E47A4
		private void method_22(UserDefineIndScript userDefineIndScript_1, bool bool_0, bool bool_1)
		{
			this.AddOrMdfIndName = userDefineIndScript_1.Name;
			if (this.eventHandler_0 != null)
			{
				UserDefineFileMgr.smethod_23(userDefineIndScript_1, this.comboBoxGroup.Text.Trim());
				if (bool_0 && bool_1)
				{
					Base.UI.smethod_164();
					if (MessageBox.Show("是否将此指标加载到图表中?", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Asterisk) == DialogResult.Yes)
					{
						this.method_0(userDefineIndScript_1);
					}
				}
			}
			else if (this.eventHandler_1 != null)
			{
				UserDefineFileMgr.smethod_22(userDefineIndScript_1, this.comboBoxGroup.Text.Trim());
				if (bool_0 && bool_1)
				{
					this.method_21(userDefineIndScript_1);
				}
			}
			else if (this.IsAddNew != null)
			{
				if (this.IsAddNew.Value)
				{
					UserDefineFileMgr.smethod_23(userDefineIndScript_1, this.comboBoxGroup.Text.Trim());
					if (Base.UI.SelectedChtCtrl != null && Base.UI.SelectedChtCtrl is ChtCtrl_KLine && MessageBox.Show("是否将此指标加载到当前图表中?", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Asterisk) == DialogResult.Yes)
					{
						(Base.UI.SelectedChtCtrl as ChtCtrl_KLine).Chart_CS.method_82(userDefineIndScript_1);
					}
				}
				else
				{
					UserDefineFileMgr.smethod_22(userDefineIndScript_1, this.comboBoxGroup.Text.Trim());
					this.method_21(userDefineIndScript_1);
				}
			}
			Base.UI.smethod_164();
		}

		// Token: 0x0600219F RID: 8607 RVA: 0x000E66DC File Offset: 0x000E48DC
		private void buttonOK_Click(object sender, EventArgs e)
		{
			UserDefineIndScript userDefineIndScript = this.method_20();
			if (this.method_18(userDefineIndScript))
			{
				if (!userDefineIndScript.method_1(this.userDefineIndScript_0))
				{
					userDefineIndScript.UserIndFlag = true;
				}
				this.method_22(userDefineIndScript, true, true);
				base.Close();
			}
		}

		// Token: 0x170005C8 RID: 1480
		// (get) Token: 0x060021A0 RID: 8608 RVA: 0x000E6724 File Offset: 0x000E4924
		// (set) Token: 0x060021A1 RID: 8609 RVA: 0x0000D7B7 File Offset: 0x0000B9B7
		public string AddOrMdfIndName { get; private set; }

		// Token: 0x170005C9 RID: 1481
		// (get) Token: 0x060021A2 RID: 8610 RVA: 0x000E673C File Offset: 0x000E493C
		// (set) Token: 0x060021A3 RID: 8611 RVA: 0x0000D7C2 File Offset: 0x0000B9C2
		public string Group
		{
			get
			{
				return this.comboBoxGroup.Text;
			}
			set
			{
				this.comboBoxGroup.Text = value;
			}
		}

		// Token: 0x170005CA RID: 1482
		// (get) Token: 0x060021A4 RID: 8612 RVA: 0x000E6758 File Offset: 0x000E4958
		// (set) Token: 0x060021A5 RID: 8613 RVA: 0x0000D7D2 File Offset: 0x0000B9D2
		public bool? IsAddNew { get; set; }

		// Token: 0x060021A6 RID: 8614 RVA: 0x0000D7DD File Offset: 0x0000B9DD
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0400103A RID: 4154
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x0400103B RID: 4155
		[CompilerGenerated]
		private EventHandler eventHandler_1;

		// Token: 0x0400103C RID: 4156
		private Scintilla scintilla_0;

		// Token: 0x0400103D RID: 4157
		private TabControl tabControl_0;

		// Token: 0x0400103E RID: 4158
		private TextBox textBox_0;

		// Token: 0x0400103F RID: 4159
		private AutocompleteMenu autocompleteMenu_0;

		// Token: 0x04001040 RID: 4160
		private UserControlIndParam userControlIndParam_0;

		// Token: 0x04001041 RID: 4161
		private UserDefineIndScript userDefineIndScript_0;

		// Token: 0x04001042 RID: 4162
		private const int int_0 = 8;

		// Token: 0x04001043 RID: 4163
		[CompilerGenerated]
		private string string_0;

		// Token: 0x04001044 RID: 4164
		[CompilerGenerated]
		private bool? nullable_0;

		// Token: 0x04001045 RID: 4165
		private IContainer icontainer_0;

		// Token: 0x02000309 RID: 777
		[CompilerGenerated]
		private sealed class Class424
		{
			// Token: 0x060021A9 RID: 8617 RVA: 0x000E7368 File Offset: 0x000E5568
			internal bool method_0(UserDefineIndScript userDefineIndScript_2)
			{
				return userDefineIndScript_2.Name.Trim() == this.userDefineIndScript_0.Name.Trim();
			}

			// Token: 0x060021AA RID: 8618 RVA: 0x000E739C File Offset: 0x000E559C
			internal bool method_1(UserDefineIndScript userDefineIndScript_2)
			{
				return userDefineIndScript_2.Name == this.userDefineIndScript_1.Name;
			}

			// Token: 0x0400105B RID: 4187
			public UserDefineIndScript userDefineIndScript_0;

			// Token: 0x0400105C RID: 4188
			public UserDefineIndScript userDefineIndScript_1;

			// Token: 0x0400105D RID: 4189
			public Func<UserDefineIndScript, bool> func_0;
		}

		// Token: 0x0200030B RID: 779
		[CompilerGenerated]
		private sealed class Class425
		{
			// Token: 0x060021AF RID: 8623 RVA: 0x000E73DC File Offset: 0x000E55DC
			internal bool method_0(Indicator indicator_0)
			{
				bool result;
				if (indicator_0.EnName == this.userDefineIndScript_0.Name)
				{
					result = (indicator_0.IsMainChartInd == this.userDefineIndScript_0.MainK);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04001060 RID: 4192
			public UserDefineIndScript userDefineIndScript_0;

			// Token: 0x04001061 RID: 4193
			public Func<Indicator, bool> func_0;
		}
	}
}
