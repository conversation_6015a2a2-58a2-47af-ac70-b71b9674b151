﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using TEx.Chart;

namespace TEx
{
	// Token: 0x02000069 RID: 105
	[Serializable]
	internal sealed class DrawFibonacciExtLines : DrawObj, ISerializable
	{
		// Token: 0x060003DF RID: 991 RVA: 0x000037AA File Offset: 0x000019AA
		public DrawFibonacciExtLines()
		{
		}

		// Token: 0x060003E0 RID: 992 RVA: 0x00003A3D File Offset: 0x00001C3D
		public DrawFibonacciExtLines(ChartCS chart, double x1, double y1, double x2, double y2) : base(chart, x1, y1, x2, y2)
		{
			base.Name = "斐波那契时间扩展";
			base.CanChgColor = true;
			base.IsOneClickLoc = false;
		}

		// Token: 0x060003E1 RID: 993 RVA: 0x000037DC File Offset: 0x000019DC
		protected DrawFibonacciExtLines(SerializationInfo info, StreamingContext context)
		{
			base.method_0(info);
		}

		// Token: 0x060003E2 RID: 994 RVA: 0x000037ED File Offset: 0x000019ED
		public override void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			base.GetObjectData(info, context);
		}

		// Token: 0x060003E3 RID: 995 RVA: 0x00021B84 File Offset: 0x0001FD84
		protected override List<GraphObj> vmethod_0(ChartCS chartCS_1, double double_1, double double_2, double double_3, double double_4, string string_5)
		{
			List<GraphObj> list = new List<GraphObj>();
			LineObj item = base.method_22(chartCS_1, double_1, string_5);
			list.Add(item);
			double max = chartCS_1.GraphPane.XAxis.Scale.Max;
			double min = chartCS_1.GraphPane.YAxis.Scale.Min;
			TextObj textObj = base.method_27(chartCS_1, double_1, min, (double_3 - double_1).ToString("F0"), null, string_5);
			textObj.Location.AlignV = AlignV.Bottom;
			textObj.Location.AlignH = AlignH.Left;
			list.Add(textObj);
			foreach (DrawSublineParam drawSublineParam in base.SublineParamList)
			{
				if (drawSublineParam.Enabled)
				{
					double num = double_1 + Math.Round((double_3 - double_1) * drawSublineParam.Value);
					if (num >= max || num <= 0.0)
					{
						break;
					}
					LineObj item2 = base.method_22(chartCS_1, num, string_5);
					list.Add(item2);
					TextObj textObj2 = base.method_27(chartCS_1, num, min, drawSublineParam.Value.ToString("F" + drawSublineParam.DigitNb.ToString()), null, string_5);
					textObj2.Location.AlignV = AlignV.Bottom;
					textObj2.Location.AlignH = AlignH.Left;
					list.Add(textObj2);
				}
			}
			return list;
		}

		// Token: 0x060003E4 RID: 996 RVA: 0x00021D0C File Offset: 0x0001FF0C
		protected override List<DrawSublineParam> vmethod_22()
		{
			List<double> list_ = new List<double>(new double[]
			{
				1.0,
				1.618,
				2.0,
				2.618,
				3.618
			});
			return base.method_28(list_, 1.0, 5000.0, 3);
		}
	}
}
