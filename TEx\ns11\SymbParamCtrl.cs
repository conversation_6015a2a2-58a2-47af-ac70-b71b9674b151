﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using ns28;
using TEx;
using TEx.Comn;
using TEx.Trading;
using TEx.Util;

namespace ns11
{
	// Token: 0x0200024B RID: 587
	internal sealed class SymbParamCtrl : UserControl
	{
		// Token: 0x14000090 RID: 144
		// (add) Token: 0x060018F7 RID: 6391 RVA: 0x000A749C File Offset: 0x000A569C
		// (remove) Token: 0x060018F8 RID: 6392 RVA: 0x000A74D4 File Offset: 0x000A56D4
		public event EventHandler FieldChanged
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_0;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_0, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060018F9 RID: 6393 RVA: 0x000A750C File Offset: 0x000A570C
		protected void method_0()
		{
			EventArgs e = new EventArgs();
			EventHandler eventHandler = this.eventHandler_0;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x14000091 RID: 145
		// (add) Token: 0x060018FA RID: 6394 RVA: 0x000A7534 File Offset: 0x000A5734
		// (remove) Token: 0x060018FB RID: 6395 RVA: 0x000A756C File Offset: 0x000A576C
		public event EventHandler FieldsChkNoChange
		{
			[CompilerGenerated]
			add
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Combine(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
			[CompilerGenerated]
			remove
			{
				EventHandler eventHandler = this.eventHandler_1;
				EventHandler eventHandler2;
				do
				{
					eventHandler2 = eventHandler;
					EventHandler value2 = (EventHandler)Delegate.Remove(eventHandler2, value);
					eventHandler = Interlocked.CompareExchange<EventHandler>(ref this.eventHandler_1, value2, eventHandler2);
				}
				while (eventHandler != eventHandler2);
			}
		}

		// Token: 0x060018FC RID: 6396 RVA: 0x000A75A4 File Offset: 0x000A57A4
		protected void method_1()
		{
			EventArgs e = new EventArgs();
			EventHandler eventHandler = this.eventHandler_1;
			if (eventHandler != null)
			{
				eventHandler(this, e);
			}
		}

		// Token: 0x060018FD RID: 6397 RVA: 0x0000A3F5 File Offset: 0x000085F5
		public SymbParamCtrl() : this(false)
		{
		}

		// Token: 0x060018FE RID: 6398 RVA: 0x000A75CC File Offset: 0x000A57CC
		public SymbParamCtrl(bool bool_5)
		{
			this.InitializeComponent();
			this.listBox_symb.SelectedIndexChanged += this.listBox_symb_SelectedIndexChanged;
			this.checkBox_OneSideFee.CheckStateChanged += this.checkBox_OneSideFee_CheckStateChanged;
			this.textBox_AutoStPt.KeyPress += this.textBox_marginRate_KeyPress;
			this.textBox_AutoTfPt.KeyPress += this.textBox_marginRate_KeyPress;
			this.textBox_avgSlpg.KeyPress += this.textBox_marginRate_KeyPress;
			this.textBox_defaultTransUnit.KeyPress += this.textBox_marginRate_KeyPress;
			this.textBox_feeAmt.KeyPress += this.textBox_marginRate_KeyPress;
			this.textBox_feeRatio.KeyPress += this.textBox_marginRate_KeyPress;
			this.textBox_marginRate.KeyPress += this.textBox_marginRate_KeyPress;
			this.textBox_AutoStPt.TextChanged += this.textBox_AutoStPt_TextChanged;
			this.textBox_AutoTfPt.TextChanged += this.textBox_AutoTfPt_TextChanged;
			this.textBox_avgSlpg.TextChanged += this.textBox_avgSlpg_TextChanged;
			this.textBox_defaultTransUnit.TextChanged += this.textBox_defaultTransUnit_TextChanged;
			this.textBox_feeAmt.TextChanged += this.textBox_feeAmt_TextChanged;
			this.textBox_feeRatio.TextChanged += this.textBox_feeRatio_TextChanged;
			this.textBox_marginRate.TextChanged += this.textBox_marginRate_TextChanged;
			this.textBox_AutoStPt.Enter += this.textBox_AutoStPt_Enter;
			this.textBox_AutoTfPt.Enter += this.textBox_AutoTfPt_Enter;
			this.textBox_avgSlpg.Enter += this.textBox_avgSlpg_Enter;
			this.textBox_defaultTransUnit.Enter += this.textBox_defaultTransUnit_Enter;
			this.textBox_feeAmt.Enter += this.textBox_feeAmt_Enter;
			this.textBox_feeRatio.Enter += this.textBox_feeRatio_Enter;
			this.textBox_marginRate.Enter += this.textBox_marginRate_Enter;
			this.textBox_marginRate.Leave += this.textBox_marginRate_Leave;
			this.textBox_AutoStPt.Leave += this.textBox_AutoStPt_Leave;
			this.textBox_AutoTfPt.Leave += this.textBox_AutoTfPt_Leave;
			this.textBox_defaultTransUnit.Leave += this.textBox_defaultTransUnit_Leave;
			this.textBox_avgSlpg.Leave += this.textBox_avgSlpg_Leave;
			this.textBox_feeAmt.Leave += this.textBox_feeAmt_Leave;
			this.textBox_feeRatio.Leave += this.textBox_feeRatio_Leave;
			this.list_2 = new List<TradingSymbol>();
			this.list_1 = new List<AcctSymbol>();
			this.bool_1 = false;
			this.bool_2 = bool_5;
		}

		// Token: 0x060018FF RID: 6399 RVA: 0x0000A3FE File Offset: 0x000085FE
		private void SymbParamCtrl_Load(object sender, EventArgs e)
		{
			bool ifShowCurrSymbOnStartup = this.IfShowCurrSymbOnStartup;
		}

		// Token: 0x17000428 RID: 1064
		// (get) Token: 0x06001900 RID: 6400 RVA: 0x000A78C0 File Offset: 0x000A5AC0
		// (set) Token: 0x06001901 RID: 6401 RVA: 0x000A78D8 File Offset: 0x000A5AD8
		public List<TradingSymbol> DataSource
		{
			get
			{
				return this.list_0;
			}
			set
			{
				if ((value == null && this.list_0 != null) || (this.list_0 == null && value != null) || this.list_0 != value)
				{
					this.list_0 = value;
					if (this.listBox_symb.DisplayMember != "AbbrCNName")
					{
						this.listBox_symb.DisplayMember = "AbbrCNName";
					}
					this.listBox_symb.DataSource = this.list_0;
				}
			}
		}

		// Token: 0x17000429 RID: 1065
		// (get) Token: 0x06001902 RID: 6402 RVA: 0x000A7948 File Offset: 0x000A5B48
		// (set) Token: 0x06001903 RID: 6403 RVA: 0x0000A409 File Offset: 0x00008609
		public TradingSymbol LastSymblInEdit
		{
			get
			{
				return this.tradingSymbol_0;
			}
			set
			{
				this.tradingSymbol_0 = value;
			}
		}

		// Token: 0x1700042A RID: 1066
		// (get) Token: 0x06001904 RID: 6404 RVA: 0x000A7960 File Offset: 0x000A5B60
		// (set) Token: 0x06001905 RID: 6405 RVA: 0x0000A414 File Offset: 0x00008614
		public TradingSymbol GlobalTsOfCurrAcctSymb
		{
			get
			{
				return this.tradingSymbol_1;
			}
			set
			{
				this.tradingSymbol_1 = value;
			}
		}

		// Token: 0x1700042B RID: 1067
		// (get) Token: 0x06001906 RID: 6406 RVA: 0x000A7978 File Offset: 0x000A5B78
		public bool IfCurrTsAcctSymb
		{
			get
			{
				return this.tradingSymbol_1 != null;
			}
		}

		// Token: 0x1700042C RID: 1068
		// (get) Token: 0x06001907 RID: 6407 RVA: 0x000A7994 File Offset: 0x000A5B94
		// (set) Token: 0x06001908 RID: 6408 RVA: 0x0000A41F File Offset: 0x0000861F
		public List<TradingSymbol> ChangedGlobalSymblsLst
		{
			get
			{
				return this.list_2;
			}
			set
			{
				this.list_2 = value;
			}
		}

		// Token: 0x1700042D RID: 1069
		// (get) Token: 0x06001909 RID: 6409 RVA: 0x000A79AC File Offset: 0x000A5BAC
		// (set) Token: 0x0600190A RID: 6410 RVA: 0x0000A42A File Offset: 0x0000862A
		public List<AcctSymbol> ChangedCurrAcctSymblsLst
		{
			get
			{
				return this.list_1;
			}
			set
			{
				this.list_1 = value;
			}
		}

		// Token: 0x1700042E RID: 1070
		// (get) Token: 0x0600190B RID: 6411 RVA: 0x000A79C4 File Offset: 0x000A5BC4
		// (set) Token: 0x0600190C RID: 6412 RVA: 0x0000A435 File Offset: 0x00008635
		public bool IsInGlobalSymbls
		{
			get
			{
				return this.bool_0;
			}
			set
			{
				this.bool_0 = value;
				if (value)
				{
					this.checkBox_OneSideFee.ThreeState = false;
				}
				else
				{
					this.checkBox_OneSideFee.ThreeState = true;
				}
				this.method_20();
			}
		}

		// Token: 0x1700042F RID: 1071
		// (get) Token: 0x0600190D RID: 6413 RVA: 0x000A79DC File Offset: 0x000A5BDC
		public TradingSymbol CurrSelectedSymb
		{
			get
			{
				return this.listBox_symb.SelectedItem as TradingSymbol;
			}
		}

		// Token: 0x17000430 RID: 1072
		// (get) Token: 0x0600190E RID: 6414 RVA: 0x000A7A00 File Offset: 0x000A5C00
		// (set) Token: 0x0600190F RID: 6415 RVA: 0x000A7A1C File Offset: 0x000A5C1C
		public int CurrSymblLstIndex
		{
			get
			{
				return this.listBox_symb.SelectedIndex;
			}
			set
			{
				try
				{
					this.listBox_symb.SelectedIndex = value;
				}
				catch (Exception exception_)
				{
					Class182.smethod_0(exception_);
				}
			}
		}

		// Token: 0x17000431 RID: 1073
		// (get) Token: 0x06001910 RID: 6416 RVA: 0x000A7A50 File Offset: 0x000A5C50
		// (set) Token: 0x06001911 RID: 6417 RVA: 0x0000A463 File Offset: 0x00008663
		public bool IsBeingClosed
		{
			get
			{
				return this.bool_1;
			}
			set
			{
				this.bool_1 = value;
			}
		}

		// Token: 0x17000432 RID: 1074
		// (get) Token: 0x06001912 RID: 6418 RVA: 0x000A7A68 File Offset: 0x000A5C68
		// (set) Token: 0x06001913 RID: 6419 RVA: 0x0000A46E File Offset: 0x0000866E
		public bool IfShowCurrSymbOnStartup
		{
			get
			{
				return this.bool_2;
			}
			set
			{
				this.bool_2 = value;
			}
		}

		// Token: 0x17000433 RID: 1075
		// (get) Token: 0x06001914 RID: 6420 RVA: 0x000A7A80 File Offset: 0x000A5C80
		// (set) Token: 0x06001915 RID: 6421 RVA: 0x0000A479 File Offset: 0x00008679
		public bool IfFocusOnAutoStopOnStartup
		{
			get
			{
				return this.bool_3;
			}
			set
			{
				this.bool_3 = value;
			}
		}

		// Token: 0x17000434 RID: 1076
		// (get) Token: 0x06001916 RID: 6422 RVA: 0x000A7A98 File Offset: 0x000A5C98
		// (set) Token: 0x06001917 RID: 6423 RVA: 0x0000A484 File Offset: 0x00008684
		public bool IfFocusOnAutoLimitOnStartup
		{
			get
			{
				return this.bool_4;
			}
			set
			{
				this.bool_4 = value;
			}
		}

		// Token: 0x06001918 RID: 6424 RVA: 0x000A7AB0 File Offset: 0x000A5CB0
		public void method_2(StkSymbol stkSymbol_0)
		{
			if (this.IfShowCurrSymbOnStartup)
			{
				SymbParamCtrl.Class312 @class = new SymbParamCtrl.Class312();
				@class.stkSymbol_0 = stkSymbol_0;
				if (@class.stkSymbol_0 == null)
				{
					@class.stkSymbol_0 = Base.UI.CurrSymbol;
				}
				if (@class.stkSymbol_0 != null)
				{
					this.listBox_symb.SelectedItem = (this.listBox_symb.DataSource as List<TradingSymbol>).Single(new Func<TradingSymbol, bool>(@class.method_0));
				}
			}
		}

		// Token: 0x06001919 RID: 6425 RVA: 0x0000A48F File Offset: 0x0000868F
		public void method_3()
		{
			if (this.IfShowCurrSymbOnStartup)
			{
				if (this.IfFocusOnAutoStopOnStartup)
				{
					this.textBox_AutoStPt.Focus();
				}
				else if (this.IfFocusOnAutoLimitOnStartup)
				{
					this.textBox_AutoTfPt.Focus();
				}
			}
		}

		// Token: 0x0600191A RID: 6426 RVA: 0x000A7B1C File Offset: 0x000A5D1C
		private bool method_4()
		{
			return this.method_5(false);
		}

		// Token: 0x0600191B RID: 6427 RVA: 0x000A7B34 File Offset: 0x000A5D34
		private bool method_5(bool bool_5)
		{
			TradingSymbol lastSymblInEdit = this.LastSymblInEdit;
			bool result;
			if (lastSymblInEdit != null)
			{
				bool flag = false;
				object obj = this.method_6(this.textBox_AutoStPt, lastSymblInEdit.AutoStopLossPoints, ref flag);
				object obj2 = this.method_6(this.textBox_AutoTfPt, lastSymblInEdit.AutoLimitTakePoints, ref flag);
				object obj3 = this.method_6(this.textBox_defaultTransUnit, lastSymblInEdit.DefaultUnits, ref flag);
				object obj4 = this.method_6(this.textBox_feeRatio, (lastSymblInEdit.FeeRate != null) ? (lastSymblInEdit.FeeRate * 1000) : null, ref flag);
				if (obj4 != null)
				{
					obj4 = (decimal)obj4 / 1000m;
				}
				object obj5 = this.method_6(this.textBox_feeAmt, lastSymblInEdit.FeePerUnit, ref flag);
				object obj6 = this.method_6(this.textBox_avgSlpg, lastSymblInEdit.AvgSlipg, ref flag);
				object obj7 = this.method_6(this.textBox_marginRate, (lastSymblInEdit.MarginRate != null) ? (lastSymblInEdit.MarginRate * 100) : null, ref flag);
				if (obj7 != null)
				{
					obj7 = (decimal)obj7 / 100m;
				}
				bool? flag2;
				if (this.checkBox_OneSideFee.CheckState == CheckState.Indeterminate)
				{
					flag2 = null;
				}
				else
				{
					flag2 = new bool?(this.checkBox_OneSideFee.Checked);
				}
				bool? flag3 = flag2;
				bool? isOneSideFee = lastSymblInEdit.IsOneSideFee;
				if (!(flag3.GetValueOrDefault() == isOneSideFee.GetValueOrDefault() & flag3 != null == (isOneSideFee != null)))
				{
					flag = true;
				}
				if (flag && !bool_5)
				{
					SymbParamCtrl.Class313 @class = new SymbParamCtrl.Class313();
					@class.tradingSymbol_0 = new TradingSymbol();
					@class.tradingSymbol_0.ID = lastSymblInEdit.ID;
					@class.tradingSymbol_0.ENName = lastSymblInEdit.ENName;
					@class.tradingSymbol_0.CNName = lastSymblInEdit.CNName;
					@class.tradingSymbol_0.Code = lastSymblInEdit.Code;
					@class.tradingSymbol_0.ExchangeID = lastSymblInEdit.ExchangeID;
					@class.tradingSymbol_0.FeeType = lastSymblInEdit.FeeType;
					@class.tradingSymbol_0.TonsPerUnit = lastSymblInEdit.TonsPerUnit;
					@class.tradingSymbol_0.LeastPriceVar = lastSymblInEdit.LeastPriceVar;
					@class.tradingSymbol_0.Type = lastSymblInEdit.Type;
					if (obj == null)
					{
						@class.tradingSymbol_0.AutoStopLossPoints = null;
					}
					else
					{
						@class.tradingSymbol_0.AutoStopLossPoints = new decimal?(Convert.ToDecimal(obj));
					}
					if (obj2 == null)
					{
						@class.tradingSymbol_0.AutoLimitTakePoints = null;
					}
					else
					{
						@class.tradingSymbol_0.AutoLimitTakePoints = new decimal?(Convert.ToDecimal(obj2));
					}
					if (obj3 == null)
					{
						@class.tradingSymbol_0.DefaultUnits = null;
					}
					else
					{
						@class.tradingSymbol_0.DefaultUnits = new int?(Convert.ToInt32(obj3));
					}
					if (obj4 == null)
					{
						@class.tradingSymbol_0.FeeRate = null;
					}
					else
					{
						@class.tradingSymbol_0.FeeRate = new decimal?(Convert.ToDecimal(obj4));
					}
					if (obj5 == null)
					{
						@class.tradingSymbol_0.FeePerUnit = null;
					}
					else
					{
						@class.tradingSymbol_0.FeePerUnit = new decimal?(Convert.ToDecimal(obj5));
					}
					if (obj6 == null)
					{
						@class.tradingSymbol_0.AvgSlipg = null;
					}
					else
					{
						@class.tradingSymbol_0.AvgSlipg = new decimal?(Convert.ToDecimal(obj6));
					}
					if (obj7 == null)
					{
						@class.tradingSymbol_0.MarginRate = null;
					}
					else
					{
						@class.tradingSymbol_0.MarginRate = new decimal?(Convert.ToDecimal(obj7));
					}
					@class.tradingSymbol_0.IsOneSideFee = flag2;
					if (lastSymblInEdit is AcctSymbol)
					{
						SymbParamCtrl.Class314 class2 = new SymbParamCtrl.Class314();
						class2.acctSymbol_0 = new AcctSymbol();
						class2.acctSymbol_0.method_0(@class.tradingSymbol_0);
						class2.acctSymbol_0.AcctID = ((AcctSymbol)lastSymblInEdit).AcctID;
						class2.acctSymbol_0.IfAutoLimitTake = ((AcctSymbol)lastSymblInEdit).IfAutoLimitTake;
						class2.acctSymbol_0.IfAutoStopLoss = ((AcctSymbol)lastSymblInEdit).IfAutoStopLoss;
						try
						{
							this.ChangedCurrAcctSymblsLst.RemoveAll(new Predicate<AcctSymbol>(class2.method_0));
						}
						catch
						{
						}
						this.ChangedCurrAcctSymblsLst.Add(class2.acctSymbol_0);
					}
					else
					{
						try
						{
							this.ChangedGlobalSymblsLst.RemoveAll(new Predicate<TradingSymbol>(@class.method_0));
						}
						catch
						{
						}
						this.ChangedGlobalSymblsLst.Add(@class.tradingSymbol_0);
					}
					result = true;
				}
				else
				{
					result = flag;
				}
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600191C RID: 6428 RVA: 0x000A8088 File Offset: 0x000A6288
		private object method_6(TextBox textBox_0, object object_0, ref bool bool_5)
		{
			object result;
			if (!textBox_0.Enabled)
			{
				result = object_0;
			}
			else
			{
				bool flag = false;
				decimal? num = null;
				if (!string.IsNullOrEmpty(textBox_0.Text.Trim()))
				{
					if (textBox_0.ForeColor != Color.Gray)
					{
						object result2;
						try
						{
							num = new decimal?(Convert.ToDecimal(textBox_0.Text));
							if (object_0 != null)
							{
								object_0 = Convert.ToDecimal(object_0);
								if (num.Value != (decimal)object_0)
								{
									flag = true;
								}
							}
							else
							{
								flag = true;
							}
							goto IL_88;
						}
						catch
						{
							result2 = object_0;
						}
						return result2;
					}
				}
				else if (object_0 != null)
				{
					flag = true;
				}
				IL_88:
				if (!bool_5 && flag)
				{
					bool_5 = true;
				}
				result = num;
			}
			return result;
		}

		// Token: 0x0600191D RID: 6429 RVA: 0x000A8144 File Offset: 0x000A6344
		private void checkBox_OneSideFee_CheckStateChanged(object sender, EventArgs e)
		{
			TradingSymbol tradingSymbol = this.listBox_symb.SelectedItem as TradingSymbol;
			bool flag = false;
			if (tradingSymbol != null)
			{
				if (this.checkBox_OneSideFee.CheckState == CheckState.Indeterminate)
				{
					if (tradingSymbol.IsOneSideFee != null)
					{
						flag = true;
					}
				}
				else if (tradingSymbol.IsOneSideFee == null || tradingSymbol.IsOneSideFee.Value != this.checkBox_OneSideFee.Checked)
				{
					flag = true;
				}
			}
			if (flag)
			{
				this.method_0();
			}
			else
			{
				this.method_11();
			}
		}

		// Token: 0x0600191E RID: 6430 RVA: 0x000A81CC File Offset: 0x000A63CC
		private void textBox_marginRate_KeyPress(object sender, KeyPressEventArgs e)
		{
			if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && e.KeyChar != '.')
			{
				e.Handled = true;
			}
			if (e.KeyChar == '.' && (sender as TextBox).Text.IndexOf('.') > -1)
			{
				e.Handled = true;
			}
		}

		// Token: 0x0600191F RID: 6431 RVA: 0x0000A4C5 File Offset: 0x000086C5
		private void textBox_AutoStPt_TextChanged(object sender, EventArgs e)
		{
			if ((sender as TextBox).ForeColor != Color.Gray)
			{
				this.method_7(this.textBox_AutoStPt, this.CurrSelectedSymb.AutoStopLossPoints);
			}
		}

		// Token: 0x06001920 RID: 6432 RVA: 0x0000A4FC File Offset: 0x000086FC
		private void textBox_AutoStPt_Enter(object sender, EventArgs e)
		{
			if (this.IfCurrTsAcctSymb)
			{
				this.method_16(sender as TextBox, this.GlobalTsOfCurrAcctSymb.AutoStopLossPoints, this.CurrSelectedSymb.AutoStopLossPoints);
			}
		}

		// Token: 0x06001921 RID: 6433 RVA: 0x0000A52A File Offset: 0x0000872A
		private void textBox_AutoStPt_Leave(object sender, EventArgs e)
		{
			this.method_12(this.textBox_AutoStPt);
			if (this.IfCurrTsAcctSymb)
			{
				this.method_14(sender as TextBox, this.GlobalTsOfCurrAcctSymb.AutoStopLossPoints);
			}
		}

		// Token: 0x06001922 RID: 6434 RVA: 0x0000A55A File Offset: 0x0000875A
		private void textBox_AutoTfPt_TextChanged(object sender, EventArgs e)
		{
			if ((sender as TextBox).ForeColor != Color.Gray)
			{
				this.method_7(this.textBox_AutoTfPt, this.CurrSelectedSymb.AutoLimitTakePoints);
			}
		}

		// Token: 0x06001923 RID: 6435 RVA: 0x0000A591 File Offset: 0x00008791
		private void textBox_AutoTfPt_Enter(object sender, EventArgs e)
		{
			if (this.IfCurrTsAcctSymb)
			{
				this.method_16(sender as TextBox, this.GlobalTsOfCurrAcctSymb.AutoLimitTakePoints, this.CurrSelectedSymb.AutoLimitTakePoints);
			}
		}

		// Token: 0x06001924 RID: 6436 RVA: 0x0000A5BF File Offset: 0x000087BF
		private void textBox_AutoTfPt_Leave(object sender, EventArgs e)
		{
			this.method_12(sender as TextBox);
			if (this.IfCurrTsAcctSymb)
			{
				this.method_14(sender as TextBox, this.GlobalTsOfCurrAcctSymb.AutoLimitTakePoints);
			}
		}

		// Token: 0x06001925 RID: 6437 RVA: 0x000A822C File Offset: 0x000A642C
		private void textBox_feeRatio_TextChanged(object sender, EventArgs e)
		{
			if ((sender as TextBox).ForeColor != Color.Gray)
			{
				this.method_8(this.textBox_feeRatio, this.CurrSelectedSymb.FeeRate, new decimal?(1000m));
			}
		}

		// Token: 0x06001926 RID: 6438 RVA: 0x0000A5EF File Offset: 0x000087EF
		private void textBox_feeRatio_Enter(object sender, EventArgs e)
		{
			if (this.IfCurrTsAcctSymb)
			{
				this.method_16(sender as TextBox, this.GlobalTsOfCurrAcctSymb.FeeRate, this.CurrSelectedSymb.FeeRate);
			}
		}

		// Token: 0x06001927 RID: 6439 RVA: 0x0000A61D File Offset: 0x0000881D
		private void textBox_feeRatio_Leave(object sender, EventArgs e)
		{
			this.method_13(this.textBox_feeRatio);
			if (this.IfCurrTsAcctSymb)
			{
				this.method_15(sender as TextBox, this.GlobalTsOfCurrAcctSymb.FeeRate, new decimal?(1000m));
			}
		}

		// Token: 0x06001928 RID: 6440 RVA: 0x0000A65B File Offset: 0x0000885B
		private void textBox_feeAmt_TextChanged(object sender, EventArgs e)
		{
			if ((sender as TextBox).ForeColor != Color.Gray)
			{
				this.method_7(this.textBox_feeAmt, this.CurrSelectedSymb.FeePerUnit);
			}
		}

		// Token: 0x06001929 RID: 6441 RVA: 0x0000A692 File Offset: 0x00008892
		private void textBox_feeAmt_Enter(object sender, EventArgs e)
		{
			if (this.IfCurrTsAcctSymb)
			{
				this.method_16(sender as TextBox, this.GlobalTsOfCurrAcctSymb.FeePerUnit, this.CurrSelectedSymb.FeePerUnit);
			}
		}

		// Token: 0x0600192A RID: 6442 RVA: 0x0000A6C0 File Offset: 0x000088C0
		private void textBox_feeAmt_Leave(object sender, EventArgs e)
		{
			this.method_13(this.textBox_feeAmt);
			if (this.IfCurrTsAcctSymb)
			{
				this.method_14(sender as TextBox, this.GlobalTsOfCurrAcctSymb.FeePerUnit);
			}
		}

		// Token: 0x0600192B RID: 6443 RVA: 0x000A8280 File Offset: 0x000A6480
		private void textBox_marginRate_TextChanged(object sender, EventArgs e)
		{
			if ((sender as TextBox).ForeColor != Color.Gray)
			{
				this.method_8(this.textBox_marginRate, this.CurrSelectedSymb.MarginRate, new decimal?(100m));
			}
		}

		// Token: 0x0600192C RID: 6444 RVA: 0x0000A6EF File Offset: 0x000088EF
		private void textBox_marginRate_Enter(object sender, EventArgs e)
		{
			if (this.IfCurrTsAcctSymb)
			{
				this.method_16(sender as TextBox, this.GlobalTsOfCurrAcctSymb.MarginRate, this.CurrSelectedSymb.MarginRate);
			}
		}

		// Token: 0x0600192D RID: 6445 RVA: 0x000A82D0 File Offset: 0x000A64D0
		private void textBox_marginRate_Leave(object sender, EventArgs e)
		{
			TextBox textBox = sender as TextBox;
			string value = textBox.Text.Trim();
			if (!string.IsNullOrEmpty(value) && !this.bool_1 && Convert.ToDecimal(value) > 100m)
			{
				MessageBox.Show("该参数应小于或等于100%！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				textBox.Focus();
			}
			else
			{
				this.method_12(this.textBox_marginRate);
				if (this.IfCurrTsAcctSymb)
				{
					this.method_15(sender as TextBox, this.GlobalTsOfCurrAcctSymb.MarginRate, new decimal?(100m));
				}
			}
		}

		// Token: 0x0600192E RID: 6446 RVA: 0x0000A71D File Offset: 0x0000891D
		private void textBox_avgSlpg_TextChanged(object sender, EventArgs e)
		{
			if ((sender as TextBox).ForeColor != Color.Gray)
			{
				this.method_7(this.textBox_avgSlpg, this.CurrSelectedSymb.AvgSlipg);
			}
		}

		// Token: 0x0600192F RID: 6447 RVA: 0x0000A754 File Offset: 0x00008954
		private void textBox_avgSlpg_Enter(object sender, EventArgs e)
		{
			if (this.IfCurrTsAcctSymb)
			{
				this.method_16(sender as TextBox, this.GlobalTsOfCurrAcctSymb.AvgSlipg, this.CurrSelectedSymb.AvgSlipg);
			}
		}

		// Token: 0x06001930 RID: 6448 RVA: 0x0000A782 File Offset: 0x00008982
		private void textBox_avgSlpg_Leave(object sender, EventArgs e)
		{
			this.method_13(this.textBox_avgSlpg);
			if (this.IfCurrTsAcctSymb)
			{
				this.method_14(sender as TextBox, this.GlobalTsOfCurrAcctSymb.AvgSlipg);
			}
		}

		// Token: 0x06001931 RID: 6449 RVA: 0x0000A7B1 File Offset: 0x000089B1
		private void textBox_defaultTransUnit_TextChanged(object sender, EventArgs e)
		{
			if ((sender as TextBox).ForeColor != Color.Gray)
			{
				this.method_7(this.textBox_defaultTransUnit, this.CurrSelectedSymb.DefaultUnits);
			}
		}

		// Token: 0x06001932 RID: 6450 RVA: 0x000A836C File Offset: 0x000A656C
		private void textBox_defaultTransUnit_Enter(object sender, EventArgs e)
		{
			if (this.IfCurrTsAcctSymb)
			{
				TextBox textBox_ = sender as TextBox;
				int? defaultUnits = this.GlobalTsOfCurrAcctSymb.DefaultUnits;
				decimal? nullable_ = (defaultUnits != null) ? new decimal?(defaultUnits.GetValueOrDefault()) : null;
				defaultUnits = this.CurrSelectedSymb.DefaultUnits;
				this.method_16(textBox_, nullable_, (defaultUnits != null) ? new decimal?(defaultUnits.GetValueOrDefault()) : null);
			}
		}

		// Token: 0x06001933 RID: 6451 RVA: 0x000A83F4 File Offset: 0x000A65F4
		private void textBox_defaultTransUnit_Leave(object sender, EventArgs e)
		{
			this.method_12(this.textBox_defaultTransUnit);
			if (this.IfCurrTsAcctSymb)
			{
				TextBox textBox_ = sender as TextBox;
				int? defaultUnits = this.GlobalTsOfCurrAcctSymb.DefaultUnits;
				this.method_14(textBox_, (defaultUnits != null) ? new decimal?(defaultUnits.GetValueOrDefault()) : null);
			}
		}

		// Token: 0x06001934 RID: 6452 RVA: 0x000A8458 File Offset: 0x000A6658
		private void method_7(TextBox textBox_0, object object_0)
		{
			this.method_8(textBox_0, object_0, null);
		}

		// Token: 0x06001935 RID: 6453 RVA: 0x0000A7E8 File Offset: 0x000089E8
		private void method_8(TextBox textBox_0, object object_0, decimal? nullable_0)
		{
			if (this.method_10(textBox_0, object_0, nullable_0))
			{
				this.method_0();
			}
			else
			{
				this.method_11();
			}
		}

		// Token: 0x06001936 RID: 6454 RVA: 0x000A8478 File Offset: 0x000A6678
		private bool method_9(TextBox textBox_0, object object_0)
		{
			return this.method_10(textBox_0, object_0, null);
		}

		// Token: 0x06001937 RID: 6455 RVA: 0x000A849C File Offset: 0x000A669C
		private bool method_10(TextBox textBox_0, object object_0, decimal? nullable_0)
		{
			bool result = false;
			string value = textBox_0.Text.Trim();
			if (string.IsNullOrEmpty(value))
			{
				if (object_0 != null)
				{
					result = true;
				}
			}
			else
			{
				try
				{
					decimal d = Convert.ToDecimal(value);
					if (nullable_0 != null)
					{
						d /= nullable_0.Value;
					}
					if (object_0 != null)
					{
						if (d != Convert.ToDecimal(object_0))
						{
							result = true;
						}
					}
					else
					{
						result = true;
					}
				}
				catch
				{
				}
			}
			return result;
		}

		// Token: 0x06001938 RID: 6456 RVA: 0x000A8518 File Offset: 0x000A6718
		private void method_11()
		{
			if (!this.method_5(true))
			{
				if ((this.ChangedCurrAcctSymblsLst != null && this.ChangedCurrAcctSymblsLst.Count >= 1) || (this.ChangedGlobalSymblsLst != null && this.ChangedGlobalSymblsLst.Count >= 1))
				{
					if (this.IfCurrTsAcctSymb)
					{
						if (this.ChangedCurrAcctSymblsLst != null && this.ChangedCurrAcctSymblsLst.Count > 0)
						{
							SymbParamCtrl.Class315 @class = new SymbParamCtrl.Class315();
							@class.acctSymbol_0 = (this.CurrSelectedSymb as AcctSymbol);
							if (this.ChangedCurrAcctSymblsLst.Where(new Func<AcctSymbol, bool>(@class.method_0)).Any<AcctSymbol>())
							{
								this.ChangedCurrAcctSymblsLst.RemoveAll(new Predicate<AcctSymbol>(@class.method_1));
								this.method_1();
							}
						}
					}
					else if (this.ChangedGlobalSymblsLst != null && this.ChangedGlobalSymblsLst.Count > 0 && this.ChangedGlobalSymblsLst.Where(new Func<TradingSymbol, bool>(this.method_25)).Any<TradingSymbol>())
					{
						this.ChangedGlobalSymblsLst.RemoveAll(new Predicate<TradingSymbol>(this.method_26));
						this.method_1();
					}
				}
				else
				{
					this.method_1();
				}
			}
		}

		// Token: 0x06001939 RID: 6457 RVA: 0x000A863C File Offset: 0x000A683C
		private bool method_12(TextBox textBox_0)
		{
			string value = textBox_0.Text.Trim();
			bool result;
			if (!string.IsNullOrEmpty(value) && !this.bool_1 && Convert.ToDecimal(value) == 0m)
			{
				MessageBox.Show("该参数不能为零！", "提醒", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
				textBox_0.Focus();
				result = true;
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x0600193A RID: 6458 RVA: 0x000A869C File Offset: 0x000A689C
		private void method_13(TextBox textBox_0)
		{
			string value = textBox_0.Text.Trim();
			if (!string.IsNullOrEmpty(value) && Convert.ToDecimal(value) == 0m)
			{
				textBox_0.Text = "0";
			}
		}

		// Token: 0x0600193B RID: 6459 RVA: 0x000A86DC File Offset: 0x000A68DC
		private void method_14(TextBox textBox_0, decimal? nullable_0)
		{
			this.method_15(textBox_0, nullable_0, null);
		}

		// Token: 0x0600193C RID: 6460 RVA: 0x000A86FC File Offset: 0x000A68FC
		private void method_15(TextBox textBox_0, decimal? nullable_0, decimal? nullable_1)
		{
			if (this.IfCurrTsAcctSymb)
			{
				if (string.IsNullOrEmpty(textBox_0.Text.Trim()))
				{
					try
					{
						if (nullable_0 != null)
						{
							textBox_0.ForeColor = Color.Gray;
							if (nullable_1 != null)
							{
								nullable_0 *= nullable_1;
							}
							textBox_0.Text = Utility.GetStringWithoutEndZero(new decimal?(nullable_0.Value));
						}
						else
						{
							textBox_0.ForeColor = Color.Black;
							textBox_0.Text = string.Empty;
						}
						return;
					}
					catch
					{
						throw;
					}
				}
				textBox_0.ForeColor = Color.Black;
			}
		}

		// Token: 0x0600193D RID: 6461 RVA: 0x000A87D4 File Offset: 0x000A69D4
		private void method_16(TextBox textBox_0, decimal? nullable_0, decimal? nullable_1)
		{
			if (this.IfCurrTsAcctSymb && nullable_0 != null && nullable_1 == null && textBox_0.ForeColor == Color.Gray)
			{
				textBox_0.Text = string.Empty;
				textBox_0.ForeColor = default(Color);
			}
		}

		// Token: 0x0600193E RID: 6462 RVA: 0x000A882C File Offset: 0x000A6A2C
		private void method_17(TextBox textBox_0, decimal? nullable_0, decimal? nullable_1)
		{
			this.method_18(textBox_0, nullable_0, nullable_1, null);
		}

		// Token: 0x0600193F RID: 6463 RVA: 0x000A8850 File Offset: 0x000A6A50
		private void method_18(TextBox textBox_0, decimal? nullable_0, decimal? nullable_1, decimal? nullable_2)
		{
			if (this.IfCurrTsAcctSymb && nullable_0 == null)
			{
				if (nullable_1 != null)
				{
					if (nullable_2 != null)
					{
						nullable_1 *= nullable_2;
					}
					textBox_0.Text = Utility.GetStringWithoutEndZero(nullable_1);
					textBox_0.ForeColor = Color.Gray;
				}
				else
				{
					textBox_0.Text = string.Empty;
					textBox_0.ForeColor = default(Color);
				}
			}
			else
			{
				if (nullable_0 != null)
				{
					if (nullable_2 != null)
					{
						nullable_0 *= nullable_2;
					}
					textBox_0.Text = Utility.GetStringWithoutEndZero(nullable_0);
				}
				else
				{
					textBox_0.Text = string.Empty;
				}
				textBox_0.ForeColor = default(Color);
			}
		}

		// Token: 0x06001940 RID: 6464 RVA: 0x000A8970 File Offset: 0x000A6B70
		private string method_19(decimal? nullable_0)
		{
			string result;
			if (nullable_0 != null)
			{
				string text = nullable_0.Value.ToString();
				if (nullable_0.Value < 1m)
				{
					text = Utility.GetStringWithoutEndZero(new decimal?(nullable_0.Value));
				}
				result = text;
			}
			else
			{
				result = string.Empty;
			}
			return result;
		}

		// Token: 0x06001941 RID: 6465 RVA: 0x000A89C8 File Offset: 0x000A6BC8
		private void method_20()
		{
			TradingSymbol tradingSymbol = this.listBox_symb.SelectedItem as TradingSymbol;
			if (tradingSymbol != null && tradingSymbol.IsOneSideFee != null)
			{
				this.checkBox_OneSideFee.Checked = tradingSymbol.IsOneSideFee.Value;
			}
			else if (!this.IsInGlobalSymbls)
			{
				this.checkBox_OneSideFee.CheckState = CheckState.Indeterminate;
			}
		}

		// Token: 0x06001942 RID: 6466 RVA: 0x000A8A2C File Offset: 0x000A6C2C
		public void method_21()
		{
			this.method_4();
			if (this.ChangedGlobalSymblsLst != null && this.ChangedGlobalSymblsLst.Count > 0)
			{
				foreach (TradingSymbol tradingSymbol in this.ChangedGlobalSymblsLst)
				{
					TradingSymbol tradingSymbol_ = SymbMgr.smethod_37(tradingSymbol.ID);
					this.method_22(tradingSymbol_, tradingSymbol, false);
				}
				SymbMgr.smethod_33();
				this.ChangedGlobalSymblsLst = new List<TradingSymbol>();
			}
			if (this.ChangedCurrAcctSymblsLst != null && this.ChangedCurrAcctSymblsLst.Count > 0)
			{
				foreach (AcctSymbol acctSymbol in this.ChangedCurrAcctSymblsLst)
				{
					AcctSymbol tradingSymbol_2 = Base.Acct.smethod_30(acctSymbol.ID);
					this.method_22(tradingSymbol_2, acctSymbol, true);
				}
				Base.Acct.smethod_38();
				this.ChangedCurrAcctSymblsLst = new List<AcctSymbol>();
			}
			SymbParamCtrl.smethod_0();
		}

		// Token: 0x06001943 RID: 6467 RVA: 0x000A8B40 File Offset: 0x000A6D40
		private void method_22(TradingSymbol tradingSymbol_2, TradingSymbol tradingSymbol_3, bool bool_5)
		{
			if (tradingSymbol_3.AutoStopLossPoints == null || tradingSymbol_3.AutoStopLossPoints.Value > 0m)
			{
				tradingSymbol_2.AutoStopLossPoints = tradingSymbol_3.AutoStopLossPoints;
			}
			if (tradingSymbol_3.AutoLimitTakePoints != null)
			{
				decimal? num = tradingSymbol_3.AutoLimitTakePoints;
				decimal d = 0m;
				if (!(num.GetValueOrDefault() > d & num != null))
				{
					goto IL_78;
				}
			}
			tradingSymbol_2.AutoLimitTakePoints = tradingSymbol_3.AutoLimitTakePoints;
			IL_78:
			if (!bool_5)
			{
				if (bool_5 || tradingSymbol_3.FeeRate == null)
				{
					goto IL_C0;
				}
				decimal? num = tradingSymbol_3.FeeRate;
				decimal d = 0m;
				if (!(num.GetValueOrDefault() >= d & num != null))
				{
					goto IL_C0;
				}
			}
			tradingSymbol_2.FeeRate = tradingSymbol_3.FeeRate;
			IL_C0:
			if (!bool_5)
			{
				if (bool_5 || tradingSymbol_3.FeePerUnit == null)
				{
					goto IL_108;
				}
				decimal? num = tradingSymbol_3.FeePerUnit;
				decimal d = 0m;
				if (!(num.GetValueOrDefault() >= d & num != null))
				{
					goto IL_108;
				}
			}
			tradingSymbol_2.FeePerUnit = tradingSymbol_3.FeePerUnit;
			IL_108:
			if (!bool_5)
			{
				if (bool_5 || tradingSymbol_3.MarginRate == null)
				{
					goto IL_150;
				}
				decimal? num = tradingSymbol_3.MarginRate;
				decimal d = 0m;
				if (!(num.GetValueOrDefault() > d & num != null))
				{
					goto IL_150;
				}
			}
			tradingSymbol_2.MarginRate = tradingSymbol_3.MarginRate;
			IL_150:
			if (!bool_5)
			{
				if (bool_5 || tradingSymbol_3.AvgSlipg == null)
				{
					goto IL_198;
				}
				decimal? num = tradingSymbol_3.AvgSlipg;
				decimal d = 0m;
				if (!(num.GetValueOrDefault() >= d & num != null))
				{
					goto IL_198;
				}
			}
			tradingSymbol_2.AvgSlipg = tradingSymbol_3.AvgSlipg;
			IL_198:
			if (tradingSymbol_3.DefaultUnits == null || tradingSymbol_3.DefaultUnits.Value >= 1)
			{
				tradingSymbol_2.DefaultUnits = tradingSymbol_3.DefaultUnits;
			}
			if (tradingSymbol_3.IsOneSideFee != null)
			{
				tradingSymbol_2.IsOneSideFee = tradingSymbol_3.IsOneSideFee;
			}
			else if (tradingSymbol_2 is AcctSymbol && bool_5)
			{
				(tradingSymbol_2 as AcctSymbol).IsOneSideFee = null;
			}
		}

		// Token: 0x06001944 RID: 6468 RVA: 0x000A8D54 File Offset: 0x000A6F54
		public static void smethod_0()
		{
			if (Base.Data.SymbDataSets != null)
			{
				foreach (SymbDataSet symbDataSet in Base.Data.SymbDataSets)
				{
					symbDataSet.method_74();
				}
			}
		}

		// Token: 0x06001945 RID: 6469 RVA: 0x000A8DB0 File Offset: 0x000A6FB0
		private void listBox_symb_SelectedIndexChanged(object sender, EventArgs e)
		{
			SymbParamCtrl.Class316 @class = new SymbParamCtrl.Class316();
			this.method_4();
			@class.tradingSymbol_0 = (this.listBox_symb.SelectedItem as TradingSymbol);
			this.LastSymblInEdit = @class.tradingSymbol_0;
			if (@class.tradingSymbol_0 is AcctSymbol)
			{
				try
				{
					if (this.ChangedGlobalSymblsLst != null)
					{
						AcctSymbol acctSymbol = this.ChangedCurrAcctSymblsLst.SingleOrDefault(new Func<AcctSymbol, bool>(@class.method_0));
						if (acctSymbol != null)
						{
							@class.tradingSymbol_0 = acctSymbol;
						}
					}
				}
				catch
				{
				}
				try
				{
					TradingSymbol tradingSymbol = SymbMgr.LocalMstSymbolList.SingleOrDefault(new Func<TradingSymbol, bool>(@class.method_1));
					if (tradingSymbol != null)
					{
						this.GlobalTsOfCurrAcctSymb = tradingSymbol;
					}
					goto IL_C9;
				}
				catch
				{
					throw;
				}
			}
			try
			{
				if (this.ChangedGlobalSymblsLst != null)
				{
					TradingSymbol tradingSymbol2 = this.ChangedGlobalSymblsLst.SingleOrDefault(new Func<TradingSymbol, bool>(@class.method_2));
					if (tradingSymbol2 != null)
					{
						@class.tradingSymbol_0 = tradingSymbol2;
					}
				}
			}
			catch
			{
			}
			this.GlobalTsOfCurrAcctSymb = null;
			IL_C9:
			this.method_17(this.textBox_AutoStPt, @class.tradingSymbol_0.AutoStopLossPoints, this.IfCurrTsAcctSymb ? this.GlobalTsOfCurrAcctSymb.AutoStopLossPoints : null);
			this.method_17(this.textBox_AutoTfPt, @class.tradingSymbol_0.AutoLimitTakePoints, this.IfCurrTsAcctSymb ? this.GlobalTsOfCurrAcctSymb.AutoLimitTakePoints : null);
			this.method_17(this.textBox_avgSlpg, @class.tradingSymbol_0.AvgSlipg, this.IfCurrTsAcctSymb ? this.GlobalTsOfCurrAcctSymb.AvgSlipg : null);
			TextBox textBox_ = this.textBox_defaultTransUnit;
			int? num = @class.tradingSymbol_0.DefaultUnits;
			decimal? nullable_ = (num != null) ? new decimal?(num.GetValueOrDefault()) : null;
			num = (this.IfCurrTsAcctSymb ? this.GlobalTsOfCurrAcctSymb.DefaultUnits : null);
			this.method_17(textBox_, nullable_, (num != null) ? new decimal?(num.GetValueOrDefault()) : null);
			this.method_18(this.textBox_marginRate, @class.tradingSymbol_0.MarginRate, this.IfCurrTsAcctSymb ? this.GlobalTsOfCurrAcctSymb.MarginRate : null, new decimal?(100m));
			if (@class.tradingSymbol_0.FeeType == FeeType.FixedAmountPerUnit)
			{
				this.textBox_feeRatio.Enabled = false;
				this.textBox_feeRatio.Text = string.Empty;
				this.radioBtn_FeeRatio.Enabled = false;
				this.label_feeRatioSymbl.Enabled = false;
				this.textBox_feeAmt.Enabled = true;
				this.radioBtn_FeeFixAmt.Enabled = true;
				this.radioBtn_FeeFixAmt.Checked = true;
				this.label_feeAmtUnit.Enabled = true;
				this.method_17(this.textBox_feeAmt, @class.tradingSymbol_0.FeePerUnit, this.IfCurrTsAcctSymb ? this.GlobalTsOfCurrAcctSymb.FeePerUnit : null);
			}
			else
			{
				this.textBox_feeAmt.Enabled = false;
				this.textBox_feeAmt.Text = string.Empty;
				this.radioBtn_FeeFixAmt.Enabled = false;
				this.label_feeAmtUnit.Enabled = false;
				this.textBox_feeRatio.Enabled = true;
				this.radioBtn_FeeRatio.Enabled = true;
				this.radioBtn_FeeRatio.Checked = true;
				this.label_feeRatioSymbl.Enabled = true;
				this.method_18(this.textBox_feeRatio, @class.tradingSymbol_0.FeeRate, this.IfCurrTsAcctSymb ? this.GlobalTsOfCurrAcctSymb.FeeRate : null, new decimal?(1000m));
			}
			if (@class.tradingSymbol_0 is AcctSymbol)
			{
				this.checkBox_OneSideFee.ThreeState = true;
			}
			else
			{
				this.checkBox_OneSideFee.ThreeState = false;
			}
			if (@class.tradingSymbol_0.IsOneSideFee != null)
			{
				if (@class.tradingSymbol_0.IsOneSideFee.Value)
				{
					this.checkBox_OneSideFee.Checked = true;
				}
				else
				{
					this.checkBox_OneSideFee.Checked = false;
				}
			}
			else if (this.checkBox_OneSideFee.ThreeState)
			{
				this.checkBox_OneSideFee.CheckState = CheckState.Indeterminate;
			}
			this.textBox_SymbName.Text = @class.tradingSymbol_0.AbbrCNName;
			this.textBox_SymbCode.Text = @class.tradingSymbol_0.AbbrCode;
			this.textBox_leastPrcVar.Text = ((@class.tradingSymbol_0.LeastPriceVar != null) ? Utility.GetStringWithoutEndZero(new decimal?(@class.tradingSymbol_0.LeastPriceVar.Value)) : string.Empty);
			this.textBox_Exchg.Text = SymbMgr.smethod_1(@class.tradingSymbol_0.ExchangeID).AbbrName_CN;
			this.textBox_TonsPerUnit.Text = ((@class.tradingSymbol_0.TonsPerUnit != null) ? @class.tradingSymbol_0.TonsPerUnit.Value.ToString() : string.Empty);
			this.textBox_leastPrcVar.Text = ((@class.tradingSymbol_0.LeastPriceVar != null) ? Utility.GetStringWithoutEndZero(new decimal?(@class.tradingSymbol_0.LeastPriceVar.Value)) : string.Empty);
			if (@class.tradingSymbol_0.ExchangeID != 1 && @class.tradingSymbol_0.ExchangeID != 5)
			{
				if (@class.tradingSymbol_0.ExchangeID != 6)
				{
					this.label_tonsPerUnit.Text = "交易单位:";
					this.label_tonsPerUnitMeasure.Text = "吨/手";
					goto IL_59F;
				}
			}
			this.label_tonsPerUnit.Text = "合约乘数:";
			this.label_tonsPerUnitMeasure.Text = "元/点";
			IL_59F:
			if (@class.tradingSymbol_0.AbbrCode.ToLower() == "au")
			{
				this.label_tonsPerUnitMeasure.Text = "克/点";
			}
			else if (@class.tradingSymbol_0.AbbrCode.ToLower() == "ag")
			{
				this.label_tonsPerUnitMeasure.Text = "kg/点";
			}
			this.method_20();
		}

		// Token: 0x06001946 RID: 6470 RVA: 0x0000A805 File Offset: 0x00008A05
		public void method_23()
		{
			this.textBox_AutoStPt.Focus();
		}

		// Token: 0x06001947 RID: 6471 RVA: 0x0000A815 File Offset: 0x00008A15
		public void method_24()
		{
			this.textBox_AutoTfPt.Focus();
		}

		// Token: 0x06001948 RID: 6472 RVA: 0x0000A825 File Offset: 0x00008A25
		protected void Dispose(bool disposing)
		{
			if (disposing && this.icontainer_0 != null)
			{
				this.icontainer_0.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001949 RID: 6473 RVA: 0x000A93E8 File Offset: 0x000A75E8
		private void InitializeComponent()
		{
			this.listBox_symb = new ListBox();
			this.label1 = new Label();
			this.groupBox_Fee = new GroupBox();
			this.checkBox_OneSideFee = new CheckBox();
			this.panel1 = new Panel();
			this.label_feeAmtUnit = new Label();
			this.textBox_feeAmt = new TextBox();
			this.radioBtn_FeeFixAmt = new RadioButton();
			this.label_feeRatioSymbl = new Label();
			this.textBox_feeRatio = new TextBox();
			this.radioBtn_FeeRatio = new RadioButton();
			this.label4 = new Label();
			this.textBox_AutoStPt = new TextBox();
			this.textBox_AutoTfPt = new TextBox();
			this.groupBox_StopLoss = new GroupBox();
			this.label2 = new Label();
			this.textBox_marginRate = new TextBox();
			this.label3 = new Label();
			this.textBox_avgSlpg = new TextBox();
			this.label5 = new Label();
			this.textBox_leastPrcVar = new TextBox();
			this.label6 = new Label();
			this.textBox_defaultTransUnit = new TextBox();
			this.label11 = new Label();
			this.groupBox_Bottom = new GroupBox();
			this.groupBox_BaseInfo = new GroupBox();
			this.label_tonsPerUnitMeasure = new Label();
			this.textBox_TonsPerUnit = new TextBox();
			this.label_tonsPerUnit = new Label();
			this.textBox_Exchg = new TextBox();
			this.label9 = new Label();
			this.label7 = new Label();
			this.textBox_SymbName = new TextBox();
			this.textBox_SymbCode = new TextBox();
			this.label8 = new Label();
			this.groupBox_Others = new GroupBox();
			this.groupBox_Fee.SuspendLayout();
			this.panel1.SuspendLayout();
			this.groupBox_StopLoss.SuspendLayout();
			this.groupBox_Bottom.SuspendLayout();
			this.groupBox_BaseInfo.SuspendLayout();
			this.groupBox_Others.SuspendLayout();
			base.SuspendLayout();
			this.listBox_symb.BorderStyle = BorderStyle.None;
			this.listBox_symb.Font = new Font("SimSun", 9f);
			this.listBox_symb.FormattingEnabled = true;
			this.listBox_symb.ItemHeight = 15;
			this.listBox_symb.Location = new Point(18, 30);
			this.listBox_symb.Name = "listBox_symb";
			this.listBox_symb.Size = new Size(160, 300);
			this.listBox_symb.TabIndex = 15;
			this.label1.AutoSize = true;
			this.label1.Location = new Point(17, 28);
			this.label1.Name = "label1";
			this.label1.Size = new Size(105, 15);
			this.label1.TabIndex = 1;
			this.label1.Text = "自动止损点数:";
			this.groupBox_Fee.Controls.Add(this.checkBox_OneSideFee);
			this.groupBox_Fee.Controls.Add(this.panel1);
			this.groupBox_Fee.Location = new Point(194, 118);
			this.groupBox_Fee.Name = "groupBox_Fee";
			this.groupBox_Fee.Size = new Size(252, 115);
			this.groupBox_Fee.TabIndex = 2;
			this.groupBox_Fee.TabStop = false;
			this.groupBox_Fee.Text = "手续费";
			this.checkBox_OneSideFee.AutoSize = true;
			this.checkBox_OneSideFee.Location = new Point(14, 85);
			this.checkBox_OneSideFee.Name = "checkBox_OneSideFee";
			this.checkBox_OneSideFee.Size = new Size(89, 19);
			this.checkBox_OneSideFee.TabIndex = 6;
			this.checkBox_OneSideFee.Text = "日内单边";
			this.checkBox_OneSideFee.ThreeState = true;
			this.checkBox_OneSideFee.UseVisualStyleBackColor = true;
			this.panel1.Controls.Add(this.label_feeAmtUnit);
			this.panel1.Controls.Add(this.textBox_feeAmt);
			this.panel1.Controls.Add(this.radioBtn_FeeFixAmt);
			this.panel1.Controls.Add(this.label_feeRatioSymbl);
			this.panel1.Controls.Add(this.textBox_feeRatio);
			this.panel1.Controls.Add(this.radioBtn_FeeRatio);
			this.panel1.Location = new Point(10, 19);
			this.panel1.Name = "panel1";
			this.panel1.Size = new Size(239, 65);
			this.panel1.TabIndex = 10;
			this.label_feeAmtUnit.AutoSize = true;
			this.label_feeAmtUnit.Location = new Point(205, 38);
			this.label_feeAmtUnit.Name = "label_feeAmtUnit";
			this.label_feeAmtUnit.Size = new Size(22, 15);
			this.label_feeAmtUnit.TabIndex = 12;
			this.label_feeAmtUnit.Text = "元";
			this.textBox_feeAmt.Location = new Point(139, 35);
			this.textBox_feeAmt.Name = "textBox_feeAmt";
			this.textBox_feeAmt.Size = new Size(63, 25);
			this.textBox_feeAmt.TabIndex = 5;
			this.textBox_feeAmt.TextAlign = HorizontalAlignment.Center;
			this.radioBtn_FeeFixAmt.AutoSize = true;
			this.radioBtn_FeeFixAmt.Location = new Point(4, 36);
			this.radioBtn_FeeFixAmt.Name = "radioBtn_FeeFixAmt";
			this.radioBtn_FeeFixAmt.Size = new Size(126, 19);
			this.radioBtn_FeeFixAmt.TabIndex = 4;
			this.radioBtn_FeeFixAmt.TabStop = true;
			this.radioBtn_FeeFixAmt.Text = "每手固定金额:";
			this.radioBtn_FeeFixAmt.UseVisualStyleBackColor = true;
			this.label_feeRatioSymbl.AutoSize = true;
			this.label_feeRatioSymbl.Font = new Font("SimSun", 8.530189f);
			this.label_feeRatioSymbl.Location = new Point(208, 10);
			this.label_feeRatioSymbl.Name = "label_feeRatioSymbl";
			this.label_feeRatioSymbl.Size = new Size(22, 15);
			this.label_feeRatioSymbl.TabIndex = 2;
			this.label_feeRatioSymbl.Text = "‰";
			this.textBox_feeRatio.Location = new Point(139, 5);
			this.textBox_feeRatio.Name = "textBox_feeRatio";
			this.textBox_feeRatio.Size = new Size(63, 25);
			this.textBox_feeRatio.TabIndex = 3;
			this.textBox_feeRatio.TextAlign = HorizontalAlignment.Center;
			this.radioBtn_FeeRatio.AutoSize = true;
			this.radioBtn_FeeRatio.Location = new Point(4, 7);
			this.radioBtn_FeeRatio.Name = "radioBtn_FeeRatio";
			this.radioBtn_FeeRatio.Size = new Size(126, 19);
			this.radioBtn_FeeRatio.TabIndex = 2;
			this.radioBtn_FeeRatio.TabStop = true;
			this.radioBtn_FeeRatio.Text = "按交易额比例:";
			this.radioBtn_FeeRatio.UseVisualStyleBackColor = true;
			this.label4.AutoSize = true;
			this.label4.Location = new Point(17, 57);
			this.label4.Name = "label4";
			this.label4.Size = new Size(105, 15);
			this.label4.TabIndex = 5;
			this.label4.Text = "自动止盈点数:";
			this.textBox_AutoStPt.Location = new Point(140, 24);
			this.textBox_AutoStPt.Name = "textBox_AutoStPt";
			this.textBox_AutoStPt.Size = new Size(78, 25);
			this.textBox_AutoStPt.TabIndex = 0;
			this.textBox_AutoStPt.TextAlign = HorizontalAlignment.Center;
			this.textBox_AutoTfPt.Location = new Point(140, 54);
			this.textBox_AutoTfPt.Name = "textBox_AutoTfPt";
			this.textBox_AutoTfPt.Size = new Size(78, 25);
			this.textBox_AutoTfPt.TabIndex = 1;
			this.textBox_AutoTfPt.TextAlign = HorizontalAlignment.Center;
			this.groupBox_StopLoss.Controls.Add(this.textBox_AutoTfPt);
			this.groupBox_StopLoss.Controls.Add(this.label1);
			this.groupBox_StopLoss.Controls.Add(this.label4);
			this.groupBox_StopLoss.Controls.Add(this.textBox_AutoStPt);
			this.groupBox_StopLoss.Location = new Point(194, 21);
			this.groupBox_StopLoss.Name = "groupBox_StopLoss";
			this.groupBox_StopLoss.Size = new Size(252, 90);
			this.groupBox_StopLoss.TabIndex = 14;
			this.groupBox_StopLoss.TabStop = false;
			this.groupBox_StopLoss.Text = "止损止盈";
			this.label2.Location = new Point(17, 26);
			this.label2.Name = "label2";
			this.label2.Size = new Size(118, 20);
			this.label2.TabIndex = 15;
			this.label2.Text = "保证金比例:";
			this.label2.TextAlign = ContentAlignment.TopRight;
			this.textBox_marginRate.Location = new Point(139, 23);
			this.textBox_marginRate.Name = "textBox_marginRate";
			this.textBox_marginRate.Size = new Size(78, 25);
			this.textBox_marginRate.TabIndex = 12;
			this.textBox_marginRate.TextAlign = HorizontalAlignment.Center;
			this.label3.AutoSize = true;
			this.label3.Font = new Font("SimSun", 10f, FontStyle.Regular, GraphicsUnit.Point, 134);
			this.label3.Location = new Point(223, 26);
			this.label3.Name = "label3";
			this.label3.Size = new Size(17, 17);
			this.label3.TabIndex = 17;
			this.label3.Text = "%";
			this.textBox_avgSlpg.Location = new Point(139, 53);
			this.textBox_avgSlpg.Name = "textBox_avgSlpg";
			this.textBox_avgSlpg.Size = new Size(78, 25);
			this.textBox_avgSlpg.TabIndex = 13;
			this.textBox_avgSlpg.TextAlign = HorizontalAlignment.Center;
			this.label5.Location = new Point(17, 56);
			this.label5.Name = "label5";
			this.label5.Size = new Size(118, 20);
			this.label5.TabIndex = 18;
			this.label5.Text = "交易滑点:";
			this.label5.TextAlign = ContentAlignment.TopRight;
			this.textBox_leastPrcVar.Enabled = false;
			this.textBox_leastPrcVar.Location = new Point(128, 146);
			this.textBox_leastPrcVar.Name = "textBox_leastPrcVar";
			this.textBox_leastPrcVar.Size = new Size(61, 25);
			this.textBox_leastPrcVar.TabIndex = 11;
			this.textBox_leastPrcVar.TextAlign = HorizontalAlignment.Center;
			this.label6.Location = new Point(5, 149);
			this.label6.Name = "label6";
			this.label6.Size = new Size(118, 20);
			this.label6.TabIndex = 20;
			this.label6.Text = "最小变动价位:";
			this.label6.TextAlign = ContentAlignment.TopRight;
			this.textBox_defaultTransUnit.Location = new Point(395, 23);
			this.textBox_defaultTransUnit.Name = "textBox_defaultTransUnit";
			this.textBox_defaultTransUnit.Size = new Size(78, 25);
			this.textBox_defaultTransUnit.TabIndex = 14;
			this.textBox_defaultTransUnit.TextAlign = HorizontalAlignment.Center;
			this.label11.Location = new Point(273, 27);
			this.label11.Name = "label11";
			this.label11.Size = new Size(118, 20);
			this.label11.TabIndex = 22;
			this.label11.Text = "默认开仓手数:";
			this.label11.TextAlign = ContentAlignment.TopRight;
			this.groupBox_Bottom.Controls.Add(this.groupBox_BaseInfo);
			this.groupBox_Bottom.Controls.Add(this.groupBox_Others);
			this.groupBox_Bottom.Controls.Add(this.listBox_symb);
			this.groupBox_Bottom.Controls.Add(this.groupBox_StopLoss);
			this.groupBox_Bottom.Controls.Add(this.groupBox_Fee);
			this.groupBox_Bottom.Dock = DockStyle.Fill;
			this.groupBox_Bottom.Location = new Point(0, 0);
			this.groupBox_Bottom.Name = "groupBox_Bottom";
			this.groupBox_Bottom.Size = new Size(722, 348);
			this.groupBox_Bottom.TabIndex = 25;
			this.groupBox_Bottom.TabStop = false;
			this.groupBox_BaseInfo.Controls.Add(this.label_tonsPerUnitMeasure);
			this.groupBox_BaseInfo.Controls.Add(this.textBox_TonsPerUnit);
			this.groupBox_BaseInfo.Controls.Add(this.label_tonsPerUnit);
			this.groupBox_BaseInfo.Controls.Add(this.textBox_Exchg);
			this.groupBox_BaseInfo.Controls.Add(this.label9);
			this.groupBox_BaseInfo.Controls.Add(this.textBox_leastPrcVar);
			this.groupBox_BaseInfo.Controls.Add(this.label6);
			this.groupBox_BaseInfo.Controls.Add(this.label7);
			this.groupBox_BaseInfo.Controls.Add(this.textBox_SymbName);
			this.groupBox_BaseInfo.Controls.Add(this.textBox_SymbCode);
			this.groupBox_BaseInfo.Controls.Add(this.label8);
			this.groupBox_BaseInfo.Location = new Point(461, 21);
			this.groupBox_BaseInfo.Name = "groupBox_BaseInfo";
			this.groupBox_BaseInfo.Size = new Size(246, 189);
			this.groupBox_BaseInfo.TabIndex = 29;
			this.groupBox_BaseInfo.TabStop = false;
			this.groupBox_BaseInfo.Text = "品种信息";
			this.label_tonsPerUnitMeasure.AutoSize = true;
			this.label_tonsPerUnitMeasure.Location = new Point(195, 120);
			this.label_tonsPerUnitMeasure.Name = "label_tonsPerUnitMeasure";
			this.label_tonsPerUnitMeasure.Size = new Size(45, 15);
			this.label_tonsPerUnitMeasure.TabIndex = 34;
			this.label_tonsPerUnitMeasure.Text = "吨/手";
			this.textBox_TonsPerUnit.Enabled = false;
			this.textBox_TonsPerUnit.Location = new Point(128, 115);
			this.textBox_TonsPerUnit.Name = "textBox_TonsPerUnit";
			this.textBox_TonsPerUnit.Size = new Size(61, 25);
			this.textBox_TonsPerUnit.TabIndex = 10;
			this.textBox_TonsPerUnit.TextAlign = HorizontalAlignment.Center;
			this.label_tonsPerUnit.Location = new Point(5, 120);
			this.label_tonsPerUnit.Name = "label_tonsPerUnit";
			this.label_tonsPerUnit.Size = new Size(118, 20);
			this.label_tonsPerUnit.TabIndex = 32;
			this.label_tonsPerUnit.Text = "交易单位:";
			this.label_tonsPerUnit.TextAlign = ContentAlignment.TopRight;
			this.textBox_Exchg.Enabled = false;
			this.textBox_Exchg.Location = new Point(128, 84);
			this.textBox_Exchg.Name = "textBox_Exchg";
			this.textBox_Exchg.Size = new Size(101, 25);
			this.textBox_Exchg.TabIndex = 9;
			this.textBox_Exchg.TextAlign = HorizontalAlignment.Center;
			this.label9.Location = new Point(5, 89);
			this.label9.Name = "label9";
			this.label9.Size = new Size(118, 20);
			this.label9.TabIndex = 30;
			this.label9.Text = "交易所:";
			this.label9.TextAlign = ContentAlignment.TopRight;
			this.label7.Location = new Point(5, 29);
			this.label7.Name = "label7";
			this.label7.Size = new Size(118, 20);
			this.label7.TabIndex = 25;
			this.label7.Text = "交易品种:";
			this.label7.TextAlign = ContentAlignment.TopRight;
			this.textBox_SymbName.Enabled = false;
			this.textBox_SymbName.Location = new Point(128, 24);
			this.textBox_SymbName.Name = "textBox_SymbName";
			this.textBox_SymbName.Size = new Size(101, 25);
			this.textBox_SymbName.TabIndex = 7;
			this.textBox_SymbName.TextAlign = HorizontalAlignment.Center;
			this.textBox_SymbCode.Enabled = false;
			this.textBox_SymbCode.Location = new Point(128, 54);
			this.textBox_SymbCode.Name = "textBox_SymbCode";
			this.textBox_SymbCode.Size = new Size(101, 25);
			this.textBox_SymbCode.TabIndex = 8;
			this.textBox_SymbCode.TextAlign = HorizontalAlignment.Center;
			this.label8.Location = new Point(5, 59);
			this.label8.Name = "label8";
			this.label8.Size = new Size(118, 20);
			this.label8.TabIndex = 27;
			this.label8.Text = "交易代码:";
			this.label8.TextAlign = ContentAlignment.TopRight;
			this.groupBox_Others.Controls.Add(this.label5);
			this.groupBox_Others.Controls.Add(this.label2);
			this.groupBox_Others.Controls.Add(this.textBox_marginRate);
			this.groupBox_Others.Controls.Add(this.textBox_defaultTransUnit);
			this.groupBox_Others.Controls.Add(this.textBox_avgSlpg);
			this.groupBox_Others.Controls.Add(this.label11);
			this.groupBox_Others.Controls.Add(this.label3);
			this.groupBox_Others.Location = new Point(194, 240);
			this.groupBox_Others.Name = "groupBox_Others";
			this.groupBox_Others.Size = new Size(513, 92);
			this.groupBox_Others.TabIndex = 24;
			this.groupBox_Others.TabStop = false;
			this.groupBox_Others.Text = "其他";
			base.AutoScaleDimensions = new SizeF(8f, 15f);
			base.AutoScaleMode = AutoScaleMode.Font;
			base.Controls.Add(this.groupBox_Bottom);
			base.Name = "SymbParamCtrl";
			base.Size = new Size(722, 348);
			base.Load += this.SymbParamCtrl_Load;
			this.groupBox_Fee.ResumeLayout(false);
			this.groupBox_Fee.PerformLayout();
			this.panel1.ResumeLayout(false);
			this.panel1.PerformLayout();
			this.groupBox_StopLoss.ResumeLayout(false);
			this.groupBox_StopLoss.PerformLayout();
			this.groupBox_Bottom.ResumeLayout(false);
			this.groupBox_BaseInfo.ResumeLayout(false);
			this.groupBox_BaseInfo.PerformLayout();
			this.groupBox_Others.ResumeLayout(false);
			this.groupBox_Others.PerformLayout();
			base.ResumeLayout(false);
		}

		// Token: 0x0600194A RID: 6474 RVA: 0x000AA7A8 File Offset: 0x000A89A8
		[CompilerGenerated]
		private bool method_25(TradingSymbol tradingSymbol_2)
		{
			return tradingSymbol_2.ID == this.CurrSelectedSymb.ID;
		}

		// Token: 0x0600194B RID: 6475 RVA: 0x000AA7A8 File Offset: 0x000A89A8
		[CompilerGenerated]
		private bool method_26(TradingSymbol tradingSymbol_2)
		{
			return tradingSymbol_2.ID == this.CurrSelectedSymb.ID;
		}

		// Token: 0x04000C8B RID: 3211
		private List<TradingSymbol> list_0;

		// Token: 0x04000C8C RID: 3212
		private TradingSymbol tradingSymbol_0;

		// Token: 0x04000C8D RID: 3213
		private TradingSymbol tradingSymbol_1;

		// Token: 0x04000C8E RID: 3214
		private List<AcctSymbol> list_1;

		// Token: 0x04000C8F RID: 3215
		private List<TradingSymbol> list_2;

		// Token: 0x04000C90 RID: 3216
		private bool bool_0;

		// Token: 0x04000C91 RID: 3217
		private bool bool_1;

		// Token: 0x04000C92 RID: 3218
		private bool bool_2;

		// Token: 0x04000C93 RID: 3219
		private bool bool_3;

		// Token: 0x04000C94 RID: 3220
		private bool bool_4;

		// Token: 0x04000C95 RID: 3221
		[CompilerGenerated]
		private EventHandler eventHandler_0;

		// Token: 0x04000C96 RID: 3222
		[CompilerGenerated]
		private EventHandler eventHandler_1;

		// Token: 0x04000C97 RID: 3223
		private IContainer icontainer_0;

		// Token: 0x04000C98 RID: 3224
		private ListBox listBox_symb;

		// Token: 0x04000C99 RID: 3225
		private Label label1;

		// Token: 0x04000C9A RID: 3226
		private GroupBox groupBox_Fee;

		// Token: 0x04000C9B RID: 3227
		private Label label4;

		// Token: 0x04000C9C RID: 3228
		private TextBox textBox_AutoStPt;

		// Token: 0x04000C9D RID: 3229
		private TextBox textBox_AutoTfPt;

		// Token: 0x04000C9E RID: 3230
		private CheckBox checkBox_OneSideFee;

		// Token: 0x04000C9F RID: 3231
		private Panel panel1;

		// Token: 0x04000CA0 RID: 3232
		private Label label_feeAmtUnit;

		// Token: 0x04000CA1 RID: 3233
		private TextBox textBox_feeAmt;

		// Token: 0x04000CA2 RID: 3234
		private RadioButton radioBtn_FeeFixAmt;

		// Token: 0x04000CA3 RID: 3235
		private Label label_feeRatioSymbl;

		// Token: 0x04000CA4 RID: 3236
		private TextBox textBox_feeRatio;

		// Token: 0x04000CA5 RID: 3237
		private RadioButton radioBtn_FeeRatio;

		// Token: 0x04000CA6 RID: 3238
		private GroupBox groupBox_StopLoss;

		// Token: 0x04000CA7 RID: 3239
		private Label label2;

		// Token: 0x04000CA8 RID: 3240
		private TextBox textBox_marginRate;

		// Token: 0x04000CA9 RID: 3241
		private Label label3;

		// Token: 0x04000CAA RID: 3242
		private TextBox textBox_avgSlpg;

		// Token: 0x04000CAB RID: 3243
		private Label label5;

		// Token: 0x04000CAC RID: 3244
		private TextBox textBox_leastPrcVar;

		// Token: 0x04000CAD RID: 3245
		private Label label6;

		// Token: 0x04000CAE RID: 3246
		private TextBox textBox_defaultTransUnit;

		// Token: 0x04000CAF RID: 3247
		private Label label11;

		// Token: 0x04000CB0 RID: 3248
		private GroupBox groupBox_Bottom;

		// Token: 0x04000CB1 RID: 3249
		private GroupBox groupBox_Others;

		// Token: 0x04000CB2 RID: 3250
		private TextBox textBox_SymbName;

		// Token: 0x04000CB3 RID: 3251
		private Label label7;

		// Token: 0x04000CB4 RID: 3252
		private TextBox textBox_SymbCode;

		// Token: 0x04000CB5 RID: 3253
		private Label label8;

		// Token: 0x04000CB6 RID: 3254
		private GroupBox groupBox_BaseInfo;

		// Token: 0x04000CB7 RID: 3255
		private TextBox textBox_Exchg;

		// Token: 0x04000CB8 RID: 3256
		private Label label9;

		// Token: 0x04000CB9 RID: 3257
		private Label label_tonsPerUnitMeasure;

		// Token: 0x04000CBA RID: 3258
		private TextBox textBox_TonsPerUnit;

		// Token: 0x04000CBB RID: 3259
		private Label label_tonsPerUnit;

		// Token: 0x0200024C RID: 588
		[CompilerGenerated]
		private sealed class Class312
		{
			// Token: 0x0600194D RID: 6477 RVA: 0x000AA7CC File Offset: 0x000A89CC
			internal bool method_0(TradingSymbol tradingSymbol_0)
			{
				return tradingSymbol_0.ID == this.stkSymbol_0.MstSymbol.ID;
			}

			// Token: 0x04000CBC RID: 3260
			public StkSymbol stkSymbol_0;
		}

		// Token: 0x0200024D RID: 589
		[CompilerGenerated]
		private sealed class Class313
		{
			// Token: 0x0600194F RID: 6479 RVA: 0x000AA7F8 File Offset: 0x000A89F8
			internal bool method_0(TradingSymbol tradingSymbol_1)
			{
				return tradingSymbol_1.ID == this.tradingSymbol_0.ID;
			}

			// Token: 0x04000CBD RID: 3261
			public TradingSymbol tradingSymbol_0;
		}

		// Token: 0x0200024E RID: 590
		[CompilerGenerated]
		private sealed class Class314
		{
			// Token: 0x06001951 RID: 6481 RVA: 0x000AA81C File Offset: 0x000A8A1C
			internal bool method_0(AcctSymbol acctSymbol_1)
			{
				bool result;
				if (acctSymbol_1.AcctID == this.acctSymbol_0.AcctID)
				{
					result = (acctSymbol_1.ID == this.acctSymbol_0.ID);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000CBE RID: 3262
			public AcctSymbol acctSymbol_0;
		}

		// Token: 0x0200024F RID: 591
		[CompilerGenerated]
		private sealed class Class315
		{
			// Token: 0x06001953 RID: 6483 RVA: 0x000AA858 File Offset: 0x000A8A58
			internal bool method_0(AcctSymbol acctSymbol_1)
			{
				bool result;
				if (acctSymbol_1.ID == this.acctSymbol_0.ID)
				{
					result = (acctSymbol_1.AcctID == this.acctSymbol_0.AcctID);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x06001954 RID: 6484 RVA: 0x000AA858 File Offset: 0x000A8A58
			internal bool method_1(AcctSymbol acctSymbol_1)
			{
				bool result;
				if (acctSymbol_1.ID == this.acctSymbol_0.ID)
				{
					result = (acctSymbol_1.AcctID == this.acctSymbol_0.AcctID);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x04000CBF RID: 3263
			public AcctSymbol acctSymbol_0;
		}

		// Token: 0x02000250 RID: 592
		[CompilerGenerated]
		private sealed class Class316
		{
			// Token: 0x06001956 RID: 6486 RVA: 0x000AA894 File Offset: 0x000A8A94
			internal bool method_0(AcctSymbol acctSymbol_0)
			{
				bool result;
				if (acctSymbol_0.AcctID == ((AcctSymbol)this.tradingSymbol_0).AcctID)
				{
					result = (acctSymbol_0.ID == this.tradingSymbol_0.ID);
				}
				else
				{
					result = false;
				}
				return result;
			}

			// Token: 0x06001957 RID: 6487 RVA: 0x000AA8D4 File Offset: 0x000A8AD4
			internal bool method_1(TradingSymbol tradingSymbol_1)
			{
				return tradingSymbol_1.ID == this.tradingSymbol_0.ID;
			}

			// Token: 0x06001958 RID: 6488 RVA: 0x000AA8D4 File Offset: 0x000A8AD4
			internal bool method_2(TradingSymbol tradingSymbol_1)
			{
				return tradingSymbol_1.ID == this.tradingSymbol_0.ID;
			}

			// Token: 0x04000CC0 RID: 3264
			public TradingSymbol tradingSymbol_0;
		}
	}
}
