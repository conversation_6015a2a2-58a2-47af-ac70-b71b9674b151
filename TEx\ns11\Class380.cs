﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using ns12;
using ns13;
using ns14;
using ns18;
using ns19;
using ns22;
using ns23;
using ns24;
using ns26;
using ns27;
using ns28;
using ns3;
using ns33;
using ns5;
using ns7;
using TEx.Chart;
using TEx.Comn;
using TEx.Inds;
using TEx.SIndicator;

namespace ns11
{
	// Token: 0x020002E2 RID: 738
	internal abstract class Class380
	{
		// Token: 0x060020D0 RID: 8400 RVA: 0x0000D40C File Offset: 0x0000B60C
		public Class380(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1)
		{
			this.indEx_0 = indEx_1;
			this.IndData = dataArray_1;
			this.DP = dataProvider_1;
		}

		// Token: 0x060020D1 RID: 8401 RVA: 0x000E16C0 File Offset: 0x000DF8C0
		protected DateTime method_0(int int_0)
		{
			if (int_0 >= this.DP.PeriodHisDataList.Count)
			{
				Class182.smethod_0(new Exception(string.Concat(new object[]
				{
					" 位置：",
					int_0,
					"溢出,状态为：",
					this.indEx_0.method_7()
				})));
				int_0 = this.DP.PeriodHisDataList.Count - 1;
			}
			return this.DP.PeriodHisDataList.Keys[int_0];
		}

		// Token: 0x060020D2 RID: 8402
		protected abstract PointPair vmethod_0(int int_0, DataArray dataArray_1);

		// Token: 0x060020D3 RID: 8403 RVA: 0x000041AE File Offset: 0x000023AE
		public virtual void vmethod_1(ZedGraphControl zedGraphControl_0)
		{
		}

		// Token: 0x060020D4 RID: 8404 RVA: 0x000041AE File Offset: 0x000023AE
		public virtual void vmethod_2(int int_0)
		{
		}

		// Token: 0x060020D5 RID: 8405 RVA: 0x000041AE File Offset: 0x000023AE
		public virtual void vmethod_3(int int_0, DataArray dataArray_1)
		{
		}

		// Token: 0x060020D6 RID: 8406 RVA: 0x000041AE File Offset: 0x000023AE
		public virtual void vmethod_4(int int_0, DataArray dataArray_1)
		{
		}

		// Token: 0x060020D7 RID: 8407
		public abstract double vmethod_5(int int_0, HisData hisData_0);

		// Token: 0x060020D8 RID: 8408
		public abstract void vmethod_6(string string_0, ZedGraphControl zedGraphControl_0, Color color_0);

		// Token: 0x060020D9 RID: 8409 RVA: 0x000E174C File Offset: 0x000DF94C
		public static List<ShapeCurve> smethod_0(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1)
		{
			List<ShapeCurve> list = new List<ShapeCurve>();
			string shapeStr = dataArray_1.ShapeStr;
			List<ShapeCurve> result;
			if (shapeStr == null)
			{
				ShapeCurve item = Class380.smethod_2(dataArray_1, dataProvider_1, indEx_1);
				list.Add(item);
				result = list;
			}
			else if (shapeStr.Equals("DRAWCOLORLINE", StringComparison.InvariantCultureIgnoreCase))
			{
				List<DataArray> list2 = Class380.smethod_1(dataArray_1);
				if (list2.Count != 2)
				{
					throw new Exception("DRAWCOLORLINE转换个数不等于2。");
				}
				Class388 item2 = new Class388(list2[0], dataProvider_1, indEx_1);
				Class388 item3 = new Class388(list2[1], dataProvider_1, indEx_1);
				list.Add(item2);
				list.Add(item3);
				result = list;
			}
			else
			{
				ShapeCurve item4 = Class380.smethod_2(dataArray_1, dataProvider_1, indEx_1);
				list.Add(item4);
				result = list;
			}
			return result;
		}

		// Token: 0x060020DA RID: 8410 RVA: 0x000E17F4 File Offset: 0x000DF9F4
		public static List<DataArray> smethod_1(DataArray dataArray_1)
		{
			List<DataArray> list = new List<DataArray>();
			string shapeStr = dataArray_1.ShapeStr;
			if (shapeStr == null)
			{
				throw new Exception("不是ColorLine类型无法转换。");
			}
			if (shapeStr.ToUpper() == "DRAWCOLORLINE")
			{
				DataArray dataArray = (DataArray)dataArray_1.Clone();
				dataArray.OtherDataArrayList = dataArray_1.OtherDataArrayList;
				dataArray.ColorStr = dataArray_1.SingleData["COLOR1"].ToString();
				dataArray.LineTypeStr = dataArray_1.LineTypeStr;
				dataArray.LineWithStr = dataArray_1.LineWithStr;
				list.Add(dataArray);
				DataArray dataArray2 = (DataArray)dataArray_1.Clone();
				dataArray2.OtherDataArrayList.Add((DataArray)dataArray_1.OtherDataArrayList[0].Clone());
				for (int i = 0; i < dataArray2.OtherDataArrayList[0].Data.Length; i++)
				{
					if (dataArray2.OtherDataArrayList[0].Data[i] == 0.0)
					{
						dataArray2.OtherDataArrayList[0].Data[i] = 1.0;
					}
					else
					{
						dataArray2.OtherDataArrayList[0].Data[i] = 0.0;
					}
				}
				dataArray2.ColorStr = dataArray_1.SingleData["COLOR2"].ToString();
				dataArray2.LineTypeStr = dataArray_1.LineTypeStr;
				dataArray2.LineWithStr = dataArray_1.LineWithStr;
				list.Add(dataArray2);
				return list;
			}
			throw new Exception("不是ColorLine类型无法转换。");
		}

		// Token: 0x060020DB RID: 8411 RVA: 0x000E1970 File Offset: 0x000DFB70
		public static ShapeCurve smethod_2(DataArray dataArray_1, DataProvider dataProvider_1, IndEx indEx_1)
		{
			Class380.Class401 @class = new Class380.Class401();
			string shapeStr = dataArray_1.ShapeStr;
			ShapeCurve result;
			if (shapeStr == null)
			{
				result = new Class387(dataArray_1, dataProvider_1, indEx_1);
			}
			else
			{
				@class.string_0 = shapeStr.ToUpper();
				if (ParserEnvironment.string_0.Any(new Func<string, bool>(@class.method_0)))
				{
					if (@class.string_0 == "LINE")
					{
						result = new Class387(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == "CIRCLEDOT")
					{
						result = new Class389(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == "COLORSTICK")
					{
						result = new Class390(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == "STICK")
					{
						result = new Class398(dataArray_1, dataProvider_1, indEx_1);
					}
					else
					{
						if (!(@class.string_0 == "VOLSTICK"))
						{
							throw new Exception(string.Format("{0}无法解析成图形类", shapeStr));
						}
						result = new Class397(dataArray_1, dataProvider_1, indEx_1);
					}
				}
				else if (!(@class.string_0 == "STICKLINE") && !(@class.string_0 == "STICKLINE1"))
				{
					if (@class.string_0 == "DRAWLINE")
					{
						result = new Class391(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == "DRAWNUMBER")
					{
						result = new Class392(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == "DRAWTEXT")
					{
						result = new Class393(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == "FILLRGN")
					{
						result = new Class394(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == "DRAWICON")
					{
						result = new ShapeDrawICON(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == "DRAWKLINE")
					{
						result = new Class395(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == "PARTLINE")
					{
						result = new Class388(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == "DRAWSL")
					{
						result = new Class385(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == "TRENDLINES")
					{
						result = new Class383(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == "HORIZONTALLINE")
					{
						result = new Class386(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == "ANGLELINE")
					{
						result = new Class381(dataArray_1, dataProvider_1, indEx_1);
					}
					else if (@class.string_0 == "GOLDENLINE")
					{
						result = new Class382(dataArray_1, dataProvider_1, indEx_1);
					}
					else
					{
						if (!(@class.string_0 == "WAVERULER"))
						{
							throw new Exception(string.Format("{0}无法解析成图形类", shapeStr));
						}
						result = new Class384(dataArray_1, dataProvider_1, indEx_1);
					}
				}
				else
				{
					result = new Class396(dataArray_1, dataProvider_1, indEx_1);
				}
			}
			return result;
		}

		// Token: 0x060020DC RID: 8412 RVA: 0x0000D42B File Offset: 0x0000B62B
		public virtual void vmethod_7(int int_0)
		{
			this.rollingPointPairList_0 = new RollingPointPairList(int_0);
		}

		// Token: 0x170005BB RID: 1467
		// (get) Token: 0x060020DD RID: 8413 RVA: 0x000E1C50 File Offset: 0x000DFE50
		// (set) Token: 0x060020DE RID: 8414 RVA: 0x0000D43B File Offset: 0x0000B63B
		public RollingPointPairList DataView
		{
			get
			{
				return this.rollingPointPairList_0;
			}
			private set
			{
				this.rollingPointPairList_0 = value;
			}
		}

		// Token: 0x170005BC RID: 1468
		// (get) Token: 0x060020DF RID: 8415 RVA: 0x000E1C68 File Offset: 0x000DFE68
		// (set) Token: 0x060020E0 RID: 8416 RVA: 0x0000D446 File Offset: 0x0000B646
		public DataArray IndData { get; protected set; }

		// Token: 0x170005BD RID: 1469
		// (get) Token: 0x060020E1 RID: 8417 RVA: 0x000E1C80 File Offset: 0x000DFE80
		// (set) Token: 0x060020E2 RID: 8418 RVA: 0x0000D451 File Offset: 0x0000B651
		protected DataProvider DP { get; set; }

		// Token: 0x170005BE RID: 1470
		// (get) Token: 0x060020E3 RID: 8419 RVA: 0x000E1C98 File Offset: 0x000DFE98
		protected int KCount
		{
			get
			{
				return this.DP.PeriodHisDataList.Keys.Count;
			}
		}

		// Token: 0x04001019 RID: 4121
		protected IndEx indEx_0;

		// Token: 0x0400101A RID: 4122
		protected RollingPointPairList rollingPointPairList_0;

		// Token: 0x0400101B RID: 4123
		[CompilerGenerated]
		private DataArray dataArray_0;

		// Token: 0x0400101C RID: 4124
		[CompilerGenerated]
		private DataProvider dataProvider_0;

		// Token: 0x020002E3 RID: 739
		[CompilerGenerated]
		private sealed class Class401
		{
			// Token: 0x060020E5 RID: 8421 RVA: 0x000E1CC0 File Offset: 0x000DFEC0
			internal bool method_0(string string_1)
			{
				return string_1 == this.string_0;
			}

			// Token: 0x0400101D RID: 4125
			public string string_0;
		}
	}
}
