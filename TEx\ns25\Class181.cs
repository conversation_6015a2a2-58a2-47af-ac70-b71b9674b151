﻿using System;

namespace ns25
{
	// Token: 0x0200012E RID: 302
	internal sealed class Class181
	{
		// Token: 0x06000C87 RID: 3207 RVA: 0x00002D25 File Offset: 0x00000F25
		public Class181()
		{
		}

		// Token: 0x06000C88 RID: 3208 RVA: 0x00005AF1 File Offset: 0x00003CF1
		public Class181(int int_1)
		{
			this.int_0 = int_1;
		}

		// Token: 0x06000C89 RID: 3209 RVA: 0x00005B02 File Offset: 0x00003D02
		public Class181(int int_1, int? nullable_2, int? nullable_3) : this(int_1, nullable_2, nullable_3, true, false)
		{
		}

		// Token: 0x06000C8A RID: 3210 RVA: 0x00005B0F File Offset: 0x00003D0F
		public Class181(int int_1, int? nullable_2, int? nullable_3, bool bool_2, bool bool_3) : this(int_1)
		{
			this.nullable_0 = nullable_2;
			this.nullable_1 = nullable_3;
			this.bool_0 = bool_2;
			this.bool_1 = bool_3;
		}

		// Token: 0x04000529 RID: 1321
		public int int_0;

		// Token: 0x0400052A RID: 1322
		public int? nullable_0;

		// Token: 0x0400052B RID: 1323
		public int? nullable_1;

		// Token: 0x0400052C RID: 1324
		public bool bool_0;

		// Token: 0x0400052D RID: 1325
		public bool bool_1;
	}
}
